package com.qcc.presto.udf;

import io.airlift.slice.Slice;
import io.airlift.slice.Slices;
import io.trino.spi.function.Description;
import io.trino.spi.function.ScalarFunction;
import io.trino.spi.function.SqlType;
import io.trino.spi.type.StandardTypes;


public class UdfTest {
    @Description("derek test Function")       //描述
    @ScalarFunction("derek_presto_udf")            //方法名称
    @SqlType(StandardTypes.VARCHAR)           //返回类型
    public static Slice derek_presto_udf(@SqlType(StandardTypes.VARCHAR) Slice str) {
        return Slices.utf8Slice(str.toStringUtf8() + "derek_test");
    }

    public static void main(String[] args) {
        System.out.println(derek_presto_udf(Slices.utf8Slice("a")).toStringUtf8());
    }
}