package com.qcc.presto.udf;

import io.airlift.slice.Slice;
import io.airlift.slice.Slices;
import io.trino.spi.block.Block;
import io.trino.spi.block.BlockBuilder;
import io.trino.spi.function.Description;
import io.trino.spi.function.ScalarFunction;
import io.trino.spi.function.SqlType;
import io.trino.spi.type.StandardTypes;
import org.json.JSONArray;
import org.json.JSONTokener;

import static io.trino.spi.type.VarbinaryType.VARBINARY;

public class GetJsonArray {

    @Description("getJsonArray")       //描述
    @ScalarFunction("getJsonArray")            //方法名称
    @SqlType("array<varchar>")           //返回类型
    public static Block getJsonArray(@SqlType(StandardTypes.VARCHAR) Slice jsonArrayStr) {

        if (jsonArrayStr.getBase() == null || jsonArrayStr.length() <= 0) {
            return null;
        }
        BlockBuilder blockBuilder = VARBINARY.createBlockBuilder(null, 15);


        if (!jsonArrayStr.toStringUtf8().trim().startsWith("[")) {

            VARBINARY.writeSlice(blockBuilder, jsonArrayStr);

        } else {
            JSONArray jsonArray = new JSONArray(new JSONTokener(jsonArrayStr.toStringUtf8()));

            for (int i = 0; i < jsonArray.length(); i++) {
                String json = jsonArray.getJSONObject(i).toString();
                VARBINARY.writeSlice(blockBuilder, Slices.utf8Slice(json));
            }
        }

        return blockBuilder.build();
    }


    public static void main(String[] args) {
        Block block = getJsonArray(Slices.utf8Slice("[{\"name\":\"王二\",\"sex\":\"男\",\"age\":\"25\"},{\"name\":\"李四\",\"sex\":\"男\",\"age\":\"47111\"}]"));

        System.out.println(block.getPositionCount());

        System.out.println(block.getSliceLength(0));

        for (int i = 0; i < block.getPositionCount(); i++) {
            System.out.println(block.getSlice(i, 0, block.getSliceLength(i)).toStringUtf8());
        }
    }
}