package com.qcc.presto.plugin;

import com.qcc.presto.udf.GetJsonArray;
import com.qcc.presto.udf.UdfTest;
import com.google.common.collect.ImmutableSet;
import io.trino.spi.Plugin;

import java.util.Set;

public class FunctionsPlugin implements Plugin {
    @Override
    public Set<Class<?>> getFunctions() {
        return ImmutableSet.<Class<?>>builder()
                .add(UdfTest.class)
                .add(GetJsonArray.class)//可添加多个函数方法名
//                .add(UdfTest.class)
                .build();
    }
}