package com.qcc.udf.cpws.wjp;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

import java.util.*;

/**
 * CaseReasonMatcher 综合测试类
 * 
 * 测试目标：
 * 1. 验证main方法中的现有测试用例
 * 2. 基于SQL查询结果结构进行数据验证测试
 * 3. 测试各种边界情况和异常情况
 * 4. 验证匹配优先级：正文 > 标题 > 爬虫案由
 * 
 * 测试数据来源：
 * - main方法中的7个现有测试用例
 * - 基于SQL查询结构模拟的16个测试用例（对应SQL中的16个ID）
 * - 边界情况和异常情况测试用例
 */
public class CaseReasonMatcherTest {

    private CaseReasonMatcher matcher;
    private List<TestResult> testResults;
    private long startTime;

    @Before
    public void setUp() {
        matcher = new CaseReasonMatcher();
        testResults = new ArrayList<>();
        startTime = System.currentTimeMillis();
        System.out.println("=== CaseReasonMatcher 测试开始 ===");
        System.out.println("开始时间: " + new Date());
    }

    /**
     * 测试数据实体类 - 对应SQL查询结果结构
     */
    static class SqlTestData {
        String id;
        String caseReason;
        String caseName;
        String combinedContent; // CONCAT(GET_JSON_OBJECT(case_main_body,'$.law_judge_party'),',',GET_JSON_OBJECT(case_main_body,'$.law_judge_trial'))
        String expectedResult; // 预期结果
        String description; // 测试描述

        public SqlTestData(String id, String caseReason, String caseName, String combinedContent, String expectedResult, String description) {
            this.id = id;
            this.caseReason = caseReason;
            this.caseName = caseName;
            this.combinedContent = combinedContent;
            this.expectedResult = expectedResult;
            this.description = description;
        }
    }

    /**
     * 测试结果记录类
     */
    static class TestResult {
        String testName;
        String input;
        String expected;
        String actual;
        boolean passed;
        long executionTime;
        String errorMessage;

        public TestResult(String testName, String input, String expected, String actual, boolean passed, long executionTime, String errorMessage) {
            this.testName = testName;
            this.input = input;
            this.expected = expected;
            this.actual = actual;
            this.passed = passed;
            this.executionTime = executionTime;
            this.errorMessage = errorMessage;
        }
    }

    @Test
    public void testExistingMainMethodCases() {
        System.out.println("\n--- 测试main方法中的现有测试用例 ---");
        
        // 测试用例1：罚金案例
        testCase("测试用例1-罚金", 
                "[\"罚金\"]", 
                "周传龙首次执行执行裁定书",
                "移送单位：仁寿县人民法院。 被执行人：周传龙,男性,****年**月**日出生,汉族，住**。,本院于2020年12月25日作出的（2020）川1421刑初381号刑事判决书并处判周传龙罚金5000元发生法律效力后，被执行人周传龙未按生效法律文书确定的内容履行缴纳罚金的义务。本院于2021年8月10日立案执行后，在执行中，本院依法向被执行人送达了执行通知书、报告财产令、限制消费令。2021年10月28日本院作出（2021）川1421执2553号执行裁定书，裁定：查封被执行人周传龙与案外人邱作祥、胡建琼、胡银楷共同共有的位于简阳市地（不动产权证号：川（2018）简阳市不动产权第0072849），期限为三年。因上述房产系共有财产，无法确定份额，故暂无法进行处置。经全国执行网络查询系统对被执行人的银行存款、互联网银行、不动产、车辆、保险、工商登记等财产信息进行了查询，依法冻结了被执行人银行账号，依法扣划被执行人银行存款4105元，未发现可供执行财产。本院另向仁寿县不动产登记中心、仁寿县综治网格中心对被执行人的下落及财产情况进行调查，均未发现可供执行的财产线索。本院依法将被执行人限制高消费。现鉴于被执行人暂无可供执行的财产，本院决定依职权终结（2020）川1421刑初381号刑事判决书并处判周传龙罚金895元财产刑的本次执行程序。被执行人尚未履行生效法律文书确定的罚金895元财产刑全部义务。依照《中华人民共和国民事诉讼法》第二百五十七条、《最高人民法院关于适用〈中华人民共和国民事诉讼法〉的解释》第五百一十九条之规定，裁定如下：",
                null); // 不预设期望结果，记录实际结果

        // 测试用例2：合同纠纷
        testCase("测试用例2-合同纠纷", 
                "", 
                "李某某合同、无因管理、不当得利纠纷执行实施类执行裁定书",
                "被执行人：李某某，男性，1965年06月20出生，汉族，住**。,本院在执行与李某某罚金一案中，依据已经发生法律效力的葫芦岛市南票区人民法院（2019）辽1404刑初151号判决书，已向被执行人李某某发出执行通知书，责令被执行人接到执行通知后立即履行该法律文书确定的义务，但被执行人李某某未按执行通知履行法律文书确定的义务，依照《中华人民共和国民事诉讼法》第一百五十四条第一款第（十一）项、第二百四十条、第二百四十二条、第二百四十三条、第二百四十四条、第二百四十七条以及第二百五十三条之规定，裁定如下：",
                null);

        // 测试用例3：储蓄存款合同纠纷
        testCase("测试用例3-储蓄存款合同纠纷", 
                "", 
                "郑向生非法吸收公众存款案处置工作领导小组、郑向生储蓄存款合同纠纷执行实施类执行裁定书",
                "申请人：郑向生非法吸收公众存款案处置工作领导小组。住所地：铜鼓县烈士陵园旁原劳动人事局大楼。 联系人：冷思敏。 被执行人：郑向生，男，****年**月**日出生，汉族，铜鼓县人，住**。 被执行人：郑永金，男，****年**月**日出生，汉族，铜鼓县人，住**。,本院在执行申请执行人郑向生非法吸收公众存款案处置工作领导小组与被执行人郑向生、郑永金（下称被执行人）非法吸收公众存款纠纷系列案中，依据发生法律效力的本院（2015）铜刑初字第56号刑事判决书，上述被执行人须退赔该案刑事受害人尚未返还资金。因被执行人未履行生效法律文书确定的义务，本院于2018年10月24日至11月20日期间两次挂网拍卖实际由被执行人郑向生开发的位于铜鼓县永宁镇城南东路\\\"子龙府\\\"1号楼150套住房，现已成功拍卖住房6套（11-1-03、4-2-01、23-2-01及24-2-01、12-2-04、16-2-03），其余144套均已流拍。经我院征询申请人郑向生非法吸收公众存款案处置工作领导小组意见，申请人同意上述144套房产以二拍流拍价（即评估价）以物抵债至其后，由其依法处置再行分配给该案债权人。依据《最高人民法院关于人民法院民事执行中拍卖、变卖财产的规定》第十九条、第二十三条、第二十七条、第二十八条、第二十九条之规定，裁定如下：",
                null);

        // 测试用例4：行纪合同纠纷
        testCase("测试用例4-行纪合同纠纷", 
                "[\"行纪合同纠纷\"]", 
                "青海安意房地产中介服务有限公司、常立娟中介合同纠纷民事一审民事裁定书",
                "原告：青海安意房地产中介服务有限公司，住所：青海省西宁市城北区柴达木路351号16号楼1单元1011室。 法定代表人：雷菊林，该公司总经理。 被告：常立娟，女，****年**月**日出生，汉族，住**。 委托诉讼代理人：宋长银，男，****年**月**日出生，汉族，住青海省西宁市城北区。,原告青海安意房地产中介服务有限公司与被告常立娟中介合同纠纷一案，本院于2023年4月11日立案。原告青海安意房地产中介服务有限公司于2023年5月19日向本院提出撤诉申请。",
                null);

        // 测试用例5：责令退赔纠纷
        testCase("测试用例5-责令退赔纠纷", 
                "", 
                "申请人株洲市天元区人民法院刑事审判庭与被执行人李志周责令退赔纠纷一案终本裁定书",
                "申请执行人：株洲市天元区人民法院刑事审判庭。 被执行人：李志周，男，****年**月**日出生，汉族。 申请人株洲市天元区人民法院,刑事审判庭与被执行人李志周责令退赔纠纷一案，本院作出（2019）湘0211刑初43号刑事判决书，已发生法律效力。由于被执行人未能按期履行生效法律文书确定的义务，本院于2019年8月12日立案执行。 2019年8月21日，本院向被执行人李志周邮寄送达了执行通知书、报告财产令，于2019年8月12日向申请执行人送达受理通知书并要求其提供财产线索。于2019年8月13日发起全国法院执行查控系统，未发现任何有可供执行的财产。于2019年9月6日向株洲市住房公积金管理中心查询被执行人公积金账户，同日向中国人寿股份有限公司株洲分公司查询被执行人收益类保险情况。于2019年9月10日向不动产登记中心查询被执行人的不动产和其他财产情况，均未发现可供执行财产。 2019年9月18日本院对被执行人李志周适用限制高消费措施，并在中国执行信息公开网上公布。 上述执行情况及信息，本院于2019年9月18日通过谈话告知了申请执行人，并要求其提供其他财产线索，但申请执行人未能提供。本院同时依法告知了申请执行人，本案将做终结本次执行程序结案。 本案认为，截止2019年9月18日，本案应当执行标的金额为79.2570万元，尚有债权79.2570万元未执行到位。经本院采取上述执行行为，目前并未发现被执行人有可供执行且适宜处置的财产。申请执行人也未能提供其他可供执行的财产线索。依照《最高人民法院关于适用〈中华人民共和国民事诉讼法〉的解释》第五百一十九条之规定，裁定如下：",
                null);

        // 测试用例6：侵害商标权纠纷
        testCase("测试用例6-侵害商标权纠纷", 
                "[\"侵害商标权纠纷\"]", 
                "欧普某公司与朱某侵害商标权纠纷一审民事裁定书",
                "原告：欧普照明股份有限公司，住所地上海市浦东新区龙东大道6111号1幢411室，统一社会信用代码91310000680999558Q。 法定代表人:王耀海，该公司董事长。 委托诉讼代理人：翟明跃，山东昌平律师事务所律师。 被告：朱绍强，男，****年**月**日出生，汉族，住**。,原告欧普照明股份有限公司与被告朱绍强侵害商标权纠纷一案，本院于2024年4月10日立案。原告欧普照明股份有限公司于2024年6月27日向本院申请撤诉。",
                null);

        // 测试用例7：金融借款合同纠纷
        testCase("测试用例7-金融借款合同纠纷", 
                "[\"金融借款合同纠纷\"]", 
                "孙鹏、河南兰考农村商业银行股份有限公司等金融借款合同纠纷民事申请再审审查民事裁定书",
                "再审申请人（案外人）：孙鹏，男，****年**月**日出生，汉族，住河南省兰考县。 委托诉讼代理人：朱胜利，河南兰桐律师事务所律师，代理权限为特别授权。 被申请人（一审原告）：河南兰考农村商业银行股份有限公司，住所地河南省兰考县。 法定代表人：王全禄。 被申请人（一审被告）：黄海龙，男，****年**月**日生，汉族，住河南省兰考县。 被申请人（一审被告）：何涛，男，****年**月**日生，汉族，住河南省兰考县。 被申请人（一审被告）：宋永红，女，****年**月**日生，汉族，住河南省兰考县。 被申请人（一审被告）：秦某，女，****年**月**日生，汉族，住郑州市。 被申请人（一审被告）：孙某，女，****年**月**日生，汉族，住郑州市。 法定代理人：秦某，女，****年**月**日生，汉族，住郑州市，系孙某之母。 被申请人（一审被告）：孙嘉盟，男，****年**月**日生，汉族，住河南省兰考县。 被申请人（一审被告）：孙嘉浩，男，****年**月**日生，汉族，住河南省兰考县。,再审申请人孙鹏因与被申请人河南兰考农村商业银行股份有限公司、黄海龙、何涛、宋永红、秦某、孙某、孙嘉盟、孙嘉浩金融借款合同纠纷一案，不服河南省兰考县人民法院（2020）豫0225民初1752号民事调解书，向本院申请再审。本院依法组成合议庭进行审查。 本院审查过程中，孙鹏以根据《最高人民法院关于适用〈中华人民共和国民事诉讼法〉的解释》第四百二十三条的规定，本案应向兰考县人民法院申请再审为由，向本院提出撤回再审申请。",
                null);
    }

    /**
     * 执行单个测试用例
     */
    private void testCase(String testName, String caseReason, String title, String content, String expected) {
        long testStart = System.currentTimeMillis();
        String input = String.format("caseReason=%s, title=%s, content=%s",
                caseReason != null ? caseReason : "null",
                title != null ? title : "null",
                content != null ? (content.length() > 50 ? content.substring(0, 50) + "..." : content) : "null");

        try {
            String actual = matcher.evaluate(caseReason, title, content);
            long executionTime = System.currentTimeMillis() - testStart;

            boolean passed = expected == null || expected.equals(actual);
            testResults.add(new TestResult(testName, input, expected, actual, passed, executionTime, null));

            System.out.printf("✓ %s\n: %s \n (执行时间: %dms)\n", input, actual, executionTime);

        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - testStart;
            testResults.add(new TestResult(testName, input, expected, null, false, executionTime, e.getMessage()));
            System.out.printf("✗ %s: 异常 - %s (执行时间: %dms)\n", testName, e.getMessage(), executionTime);
        }
    }

    @Test
    public void testSqlBasedTestData() {
        System.out.println("\n--- 基于SQL查询获取的真实测试数据 ---");

        // 使用从SQL查询获得的真实测试数据
        List<SqlTestData> sqlTestDataList = createRealSqlTestData();

        for (SqlTestData testData : sqlTestDataList) {
            testCase(
                "SQL测试-" + testData.id.substring(0, 8) + "-" + testData.description,
                testData.caseReason,
                testData.caseName,
                testData.combinedContent,
                testData.expectedResult
            );
        }
    }

    /**
     * 创建基于SQL查询结构的模拟测试数据
     */
    private List<SqlTestData> createSqlTestData() {
        List<SqlTestData> testDataList = new ArrayList<>();

        // 模拟SQL查询中16个ID的测试数据
        testDataList.add(new SqlTestData(
            "1c8f745c72abe212c37a92e2ccccc8530",
            "[\"买卖合同纠纷\"]",
            "张某与李某买卖合同纠纷案",
            "原告张某,被告李某,买卖合同纠纷一案,本院受理后依法组成合议庭公开开庭进行了审理",
            null,
            "买卖合同纠纷案例"
        ));

        testDataList.add(new SqlTestData(
            "1c99660ff74db3081a9e247264f1e6ab0",
            "[\"劳动争议\"]",
            "王某与某公司劳动争议案",
            "申请人王某,被申请人某公司,劳动争议一案,经调解双方达成协议",
            null,
            "劳动争议案例"
        ));

        testDataList.add(new SqlTestData(
            "1c9eadd318854e9a56fa2f930e2b14f30",
            "[\"交通事故责任纠纷\"]",
            "赵某与保险公司交通事故责任纠纷案",
            "原告赵某,被告保险公司,交通事故责任纠纷一案,事故发生后造成人员伤亡",
            null,
            "交通事故责任纠纷案例"
        ));

        testDataList.add(new SqlTestData(
            "1c9efed37858a654de0fcd50539502cb0",
            "[\"房屋买卖合同纠纷\"]",
            "陈某与房地产公司房屋买卖合同纠纷案",
            "原告陈某,被告房地产公司,房屋买卖合同纠纷一案,涉及房屋交付问题",
            null,
            "房屋买卖合同纠纷案例"
        ));

        testDataList.add(new SqlTestData(
            "1ca4c3237ed9a574f85e40c2ab7aa3730",
            "[\"借款合同纠纷\"]",
            "刘某与银行借款合同纠纷案",
            "原告银行,被告刘某,借款合同纠纷一案,被告未按期还款",
            null,
            "借款合同纠纷案例"
        ));

        testDataList.add(new SqlTestData(
            "1cc09019c22c44d5a1ec037b49c9affc0",
            "[\"离婚纠纷\"]",
            "孙某与配偶离婚纠纷案",
            "原告孙某,被告配偶,离婚纠纷一案,双方感情确已破裂",
            null,
            "离婚纠纷案例"
        ));

        testDataList.add(new SqlTestData(
            "1cc42da25b26419fbf457acb98cf76310",
            "[\"物业服务合同纠纷\"]",
            "业主与物业公司物业服务合同纠纷案",
            "原告业主,被告物业公司,物业服务合同纠纷一案,涉及物业费缴纳问题",
            null,
            "物业服务合同纠纷案例"
        ));

        testDataList.add(new SqlTestData(
            "6388b1bf0bc6962d05f9ab95e4658c390",
            "[\"知识产权纠纷\"]",
            "某公司与竞争对手知识产权纠纷案",
            "原告某公司,被告竞争对手,知识产权纠纷一案,涉及专利侵权",
            null,
            "知识产权纠纷案例"
        ));

        testDataList.add(new SqlTestData(
            "638b2480abd4c1b5079e8e5c259bb5550",
            "[\"合同纠纷\"]",
            "甲方与乙方合同纠纷案",
            "原告甲方,被告乙方,合同纠纷一案,双方就合同履行产生争议",
            null,
            "一般合同纠纷案例"
        ));

        testDataList.add(new SqlTestData(
            "1caec9886b5f1910a65a01ba0aa4318f0",
            "[\"侵权责任纠纷\"]",
            "受害人与侵权人侵权责任纠纷案",
            "原告受害人,被告侵权人,侵权责任纠纷一案,造成财产损失",
            null,
            "侵权责任纠纷案例"
        ));

        testDataList.add(new SqlTestData(
            "1cb997770a9e4505ae8761e6eb1a9bf00",
            "[\"租赁合同纠纷\"]",
            "房东与租客租赁合同纠纷案",
            "原告房东,被告租客,租赁合同纠纷一案,涉及租金支付问题",
            null,
            "租赁合同纠纷案例"
        ));

        testDataList.add(new SqlTestData(
            "1cc88e527fdfafd5f2df7753f3c4b7260",
            "[\"建设工程合同纠纷\"]",
            "建设方与施工方建设工程合同纠纷案",
            "原告建设方,被告施工方,建设工程合同纠纷一案,涉及工程质量问题",
            null,
            "建设工程合同纠纷案例"
        ));

        testDataList.add(new SqlTestData(
            "1ccdda52ffc7495395ed0dc30705d2ce0",
            "[\"保险合同纠纷\"]",
            "投保人与保险公司保险合同纠纷案",
            "原告投保人,被告保险公司,保险合同纠纷一案,涉及理赔争议",
            null,
            "保险合同纠纷案例"
        ));

        testDataList.add(new SqlTestData(
            "1ccf93d7d919430fa00608bf1fe09fb10",
            "[\"股权转让纠纷\"]",
            "转让方与受让方股权转让纠纷案",
            "原告转让方,被告受让方,股权转让纠纷一案,涉及股权价值争议",
            null,
            "股权转让纠纷案例"
        ));

        testDataList.add(new SqlTestData(
            "1cd7e1a0256242c0809feb65b9fe78490",
            "[\"继承纠纷\"]",
            "继承人之间继承纠纷案",
            "原告继承人甲,被告继承人乙,继承纠纷一案,涉及遗产分配",
            null,
            "继承纠纷案例"
        ));

        testDataList.add(new SqlTestData(
            "1d29450f36b94263b034623be63cb6350",
            "",
            "无案由信息的案件",
            "当事人甲,当事人乙,案件详情不明,需要通过内容匹配确定案由",
            null,
            "无案由信息案例"
        ));

        return testDataList;
    }

    /**
     * 创建基于SQL查询获取的真实测试数据
     */
    private List<SqlTestData> createRealSqlTestData() {
        List<SqlTestData> testDataList = new ArrayList<>();

        // 基于SQL查询结果的真实测试数据 - 使用完整的真实数据
        testDataList.add(new SqlTestData(
            "1ca4c3237ed9a574f85e40c2ab7aa3730",
            null, // case_reason为null
            "郑向生非法吸收公众存款案处置工作领导小组、郑向生储蓄存款合同纠纷执行实施类执行裁定书",
            "申请人：郑向生非法吸收公众存款案处置工作领导小组。住所地：铜鼓县烈士陵园旁原劳动人事局大楼。 联系人：冷思敏。 被执行人：郑向生，男，****年**月**日出生，汉族，铜鼓县人，住**。 被执行人：郑永金，男，****年**月**日出生，汉族，铜鼓县人，住**。,本院在执行申请执行人郑向生非法吸收公众存款案处置工作领导小组与被执行人郑向生、郑永金（下称被执行人）非法吸收公众存款纠纷系列案中，依据发生法律效力的本院（2015）铜刑初字第56号刑事判决书，上述被执行人须退赔该案刑事受害人尚未返还资金。因被执行人未履行生效法律文书确定的义务，本院于2018年10月24日至11月20日期间两次挂网拍卖实际由被执行人郑向生开发的位于铜鼓县永宁镇城南东路子龙府1号楼150套住房，现已成功拍卖住房6套（11-1-03、4-2-01、23-2-01及24-2-01、12-2-04、16-2-03），其余144套均已流拍。经我院征询申请人郑向生非法吸收公众存款案处置工作领导小组意见，申请人同意上述144套房产以二拍流拍价（即评估价）以物抵债至其后，由其依法处置再行分配给该案债权人。依据《最高人民法院关于人民法院民事执行中拍卖、变卖财产的规定》第十九条、第二十三条、第二十七条、第二十八条、第二十九条之规定，裁定如下：",
            null,
            "非法吸收公众存款案例"
        ));

        testDataList.add(new SqlTestData(
            "1c8f745c72abe212c37a92e2ccccc8530",
            "[\"金融借款合同纠纷\"]",
            "孙鹏、河南兰考农村商业银行股份有限公司等金融借款合同纠纷民事申请再审审查民事裁定书",
            "再审申请人（案外人）：孙鹏，男，****年**月**日出生，汉族，住河南省兰考县。 委托诉讼代理人：朱胜利，河南兰桐律师事务所律师，代理权限为特别授权。 被申请人（一审原告）：河南兰考农村商业银行股份有限公司，住所地河南省兰考县。 法定代表人：王全禄。 被申请人（一审被告）：黄海龙，男，****年**月**日生，汉族，住河南省兰考县。 被申请人（一审被告）：何涛，男，****年**月**日生，汉族，住河南省兰考县。 被申请人（一审被告）：宋永红，女，****年**月**日生，汉族，住河南省兰考县。 被申请人（一审被告）：秦某，女，****年**月**日生，汉族，住郑州市。 被申请人（一审被告）：孙某，女，****年**月**日生，汉族，住郑州市。 法定代理人：秦某，女，****年**月**日生，汉族，住郑州市，系孙某之母。 被申请人（一审被告）：孙嘉盟，男，****年**月**日生，汉族，住河南省兰考县。 被申请人（一审被告）：孙嘉浩，男，****年**月**日生，汉族，住河南省兰考县。,再审申请人孙鹏因与被申请人河南兰考农村商业银行股份有限公司、黄海龙、何涛、宋永红、秦某、孙某、孙嘉盟、孙嘉浩金融借款合同纠纷一案，不服河南省兰考县人民法院（2020）豫0225民初1752号民事调解书，向本院申请再审。本院依法组成合议庭进行审查。 本院审查过程中，孙鹏以根据《最高人民法院关于适用〈中华人民共和国民事诉讼法〉的解释》第四百二十三条的规定，本案应向兰考县人民法院申请再审为由，向本院提出撤回再审申请。",
            null,
            "金融借款合同纠纷案例"
        ));

        testDataList.add(new SqlTestData(
            "1ccdda52ffc7495395ed0dc30705d2ce0",
            "[\"其他案由\"]",
            "张某其他案由首次执行执行通知书",
            "张锋：,张锋罚金一案，申请执行标的共计3000.00元。",
            null,
            "其他案由案例"
        ));

        testDataList.add(new SqlTestData(
            "1d29450f36b94263b034623be63cb6350",
            "[\"合同、无因管理、不当得利\"]",
            "深圳某某控股有限公司与湖北某某投资有限公司合同、无因管理等执行异议裁定书",
            "案外人：罗某梅，女，****年**月**日出生，汉族，住**。 委托诉讼代理人：马红军，湖北今天律师事务所律师。 申请执行人：深圳某某控股有限公司。住所地：广东省深圳市南山区。 法定代表人：朱某蕊。 被执行人：湖北某某投资有限公司。住所地：湖北省武汉市黄陂区。 法定代表人：张某冰。,本院在（2023）鄂01执恢72号申请执行人深圳某某控股有限公司与被执行人湖北某某投资有限公司公证债权文书执行一案中，案外人罗某梅向本院提出书面异议。本院受理后，依法组成合议庭进行审查。 在本院审查期间，案外人罗某梅向本院提交书面申请，请求撤回异议申请。",
            null,
            "合同纠纷案例"
        ));

        testDataList.add(new SqlTestData(
            "1cb997770a9e4505ae8761e6eb1a9bf00",
            "[\"合同纠纷\"]",
            "上海雏兴网络科技有限公司与于欣竹其他合同纠纷一审民事判决书",
            "原告：上海雏兴网络科技有限公司，住所地上海市奉贤区北虹路86号。 法定代表人：高燕，董事长。 委托诉讼代理人：卢宗菊，山东众成清泰（上海）律师事务所律师。 被告：于欣竹，女，****年**月**日生，汉族，住**。,原告上海雏兴网络科技有限公司诉被告于欣竹其他合同纠纷一案，本院于2022年1月6日立案后，依法适用简易程序，并于1月27日公开开庭进行了审理。原告的委托诉讼代理人卢宗菊、被告于欣竹均到庭参加诉讼。本案现已审理终结。",
            null,
            "合同纠纷案例"
        ));

        testDataList.add(new SqlTestData(
            "1c9eadd318854e9a56fa2f930e2b14f30",
            "[\"罚金\"]",
            "周传龙首次执行执行裁定书",
            "移送单位：仁寿县人民法院。 被执行人：周传龙,男性,****年**月**日出生,汉族，住**。,本院于2020年12月25日作出的（2020）川1421刑初381号刑事判决书并处判周传龙罚金5000元发生法律效力后，被执行人周传龙未按生效法律文书确定的内容履行缴纳罚金的义务。本院于2021年8月10日立案执行后，在执行中，本院依法向被执行人送达了执行通知书、报告财产令、限制消费令。2021年10月28日本院作出（2021）川1421执2553号执行裁定书，裁定：查封被执行人周传龙与案外人邱作祥、胡建琼、胡银楷共同共有的位于简阳市地（不动产权证号：川（2018）简阳市不动产权第0072849），期限为三年。因上述房产系共有财产，无法确定份额，故暂无法进行处置。经全国执行网络查询系统对被执行人的银行存款、互联网银行、不动产、车辆、保险、工商登记等财产信息进行了查询，依法冻结了被执行人银行账号，依法扣划被执行人银行存款4105元，未发现可供执行财产。本院另向仁寿县不动产登记中心、仁寿县综治网格中心对被执行人的下落及财产情况进行调查，均未发现可供执行的财产线索。本院依法将被执行人限制高消费。现鉴于被执行人暂无可供执行的财产，本院决定依职权终结（2020）川1421刑初381号刑事判决书并处判周传龙罚金895元财产刑的本次执行程序。被执行人尚未履行生效法律文书确定的罚金895元财产刑全部义务。依照《中华人民共和国民事诉讼法》第二百五十七条、《最高人民法院关于适用〈中华人民共和国民事诉讼法〉的解释》第五百一十九条之规定，裁定如下：",
            null,
            "罚金案例"
        ));

        testDataList.add(new SqlTestData(
            "1cd7e1a0256242c0809feb65b9fe78490",
            null, // case_reason为null
            "刘海波1.doc",
            "null", // content为null
            null,
            "文档案例"
        ));

        testDataList.add(new SqlTestData(
            "1caec9886b5f1910a65a01ba0aa4318f0",
            null, // case_reason为null
            "(2020)赣刑更329号_刑事裁定书",
            "罪犯彭建宏，男，****年**月**日出生，汉族，江西省景德镇市乐平市人，初中文化。现在江西省赣州监狱服刑。,江西省景德镇市中级人民法院于2016年9月29日作出（2015）景刑一初字第29号刑事判决，认定被告人彭建宏犯贩卖、运输毒品罪，判处死刑，缓期二年执行，剥夺政治权利终身，并处没收个人全部财产，对其限制减刑。宣判后，被告人彭建宏不服，提出上诉。江西省高级人民法院于2018年6月29日作出（2016）赣刑终285号刑事裁定,驳回上诉，维持原判。裁判发生法律效力后交付执行。执行机关江西省赣州监狱于2020年8月5日提出减刑建议书，经江西省监狱管理局审核后，报请本院审理。本院于2020年9月15日受理后依法公示，公示期间没有收到异议，经组成合议庭进行审理，现已审理终结。 执行机关以罪犯彭建宏在死刑缓期执行期间，没有故意犯罪为由，建议对该犯减刑。",
            null,
            "刑事案例"
        ));

        testDataList.add(new SqlTestData(
            "1c99660ff74db3081a9e247264f1e6ab0",
            null, // case_reason为null
            "申请人株洲市天元区人民法院刑事审判庭与被执行人李志周责令退赔纠纷一案终本裁定书",
            "申请执行人：株洲市天元区人民法院刑事审判庭。 被执行人：李志周，男，****年**月**日出生，汉族。 申请人株洲市天元区人民法院,刑事审判庭与被执行人李志周责令退赔纠纷一案，本院作出（2019）湘0211刑初43号刑事判决书，已发生法律效力。由于被执行人未能按期履行生效法律文书确定的义务，本院于2019年8月12日立案执行。 2019年8月21日，本院向被执行人李志周邮寄送达了执行通知书、报告财产令，于2019年8月12日向申请执行人送达受理通知书并要求其提供财产线索。于2019年8月13日发起全国法院执行查控系统，未发现任何有可供执行的财产。于2019年9月6日向株洲市住房公积金管理中心查询被执行人公积金账户，同日向中国人寿股份有限公司株洲分公司查询被执行人收益类保险情况。于2019年9月10日向不动产登记中心查询被执行人的不动产和其他财产情况，均未发现可供执行财产。 2019年9月18日本院对被执行人李志周适用限制高消费措施，并在中国执行信息公开网上公布。 上述执行情况及信息，本院于2019年9月18日通过谈话告知了申请执行人，并要求其提供其他财产线索，但申请执行人未能提供。本院同时依法告知了申请执行人，本案将做终结本次执行程序结案。 本案认为，截止2019年9月18日，本案应当执行标的金额为79.2570万元，尚有债权79.2570万元未执行到位。经本院采取上述执行行为，目前并未发现被执行人有可供执行且适宜处置的财产。申请执行人也未能提供其他可供执行的财产线索。依照《最高人民法院关于适用〈中华人民共和国民事诉讼法〉的解释》第五百一十九条之规定，裁定如下：",
            null,
            "责令退赔案例"
        ));

        testDataList.add(new SqlTestData(
            "1cc42da25b26419fbf457acb98cf76310",
            "[\"侵害商标权纠纷\"]",
            "欧普某公司与朱某侵害商标权纠纷一审民事裁定书",
            "原告：欧普照明股份有限公司，住所地上海市浦东新区龙东大道6111号1幢411室，统一社会信用代码91310000680999558Q。 法定代表人:王耀海，该公司董事长。 委托诉讼代理人：翟明跃，山东昌平律师事务所律师。 被告：朱绍强，男，****年**月**日出生，汉族，住**。,原告欧普照明股份有限公司与被告朱绍强侵害商标权纠纷一案，本院于2024年4月10日立案。原告欧普照明股份有限公司于2024年6月27日向本院申请撤诉。",
            null,
            "商标权纠纷案例"
        ));

        testDataList.add(new SqlTestData(
            "1cc09019c22c44d5a1ec037b49c9affc0",
            "[\"行纪合同纠纷\"]",
            "青海安意房地产中介服务有限公司、常立娟中介合同纠纷民事一审民事裁定书",
            "原告：青海安意房地产中介服务有限公司，住所：青海省西宁市城北区柴达木路351号16号楼1单元1011室。 法定代表人：雷菊林，该公司总经理。 被告：常立娟，女，****年**月**日出生，汉族，住**。 委托诉讼代理人：宋长银，男，****年**月**日出生，汉族，住青海省西宁市城北区。,原告青海安意房地产中介服务有限公司与被告常立娟中介合同纠纷一案，本院于2023年4月11日立案。原告青海安意房地产中介服务有限公司于2023年5月19日向本院提出撤诉申请。",
            null,
            "中介合同纠纷案例"
        ));

        return testDataList;
    }

    /**
     * 解析组合内容为标题和正文
     */
    private String[] parseCombinedContent(String combinedContent) {
        if (combinedContent == null || combinedContent.trim().isEmpty()) {
            return new String[]{"", ""};
        }

        // 简单的解析逻辑：前50个字符作为标题，其余作为正文
        if (combinedContent.length() <= 50) {
            return new String[]{combinedContent, ""};
        } else {
            return new String[]{combinedContent.substring(0, 50), combinedContent.substring(50)};
        }
    }

    @Test
    public void testBoundaryAndExceptionCases() {
        System.out.println("\n--- 边界情况和异常情况测试 ---");

        // 测试null值
        testCase("边界测试-全null", null, null, null, "兜底案由");

        // 测试空字符串
        testCase("边界测试-全空字符串", "", "", "", "兜底案由");

        // 测试只有空格
        testCase("边界测试-只有空格", "   ", "   ", "   ", "兜底案由");

        // 测试超长字符串
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 100; i++) {
            sb.append("这是一个非常长的字符串");
        }
        String longString = sb.toString();
        testCase("边界测试-超长字符串", longString, longString, longString, null);

        // 测试特殊字符
        testCase("边界测试-特殊字符", "[\"特殊@#$%^&*()字符\"]", "标题包含特殊字符@#$%", "正文包含特殊字符@#$%", null);

        // 测试JSON格式错误
        testCase("边界测试-JSON格式错误", "[\"未闭合的JSON", "正常标题", "正常正文", null);

        // 测试匹配优先级：正文 > 标题 > 爬虫案由
        testCase("优先级测试-正文优先",
                "[\"合同纠纷\"]",
                "买卖合同纠纷案",
                "这是一个劳动争议案件的详细描述",
                null);

        // 测试合同纠纷的特殊逻辑
        testCase("合同纠纷特殊逻辑测试",
                "[\"合同纠纷\"]",
                "某某合同纠纷案",
                "这是一个涉及合同纠纷的案件，双方就合同履行产生争议",
                null);

        // 测试短于3个字符的原始案由（应该被跳过）
        testCase("短案由测试",
                "[\"买\"]",
                "短案由测试",
                "测试短于3个字符的案由",
                null);

        // 测试多个匹配项
        testCase("多匹配项测试",
                "[\"买卖合同纠纷\", \"劳动争议\"]",
                "买卖合同纠纷与劳动争议",
                "这个案件同时涉及买卖合同纠纷和劳动争议",
                null);
    }

    @Test
    public void testPerformance() {
        System.out.println("\n--- 性能测试 ---");

        int testCount = 100;
        long totalTime = 0;

        String testCaseReason = "[\"买卖合同纠纷\"]";
        String testTitle = "张某与李某买卖合同纠纷案";
        String testContent = "原告张某与被告李某因买卖合同产生纠纷，现诉至法院要求被告履行合同义务并赔偿损失。";

        for (int i = 0; i < testCount; i++) {
            long start = System.currentTimeMillis();
            matcher.evaluate(testCaseReason, testTitle, testContent);
            totalTime += (System.currentTimeMillis() - start);
        }

        double avgTime = (double) totalTime / testCount;
        System.out.printf("性能测试结果：%d次调用，总时间：%dms，平均时间：%.2fms\n", testCount, totalTime, avgTime);

        // 性能断言：平均执行时间应该小于10ms
        assertTrue("平均执行时间应该小于10ms，实际：" + avgTime + "ms", avgTime < 10);
    }

    @Test
    public void generateTestReport() {
        System.out.println("\n=== 测试报告生成 ===");

        // 先执行所有测试
        testExistingMainMethodCases();
        testSqlBasedTestData();
        testBoundaryAndExceptionCases();
        testPerformance();

        // 生成报告
        generateDetailedReport();
    }

    /**
     * 生成详细的测试报告
     */
    private void generateDetailedReport() {
        long totalTime = System.currentTimeMillis() - startTime;
        int totalTests = testResults.size();
        int passedTests = (int) testResults.stream().filter(r -> r.passed).count();
        int failedTests = totalTests - passedTests;
        double passRate = totalTests > 0 ? (double) passedTests / totalTests * 100 : 0;

        StringBuilder separator = new StringBuilder();
        for (int i = 0; i < 80; i++) {
            separator.append("=");
        }
        System.out.println("\n" + separator.toString());
        System.out.println("                        CaseReasonMatcher 测试报告");
        System.out.println(separator.toString());
        System.out.println("测试开始时间: " + new Date(startTime));
        System.out.println("测试结束时间: " + new Date());
        System.out.println("总执行时间: " + totalTime + "ms");
        System.out.println();

        System.out.println("测试统计:");
        System.out.println("  总测试数: " + totalTests);
        System.out.println("  通过数: " + passedTests);
        System.out.println("  失败数: " + failedTests);
        System.out.println("  通过率: " + String.format("%.2f%%", passRate));
        System.out.println();

        if (failedTests > 0) {
            System.out.println("失败测试详情:");
            testResults.stream()
                    .filter(r -> !r.passed)
                    .forEach(r -> {
                        System.out.println("  ✗ " + r.testName);
                        System.out.println("    输入: " + r.input);
                        System.out.println("    期望: " + r.expected);
                        System.out.println("    实际: " + r.actual);
                        System.out.println("    错误: " + r.errorMessage);
                        System.out.println("    执行时间: " + r.executionTime + "ms");
                        System.out.println();
                    });
        }

        System.out.println("性能指标:");
        double avgExecutionTime = testResults.stream()
                .mapToLong(r -> r.executionTime)
                .average()
                .orElse(0.0);
        long maxExecutionTime = testResults.stream()
                .mapToLong(r -> r.executionTime)
                .max()
                .orElse(0);
        long minExecutionTime = testResults.stream()
                .mapToLong(r -> r.executionTime)
                .min()
                .orElse(0);

        System.out.println("  平均执行时间: " + String.format("%.2fms", avgExecutionTime));
        System.out.println("  最大执行时间: " + maxExecutionTime + "ms");
        System.out.println("  最小执行时间: " + minExecutionTime + "ms");
        System.out.println();

        System.out.println("发现的问题和改进建议:");
        analyzeResults();

        System.out.println(separator.toString());
    }

    /**
     * 分析测试结果并提供改进建议
     */
    private void analyzeResults() {
        List<String> issues = new ArrayList<>();
        List<String> suggestions = new ArrayList<>();

        // 分析性能问题
        double avgTime = testResults.stream()
                .mapToLong(r -> r.executionTime)
                .average()
                .orElse(0.0);

        if (avgTime > 5) {
            issues.add("平均执行时间较长: " + String.format("%.2fms", avgTime));
            suggestions.add("考虑优化匹配算法，使用更高效的字符串匹配方法");
        }

        // 分析失败率
        double failureRate = testResults.stream()
                .mapToDouble(r -> r.passed ? 0 : 1)
                .average()
                .orElse(0.0) * 100;

        if (failureRate > 10) {
            issues.add("测试失败率较高: " + String.format("%.2f%%", failureRate));
            suggestions.add("需要检查匹配逻辑的正确性和完整性");
        }

        // 分析异常情况
        long exceptionCount = testResults.stream()
                .filter(r -> r.errorMessage != null)
                .count();

        if (exceptionCount > 0) {
            issues.add("存在异常情况: " + exceptionCount + "个测试用例抛出异常");
            suggestions.add("需要增强异常处理机制，确保程序的健壮性");
        }

        if (issues.isEmpty()) {
            System.out.println("  ✓ 未发现明显问题，测试表现良好");
        } else {
            System.out.println("  发现的问题:");
            issues.forEach(issue -> System.out.println("    - " + issue));
            System.out.println();
            System.out.println("  改进建议:");
            suggestions.forEach(suggestion -> System.out.println("    - " + suggestion));
        }
    }
}
