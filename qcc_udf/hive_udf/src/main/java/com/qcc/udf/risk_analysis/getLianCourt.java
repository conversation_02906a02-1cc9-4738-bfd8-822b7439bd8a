package com.qcc.udf.risk_analysis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class getLianCourt extends UDF {

    public String evaluate(List<String> infoList) {
        JSONArray array= new JSONArray();

        Map<String, Integer> map = new LinkedHashMap<>();
        Map<String, String> mapId = new LinkedHashMap<>();
        for (String str : infoList){
            String[] info = str.split(",");
            String court = info[0];
            String id = info[1];
            if (!map.containsKey(court)){
                map.put(court, 1);
            }else{
                map.put(court, map.get(court) + 1);
            }

            if (!mapId.containsKey(court)){
                mapId.put(court, id);
            }else{
                mapId.put(court, mapId.get(court).concat(",").concat(id));
            }
        }

        Set<String> idSet = map.keySet();
        for (String str : idSet){
            array.add(str.concat("#FGF#").concat(map.get(str).toString()).concat("#FGF#").concat(mapId.get(str)));
        }

        return array.toString();
    }

    public static void main(String[] args) {
        getLianCourt aa = new getLianCourt();
        List<String> infoList = JSON.parseArray("[\"3,5bb63335835afee109b0c0f6da8ea080\",\"2,228c86be00bf96ddba07e17b2bdccbd5\",\"2,994011969b54c98e4b21dca4177fac1c\",\"2,f3a32234d81819aa34c65ed64039f4e3\",\"2,b6b83562bf3fc2fe0efe79eae08aca35\",\"3,937968b1f9ab018ab9af376c6dd919d6\",\"3,78f81933addf851d4aa09d4e4e3fe755\",\"2,847c4d41bb28547f39647bb89aa6e9aa\"]", String.class);
        System.out.println(aa.evaluate(infoList));
    }
}
