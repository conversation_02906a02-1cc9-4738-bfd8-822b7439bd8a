package com.qcc.udf.cpws;

import com.qcc.udf.tax.CommonUtils;
import com.qcc.udf.tax.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;

/**
 * <AUTHOR>
 */
public class CaseShangHaiTimeUDF extends UDF {
    public static String evaluate(String input, String fmtContent) {
        if (StringUtils.isEmpty(input) && StringUtils.isEmpty(fmtContent)) {
            return "";
        }
        if(StringUtils.isNotEmpty(fmtContent)){
            Document document = Jsoup.parse(fmtContent.replace("\\n","").replace("\\",""));
            String judgeDate = "";
            judgeDate = getSegmentByClassName(document, "qcc_law_judge_date");
            if (StringUtils.isNotEmpty(judgeDate)) {
                input = judgeDate;
            }
        }

        String publishDate = CommonUtils.cleanDate(input);
        if (StringUtils.isNotEmpty(publishDate)) {
            return DateUtil.parseDateToStr(DateUtil.strToDate(publishDate), DateUtil.DATE_FORMAT_YYYY_MM_DD);
        }
        return "";
    }

    /**
     * 获取html文档对应的className部分
     *
     * @param document  html文档
     * @param className 样式类名
     * @return
     */
    public static String getSegmentByClassName(Document document, String className) {
        if (document != null && StringUtils.isNotBlank(className)) {
            Elements elements = document.getElementsByClass(className);
            if (elements != null && elements.size() > 0) {
                String segment = elements.get(0).toString();
                if (StringUtils.isNotBlank(segment)) {
                    return HtmlToTextUtil.convert(segment).replace("\n", "").trim();
                }
            }
        }
        return "";
    }

    public static void main(String[] args) {
        System.out.println(evaluate("二零二二年二月十五日附记：公告方式登报。审判员徐菁","<div class=\\\"qcc_law_doc\\\"><div class=\\\"qcc_law_case_no\\\"> \\n <span>公告案号：（2022）沪0120民初730号</span>\\n</div><div class=\\\"qcc_law_court\\\"> \\n <span><a href=\\\"https://www.qcc.com/firm_g322b6a4899078722bbb5ce095b21e57.html\\\" target=\\\"_blank\\\">上海市奉贤区人民法院</a></span>\\n</div><div class=\\\"qcc_law_judge_party_label\\\"><span>当事人信息</span></div>\\n<div class=\\\"qcc_law_judge_party\\\"> \\n \\n <span>公告</span>\\n \\n</div>\\n <div class=\\\"qcc_law_judge_trial_label\\\"><span>审理经过</span></div>\\n <div class=\\\"qcc_law_judge_trial\\\">\\n \\n <span>（2022）沪0120民初730号</span>\\n \\n <span>、<a href=\\\"https://www.qcc.com/pl_pfbe1b4b27328426bd5719790643a558.html\\\" target=\\\"_blank\\\">彭锡群</a>：</span>\\n \\n <span>本院受理原告与被告、<a href=\\\"https://www.qcc.com/pl_pfbe1b4b27328426bd5719790643a558.html\\\" target=\\\"_blank\\\">彭锡群</a>买卖合同纠纷一案，因你们现址不明，采用其他方式无法送达，现依法向你公告送达起诉状副本、应诉通知书、举证通知书及开庭传票等。</span>\\n \\n </div>\\n\\n\\n <div class=\\\"qcc_law_judge_result_label\\\"><span>裁判结果</span></div>\\n \\n <div class=\\\"qcc_law_judge_result\\\">\\n \\n <span>自公告之日起经过30日，即视为送达。提出答辩状和举证的期限为公告期满后15日和30日。并定于举证期满后第2日上午9时00分在本院第24法庭公开开庭审理，逾期将依法缺席裁判。</span>\\n \\n </div>\\n\\n<div class=\\\"qcc_law_judge_date_label\\\"><span>裁判日期</span></div>\\n<div class=\\\"qcc_law_judge_date\\\">\\n <span>二零二二年二月十五日附记：公告方式登报。审判员徐菁</span>\\n</div>\\n\\n<div class=\\\"qcc_law_judge_recorder_label\\\"><span>书记员及法官助理</span></div>\\n<div class=\\\"qcc_law_judge_recorder\\\">\\n \\n <span>书记员计晓磊</span>\\n \\n</div>\\n</div>\""));
    }
}
