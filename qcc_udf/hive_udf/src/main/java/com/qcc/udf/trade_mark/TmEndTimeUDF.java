package com.qcc.udf.trade_mark;

import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 */
public class TmEndTimeUDF extends UDF {
    public static void main(String[] args) {
        System.out.println(TmEndTimeUDF.evaluate("2020-01-01 至 2020-02-09"));
    }


    public static Long evaluate(String dateStr) {
        if (StringUtils.isEmpty(dateStr) ||
                StringUtils.isEmpty(dateStr.trim().replace("至", ""))) {
            return 0L;
        }
        try {
            String[] strs = dateStr.split("至");
            if (strs.length == 2) {
                String end = strs[1].trim();
                return LocalDateTime.parse(end + " 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).toEpochSecond(ZoneOffset.of("+8"));
            }
        } catch (Exception e) {
            return 0L;
        }
        return 0L;
    }
}
