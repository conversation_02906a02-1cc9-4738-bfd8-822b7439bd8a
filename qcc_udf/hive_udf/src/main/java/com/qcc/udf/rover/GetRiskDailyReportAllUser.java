//package com.qcc.udf.rover;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import kong.unirest.Unirest;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.hadoop.hive.ql.exec.UDF;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR>
// * @date ：Created in 2022/01/17 16:40
// * @description ：获取rover日报所有需要推送的用户
// */
//public class GetRiskDailyReportAllUser extends UDF {
//
//    private static final String NEED_REPORT_USERS_URL = "http://qcc-risk-app-risk-api.sit.office.greatld.com/Rover/Report/NeedReportUsers";
//    private static final String JSON_KEY = "UserIds";
//
//    public static String evaluate(String date) {
//        String result = "";
//        if (StringUtils.isNotEmpty(date)) {
//            String strResponse = Unirest.post(NEED_REPORT_USERS_URL)
//                    .header("Content-Type", "application/json; charset=UTF-8").asString().getBody();
//
//            List<String> userIds = new ArrayList<>();
//
//            if (StringUtils.isNotEmpty(strResponse)) {
//                JSONObject jsonObject = JSON.parseObject(strResponse);
//                if (jsonObject.containsKey(JSON_KEY)) {
//                    JSONArray jsonArray = jsonObject.getJSONArray(JSON_KEY);
//                    for (int i=0; i <jsonArray.size(); i ++) {
//                        userIds.add(jsonArray.getString(i));
//                    }
//                }
//            }
//            if (CollectionUtils.isNotEmpty(userIds)) {
//                result = userIds.stream().collect(Collectors.joining(","));
//            }
//        }
//        return result;
//    }
//
//    public static void main(String[] args) {
//        System.out.println("值为=" + evaluate("20220117"));
//    }
//}
