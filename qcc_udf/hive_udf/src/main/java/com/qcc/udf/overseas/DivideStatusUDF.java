package com.qcc.udf.overseas;

import com.qcc.udf.overseas.constant.Time;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 业务UDF（海外企业）将状态字段切分为状态以及状态变更日期两个维度
 * ---------------------------------------------------------------------------------------------------------
 * add jar hdfs://ldh/data/hive/udf/qcc_udf.jar;
 * create temporary function DivideStatus as 'com.qcc.udf.overseas.DivideStatusUDF';
 * ---------------------------------------------------------------------------------------------------------
 * select DivideStatus ('ADMIN DISSOLVED 04 Jun 2009');
 * 结果: ['ADMIN DISSOLVED', '']
 * select DivideStatus ('ACTIVE');
 * 结果: ['ACTIVE', '1244044800000']
 */
public class DivideStatusUDF extends UDF {

    public List<String> evaluate(String input) {
        List<String> statusList = new ArrayList<>();
        try {
            if (StringUtils.isNotBlank(input)) {
                if (Pattern.matches("^\\d.*$", input)) {    // 过滤掉以数字开头的情况
                    statusList = Arrays.asList("", "");
                } else {
                    if (!Pattern.matches(".*\\d.*", input)) {
                        statusList = Arrays.asList(input, "");
                    } else {    // 当包含数字信息时，尝试提取日期信息并转为时间戳
                        statusList = parseTimestampFromMessage(input);
                    }
                }
            } else {
                statusList = Arrays.asList("", "");
            }
        } catch (Exception e) {

        }
        return statusList;
    }

    private static List<String> parseTimestampFromMessage(String input) {
        List<String> itemList = Arrays.asList(StringUtils.split(input, " |(|)")).stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        if (itemList.size() <= 3) { // 切分后的元素不多于3个，则置为空
            return Arrays.asList("" ,"");
        } else {
            String status = StringUtils.join(itemList.stream().limit(itemList.size()-3).toArray(), " ");
            String dateInfo = StringUtils.join(itemList.stream().skip(itemList.size()-3).limit(3).toArray(), " ");

            String date = "";
            if (dateInfo.contains(",")) {
                date = CommonUtil.getTimestampFromDateField(dateInfo, Time.Formatter_T5);
            } else {
                date = CommonUtil.getTimestampFromDateField(dateInfo, Time.Formatter_T4);
            }
            return Arrays.asList(status, date);
        }
    }
}
