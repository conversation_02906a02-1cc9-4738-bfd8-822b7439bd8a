package com.qcc.udf.cpws.search;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

/**
 * <AUTHOR>
 */
public class getJudgeResultSearch2 extends UDF {

    public String evaluate(String keynos, String caseRole) {
        String result = "";

        Set<String> resultSet = new LinkedHashSet<>();
        Map<String, String> roleMap = new LinkedHashMap<>();
        if (StringUtils.isNotEmpty(caseRole)){
            try {
                JSONArray array = JSONArray.parseArray(caseRole);
                Iterator<Object> it = array.iterator();
                while (it.hasNext()){
                    JSONObject json = (JSONObject)it.next();
                    roleMap.put((StringUtils.isNotEmpty(json.getString("N")) ? json.getString("N") : json.getString("P")), json.getString("T") == null ? "" : json.getString("T"));
                }

            }catch (Exception ex){
                ex.printStackTrace();
            }
        }

        if (StringUtils.isNotEmpty(keynos)){
            String[] arr = keynos.split(",");
            for (String str : arr){
                if (roleMap.keySet().contains(str)){
                    String role = roleMap.get(str);
                    if (StringUtils.isNotEmpty(role)){
                        resultSet.add(role);
                    }

                }
            }
        }

        result = StringUtils.join(resultSet, ",");
        return result;
    }

    public static void main(String[] args) {
        System.out.println(new getJudgeResultSearch2().evaluate("莫金凤,",
                "[{\"P\":\"莫金凤\",\"T\":\"8\",\"ShowName\":\"莫**\",\"N\":\"\"},{\"P\":\"粟文炳\",\"T\":\"8\",\"ShowName\":\"粟**\",\"N\":\"\"},{\"P\":\"桂林科创置业有限公司\",\"T\":\"9\",\"ShowName\":\"桂林科创置业有限公司\",\"N\":\"417111f33108562bcca9fd3e07136e1e\"},{\"P\":\"桂林高新技术产业开发总公司\",\"T\":\"9\",\"ShowName\":\"桂林高新技术产业开发总公司\",\"N\":\"dd6680a5bf617aee046683e234ef4c0a\"},{\"P\":\"桂林市高科房地产开发有限责任公司\",\"T\":\"9\",\"ShowName\":\"桂林市高科房地产开发有限责任公司\",\"N\":\"787b5799f7ae80bed5a3d48f722964a5\"},{\"P\":\"桂林福龙房地产开发有限公司\",\"T\":\"9\",\"ShowName\":\"桂林福龙房地产开发有限公司\",\"N\":\"9e744bafba3ef8249e46e20f8df48b3c\"},{\"P\":\"桂林市竣为房地产有限责任公司\",\"T\":\"9\",\"ShowName\":\"桂林市竣为房地产有限责任公司\",\"N\":\"7e802836f5a1553ea026d2cc737f2026\"}]"));
    }
}
