package com.qcc.udf.group;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2021-08-11
 * @description
 */
public class RegexHelper {
    public RegexHelper() {
    }

    public static String getRegexValue(String regexStr, String content) {
        Pattern p = Pattern.compile(regexStr);
        Matcher m = p.matcher(content);
        return m.find() ? m.group("key") : "";
    }

    public static List<String> getGlobalRegex(String regexStr, String content) {
        List<String> strs = new ArrayList();
        if (StringUtils.isEmpty(content)) {
            return strs;
        } else {
            Pattern p = Pattern.compile(regexStr);
            Matcher m = p.matcher(content);

            while (m.find()) {
                strs.add(m.group());
            }

            return strs;
        }
    }

    public static Integer getMatchCount(String regexStr, String content) {
        List<String> result = getGlobalRegex(regexStr, content);
        return result.size();
    }

    public static boolean isMatch(String content, String regexStr) {
        Pattern p = Pattern.compile(regexStr);
        Matcher m = p.matcher(content);
        return m.matches();
    }

    public static boolean isFind(String content, String regexStr) {
        Pattern p = Pattern.compile(regexStr);
        Matcher m = p.matcher(content);
        return m.find();
    }

    public static String replaceAll(String content, Pattern pattern, String replacementTemplate) {
        if (StringUtils.isEmpty(content)) {
            return "";
        } else {
            Matcher matcher = pattern.matcher(content);
            boolean result = matcher.find();
            return result ? content.replaceAll(pattern.pattern(), replacementTemplate) : content;
        }
    }
}
