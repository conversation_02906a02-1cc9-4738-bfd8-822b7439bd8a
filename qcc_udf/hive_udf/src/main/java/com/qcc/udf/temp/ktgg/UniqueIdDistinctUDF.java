package com.qcc.udf.temp.ktgg;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.casesearch_v3.role.NameKeyNoEntity;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 去重规则：
 * 2.优先保留原被告都有值且主体多的数据，其次被告>原告主体多的数据
 * 3.优先保留其他当事人、第三人都有值且主体多的数据，其次其他当事人有值>第三人主体多
 * 4.优先保留标准案由！‘其他’的数据
 * 5.保留采集时间（创建时间）最早的一条
 *
 * @Auther: wuql
 * @Date: 2020/11/17 10:00
 * @Description:
 */
public class UniqueIdDistinctUDF extends UDF {
    public static final String NULL_ARRAY_STRING = "[]";
    public static final String CASE_REASON_STRING = "其他";

    public static void main(String[] args) {
        String json1 ="{\"id\":\"f397808966ad5a3bb8d1ad3a5871a169\",\"create_date\":\"2021-04-28 11:54:40.0\",\"data_status\":5,\"pltf_keyno_array\":\"[]\",\"defd_keyno_array\":\"[{\\\"KeyNo\\\":\\\"c1da33138d891fa238ede4e9b18680aa\\\",\\\"Name\\\":\\\"温州市双马医疗器材销售有限公司\\\",\\\"Org\\\":0,\\\"ShowName\\\":\\\"温州市双马医疗器材销售有限公司\\\"},{\\\"KeyNo\\\":\\\"pbc46b0fe7dfdbceb7c639fef71158f1\\\",\\\"Name\\\":\\\"王天祥\\\",\\\"Org\\\":2,\\\"ShowName\\\":\\\"王天祥\\\"},{\\\"KeyNo\\\":\\\"p3c4b876874f84cf7f65db0133b38372\\\",\\\"Name\\\":\\\"范庆富\\\",\\\"Org\\\":2,\\\"ShowName\\\":\\\"范庆富\\\"}]\",\"tdpt_keyno_array\":\"[]\",\"other_keyno_array\":\"[]\",\"casereason\":\"其他刑事\"}";
//        String json2 ="{\"id\":\"0329af8147b73668d48218526700c69f\",\"create_date\":\"2021-04-28 22:16:21.0\",\"data_status\":2,\"pltf_keyno_array\":\"[]\",\"defd_keyno_array\":\"[{\\\"KeyNo\\\":\\\"c1da33138d891fa238ede4e9b18680aa\\\",\\\"Name\\\":\\\"温州市双马医疗器材销售有限公司\\\",\\\"Org\\\":0,\\\"ShowName\\\":\\\"温州市双马医疗器材销售有限公司\\\"},{\\\"KeyNo\\\":\\\"pbc46b0fe7dfdbceb7c639fef71158f1\\\",\\\"Name\\\":\\\"王天祥\\\",\\\"Org\\\":2,\\\"ShowName\\\":\\\"王天祥\\\"},{\\\"KeyNo\\\":\\\"p3c4b876874f84cf7f65db0133b38372\\\",\\\"Name\\\":\\\"范庆富\\\",\\\"Org\\\":2,\\\"ShowName\\\":\\\"范庆富\\\"}]\",\"tdpt_keyno_array\":\"[]\",\"other_keyno_array\":\"[]\",\"casereason\":\"其他刑事\"}";
//        String json3 ="{\"id\":\"89e0206a75e7cbf1f296ee002ee01130\",\"create_date\":\"2021-05-03 05:46:20.0\",\"data_status\":3,\"pltf_keyno_array\":\"[]\",\"defd_keyno_array\":\"[{\\\"KeyNo\\\":\\\"c1da33138d891fa238ede4e9b18680aa\\\",\\\"Name\\\":\\\"温州市双马医疗器材销售有限公司\\\",\\\"Org\\\":0,\\\"ShowName\\\":\\\"温州市双马医疗器材销售有限公司\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Name\\\":\\\"王天祥\\\",\\\"Org\\\":-1,\\\"ShowName\\\":\\\"王**\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Name\\\":\\\"范庆富\\\",\\\"Org\\\":-1,\\\"ShowName\\\":\\\"范**\\\"}]\",\"tdpt_keyno_array\":\"[]\",\"other_keyno_array\":null,\"casereason\":\"其他刑事\"}";
        List<String> strings = new ArrayList<>();
        strings.add(json1);
//        strings.add(json2);
//        strings.add(json3);
        System.out.println(evaluate(strings));
    }
    public static String evaluate(List<String> infoList) {
        if (CollectionUtils.isEmpty(infoList)) {
            return "";
        }

        List<CaseLawsAnnouncementSession> sessions = new ArrayList<>();
        List<CaseLawsAnnouncementSession> finalSessions = sessions;
        infoList.stream()
                .filter(e -> StringUtils.isNotBlank(e))
                .forEach(e -> {
                    try {
                        CaseLawsAnnouncementSession session = JSONObject.parseObject(e, CaseLawsAnnouncementSession.class);
                        finalSessions.add(session);
                    } catch (Exception ex) {
                    }
                });

        String id = "";
        //2.优先保留原被告都有值且主体多的数据，其次被告主体多的数据（前提条件：原被告有一方有值）
        id = rule2(sessions);
        sessions=resolveList(id,sessions);
        //3.优先保留其他当事人、第三人都有值且主体多的数据，其次其他当事人有值>第三人主体多
        if (StringUtils.isBlank(id)) {
            id = rule3(sessions);
            sessions=resolveList(id,sessions);
        }
        //4.优先保留标准案由！‘其他’的数据
        if (StringUtils.isBlank(id)) {
            id = rule4(sessions);
        }
        //5.保留采集时间（创建时间）最早的一条
        if (StringUtils.isBlank(id)) {
            id = rule5(sessions);
        }

        int dataStatus = 1;

//        //任何一个0 就返回0
//        boolean flag0 = sessions.stream()
//                .filter(e -> e != null)
//                .filter(e -> e.getDataStatus() != null)
//                .anyMatch(e -> Objects.equals(0, e.getDataStatus()));
//        if (flag0) {
//            dataStatus = 0;
//        }
        //任意2 就返回2

//        boolean flag2 = sessions.stream()
//                .filter(e -> e != null)
//                .filter(e -> e.getDataStatus() != null)
//                .anyMatch(e -> Objects.equals(2, e.getDataStatus()));
//        if (flag2) {
//            dataStatus = 2;
//        }
        //否则就是1

        CaseLawsAnnouncementSession maxDs = sessions.stream().max(Comparator.comparing(CaseLawsAnnouncementSession::getDataStatus)).orElse(null);
        if (maxDs!=null){
            dataStatus=maxDs.getDataStatus();
        }
        JSONObject result = new JSONObject();
        result.put("Id", id);
        result.put("DataStatus", dataStatus);
        return result.toString();
    }

    private static List<CaseLawsAnnouncementSession> resolveList(String id, List<CaseLawsAnnouncementSession> sessions) {
        if ("".equals(id)) {
            Integer maxC = sessions.stream().max(Comparator.comparing(CaseLawsAnnouncementSession::getCount)).map(CaseLawsAnnouncementSession::getCount).orElse(null);
            List<CaseLawsAnnouncementSession> resList = sessions.stream().filter(e -> Objects.equals(maxC, e.getCount())).collect(Collectors.toList());

            for (CaseLawsAnnouncementSession it : sessions) {
                //重置count
                it.setCount(-1);
            }
            return resList;
        }
        return sessions;
    }
    public static String rule2(List<CaseLawsAnnouncementSession> sessions) {
        String result = "";
        List<CaseLawsAnnouncementSession> rule2s = sessions.stream()
                .filter(e -> e != null)
                .filter(e -> (StringUtils.isNotBlank(e.getPltfKeynoArray()) && (!Objects.equals(NULL_ARRAY_STRING, e.getPltfKeynoArray())) || (StringUtils.isNotBlank(e.getDefdKeynoArray()) && (!Objects.equals(NULL_ARRAY_STRING, e.getDefdKeynoArray())))))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(rule2s)) {
            return result;
        }
        //仅有一条，直接返回
        if (rule2s.size() == 1) {
            return rule2s.get(0).getId();
        }

        //2.1 原被告都有值
        List<CaseLawsAnnouncementSession> pds = rule2s.stream()
                .filter(e -> e != null)
                .filter(e -> StringUtils.isNotBlank(e.getPltfKeynoArray()) && (!Objects.equals(NULL_ARRAY_STRING, e.getPltfKeynoArray())))
                .filter(e -> StringUtils.isNotBlank(e.getDefdKeynoArray()) && (!Objects.equals(NULL_ARRAY_STRING, e.getDefdKeynoArray())))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(pds)) {
            result = comparePd(pds);
        } else {
            //2.2 原被告只有一方有
            // 2.2.1 有被告
            List<CaseLawsAnnouncementSession> ds = rule2s.stream()
                    .filter(e -> e != null)
                    .filter(e -> StringUtils.isNotBlank(e.getDefdKeynoArray()) && (!Objects.equals(NULL_ARRAY_STRING, e.getDefdKeynoArray())))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ds)) {
                result = compareDefd(ds);
            } else {
                // 2.2.2 仅有原告
                result = compareOnlyPltf(rule2s);
            }
        }
        return result;
    }

    private static String comparePd(List<CaseLawsAnnouncementSession> pds) {
        String reuslt = "";
        //2.1.1 仅有一条，直接返回
        if (pds.size() == 1) {
            return pds.get(0).getId();
        }
        //2.1.2 多条，比较主体数量
        Map<Integer, List<CaseLawsAnnouncementSession>> countMap = pds.stream().filter(e -> e != null)
                .map(session -> {
                    session.setCount(getNameKeyNoCount(session.getPltfKeynoArray()) + getNameKeyNoCount(session.getDefdKeynoArray()));
                    return session;
                })
                .distinct()
                .collect(Collectors.groupingBy(e -> e.getCount(), Collectors.toList()));

        reuslt = getMaxCount(reuslt, countMap);
        return reuslt;
    }

    private static String compareDefd(List<CaseLawsAnnouncementSession> ds) {
        String reuslt = "";
        //仅有一条，直接返回
        if (ds.size() == 1) {
            return ds.get(0).getId();
        }

        Map<Integer, List<CaseLawsAnnouncementSession>> countMap = ds.stream().filter(e -> e != null)
                .map(session -> {
                    session.setCount(getNameKeyNoCount(session.getDefdKeynoArray()));
                    return session;
                })
                .distinct()
                .collect(Collectors.groupingBy(e -> e.getCount(), Collectors.toList()));

        reuslt = getMaxCount(reuslt, countMap);

        return reuslt;
    }

    private static String compareOnlyPltf(List<CaseLawsAnnouncementSession> rule2s) {
        String reuslt = "";
        List<CaseLawsAnnouncementSession> ps = rule2s.stream()
                .filter(e -> e != null)
                .filter(e -> StringUtils.isNotBlank(e.getPltfKeynoArray()) && (!Objects.equals(NULL_ARRAY_STRING, e.getPltfKeynoArray())))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(ps)) {
            return reuslt;
        }

        //仅有一条，直接返回
        if (ps.size() == 1) {
            return ps.get(0).getId();
        }

        Map<Integer, List<CaseLawsAnnouncementSession>> countMap = ps.stream().filter(e -> e != null)
                .map(session -> {
                    session.setCount(getNameKeyNoCount(session.getPltfKeynoArray()));
                    return session;
                })
                .distinct()
                .collect(Collectors.groupingBy(e -> e.getCount(), Collectors.toList()));

        reuslt = getMaxCount(reuslt, countMap);
        return reuslt;
    }

    public static int getNameKeyNoCount(String nameAndKeyNo) {
        List<NameKeyNoEntity> nameAndKeyNoList = new ArrayList<>();
        if (StringUtils.isNotEmpty(nameAndKeyNo) && !NULL_ARRAY_STRING.equals(nameAndKeyNo)) {
            nameAndKeyNoList = JSON.parseArray(nameAndKeyNo, NameKeyNoEntity.class);
        }
        List<NameKeyNoEntity> vKeyNos = nameAndKeyNoList.stream().filter(e -> StringUtils.isNotEmpty(e.getKeyNo())).collect(Collectors.toList());
        return vKeyNos.size();
    }

    public static String rule3(List<CaseLawsAnnouncementSession> sessions) {
        String result = "";
        List<CaseLawsAnnouncementSession> rules = sessions.stream()
                .filter(e -> e != null)
                .filter(e -> (StringUtils.isNotBlank(e.getTdptKeynoArray()) && (!Objects.equals(NULL_ARRAY_STRING, e.getTdptKeynoArray())) || (StringUtils.isNotBlank(e.getOtherKeynoArray()) && (!Objects.equals(NULL_ARRAY_STRING, e.getOtherKeynoArray())))))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(rules)) {
            return result;
        }

        //仅有一条，直接返回
        if (rules.size() == 1) {
            return rules.get(0).getId();
        }

        //2.1 第三人和其他当事人都有值
        List<CaseLawsAnnouncementSession> tos = rules.stream()
                .filter(e -> e != null)
                .filter(e -> StringUtils.isNotBlank(e.getTdptKeynoArray()) && (!Objects.equals(NULL_ARRAY_STRING, e.getOtherKeynoArray())))
                .filter(e -> StringUtils.isNotBlank(e.getTdptKeynoArray()) && (!Objects.equals(NULL_ARRAY_STRING, e.getOtherKeynoArray())))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tos)) {
            result = compareTo(tos);
        } else {
            //2.2 第三人和其他当事人只有一方有
            // 2.2.1 有其他当事人
            List<CaseLawsAnnouncementSession> others = rules.stream()
                    .filter(e -> e != null)
                    .filter(e -> StringUtils.isNotBlank(e.getOtherKeynoArray()) && (!Objects.equals(NULL_ARRAY_STRING, e.getOtherKeynoArray())))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(others)) {
                result = compareOther(others);
            } else {
                // 2.2.2 仅有第三人
                result = compareOnlyTdpt(rules);
            }
        }
        return result;
    }

    private static String compareTo(List<CaseLawsAnnouncementSession> tos) {
        String reuslt = "";
        //2.1.1 仅有一条，直接返回
        if (tos.size() == 1) {
            return tos.get(0).getId();
        }
        //2.1.2 多条，比较主体数量
        Map<Integer, List<CaseLawsAnnouncementSession>> countMap = tos.stream().filter(e -> e != null)
                .map(session -> {
                    session.setCount(getNameKeyNoCount(session.getTdptKeynoArray()) + getNameKeyNoCount(session.getOtherKeynoArray()));
                    return session;
                })
                .distinct()
                .collect(Collectors.groupingBy(e -> e.getCount(), Collectors.toList()));

        reuslt = getMaxCount(reuslt, countMap);

        return reuslt;
    }

    private static String compareOther(List<CaseLawsAnnouncementSession> others) {
        String reuslt = "";
        //仅有一条，直接返回
        if (others.size() == 1) {
            return others.get(0).getId();
        }

        Map<Integer, List<CaseLawsAnnouncementSession>> countMap = others.stream().filter(e -> e != null)
                .map(session -> {
                    session.setCount(getNameKeyNoCount(session.getOtherKeynoArray()));
                    return session;
                })
                .distinct()
                .collect(Collectors.groupingBy(e -> e.getCount(), Collectors.toList()));

        reuslt = getMaxCount(reuslt, countMap);

        return reuslt;
    }

    private static String compareOnlyTdpt(List<CaseLawsAnnouncementSession> rules) {
        String reuslt = "";
        List<CaseLawsAnnouncementSession> tdpts = rules.stream()
                .filter(e -> e != null)
                .filter(e -> StringUtils.isNotBlank(e.getTdptKeynoArray()) && (!Objects.equals(NULL_ARRAY_STRING, e.getTdptKeynoArray())))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(tdpts)) {
            return reuslt;
        }

        //仅有一条，直接返回
        if (tdpts.size() == 1) {
            return tdpts.get(0).getId();
        }

        Map<Integer, List<CaseLawsAnnouncementSession>> countMap = tdpts.stream().filter(e -> e != null)
                .map(session -> {
                    session.setCount(getNameKeyNoCount(session.getTdptKeynoArray()));
                    return session;
                })
                .distinct()
                .collect(Collectors.groupingBy(e -> e.getCount(), Collectors.toList()));

        reuslt = getMaxCount(reuslt, countMap);
        return reuslt;
    }

    private static String getMaxCount(String reuslt, Map<Integer, List<CaseLawsAnnouncementSession>> countMap) {
        Integer countKey = countMap.keySet().stream()
                .sorted((a, b) -> b.compareTo(a))
                .findFirst()
                .orElse(null);
        if (countKey != null) {
            List<CaseLawsAnnouncementSession> ss = countMap.get(countKey);
            if (CollectionUtils.isNotEmpty(ss) && ss.size() == 1) {
                reuslt = ss.get(0).getId();
            }
        }
        return reuslt;
    }

    public static String rule4(List<CaseLawsAnnouncementSession> sessions) {
        String result = "";
        List<CaseLawsAnnouncementSession> rules = sessions.stream()
                .filter(e -> e != null)
                .filter(e -> StringUtils.isNotBlank(e.getCasereason()) && (!e.getCasereason().contains(CASE_REASON_STRING)))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(rules)) {
            return result;
        }
        if (rules.size() == 1) {
            result = rules.get(0).getId();
        } else {
            result = sortCreateDate(rules);
        }
        return result;
    }

    private static String sortCreateDate(List<CaseLawsAnnouncementSession> rules) {
        return rules.stream().filter(e -> e != null)
                .sorted(Comparator.comparing(CaseLawsAnnouncementSession::getCreateDate, Comparator.reverseOrder()))
                .map(CaseLawsAnnouncementSession::getId)
                .findFirst().orElse("");
    }

    public static String rule5(List<CaseLawsAnnouncementSession> sessions) {
        String result = "";
        if (CollectionUtils.isEmpty(sessions)) {
            return result;
        }

        return sortCreateDate(sessions);
    }
}
