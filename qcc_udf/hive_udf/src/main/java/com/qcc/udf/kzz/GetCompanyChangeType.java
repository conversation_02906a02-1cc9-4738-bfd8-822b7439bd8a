package com.qcc.udf.kzz;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * 根据projectName判断变更类型
 *
 * <AUTHOR>
 * @date 2021/9/15
 */
public class GetCompanyChangeType extends UDF {

//    public static void main(String[] args) throws IOException {
//        File sourceFile = new File("C:\\Users\\<USER>\\Desktop\\企业变更分类.txt");
//        File targetFile = new File("C:\\Users\\<USER>\\Desktop\\企业变更分类结果.txt");
//
//        List<String> lines = FileUtils.readLines(sourceFile);
//        List<String> resultLines = new ArrayList<>();
//        for (String line : lines) {
////            String  templine = line.replaceAll("(\\(|（).*(\\)|）)?","");
//            String type = evaluate(line);
////            resultLines.add(type+"\t"+line);
//            resultLines.add(type);
//        }
//        FileUtils.writeLines(targetFile,resultLines);
//
////        String line = "住所所在经济开发区";
////        String type = evaluate(line);
////        System.out.println(type);
//    }


    public static String evaluate(String projectName) {
        String type = "其他";
        try {
            if (StringUtils.isNotBlank(projectName)) {

                //去掉括号内的内容
                projectName = projectName.replaceAll("(\\(|（).*(\\)|）)", "");
                if (projectName.contains("备案")) {
                    return type;
                }
                if(projectName.contains("(")&&!projectName.contains(")")){
                    projectName = projectName.substring(0,projectName.indexOf("("));
                }
                if(projectName.contains("（")&&!projectName.contains("）")){
                    projectName = projectName.substring(0,projectName.indexOf("（"));
                }
                if ((projectName.contains("经营场所") && !projectName.contains("期限") && !projectName.contains("电话"))
                        || projectName.contains("营业场所")
                        || projectName.contains("经营地址")
                        || projectName.contains("营业场所")
                        || projectName.contains("业务场所")
                        || ((projectName.contains("地址") || projectName.contains("住址")) && projectName.contains("变更"))
                        || ((projectName.contains("住所")||projectName.contains("地址")) &&!(projectName.contains("经营者")||projectName.contains("合伙人")||projectName.contains("负责人")||projectName.contains("投资人")||projectName.contains("电子邮件")||projectName.contains("所在经济开发区")))
                        || projectName.equals("住所") || projectName.equals("地址")
                        || projectName.equals("所在行政区划")
                        || projectName.equals("法律文书送达地址")
                        || projectName.equals("驻在地址")
                        || projectName.equals("住所来源")
                        || projectName.contains("企业住所")
                        || projectName.equals("清算组负责人通信地址")
                        || projectName.equals("分支机构地址")
                        || projectName.equals("场所")
                        || projectName.equals("母公司住所")
                        || projectName.equals("驻在场所")
                        || projectName.equals("住所")
                        || projectName.contains("集团地址")
                ) {
                    return "经营场所变更";
                }
                if (projectName.contains("经营范围")
                        || projectName.contains("经营项目变更")) {
                    return "经营范围变更";
                }
                if (projectName.contains("期限") && !(projectName.contains("出资") || projectName.contains("缴付") || projectName.contains("经营场所"))) {
                    return "经营期限变更";
                }
                if ((projectName.contains("电话") && (
                        projectName.contains("联系")
                                || projectName.contains("经营者")
                                || projectName.contains("清算组负责人")
                                || projectName.contains("联络人")
                                || projectName.contains("联系人")
                                || projectName.contains("年检时")
                                || projectName.contains("经营场所")
                                || projectName.contains("所有权人")))
                        || projectName.equals("电话")
                        || projectName.equals("联系人")
                        || projectName.equals("指定联系人")
                        || projectName.equals("所有权人联系方式")
                ) {
                    return "联系方式变更";
                }
                if (projectName.contains("法定代表人")
                        || projectName.contains("内资企业法人")
                        || projectName.contains("法人或者其他组织的执行事务合伙委派代表")
                        ) {
                    return "法定代表人变更";
                }
                if ((projectName.contains("负责人")
                        || projectName.contains("高级管理人员备案")) && !projectName.contains("电话") && !projectName.contains("地址")) {
                    return "负责人变更";
                }

                if (projectName.contains("股东")) {
                    return "股东变更";
                }
                if ((projectName.contains("股权") || projectName.contains("股份"))
                        && projectName.contains("转让")) {
                    return "股权转让";
                }
                if ((projectName.contains("经营者")&&!(projectName.contains("住所")||projectName.contains("地址")))
                ||(projectName.contains("经营者姓名"))) {
                    return "经营者变更";
                }
                if (projectName.contains("投资人") || projectName.contains("投资者")) {
                    return "投资人变更";
                }
                if ((projectName.contains("名称") &&!(projectName.contains("发起人"))&&
                        (
                                projectName.contains("变更")
                                        || projectName.contains("字号")
                                        || projectName.contains("企业")
                                        || projectName.contains("集团")
                                        || projectName.contains("联系人")
                        ))
                        || projectName.equals("名称")
                        || projectName.contains("集团简称")
                        || projectName.contains("编号升级")
                        || projectName.contains("编号升级")
                ) {
                    return "名称变更";
                }
                if (projectName.contains("企业类型")
                        || projectName.contains("主体类型")
                        || projectName.contains("行业类型")
                        || projectName.contains("合伙类型")
                ) {
                    return "企业类型变更";
                }

                if (projectName.contains("注册资本")) {
                    return "注册资本变更";
                }

            }
        } catch (Exception e) {

        }

        return type;
    }
}
