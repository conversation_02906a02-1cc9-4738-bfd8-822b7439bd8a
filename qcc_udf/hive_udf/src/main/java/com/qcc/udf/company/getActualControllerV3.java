package com.qcc.udf.company;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.qcc.udf.company.controller.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class getActualControllerV3 extends UDF {
    /**
     * 获取实际控制人或者控制权最大的
     *
     * @param keyNo        公司keyno
     * @param companyName  公司名称
     * @param partners     公司股东
     * @param multipleOper 法人
     * @param employees    主要人员
     * @param isIpo        是否为上市公司
     * @return
     */
    public String evaluate(String keyNo, String companyName, String partners, String multipleOper, String employees, Integer isIpo) {
        CompanyActualFinal result = new CompanyActualFinal();

        BigDecimal minPercent;
        if (Pattern.matches(".*股份.*公司.*", companyName) || isIpo == 1) {
            minPercent = new BigDecimal("0.3");
        } else {
            minPercent = new BigDecimal("0.1");
        }

        //实际控制人
        CompanyActualController actualController = getComapnyActualController(keyNo, partners, multipleOper, employees, minPercent);
        if (StringUtils.isNotEmpty(actualController.getPkeyno()) || StringUtils.isNotEmpty(actualController.getPcompanyname())) {
            result.setActualControl(actualController);
        }

        List<CompanyPartner> groupList = new ArrayList<>();
        //最大控制权
        CompanyActualController maxControlPower = getComapnyMaxControlPower(keyNo, partners, employees, multipleOper, groupList);
        if (StringUtils.isNotEmpty(maxControlPower.getPkeyno()) || StringUtils.isNotEmpty(maxControlPower.getPcompanyname())) {
            result.setControlPower(maxControlPower);
        }

        result.setGroupList(groupList);

        return JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect);
    }

    /**
     * 获取实际控制人
     *
     * @param keyNo
     * @param partners
     * @param multipleOper
     * @param employees
     * @param minPercent
     * @return
     */
    public CompanyActualController getComapnyActualController(String keyNo, String partners, String multipleOper, String employees, BigDecimal minPercent) {
        CompanyActualController result = new CompanyActualController();

        if (StringUtils.isNotEmpty(keyNo) && StringUtils.isNotEmpty(partners)) {
            List<CompanyPartner> partnerList = JSONObject.parseArray(partners.replace("\\", "\\\\"), CompanyPartner.class);
            for (CompanyPartner partner : partnerList) {
                if (StringUtils.isNotEmpty(partner.control) && !"{}".equals(partner.control)) {
                    CompanyActualFinal companyControl = JSONObject.parseObject(partner.control, CompanyActualFinal.class);
                    if (StringUtils.isNotEmpty(multipleOper) && !"{}".equals(multipleOper) && companyControl.getActualControl() != null) {
                        CompanyMultipleOper companyMultipleOper = JSONObject.parseObject(multipleOper, CompanyMultipleOper.class);
                        if (companyMultipleOper != null && companyMultipleOper.getOperlist() != null && companyMultipleOper.getOperlist().size() > 0) {
                            if (companyMultipleOper.getOperlist().stream().anyMatch(s -> (StringUtils.isNotEmpty(s.getKeyNo()) && StringUtils.isNotEmpty(companyControl.getActualControl().getPkeyno()) && companyControl.getActualControl().getPkeyno().equals(s.getKeyNo()))
                                    || (StringUtils.isEmpty(s.getKeyNo()) && StringUtils.isEmpty(companyControl.getActualControl().getPkeyno()) && full2Half(s.getName()).equals(full2Half(companyControl.getActualControl().getPcompanyname()))))) {
                                partner.setIsoper(1);
                            } else {
                                partner.setIsoper(0);
                            }
                        }
                    }

                    if (companyControl.getActualControl() != null
                            && (!StringUtils.isEmpty(companyControl.getActualControl().getPkeyno()) || !StringUtils.isEmpty(companyControl.getActualControl().getPcompanyname()))) {
                        partner.setActualController(companyControl.getActualControl());
                    } else {
                        CompanyActualController actualController = new CompanyActualController();
                        actualController.setPkeyno("");
                        actualController.setPcompanyname("");
                        actualController.setIsgp(0);
                        actualController.setIsoper(0);
                        partner.setActualController(actualController);
                    }
                } else if (partner.haspartner == 0) {
                    CompanyActualController actualController = new CompanyActualController();
                    actualController.setPkeyno(partner.getPkeyno() == null ? "" : partner.getPkeyno());
                    actualController.setPcompanyname(partner.getPcompanyname());
                    actualController.setStockpercent(partner.getStockpercent());
                    actualController.setKeynopath(partner.getKeynopath());
                    actualController.setIsoper(partner.getIsoper());
                    actualController.setIsgp(partner.getIsgp());
//                    actualController.setGppath(partner.getGppath());
                    actualController.setHasimage(partner.isHasimage());
                    actualController.setFlag(1);
                    partner.setActualController(actualController);
                } else {
                    CompanyActualController actualController = new CompanyActualController();
                    actualController.setPkeyno("");
                    actualController.setPcompanyname("");
                    actualController.setIsgp(0);
                    actualController.setIsoper(0);
                    partner.setActualController(actualController);
                }

                //获取股东实际控制人的职位
                if (partner.getIsoper() == 1) {
                    partner.setJob("法定代表人");
                    partner.setJobLevel(10);
                } else {
                    partner.setJob("");
                    partner.setJobLevel(0);
                }
                if (StringUtils.isNotEmpty(employees) && !"[]".equals(employees) && partner.getActualController() != null) {
                    List<CompanyEmployee> employeeList = JSONObject.parseArray(employees, CompanyEmployee.class);
                    if (employeeList != null && employeeList.size() > 0) {
                        CompanyEmployee employee = employeeList.stream().filter(s -> (StringUtils.isNotEmpty(s.getKeyNo()) && StringUtils.isNotEmpty(partner.getActualController().getPkeyno()) && partner.getActualController().getPkeyno().equals(s.getKeyNo()))
                                || (StringUtils.isEmpty(s.getKeyNo()) && StringUtils.isEmpty(partner.getActualController().getPkeyno()) && full2Half(s.getName()).equals(full2Half(partner.getActualController().getPcompanyname())))).findFirst().orElse(null);
                        if (employee != null) {
                            partner.setJob(employee.getJob());
                            partner.setJobLevel(employee.getJobLevel());
                        }
                    }
                }

                if (StringUtils.isNotEmpty(partner.getActualController().getPkeyno())) {
                    partner.setGroupKey(partner.getActualController().getPkeyno());
                } else if (StringUtils.isNotEmpty(partner.getActualController().getPcompanyname())) {
                    partner.setGroupKey(full2Half(partner.getActualController().getPcompanyname()));
                } else {
                    partner.setGroupKey("");
                }
            }

            //实际控制人聚合
            List<CompanyPartner> groupPartnerList = getGroupPartnerList(partnerList);

            //获取实际控制人
            CompanyPartner finalActual = getActualController(groupPartnerList, minPercent);
            result = getFinalActualController(keyNo, finalActual, partnerList);
        }

        return result;
    }

    /**
     * 获取最大控制权
     *
     * @param keyNo
     * @param partners
     * @param employees
     * @param multipleOper
     * @param groupList
     * @return
     */
    public CompanyActualController getComapnyMaxControlPower(String keyNo, String partners, String employees, String multipleOper, List<CompanyPartner> groupList) {
        CompanyActualController result = new CompanyActualController();

        if (StringUtils.isNotEmpty(keyNo) && StringUtils.isNotEmpty(partners)) {
            List<CompanyPartner> partnerList = JSONObject.parseArray(partners.replace("\\", "\\\\"), CompanyPartner.class);
            for (CompanyPartner partner : partnerList) {
                if (StringUtils.isNotEmpty(partner.control) && !"{}".equals(partner.control)) {
                    CompanyActualFinal companyControl = JSONObject.parseObject(partner.control, CompanyActualFinal.class);
                    if (StringUtils.isNotEmpty(multipleOper) && !"{}".equals(multipleOper) && companyControl.getControlPower() != null) {
                        CompanyMultipleOper companyMultipleOper = JSONObject.parseObject(multipleOper, CompanyMultipleOper.class);
                        if (companyMultipleOper != null && companyMultipleOper.getOperlist() != null && companyMultipleOper.getOperlist().size() > 0) {
                            if (companyMultipleOper.getOperlist().stream().anyMatch(s -> (StringUtils.isNotEmpty(s.getKeyNo()) && StringUtils.isNotEmpty(companyControl.getControlPower().getPkeyno()) && companyControl.getControlPower().getPkeyno().equals(s.getKeyNo()))
                                    || (StringUtils.isEmpty(s.getKeyNo()) && StringUtils.isEmpty(companyControl.getControlPower().getPkeyno()) && full2Half(s.getName()).equals(full2Half(companyControl.getControlPower().getPcompanyname()))))) {
                                partner.setIsoper(1);
                            } else {
                                partner.setIsoper(0);
                            }
                        }
                    }

                    if (companyControl.getControlPower() != null) {
                        partner.setActualController(companyControl.getControlPower());
                    } else {
                        CompanyActualController actualController = new CompanyActualController();
                        actualController.setPkeyno("");
                        actualController.setPcompanyname("");
                        actualController.setIsgp(0);
                        actualController.setIsoper(0);
                        partner.setActualController(actualController);
                    }
                } else if (partner.haspartner == 0) {
                    CompanyActualController actualController = new CompanyActualController();
                    actualController.setPkeyno(partner.getPkeyno() == null ? "" : partner.getPkeyno());
                    actualController.setPcompanyname(partner.getPcompanyname());
                    actualController.setStockpercent(partner.getStockpercent());
                    actualController.setKeynopath(partner.getKeynopath());
                    actualController.setIsoper(partner.getIsoper());
                    actualController.setIsgp(partner.getIsgp());
//                    actualController.setGppath(partner.getGppath());
                    actualController.setHasimage(partner.isHasimage());
                    actualController.setFlag(1);
                    partner.setActualController(actualController);
                } else {
                    CompanyActualController actualController = new CompanyActualController();
                    actualController.setPkeyno("");
                    actualController.setPcompanyname("");
                    actualController.setIsgp(0);
                    actualController.setIsoper(0);
                    partner.setActualController(actualController);
                }

                //获取股东实际控制人的职位
                if (partner.getIsoper() == 1) {
                    partner.setJob("法定代表人");
                    partner.setJobLevel(10);
                } else {
                    partner.setJob("");
                    partner.setJobLevel(0);
                }
                if (StringUtils.isNotEmpty(employees) && !"[]".equals(employees) && partner.getActualController() != null) {
                    List<CompanyEmployee> employeeList = JSONObject.parseArray(employees, CompanyEmployee.class);
                    if (employeeList != null && employeeList.size() > 0) {
                        CompanyEmployee employee = employeeList.stream().filter(s -> (StringUtils.isNotEmpty(s.getKeyNo()) && StringUtils.isNotEmpty(partner.getActualController().getPkeyno()) && partner.getActualController().getPkeyno().equals(s.getKeyNo()))
                                || (StringUtils.isEmpty(s.getKeyNo()) && StringUtils.isEmpty(partner.getActualController().getPkeyno()) && full2Half(s.getName()).equals(full2Half(partner.getActualController().getPcompanyname())))).findFirst().orElse(null);
                        if (employee != null) {
                            partner.setJob(employee.getJob());
                            partner.setJobLevel(employee.getJobLevel());
                        }
                    }
                }

                if (StringUtils.isNotEmpty(partner.getActualController().getPkeyno())) {
                    partner.setGroupKey(partner.getActualController().getPkeyno());
                } else if (StringUtils.isNotEmpty(partner.getActualController().getPcompanyname())) {
                    partner.setGroupKey(full2Half(partner.getActualController().getPcompanyname()));
                } else {
                    partner.setGroupKey("");
                }
            }

            //实际控制人聚合
            List<CompanyPartner> groupPartnerList = getGroupPartnerList(partnerList);

            //获取最大控制权
            result = getMaxControlPower(keyNo, groupPartnerList, partnerList);

            if (CollectionUtils.isNotEmpty(groupPartnerList)) {
                groupList.clear();
                groupList.addAll(groupPartnerList);
            }

            //groupList处理，合伙企业执行事务人的控制权是100%
            if (CollectionUtils.isNotEmpty(groupList) && groupList.size() > 1) {
                if (groupList.get(0).getIsgp() == 1 && groupList.get(1).getIsgp() != 1) {
                    groupList.get(0).setStockpercent(new BigDecimal("1"));
                    groupList.removeIf(s -> s.getIsgp() != 1);
                } else if (groupList.get(0).getIsgp() == 1 && groupList.get(1).getIsgp() == 1) {
                    groupList.clear();
                }
            }

            for (CompanyPartner item : groupList) {
                String groupKey = item.getGroupKey();
                if (StringUtils.isNotEmpty(groupKey)) {
                    String pCompanyName = partnerList.stream().filter(s -> s.getGroupKey().equals(groupKey))
                            .sorted(Comparator.comparing(CompanyPartner::getPcompanyname).reversed())
                            .map(s -> s.getActualController().getPcompanyname()).findFirst().orElse("");
                    item.setPcompanyname(pCompanyName);
                    if (groupKey.equals(full2Half(pCompanyName))) {
                        item.setGroupKey(pCompanyName);
                    }
                }
            }
        }

        return result;
    }

    /**
     * 根据股东实际控制人聚合
     *
     * @param partnerList
     * @return
     */
    private List<CompanyPartner> getGroupPartnerList(List<CompanyPartner> partnerList) {
        List<CompanyPartner> groupPartnerList = new ArrayList<>();

        // 根据GroupKey聚合，实际控制人keyno不为空但是名称为空应该没有实际控制人
        Map<String, List<CompanyPartner>> groupList = partnerList.stream().collect(Collectors.groupingBy(s -> {
            if (StringUtils.isEmpty(s.getActualController().getPcompanyname())
                    || "无实际控制人".equals(s.getActualController().getPcompanyname())
                    || (StringUtils.isNotEmpty(s.getGroupKey()) && Pattern.matches("^[0-9a-f]{32}$", s.getGroupKey()))) {
//                    || (s.getHaspartner() == 1 && StringUtils.isNotEmpty(s.getGroupKey()) && s.getGroupKey().equals(s.getPkeyno()) && Pattern.matches("^[0-9a-f]{32}$", s.getGroupKey()))) {
                return "";
            }
            return s.getGroupKey();
        }));

        groupList.forEach((key, list) -> {
            CompanyPartner item = new CompanyPartner();
            item.setGroupKey(key);
            if (Pattern.matches("^[0-9a-z]{32}$", key)) {
                item.setPkeyno(key);
                list.sort(Comparator.comparingInt(CompanyPartner::getIsgp).thenComparing(CompanyPartner::getStockpercent).thenComparing(CompanyPartner::getPcompanyname).reversed());
                item.setPcompanyname(list.get(0).getActualController().getPcompanyname());
            } else {
                item.setPkeyno("");
                item.setPcompanyname(key);
            }

            BigDecimal stockPercent = new BigDecimal("0");
            for (CompanyPartner s : list) {
                stockPercent = stockPercent.add(s.getStockpercent());
                if (s.getHaspartner() == 1 && StringUtils.isNotEmpty(key)) {
                    String[] actuakPaths = s.getActualController().getKeynopath().split(",");
                    for (int i = 0; i < actuakPaths.length; i++) {
                        String m = s.getKeynopath() + "-" + actuakPaths[i];
                        if (!actuakPaths[i].contains(s.getKeynopath()) && !Arrays.asList(actuakPaths).contains(m)) {
                            actuakPaths[i] = m;
                        }
                    }

                    s.setKeynopath(String.join(",", actuakPaths));
                }
            }

            if (StringUtils.isNotEmpty(key)) {
                String keyNoPaths = list.stream().filter(s -> StringUtils.isNotEmpty(s.getKeynopath()))
                        .map(s -> s.getKeynopath())
                        .distinct().collect(Collectors.joining(","));
                if (StringUtils.isNotEmpty(keyNoPaths)) {
                    keyNoPaths = Arrays.asList(keyNoPaths.split(",")).stream().distinct().sorted(Comparator.comparing(s -> s)).limit(100).collect(Collectors.joining(","));
                    item.setKeynopath(keyNoPaths);
                }
            }

            item.setStockpercent(stockPercent);
            item.setIsoper(list.stream().mapToInt(s -> s.getIsoper()).max().getAsInt());
            item.setIsgp(list.stream().mapToInt(s -> s.getIsgp()).max().getAsInt());
            item.setHasimage(list.stream().map(s -> s.getActualController().getHasimage()).findFirst().orElse(false));
            item.setHaspartner(list.stream().mapToInt(s -> s.getHaspartner()).max().getAsInt());
            item.setDataextend(list.stream().filter(s -> s.getActualController() != null && s.getActualController().getDataextend() != null).map(s -> s.getActualController().getDataextend()).findFirst().orElse(null));
            item.setJob(list.stream().filter(s -> StringUtils.isNotEmpty(s.getJob())).map(s -> s.getJob()).findFirst().orElse(""));
            item.setJobLevel(list.stream().mapToInt(s -> s.getJobLevel()).max().getAsInt());

            groupPartnerList.add(item);
        });

        return groupPartnerList;
    }

    /**
     * 获取实际控制人
     *
     * @param groupPartnerList
     * @param minPercent
     * @return
     */
    private CompanyPartner getActualController(List<CompanyPartner> groupPartnerList, BigDecimal minPercent) {
        CompanyPartner finalActual = null;

        if (groupPartnerList == null || groupPartnerList.size() == 0) {
            return finalActual;
        }

        // 只有一个股东
        if (groupPartnerList.size() == 1) {
            if (groupPartnerList.get(0).getIsgp() == 1 || groupPartnerList.get(0).getStockpercent().compareTo(minPercent) >= 0) {
                finalActual = groupPartnerList.get(0);
            }
        } else {
            // 根据isgp和股比倒序
            groupPartnerList.sort(Comparator.comparingInt(CompanyPartner::getIsgp).thenComparing(CompanyPartner::getStockpercent).thenComparing(CompanyPartner::getJobLevel).reversed());

            CompanyPartner firstActual = groupPartnerList.get(0);
            CompanyPartner secondActual = groupPartnerList.get(1);

            //多gp没有实际控制人
            if (firstActual.getIsgp() == 1 && secondActual.getIsgp() == 1) {
                return finalActual;
            }

            // gp优先，否则是控制权最大的
            if (firstActual.getIsgp() == 1 || firstActual.getStockpercent().compareTo(new BigDecimal("0.5")) > 0) {
                finalActual = firstActual;
            } else if (StringUtils.isNotEmpty(firstActual.getPcompanyname()) && firstActual.getStockpercent().compareTo(minPercent) >= 0) {
                if (firstActual.getStockpercent().compareTo(secondActual.getStockpercent().add(new BigDecimal("0.1"))) >= 0) {
                    finalActual = firstActual;
                } else if (firstActual.getStockpercent().compareTo(secondActual.getStockpercent()) > 0) {
                    if (firstActual.getJobLevel() > 0) {
                        finalActual = firstActual;
                    }
                } else {
                    List<CompanyPartner> matchList = groupPartnerList.stream().filter(s -> s.getStockpercent().compareTo(firstActual.getStockpercent()) == 0).collect(Collectors.toList());
                    matchList.sort(Comparator.comparingInt(CompanyPartner::getJobLevel).reversed());
                    if (matchList.get(0).getJobLevel().compareTo(matchList.get(1).getJobLevel()) > 0) {
                        finalActual = matchList.get(0);
                    }
                }

                //没有实际控制人的分组
                CompanyPartner noActualGroup = groupPartnerList.stream().filter(s -> StringUtils.isEmpty(s.getGroupKey())).findFirst().orElse(null);
                if (finalActual != null && noActualGroup != null && groupPartnerList.size() > 2) {
                    //如果控制权第一>控制权第二+没有控制权
                    List<CompanyPartner> hasActualGroupList = groupPartnerList.stream().filter(s -> StringUtils.isNotEmpty(s.getGroupKey())).collect(Collectors.toList());
                    hasActualGroupList.sort(Comparator.comparing(CompanyPartner::getStockpercent).reversed());
                    if (firstActual.getStockpercent().compareTo(hasActualGroupList.get(1).getStockpercent().add(noActualGroup.getStockpercent())) < 0) {
                        finalActual = null;
                    } else if (firstActual.getJobLevel() == 0
                            && firstActual.getStockpercent().compareTo(hasActualGroupList.get(1).getStockpercent().add(noActualGroup.getStockpercent())) == 0) {
                        finalActual = null;
                    }
                }
            }
        }

        return finalActual;
    }

    /**
     * 拼接实际控制人信息
     *
     * @param finalActual
     * @param partnerList
     * @return
     */
    private CompanyActualController getFinalActualController(String keyNo, CompanyPartner finalActual, List<CompanyPartner> partnerList) {
        CompanyActualController finalActualController = new CompanyActualController();

        if (finalActual == null || StringUtils.isEmpty(finalActual.getPcompanyname())) {
            //如果股东都有实际控制人，但是当前公司没有，实际控制人pkeyno给当前公司keyno
            if (!partnerList.stream().anyMatch(s -> StringUtils.isEmpty(s.getGroupKey()))) {
//                finalActualController.setGppath("");
                finalActualController.setHasimage(false);
                finalActualController.setIsgp(0);
                finalActualController.setIsoper(0);
                finalActualController.setKeynopath("");
                finalActualController.setPkeyno(keyNo);
                finalActualController.setPcompanyname("无实际控制人");
                finalActualController.setStockpercent(new BigDecimal("0"));
                finalActualController.setFlag(1);
            }
            return finalActualController;
        }

        if (!partnerList.stream().anyMatch(s -> StringUtils.isEmpty(s.getGroupKey()) || s.getActualController() == null || s.getActualController().getFlag() == null || s.getActualController().getFlag() == 0)) {
            finalActualController.setFlag(1);
        } else {
            finalActualController.setFlag(0);
        }

        //如果股东有实际控制人拼接Keynopath和Gppath，计算股比
        List<CompanyPartner> list = partnerList.stream()
                .filter(s -> s.getGroupKey().equals(finalActual.getGroupKey()))
                .sorted(Comparator.comparingInt(CompanyPartner::getIsgp).thenComparing(CompanyPartner::getStockpercent).thenComparing(CompanyPartner::getPcompanyname).reversed())
                .collect(Collectors.toList());

        finalActualController.setPkeyno(finalActual.getPkeyno());
        finalActualController.setPcompanyname(list.get(0).getActualController().getPcompanyname());
        finalActualController.setIsoper(finalActual.getIsoper());
        finalActualController.setIsgp(finalActual.getIsgp());
        finalActualController.setHasimage(finalActual.isHasimage());
        finalActualController.setDataextend(finalActual.getDataextend());

        BigDecimal stockPercent = new BigDecimal("0");
        for (CompanyPartner s : list) {
            stockPercent = stockPercent.add(s.getStockpercent());
//            if (s.getHaspartner() == 1) {
//                String[] actuakPaths = s.getActualController().getKeynopath().split(",");
//                for (int i = 0; i < actuakPaths.length; i++) {
//                    String item = s.getKeynopath() + "-" + actuakPaths[i];
//                    if (!actuakPaths[i].contains(s.getKeynopath()) && !Arrays.asList(actuakPaths).contains(item)) {
//                        actuakPaths[i] = item;
//                    }
//                }
//
//                s.setKeynopath(String.join(",", actuakPaths));
//            }
        }
        finalActualController.setStockpercent(stockPercent);

        //如果大股东股比>50%，取大股东的实际控制人路径作为主路径
        String keyNoPaths;
        List<CompanyPartner> gpList = list.stream().filter(s -> s.getIsgp() == 1).collect(Collectors.toList());
        if (gpList != null && gpList.size() >= 1) {
            keyNoPaths = gpList.stream().map(s -> s.getKeynopath()).distinct().collect(Collectors.joining(","));
        } else {
            keyNoPaths = list.stream().map(s -> s.getKeynopath()).distinct().collect(Collectors.joining(","));
        }

        if (StringUtils.isNotEmpty(keyNoPaths)) {
            keyNoPaths = Arrays.asList(keyNoPaths.split(",")).stream().distinct().sorted(Comparator.comparingInt(s -> s.length())).limit(100).collect(Collectors.joining(","));
        }

        finalActualController.setKeynopath(keyNoPaths);
        return finalActualController;
    }

    /**
     * 获取控制权最大
     *
     * @param keyNo
     * @param groupPartnerList
     * @param partnerList
     * @return
     */
    private CompanyActualController getMaxControlPower(String keyNo, List<CompanyPartner> groupPartnerList, List<CompanyPartner> partnerList) {
        CompanyPartner finalActual = null;
        if (groupPartnerList != null && groupPartnerList.size() > 0) {
            // 根据isgp和股比倒序
            groupPartnerList.sort(Comparator.comparingInt(CompanyPartner::getIsgp).thenComparing(CompanyPartner::getStockpercent).thenComparing(CompanyPartner::getJobLevel).reversed());

            CompanyPartner firstActual = groupPartnerList.get(0);
            if (groupPartnerList.size() == 1) {
                finalActual = firstActual;
            } else {
                CompanyPartner secondActual = groupPartnerList.get(1);
                //多gp没有最大控制权
                if (firstActual.getIsgp() == 1 && secondActual.getIsgp() == 1) {
                    finalActual = null;
                } else if (StringUtils.isNotEmpty(firstActual.getPcompanyname())) {
                    if (firstActual.getIsgp() == 1) {
                        finalActual = firstActual;
                        finalActual.setStockpercent(new BigDecimal("1"));
                    } else if (firstActual.getStockpercent().compareTo(new BigDecimal("0.5")) > 0) {
                        finalActual = firstActual;
                    } else if (firstActual.getStockpercent().compareTo(secondActual.getStockpercent()) >= 0) {
                        if (firstActual.getStockpercent().compareTo(secondActual.getStockpercent()) > 0) {
                            finalActual = firstActual;
                        } else {
                            List<CompanyPartner> matchList = groupPartnerList.stream().filter(s -> s.getStockpercent().compareTo(firstActual.getStockpercent()) == 0).collect(Collectors.toList());
                            matchList.sort(Comparator.comparingInt(CompanyPartner::getJobLevel).reversed());
                            if (matchList.get(0).getJobLevel().compareTo(matchList.get(1).getJobLevel()) > 0) {
                                finalActual = matchList.get(0);
                            }
                        }

                        //没有实际控制人的分组
                        CompanyPartner noActualGroup = groupPartnerList.stream().filter(s -> StringUtils.isEmpty(s.getGroupKey())).findFirst().orElse(null);
                        if (finalActual != null && noActualGroup != null && groupPartnerList.size() > 2) {
                            //如果控制权第一>控制权第二+没有控制权
                            List<CompanyPartner> hasActualGroupList = groupPartnerList.stream().filter(s -> StringUtils.isNotEmpty(s.getGroupKey())).collect(Collectors.toList());
                            hasActualGroupList.sort(Comparator.comparing(CompanyPartner::getStockpercent).reversed());
                            if (firstActual.getStockpercent().compareTo(hasActualGroupList.get(1).getStockpercent().add(noActualGroup.getStockpercent())) < 0) {
                                finalActual = null;
                            } else if (firstActual.getJobLevel() == 0
                                    && firstActual.getStockpercent().compareTo(hasActualGroupList.get(1).getStockpercent().add(noActualGroup.getStockpercent())) == 0) {
                                finalActual = null;
                            }
                        }
                    }
                }
            }
        }

        if (finalActual == null || StringUtils.isEmpty(finalActual.getPcompanyname())) {
            //如果股东都有最大控制权，但是当前公司没有，最大控制权pkeyno给当前公司keyno，下一层无需再计算
            if (!partnerList.stream().anyMatch(s -> StringUtils.isEmpty(s.getGroupKey()))) {
                finalActual = new CompanyPartner();
//                finalActual.setGppath("");
                finalActual.setHasimage(false);
                finalActual.setIsgp(0);
                finalActual.setIsoper(0);
                finalActual.setKeynopath("");
                finalActual.setPkeyno(keyNo);
                finalActual.setPcompanyname("无实际控制人");
                finalActual.setStockpercent(new BigDecimal("0"));
            }
        }

        CompanyActualController result = new CompanyActualController();
        if (finalActual != null) {
            String groupKey = finalActual.getGroupKey();
            String pCompanyName = finalActual.getPcompanyname();
            if (StringUtils.isNotEmpty(groupKey)) {
                pCompanyName = partnerList.stream().filter(s -> s.getGroupKey().equals(groupKey))
                        .sorted(Comparator.comparing(CompanyPartner::getPcompanyname).reversed())
                        .map(s -> s.getActualController().getPcompanyname()).findFirst().orElse("");
            }

            result.setPkeyno(finalActual.getPkeyno());
            result.setPcompanyname(pCompanyName);
            result.setHasimage(finalActual.isHasimage());
            result.setStockpercent(finalActual.getStockpercent());
            result.setIsgp(finalActual.getIsgp());
            result.setIsoper(finalActual.getIsoper());
//            result.setGppath(finalActual.getGppath());
            result.setKeynopath(finalActual.getKeynopath());
            result.setDataextend(finalActual.getDataextend());

            if (!partnerList.stream().anyMatch(s -> StringUtils.isEmpty(s.getGroupKey()) || s.getActualController() == null || s.getActualController().getFlag() == null || s.getActualController().getFlag() == 0)) {
                result.setFlag(1);
            } else {
                result.setFlag(0);
            }
        }

        return result;
    }

    /**
     * 全角转半角
     *
     * @param input 输入
     * @return 半角文本
     */
    private String full2Half(String input) {
        if (StringUtils.isEmpty(input)) {
            return "";
        }

        char c[] = input.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == '\u3000') {
                c[i] = ' ';
            } else if (c[i] > '\uFF00' && c[i] < '\uFF5F') {
                c[i] = (char) (c[i] - 65248);
            }
        }
        return new String(c);
    }

    public static void main(String[] args) {
        getActualControllerV3 model = new getActualControllerV3();
        String result = "";
//        result = model.evaluate("fc9ac2c207a8f102d0c1e6d372a501df", "苏州知彼信息科技中心（有限合伙）", "[{\"pkeyno\":\"p668a85d537bd0be245562146439102c\",\"pcompanyname\":\"尹尚文\",\"stockpercent\":0.05,\"org\":2,\"hasimage\":true,\"shouldcapi\":\"5\",\"keynopath\":\"fc9ac2c207a8f102d0c1e6d372a501df\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":0,\"control\":{}},{\"pkeyno\":\"p5319b01bc4e3c7fa6044d6227e3d827\",\"pcompanyname\":\"王金虎\",\"stockpercent\":0.05,\"org\":2,\"hasimage\":false,\"shouldcapi\":\"5\",\"keynopath\":\"fc9ac2c207a8f102d0c1e6d372a501df\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":0,\"control\":{}},{\"pkeyno\":\"pb9ac28faba8285ca3d516ab6a91235f\",\"pcompanyname\":\"陈德强\",\"stockpercent\":0.9,\"org\":2,\"hasimage\":true,\"shouldcapi\":\"90\",\"keynopath\":\"fc9ac2c207a8f102d0c1e6d372a501df\",\"isoper\":1,\"isgp\":1,\"gppath\":\"1\",\"haspartner\":0,\"control\":{}}]", "{\"OperType\":2,\"OperList\":[{\"KeyNo\":\"pb9ac28faba8285ca3d516ab6a91235f\",\"Org\":2,\"HasImage\":true,\"CompanyCount\":7,\"Name\":\"陈德强\"}]}", "[]", 0);
//        result = model.evaluate("f625a5b661058ba5082ca508f99ffe1b", "企查查科技有限公司", "[{\"pkeyno\":\"p78c9e5065ffdd42f396bb8d8165abeb\",\"pcompanyname\":\"施阳\",\"stockpercent\":0.050682,\"org\":2,\"hasimage\":true,\"shouldcapi\":\"253.41\",\"keynopath\":\"f625a5b661058ba5082ca508f99ffe1b\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":0,\"control\":{}},{\"pkeyno\":\"c46316563c5b16bc5e1206575d0e5f4b\",\"pcompanyname\":\"杭州险峰投资合伙企业（有限合伙）\",\"stockpercent\":0.071548,\"org\":0,\"hasimage\":true,\"shouldcapi\":\"357.74\",\"keynopath\":\"f625a5b661058ba5082ca508f99ffe1b\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"actualControl\":{\"flag\":1,\"hasimage\":true,\"isgp\":1,\"isoper\":0,\"keynopath\":\"c46316563c5b16bc5e1206575d0e5f4b-1b59ee4181717f91652e8ac08125a45b\",\"pcompanyname\":\"陈科屹\",\"pkeyno\":\"p91683d0371e3231d8e9988f4930c6d0\",\"stockpercent\":0.014633},\"controlPower\":{\"flag\":1,\"hasimage\":true,\"isgp\":1,\"isoper\":0,\"keynopath\":\"c46316563c5b16bc5e1206575d0e5f4b-1b59ee4181717f91652e8ac08125a45b\",\"pcompanyname\":\"陈科屹\",\"pkeyno\":\"p91683d0371e3231d8e9988f4930c6d0\",\"stockpercent\":0.014633}}},{\"pkeyno\":\"1cdbda7a7a0c38f464df34afdaf1387c\",\"pcompanyname\":\"苏州中新兴富新兴产业投资合伙企业（有限合伙）\",\"stockpercent\":0.03,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"150\",\"keynopath\":\"f625a5b661058ba5082ca508f99ffe1b\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"actualControl\":{\"flag\":1,\"hasimage\":true,\"isgp\":1,\"isoper\":0,\"keynopath\":\"1cdbda7a7a0c38f464df34afdaf1387c-c84d493a6cfbf96688f8a236cd63e6ae,1cdbda7a7a0c38f464df34afdaf1387c-c84d493a6cfbf96688f8a236cd63e6ae-2f5220a822d10d372662625ac1e5d22c-61290a6d4313053436743c5c667526c0\",\"pcompanyname\":\"王廷富\",\"pkeyno\":\"p6656c818e9821ddfac1e66708f77211\",\"stockpercent\":0.3},\"controlPower\":{\"flag\":0,\"hasimage\":true,\"isgp\":1,\"isoper\":0,\"keynopath\":\"1cdbda7a7a0c38f464df34afdaf1387c-1e662bf0dcee6e9a87b03665660dc29a,1cdbda7a7a0c38f464df34afdaf1387c-1e662bf0dcee6e9a87b03665660dc29a-61290a6d4313053436743c5c667526c0,1cdbda7a7a0c38f464df34afdaf1387c-4fbdc6ce806c4c4190454e6969405742-c84d493a6cfbf96688f8a236cd63e6ae,1cdbda7a7a0c38f464df34afdaf1387c-4fbdc6ce806c4c4190454e6969405742-c84d493a6cfbf96688f8a236cd63e6ae-2f5220a822d10d372662625ac1e5d22c,1cdbda7a7a0c38f464df34afdaf1387c-4fbdc6ce806c4c4190454e6969405742-c84d493a6cfbf96688f8a236cd63e6ae-2f5220a822d10d372662625ac1e5d22c-61290a6d4313053436743c5c667526c0,1cdbda7a7a0c38f464df34afdaf1387c-c84d493a6cfbf96688f8a236cd63e6ae,1cdbda7a7a0c38f464df34afdaf1387c-c84d493a6cfbf96688f8a236cd63e6ae-2f5220a822d10d372662625ac1e5d22c,1cdbda7a7a0c38f464df34afdaf1387c-c84d493a6cfbf96688f8a236cd63e6ae-2f5220a822d10d372662625ac1e5d22c-61290a6d4313053436743c5c667526c0\",\"pcompanyname\":\"王廷富\",\"pkeyno\":\"p6656c818e9821ddfac1e66708f77211\",\"stockpercent\":0.3}}},{\"pkeyno\":\"20145ab23917263cce0d1710afc1d2e5\",\"pcompanyname\":\"江苏苏大天宫创业投资管理有限公司\",\"stockpercent\":0.020042,\"org\":0,\"hasimage\":true,\"shouldcapi\":\"100.21\",\"keynopath\":\"f625a5b661058ba5082ca508f99ffe1b\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"actualControl\":{\"flag\":1,\"hasimage\":true,\"isgp\":0,\"isoper\":1,\"keynopath\":\"20145ab23917263cce0d1710afc1d2e5\",\"pcompanyname\":\"袁方\",\"pkeyno\":\"pa7083dcf4720fb2ba9cf2e39ce68037\",\"stockpercent\":0.89688},\"controlPower\":{\"flag\":1,\"hasimage\":true,\"isgp\":0,\"isoper\":1,\"keynopath\":\"20145ab23917263cce0d1710afc1d2e5\",\"pcompanyname\":\"袁方\",\"pkeyno\":\"pa7083dcf4720fb2ba9cf2e39ce68037\",\"stockpercent\":0.89688}}},{\"pkeyno\":\"9ea214142d936872be2554096d8cfa48\",\"pcompanyname\":\"苏州天宫一号投资中心（有限合伙）\",\"stockpercent\":0.010435,\"org\":0,\"hasimage\":true,\"shouldcapi\":\"52.175\",\"keynopath\":\"f625a5b661058ba5082ca508f99ffe1b\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"actualControl\":{\"flag\":1,\"hasimage\":true,\"isgp\":1,\"isoper\":0,\"keynopath\":\"9ea214142d936872be2554096d8cfa48-51a8f97a9763171f768108cb9849f5ea-20145ab23917263cce0d1710afc1d2e5\",\"pcompanyname\":\"袁方\",\"pkeyno\":\"pa7083dcf4720fb2ba9cf2e39ce68037\",\"stockpercent\":0.0375},\"controlPower\":{\"flag\":1,\"hasimage\":true,\"isgp\":1,\"isoper\":0,\"keynopath\":\"9ea214142d936872be2554096d8cfa48-20145ab23917263cce0d1710afc1d2e5,9ea214142d936872be2554096d8cfa48-51a8f97a9763171f768108cb9849f5ea-20145ab23917263cce0d1710afc1d2e5\",\"pcompanyname\":\"袁方\",\"pkeyno\":\"pa7083dcf4720fb2ba9cf2e39ce68037\",\"stockpercent\":0.0375}}},{\"pkeyno\":\"p9e55b5d48140bf14b61c08bd7ef98ec\",\"pcompanyname\":\"杨京\",\"stockpercent\":0.093203,\"org\":2,\"hasimage\":true,\"shouldcapi\":\"466.015\",\"keynopath\":\"f625a5b661058ba5082ca508f99ffe1b\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":0,\"control\":{}},{\"pkeyno\":\"b37fb0fcc4813a17446a9503a12dec67\",\"pcompanyname\":\"宁波小溪聚合文创投资管理中心（有限合伙）\",\"stockpercent\":0.017391,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"86.955\",\"keynopath\":\"f625a5b661058ba5082ca508f99ffe1b\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"actualControl\":{\"flag\":1,\"hasimage\":false,\"isgp\":1,\"isoper\":0,\"keynopath\":\"b37fb0fcc4813a17446a9503a12dec67-3a87a6303dec5c0a91ecb62d749bdc96\",\"pcompanyname\":\"姬兴慧\",\"pkeyno\":\"p1c5dcd1841d57962cf656ec38810036\",\"stockpercent\":0.708807},\"controlPower\":{\"flag\":1,\"hasimage\":false,\"isgp\":1,\"isoper\":0,\"keynopath\":\"b37fb0fcc4813a17446a9503a12dec67-3a87a6303dec5c0a91ecb62d749bdc96,b37fb0fcc4813a17446a9503a12dec67-3a87a6303dec5c0a91ecb62d749bdc96-62a3c5ded3735dc17258eab20426e8c4,b37fb0fcc4813a17446a9503a12dec67-3a87a6303dec5c0a91ecb62d749bdc96-62a3c5ded3735dc17258eab20426e8c4-d329fca871affb4b8ccbc7b512b3a927,b37fb0fcc4813a17446a9503a12dec67-7a953a84558a5a8e511a4e5be62e65c9-ffaef5d4299b1843735adb9e595be8be-d329fca871affb4b8ccbc7b512b3a927\",\"pcompanyname\":\"姬兴慧\",\"pkeyno\":\"p1c5dcd1841d57962cf656ec38810036\",\"stockpercent\":0.708807}}},{\"pkeyno\":\"0e0a6da9c9940af0e623112003c9bd84\",\"pcompanyname\":\"深圳前海燧石鼎天投资合伙企业（有限合伙）\",\"stockpercent\":0.076521,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"382.605\",\"keynopath\":\"f625a5b661058ba5082ca508f99ffe1b\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"actualControl\":{\"flag\":1,\"hasimage\":true,\"isgp\":1,\"isoper\":0,\"keynopath\":\"0e0a6da9c9940af0e623112003c9bd84-cc9dc77082783da95e0f73232bf00e83\",\"pcompanyname\":\"刘朝阳\",\"pkeyno\":\"p5d594e9d257f69673159cbd274f5bf9\",\"stockpercent\":0.009901},\"controlPower\":{\"flag\":1,\"hasimage\":true,\"isgp\":1,\"isoper\":0,\"keynopath\":\"0e0a6da9c9940af0e623112003c9bd84-cc9dc77082783da95e0f73232bf00e83\",\"pcompanyname\":\"刘朝阳\",\"pkeyno\":\"p5d594e9d257f69673159cbd274f5bf9\",\"stockpercent\":0.009901}}},{\"pkeyno\":\"d1b84f508d958bdd374cba6f903686b3\",\"pcompanyname\":\"万得信息技术股份有限公司\",\"stockpercent\":0.18,\"org\":0,\"hasimage\":true,\"shouldcapi\":\"900\",\"keynopath\":\"f625a5b661058ba5082ca508f99ffe1b\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"actualControl\":{\"flag\":1,\"hasimage\":false,\"isgp\":0,\"isoper\":0,\"keynopath\":\"\",\"pcompanyname\":\"无实际控制人\",\"pkeyno\":\"d1b84f508d958bdd374cba6f903686b3\",\"stockpercent\":0},\"controlPower\":{\"flag\":1,\"hasimage\":true,\"isgp\":0,\"isoper\":0,\"keynopath\":\"d1b84f508d958bdd374cba6f903686b3,d1b84f508d958bdd374cba6f903686b3-9950c6a4519042980a57ba2156720c8f\",\"pcompanyname\":\"陆风\",\"pkeyno\":\"pd3eeda3604ea1a2425f9831dd4f06dd\",\"stockpercent\":0.124745}}},{\"pkeyno\":\"fc9ac2c207a8f102d0c1e6d372a501df\",\"pcompanyname\":\"苏州知彼信息科技中心（有限合伙）\",\"stockpercent\":0.123583,\"org\":0,\"hasimage\":true,\"shouldcapi\":\"617.915\",\"keynopath\":\"f625a5b661058ba5082ca508f99ffe1b\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"actualControl\":{\"flag\":1,\"hasimage\":true,\"isgp\":1,\"isoper\":1,\"keynopath\":\"fc9ac2c207a8f102d0c1e6d372a501df\",\"pcompanyname\":\"陈德强\",\"pkeyno\":\"pb9ac28faba8285ca3d516ab6a91235f\",\"stockpercent\":0.9},\"controlPower\":{\"flag\":1,\"hasimage\":true,\"isgp\":1,\"isoper\":1,\"keynopath\":\"fc9ac2c207a8f102d0c1e6d372a501df\",\"pcompanyname\":\"陈德强\",\"pkeyno\":\"pb9ac28faba8285ca3d516ab6a91235f\",\"stockpercent\":0.9}}},{\"pkeyno\":\"bf092311183147c7c96fe950209ccfb4\",\"pcompanyname\":\"深圳市创东方互联网金融投资企业（有限合伙）\",\"stockpercent\":0.038261,\"org\":0,\"hasimage\":true,\"shouldcapi\":\"191.305\",\"keynopath\":\"f625a5b661058ba5082ca508f99ffe1b\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"actualControl\":{\"flag\":1,\"hasimage\":true,\"isgp\":1,\"isoper\":0,\"keynopath\":\"bf092311183147c7c96fe950209ccfb4-4fc2e5da2bbd28bcad72a1f8d7bcd70a,bf092311183147c7c96fe950209ccfb4-4fc2e5da2bbd28bcad72a1f8d7bcd70a-b1de183da1740ce5a46ced8b9f43fbfb,bf092311183147c7c96fe950209ccfb4-4fc2e5da2bbd28bcad72a1f8d7bcd70a-7496c8c559fdae6c653322c412d8fddf\",\"pcompanyname\":\"肖水龙\",\"pkeyno\":\"p6db6c313b4d350ac6d69635e6f2211c\",\"stockpercent\":0.144385},\"controlPower\":{\"flag\":1,\"hasimage\":true,\"isgp\":1,\"isoper\":0,\"keynopath\":\"bf092311183147c7c96fe950209ccfb4,bf092311183147c7c96fe950209ccfb4-4fc2e5da2bbd28bcad72a1f8d7bcd70a,bf092311183147c7c96fe950209ccfb4-4fc2e5da2bbd28bcad72a1f8d7bcd70a-7496c8c559fdae6c653322c412d8fddf,bf092311183147c7c96fe950209ccfb4-4fc2e5da2bbd28bcad72a1f8d7bcd70a-b1de183da1740ce5a46ced8b9f43fbfb\",\"pcompanyname\":\"肖水龙\",\"pkeyno\":\"p6db6c313b4d350ac6d69635e6f2211c\",\"stockpercent\":0.144385}}},{\"pkeyno\":\"pb9ac28faba8285ca3d516ab6a91235f\",\"pcompanyname\":\"陈德强\",\"stockpercent\":0.288334,\"org\":2,\"hasimage\":true,\"shouldcapi\":\"1441.67\",\"keynopath\":\"f625a5b661058ba5082ca508f99ffe1b\",\"isoper\":1,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":0,\"control\":{}}]", "{\"OperType\":1,\"OperList\":[{\"KeyNo\":\"pb9ac28faba8285ca3d516ab6a91235f\",\"Org\":2,\"HasImage\":true,\"CompanyCount\":7,\"Name\":\"陈德强\"}]}", "[{\"KeyNo\":\"pb9ac28faba8285ca3d516ab6a91235f\",\"Name\":\"陈德强\",\"Job\":\"董事长\",\"JobLevel\":50}]", 0);
        result = model.evaluate("0b96f78c25b330d67beed6fd906732e0", "银川市兴庆区（北）信宇声像电子服务部", "[{\"pkeyno\":\"pc921053bdae548011b9a450e7cad3d8\",\"pcompanyname\":\"任秀秀\",\"stockpercent\":0.966667,\"org\":2,\"hasimage\":false,\"shouldcapi\":\"2.9\",\"keynopath\":\"0b96f78c25b330d67beed6fd906732e0\",\"isoper\":1,\"isgp\":1,\"gppath\":\"1\",\"haspartner\":0,\"control\":{}},{\"pkeyno\":\"p07f2f228fa16d748cfb302b4ca80870\",\"pcompanyname\":\"朱生录\",\"stockpercent\":0.033333,\"org\":2,\"hasimage\":false,\"shouldcapi\":\"0.1\",\"keynopath\":\"0b96f78c25b330d67beed6fd906732e0\",\"isoper\":1,\"isgp\":1,\"gppath\":\"1\",\"haspartner\":0,\"control\":{}}]", "{\"OperType\":2,\"OperList\":[{\"KeyNo\":\"pc921053bdae548011b9a450e7cad3d8\",\"Org\":2,\"HasImage\":false,\"CompanyCount\":2,\"Name\":\"任秀秀\"},{\"KeyNo\":\"p07f2f228fa16d748cfb302b4ca80870\",\"Org\":2,\"HasImage\":false,\"CompanyCount\":1,\"Name\":\"朱生录\"}]}", "[]", 0);
    }
}
