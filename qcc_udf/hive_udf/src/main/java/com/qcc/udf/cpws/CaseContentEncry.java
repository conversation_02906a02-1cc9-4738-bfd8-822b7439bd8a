package com.qcc.udf.cpws;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.List;

public class CaseContentEncry extends UDF {
    public  String evaluate(String names, String text) {
        if (StringUtils.isEmpty(names) || StringUtils.isEmpty(text)) {
            return text;
        }

        List<String> acompany = Arrays.asList(names.split(","));

        if (CollectionUtils.isNotEmpty(acompany)) {
            for (String s : acompany) {
                if (names.matches("[xX]+")) {
                } else {
                    StringBuilder x = new StringBuilder();
                    for (int i = 0; i < (s.length() - 1); i++) {
                        x.append("*");
                    }
                    if (x.length() > 2) {
                        x = new StringBuilder(x.substring(0, 2));
                    }
                    String name = s.substring(0, 1) + x.toString();
                    text = text.replace(s, name);
                }
            }
        }
        return text;
    }

//    public static void main(String[] args) {
//        System.out.println( evaluate("刘xxxsfasd","法定代表人：刘xxxsfasd"));
//    }
}
