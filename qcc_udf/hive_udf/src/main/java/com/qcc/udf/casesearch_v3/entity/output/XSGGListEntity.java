package com.qcc.udf.casesearch_v3.entity.output;

import com.alibaba.fastjson.annotation.JSONField;
import com.qcc.udf.casesearch_v3.entity.input.XSGGRewardInfo;
import lombok.Data;

import java.util.LinkedList;
import java.util.List;

/**
 * 悬赏公告
 * <AUTHOR>
 * @date 2021年12月30日 14:27
 */
@Data
public class XSGGListEntity  extends BaseCaseOutEntity{
    @JSONField(name = "Id")
    private String id = "";
    @JSONField(name = "IsValid")
    private int isValid;
    @JSONField(name = "NameAndKeyNo")
    private List<NameAndKeyNoEntity> nameAndKeyNo = new LinkedList<>();
    @JSONField(name = "PublishDate")
    private Long publishDate;
    @JSONField(name = "StartDate")
    private Long startDate;
    @JSONField(name = "EndDate")
    private Long endDate;
    @JSONField(name = "Type")
    private String type;
    @JSONField(name = "ExecutorAddress")
    private String executorAddress;
    @<PERSON><PERSON><PERSON>ield(name = "ExecutionMoney")
    private String executionMoney;
    @J<PERSON><PERSON>ield(name = "OutstandingAmount")
    private String outstandingAmount;
    @JSONField(name = "Judge")
    private String judge;
    @JSONField(name = "JudgeContact")
    private String judgeContact;
    @JSONField(name = "RewardInfoList")
    private List<XSGGRewardInfo> rewardInfoList;
    @JSONField(name = "SpiderId")
    private String spiderId;
}
