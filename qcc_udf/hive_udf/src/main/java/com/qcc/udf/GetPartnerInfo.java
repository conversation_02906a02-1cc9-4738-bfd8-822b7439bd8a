package com.qcc.udf;

import com.alibaba.fastjson.JSONObject;
import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentTypeException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDTF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.PrimitiveObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class GetPartnerInfo extends GenericUDTF {

    @Override
    public StructObjectInspector initialize(ObjectInspector[] argOIs) throws UDFArgumentException {
        if (argOIs.length != 8) {
            throw new UDFArgumentException("GetPartnerInfo takes exactly 8 arguments.");
        }
        if (argOIs[0].getCategory() != ObjectInspector.Category.PRIMITIVE
                && ((PrimitiveObjectInspector) argOIs[0]).getPrimitiveCategory() != PrimitiveObjectInspector.PrimitiveCategory.STRING) {
            throw new UDFArgumentTypeException(0, "GetPartnerInfo takes a string as a parameter.");
        }
        if (argOIs[1].getCategory() != ObjectInspector.Category.PRIMITIVE
                && ((PrimitiveObjectInspector) argOIs[1]).getPrimitiveCategory() != PrimitiveObjectInspector.PrimitiveCategory.STRING) {
            throw new UDFArgumentTypeException(0, "GetPartnerInfo takes a string as a parameter.");
        }
        if (argOIs[2].getCategory() != ObjectInspector.Category.PRIMITIVE
                && ((PrimitiveObjectInspector) argOIs[2]).getPrimitiveCategory() != PrimitiveObjectInspector.PrimitiveCategory.STRING) {
            throw new UDFArgumentTypeException(0, "GetPartnerInfo takes a string as a parameter.");
        }
        if (argOIs[3].getCategory() != ObjectInspector.Category.PRIMITIVE
                && ((PrimitiveObjectInspector) argOIs[2]).getPrimitiveCategory() != PrimitiveObjectInspector.PrimitiveCategory.STRING) {
            throw new UDFArgumentTypeException(0, "GetPartnerInfo takes a string as a parameter.");
        }
        if (argOIs[4].getCategory() != ObjectInspector.Category.PRIMITIVE
                && ((PrimitiveObjectInspector) argOIs[2]).getPrimitiveCategory() != PrimitiveObjectInspector.PrimitiveCategory.STRING) {
            throw new UDFArgumentTypeException(0, "GetPartnerInfo takes a string as a parameter.");
        }
        if (argOIs[5].getCategory() != ObjectInspector.Category.PRIMITIVE
                && ((PrimitiveObjectInspector) argOIs[2]).getPrimitiveCategory() != PrimitiveObjectInspector.PrimitiveCategory.STRING) {
            throw new UDFArgumentTypeException(0, "GetPartnerInfo takes a string as a parameter.");
        }
        if (argOIs[6].getCategory() != ObjectInspector.Category.PRIMITIVE
                && ((PrimitiveObjectInspector) argOIs[2]).getPrimitiveCategory() != PrimitiveObjectInspector.PrimitiveCategory.STRING) {
            throw new UDFArgumentTypeException(0, "GetPartnerInfo takes a string as a parameter.");
        }
        if (argOIs[6].getCategory() != ObjectInspector.Category.PRIMITIVE
                && ((PrimitiveObjectInspector) argOIs[2]).getPrimitiveCategory() != PrimitiveObjectInspector.PrimitiveCategory.STRING) {
            throw new UDFArgumentTypeException(0, "GetPartnerInfo takes a string as a parameter.");
        }

        ArrayList<String> fieldNames = new ArrayList<String>();
        ArrayList<ObjectInspector> fieldOIs = new ArrayList<ObjectInspector>();
        fieldNames.add("keyno");
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        fieldNames.add("beforepartners");
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        fieldNames.add("afterpartners");
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        fieldNames.add("afterdates");
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        fieldNames.add("beforedates");
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        fieldNames.add("equityshare");
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        fieldNames.add("actualcontrol");
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        fieldNames.add("benefitlist");
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        fieldNames.add("flag");
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);

        return ObjectInspectorFactory.getStandardStructObjectInspector(fieldNames, fieldOIs);
    }

    @Override
    public void process(Object[] args) throws HiveException {
        // TODO Auto-generated method stub
        List<String> tempst = new ArrayList<String>();
        try {
            String keyno = args[0].toString();
            String beforepartners = args[1].toString();
            String afterpartners = args[2].toString();
            String afterdates = args[3].toString();
            String beforedates = args[4].toString();
            String equityshare = args[5] == null ? "" : args[5].toString();
            String actualcontrol = args[6] == null ? "" : args[6].toString();
            String benefitlist = args[7] == null ? "" : args[7].toString();
            String flag = "0";

            beforepartners = GetPartnerKeyInfo(beforepartners);
            afterpartners = GetPartnerKeyInfo(afterpartners);
            if (beforepartners.compareTo(afterpartners) == 0 || ComparePartner(beforepartners, afterpartners)) {
                flag = "1";
                actualcontrol = "";
                benefitlist = "";
            } else {
                actualcontrol = GetActualControlKeyInfo(actualcontrol);
                benefitlist = GetActualControlKeyInfo(benefitlist);
            }

            tempst.add(keyno);
            tempst.add(beforepartners);
            tempst.add(afterpartners);
            tempst.add(afterdates);
            tempst.add(beforedates);
            tempst.add(equityshare);
            tempst.add(actualcontrol);
            tempst.add(benefitlist);
            tempst.add(flag);

            String[] strings = new String[tempst.size()];
            tempst.toArray(strings);
            forward(strings);
        } catch (Exception e) {
            System.out.println("error message===" + e.toString());
        }
    }

    @Override
    public void close() throws HiveException {
        // TODO Auto-generated method stub

    }

    public boolean ComparePartner(String beforepartners, String afterpartners) {
        List<PartnerModel> bList = JSONObject.parseArray(beforepartners, PartnerModel.class);
        List<PartnerModel> aList = JSONObject.parseArray(afterpartners, PartnerModel.class);

        for (PartnerModel item1 : bList) {
            double stockpercent1 = 0;
            if (item1.StockPercent != null && !"".equals(item1.StockPercent)) {
                stockpercent1 = Double.parseDouble(item1.StockPercent.replaceAll("%", ""));
            }
            boolean flag = false;
            for (PartnerModel item2 : aList) {
                double stockpercent2 = 0;
                if (item2.StockPercent != null && !"".equals(item2.StockPercent)) {
                    stockpercent2 = Double.parseDouble(item2.StockPercent.replaceAll("%", ""));
                }
                if (stockpercent1 == stockpercent2 && (item1.StockName.compareTo(item2.StockName) == 0 || (item1.KeyNo != null && !"".equals(item1.KeyNo) && item1.KeyNo.compareTo(item2.KeyNo) == 0))) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                return false;
            }
        }

        for (PartnerModel item1 : aList) {
            double stockpercent1 = 0;
            if (item1.StockPercent != null && !"".equals(item1.StockPercent)) {
                stockpercent1 = Double.parseDouble(item1.StockPercent.replaceAll("%", ""));
            }
            boolean flag = false;
            for (PartnerModel item2 : bList) {
                double stockpercent2 = 0;
                if (item2.StockPercent != null && !"".equals(item2.StockPercent)) {
                    stockpercent2 = Double.parseDouble(item2.StockPercent.replaceAll("%", ""));
                }
                if (stockpercent1 == stockpercent2 && (item1.StockName.compareTo(item2.StockName) == 0 || (item1.KeyNo != null && !"".equals(item1.KeyNo) && item1.KeyNo.compareTo(item2.KeyNo) == 0))) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                return false;
            }
        }

        return true;
    }

    public String GetPartnerKeyInfo(String partners) {
        String result = "";
        try {
            if (partners != null && !"".equals(partners)) {
                List<PartnerModel> list = JSONObject.parseArray(partners, PartnerModel.class);
                Collections.sort(list);
                result = JSONObject.toJSONString(list);
            }
        } catch (Exception e) {
            result = "";
        }
        return result;
    }

    public String GetActualControlKeyInfo(String actualcontrol) {
        String result = "";
        try {
            if (actualcontrol != null && !"".equals(actualcontrol)) {
                ActualControlModel model = JSONObject.parseObject(actualcontrol, ActualControlModel.class);
                result = JSONObject.toJSONString(model.Names);
            }
        } catch (Exception e) {
            result = "";
        }
        return result;
    }

    public static class ActualControlModel {
        public String KeyNo;
        public String CompanyName;
        public List<ActualNames> Names;

        public void setKeyNo(String keyNo) {
            KeyNo = keyNo;
        }

        public void setCompanyName(String companyName) {
            CompanyName = companyName;
        }

        public void setNames(List<ActualNames> names) {
            Names = names;
        }

        public class ActualNames {
            public String KeyNo;
            public String Name;

            public void setKeyNo(String keyNo) {
                KeyNo = keyNo;
            }

            public void setName(String name) {
                Name = name;
            }
        }
    }

    public static class PartnerModel implements Comparable<PartnerModel> {
        public String KeyNo;
        public String StockName;
        public String StockPercent;

        public void setStockName(String stockName) {
            StockName = stockName;
        }

        public void setStockPercent(String stockPercent) {
            StockPercent = stockPercent;
        }

        public void setKeyNo(String keyNo) {
            KeyNo = keyNo;
        }

        public int compareTo(PartnerModel s) {
            //自定义比较方法，如果认为此实体本身大则返回1，否则返回-1
            if (this.StockName.compareTo(s.StockName) >= 0) {
                return 1;
            }
            return -1;
        }
    }

//    public static void main(String[] args) throws HiveException {
//        String keyno = "09eb7d0b4630c07b780ed57707e29033\n";
//        String beforepartners = "[{\"Org\":0,\"StockName\":\"苏州民营资本投资控股有限公司\",\"HasImage\":false,\"StockPercent\":\"90.00%\",\"CompanyCount\":15,\"ShouldCapi\":\"900\",\"InvestType\":null,\"KeyNo\":\"77c0715803e7fe7fddbb649f49ac4acc\",\"ShoudDate\":null,\"IdentifyType\":\"营业执照\",\"CapiDate\":null,\"StockType\":\"企业法人\",\"IdentifyNo\":\"91320594MA1MJ44F10\",\"RealCapi\":\"\",\"InvestName\":null},{\"Org\":0,\"StockName\":\"苏州苏榆投资合伙企业（有限合伙）\",\"HasImage\":false,\"StockPercent\":\"10.00%\",\"CompanyCount\":1,\"ShouldCapi\":\"100\",\"InvestType\":null,\"KeyNo\":\"c3ea79f17a1375296ece3ae4f2964daa\",\"ShoudDate\":null,\"IdentifyType\":\"营业执照\",\"CapiDate\":null,\"StockType\":\"合伙企业\",\"IdentifyNo\":\"91320506MA1R5WH49U\",\"RealCapi\":\"\",\"InvestName\":null}]\n";
//        String afterpartners = "[{\"Org\":0,\"StockName\":\"苏州翼朴股权投资基金管理有限公司\",\"HasImage\":false,\"StockPercent\":\"\",\"CompanyCount\":10,\"ShouldCapi\":\"\",\"InvestType\":null,\"KeyNo\":\"f4e6828316f1a172273aed4f2c8feb8c\",\"ShoudDate\":null,\"IdentifyType\":\"营业执照\",\"CapiDate\":null,\"StockType\":\"企业法人\",\"IdentifyNo\":\"91320594MA1MMUYC5A\",\"RealCapi\":\"\",\"InvestName\":null},{\"Org\":0,\"StockName\":\"苏州民营资本投资控股有限公司\",\"HasImage\":false,\"StockPercent\":\"\",\"CompanyCount\":15,\"ShouldCapi\":\"\",\"InvestType\":null,\"KeyNo\":\"77c0715803e7fe7fddbb649f49ac4acc\",\"ShoudDate\":null,\"IdentifyType\":\"营业执照\",\"CapiDate\":null,\"StockType\":\"企业法人\",\"IdentifyNo\":\"91320594MA1MJ44F10\",\"RealCapi\":\"\",\"InvestName\":null},{\"Org\":0,\"StockName\":\"苏州丹青二期创新医药产业投资合伙企业（有限合伙）\",\"HasImage\":false,\"StockPercent\":\"\",\"CompanyCount\":1,\"ShouldCapi\":\"\",\"InvestType\":null,\"KeyNo\":\"4d7733262acf2b86a2bbef8785af8ce0\",\"ShoudDate\":null,\"IdentifyType\":\"营业执照\",\"CapiDate\":null,\"StockType\":\"合伙企业\",\"IdentifyNo\":\"91320505MA1WK2RX4D\",\"RealCapi\":\"\",\"InvestName\":null},{\"Org\":0,\"StockName\":\"苏州工业园区国创开元二期投资中心（有限合伙）\",\"HasImage\":false,\"StockPercent\":\"\",\"CompanyCount\":31,\"ShouldCapi\":\"\",\"InvestType\":null,\"KeyNo\":\"b85f3ab90fcb8bb26b2df3b6fd340fa9\",\"ShoudDate\":null,\"IdentifyType\":\"营业执照\",\"CapiDate\":null,\"StockType\":\"合伙企业\",\"IdentifyNo\":\"91320594MA1MBRLG70\",\"RealCapi\":\"\",\"InvestName\":null},{\"Org\":0,\"StockName\":\"北京亦庄生物医药并购投资中心（有限合伙）\",\"HasImage\":false,\"StockPercent\":\"\",\"CompanyCount\":5,\"ShouldCapi\":\"\",\"InvestType\":null,\"KeyNo\":\"efdc2b6d45a9508b8a7ec76112ce21e6\",\"ShoudDate\":null,\"IdentifyType\":\"营业执照\",\"CapiDate\":null,\"StockType\":\"合伙企业\",\"IdentifyNo\":\"91110302MA001XPYXJ\",\"RealCapi\":\"\",\"InvestName\":null}]\n";
//        String afterdates = "20180822";
//        String beforedates = "20180820";
//        String equityshare = null;
//        String actualcontrol = null;
//        String benefitlist = null;
//
//        GetPartnerInfo info = new GetPartnerInfo();
//        info.process(new Object[]{keyno, beforepartners, afterpartners, afterdates, beforedates, equityshare, actualcontrol, benefitlist});
//    }
}