package com.qcc.udf.graph;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PartnerPathOut {

    @JSONField(name = "KeyNo")
    private String keyNo = "";

    @JSONField(name = "Name")
    private String name = "";

    @JSONField(name = "Org")
    private Integer org;

    @JSONField(name = "HasImage")
    private Boolean hasImage;

    @JSONField(name = "Percent")
    private String percent = "";

    @JSONField(name = "PercentTotal")
    private String percentTotal = "";

    @JSONField(name = "Level")
    private String level = "";

    @JSONField(name = "DataType")
    private Integer dataType;

    @JSONField(name = "Paths")
    private List<List<PartnerPathOut>> paths = new ArrayList<>();
}
