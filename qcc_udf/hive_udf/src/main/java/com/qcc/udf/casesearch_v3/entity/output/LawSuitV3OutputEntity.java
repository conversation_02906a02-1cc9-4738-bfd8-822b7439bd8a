package com.qcc.udf.casesearch_v3.entity.output;

import com.alibaba.fastjson.annotation.JSONField;
import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.input.OsPatent;
import com.qcc.udf.casesearch_v3.entity.input.Trademark;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * @Auther: zhangqiang
 * @Date: 2020/11/11 17:54
 * @Description:出参大实体
 */
@Data
@ToString
public class LawSuitV3OutputEntity {
    /**
     * 案件组id
     */
    @JSONField(name = "GroupId")
    private String groupId;
    /**
     * 组操作类型0-更新 1-新增  2-删除
     */
    @JSONField(name = "Type")
    private int type;
    /**
     * OT-离线任务，RT-实时任务
     */
    @JSONField(name = "Source")
    private String source;
    /**
     * 数据主键
     */
    @JSONField(name = "Id")
    private String id;

    /**
     * 省份信息
     */
    @JSONField(name = "Province")
    private String province;


    /**
     * 案件名称
     */
    @JSONField(name = "CaseName")
    private String caseName;

    /**
     * 标签
     */
    @JSONField(name = "Tags")
    private String tags;

    /**
     * 案件类型
     */
    @JSONField(name = "CaseType")
    private String caseType;

    /**
     * 统计信息-审理程序总数
     */
    @JSONField(name = "AnnoCnt")
    private int annoCnt;
    /**
     * 统计信息-最新审理程序
     */
    @JSONField(name = "LatestTrialRound")
    private String latestTrialRound;
    /**
     * 统计信息-裁判文书
     */
    @JSONField(name = "CaseCnt")
    private int caseCnt;
    /**
     * 统计信息-失信
     */
    @JSONField(name = "SxCnt")
    private int sxCnt;
    /**
     * 统计信息-执行
     */
    @JSONField(name = "ZxCnt")
    private int zxCnt;
    /**
     * 统计信息-限高
     */
    @JSONField(name = "XgCnt")
    private int xgCnt;
    /**
     * 统计信息-破产重组
     */
    @JSONField(name = "PcczCnt")
    private int pcczCnt;
    /**
     * 统计信息-终本
     */
    @JSONField(name = "ZbCnt")
    private int zbCnt;
    /**
     * 统计信息-询价评估
     */
    @JSONField(name = "XjpgCnt")
    private int xjpgCnt;
    /**
     * 统计信息-股权冻结
     */
    @JSONField(name = "GqdjCnt")
    private int gqdjCnt;
    /**
     * 统计信息-送达公告
     */
    @JSONField(name = "SdggCnt")
    private int sdggCnt;
    /**
     * 统计信息-
     */
    @JSONField(name = "FyggCnt")
    private int fyggCnt;
    /**
     * 统计信息-开庭公告
     */
    @JSONField(name = "KtggCnt")
    private int ktggCnt;
    /**
     * 统计信息-立案
     */
    @JSONField(name = "LianCnt")
    private int lianCnt;
    /**
     * 统计信息-环保处罚
     */
    @JSONField(name = "HbcfCnt")
    private int hbcfCnt;
    /**
     * 统计信息-行政处罚
     */
    @JSONField(name = "XzcfCnt")
    private int xzcfCnt;
    /**
     * 统计信息-司法拍卖
     */
    @JSONField(name = "SfpmCnt")
    private Integer sfpmCnt;
    /**
     * 统计信息-诉前调解
     */
    @JSONField(name = "SqtjCnt")
    private Integer sqtjCnt;
    /**
     * 统计信息-限制出境
     */
    @JSONField(name = "XzcjCnt")
    private Integer xzcjCnt;
    /**
     * 统计信息-选定评估机构
     */
    @JSONField(name = "XdpgjgCnt")
    private Integer xdpgjgCnt;

    /**
     * 统计信息-悬赏公告
     */
    @JSONField(name = "XsggCnt")
    private Integer xsggCnt;



    /**
     * 四合一后废弃工商，地方，信用中国处罚数据
     */
    @JSONField(name = "CfdfCnt")
    private int CfdfCnt;
    @JSONField(name = "CfxyCnt")
    private int CfxyCnt;
    @JSONField(name = "CfgsCnt")
    private int CfgsCnt;

    /**
     * 统计信息-涉案商标
     */
    @JSONField(name = "SBCnt")
    private Integer sBCnt;
    /**
     * 统计信息-涉案专利
     */
    @JSONField(name = "ZLCnt")
    private Integer zLCnt;

    /**
     * 案件身份
     * eg:"[{\"P\":\"陈桂香\",\"R\":\"申请执行人\",\"N\":\"\",\"O\":-2},{\"P\":\"吉林省宏利房地产开发有限公司乾安第一分公司\",\"R\":\"被执行人\",\"N\":\"12d6dbdb87107811a62173d591d63824\",\"O\":0}]"
     */
    @JSONField(name = "CaseRole")
    private String CaseRole;

    /**
     * 案由
     * eg:民事案件执行
     */
    @JSONField(name = "CaseReason")
    private String CaseReason;

    /**
     * 相关案号
     * eg:(2016)吉0723执987号
     */
    @JSONField(name = "AnNoList")
    private String AnNoList;

    /**
     * 当前该案件最早的数据对应审理程序
     * eg:首次执行|被执行人立案日期
     */
    @JSONField(name = "EarliestDateType")
    private String EarliestDateType;
    /**
     * 当前该案件最最早的数据对应时间
     * eg:1475078400
     */
    @JSONField(name = "EarliestDate")
    private long EarliestDate;
    /**
     * 当前该案件最晚的数据对应审理程序
     * eg:首次执行|限制高消费发布日期
     */
    @JSONField(name = "LastestDateType")
    private String LastestDateType;

    /**
     * 当前该案件最晚的数据对应时间
     * eg:1543852800
     */
    @JSONField(name = "LastestDate")
    private long LastestDate;

    /**
     * 相关法院
     * 各阶段取值，再去重，允许多值
     */
    @JSONField(name = "CourtList")
    private String CourtList;

    /**
     * 相关检察院
     * 各阶段取值，再去重，允许多值
     */
    @JSONField(name = "ProcuratorateList")
    private String ProcuratorateList;

    /**
     * 最新审理程序
     * eg:首次执行
     */
    @JSONField(name = "LatestTrialRound")
    private String LatestTrialRound;

    /**
     * 搜索字段
     */
    @JSONField(name = "CompanyKeywords")
    private String CompanyKeywords;

    /**
     * 明细信息
     */
    @JSONField(name = "InfoList")
    private List<InfoListEntity> infoList;


    /**
     * 案件金额信息
     */
    @JSONField(name = "AmtInfo")
    private Map<String,AmtInfo> amtInfo;

    /**
     * 其他标签
     */
    @JSONField(name = "OtherTags")
    private String otherTags;

    /**
     * 涉案环保处罚信息
     */
    @JSONField(name = "HbcfList")
    private List<HbcfListEntity> hbcfList;
    /**
     * 涉案行政处罚信息
     */
    @JSONField(name = "XzcfList")
    private List<XZCFListEntity> xzcfList;

    /**
     * 商标
     */
    @JSONField(name = "SBList")
    private List<TrademarkListEntity> sBList;

    /**
     * 专利
     */
    @JSONField(name = "ZLList")
    private List<PatentListEntity> zLList;


    /**
     * 案件组标识
     */
    @JSONField(name = "GroupMark")
    private String groupMark;
    @JSONField(name = "AnNoEnd")
    private String anNoEnd;
    @JSONField(name = "LawyerIds")
    private String lawyerIds;

    @JSONField(name = "CaseStatus")
    private String caseStatus;

    /**
     * 案件名称(脱敏)
     */
    @JSONField(name = "CaseNameClean")
    private String caseNameClean;
}
