package com.qcc.udf.casesearch_v3.entity.input;
import com.alibaba.fastjson.JSON;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.casesearch_v3.enums.CaseCategoryEnum;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import com.google.common.base.Strings;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther: zhangqiang
 * @Date: 2020/11/11 17:54
 * @Description:行政处罚
 */
@Data
public class XZCFEntity  extends BaseCaseEntity {
    private String id;
    private int isvalid;
    private String nameandkeyno;
    private String docno;
    private String source;
    private String sourceid;
    private String penaltylink;
    private long punishdate;
    private String groupid;
    private int groupcount;
    private String punishreason;
    private String punishresult;
    private String punishoffice;
    private String companynames;
    private String ver;

    private long update_date;

    private List<NameAndKeyNoEntity> nameandkeynoEntityList;

    public static List<XZCFEntity> convert(List<String> jsonList) {
        List<XZCFEntity> list = new ArrayList<>();
        XZCFEntity entity = null;
        if(CollectionUtils.isEmpty(jsonList)){
            return list;
        }
        for (String json : jsonList) {
            if(Strings.isNullOrEmpty(json)){
                continue;
            }
            entity = JSON.parseObject(json, XZCFEntity.class);
            if(entity == null  || Strings.isNullOrEmpty(entity.getId())){
                continue;
            }

            if(Strings.isNullOrEmpty(entity.getVer())){
                continue;
            }

            String str = entity.getNameandkeyno();

            if (!Strings.isNullOrEmpty(str)) {
                entity.setNameandkeynoEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
            }

            entity.setBaseCaseCategoryEnum(CaseCategoryEnum.XZCF);
            entity.setBaseCaseNo(entity.getDocno());
            if(!Strings.isNullOrEmpty(entity.getCompanynames())){
                entity.setBaseSearchWordSet(Arrays.stream(entity.getCompanynames().split(","))
                        .collect(Collectors.toSet()));
            }

            list.add(entity);
        }
        return list;
    }
}
