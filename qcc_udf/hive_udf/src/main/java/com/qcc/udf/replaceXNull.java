package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.text.ParseException;

public class replaceXNull extends UDF {

    public String evaluate(String args, Integer... argsint) throws ParseException {
        //0代表0x00 1代表\001hive默认分隔符 9代表制表符 10代表换行 13代表回车  32代表空格
        String result = args;
        if (result == null) {
            return result;
        }

        Integer[] asciicode = argsint;
        for (int i : asciicode
        ) {
            byte[] enp = new byte[]{(byte) i};
            String str = new String(enp);
            if (i == 13) {
                result = result.replace(str, "").replace("\\\\\\r", "").replace("\\\\r", "").replace("\\r", "");
            } else if (i == 10) {
                result = result.replace(str, "").replace("\\\\\\n", "").replace("\\\\n", "").replace("\\n", "");
            } else if (i == 9) {
                result = result.replace(str, "").replace("\\\\\\t", "").replace("\\\\t", "").replace("\\t", "");
            } else
                result = result.replace(str, "");
        }
        return result;
    }


}
