package com.qcc.udf.kzz;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * 计算公司新营业状态code
 * <AUTHOR>
 * @date 2021/11/10
 */
public class GetCompNewStatusCode extends UDF {

    private static final String CODE_SURVIVAL="0101";
    private static final String CODE_REGISTRATION="0102";
    private static final String CODE_NORMAL="0103";

    private static final String CODE_REVOKED="0201";
    private static final String CODE_REVOKED_NO_CANCEL="0202";
    private static final String CODE_REVOKED_CANCELED="0203";
    private static final String CODE_HAS_REVOKED="0204";

    private static final String CODE_CANCELED="0301";

    private static final String CODE_OTHER_UNDO="9901";
    private static final String CODE_OTHER_OUT="9902";
    private static final String CODE_OTHER_IN="9903";
    private static final String CODE_OTHER_PREPARATION="9904";
    private static final String CODE_OTHER_LIQUIDATION="9905";
    private static final String CODE_OTHER_CLOSED="9906";
    private static final String CODE_OTHER="9999";

//    public static void main(String[] args) throws IOException {
//
//    }


    /**
     * 营业:01
     *      0101: 存续(在营,开业,在册)
     *      0102: 登记成立
     *      0103: 正常
     * 吊销:02
     *      0201:吊销
     *      0202:吊销未注销
     *      0203:吊销已注销
     *      0204:已吊销
     * 注销:03
     *      0301:注销
     *  其他:99
     *      9901: 撤销
     *      9902: 迁出
     *      9903: 迁入
     *      9904: 筹建
     *      9905: 清算
     *      9906: 停业
     *      9999: 其他
     *
     * @param status
     * @return
     */
    public static String evaluate(String status,String statusCode) {
        String code = CODE_OTHER;
        try{
            if(StringUtils.isNotBlank(status)){
                if(status.contains("登记成立")){
                    code= CODE_REGISTRATION;
                }else if(status.contains("存续")){
                    code= CODE_SURVIVAL;
                }else if((status.contains("正常")&&!status.contains("非")&&!status.contains("不"))||status.contains("在业")||status.contains("开业")||status.contains("在营")){
                    code= CODE_NORMAL;
                }else if(status.contains("吊销")&&status.contains("未注销")){
                    code= CODE_REVOKED_NO_CANCEL;
                }else if(status.contains("吊销")&&status.contains("注销")&&(status.contains("已")||status.contains("后")||status.contains("并"))){
                    code= CODE_REVOKED_CANCELED;
                }else if(status.contains("已吊销")||status.contains("被吊销")){
                    code= CODE_HAS_REVOKED;
                }else if(status.contains("吊销")){
                    code= CODE_REVOKED;
                }else if(status.contains("注销")&&!status.contains("吊销")){
                    code= CODE_CANCELED;
                }else if(status.contains("撤销")){
                    code= CODE_OTHER_UNDO;
                }else if(status.contains("迁出")){
                    code= CODE_OTHER_OUT;
                }else if(status.contains("迁入")){
                    code= CODE_OTHER_IN;
                }else if(status.contains("清算")){
                    code= CODE_OTHER_LIQUIDATION;
                }else if(status.contains("停业")){
                    code= CODE_OTHER_CLOSED;
                }
            }
            //社会组织判断statusCode
            if(StringUtils.isNotBlank(statusCode)){
                if("10".equals(statusCode)){
                    code= CODE_NORMAL;
                }else if("20".equals(statusCode)){
                    code= CODE_SURVIVAL;
                }else if("30".equals(statusCode)){
                    code= CODE_OTHER_PREPARATION;
                }else if("40".equals(statusCode)){
                    code= CODE_OTHER_LIQUIDATION;
                }else if("50".equals(statusCode)){
                    code= CODE_OTHER_IN;
                }else if("60".equals(statusCode)){
                    code= CODE_OTHER_OUT;
                }else if("70".equals(statusCode)){
                    code= CODE_OTHER_CLOSED;
                }else if("80".equals(statusCode)){
                    code= CODE_OTHER_UNDO;
                }else if("90".equals(statusCode)){
                    code= CODE_REVOKED;
                }else if(statusCode.startsWith("99")){
                    code= CODE_CANCELED;
                }
            }

        }catch (Exception e){

        }
        return code;
    }
}
