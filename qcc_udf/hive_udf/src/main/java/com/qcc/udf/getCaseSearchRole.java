package com.qcc.udf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Auther: liulh
 * @Date: 2020/6/2 20:33
 * @Description:
 */
public class getCaseSearchRole  extends UDF {
    public static String  evaluate(String roleArray, String keyno1, String keyno2) {
        String result = "";

        if (StringUtils.isEmpty(roleArray)){
            return "";
        }
        Set<String> keyNoSet = new LinkedHashSet<>();
        keyNoSet.add(keyno1);
        keyNoSet.add(keyno2);

        JSONArray caseRoleJsonArray = JSONArray.parseArray(roleArray);
        Iterator<Object> it = caseRoleJsonArray.iterator();
        while(it.hasNext()){
            JSONObject jsonObject = (JSONObject)it.next();
            if (StringUtils.isNotEmpty(jsonObject.getString("N")) && keyNoSet.contains(jsonObject.getString("N"))){
                result = result.concat(jsonObject.getString("N")).concat("_").concat(getCaseRoleCode(jsonObject.getString("R"))).concat(",");
            }
        }
        if (result.length() > 0){
            result = result.substring(0, result.length() - 1);
        }

        return result;
    }

    public static String getCaseRoleCode(String caseRole){
        String result = "";
        if (StringUtils.isEmpty(caseRole)){
            return "";
        }

        Pattern p1 = Pattern.compile("(被执行人)|(被告)|(被申请人)|(被申请执行人)|(原审被告)|(被上诉人\\(原审被告\\))|(上诉人\\(原审被告\\))|(被告\\(反诉原告\\))|(被告人)|(上诉人\\(一审被告\\))|" +
                "(被上诉人\\(一审被告\\))|(被上诉人)|(上诉人\\(原审被告反诉原告\\))|(被告二)|(被告一)|(原告\\(被告\\))|(被申请人\\(一审被告二审被上诉人\\))|(被申请人\\(原审被告\\))|(再审申请人\\(一审被告二审上诉人\\))|" +
                "(再审申请人\\(原审被告\\))|(被申请人\\(仲裁被申请人\\))|(被申请人\\(原被执行人\\))|(再审被申请人)|(上诉人\\(原审被告原审原告\\))");
        Matcher m1 = p1.matcher(caseRole);
        if (m1.matches()) {
            result = "D";
        }

        Pattern p2 = Pattern.compile("(申请执行人)|(原告)|(申请人)|(被上诉人\\(原审原告\\))|(复议申请人)|(上诉人\\(原审原告\\))|(原告\\(反诉被告\\))|(上诉人)|(被上诉人\\(一审原告\\))|(上诉人\\(一审原告\\))|(被上诉人\\(原审原告反诉被告\\))|" +
                "(原审原告)|(再审申请人)|(被告\\(原告\\))|(被申请人\\(原审原告\\))|(附带民事诉讼原告人)|(复议申请人\\(原申请执行人\\))|(再审申请人\\(一审原告二审上诉人\\))|(再审申请人\\(原审原告\\))|(申请再审人\\(一审原告二审上诉人\\))|" +
                "(二审上诉人)|(原告人)|(附带民事诉讼原告)|(上诉人\\(原审原告原审被告\\))|(起诉人)|(申请人\\(仲裁申请人\\))|(赔偿请求人)");
        Matcher m2 = p2.matcher(caseRole);
        if (m2.matches()) {
            result = "P";
        }

        return result;
    }
}
