package com.qcc.udf.casesearch_v3.entity.output;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class XDPGJGListEntity  extends BaseCaseOutEntity{
    @JSONField(name = "Id")
    private String id = "";
    @JSONField(name = "<PERSON>Valid")
    private Integer isValid = 0;

    /**
     * 摇号日期
     */
    @JSONField(name = "LotteryDate")
    private Long lotteryDate = 0L;


    /**
     *标的物类型
     */
    @JSONField(name = "SubjectClass")
    private String subjectClass;
    /**
     *标的物名称
     */
    @JSONField(name = "SubjectName")
    private String subjectName;
    /**
     *标的物二级类型
     */
    @JSONField(name = "SubjectSubClass")
    private String subjectSubClass;

    /**
     * 当事人
     */
    @JSONField(name = "OwnerList")
    private List<NameAndKeyNoEntity> ownerList;

    /**
     * 评估机构array
     */
    @JSONField(name = "AgencyList")
    private List<NameAndKeyNoEntity> agencyList;
}
