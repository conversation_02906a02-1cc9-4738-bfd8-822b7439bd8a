package com.qcc.udf.casesearch_v3;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.court_notice.anUtils.Util;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

/**
 * <AUTHOR>
 */
public class mergeCase extends UDF {
    public static String evaluate(String id, List<String> param) {
        JSONArray result = new JSONArray();

        if (CollectionUtil.isNotEmpty(param)){
            Map<String, JSONArray> caseMap = new LinkedHashMap<>();
            JSONArray array = new JSONArray();
            for (String str : param){
                array.add(JSONObject.parseObject(str));
            }
            if (array != null && !array.isEmpty()){
                getInfoFromArray(array, caseMap);
            }

            //
            if (caseMap.size() > 0){
                Set<String> keyNoSet = caseMap.keySet();
                for (String str : keyNoSet){
                    if (caseMap.get(str).size() >= 1){
                        JSONArray infoArray = caseMap.get(str);
                        Iterator<Object> myIt = infoArray.iterator();
                        Set<String> idSet = new LinkedHashSet<>();
                        Long time = 0L;
                        String newestId = "";
                        while (myIt.hasNext()){
                            JSONObject json = (JSONObject)myIt.next();
                            idSet.add(json.getString("Id"));
                            if (json.getLong("SortDate").compareTo(time) >= 0){
                                time = json.getLong("SortDate");
                                newestId = json.getString("Id");
                            }
                        }

                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("Id", id);
                        jsonObject.put("KeyNo", str.substring(0,32));
                        jsonObject.put("AllIds", String.join(",", idSet));
                        idSet.remove(newestId);
                        jsonObject.put("DelIds", String.join(",", idSet));
                        jsonObject.put("NewestId", newestId);
                        jsonObject.put("IsValid", Integer.parseInt(str.substring(32,33)));

                        result.add(jsonObject);
                    }
                }
            }
        }

        return result.toString();
    }

    public static void getInfoFromArray(JSONArray array, Map<String, JSONArray> infoMap){
        if (array != null && !array.isEmpty() && array.size() > 0){
            Iterator<Object> it = array.iterator();
            while (it.hasNext()){
                JSONObject json = (JSONObject) it.next();
                String isValid = json.getString("isvalid");
                String caseid = json.getString("caseid");
                String keyword = json.getString("companynames");
                String sortDate = json.getString("sortdate");

                if (StringUtils.isNotEmpty(keyword)){
                    String[] keywords = keyword.split(",");
                    for (String key : keywords){
                        if (Util.isKeyword(key) && key.length() == 32){
                            JSONArray infoArr = new JSONArray();
                            if (infoMap.keySet().contains(key.concat(isValid))){
                                infoArr = infoMap.get(key.concat(isValid));
                            }

                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("Id", caseid);
                            jsonObject.put("IsValid", isValid);
                            jsonObject.put("SortDate", sortDate);
                            infoArr.add(jsonObject);

                            infoMap.put(key.concat(isValid), infoArr);
                        }
                    }
                }
            }
        }
    }

    public static void main(String[] args) {
        //System.out.println(evaluate("id123", "{\"id\":\"47b3c2d61c4007dd74efcbef9fb84548\",\"csid\":\"0000116cef03ec7d54cad8274d2320d7\",\"caseid\":\"03ced18c57d4450ac64fddd149dc57c80\",\"isvalid\":\"1\",\"sortdate\":\"1469491200\",\"companynames\":\"罗清全,危淑英,德化县卫生和计划生育局\"}"));
    }
}
