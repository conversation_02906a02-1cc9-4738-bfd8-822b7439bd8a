package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentLengthException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.Collector;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDTF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;
import org.apache.hadoop.io.Text;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashSet;
import java.util.List;

public class ProjectUpstreamDependencyUDTF extends GenericUDTF {

    @Override
    public StructObjectInspector initialize(StructObjectInspector argOIs) throws UDFArgumentException {
        if (argOIs.getAllStructFieldRefs().size() != 1) {
            throw new UDFArgumentLengthException("ProjectUpstreamDependencyUDTF only one argument");
        }


        List<String> fieldNames = new ArrayList<String>(2);
        List<ObjectInspector> fieldOIs = new ArrayList<ObjectInspector>(2);

        fieldNames.add("project_id");
        fieldNames.add("project_up_ids");

        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);

        return ObjectInspectorFactory.getStandardStructObjectInspector(fieldNames,fieldOIs);
    }

    @Override
    public void process(Object[] args) throws HiveException {

//        String arrStr = args[0].toString();
//        String[] arrStrsplit = arrStr.replaceAll("]|\\[","").split(", ");
//
//        ArrayList<String[]> arr = new ArrayList<>();
//
//        for (String s : arrStrsplit) {
//            arr.add(s.split("\\|"));
//        }

        ArrayList<Text> argsArr = (ArrayList<Text>) args[0];
        ArrayList<String[]> arr = new ArrayList<>();
        for (Text s : argsArr) {
            arr.add(s.toString().split("\\|"));
        }

//        for (String[] strings : arr) {
//            System.out.println(strings[0] + ":" + recurrenceSelect(strings,arr));
//        }
//        ArrayList<String> arrRes = new ArrayList<>();

        for (String[] strings : arr) {
            LinkedHashSet<String> strSet = new LinkedHashSet<>();
            //ArrayList<String> arrRes = new ArrayList<>();
            recurrenceSelect_1(strings,arr,strSet,0);
            //System.out.println(strings[0] + ":" + strSet.toString().replaceAll("]|\\[", ""));
            String[] strRes = {strings[0],strSet.toString().replaceAll("]|\\[", "")};
            //System.out.println(Arrays.toString(strRes));
            forward(strRes);
        }



    }

    public String recurrenceSelect(String[] s0,ArrayList<String[]> arr){

        //String strRes = "";

        for (String[] s1 : arr) {
            //System.out.println(s0[1] + ' ' + s1[0]);
            if(s0[1].equals(s1[0])) {
                //strRes = strRes + ',' + s1[0];
                return s1[0] + "," + recurrenceSelect(s1,arr);
            }
        }


        return s0[1];
        //return strRes + "," + recurrenceSelect(s1,arr);

    }

    public void recurrenceSelect_1(String[] s0,ArrayList<String[]> arr,LinkedHashSet<String> strRes,int cnt){

        boolean flag = false;
        cnt+=1;

        if(s0[1].equals(" ")){
            return;
        }

        if(cnt == 30){
            System.out.println("可能存在死循环！！！");
            return;
        }

        for (String[] s1 : arr) {
            if(s0[1].equals(s1[0])) {
                strRes.add(s1[0]);
                flag = true;
                recurrenceSelect_1(s1,arr,strRes,cnt);
            }
        }

        if (flag){
            return;
        }

        strRes.add(s0[1]);
        return;
    }


    @Override
    public void close() throws HiveException {

    }

    public static void main(String[] args) throws HiveException {
        Object[] item = new Object[1];
        ArrayList<Text> as = new ArrayList<>();

        Text text = new Text();
        text.set("33914|33916");
        as.add(text);
        Text text1 = new Text();
        text1.set("33916|33918");
        as.add(text1);
        Text text2 = new Text();
        text2.set("33918|22777");
        as.add(text2);
        Text text3 = new Text();
        text3.set("22777| ");
        as.add(text3);
        Text text4 = new Text();
        text4.set("33784| ");
        as.add(text4);
        Text text5 = new Text();
        text5.set("33920| ");
        as.add(text5);
        Text text6 = new Text();
        text6.set("33920|33820");
        as.add(text6);
        Text text7 = new Text();
        text7.set("33820|33928");
        as.add(text7);
        Text text8 = new Text();
        text8.set("33928|38828");
        as.add(text8);
        Text text9 = new Text();
        text9.set("33928|33930");
        as.add(text9);
        Text text10 = new Text();
        text10.set("48656| ");
        as.add(text10);
        Text text11 = new Text();
        text11.set("48658| ");
        as.add(text11);
        Text text12 = new Text();
        text12.set("48660|48662");
        as.add(text12);
        Text text13 = new Text();
        text13.set("48660|48660");
        as.add(text13);

        item[0] = as;
        //item[0] = "[33914|33916, 33916|33918, 33918|22777, 22777| , 33784| , 33920| , 33920|33820, 33820|33928, 33928|38828, 33928|33930, 48656| , 48658| , 48660|48662, 48660|48660]";

        ProjectUpstreamDependencyUDTF projectUpstreamDependencyUDTF = new ProjectUpstreamDependencyUDTF();
        projectUpstreamDependencyUDTF.setCollector(new Collector() {
            @Override
            public void collect(Object input) throws HiveException {

            }
        });
        projectUpstreamDependencyUDTF.process(item);
    }
}
