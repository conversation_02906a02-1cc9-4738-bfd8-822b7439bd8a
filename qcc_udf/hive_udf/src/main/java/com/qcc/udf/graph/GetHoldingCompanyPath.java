package com.qcc.udf.graph;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class GetHoldingCompanyPath extends UDF {
//    public static void main(String[] args) {
//        String msg = "{\"KeyNo\":\"4e1213c71f14cd183bdecccab19b43ba\",\"Name\":\"海南企查查网络科技有限公司\",\"Percent\":\"\",\"PercentTotal\":\"0.4574502\",\"Level\":\"2,3,3\",\"ShortStatus\":\"存续\",\"Paths\":[[{\"KeyNo\":\"f625a5b661058ba5082ca508f99ffe1b\",\"Name\":\"企查查科技有限公司\",\"Org\":\"0\",\"HasImage\":true,\"Percent\":\"100%\",\"PercentTotal\":\"100%\",\"Level\":1},{\"KeyNo\":\"cfbcf196fa51b0f78055a11239df734e\",\"Name\":\"苏州知己网络科技中心（有限合伙）\",\"Org\":\"0\",\"DataType\":\"0\",\"HasImage\":false,\"Percent\":\"6%\",\"PercentTotal\":\"6%\",\"Level\":\"2\"},{\"KeyNo\":\"pb9ac28faba8285ca3d516ab6a91235f\",\"Name\":\"陈德强\",\"Org\":\"2\",\"DataType\":\"5\",\"HasImage\":true,\"Percent\":\"50%\",\"PercentTotal\":\"3%\",\"Level\":\"3\"}],[{\"KeyNo\":\"f625a5b661058ba5082ca508f99ffe1b\",\"Name\":\"企查查科技有限公司\",\"Org\":\"0\",\"HasImage\":true,\"Percent\":\"100%\",\"PercentTotal\":\"100%\",\"Level\":1},{\"KeyNo\":\"fc9ac2c207a8f102d0c1e6d372a501df\",\"Name\":\"苏州知彼信息科技中心（有限合伙）\",\"Org\":\"0\",\"DataType\":\"0\",\"HasImage\":true,\"Percent\":\"11.6168%\",\"PercentTotal\":\"11.6168%\",\"Level\":\"2\"},{\"KeyNo\":\"pb9ac28faba8285ca3d516ab6a91235f\",\"Name\":\"陈德强\",\"Org\":\"2\",\"DataType\":\"5\",\"HasImage\":true,\"Percent\":\"90%\",\"PercentTotal\":\"10.4551%\",\"Level\":\"3\"}],[{\"KeyNo\":\"f625a5b661058ba5082ca508f99ffe1b\",\"Name\":\"企查查科技有限公司\",\"Org\":\"0\",\"HasImage\":true,\"Percent\":\"100%\",\"PercentTotal\":\"100%\",\"Level\":1},{\"KeyNo\":\"pb9ac28faba8285ca3d516ab6a91235f\",\"Name\":\"陈德强\",\"Org\":\"2\",\"DataType\":\"0\",\"HasImage\":true,\"Percent\":\"32.2899%\",\"PercentTotal\":\"32.2899%\",\"Level\":\"2\"}]]}";
//        String result = evaluate(msg);
//        System.out.printf(result);
//    }

    public static String evaluate(String jsonString) {
        try {
            if (StringUtils.isBlank(jsonString)) {
                throw new Exception("格式不正确");
            }
            PartnerPathOut path = JSONObject.parseObject(jsonString, PartnerPathOut.class);
            if (path == null) {
                throw new Exception("格式不正确");
            }
            List<List<PartnerPathOut>> result = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(path.getPaths())) {
                PartnerPathOut parentNode = new PartnerPathOut();
                parentNode.setKeyNo(path.getKeyNo());
                parentNode.setName(path.getName());
                parentNode.setOrg(CommonV3Util.getOrgByKeyNo(path.getKeyNo(), path.getName()));
                parentNode.setPercent(path.getPercent());
                parentNode.setPercentTotal(path.getPercentTotal());
                parentNode.setLevel(path.getLevel());
                for (List<PartnerPathOut> pathItem : path.getPaths()) {
                    List<PartnerPathOut> finalPath = getPartnerPathOrder(pathItem, parentNode);
                    result.add(finalPath);
                }
            }
            //控股企业path排序
            result.sort((i, j) -> {
                int firstValue = i.stream().anyMatch(s -> s.getDataType() != null && s.getDataType().equals(5)) ? 1 : 0;
                int lastValue = j.stream().anyMatch(s -> s.getDataType() != null && s.getDataType().equals(5)) ? 1 : 0;
                int diff = lastValue - firstValue;
                if (diff == 0) {
                    double firstPercent = CollectionUtils.isNotEmpty(i) ? getPercentValue(i.get(i.size() - 1).getPercentTotal()) : 0.0;
                    double lastPercent = CollectionUtils.isNotEmpty(j) ? getPercentValue(j.get(j.size() - 1).getPercentTotal()) : 0.0;
                    return lastPercent > firstPercent ? 1 : (lastPercent == firstPercent ? 0 : -1);
                } else {
                    return lastValue - firstValue;
                }
            });
            return JSONObject.toJSONString(result);
        } catch (Exception e) {
            return "[]";
        }
    }

    private static List<PartnerPathOut> getPartnerPathOrder(List<PartnerPathOut> oldPath, PartnerPathOut parentNode) {
        oldPath.sort((i, j) -> {
            return (int) (getIntValue(j.getLevel()) - getIntValue(i.getLevel()));
        });
        List<PartnerPathOut> filterPath = new ArrayList<>();
        int level = 0;
        String percentTotal = "";
        double lastPercentTotal = 0d;
        for (PartnerPathOut node : oldPath) {
            level++;
            PartnerPathOut lastNode = null;
            if (getIntValue(node.getLevel()) > 1) {
                List<PartnerPathOut> tempList = oldPath.stream().filter(x -> getIntValue(x.getLevel()) == getIntValue(node.getLevel()) - 1).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(tempList)) {
                    lastNode = tempList.get(0);
                }
            } else {
                lastNode = parentNode;
            }
            if (lastNode == null) {
                continue;
            }
            if (level == 1) {
                lastPercentTotal = getPercentValue(node.getPercent());
            } else {
                lastPercentTotal = lastPercentTotal * getPercentValue(node.getPercent()) / 100;
            }
            percentTotal = new BigDecimal(lastPercentTotal).setScale(4, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString() + "%";

            PartnerPathOut outItem = new PartnerPathOut();
            outItem.setKeyNo(lastNode.getKeyNo());
            outItem.setLevel(level + "");
            outItem.setName(lastNode.getName());
            outItem.setOrg(lastNode.getOrg());
            outItem.setPercent(new BigDecimal(getPercentValue(node.getPercent())).setScale(4, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString() + "%");
            outItem.setPercentTotal(percentTotal);
            outItem.setDataType(node.getDataType());
            filterPath.add(outItem);
        }
        return filterPath;
    }

    private static double getPercentValue(String source) {
        double result = 0.0;
        try {
            source = StringUtils.isBlank(source) ? "" : source;
            result = parseToDecimal(source.replace("%", ""), new BigDecimal(0), 4).doubleValue();
            if (result > 100) {
                result = 100;
            }
        } catch (Exception e) {

        }
        return result;
    }

    private static double getIntValue(String source) {
        int result = 0;
        try {
            result = parseToDecimal(source, new BigDecimal(0), 4).intValue();

        } catch (Exception e) {

        }
        return result;
    }

    private static BigDecimal parseToDecimal(Object val, BigDecimal defaultValue, int decimals) {
        BigDecimal num = new BigDecimal(0);
        if ((val == null)) {
            return defaultValue;
        }

        if (StringUtils.isEmpty(val.toString())) {
            return defaultValue;
        }

        if (val instanceof BigDecimal || val instanceof Double) {
            BigDecimal valDeciaml = new BigDecimal(val.toString());
            return valDeciaml.setScale(decimals, BigDecimal.ROUND_HALF_UP);
        }

        try {
            Float.parseFloat(val.toString());
        } catch (Exception e) {
            return defaultValue;
        }

        BigDecimal valDeciaml = new BigDecimal(val.toString());
        return valDeciaml.setScale(decimals, BigDecimal.ROUND_HALF_UP);
    }
}