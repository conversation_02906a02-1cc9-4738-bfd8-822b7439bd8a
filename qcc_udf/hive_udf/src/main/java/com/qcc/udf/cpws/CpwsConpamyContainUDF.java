package com.qcc.udf.cpws;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;


public class CpwsConpamyContainUDF extends UDF {

    public static String evaluate(String caserole)
    {
        List<JSONObject> caserolenew = new ArrayList<>();
      if(StringUtils.isNotEmpty(caserole)){
          caserolenew = JSON.parseArray(caserole,JSONObject.class);
      }

        List<String> names = new ArrayList<>();
        for (JSONObject object : caserolenew) {
           JSONArray list =object.getJSONArray("DetailList");
            for (Object o : list) {
                JSONObject de= (JSONObject) o;
                names.add(de.getString("Name"));}

        }
        if(CollectionUtils.isNotEmpty(names))
        {

            List<String> sortedList = names.stream()
                    .sorted(Comparator.comparingInt(String::length).reversed())
                    .collect(Collectors.toList());
            for (int i = 0; i < sortedList.size()-1; i++) {
                for (int i1 = i+1; i1 < sortedList.size(); i1++) {
                    if(sortedList.get(i).contains(sortedList.get(i1))){
                        return sortedList.get(i1);
                    }
                }
            }
        }

        return "false";

    }

    public static void main(String[] args) {
        String a= "[{\"Role\":\"申请执行人\",\"LawyerTag\":0,\"DetailList\":[{\"KeyNo\":\"\",\"Org\":-2,\"JR\":\"\",\"JudgeResult\":\"\",\"ShowName\":\"刘**\",\"Name\":\"刘凤美\",\"LawyerList\":[]}],\"RoleTag\":0},{\"Role\":\"被执行人\",\"LawyerTag\":0,\"DetailList\":[{\"KeyNo\":\"eda45ecab6b5ea47280176dc394ba357\",\"Org\":0,\"JR\":\"25\",\"JudgeResult\":\"17\",\"ShowName\":\"天津天华置业有限公司\",\"Name\":\"天津天华置业有限公司\",\"LawyerList\":[]}],\"RoleTag\":1},{\"Role\":\"当事人\",\"LawyerTag\":0,\"DetailList\":[{\"KeyNo\":\"hb149237bd519979b8beb97519704e17\",\"Org\":3,\"JudgeResult\":\"\",\"ShowName\":\"天华置业有限公司\",\"Name\":\"天华置业有限公司\",\"LawyerList\":[]}],\"RoleTag\":3}]";
            System.out.println(evaluate(a));

    }


}
