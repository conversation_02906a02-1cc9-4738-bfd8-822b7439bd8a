package com.qcc.udf.tag.dimtype;

import com.qcc.udf.tag.interfaces.BusinessEnumInterface;

/**
 * <AUTHOR>
 * @date ：2021年4月12日 10点43分
 * @description ：维度枚举类
 */
public enum DimensionEnum implements BusinessEnumInterface {

    /**
     * 1.禁止使用DEFAULT_VALUE，如果没有自己的维度添加一下。
     * 2. type为int类型，顺序按照数字大小添加；name为String类型，维度的具体中文名。
     * 3. 枚举的类别，用维度中文名的全拼，禁止使用缩写和英文单词。
     */
    DEFAULT_VALUE(0, "默认值"),
    SHI_XING_BEI_ZHI_XING_REN(2, "失信被执行人"),
    BEI_ZHI_XING_REN(3, "被执行人"),
    CAN_PAN_WEN_SHU(4, "裁判文书"),
    FA_YUAN_GONG_GAO(7, "法院公告"),
    JING_YING_YI_CHANG(11, "经营异常"),
    GU_QUAN_CHU_ZHI(12, "股权出质"),
    XING_ZHENG_CHU_FA(13, "行政处罚"),
    CHOU_CHA_JIAN_CHA(14, "抽查检查"),
    DONG_CHAN_DI_YA(15, "动产抵押"),
    QING_SUAN_XING_XI(16, "清算信息"),
    DUI_WAI_TOU_ZI(17, "对外投资"),
    KAI_TING_GONG_GAO(18, "开庭公告"),
    YAN_ZHONG_WEI_FA(20, "严重违法"),
    ZUI_ZHONG_SHOU_YI_REN(21, "最终受益人"),
    HUAN_BAO_CHU_FA(22, "环保处罚"),
    JIAN_YI_ZHU_XIAO(23, "简易注销"),
    DA_GU_DONG_BIAN_DONG(24, "大股东变动"),
    SHI_JI_KONG_ZHI_REN(25, "实际控制人"),
    GU_QUAN_DONG_JIE(26, "股权冻结"),
    SONG_DA_GONG_GAO(27, "送达公告"),
    RONG_ZI_DONG_TAI(28, "融资动态"),
    TU_DI_DI_YA(30, "土地抵押"),
    QIAN_SHUI_GONG_GAO(31, "欠税公告"),
    ZHU_CE_ZI_BEN(37, "注册资本"),
    JING_YING_ZHUANG_TAI(38, "经营状态"),
    QI_YE_FA_REN(39, "企业法人"),
    QI_YE_DI_ZHI(40, "企业地址"),
    JING_YING_FAN_WEI(41, "经营范围"),
    GONGS_SI_LEI_XIN(42, "公司类型"),
    YING_YE_QI_XIAN(43, "营业期限"),
    FEI_DA_GU_DONG_BIAN_DONG(44, "非大股东变动"),
    KONG_GU_QI_YE(45, "控股企业"),
    ZHU_YAO_CHENG_YUAN(46, "主要成员"),
    FEN_ZHI_JI_GOU(47, "分支机构"),
    WEI_GUI_CHU_LI(48, "违规处理"),
    LI_AN_XING_XI(49, "立案信息"),
    GU_QUAN_ZHI_YA(50, "股权质押"),
    GONG_GAO_CUI_SHI(51, "公示催告"),
    TOU_ZI_JI_GOU(52, "投资机构"),
    DUI_WAI_DAN_BAO(53, "对外担保"),
    ZHAO_TOU_BIAO(54, "招投标"),
    XIAN_ZHI_GAO_XIAO_FEI(55, "限制高消费"),
    ZHONG_BENG_AN_JIAN(56, "终本案件"),
    SI_FA_PAI_MAI(57, "司法拍卖"),
    PO_CHAN_CHONG_ZU(58, "破产重组"),
    XUNG_JIA_PIN_GU(59, "询价评估"),
    SHANG_BIAO(69, "商标"),
    ZHUAN_LI(70, "专利"),
    AN_JIAN_HUI_FU_ZHI_XING(73, "案件恢复执行"),
    QI_SU_TA_RENG_AN_JIAN(74, "起诉他人案件"),
    ZI_CHAN_PAI_MAI(75, "资产拍卖"),
    PING_GU_JI_GOU(76, "选定评估机构"),
    HEI_MING_DAN(77, "黑名单"),
    CHAN_PING_ZHAO_HUI(78, "产品召回"),
    SHI_PING_AN_QUAN(79, "食品安全"),
    CHAN_QUAN_JIAO_YI(80, "产权交易"),
    ZHAI_QUAN(81, "债券"),
    SHANG_BIAO_CAI_CHAN(82, "商标（财产线索）"),
    ZHUAN_LI_CAI_CHAN(83, "专利（财产线索）"),
    ZHU_ZUO_QUAN(84, "著作权"),
    RUAN_JIAN_ZHU_ZUO_QUAN(85, "软件著作权"),
    ZHI_CHAN_CHU_ZHI(86, "知产出质"),
    BEI_BAO_QUAN_AN_JIAN(87, "被保全的案件"),
    BAO_QUAN_AN_JIAN(88, "保全的案件"),
    XING_ZHENG_XU_KE(89, "行政许可"),
    YONG_HU_TOU_SU(90, "用户投诉"),
    TU_DI_ZHUAN_RANG(91, "土地转让"),
    DI_KUAI_GONG_SHI(92, "地块公示"),
    GOU_DI_XIN_XI(93, "购地信息"),
    BANG_DAN(94, "榜单"),
    A_GU(95, "A股"),
    A_GU_SHARE_HOLDER(9501, "A股股东"),
    A_GU_RELATED_PARTY(9502, "A股公司关联企业表"),
    A_GU_NEED_QUERY(9503, "A股关联公司维表"),
    A_GU_IPO(9504, "A股IPO"),
    A_GU_ISSUE_DETAIL(9505, "A股机构获配明细"),
    A_GU_COMPANY_SUBJECT(9506, "A股概念主题基本信息"),
    A_GU_INSTITUTE_SURVEY(9507, "A股机构调研信息"),
    A_GU_MANAGER_INFO(9508, "A股公司管理层信息"),
    A_GU_PLEDG_DETAIL(9509, "A股股东质押明细"),
    A_GU_BIG_EVENT(9510, "A股公司大事记录"),
    A_GU_CONTRACT(9511, "A股公司重大合同信息"),
    A_GU_ACQUISITION(9512, "A股并购重组交易信息"),
    A_GU_ACQ_BUYER(9513, "A股并购重组买方信息"),
    A_GU_ACQ_SELLER(9514, "A股并购重组卖方信息"),
    A_GU_RELATED_TRADE(9515, "A股公司关联交易"),
    A_GU_SPONSION_INFO(9516, "A股公司对外担保信息"),
    A_GU_STOCK_HOLDST_DETAIL(9517, "A股机构类股东持股明细"),
    A_GU_MAIN_SUPPLIER(9518, "A股公司主要供应商"),
    A_GU_YZXDR(9519, "A股一致行动人"),
    A_GU_ASHARE_SUBSCRIBE(9520, "A股新股申购信息"),
    A_GU_INCREASED_NOTICE(9521, "A股险资举牌"),
    A_GU_RELATED_COMP(9522, "A股参控股公司"),
    REN_YUAN_HE_BING(96, "人员合并-外部接口数据"),
    ZHI_SHI_CHAN_QU_CHU_ZHI(97, "知识产权出质"),
    XIAO_CHENG_XU(98, "小程序"),
    WEI_XIN_GONG_ZHONG_HAO(99, "微信公众号"),
    XIAN_ZHI_CHU_JING(100, "限制出境"),
    GONG_YING_SHANG_KE_HU(101, "供应商客户"),
    WEI_ZHUN_RU_JING(102, "未准入境"),
    DAN_BAO_FENG_XIAN(103, "担保风险"),
    REN_MIN_SHU_JU(104, "人民数据"),
    KE_JI_XING_QI_YE(105, "科技型企业"),
    JIAN_ZHU_CHA_CHA(106, "建筑查查"),
    LAW_FIRM(107, "律所"),
    KCB_SHARE_IPO(108, "科创板"),
    CYB_SHARE_IPO(109, "创业板"),
    Hospital(110, "医院查查"),
    JING_WAI_SHANG_SHI_GONG_SI_CAN_KONG_GU(111, "境外上市公司参控股公司"),
    Boss_Wiki(112, "bosswiki"),
    INDUSTRY_STANDARD(113, "行业标准"),
    SHUI_SHOU_WEI_FA(114, "税收违法"),
    US_STOCK(115, "美股"),
    GONG_ZHI_REN_RUAN(116, "公职人员"),
    UNIVERSITY_INFO(117, "大学查查"),
    GONG_SHANG_FENG_XIAN(118, "工商风险"),
    STANDARD_COMMITTEE(119, "标准相关主体"),
    COUNTRY_STANDARD(120, "国家标准"),
    COMPANY_STANDARD(121, "企业标准"),
    GROUP_STANDARD(122, "团体标准"),
    LOCAL_STANDARD(123, "地方标准"),
    HONG_KONG_REGULATORY_ACTIONS(124, "香港监管处罚"),
    XZCF_MUL_SOURCE(125, "行政惩罚_多数据源"),
    JING_ZHENG_GUAN_XI(126, "竞争关系"),
    HONG_KONG_COURT_HEARINGS(127, "香港法院公告"),
    HONG_KONG_TRADE_MARKS(128, "香港商标信息"),
    B2B_WANG_ZHAN(129, "B2B网站补数据"),
    FIND_GUEST(130, "客找找"),
    RISK_COOP_RELATION(131, "合作关系"),
    PD_PRODUCT(132, "公司主营产品"),
    HK_PARTNER(133, "港股股东"),
    BOND_INFO(134, "债券违约"),
    ZHI_SHI_WEI_XIN(135, "知识产权微信公众号"),
    ZHI_SHI_WEI_XIN_APP(136, "知识产权微信小程序"),
    ZHI_SHI_OVERSEA_PATENT(137, "知识产权海外专利"),
    XUAN_SHANG_GONG_GAO(138, "悬赏公告"),
    XING_HUI_REN(140, "行贿人"),
    WEI_GUI_RUAN_JIAN(139, "违规软件"),
    GUANG_GAO_SHEN_CHA(141, "广告审查"),
    COMMERCE_FRANCHISE(142, "知识产权商业特许经营备案"),
    JING_ZHENG_FENG_XIAN_BU_CHONG(143, "竞争风险补充数据"),
    HUAN_BAO_XIN_YONG_PING_JIA(144, "环保信用评价数据"),
    SHANG_BIAO_WEN_SHU(145, "商标文书"),
    LAO_DONG_ZHONG_CAI_SONG_DA_GONG_GAO(146, "劳动仲裁送达公告"),
    LAO_DONG_ZHONG_CAI_KAI_TING_GONG_GAO(147, "劳动仲裁开庭公告"),
    ZHI_SHI_APP(148, "知识产权APP"),
    HONG_KONG_ANNOUNCEMENTS(149, "香港公司公告"),
    CHARITY(150, "慈善组织"),
    FUND(151, "公募基金"),
    ZHAO_PIN(152, "招聘"),
    SHE_HUI_ZHU_ZHI(153, "社会组织"),
    COMP_SHARE_HOLDER(154, "企业股东"),
    BOND(155, "债券"),
    LAO_DONG_BAO_ZHANG(156, "劳动保障违法"),
    FEI_ZHENG_CHANG_HU(157, "非正常户"),
    ZHAIQUAN_ZHONGJIE_JIGOU(158, "债券中介机构"),
    WIPO_PATENT(159, "国际专利"),
    ZHAIQUAN_XINYONG_PINGJI(160, "债券信用评级表来源"),
    ZHENGFU_YUETAN(161, "政府约谈"),
    LEI(162, "LEI法人机构识别码"),
    JIGOU_PINGJI(163, "机构评级"),
    CN_PATENT(164, "知识产权-国内专利"),
    CERTIFICATION(165, "知识产权-资质证书"),
    BANNED_ILLEGAL_SOCIAL_ORGANIZATION(166, "取缔非法社会组织"),
    SUSPECTED_ILLEGAL_SOCIAL_ORGANIZATION(167, "涉嫌非法社会组织"),
    SOCIAL_ANNUAL_INSPECTION(168, "社会组织年报年检信息"),
    SOCIAL_MAIN_PER(169, "社会组织主要负责人"),
    SOCIAL_ABNORMAL_LIST(170, "社会组织经营异常"),
    INVOICE(171, "发票抬头"),
    DOMAIN_ICP(172, "知识产权名备案"),
    OVER_THE_COUNTER(173, "新三板"),
    SOCIAL_CHANGE_INFO(174, "社会组织变更信息"),
    SOCIAL_HONOR_INFO(175, "社会组织表彰信息"),
    SOCIAL_LOGOUT_INFO(176, "社会组织注销信息"),
    SOCIAL_EVALUATION_INFO(177, "社会组织评估信息"),
    ECPT(178, "公司经营异常"),
    Illegal(179, "公司严重违法"),
    PRIVATELYFUND_PRODUCTLIST(180, "私募基金产品信息"),
    IC_LAYOUT_DESIGN(181, "知识产权-集成电路布图设计"),
    BOND_DETAIL(182, "债券-相关债券列表"),
    DRUG(183, "医药"),
    XING_YONG_PING_JI(194, "信用评级融合"),
    JIN_RONG_KUAIXUN(197, "金融快讯"),
    SPOT_CHECK(195, "抽查检查"),
    DOUBLE_RANDOM_CHECK(196, "双随机抽查"),
    HK_GU(198, "港股"),
    AS_PU(199, "金融行政处罚"),
    YONG_YOU_SHU_JU(200, "用友数据"),
    ENLIQ(201, "注销备案"),
    HKJGCF(202, "香港监管处罚"),
    PLEDG(203, "股权出质"),
    XTGD(204, "协同股东"),
    ASSISTANCE(205, "股权冻结"),
    HANG_BAO_XING_YONG_PING_JIA(208, "环保信用评价"),
    YOU_XI_BAN_HAO(209, "游戏版号"),
    ACHIEVEMENT(213, "荣誉成果"),
    SANCTIONS(211, "对华制裁"),
    TAX(212, "税务评级"),
    LICENSE_STATEMENT(214, "营业执照作废声明"),
    ANNUAL_REPORT(215, "公司年报"),
    HAI_GUAN_XIN_YONG(216, "海关进出口信用"),
    GENERALTAX(217, "一般纳税人"),
    STATEOWNED(218, "国企名录"),
    QIYEMINGLU(219, "企业名录"),
    SHESHUIJIGOU(220, "涉税机构"),
    OVERSEA_COMP_HONGKONG(221, "海外香港"),
    XIAO_WEI_QI_YE(222, "小微企业"),
    DIAN_XIN_XU_KE(223, "电信许可证"),
    WEI_BO(224, "微博"),
    CHINA_GOODS_BAR_CODE(278, "中国商品和商品条形码维度"),
    QXT(279, "企信通"),
    WAI_SHAN_COMPANY(280, "外商企业"),
    INSPECT_AGENCY(281, "检验检测机构"),
    ZHENGCE_NEWS(283, "政策新闻"),
    XINGGUANG_XINWEN(284, "星光新闻"),
    KZZ_CASE_CONTENT(282, "获客裁判文书提取合同内容"),
    FA_LV_FA_GUI(285, "法律法规"),
    MINERAL_RIGTH(286, "矿业权"),
    MAO_YONG_TA_REN(287, "冒用他人"),
    CASE_ILLEGAL_FUND(288, "涉案企业"),
    INDUSTRY_CODE(289, "行业代码"),
    SELF_PUBLICATION(290, "自主公示"),
    SI_FA_AN_JIAN(291, "司法案件"),
    ;


    private Integer type;
    private String name;

    private DimensionEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public static DimensionEnum getDimensionEnum(int type) {
        DimensionEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            DimensionEnum dimensionEnum = var1[var3];
            if (dimensionEnum.getType().equals(type)) {
                return dimensionEnum;
            }
        }
        return null;
    }

    @Override
    public Integer getType() {
        return this.type;
    }

    @Override
    public String getName() {
        return this.name;
    }
}