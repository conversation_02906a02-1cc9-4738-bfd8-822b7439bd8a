package com.qcc.udf.cpws.casesearch_v2;

import java.util.ArrayList;
import java.util.List;

public class LawSuitV2Entity {
    // 该对象中提取到的当事人信息集合（民事案件->ktgg/lian/fygg/sdgg中汇总，执行案件->相同立案日期的sx/zx/xg中汇总）
    private String keyword;
    // 对象中所有获取到的法院名称按照递增排序后的第一个法院名
    private String court;
    // 立案信息列表
    private List<String> lianList;
    // 开庭公告列表
    private List<String> ktggList;
    // 送达公告列表
    private List<String> sdggList;
    // 法院公告列表
    private List<String> fyggList;
    // 失信列表
    private List<String> sxList;
    // 执行列表
    private List<String> zxList;
    // 限高列表
    private List<String> xgList;
    // 裁判文书列表
    private List<String> caseList;
    // 破产重整列表
    private List<String> pcczList;
    // 终本案件列表
    private List<String> zbList;
    // 询价评估列表
    private List<String> xjpgList;
    // 股权冻结列表
    private List<String> gqdjList;
    // 环保处罚列表
    private List<String> hbcfList;
    // 行政处罚（工商）列表
    private List<String> cfgsList;
    // 行政处罚（信用中国）列表
    private List<String> cfxyList;
    // 行政处罚（地方）
    private List<String> cfdfList;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getCourt() {
        return court;
    }

    public void setCourt(String court) {
        this.court = court;
    }

    public List<String> getLianList() {
        return lianList;
    }

    public void setLianList(List<String> lianList) {
        this.lianList = lianList;
    }

    public List<String> getKtggList() {
        return ktggList;
    }

    public void setKtggList(List<String> ktggList) {
        this.ktggList = ktggList;
    }

    public List<String> getSdggList() {
        return sdggList;
    }

    public void setSdggList(List<String> sdggList) {
        this.sdggList = sdggList;
    }

    public List<String> getFyggList() {
        return fyggList;
    }

    public void setFyggList(List<String> fyggList) {
        this.fyggList = fyggList;
    }

    public List<String> getSxList() {
        return sxList;
    }

    public void setSxList(List<String> sxList) {
        this.sxList = sxList;
    }

    public List<String> getZxList() {
        return zxList;
    }

    public void setZxList(List<String> zxList) {
        this.zxList = zxList;
    }

    public List<String> getXgList() {
        return xgList;
    }

    public void setXgList(List<String> xgList) {
        this.xgList = xgList;
    }

    public List<String> getCaseList() {
        return caseList;
    }

    public void setCaseList(List<String> caseList) {
        this.caseList = caseList;
    }

    public List<String> getPcczList() {
        return pcczList;
    }

    public void setPcczList(List<String> pcczList) {
        this.pcczList = pcczList;
    }

    public List<String> getZbList() {
        return zbList;
    }

    public void setZbList(List<String> zbList) {
        this.zbList = zbList;
    }

    public List<String> getXjpgList() {
        return xjpgList;
    }

    public void setXjpgList(List<String> xjpgList) {
        this.xjpgList = xjpgList;
    }

    public List<String> getGqdjList() {
        return gqdjList;
    }

    public void setGqdjList(List<String> gqdjList) {
        this.gqdjList = gqdjList;
    }

    public List<String> getHbcfList() {
        return hbcfList;
    }

    public void setHbcfList(List<String> hbcfList) {
        this.hbcfList = hbcfList;
    }

    public List<String> getCfgsList() {
        return cfgsList;
    }

    public void setCfgsList(List<String> cfgsList) {
        this.cfgsList = cfgsList;
    }

    public List<String> getCfxyList() {
        return cfxyList;
    }

    public void setCfxyList(List<String> cfxyList) {
        this.cfxyList = cfxyList;
    }

    public List<String> getCfdfList() {
        return cfdfList;
    }

    public void setCfdfList(List<String> cfdfList) {
        this.cfdfList = cfdfList;
    }

    /**
     * 创建一个空对象
     */
    public LawSuitV2Entity() {
        this.keyword = "";
        this.court = "";
        this.lianList = new ArrayList<>();
        this.ktggList = new ArrayList<>();
        this.fyggList = new ArrayList<>();
        this.sdggList = new ArrayList<>();
        this.sxList = new ArrayList<>();
        this.zxList = new ArrayList<>();
        this.xgList = new ArrayList<>();
        this.caseList = new ArrayList<>();
        this.pcczList = new ArrayList<>();
        this.zbList = new ArrayList<>();
        this.xjpgList = new ArrayList<>();
        this.gqdjList = new ArrayList<>();
        this.hbcfList = new ArrayList<>();
        this.cfgsList = new ArrayList<>();
        this.cfxyList = new ArrayList<>();
        this.cfdfList = new ArrayList<>();
    }

    /**
     * 创建一个民事类型对象
     * @param keyword
     * @param lianList
     * @param ktggList
     * @param sdggList
     * @param fyggList
     */
    public LawSuitV2Entity(String keyword, List<String> lianList, List<String> ktggList, List<String> sdggList, List<String> fyggList) {
        this.keyword = keyword;
        this.court = "";
        this.lianList = lianList;
        this.ktggList = ktggList;
        this.sdggList = sdggList;
        this.fyggList = fyggList;
        this.caseList = new ArrayList<>();
        this.zxList = new ArrayList<>();
        this.sxList = new ArrayList<>();
        this.xgList = new ArrayList<>();
        this.pcczList = new ArrayList<>();
        this.zbList = new ArrayList<>();
        this.xjpgList = new ArrayList<>();
        this.gqdjList = new ArrayList<>();
        this.hbcfList = new ArrayList<>();
        this.cfgsList = new ArrayList<>();
        this.cfxyList = new ArrayList<>();
        this.cfdfList = new ArrayList<>();
    }

    /**
     * 创建一个民事类型对象
     * @param keyword
     * @param lianList
     * @param ktggList
     * @param sdggList
     * @param fyggList
     */
    public LawSuitV2Entity(String keyword, List<String> lianList, List<String> ktggList, List<String> sdggList, List<String> fyggList, List<String> caseList
            , List<String> zxList, List<String> sxList, List<String> xgList, List<String> pcczList, List<String> zbList, List<String> xjpgList
            , List<String> gqdjList, List<String> hbcfList, List<String> cfgsList, List<String> cfxyList, List<String> cfdfList) {
        this.keyword = keyword;
        this.court = "";
        this.lianList = lianList;
        this.ktggList = ktggList;
        this.sdggList = sdggList;
        this.fyggList = fyggList;
        this.caseList = caseList;
        this.zxList = zxList;
        this.sxList = sxList;
        this.xgList = xgList;
        this.pcczList = pcczList;
        this.zbList = zbList;
        this.xjpgList = xjpgList;
        this.gqdjList = gqdjList;
        this.hbcfList = hbcfList;
        this.cfgsList = cfgsList;
        this.cfxyList = cfxyList;
        this.cfdfList = cfdfList;
    }

    /**
     * 创建一个执行案件对象
     * @param sxList
     * @param zxList
     * @param xgList
     */
    public LawSuitV2Entity(String keyword, List<String> sxList, List<String> zxList, List<String> xgList) {
        this.keyword = keyword;
        this.court = "";
        this.sxList = sxList;
        this.zxList = zxList;
        this.xgList = xgList;
        this.caseList = new ArrayList<>();
        this.lianList = new ArrayList<>();
        this.ktggList = new ArrayList<>();
        this.fyggList = new ArrayList<>();
        this.sdggList = new ArrayList<>();
        this.pcczList = new ArrayList<>();
        this.zbList = new ArrayList<>();
        this.xjpgList = new ArrayList<>();
        this.gqdjList = new ArrayList<>();
        this.hbcfList = new ArrayList<>();
        this.cfgsList = new ArrayList<>();
        this.cfxyList = new ArrayList<>();
        this.cfdfList = new ArrayList<>();
    }

    /**
     * 创建一个只有裁判文书的对象
     * @param keyword
     * @param caseList
     */
    public LawSuitV2Entity(String keyword, List<String> caseList) {
        this.keyword = keyword;
        this.court = "";
        this.caseList = caseList;
        this.lianList = new ArrayList<>();
        this.ktggList = new ArrayList<>();
        this.fyggList = new ArrayList<>();
        this.sdggList = new ArrayList<>();
        this.sxList = new ArrayList<>();
        this.zxList = new ArrayList<>();
        this.xgList = new ArrayList<>();
        this.pcczList = new ArrayList<>();
        this.zbList = new ArrayList<>();
        this.xjpgList = new ArrayList<>();
        this.gqdjList = new ArrayList<>();
        this.hbcfList = new ArrayList<>();
        this.cfgsList = new ArrayList<>();
        this.cfxyList = new ArrayList<>();
        this.cfdfList = new ArrayList<>();
    }

    /**
     * 创建一个只有破产公告的对象
     * @param keyword
     * @param pcczList
     * @return
     */
    public static LawSuitV2Entity createSinglePcczListLawsuitV2Entity(String keyword, List<String> pcczList) {
        LawSuitV2Entity lawSuitV2Entity = new LawSuitV2Entity();
        lawSuitV2Entity.setKeyword(keyword);
        lawSuitV2Entity.setPcczList(pcczList);
        return lawSuitV2Entity;
    }

    /**
     * 创建一个只有终本案件的对象
     * @param keyword
     * @param zbList
     * @return
     */
    public static LawSuitV2Entity createSingleZbListLawsuitV2Entity(String keyword, List<String> zbList) {
        LawSuitV2Entity lawSuitV2Entity = new LawSuitV2Entity();
        lawSuitV2Entity.setKeyword(keyword);
        lawSuitV2Entity.setZbList(zbList);
        return lawSuitV2Entity;
    }

    /**
     * 创建一个只有询价评估的对象
     * @param keyword
     * @param xjpgList
     * @return
     */
    public static LawSuitV2Entity createSingleXjpgListLawsuitV2Entity(String keyword, List<String> xjpgList) {
        LawSuitV2Entity lawSuitV2Entity = new LawSuitV2Entity();
        lawSuitV2Entity.setKeyword(keyword);
        lawSuitV2Entity.setXjpgList(xjpgList);
        return lawSuitV2Entity;
    }

    /**
     * 创建一个只有股权冻结的对象
     * @param keyword
     * @param gqdjList
     * @return
     */
    public static LawSuitV2Entity createSingleGqdjListLawsuitV2Entity(String keyword, List<String> gqdjList) {
        LawSuitV2Entity lawSuitV2Entity = new LawSuitV2Entity();
        lawSuitV2Entity.setKeyword(keyword);
        lawSuitV2Entity.setGqdjList(gqdjList);
        return lawSuitV2Entity;
    }

    /**
     * 创建一个只有送达公告的对象
     * @param keyword
     * @param sdggList
     * @return
     */
    public static LawSuitV2Entity createSingleSdggListLawsuitV2Entity(String keyword, List<String> sdggList) {
        LawSuitV2Entity lawSuitV2Entity = new LawSuitV2Entity();
        lawSuitV2Entity.setKeyword(keyword);
        lawSuitV2Entity.setSdggList(sdggList);
        return lawSuitV2Entity;
    }

    /**
     * 创建一个只有法院公告的对象
     * @param keyword
     * @param fyggList
     * @return
     */
    public static LawSuitV2Entity createSingleFyggListLawsuitV2Entity(String keyword, List<String> fyggList) {
        LawSuitV2Entity lawSuitV2Entity = new LawSuitV2Entity();
        lawSuitV2Entity.setKeyword(keyword);
        lawSuitV2Entity.setFyggList(fyggList);
        return lawSuitV2Entity;
    }

    /**
     * 创建一个只有开庭公告的对象
     * @param keyword
     * @param ktggList
     * @return
     */
    public static LawSuitV2Entity createSingleKtggListLawsuitV2Entity(String keyword, List<String> ktggList) {
        LawSuitV2Entity lawSuitV2Entity = new LawSuitV2Entity();
        lawSuitV2Entity.setKeyword(keyword);
        lawSuitV2Entity.setKtggList(ktggList);
        return lawSuitV2Entity;
    }

    /**
     * 创建一个只有立案信息的对象
     * @param keyword
     * @param lianList
     * @return
     */
    public static LawSuitV2Entity createSingleLianListLawsuitV2Entity(String keyword, List<String> lianList) {
        LawSuitV2Entity lawSuitV2Entity = new LawSuitV2Entity();
        lawSuitV2Entity.setKeyword(keyword);
        lawSuitV2Entity.setLianList(lianList);
        return lawSuitV2Entity;
    }
}
