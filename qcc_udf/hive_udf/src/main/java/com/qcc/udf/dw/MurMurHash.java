package com.qcc.udf.dw;
import com.google.common.hash.Hashing;
import org.apache.hadoop.hive.ql.exec.UDF;
import java.nio.charset.Charset;

/**
 * @Auther: chenjh
 * @Date: 2021/9/1 17:45
 * @Description:hash值
 */


public class MurMurHash extends UDF {
    public static Long evaluate(String Str) {
        if(null==Str){
            return  null;
        }

        return Hashing.murmur3_128(0).hashString(Str, Charset.defaultCharset()).asLong();
    }

    public static void main(String[] args) {
        System.out.println(MurMurHash.evaluate("f625a5b661058ba5082ca508f99ffe1b"));
    }
}
