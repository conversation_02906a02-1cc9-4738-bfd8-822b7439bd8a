package com.qcc.udf.kzz;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 提取角色判决结果
 *
 * <AUTHOR>
 * @date 2022/4/14
 */
public class GetCaseRoleResult extends UDF {

    public static String evaluate(String caseRoleSearch, String keyNo) {
        String result = "";
        try {
            if (StringUtils.isNotBlank(caseRoleSearch) && StringUtils.isNotBlank(keyNo)) {
                JSONArray array = JSONArray.parseArray(caseRoleSearch);
                List<String> list = new ArrayList<>();
                if (!array.isEmpty()) {
                    for (Object obj : array) {
                        JSONObject jsonObject = (JSONObject) obj;
                        String targetKeyNo = jsonObject.getString("N");
                        JSONArray rlArray = jsonObject.getJSONArray("RL");
                        if (keyNo.equals(targetKeyNo) && !rlArray.isEmpty()) {
                            for (Object rlobj : rlArray) {
                                JSONObject rl = (JSONObject) rlobj;
                                String lr = rl.getString("LR");
                                if(StringUtils.isNotBlank(lr)){
                                    list.add(lr);
                                }
                            }
                        }
                    }
                }
                if(CollectionUtils.isNotEmpty(list)){
                    result = list.stream().collect(Collectors.joining(","));
                }
            }

        } catch (Exception e) {

        }
        return result;
    }
//    public static void main(String[] args) {
//        String content = "";
//        String region = evaluate(content,"eb37d25f389d200732dc98b7775b3533");
//        System.out.println(region);
//    }
    /**
     * [
     *   {
     *     "P": "河南双茂钢铁贸易有限公司",
     *     "R": "申请人",
     *     "D": "财产保全执行申请人",
     *     "RL": [
     *       {
     *         "LR": "3",
     *         "R": "申请人",
     *         "T": "财产保全执行"
     *       }
     *     ],
     *     "N": "eb37d25f389d200732dc98b7775b3533",
     *     "O": 0
     *   },
     *   {
     *     "P": "河南鑫萌机械制造有限公司",
     *     "R": "被申请人",
     *     "D": "财产保全执行被申请人",
     *     "RL": [
     *       {
     *         "LR": "15",
     *         "R": "被申请人",
     *         "T": "财产保全执行"
     *       }
     *     ],
     *     "N": "1ac05f064664ffe748838fc4aa9ac171",
     *     "O": 0
     *   }
     * ]
     */
}
