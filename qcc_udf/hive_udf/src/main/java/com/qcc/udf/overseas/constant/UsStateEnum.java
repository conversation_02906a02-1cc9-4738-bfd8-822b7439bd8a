package com.qcc.udf.overseas.constant;

import org.apache.commons.lang3.StringUtils;

/**
 * 美国各州枚举类
 */
public enum UsStateEnum {
    ALABAMA("Alabama", "AL", "亚拉巴马州"),
    ALASKA("Alaska", "AK", "阿拉斯加州"),
    ARIZONA("Arizona", "AZ", "亚利桑那州"),
    ARKANSAS("Arkansas", "AR", "阿肯色州"),
    CALIFORNIA("California", "CA", "加利福尼亚州"),
    COLORADO("Colorado", "CO", "科罗拉多州"),
    CONNECTICUT("Connecticut", "CT", "康涅狄格州"),
    DELAWARE("Delaware", "DE", "特拉华州"),
    FLORIDA("Florida", "FL", "佛罗里达州"),
    GEORGIA("Georgia", "GA", "佐治亚州"),
    HAWAII("Hawaii", "HI", "夏威夷州"),
    IDAHO("Idaho", "ID", "爱达荷州"),
    ILLINOIS("Illinois", "IL", "伊利诺伊州"),
    INDIANA("Indiana", "IN", "印第安纳州"),
    IOWA("Iowa", "IA", "爱荷华州"),
    KANSAS("Kansas", "KS", "堪萨斯州"),
    KENTUCKY("Kentucky", "KY", "肯塔基州"),
    LOUISIANA("Louisiana", "LA", "路易斯安那州"),
    MAINE("Maine", "ME", "缅因州"),
    MARYLAND("Maryland", "MD", "马里兰州"),
    MASSACHUSETTS("Massachusetts", "MA", "马萨诸塞州"),
    MICHIGAN("Michigan", "MI", "密歇根州"),
    MINNESOTA("Minnesota", "MN", "明尼苏达州"),
    MISSISSIPPI("Mississippi", "MS", "密西西比州"),
    MISSOURI("Missouri", "MO", "密苏里州"),
    MONTANA("Montana", "MT", "蒙大拿州"),
    NEBRASKA("Nebraska", "NE", "内布拉斯加州"),
    NEVADA("Nevada", "NV", "内华达州"),
    NEW_HAMPSHIRE("New Hampshire", "NH", "新罕布什尔州"),
    NEW_JERSEY("New Jersey", "NJ", "新泽西州"),
    NEW_MEXICO("New Mexico", "NM", "新墨西哥州"),
    NEW_YORK("New York", "NY", "纽约州"),
    NORTH_CAROLINA("North Carolina", "NC", "北卡罗来纳州"),
    NORTH_DAKOTA("North Dakota", "ND", "北达科他州"),
    OHIO("Ohio", "OH", "俄亥俄州"),
    OKLAHOMA("Oklahoma", "OK", "俄克拉何马州"),
    OREGON("Oregon", "OR", "俄勒冈州"),
    PENNSYLVANIA("Pennsylvania", "PA", "宾夕法尼亚州"),
    RHODE_ISLAND("Rhode Island", "RL", "罗得岛州"),
    SOUTH_CAROLINA("South Carolina", "SC", "南卡罗来纳州"),
    SOUTH_DAKOTA("South Dakota", "SD", "南达科他州"),
    TENNESSEE("Tennessee", "TN", "田纳西州"),
    TEXAS("Texas", "TX", "得克萨斯"),
    UTAH("Utah", "UT", "犹他州"),
    VERMONT("Vermont", "VT", "佛蒙特州"),
    VIRGINIA("Virginia", "VA", "弗吉尼亚州"),
    WASHINGTON("Washington", "WA", "华盛顿州"),
    WESTVIRGINIA("WestVirginia", "WV", "西弗吉尼亚州"),
    WISCONSIN("Wisconsin", "WI", "威斯康星州"),
    WYOMING("wyoming", "WY", "怀俄明州");

    UsStateEnum(String name, String abbr, String desc) {
        this.name = name;
        this.abbr = abbr;
        this.desc = desc;
    }

    private String name;
    private String abbr;
    private String desc;

    public String getName() {
        return name;
    }

    public String getAbbr() {
        return abbr;
    }

    public String getDesc() {
        return desc;
    }

    public static UsStateEnum getStateEnumByName(String abbr) {
        try {
            if (StringUtils.isNotBlank(abbr)) {
                for (UsStateEnum usStateEnum : UsStateEnum.values()) {
                    if (usStateEnum.getAbbr().equals(abbr)) {
                        return usStateEnum;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
