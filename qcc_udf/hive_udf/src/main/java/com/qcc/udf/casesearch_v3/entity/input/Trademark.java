package com.qcc.udf.casesearch_v3.entity.input;

import com.alibaba.fastjson.annotation.JSONField;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class Trademark {
    @JSONField(name = "Id")
    private String id;
    @JSONField(name = "RegNo")
    private String regNo;
    @JSONField(name = "IntCls")
    private Integer intCls;
    @JSONField(name = "Name")
    private String name;
    @JSONField(name = "AppDate")
    private Long appDate;
    @JSONField(name = "ApplicantCn")
    private String applicantCn;
//    @JSONField(name = "ApplicantEn")
//    private String applicantEn;
    @JSONField(name = "Agent")
    private String agent;
//    @JSONField(name = "Status")
//    private Integer status;
//    @JSONField(name = "FlowNo")
//    private String flowNo = "0";
//    @JSONField(name = "HasImage")
//    private Integer hasImage = 0;
    @JSONField(name = "Type")
    private Integer type = 3;
    @JSONField(name = "CompanyKeywords")
    private String companyKeywords;
//    @JSONField(name = "GroupYear")
//    private Integer groupYear;
//    @JSONField(name = "NameList")
//    private String nameList;
//    @JSONField(name = "Goods")
//    private String goods;
    @JSONField(name = "IsValid")
    private Integer isValid;
    @JSONField(name = "NameAndKeyNo")
    private String nameAndKeyNo;
    @JSONField(name = "tmType")
    private String tmType;
//    @JSONField(name = "isShare")
//    private Integer isShare = 0;
//    @JSONField(name = "statusInfo")
//    private String statusInfo;
//    @JSONField(name = "gonggaoInfo")
//    private String gonggaoInfo;
    @JSONField(name = "statusCode")
    private String statusCode = "019";

//    @JSONField(name = "issueStatus")
//    private Integer issueStatus = 0;

//    @JSONField(name = "dynamicId")
//    private String dynamicId = "";
    /**
     * 图片ID
     */
    @JSONField(name = "picId")
    private String picId = "";

    /**
     * 更新时间
     */
//    @JSONField(name = "UpdateTime")
//    private LocalDateTime updateTime = LocalDateTime.now();

    /**
     * 创建时间
     */
//    @JSONField(name = "CreateTime")
//    private LocalDateTime createTime = LocalDateTime.now();

    /**
     * 根据流程洗出的状态
     */
    @JSONField(name = "flowStatus")
    private Integer flowStatus = 1;

    /**
     * 所属公司集团Id
     */
//    @JSONField(name = "GroupId")
//    private String groupId = "";

}
