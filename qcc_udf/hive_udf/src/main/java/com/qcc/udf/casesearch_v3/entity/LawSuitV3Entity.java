package com.qcc.udf.casesearch_v3.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.qcc.udf.casesearch_v3.entity.input.*;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Set;

/**
 * @Auther: zhangqiang
 * @Date: 2020/11/11 17:54
 * @Description:入参大实体
 */
@Data
@ToString
public class LawSuitV3Entity {
    // 该对象中提取到的当事人信息集合（民事案件->ktgg/lian/fygg/sdgg中汇总，执行案件->相同立案日期的sx/zx/xg中汇总）
    private String keyword;
    // 对象中所有获取到的法院名称按照递增排序后的第一个法院名
    private String court;
    // 立案信息列表
    private List<LAEntity> lianList;
    // 开庭公告列表
    private List<KTGGEntity> ktggList;
    // 送达公告列表
    private List<SDGGEntity> sdggList;
    // 法院公告列表
    private List<FYGGEntity> fyggList;
    // 失信列表
    private List<SXEntity> sxList;
    // 执行列表
    private List<ZXEntity> zxList;
    // 限高列表
    private List<XGEntity> xgList;
    // 裁判文书列表
    private List<CPWSEntity> cpwsList;
    // 破产重整列表
    private List<PCCZEntity> pcczList;
    // 终本案件列表
    private List<ZBEntity> zbList;
    // 询价评估列表
    private List<XJPGEntity> xjpgList;
    // 股权冻结列表
    private List<GQDJEntity> gqdjList;
    // 环保处罚列表
    private List<HBCFEntity> hbcfList;
    // 行政处罚列表
    private List<XZCFEntity> xzcfList;
    // 司法拍卖列表
    private List<SFPMEntity> sfpmList;
    // 诉前调解列表
    private List<SQTJEntity> sqtjList;
    // 限制出境列表
    private List<XZCJEntity> xzcjList;
    // 选定评估机构列表
    private List<XDPGJGEntity> xdpgjgList;
    // 悬赏公告列表
    private List<XSGGEntity> xsggList;


    //纯案号组
    private Set<String> simpleAnnoSet;

    /**
     * 转为对应JaveBean
     *
     * @param sourceSxList
     * @param sourceZxList
     * @param sourceXgList
     * @param sourceCaseList
     * @param sourcePcczList
     * @param sourceZbList
     * @param sourceXjpgList
     * @param sourceGqdjList
     * @param sourceSdggList
     * @param sourceFyggList
     * @param sourceKtggList
     * @param sourceLianList
     * @param sourceHbcfList
     * @param sourceXzcfList
     */
    public void convert(List<String> sourceSxList, List<String> sourceZxList, List<String> sourceXgList,
                        List<String> sourceCaseList, List<String> sourcePcczList, List<String> sourceZbList,
                        List<String> sourceXjpgList, List<String> sourceGqdjList, List<String> sourceSdggList,
                        List<String> sourceFyggList, List<String> sourceKtggList, List<String> sourceLianList,
                        List<String> sourceHbcfList, List<String> sourceXzcfList
            , List<String> sourceSfpmList, List<String> sourceSqtjList, List<String> sourceXzcjList
            , List<String> sourceXdpgjgList, List<String> sourceXsggList) {
        this.lianList = LAEntity.convert(sourceLianList);
        this.cpwsList = CPWSEntity.convert(sourceCaseList);
        this.sxList = SXEntity.convert(sourceSxList);
        this.zxList = ZXEntity.convert(sourceZxList);
        this.ktggList = KTGGEntity.convert(sourceKtggList);
        this.sdggList = SDGGEntity.convert(sourceSdggList);
        this.fyggList = FYGGEntity.convert(sourceFyggList);
        this.pcczList = PCCZEntity.convert(sourcePcczList);
        this.xjpgList = XJPGEntity.convert(sourceXjpgList);
        this.hbcfList = HBCFEntity.convert(sourceHbcfList);
        this.zbList = ZBEntity.convert(sourceZbList);
        this.xgList = XGEntity.convert(sourceXgList);
        this.gqdjList = GQDJEntity.convert(sourceGqdjList);
        this.xzcfList = XZCFEntity.convert(sourceXzcfList);
        this.sfpmList = SFPMEntity.convert(sourceSfpmList);
        this.sqtjList = SQTJEntity.convert(sourceSqtjList);
        this.xzcjList = XZCJEntity.convert(sourceXzcjList);
        this.xdpgjgList = XDPGJGEntity.convert(sourceXdpgjgList);
        this.xsggList = XSGGEntity.convert(sourceXsggList);
    }
}
