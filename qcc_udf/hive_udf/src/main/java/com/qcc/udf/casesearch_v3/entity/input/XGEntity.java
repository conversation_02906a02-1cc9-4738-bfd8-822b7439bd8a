package com.qcc.udf.casesearch_v3.entity.input;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.casesearch_v3.enums.CaseCategoryEnum;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: zhangqiang
 * @Date: 2020/11/11 17:54
 * @Description:限高
 */
@Data
public class XGEntity extends BaseCaseEntity {
    private String id;
    private String anno;
    private String companynames;
    private String isvalid;
    private String courtname;
    private String provincecode;

    private long judgedate;
    private long publishdate;

    private String xglArray;
    private String glArray;
    private String sqrArray;

    //限高申请人数组(代替sqrArray)
    private String sqrinfo;


    private List<NameAndKeyNoEntity> xglArrayEntityList;
    private List<NameAndKeyNoEntity> glArrayArrayEntityList;
    private List<NameAndKeyNoEntity> sqrArrayEntityList;

    public static List<XGEntity> convert(List<String> jsonList) {
        List<XGEntity> list = new ArrayList<>();
        XGEntity entity = null;
        if (CollectionUtils.isEmpty(jsonList)) {
            return list;
        }
        for (String json : jsonList) {
            if (parquet.Strings.isNullOrEmpty(json)) {
                continue;
            }
            entity = JSON.parseObject(json, XGEntity.class);

            if (entity == null || parquet.Strings.isNullOrEmpty(entity.getId())) {
                continue;
            }

            //公共字段赋值
            entity.setBaseCaseNo(entity.getAnno());
            entity.setBaseCaseCategoryEnum(CaseCategoryEnum.XG);
            if(!Strings.isNullOrEmpty(entity.getCompanynames())){
                entity.setBaseSearchWordSet(Arrays.stream(entity.getCompanynames().split(","))
                        .collect(Collectors.toSet()));
            }

            entity.setBaseCourt(entity.getCourtname());
            entity.setBaseProvinceCode(entity.getProvincecode());

            List<NameAndKeyNoEntity> baseNameKeyNoList = new ArrayList<>();

            String str = entity.getXglArray();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setXglArrayEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getXglArrayEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                }
            }else {
                entity.setXglArrayEntityList(new ArrayList<>());
            }
            str = entity.getGlArray();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setGlArrayArrayEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getGlArrayArrayEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                }
            } else {
                entity.setGlArrayArrayEntityList(new ArrayList<>());
            }
            str = entity.getSqrArray();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setSqrArrayEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getSqrArrayEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                }
            } else {
                entity.setSqrArrayEntityList(new ArrayList<>());
            }

            //Sqrinfo有值，则用它替换申请人信息(用于实时未重做数据的情况下兼容处理)
            if(!Strings.isNullOrEmpty(entity.getSqrinfo())){
                List<NameAndKeyNoEntity> sqrInfoEntityList = JSON.parseArray(entity.getSqrinfo(), NameAndKeyNoEntity.class);
                for (NameAndKeyNoEntity namekey : sqrInfoEntityList) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                }
                if(CollectionUtils.isNotEmpty(sqrInfoEntityList)){
                    entity.setSqrArrayEntityList(sqrInfoEntityList);
                }
            }

            baseNameKeyNoList.addAll(entity.getXglArrayEntityList());
            baseNameKeyNoList.addAll(entity.getGlArrayArrayEntityList());
            baseNameKeyNoList.addAll(entity.getSqrArrayEntityList());

            entity.setBaseNameKeyNoList(baseNameKeyNoList);
            entity.setBaseId(entity.getBaseCaseCategoryEnum().getType()+"_"+entity.getId());
            String caseType= CommonV3Util.getCaseType(CommonV3Util.getCaseNo(entity.getBaseCaseNo()));
            //案件类型为空的数据直接过滤
            if(parquet.Strings.isNullOrEmpty(caseType)){
                continue;
            }
            list.add(entity);
        }
        return list;
    }

}
