package com.qcc.udf.casesearch_v3.entity.input;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.casesearch_v3.enums.CaseCategoryEnum;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 司法拍卖
 */
@Data
public class SFPMEntity  extends BaseCaseEntity {
    /**
     * {
     *     "id":"00102a869fe41bc63f035b4c3c3f58f0",
     *     "companynames":"",
     *     "anno":"[]",
     *     "provincecode":"NMG",
     *     "courtname":"内蒙古自治区乌兰察布市中级人民法院",
     *     "liandate":1498060800,
     *     "isvalid":1,
     *     "ownerkeynoarray":"",
     *     "yiwu":"33540926",
     *     "amountunit":"元",
     *     "evaluationprice":"33540926",
     *     "name":"乌兰察布市中级人民法院关于拍卖乌兰察布市雄鹰蔬菜加工有限责任公司房产、土地及机器设备（第一次拍卖）的公告",
     *     "biaodi":"乌兰察布市雄鹰蔬菜加工有限责任公司房产、土地及机器设备"
     * }
     */

    private String id;

    private String companynames;

    private String anno;

    private String provincecode;

    private String courtname;

    /**
     * 拍卖日期
     */
    private long liandate;

    private int isvalid;

    /**
     * 拍卖物所有人keyno数组
     */
    private String ownerkeynoarray;

    /**
     * 起拍价
     */
    private String yiwu;

    /**
     * 起拍价单位
     */
    private String amountunit;

    /**
     * 评估价
     */
    private String evaluationprice;

    /**
     * 法拍卖名称
     */
    private String name;

    /**
     * 标的
     */
    private String biaodi;

    private List<NameAndKeyNoEntity> ownerkeynoarrayEntityList;


    public static List<SFPMEntity> convert(List<String> jsonList) {
        List<SFPMEntity> list = new ArrayList<>();
        SFPMEntity entity = null;
        if(CollectionUtils.isEmpty(jsonList)){
            return list;
        }
        if(CollectionUtils.isEmpty(jsonList)){
            return list;
        }
        for (String json : jsonList) {
            if(Strings.isNullOrEmpty(json)){
                continue;
            }
            entity = JSON.parseObject(json, SFPMEntity.class);
            if(entity == null  || Strings.isNullOrEmpty(entity.getId())){
                continue;
            }

            Set<String> searchSet = new HashSet<>();
            //案件类型为空的数据也要过滤
            String str = entity.getOwnerkeynoarray();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setOwnerkeynoarrayEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getOwnerkeynoarrayEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                    if(!Strings.isNullOrEmpty(namekey.getKeyNo())){
                        searchSet.add(namekey.getKeyNo());
                    }
                    if(!Strings.isNullOrEmpty(namekey.getName())){
                        namekey.setName(namekey.getName().replace("(", "（").replace(")", "）"));
                        searchSet.add(namekey.getName());
                    }
                }
            }

            //公共字段赋值
            entity.setBaseCaseNo(entity.getAnno());
            entity.setBaseCaseCategoryEnum(CaseCategoryEnum.SFPM);
            entity.setBaseCourt(entity.getCourtname());
            entity.setBaseSearchWordSet(searchSet);
//
//            if(!Strings.isNullOrEmpty(entity.getCompanynames())){
//                entity.setBaseSearchWordSet(Arrays.stream(entity.getCompanynames().split(","))
//                        .collect(Collectors.toSet()));
//            }

            entity.setBaseProvinceCode(entity.getProvincecode());
            entity.setBaseNameKeyNoList(entity.getOwnerkeynoarrayEntityList());
            entity.setBaseId(entity.getBaseCaseCategoryEnum().getType()+"_"+entity.getId());

            String caseType= CommonV3Util.getCaseType(CommonV3Util.getCaseNo(entity.getBaseCaseNo()));
            //案件类型为空的数据直接过滤
            if(Strings.isNullOrEmpty(caseType)){
                continue;
            }
            list.add(entity);
        }
        return list;
    }
}
