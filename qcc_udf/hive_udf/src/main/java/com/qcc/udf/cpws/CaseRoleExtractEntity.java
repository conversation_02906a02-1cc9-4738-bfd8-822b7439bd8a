package com.qcc.udf.cpws;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 裁判文书涉案人员提取信息对象
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CaseRoleExtractEntity {
    // 0-原告 1-被告 2-第三人 3-其他当事人
    @JSONField(name = "RoleTag")
    private Integer RoleTag;
    // 涉案身份
    @JSONField(name = "Role")
    private String Role;
    // 提取到的涉案单位（人员）名称
    @JSONField(name = "Name")
    private String Name;
    // 提取到的涉案单位（人员）对应的keyNo或cerNo
    @JSONField(name = "KeyNo")
    private String KeyNo;
    // 原被告/第三方/其他当事人的代理律师信息对象集合
    @JSONField(name = "LawFirmList")
    private List<LawyerExtractEntity> LawFirmList;

    @JSONField(name = "OperName")
    private String OperName;

    @JSONField(name = "Address")
    private String Address;

    @Override
    public String toString() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("RoleTag", RoleTag);
        jsonObject.put("Role", Role);
        jsonObject.put("Name", (StringUtils.isNotBlank(Name) ? Name : ""));
        jsonObject.put("KeyNo", (StringUtils.isNotBlank(KeyNo) ? KeyNo : ""));
        jsonObject.put("OperName", (StringUtils.isNotBlank(OperName) ? OperName : ""));
        jsonObject.put("Address", (StringUtils.isNotBlank(Address) ? Address : ""));
        JSONArray jsonArray = new JSONArray();
        if (CollectionUtils.isNotEmpty(LawFirmList)) {
            for (LawyerExtractEntity lawyer : LawFirmList) {
                jsonArray.add(JSONObject.parseObject(lawyer.toString()));
            }
        }
        jsonObject.put("LawFirmList", jsonArray);
        return jsonObject.toJSONString();
    }
}