package com.qcc.udf.liu;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

/**
 * <AUTHOR>
 */
public class getSxAmtInfo extends UDF {
    public static String evaluate(List<String> param) {
        String result = "";

        if (param != null && param.size() > 0){
            Map<Long, String> sxMap = new LinkedHashMap<>();
            Map<Long, String> zbMap = new LinkedHashMap<>();
            for (String str : param){
                JSONObject jsonObject = JSONObject.parseObject(str);
                if (StringUtils.isNotEmpty(jsonObject.getString("amt")) && !"0".equals(jsonObject.getString("amt"))){
                    sxMap.put(jsonObject.getLong("publicdate"), jsonObject.getString("amt"));
                }
                if (StringUtils.isNotEmpty(jsonObject.getString("executeobject")) && !"0".equals(jsonObject.getString("executeobject"))){
                    zbMap.put(jsonObject.getLong("judgedate"), jsonObject.getString("executeobject"));
                }
            }

            if (sxMap.size() > 0){
                Set<Long> keySet = sxMap.keySet();
                long max = 0L;
                for (Long l : keySet){
                    if (l >= max){
                        max = l.longValue();
                    }
                }
                result = sxMap.get(max);
            }else{
                Set<Long> keySet = zbMap.keySet();
                long max = 0L;
                for (Long l : keySet){
                    if (l >= max){
                        max = l.longValue();
                    }
                }
                result = zbMap.get(max);
            }

        }


        return result;
    }

    public static void main(String[] args) {
        List<String> infoList = new LinkedList<>();
        infoList.add("{\"publicdate\":0,\"amt\":\"0\",\"judgedate\":\"1585843200\",\"executeobject\":\"46358\"}");
        System.out.println(evaluate(infoList));
    }
}
