package com.qcc.udf.cpws;


import org.apache.hadoop.hive.ql.exec.UDF;

public class deal_symbol extends UDF {

    public String evaluate(String s) {
        if (s != null && s.length() > 0) {
            s = s.replace("\\", "").replace("（", "(").replace("）", ")").replace("。", "").replace(";", ",").replace("！", "!").replace("？", "?").replace("，", ",").replace(" ", "").replace("　", "").replace("\t", "");
            return s;
        } else {
            return null;
        }
    }
}