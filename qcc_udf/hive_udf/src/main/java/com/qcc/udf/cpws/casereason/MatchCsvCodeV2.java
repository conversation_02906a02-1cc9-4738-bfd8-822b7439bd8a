package com.qcc.udf.cpws.casereason;

import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * 匹配Excel中的code，读取标签
 *
 * <AUTHOR>
 */
public class MatchCsvCodeV2 extends UDF {


    public static String evaluate(String code) {
        return CaseCodeMatchUtil.checkRole(code);
    }


    public static void main(String[] args) {
        String code = "A,A03,A0303,A030306";
        System.out.println(evaluate(code));
    }
}
