package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDF;

//注解
@Description(name="Unicode_verifyUDF",
        value = "_FUNC_(string)-verify if the str is in unicode")

public class UnicodeVerify extends UDF {

    public int evaluate(String str) {
        if (str == null) {
            return 1;
        }
        String str1 = str.replaceAll("[^\\u0000-\\uFFFF]", "");
        return str.equals(str1)?1:0;
    }
}
