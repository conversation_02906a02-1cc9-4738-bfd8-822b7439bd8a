package com.qcc.udf.kzz;

import com.qcc.udf.CompanyTypeJudy;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class GetCompanyLabel extends UDF {
    //企业简介，经营范围
    private final static String SCOPE_SALES_REGEX = "销售|租赁|售卖|批发|零售|经销|内销|外销";
    private final static String SCOPE_PRODUCTION_REGEX = "生产|加工|包装|制造";
    //所属行业
    private final static String INDUSTRY_SALES_REGEX = "F|L";
    private final static String INDUSTRY_PRODUCTION_REGEX = "C";
    //招聘岗位
    private final static String RECRUITMENT_SALES_REGEX = "销售|业务员|客户经理|营业员";
    private final static String RECRUITMENT_PRODUCTION_REGEX = "生产主管|车间工人|生产工人|车间普工|车间主任|车间操作|车间经理|厂长|厂长经理|厂|化工";
    //企业名称
    private final static String COMPANY_NAME_SALES_REGEX = "贸易|销售|零售|批发";
    private final static String COMPANY_NAME_PRODUCTION_REGEX = "加工厂|厂|机械制造|制造|化工";
    //进出口信信用登记
    private final static String CIE_SALES_REGEX = "CI";
    //资质证书类型
    private final static String CERTIFICATE_PRODUCTION_REGEX = "安全生产许可证取证|其他自愿性工业产品认证|环境管理体系认证";

//    public static void main(String[] args) {
//        String a = " 企查查是企查查科技有限公司旗下的一款企业信用查询工具，旨在为用户提供快速查询企业工商信息、法院判决信息、关联企业信息、法律诉讼、失信信息、被执行人信息、知识产权信息、公司新闻、企业年报等服务。 公司总部位于苏州2.5产业园，依赖超强研发实力，公司拥有大数据挖掘，数据建模，行业标准定义和可视化分析技术，同时公司在北京、上海建立了研发团队。公司核心团队成员有十多年互联网专业知识，通过在实践中不停创新，持续迭代，为顾客提供最优质的服务。我们的口号是: 缔造有远见的商业传奇！";
//        String b = "计算机软件开发、计算机信息技术服务；通信系统自动化软硬件的开发，并提供技术咨询、技术服务；计算机软硬件的销售及维护；软件设计及技术转让，并提供相关技术服务；企业管理咨询；市场调查；企业征信业务；企业信用评估；信用管理咨询。（依法须经批准的项目，经相关部门批准后方可开展经营活动）许可项目：第二类增值电信业务；互联网信息服务（依法须经批准的项目，经相关部门批准后方可开展经营活动，具体经营项目以审批结果为准）一般项目：互联网数据服务；大数据服务；人工智能应用软件开发（除依法须经批准的项目外，凭营业执照依法自主开展经营活动）";
//        String c = "G";
//        String d = "研发工程师，大数据";
//        String e = "企查查科技有限公司";
//        String f = "质量管理体系认证（ISO9000） 软件产品证书";
//        String g = "K,AOP,CPWS,CI";
//        String h = "有限责任公司";
//        String i = "91320594088140947F";
//        String j = "320594000299470";
//        String result = evaluate(a, b, c, d, e, f, g, h, i, j);
//        System.out.printf(result);
//    }

    /**
     * 获取当前企业标识
     *
     * @param introduction(企业简介)
     * @param scope(经营范围)
     * @param industry(行业一级分类)
     * @param position(招聘岗位)
     * @param companyName(企业名称)
     * @param certificateType(资质证书类型)
     * @param flag(公司标签)
     * @param econKind(公司类型)
     * @param creditCode(统一社会信用代码)
     * @param regNo(注册号)
     */
    public static String evaluate(String introduction, String scope, String industry, String position, String companyName, String certificateType, String flag, String econKind, String creditCode, String regNo) {
        String salesTag = "KZZ_SALES_COMP";
        String manufactureTag = "KZZ_MANUFACTURE_COMP";
        List<String> flagList = new ArrayList<>();
        try {
            String type = CompanyTypeJudy.evaluate(econKind, creditCode, regNo);
            if (type == "个体工商户" || type == "农民专业合作经济组织") {
                return "";
            }
            //企业简介，经营范围
            if (StringUtils.isNotBlank(introduction) || StringUtils.isNotBlank(scope)) {
                if (RegexHelper.isFind(introduction, SCOPE_SALES_REGEX) || RegexHelper.isFind(scope, SCOPE_SALES_REGEX)) {
                    flagList.add(salesTag);
                }
                if (RegexHelper.isFind(introduction, SCOPE_PRODUCTION_REGEX) || RegexHelper.isFind(scope, SCOPE_PRODUCTION_REGEX)) {
                    if (introduction.contains("包装") || scope.contains("包装")) {
                        if (!RegexHelper.isFind(companyName, "货运|物流|运输|快运|托运|快递|储运")) {
                            flagList.add(manufactureTag);
                        }
                    } else {
                        flagList.add(manufactureTag);
                    }
                }
            }
            //行业
            if (StringUtils.isNotBlank(industry) && flagList.size() < 2) {
                if (RegexHelper.isFind(industry, INDUSTRY_SALES_REGEX)) {
                    flagList.add(salesTag);
                }
                if (RegexHelper.isFind(industry, INDUSTRY_PRODUCTION_REGEX)) {
                    flagList.add(manufactureTag);
                }
            }
            //招聘岗位
            if (StringUtils.isNotBlank(position) && flagList.size() < 2) {
                if (RegexHelper.isFind(position, RECRUITMENT_SALES_REGEX)) {
                    flagList.add(salesTag);
                }
                if (RegexHelper.isFind(position, RECRUITMENT_PRODUCTION_REGEX)) {
                    flagList.add(manufactureTag);
                }
            }
            //企业名称
            if (StringUtils.isNotBlank(companyName) && flagList.size() < 2) {
                if (RegexHelper.isFind(companyName, COMPANY_NAME_SALES_REGEX)) {
                    flagList.add(salesTag);
                }
                if (RegexHelper.isFind(companyName, COMPANY_NAME_PRODUCTION_REGEX)) {
                    flagList.add(manufactureTag);
                }
            }
            //资质证书
            if (StringUtils.isNotBlank(certificateType) && flagList.size() < 2) {
                if (RegexHelper.isFind(certificateType, CERTIFICATE_PRODUCTION_REGEX)) {
                    flagList.add(manufactureTag);
                }
            }
            //进出口信用登记
            if (StringUtils.isNotBlank(flag) && flagList.size() < 2) {
                List<String> creditList = Arrays.asList(flag.split(","));
                if (creditList.contains(CIE_SALES_REGEX)) {
                    flagList.add(salesTag);
                }
            }
        } catch (Exception e) {

        }
        return StringUtils.join(flagList.stream().distinct().sorted().collect(Collectors.toList()), ",");
    }
}