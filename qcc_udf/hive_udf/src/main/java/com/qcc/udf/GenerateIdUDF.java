package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;


public class GenerateIdUDF extends UDF {

    public String evaluate(long seed) throws InterruptedException {
      

        return GenerateId(seed);
    }

    public String GenerateId(long seed) throws InterruptedException {
        String[] numbers = new String[] { "1", "2", "3", "4", "5", "6", "7", "8", "9" };
        String[] numbers2 = new String[] { "0","1", "2", "3", "4", "5", "6", "7", "8", "9" };
        String[] chars = new String[] { "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G","J", "H", "I", "G", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z" };
        Thread.sleep(1);
    
        long tick = new Date().getTime();
        // Random random ;
        // if(type == 0){
        //     random = new Random((int)(tick & 0xffffffffL) | (int)(tick >> 32));
        // }
        // else{
        //     random = new Random();
        // } 

        Random random = new Random(seed);
         
        int index = random.nextInt(9);

        String str = numbers[index];
        index = random.nextInt(10);
        String str2 = numbers2[index];
        //var id = ShortId.Generate(true, false, 8).Substring(0, 5).ToUpper();
    
        index = random.nextInt(37);
        String str3 = chars[index];
    
        index = random.nextInt(37);
        String str4 = chars[index];
    
        index = random.nextInt(37);
        String str5 = chars[index];
      
        index = random.nextInt(37);
        String str6 = chars[index];
    
        index = random.nextInt(37);
        String str7 = chars[index];
    
        return str + str2 + str3 + str4 + str5 + str6 + str7;
    
    
       }
   
    
}