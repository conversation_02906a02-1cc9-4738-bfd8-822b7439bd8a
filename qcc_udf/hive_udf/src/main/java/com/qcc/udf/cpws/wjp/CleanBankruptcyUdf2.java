package com.qcc.udf.cpws.wjp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.cpws.HtmlToTextUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.List;

public class CleanBankruptcyUdf2 extends UDF {
    public static List<String> fujianList = Arrays.asList("见附件", "下载附件");

    public static String evaluate(String content, String title, String pdfList) {

        Boolean showPDFFlag = false;
        // 标题包含正文 展示附件
        if (StringUtils.isNotEmpty(title) && StringUtils.isNotEmpty(content)) {
            content = HtmlToTextUtil.convert(content);
            content = content.replace("\\n", "").replace("\n", "");
            if (title.contains(content) || title.replace("的", "").contains(content)) {

                showPDFFlag = true;
            }

        }
        if (StringUtils.isNotEmpty(content) && content.length() > 2) {
            //正文符合条件展示附件
            if (content.length() <= 100) {
                for (String s : fujianList) {
                    if (content.contains(s)) {
                        showPDFFlag = true;
                    }
                }
            }
        } else {
            //正文为空展示附件
            showPDFFlag = true;
        }
        if (StringUtils.isNotEmpty(pdfList)) {
            List<JSONObject> pdfJSONList = JSON.parseArray(pdfList, JSONObject.class);
            if (CollectionUtils.isNotEmpty(pdfJSONList) && pdfJSONList.size() == 1) {
                return showPDFFlag.toString();
            }
        }
        return "false";
    }

    public static void main(String[] args) {

        String a = "关于职工债权的公示\n";

        System.out.println(evaluate(a, "（2023）苏0685破42号关于职工债权的公示"
                , "[{\"fileName\":\"25关于职工债权的公示.pdf\",\"url\":\"http://qccdata.qichacha.com/BankRuptcy/eb1c452972e65de9763d20f77bbcfd6b.pdf\"}]"));
    }
}
