package com.qcc.udf.cpws;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.stream.Collectors;

public class GetDetailInfoMSUDF extends UDF {

    public String evaluate(List<String> sourceLianList, List<String> sourceKtggList,
                           List<String> sourceSdggList, List<String> sourceFyggList, List<String> sourceCaseList) {
        LawSuitEntity inputLawSuitEntity = new LawSuitEntity();
        inputLawSuitEntity.setLianList(sourceLianList);
        inputLawSuitEntity.setKtggList(sourceKtggList);
        inputLawSuitEntity.setSdggList(sourceSdggList);
        inputLawSuitEntity.setFyggList(sourceFyggList);
        inputLawSuitEntity.setCaseList(sourceCaseList);
        List<LawSuitEntity> lawSuitEntityList = CommonUtil.getLawSuitEntityList(inputLawSuitEntity, "ms");

        JSONArray detailInfoArray = new JSONArray();
        for (LawSuitEntity lawSuitEntity : lawSuitEntityList) {
            List<String> lianList = lawSuitEntity.getLianList();
            List<String> ktggList = lawSuitEntity.getKtggList();
            List<String> sdggList = lawSuitEntity.getSdggList();
            List<String> fyggList = lawSuitEntity.getFyggList();
            List<String> caseList = lawSuitEntity.getCaseList();

            Set<String> provinceCodeSet = CommonUtil.collectProvinceCode(lianList, ktggList, sdggList, fyggList, caseList);
            for (String provinceCode : provinceCodeSet) {
                JSONObject result = new JSONObject();
                try {
                    /**
                     * 各审理程序列表对应字段
                     */
                    // 审理程序总数
                    Set<String> caseNoSet = new LinkedHashSet<>();
                    // 搜索关键字集合
                    Set<String> searchWordSet = new HashSet<>();
                    // 所有时间节点的map集合（key-> 时间戳; value->表示当前的节点状态，审判程序 + (立案/法院公告/开庭公告/送达公告)发布日期 或 判决日期 或 裁定日期）
                    Map<Long, String> dateNodeMap = new HashMap<>();

                    // 案号分组
                    Map<String, JSONObject> anNoMap = new LinkedHashMap<>();
                    Set<String> lianIdSet = new LinkedHashSet<>();
                    Set<String> ktggIdSet = new LinkedHashSet<>();
                    Set<String> sdggIdSet = new LinkedHashSet<>();
                    Set<String> fyggIdSet = new LinkedHashSet<>();
                    Set<String> caseIdSet = new LinkedHashSet<>();

                    dataClean(provinceCode, lianList, 1, anNoMap, caseNoSet, lianIdSet, searchWordSet, dateNodeMap);
                    dataClean(provinceCode, ktggList, 2, anNoMap, caseNoSet, ktggIdSet, searchWordSet, dateNodeMap);
                    dataClean(provinceCode, sdggList, 3, anNoMap, caseNoSet, sdggIdSet, searchWordSet, dateNodeMap);
                    dataClean(provinceCode, fyggList, 4, anNoMap, caseNoSet, fyggIdSet, searchWordSet, dateNodeMap);
                    dataClean(provinceCode, caseList, 5, anNoMap, caseNoSet, caseIdSet, searchWordSet, dateNodeMap);

                    // 按照案号显示数据
                    JSONArray array = new JSONArray();
                    for (String str : caseNoSet) {
                        /**
                         * 兼容执行案件中的infoList项
                         */
                        JSONObject jsonObj = anNoMap.get(str);
                        if (jsonObj != null) {
                            jsonObj = CommonUtil.addExternalFieldToJsonStruct(jsonObj);
                            Long latestTimestamp = CommonUtil.getLatestTimestampFromInfoListItem(jsonObj);
                            jsonObj.put("LatestTimestamp", latestTimestamp);
                            array.add(jsonObj);
                        }
                    }
                    result.put("InfoList", array);
                    result.put("AnNoList", StringUtils.join(caseNoSet, ","));

                    /**
                     * 案件统计字段
                     */
                    // 最新审理程序
                    result.put("LatestTrialRound", CommonUtil.getLatestTrialRoundFromInfoList(array));
                    // 审理程序总数
                    result.put("AnnoCnt", caseNoSet.size());
                    // 关联裁判文书数 / 有效裁判文书数
                    result.put("CaseCnt", caseIdSet.size());
                    // 关联立案信息数 / 有效立案信息数
                    result.put("LianCnt", lianIdSet.size());
                    // 关联开庭公告数 / 有效开庭公告数
                    result.put("KtggCnt", ktggIdSet.size());
                    // 关联送达公告数 / 有效送达公告数
                    result.put("SdggCnt", sdggIdSet.size());
                    // 关联法院公告数 / 有效法院公告数
                    result.put("FyggCnt", fyggIdSet.size());

                    // 兼容字段处理
                    // 关联被执行数 / 有效被执行数
                    result.put("ZxCnt", 0);
                    // 关联失信数 / 有效失信数
                    result.put("SxCnt", 0);
                    // 关联限高数 / 有效限高数
                    result.put("XgCnt", 0);

                    /**
                     * 案件基础字段
                     */
                    // 分组法院信息
                    result.put("GroupCourt", lawSuitEntity.getCourt());
                    // 所在省份编码
                    result.put("Province", provinceCode);
                    // 案件名称
                    result.put("CaseName", CommonUtil.getCaseNameFromInfoList(array, "ms"));
                    // 案件类型
                    result.put("CaseType", "民事案件");
                    // 关联的公司或个人信息
                    result.put("CompanyKeywords", CommonUtil.getCompanyKeywordsFromSearchWordSet(searchWordSet));
                    // 相关案号
                    result.put("AnNoList", CommonUtil.getKeywordsFromInfoList(array, "AnNo"));
                    // 列表中的案由
                    result.put("CaseReason", CommonUtil.getCaseReasonFromInfoList(array));
                    // 列表中的案件身份
                    result.put("CaseRole", getCaseRoleInfo(caseList, lianList, ktggList, provinceCode));
                    // 相关法院
                    result.put("CourtList", CommonUtil.getKeywordsFromInfoList(array, "Court"));
                    // 相关检察院
                    result.put("ProcuratorateList", CommonUtil.getKeywordsFromInfoList(array, "Procuratorate"));

                    Map<Long, String> sortedDateNodeMap = new LinkedHashMap<>();
                    dateNodeMap.entrySet().stream()
                            .sorted(Map.Entry.comparingByKey())
                            .forEachOrdered(e -> sortedDateNodeMap.put(e.getKey(), e.getValue()));

                    result.put("EarliestDate", -1L);
                    result.put("EarliestDateType", "");
                    result.put("LastestDate", -1);
                    result.put("LastestDateType", "");

                    Long earliestDate = -1L;
                    String earliestDateType = "";
                    Long lastestDate = -1L;
                    String lastestDateType = "";
                    boolean flag = true;
                    for (Map.Entry<Long, String> sortedDateNodeEntry : sortedDateNodeMap.entrySet()) {
                        if (flag) {
                            earliestDate = sortedDateNodeEntry.getKey();
                            earliestDateType = sortedDateNodeEntry.getValue();
                            flag = false;
                        }
                        lastestDate = sortedDateNodeEntry.getKey();
                        lastestDateType = sortedDateNodeEntry.getValue();
                    }
                    result.put("EarliestDate", earliestDate);
                    result.put("EarliestDateType", CommonUtil.getDataTypeWithoutTrialRound(earliestDateType));
                    if (sortedDateNodeMap.size() > 1) {
                        result.put("LastestDate", lastestDate);
                        result.put("LastestDateType", CommonUtil.getDataTypeWithoutTrialRound(lastestDateType));
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                detailInfoArray.add(result);
            }
        }

        JSONArray resDetailInfoJSONArray = new JSONArray();
        Iterator iterator = detailInfoArray.iterator();
        while (iterator.hasNext()) {
            try {
                JSONObject jsonObject = (JSONObject) iterator.next();
                if (jsonObject.getLong("AnnoCnt") > 0) {
                    resDetailInfoJSONArray.add(jsonObject);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return resDetailInfoJSONArray.toJSONString();
    }

    public static void dataClean(String provinceCode, List<String> infoList, int type, Map<String, JSONObject> anNoMap,
                                 Set<String> caseNoSet, Set<String> idSet, Set<String> searchWordSet, Map<Long, String> dateNodeMap){
        // 编辑数据
        if (infoList != null && infoList.size() > 0) {
            Iterator it = infoList.iterator();
            while(it.hasNext()){
                JSONObject json = JSONObject.parseObject(it.next().toString());
                if (json == null || json.isEmpty()) {
                    continue;
                }
                // 过滤涉诉历史信息记录
                if (json.getInteger("isvalid") != 1) {
                    continue;
                }
                if (!json.getString("province").equals(provinceCode)) {
                    continue;
                }

                // 获取案号
                String anNo = CommonUtil.full2Half(json.getString("anno"));
                if (type == 5){
                    anNo = CommonUtil.full2Half(json.getString("caseno"));
                }
                // 过滤掉案号没有对应到执行案件类型的记录
                if (!new ExtractCaseTypeUDF().evaluate(anNo).equals("民事案件")) {
                    continue;
                }

                caseNoSet.add(anNo);

                // 部分字段的汇总逻辑
                try {
                    List<String> companyNameList = new ArrayList<>();
                    if (type == 1) {
                        companyNameList = Arrays.stream(json.getString("companykeywords").split(","))
                                .collect(Collectors.toList());
                    } else if (type == 2 || type == 3 || type == 5) {
                        companyNameList = Arrays.stream(json.getString("companynames").split(","))
                                .collect(Collectors.toList());
                    } else if (type == 4) {
                        JSONArray jsonArray = JSONArray.parseArray(json.getString("nameandkeyno"));
                        for (int i = 0; i < jsonArray.size(); i++) {
                            JSONObject jsonObject = jsonArray.getJSONObject(i);
                            companyNameList.add(jsonObject.getString("Name"));
                            companyNameList.add(jsonObject.getString("KeyNo"));
                        }
                    }

                    // 汇总关联公司或个人信息
                    for (String companyName : companyNameList) {
                        searchWordSet.add(companyName);
                    }
                } catch (Exception ex) {
                }

                // 判断该案号是否已经存在
                JSONObject jsonObject = new JSONObject();
                JSONArray itemArray = new JSONArray();
                JSONObject itemJson = new JSONObject();

                // 不存在，则创建新的对象
                if (!anNoMap.keySet().contains(anNo)){
                    // 列表数据
                    itemJson = editItemJson(json, type, dateNodeMap, anNo);
                }else{
                    // 存在则获取原有列表，进行数据补充
                    jsonObject = anNoMap.get(anNo);
                    // 列表数据
                    if (type == 1){
                        itemArray = jsonObject.getJSONArray("LianList");
                    }else if (type == 2){
                        itemArray = jsonObject.getJSONArray("KtggList");
                    }else if (type == 3){
                        itemArray = jsonObject.getJSONArray("SdggList");
                    }else if (type == 4){
                        itemArray = jsonObject.getJSONArray("FyggList");
                    }else if (type == 5){
                        itemArray = jsonObject.getJSONArray("CaseList");
                    }
                    if (!idSet.contains(json.getString("id"))){
                        itemJson = editItemJson(json, type, dateNodeMap, anNo);
                    }
                }
                idSet.add(json.getString("id"));
                itemArray = itemArray == null ? new JSONArray() : itemArray;
                itemArray.add(itemJson);
                if (type == 1){
                    jsonObject.put("LianList", itemArray);
                }else if (type == 2){
                    jsonObject.put("KtggList", itemArray);
                }else if (type == 3){
                    jsonObject.put("SdggList", itemArray);
                }else if (type == 4){
                    jsonObject.put("FyggList", itemArray);
                }else if (type == 5){
                    jsonObject.put("CaseList", itemArray);
                }
                jsonObject.put("AnNo", anNo);
                jsonObject.put("TrialRound", new ExtractCaseTrialRoundUDF().evaluate(anNo));
                jsonObject.put("CaseReason", "");
                jsonObject.put("Court", "");
                jsonObject.put("Procuratorate", "");

                if (type == 1 || type == 2) {
                    String caseRole = CommonUtil.getCaseRoleFromLianOrKtggList(Arrays.asList(json.toJSONString()), type);
                    JSONArray caseRoleJsonArray = JSONArray.parseArray(caseRole);
                    if (caseRoleJsonArray != null && caseRoleJsonArray.size() > 0) {
                        JSONArray prosecutorArray = new JSONArray();
                        JSONArray defendantArray = new JSONArray();
                        for (int i = 0; i < caseRoleJsonArray.size(); i++) {
                            JSONObject caseRoleJson = caseRoleJsonArray.getJSONObject(i);

                            JSONObject jsonObj = new JSONObject();
                            jsonObj.put("Name", caseRoleJson.getString("P"));
                            jsonObj.put("KeyNo", caseRoleJson.getString("N"));
                            jsonObj.put("Role", caseRoleJson.getString("R"));
                            jsonObj.put("Org", caseRoleJson.getInteger("O"));

                            if (caseRoleJson.getString("R").equals("原告")) {
                                prosecutorArray.add(jsonObj);
                            } else if (caseRoleJson.getString("R").equals("被告")) {
                                defendantArray.add(jsonObj);
                            }
                        }
                        jsonObject.put("Prosecutor", prosecutorArray);
                        jsonObject.put("Defendant", defendantArray);
                    } else {
                        jsonObject.put("Prosecutor", new JSONArray());
                        jsonObject.put("Defendant", new JSONArray());
                    }

                    // 提取法院公告中的案由信息
                    if (type == 2) {
                        String caseReason = json.getString("casereason");
                        if (StringUtils.isNotBlank(caseReason)) {
                            jsonObject.put("CaseReason", caseReason);
                        }
                    }

                    // 法院提取
                    String court = json.getString("executegov");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }
                } else if (type == 3 || type == 4) {
                    if (jsonObject.getJSONArray("Defendant") == null) {
                        jsonObject.put("Defendant", new JSONArray());
                    }

                    if (jsonObject.getJSONArray("Prosecutor") == null) {
                        jsonObject.put("Prosecutor", new JSONArray());
                    }

                    // 法院提取
                    String court = json.getString("courtname");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }
                    String anotherCourt = json.getString("court");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", anotherCourt);
                    }
                } else if (type == 5) { // 从裁判文书信息中提取案由 / 当事人（双方）/ 执行法院
                    String trialRound = json.getString("trialround");
                    if (StringUtils.isNotBlank(trialRound)) {
                        jsonObject.put("TrialRound", trialRound);
                    }

                    String caseReason = json.getString("casereason");
                    if (StringUtils.isNotBlank(caseReason)) {
                        jsonObject.put("CaseReason", caseReason);
                    }

                    JSONArray prosecutorArray = CommonUtil.getLitigantJSONArray(json.getString("prosecutor"), json.getString("caserole"));
                    if (prosecutorArray != null && prosecutorArray.size() > 0) {
                        jsonObject.put("Prosecutor", prosecutorArray);
                    }

                    JSONArray defendantArray = CommonUtil.getLitigantJSONArray(json.getString("defendant"), json.getString("caserole"));
                    if (defendantArray != null && defendantArray.size() > 0) {
                        jsonObject.put("Defendant", defendantArray);
                    }

                    String court = json.getString("court");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }

                    JSONObject protestorganJsonObj = JSONObject.parseObject(json.getString("protestorgan"));
                    if (protestorganJsonObj != null && protestorganJsonObj.containsKey("name")) {
                        jsonObject.put("Procuratorate", protestorganJsonObj.getString("name"));
                    }
                }
                anNoMap.put(anNo, jsonObject);
            }
        }
    }

    public static JSONObject editItemJson(JSONObject jsonObject, int type, Map<Long, String> dateNodeMap, String anNo){
        JSONObject result = new JSONObject();

        String trialRound = new ExtractCaseTrialRoundUDF().evaluate(anNo);
        // 编辑字段
        result.put("Id", jsonObject.getString("id"));
        result.put("IsValid", jsonObject.getInteger("isvalid"));

        if (type == 1) {        // 立案信息（lianList）
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = CommonUtil.parseDateToTimeStamp(jsonObject.getString("punishdate"));
            result.put("LianDate", publishDate);
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "立案日期");
            }
        } else if (type == 2) { // 开庭公告（ktggList）
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long openDate = CommonUtil.parseDateToTimeStamp(jsonObject.getString("liandate"));
            result.put("OpenDate", openDate);
            if (openDate != -1) {
                dateNodeMap.put(openDate, trialRound + "|" + "开庭时间");
            }
        } else if (type == 3) { // 送达公告（sdggList）
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = CommonUtil.parseDateToTimeStamp(jsonObject.getString("publishdate"));
            result.put("PublishDate", publishDate);
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "送达公告发布日期");
            }
        } else if (type == 4) { // 法院公告（fyggList）
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("Category", jsonObject.getString("category"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = CommonUtil.parseDateToTimeStamp(jsonObject.getString("publishdate"));
            result.put("PublishDate", publishDate);
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "法院公告刊登日期");
            }
        } else if (type == 5) { // 裁判文书（caseList）
            result.put("Id", jsonObject.getString("id"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));
            Long judgeDate = CommonUtil.parseDateToTimeStamp(jsonObject.getString("judgedate"));
            result.put("JudgeDate", judgeDate);

            String docType = jsonObject.getString("doctype");
            if (StringUtils.isNotBlank(docType)) {
                if (docType.equals("ver")) {
                    result.put("DocType", "民事判决日期");
                    dateNodeMap.put(judgeDate, trialRound + "|" + "判决日期");
                } else {
                    result.put("DocType", "民事裁定日期");
                    dateNodeMap.put(judgeDate, trialRound + "|" + "裁定日期");
                }
            }
        }

        return result;
    }

    /**
     * 提取案件角色信息
     */
    private static String getCaseRoleInfo(List<String> caseList, List<String> lianList, List<String> ktggList, String provinceCode) {
        /**
         * 提取规则
         * 1，如果有裁判文书，取裁判文书中的当事人信息 - caserole
         * 2，如果裁判文书缺失，取开庭公告中的当事人信息 - prosecutorlistos / defendantlistos
         * 3，如果1、2都缺失，取立案信息中的当事人信息 - prosecutorlistos / defendantlistos
         */
        String caseRole = CommonUtil.getCaseRoleFromCaseList(caseList, provinceCode);
        if (caseRole.equals(new JSONArray().toJSONString())) {
            caseRole = CommonUtil.getCaseRoleFromLianOrKtggList(ktggList, 2);
        }
        if (caseRole.equals(new JSONArray().toJSONString())) {
            caseRole = CommonUtil.getCaseRoleFromLianOrKtggList(lianList, 1);
        }
        return caseRole;
    }

    public static void main(String[] args) {

        List<String> lianList = new ArrayList<>();

        List<String> ktggList = new ArrayList<>();

        List<String> sdggList= new ArrayList<>();

        List<String> fyggList = new ArrayList<>();

        List<String> caseList = new ArrayList<>();
        caseList.add("{\"id\":\"2e0233d8adca36cb4e1400685eabe2600\",\"defendant\":\"刘忠,潘登,张宗祥,向彩兰\",\"prosecutor\":\"刘克明\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"刘克明\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"刘忠\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"向彩兰\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"张宗祥\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"潘登\\\"}]\",\"caseno\":\"（2014）鄂恩施中民终字第00231号\",\"submitdate\":\"2014-08-20T08:00:00+08:00\",\"judgedate\":\"2014-06-27T08:00:00+08:00\",\"courtdate\":\"2014-08-20T08:00:00+08:00\",\"casereason\":\"提供劳务受害者责任纠纷\",\"isvalid\":\"1\",\"trialround\":\"民事二审\",\"court\":\"湖北省恩施土家族苗族自治州中级人民法院\",\"companynames\":\"刘忠,刘克明,潘登,张宗祥,向彩兰\",\"caserole\":\"[{\\\"P\\\":\\\"刘克明\\\",\\\"R\\\":\\\"上诉人（原审被告）\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"刘忠\\\",\\\"R\\\":\\\"被上诉人（原审被告）\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"向彩兰\\\",\\\"R\\\":\\\"被上诉人（原审被告）\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"张宗祥\\\",\\\"R\\\":\\\"被上诉人（原审被告）\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"潘登\\\",\\\"R\\\":\\\"被上诉人（原审原告）\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2}]\",\"doctype\":\"ver\",\"protestorgan\":\"\",\"province\":\"HUB\"}");
        caseList.add("{\"id\":\"ab0e5d113597a6205e5e58a098165ef50\",\"defendant\":\"\",\"prosecutor\":\"\",\"nameandkeyno\":\"[]\",\"caseno\":\"（2013）鄂恩施民初字第01708号\",\"submitdate\":null,\"judgedate\":\"2014-03-19T08:00:00+08:00\",\"courtdate\":null,\"casereason\":\"\",\"isvalid\":\"1\",\"trialround\":\"民事一审\",\"court\":\"恩施市人民法院\",\"companynames\":\"\",\"caserole\":\"[]\",\"doctype\":\"other\",\"protestorgan\":\"\",\"province\":\"HUB\"}");
        caseList.add("{\"id\":\"1da428554e87b0742d46fcd0bb3a1e950\",\"defendant\":\"刘忠,刘克明,张宗祥,向彩兰\",\"prosecutor\":\"潘登\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"潘登\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"刘克明\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"刘忠\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"向彩兰\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"张宗祥\\\"}]\",\"caseno\":\"（2013）鄂恩施民初字第01708号\",\"submitdate\":\"2014-10-29T08:00:00+08:00\",\"judgedate\":\"2014-01-06T08:00:00+08:00\",\"courtdate\":\"2014-10-29T08:00:00+08:00\",\"casereason\":\"提供劳务受害者责任纠纷\",\"isvalid\":\"1\",\"trialround\":\"民事一审\",\"court\":\"恩施市人民法院\",\"companynames\":\"刘忠,刘克明,潘登,张宗祥,向彩兰\",\"caserole\":\"[{\\\"P\\\":\\\"潘登\\\",\\\"R\\\":\\\"原告\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"刘克明\\\",\\\"R\\\":\\\"被告\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"湖北正典律师事务所\\\",\\\"R\\\":\\\"代理律师事务所\\\",\\\"N\\\":\\\"w9544e1d87ea0c975585e8c9239833d8\\\",\\\"O\\\":4},{\\\"P\\\":\\\"刘忠\\\",\\\"R\\\":\\\"被告\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"向彩兰\\\",\\\"R\\\":\\\"被告\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"张宗祥\\\",\\\"R\\\":\\\"被告\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2}]\",\"doctype\":\"ver\",\"protestorgan\":\"\",\"province\":\"HUB\"}");
        caseList.add("{\"id\":\"20152d991714913540c18392e17ac3a90\",\"defendant\":\"\",\"prosecutor\":\"\",\"nameandkeyno\":\"[]\",\"caseno\":\"（2013）鄂恩施民初字第01708号\",\"submitdate\":null,\"judgedate\":\"2014-03-19T08:00:00+08:00\",\"courtdate\":null,\"casereason\":\"\",\"isvalid\":\"1\",\"trialround\":\"民事一审\",\"court\":\"恩施市人民法院\",\"companynames\":\"\",\"caserole\":\"[]\",\"doctype\":\"ver\",\"protestorgan\":\"\",\"province\":\"HUB\"}");
        caseList.add("{\"id\":\"f594802747ac1c26fba9743ca56342d80\",\"defendant\":\"刘忠,张宗祥,潘登,向彩兰\",\"prosecutor\":\"刘克明\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"刘克明\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"潘登\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"刘忠\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"向彩兰\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"张宗祥\\\"}]\",\"caseno\":\"（2015）鄂恩施中民再终字第00011号\",\"submitdate\":\"2015-10-29T08:00:00+08:00\",\"judgedate\":\"2015-09-19T08:00:00+08:00\",\"courtdate\":\"2015-10-29T08:00:00+08:00\",\"casereason\":\"提供劳务受害者责任纠纷\",\"isvalid\":\"1\",\"trialround\":\"民事再审\",\"court\":\"湖北省恩施土家族苗族自治州中级人民法院\",\"companynames\":\"刘忠,刘克明,张宗祥,潘登,向彩兰\",\"caserole\":\"[{\\\"P\\\":\\\"刘克明\\\",\\\"R\\\":\\\"再审申请人（一审被告二审上诉人）\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"潘登\\\",\\\"R\\\":\\\"被申请人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"湖北领汇律师事务所\\\",\\\"R\\\":\\\"代理律师事务所\\\",\\\"N\\\":\\\"w0e5b830f628d1fdb87dd7238515a81d\\\",\\\"O\\\":4},{\\\"P\\\":\\\"刘忠\\\",\\\"R\\\":\\\"被申请人（一审被告二审被上诉人）\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"向彩兰\\\",\\\"R\\\":\\\"被申请人（一审被告二审被上诉人）\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"张宗祥\\\",\\\"R\\\":\\\"被申请人（一审被告二审被上诉人）\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"湖北正典律师事务所\\\",\\\"R\\\":\\\"代理律师事务所\\\",\\\"N\\\":\\\"w9544e1d87ea0c975585e8c9239833d8\\\",\\\"O\\\":4}]\",\"doctype\":\"adj\",\"protestorgan\":\"\",\"province\":\"HUB\"}");
        caseList.add("{\"id\":\"73b212f1745fc6554349caaa32f774e10\",\"defendant\":\"\",\"prosecutor\":\"刘克明\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"刘克明\\\"}]\",\"caseno\":\"（2015）鄂民申字第00619号\",\"submitdate\":\"2016-01-04T08:00:00+08:00\",\"judgedate\":\"2015-04-21T09:00:00+08:00\",\"courtdate\":\"2016-01-04T08:00:00+08:00\",\"casereason\":\"提供劳务受害者责任纠纷\",\"isvalid\":\"1\",\"trialround\":\"民事申请再审审查\",\"court\":\"湖北省高级人民法院\",\"companynames\":\"刘忠,刘克明,潘登,张宗祥,向彩兰\",\"caserole\":\"[{\\\"P\\\":\\\"刘克明\\\",\\\"R\\\":\\\"再审申请人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2}]\",\"doctype\":\"adj\",\"protestorgan\":\"\",\"province\":\"HUB\"}");

        String output = new GetDetailInfoMSUDF().evaluate(lianList, ktggList, sdggList, fyggList, caseList);
        System.out.println(output);

    }
}
