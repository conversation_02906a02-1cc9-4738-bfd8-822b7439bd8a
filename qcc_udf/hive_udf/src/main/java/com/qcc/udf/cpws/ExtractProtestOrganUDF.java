package com.qcc.udf.cpws;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 裁判文书清洗UDF：从文书正文中提取抗诉机关/监督机关
 * ---------------------------------------------------------------------------------------------------------
 * create temporary function extractProtestOrgan as 'com.qcc.udf.cpws.ExtractProtestOrganUDF' using jar 'hdfs:///data/hive/udf/qcc_udf.jar';
 * ---------------------------------------------------------------------------------------------------------
 * select extractCaseType (fmtContent, trialRound);
 * 结果: {"name":"河北省人民检察院","title":"抗诉机关"}
 */
public class ExtractProtestOrganUDF extends UDF {
    private final static Set<String> ExtractTrialRoundSet;
    static {
        ExtractTrialRoundSet = new HashSet<>();
        ExtractTrialRoundSet.add("民事依职权再审审查");
        ExtractTrialRoundSet.add("民事申请再审审查");
        ExtractTrialRoundSet.add("民事抗诉再审审查");
        ExtractTrialRoundSet.add("民事再审");
    }

    /**
     * 提取"抗诉机关"或"监督机关"信息
     * @param fmtContent
     * @param trialRound
     * @return
     */
    public String evaluate(String fmtContent, String trialRound) {
        try {
            if (StringUtils.isNotBlank(fmtContent) && StringUtils.isNotBlank(trialRound)
                    && ExtractTrialRoundSet.contains(trialRound)) {

                Document doc = Jsoup.parse(fmtContent);
                if (doc != null) {
                    Element element = doc.selectFirst(".qcc_law_judge_party");
                    if (element != null) {
                        List<String> lineList = Arrays.stream(HtmlToTextUtil.convert(element.toString()).split("\n|\r\n|\r"))
                                .map(StringUtils::trim)
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.toList());
                        for (String line : lineList) {
                            JSONObject resJsonObj = extract(line);
                            if (resJsonObj.containsKey("name")) {
                                return resJsonObj.toJSONString();
                            }
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return "";
    }

    private static JSONObject extract(String input) {
        JSONObject jsonObj = new JSONObject();
        if (StringUtils.isNotBlank(input)) {
            String title = "";
            if (input.startsWith("抗诉机关")) {
                title = "抗诉机关";
            } else if (input.startsWith("监督机关")) {
                title = "监督机关";
            }

            if (StringUtils.isNotBlank(title)) {
                Matcher matcher = Pattern.compile(".*检察院").matcher(input.replace(title, ""));
                if (matcher.find()) {
                    String name = matcher.group().replaceAll("：|\\:|,|，|\\.|。", "");
                    jsonObj.put("title", title);
                    jsonObj.put("name", name);
                }
            }
        }
        return jsonObj;
    }

//    public static void main(String[] args) {
//        try {
//            String fmtContent = "";
//            String trialRound = "民事抗诉再审审查";
//
//            String result = new ExtractProtestOrganUDF().evaluate(fmtContent, trialRound);
//            System.out.println(result);
//        } catch (Exception ex) {
//            ex.printStackTrace();
//        }
//    }
}
