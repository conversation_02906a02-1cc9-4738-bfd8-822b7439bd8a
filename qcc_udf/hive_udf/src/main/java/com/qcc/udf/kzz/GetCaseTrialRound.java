package com.qcc.udf.kzz;

import com.qcc.udf.temp.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.Optional;

/**
 * 处理司法案件进程
 *
 * <AUTHOR>
 * @date 2022/4/14
 */
public class GetCaseTrialRound extends UDF {

    private static final String[] TrialRounds = new String[]{"民事一审", "民事二审", "刑事一审", "刑事二审", "行政一审", "行政二审",
            "首次执行", "恢复执行", "刑罚与执行变更", "财产保全执行", "特别程序", "民事申请再审审查", "财产保全", "执行异议", "非诉行政行为申请执行审查", "诉前调解",
            "民事管辖上诉", "民事再审", "行政申请再审审查", "申请支付令审查", "执行复议", "破产", "刑事审判监督", "民事管辖", "其他执行"};

    public static String evaluate(String content) {
        String result = "其他";
        try {
            if (StringUtils.isNotBlank(content)) {
                Optional<String> optional = Arrays.stream(TrialRounds).filter(item -> content.equals(item)).findAny();
                if (optional.isPresent()) {
                    result = content;
                }
            }

        } catch (Exception e) {

        }
        return result;
    }
//    public static void main(String[] args) {
//        String content = "行政申请再审审查";
//        String region = evaluate(content);
//        System.out.println(region);
//    }
}
