package com.qcc.udf.cpws;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;

/**
 * 业务UDF（裁判文书）从标准格式的裁判文书从公司地址中获取城市信息
 * ---------------------------------------------------------------------------------------------------------
 * create temporary function extractTrialProcess as 'com.qcc.udf.cpws.ExtractTrialProcessUDF' using jar 'hdfs:///data/hive/udf/qcc_udf.jar';
 * ---------------------------------------------------------------------------------------------------------
 * select extractTrialProcess (fmtContent);
 * 结果: ''
 */
public class ExtractTrialProcessUDF extends UDF {

    public String evaluate(String fmtContent) {
        String trialContent = "";
        try {
            Document document = Jsoup.parse(fmtContent);
            if (document != null) {
                Elements elements = document.getElementsByClass("qcc_law_judge_trial");
                if (elements != null && elements.size() > 0) {
                    String trialProcessHtml = elements.get(0).toString();
                    if (StringUtils.isNotBlank(trialProcessHtml)) {
                        trialContent = HtmlToTextUtil.convert(trialProcessHtml).replace("\n", "").trim();
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return trialContent;
    }
}