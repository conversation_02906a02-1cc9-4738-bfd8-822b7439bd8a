package com.qcc.udf.cpws;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import org.apache.commons.collections.MultiMap;
import org.apache.commons.collections.map.MultiValueMap;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.stream.Collectors;


public class CpwsCaseRoleUDFv10 extends UDF {
    public static String evaluate(String caserole, String partyText) {
        try {

            Set<String> mySet = Arrays.stream(caserole.split(","))
                    .map(s -> s.replaceAll("^[a-fA-F0-9]{32}$", ""))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .collect(Collectors.toSet());
            MultiMap nameIndex = new MultiValueMap();
            for (String name : mySet) {
                if (nameIndex.containsKey(name)) {
                    continue;
                }
                // 计算当前名称所在段落坐标
                int startIdx = partyText.indexOf(name);
                if (startIdx == -1) {
                    nameIndex.put(name, -1 + "," + -1);
                    continue;
                }
                while (startIdx >= 0) {
                    int lastIdx = startIdx + name.length() - 1;
                    nameIndex.put(name, startIdx + "," + lastIdx);
                    startIdx = partyText.indexOf(name, startIdx + name.length());
                }
            }
            return JSON.toJSONString(checkValueIntersection(nameIndex));
        } catch (Exception e) {
            e.printStackTrace();
            return caserole;
        }

    }

    public static Set<String> checkValueIntersection(Map<String,Object> dataMap) {

        Map<String, List<IndexRange>> parsedData = new HashMap<>();
        for (Map.Entry<String,Object> entry : dataMap.entrySet()) {
            String key = entry.getKey();
            Collection coll = (Collection) entry.getValue();

            parsedData.put(key, parseIndexes(coll));
        }

        List<String> keysToRemove = new ArrayList<>();
        for (Map.Entry<String, List<IndexRange>> entry1 : parsedData.entrySet()) {
            String key1 = entry1.getKey();
            List<IndexRange> indexes1 = entry1.getValue();
            for (Map.Entry<String, List<IndexRange>> entry2 : parsedData.entrySet()) {
                String key2 = entry2.getKey();
                List<IndexRange> indexes2 = entry2.getValue();
                if (key1.equals(key2)) {
                    continue;
                }
                StringBuilder isc= new StringBuilder();
                for (IndexRange indexRange : indexes1) {
                    if(indexRange.getStart()==-1 && indexRange.getEnd()==-1){
                        continue;
                    }

                    for (IndexRange range : indexes2) {
                        if (indexRange.getStart() >= range.getStart() && indexRange.getEnd() <= range.getEnd()) {
                            isc.append(1);
                            break;
                        }
                    }

                    if (keysToRemove.contains(key1)) {
                        break;
                    }
                }
                if(isc.length()==indexes1.size()&&!isc.toString().equals("")){
                    keysToRemove.add(key1);
                }
            }
        }

        for (String key : keysToRemove) {

            parsedData.remove(key);
        }
        return parsedData.keySet();
    }


    public static void main(String[] args) {
        String a = "宋继光与汪清北方水泥有限公司和龙分公司劳动争议纠纷执行通知书";
        System.out.println(evaluate("宋继光,,汪清北方水泥有限公司和龙分公司,360fcdec447bed1b374fa30d8c036e12,和龙分公司,汪清北方水泥有限公司,4c60a095ac5f0d13e9950617fd0b9ee4,和龙市人民法院,gfa50cfaddb6afdcffbfff6d35778edb", a));

    }


    private static List<IndexRange> parseIndexes(Collection indexes) {
        List<IndexRange> result = new ArrayList<>();
        for (Object index : indexes) {
            String indexstr = (String) index;
            String[] parts = indexstr.split(",");
            int start = Integer.parseInt(parts[0]);
            int end = Integer.parseInt(parts[1]);
            result.add(new IndexRange(start, end));
        }
        return result;
    }

    private static class IndexRange {
        private int start;
        private int end;

        public IndexRange(int start, int end) {
            this.start = start;
            this.end = end;
        }

        public int getStart() {
            return start;
        }

        public int getEnd() {
            return end;
        }

        @Override
        public String toString() {
            return start + "," + end;
        }
    }


}
