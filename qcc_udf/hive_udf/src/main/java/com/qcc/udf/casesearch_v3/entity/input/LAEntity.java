package com.qcc.udf.casesearch_v3.entity.input;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.casesearch_v3.enums.CaseCategoryEnum;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import com.google.common.base.Strings;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: zhangqiang
 * @Date: 2020/11/11 17:54
 * @Description:立案
 */
@Data
public class LAEntity  extends BaseCaseEntity {

    private String id;
    private String anno;
    private String companynames;
    private String isvalid;
    private String courtname;
    private String provincecode;

    private long punishdate;

    private String prosecutorlist;
    private String defendantlist;
    private String nameandkeyno;

    private String caseReason;

    private List<NameAndKeyNoEntity> prosecutorEntityList;
    private List<NameAndKeyNoEntity> defendantEntityList;
    private List<NameAndKeyNoEntity> nameandkeynoEntityList;


    public static List<LAEntity> convert(List<String> jsonList) {
        List<LAEntity> list = new ArrayList<>();
        LAEntity entity = null;
        if(CollectionUtils.isEmpty(jsonList)){
            return list;
        }
        if(CollectionUtils.isEmpty(jsonList)){
            return list;
        }
        for (String json : jsonList) {
            if(Strings.isNullOrEmpty(json)){
                continue;
            }
            entity = JSON.parseObject(json, LAEntity.class);
            if(entity == null  || Strings.isNullOrEmpty(entity.getId())){
                continue;
            }

            //案件类型为空的数据也要过滤

            String str = entity.getNameandkeyno();
            Map<String,String> nameRoleMap = new HashMap<>();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setNameandkeynoEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getNameandkeynoEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                    namekey.setLawFirmList(null);
                    nameRoleMap.put(namekey.getName(),namekey.getRole());
                }
            }

             str = entity.getProsecutorlist();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setProsecutorEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getProsecutorEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                    namekey.setRole(nameRoleMap.getOrDefault(namekey.getName(),""));
                    namekey.setLawFirmList(null);
                }
            }

             str = entity.getDefendantlist();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setDefendantEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getDefendantEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                    namekey.setRole(nameRoleMap.getOrDefault(namekey.getName(),""));
                    namekey.setLawFirmList(null);
                }
            }
            //公共字段赋值
            entity.setBaseCaseNo(entity.getAnno());
            entity.setBaseCaseCategoryEnum(CaseCategoryEnum.LA);
            entity.setBaseCourt(entity.getCourtname());
            if(!Strings.isNullOrEmpty(entity.getCompanynames())){
                entity.setBaseSearchWordSet(Arrays.stream(entity.getCompanynames().split(","))
                        .collect(Collectors.toSet()));
            }

            entity.setBaseProvinceCode(entity.getProvincecode());
            entity.setBaseNameKeyNoList(entity.getNameandkeynoEntityList());
            entity.setBaseId(entity.getBaseCaseCategoryEnum().getType()+"_"+entity.getId());
            entity.setBaseCaseReason(entity.getCaseReason());

            String caseType= CommonV3Util.getCaseType(CommonV3Util.getCaseNo(entity.getBaseCaseNo()));
            //案件类型为空的数据直接过滤
            if(Strings.isNullOrEmpty(caseType)){
                continue;
            }
            list.add(entity);
        }
        return list;
    }

}
