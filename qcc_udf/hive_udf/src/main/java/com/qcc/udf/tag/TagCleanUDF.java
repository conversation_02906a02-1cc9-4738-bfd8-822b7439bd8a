package com.qcc.udf.tag;

import com.alibaba.fastjson.JSON;
import com.qcc.udf.tag.dimtype.DimensionEnum;
import com.qcc.udf.tag.dimtype.LDDimtypeKeywordEnum;
import com.qcc.udf.tag.dimtype.SWDimtypeKeywordEnum;
import com.qcc.udf.tag.dimtype.ZLDimtypeKeywordEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther: zhanqgiang
 * @Date: 2020/11/19 10:00
 * @Description: 案号简单清洗
 */
public class TagCleanUDF extends UDF {
    public static final String SPLIT_REGEX = ",|;";
    public static final String NULL_ARRAY_STRING = "[]";

    public static String evaluate(String tagName, int type, String keyword) {
        DimensionEnum dimensionEnum = DimensionEnum.getDimensionEnum(type);
        if (dimensionEnum == null) {
            return NULL_ARRAY_STRING;
        }

        List<TagEntity> tags = new ArrayList<>();
        String[] tagArr = tagName.split(SPLIT_REGEX);
        for (String tag : tagArr){
            TagEnum tagEnum = TagEnum.getTagEnum(tag);
            if (tagEnum == null){
                continue;
            }
            switch (tagEnum){
                case LD:
                    //劳动争议
                    addTags(tags, LDDimtypeKeywordEnum.find(dimensionEnum, keyword));
                    break;
                case SW:
                    //税务风险
                    addTags(tags, SWDimtypeKeywordEnum.find(dimensionEnum, keyword));
                    break;
                case ZL:
                    //产品质量问题
                    addTags(tags, ZLDimtypeKeywordEnum.find(dimensionEnum, keyword));
                    break;
            }
        }
        tags = tags.stream().distinct().collect(Collectors.toList());
        return JSON.toJSONString(tags);
    }

    private static void addTags(List<TagEntity> tags, List<TagEntity> tagEntities) {
        List<TagEntity> ldTags = tagEntities;
        if (ldTags.size() > 0) {
            tags.addAll(ldTags);
        }
    }


    public static void main(String[] args) {
//        System.out.println(evaluate("sw",291,"A030615"));
        System.out.println(evaluate("LD,sw",13,"其他解除、终止劳动合同类争议"));
//        System.out.println(evaluate("sw",157, "2018-10-01至2018-10-31个人所得税（工资薪金所得）未按期进行申报2018-10-01至2018-10-31城市维护建设税（县城、镇（增值税附征））未按期进行申报2018-10-01至2018-10-31个人所得税（个体户生产经营所得）未按期进行申报处罚金额2000元"));
    }

}
