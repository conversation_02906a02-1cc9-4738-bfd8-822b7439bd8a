package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.text.DecimalFormat;

/**
 * @Auther: liulh
 * @Date: 2020/6/1 17:54
 * @Description:
 */
public class formatNumber extends UDF {
    public static String evaluate(Object o, int i) {
        String format = "";
        if (i <= 0) {
            return "";
        }
        format = "0.0";
        for (int j = 1; j < i; j++) {
            format = format.concat("0");
        }
        DecimalFormat df = new DecimalFormat(format);
        String formatStgring = df.format(o);
        return formatStgring;

    }

}
