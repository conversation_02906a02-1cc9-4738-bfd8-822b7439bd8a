package com.qcc.udf.casesearch_v3.entity.tmp;

import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.risk_analysis.entity.NameKeyDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021年07月15日 15:08
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RiskLawsKeyNoArray {
    private List<NameAndKeyNoEntity> plaintiff;
    private List<NameAndKeyNoEntity> defendant;
    private List<NameAndKeyNoEntity> thirdParty;
    private List<NameAndKeyNoEntity> keyNoArray;
    private static final long serialVersionUID = 1L;
}
