package com.qcc.udf;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;

/**
 * @Auther: wangb
 * @Date: 2020/10/9 16:06
 * @Description: spark2.4.0 decimal类型的数据转换成hive1.1.0 decimal类型(去除末尾的0)
 * 1.000 转换成 1 ； 1.00100 转换成 1.001
 */
public class DecimalTypeConversion extends UDF {

    public  String evaluate(String s) {
//        int l = s.length();
//        for (int i = 1; i < l; i++) {
//            if (s.charAt(l - i) == '0' || s.charAt(l - i) == '.') {
//                s = s.substring(0, l - i);
//            } else {
//                break;
//            }
//        }
//        return  s;
        if (StringUtils.isEmpty(s)){return "";}

        if(s.indexOf('.')>=0){
            if(s.contains("E")){
                //科学计数的数据转换
                String str=new BigDecimal(s.toString()).toPlainString();
                s= StringUtils.stripEnd(StringUtils.stripEnd(str,"0"),".");
                return s;
            }else {
                //去除末尾多余的0，再去除末尾多余的 “.”
                s= StringUtils.stripEnd(StringUtils.stripEnd(s,"0"),".");
                return  s;
            }
        }
        else{
            return s;
        }
    }
    public static void main(String[] args) {
        DecimalTypeConversion c = new DecimalTypeConversion();
        String s = "0.000100";
        System.out.println(c.evaluate(s));
    }
}
