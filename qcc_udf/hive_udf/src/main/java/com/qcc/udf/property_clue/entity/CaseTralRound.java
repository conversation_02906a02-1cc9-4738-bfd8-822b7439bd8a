package com.qcc.udf.property_clue.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc
 * @date 2021/1/18
 */
@Data
public class CaseTralRound {
    /**
     * 案件id
     */
    @JSONField(name = "Id")
    private String id = "";

    @JSONField(name = "KeyNo")
    private String keyno = "";

    @JSONField(name = "CompanyName")
    private String CompanyName = "";

    @JSONField(name = "Role")
    private String role = "";

    @JSONField(name = "TrialRound")
    private String trialRound = "";

    @JSONField(name = "LatestTimestamp")
    private Long latestTimestamp = 0L;
}
