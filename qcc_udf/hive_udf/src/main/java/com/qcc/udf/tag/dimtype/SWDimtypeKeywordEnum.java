package com.qcc.udf.tag.dimtype;

import com.google.common.collect.Lists;
import com.qcc.udf.tag.TagEntity;
import com.qcc.udf.tag.keywordEnum.SWKeywordMatchEnum;
import com.qcc.udf.tag.tagEnum.TaxRiskEnum;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 税务风险-相关维度关键词
 */
public enum SWDimtypeKeywordEnum {
    //严重违法
    //非正常户
    FEI_ZHENG_CHANG_HU(DimensionEnum.FEI_ZHENG_CHANG_HU, null),
    //税收违法
    SHUI_SHOU_WEI_FA(DimensionEnum.SHUI_SHOU_WEI_FA, new SWKeywordMatchEnum[]{SWKeywordMatchEnum.XK_FP, SWKeywordMatchEnum.TS_TS_KS, SWKeywordMatchEnum.DEFAULT}),
    //司法案件
    SI_FA_AN_JIAN(DimensionEnum.SI_FA_AN_JIAN, new SWKeywordMatchEnum[]{SWKeywordMatchEnum.SSXSAJ}),
    //欠税公告
    QIAN_SHUI_GONG_GAO(DimensionEnum.QIAN_SHUI_GONG_GAO, null),
    //行政处罚
    XING_ZHENG_CHU_FA(DimensionEnum.XING_ZHENG_CHU_FA, new SWKeywordMatchEnum[]{SWKeywordMatchEnum.WAGD, SWKeywordMatchEnum.WFGD, SWKeywordMatchEnum.XK_FP, SWKeywordMatchEnum.YQ_WAQ, SWKeywordMatchEnum.TS_TS_KS, SWKeywordMatchEnum.SJ, SWKeywordMatchEnum.DEFAULT}),
    //抽查检查
    CHOU_CHA_JIAN_CHA(DimensionEnum.CHOU_CHA_JIAN_CHA, new SWKeywordMatchEnum[]{SWKeywordMatchEnum.YQ_WAQ, SWKeywordMatchEnum.WAGD, SWKeywordMatchEnum.SJ, SWKeywordMatchEnum.QS, SWKeywordMatchEnum.TS_TS_KS, SWKeywordMatchEnum.DEFAULT}),
    //双随机抽查
    DOUBLE_RANDOM_CHECK(DimensionEnum.DOUBLE_RANDOM_CHECK, new SWKeywordMatchEnum[]{SWKeywordMatchEnum.SJ, SWKeywordMatchEnum.XK_FP, SWKeywordMatchEnum.YQ_WAQ, SWKeywordMatchEnum.WAGD, SWKeywordMatchEnum.QS, SWKeywordMatchEnum.TS_TS_KS, SWKeywordMatchEnum.DEFAULT}),
    //行政许可
    XING_ZHENG_XU_KE(DimensionEnum.XING_ZHENG_XU_KE, new SWKeywordMatchEnum[]{SWKeywordMatchEnum.SW09_YQ}),
    //黑名单
    HEI_MING_DAN(DimensionEnum.HEI_MING_DAN, new SWKeywordMatchEnum[]{SWKeywordMatchEnum.XK_FP, SWKeywordMatchEnum.QS, SWKeywordMatchEnum.SJ, SWKeywordMatchEnum.TS_TS_KS, SWKeywordMatchEnum.FF_QD_JXFP, SWKeywordMatchEnum.DEFAULT}),
    ;


    private DimensionEnum dimensionEnum;
    private SWKeywordMatchEnum[] sWKeywordMatchEnum;

    SWDimtypeKeywordEnum(DimensionEnum dimensionEnum, SWKeywordMatchEnum[] sWKeywordMatchEnum) {
        this.dimensionEnum = dimensionEnum;
        this.sWKeywordMatchEnum = sWKeywordMatchEnum;
    }

    private static final Map<DimensionEnum, SWKeywordMatchEnum[]> lookup = new LinkedHashMap<>();

    static {
        EnumSet.allOf(SWDimtypeKeywordEnum.class).stream().forEach(e -> {
                    lookup.put(e.dimensionEnum, e.sWKeywordMatchEnum);
                }
        );
    }

    public static List<TagEntity> find(DimensionEnum dimensionEnum, String keyword) {
        List<TagEntity> tagEntities = new LinkedList<>();
        if (DimensionEnum.FEI_ZHENG_CHANG_HU == dimensionEnum){
            tagEntities.add(TaxRiskEnum.SW03.getTagEntity());
            return tagEntities;
        }
        if (DimensionEnum.QIAN_SHUI_GONG_GAO == dimensionEnum){
            tagEntities.add(SWKeywordMatchEnum.QS.getTagEnum().getTagEntity());
            return tagEntities;
        }
        SWKeywordMatchEnum[] sWKeywordMatchEnums = getSWKeywordMatchEnums(dimensionEnum);
        if (sWKeywordMatchEnums == null) {
            return tagEntities;
        }
        for (SWKeywordMatchEnum kmEnum : sWKeywordMatchEnums) {
            if (SWKeywordMatchEnum.DEFAULT == kmEnum){
                continue;
            }
            if (checkLikeRole(keyword, kmEnum.getKeyword())) {
                tagEntities.add(kmEnum.getTagEnum().getTagEntity());
            }
        }
        if (tagEntities.size() > 0) {
            return tagEntities;
        }
        boolean flag = Arrays.stream(sWKeywordMatchEnums).anyMatch(e -> e == SWKeywordMatchEnum.DEFAULT);
        if (flag) {
            return Lists.newArrayList(SWKeywordMatchEnum.DEFAULT.getTagEnum().getTagEntity());
        }
        return tagEntities;
    }


    public static SWKeywordMatchEnum[] getSWKeywordMatchEnums(DimensionEnum dimensionEnum) {
        return lookup.get(dimensionEnum);
    }

    private static boolean checkLikeRole(String role, String regex) {
        Pattern p = Pattern.compile(regex);
        Matcher matcher = p.matcher(role);
        return matcher.find();
    }

    public static void main(String[] args) {
        DimensionEnum dimensionEnum = DimensionEnum.CHOU_CHA_JIAN_CHA;
        String keyword = "未发放员工薪酬";
        System.out.println(find(dimensionEnum, keyword));
    }
}
