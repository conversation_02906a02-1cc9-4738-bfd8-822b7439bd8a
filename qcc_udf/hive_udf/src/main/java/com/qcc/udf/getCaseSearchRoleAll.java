package com.qcc.udf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.court_notice.anUtils.Util;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Auther: liulh
 * @Date: 2020/6/2 20:33
 * @Description:
 */
public class getCaseSearchRoleAll extends UDF {
    public static String  evaluate(String roleArray, String keynos) {
        JSONArray result = new JSONArray();

        if (StringUtils.isEmpty(roleArray) || StringUtils.isEmpty(keynos)){
            return "";
        }
        Set<String> keyNoSet = new LinkedHashSet<>();
        String[] keyNoArr = keynos.split(",");
        for (String str : keyNoArr){
            if (Util.isKeyword(str)){
                keyNoSet.add(str);
            }
        }

        Set<String> roleSetP = new LinkedHashSet<>();
        Set<String> roleSetD = new LinkedHashSet<>();
        try{
            JSONArray caseRoleJsonArray = JSONArray.parseArray(roleArray.replace("\\", ""));
            Iterator<Object> it = caseRoleJsonArray.iterator();
            while(it.hasNext()){
                JSONObject jsonObject = (JSONObject)it.next();
                if (StringUtils.isNotEmpty(jsonObject.getString("N")) && keyNoSet.contains(jsonObject.getString("N"))){
                    String role = jsonObject.getString("N").concat("_").concat(getCaseRoleCode(Util.full2Half(jsonObject.getString("R"))));
                    if (role.contains("P")){
                        roleSetP.add(role.split("_")[0]);
                    }
                    if (role.contains("D")){
                        roleSetD.add(role.split("_")[0]);
                    }
                }
            }

            if (roleSetP.size() > 0 && roleSetD.size() > 0){
                for (String str : roleSetP){
                    for (String sub : roleSetD){
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("P", str);
                        jsonObject.put("D", sub);

                        result.add(jsonObject);
                    }
                }
            }
        } catch (Exception e) {

        }

        return result.isEmpty() || result.size() == 0 ? "" : result.toString();
    }

    public static String getCaseRoleCode(String caseRole){
        String result = "";
        if (StringUtils.isEmpty(caseRole)){
            return "";
        }

        Pattern p1 = Pattern.compile("(被执行人)|(被告)|(被申请人)|(被申请执行人)|(原审被告)|(被上诉人\\(原审被告\\))|(上诉人\\(原审被告\\))|(被告\\(反诉原告\\))|(被告人)|(上诉人\\(一审被告\\))|" +
                "(被上诉人\\(一审被告\\))|(被上诉人)|(上诉人\\(原审被告反诉原告\\))|(被告二)|(被告一)|(原告\\(被告\\))|(被申请人\\(一审被告二审被上诉人\\))|(被申请人\\(原审被告\\))|(再审申请人\\(一审被告二审上诉人\\))|" +
                "(再审申请人\\(原审被告\\))|(被申请人\\(仲裁被申请人\\))|(被申请人\\(原被执行人\\))|(再审被申请人)|(上诉人\\(原审被告原审原告\\))");
        Matcher m1 = p1.matcher(caseRole);
        if (m1.matches()) {
            result = "D";
        }

        Pattern p2 = Pattern.compile("(申请执行人)|(原告)|(申请人)|(被上诉人\\(原审原告\\))|(复议申请人)|(上诉人\\(原审原告\\))|(原告\\(反诉被告\\))|(上诉人)|(被上诉人\\(一审原告\\))|(上诉人\\(一审原告\\))|(被上诉人\\(原审原告反诉被告\\))|" +
                "(原审原告)|(再审申请人)|(被告\\(原告\\))|(被申请人\\(原审原告\\))|(附带民事诉讼原告人)|(复议申请人\\(原申请执行人\\))|(再审申请人\\(一审原告二审上诉人\\))|(再审申请人\\(原审原告\\))|(申请再审人\\(一审原告二审上诉人\\))|" +
                "(二审上诉人)|(原告人)|(附带民事诉讼原告)|(上诉人\\(原审原告原审被告\\))|(起诉人)|(申请人\\(仲裁申请人\\))|(赔偿请求人)");
        Matcher m2 = p2.matcher(caseRole);
        if (m2.matches()) {
            result = "P";
        }

        return result;
    }

    public static void main(String[] args) {
        System.out.println(evaluate(
                "[{\\\"D\\\":\\\"一审原告\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-1,\\\"P\\\":\\\"\\\\\\\"Name\\\\\\\":\\\\\\\"恒安（中国）纸业有限公司\\\\\\\"}]\\\",\\\"R\\\":\\\"原告\\\",\\\"RL\\\":[{\\\"R\\\":\\\"原告\\\",\\\"T\\\":\\\"一审\\\"}]},{\\\"D\\\":\\\"一审原告\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-1,\\\"P\\\":\\\"\\\\\\\"Org\\\\\\\":0\\\",\\\"R\\\":\\\"原告\\\",\\\"RL\\\":[{\\\"R\\\":\\\"原告\\\",\\\"T\\\":\\\"一审\\\"}]},{\\\"D\\\":\\\"一审原告\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-1,\\\"P\\\":\\\"[{\\\\\\\"KeyNo\\\\\\\":\\\\\\\"ae908d5294828dfffa52a5abb5f08ccb\\\\\\\"\\\",\\\"R\\\":\\\"原告\\\",\\\"RL\\\":[{\\\"R\\\":\\\"原告\\\",\\\"T\\\":\\\"一审\\\"}]},{\\\"D\\\":\\\"一审原告\\\",\\\"N\\\":\\\"ae908d5294828dfffa52a5abb5f08ccb\\\",\\\"O\\\":0,\\\"P\\\":\\\"恒安（中国）纸业有限公司\\\",\\\"R\\\":\\\"原告\\\",\\\"RL\\\":[{\\\"R\\\":\\\"原告\\\",\\\"T\\\":\\\"一审\\\"}]},{\\\"D\\\":\\\"一审被告\\\",\\\"N\\\":\\\"169338bd535ed2f22199b9380a48cfd5\\\",\\\"O\\\":0,\\\"P\\\":\\\"永嘉县三江缪龙宽副食品店\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"}]}]" ,
                "169338bd535ed2f22199b9380a48cfd5,ae908d5294828dfffa52a5abb5f08ccb,恒安（中国）纸业有限公司,永嘉县三江缪龙宽副食品店"));
    }
}
