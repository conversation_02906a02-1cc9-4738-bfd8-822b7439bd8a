package com.qcc.udf.cpws.wjp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.qcc.udf.temp.MD5Util;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2022/12/8 22:10
 **/
public class CaseGYSKHUtil extends UDF {


    public static String evaluate(String content, String defendant, String prosecutor, String OtherPartyRole) {
        JSONObject result = new JSONObject();
        if (StringUtils.isNotEmpty(content)) {

            //正文关键字
            String regex = "(民事案由|合同、无因管理、不当得利纠纷|缔约过失责任纠纷|确认合同效力纠纷|确认合同有效纠纷|确认合同无效纠纷|债权人代位权纠纷|债权人撤销权纠纷|互易纠纷|承租人优先购买权纠纷|建设工程价款优先受偿权纠纷|追偿权纠纷|合同纠纷)";
            // 编译正则表达式
            Pattern pattern = Pattern.compile(regex);
            // 创建 Matcher 对象
            Matcher matcher = pattern.matcher(content);
            // 查找匹配项
            if (!matcher.find()) {
                result.put("code", "error");
                result.put("message", "正文案由不匹配");
                return result.toJSONString();  // 如果没有匹配项，返回 null
            }
            // keyno关联
            if (countKeynos(defendant) > 0 && countKeynos(prosecutor) > 0) {

            } else if (countKeynos(OtherPartyRole) > 1) {

            } else {
                result.put("code", "error");
                result.put("message", "keyno数量不匹配");
                return result.toJSONString();  // 如 // 如果没有匹配项，返回 null
            }

            CaseMainBodySyncEntity caseMainBody = JSON.parseObject(content, CaseMainBodySyncEntity.class);
            String message = caseMainBody.getLawJudgeParty() + caseMainBody.getLawJudgeTrial();

            if (StringUtils.isNotEmpty(caseMainBody.getLawCurrentReview()) && matchContract(caseMainBody.getLawCurrentReview())) {
                result.put("code", "law_current_review");
                message = message + caseMainBody.getLawCurrentReview();
            } else if (StringUtils.isNotEmpty(caseMainBody.getLawCurrentIdentification()) && matchContract(caseMainBody.getLawCurrentIdentification())) {
                message = message + caseMainBody.getLawCurrentIdentification();
                result.put("code", "law_current_identification");
            } else if (StringUtils.isNotEmpty(caseMainBody.getLawPreCourtReview()) && matchContract(caseMainBody.getLawPreCourtReview())) {
                message = message + caseMainBody.getLawPreCourtReview();
                result.put("code", "law_pre_court_review");
            } else if (StringUtils.isNotEmpty(caseMainBody.getLawPreCourtIdentification()) && matchContract(caseMainBody.getLawPreCourtIdentification())) {
                message = message + caseMainBody.getLawPreCourtIdentification();
                result.put("code", "law_pre_court_identification");
            } else if (StringUtils.isNotEmpty(caseMainBody.getLawJudgeResult()) && matchContract(caseMainBody.getLawJudgeResult())) {
                message = message + caseMainBody.getLawJudgeResult();
                result.put("code", "law_judge_result");
            } else if (StringUtils.isNotEmpty(caseMainBody.getLawAppellantRequest()) && matchContract(caseMainBody.getLawAppellantRequest())) {
                message = message + caseMainBody.getLawAppellantRequest();
                result.put("code", "law_appellant_request");
            } else if (StringUtils.isNotEmpty(caseMainBody.getLawAppelleeArguing()) && matchContract(caseMainBody.getLawAppelleeArguing())) {
                message = message + caseMainBody.getLawAppelleeArguing();
                result.put("code", "law_appellee_arguing");
            } else if (StringUtils.isNotEmpty(caseMainBody.getLawPlaintiffRequest()) && matchContract(caseMainBody.getLawPlaintiffRequest())) {
                message = message + caseMainBody.getLawPlaintiffRequest();
                result.put("code", "law_plaintiff_request");
            } else if (StringUtils.isNotEmpty(caseMainBody.getLawDefendantArguing()) && matchContract(caseMainBody.getLawDefendantArguing())) {
                message = message + caseMainBody.getLawDefendantArguing();
                result.put("code", "law_defendant_arguing");
            } else if (StringUtils.isNotEmpty(caseMainBody.getLawPrePlaintiffRequest()) && matchContract(caseMainBody.getLawPrePlaintiffRequest())) {
                message = message + caseMainBody.getLawPrePlaintiffRequest();
                result.put("code", "law_pre_plaintiff_request");
            } else if (StringUtils.isNotEmpty(caseMainBody.getLawPreDefendantArguing()) && matchContract(caseMainBody.getLawPreDefendantArguing())) {
                message = message + caseMainBody.getLawPreDefendantArguing();
                result.put("code", "law_pre_defendant_arguing");
            } else {
                result.put("code", "error");
                result.put("message", "段落合同正则不匹配");
                return result.toJSONString();
            }
            result.put("message", message);
            return result.toJSONString();

        }
        result.put("code", "error");
        result.put("message", "段落合同正则不匹配");
        return result.toJSONString();

    }


    public static boolean matchContract(String inputText) {
        // 优化后的正则表达式
        String regex = "(签订[^，。]{0,20}合同.{0,50}元|合同[^，。]{0,20}签订.{0,50}元|支付[^，。]{0,20}货款.{0,50}元|签订[^，。]{0,20}合同|合同[^，。]{0,20}签订|支付[^，。]{0,20}货款|欠[^，。]{0,20}货款|工程[^，。]{0,20}欠款|交付[^，。]{0,20}货物)";
        // 编译正则表达式
        Pattern pattern = Pattern.compile(regex);
        // 创建 Matcher 对象
        Matcher matcher = pattern.matcher(inputText);
        // 查找匹配项
        if (matcher.find()) {
            System.out.println(matcher.group());
            return true;
        } else {
            return false;  // 如果没有匹配项，返回 null
        }
    }

    public static int countKeynos(String inputText) {
        if (StringUtils.isEmpty(inputText)) {
            return 0;
        }
        // 正则表达式：匹配32个字符的字母数字字符串（keyno）
        String regex = "(?!p)[A-Za-z0-9]{32}";

        // 编译正则表达式
        Pattern pattern = Pattern.compile(regex);

        // 创建 Matcher 对象
        Matcher matcher = pattern.matcher(inputText);

        // 计数匹配的 keyno 数量
        int count = 0;
        while (matcher.find()) {
            count++;  // 找到一个 keyno，计数加一
        }

        return count;  // 返回匹配的 keyno 数量
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class CaseMainBodySyncEntity {

        /**
         * 涉案法官信息
         */
        @JSONField(name = "officer_info")
        private String officerInfo;

        /**
         * 涉案律师信息
         */
        @JSONField(name = "lawyer_info")
        private String lawyerInfo;

        /**
         * 案件金额区间
         */
        @JSONField(name = "amountinvolved_level")
        private String amountinvolvedLevel;

        /**
         * 正文-当事人段落
         */
        @JSONField(name = "law_judge_party")
        private String lawJudgeParty;

        /**
         * 正文-审理经过段落
         */
        @JSONField(name = "law_judge_trial")
        private String lawJudgeTrial;

        /**
         * 正文-原告诉求段落
         */
        @JSONField(name = "law_plaintiff_request")
        private String lawPlaintiffRequest;

        /**
         * 正文-被告答辩段落
         */
        @JSONField(name = "law_defendant_arguing")
        private String lawDefendantArguing;

        /**
         * 正文-一审原告诉求段落
         */
        @JSONField(name = "law_pre_plaintiff_request")
        private String lawPrePlaintiffRequest;

        /**
         * 正文-一审被告答辩段落
         */
        @JSONField(name = "law_pre_defendant_arguing")
        private String lawPreDefendantArguing;

        /**
         * 正文-一审法院查明段落
         */
        @JSONField(name = "law_pre_court_review")
        private String lawPreCourtReview;

        /**
         * 正文-一审法院认为段落
         */
        @JSONField(name = "law_pre_court_identification")
        private String lawPreCourtIdentification;

        /**
         * 正文-上诉人诉求段落
         */
        @JSONField(name = "law_appellant_request")
        private String lawAppellantRequest;

        /**
         * 正文-被上诉人答辩段落
         */
        @JSONField(name = "law_appellee_arguing")
        private String lawAppelleeArguing;

        /**
         * 正文-本院查明段落
         */
        @JSONField(name = "law_current_review")
        private String lawCurrentReview;

        /**
         * 正文-本院认为段落
         */
        @JSONField(name = "law_current_identification")
        private String lawCurrentIdentification;

        /**
         * 正文-执行经过段落
         */
        @JSONField(name = "law_execute_process")
        private String lawExecuteProcess;

        /**
         * 正文-判决结果段落
         */
        @JSONField(name = "law_judge_result")
        private String lawJudgeResult;

        /**
         * 正文-合议庭段落
         */
        @JSONField(name = "law_judge_collegiate_bench")
        private String lawJudgeCollegiateBench;

        /**
         * 非格式化文书的全文内容
         */
        @JSONField(name = "full_content")
        private String fullContent;

        /**
         * 正文-附录段落
         */
        @JSONField(name = "law_judge_appendix")
        private String lawJudgeAppendix;

        /**
         * 正文-附录段落
         */
        @JSONField(name = "qcc_law_judge_date")
        private String judgeDate;

    }

    public static void main(String[] args) throws InterruptedException {
//        System.out.println(evaluate(
//                "{\"officer_info\": \"[{\\\"Role\\\":\\\"审判员\\\",\\\"Name\\\":\\\"张忻\\\"}]\", \"lawyer_info\": \"[{\\\"Role\\\":0,\\\"LawFirm\\\":\\\"江苏君合力律师事务所\\\",\\\"Name\\\":\\\"郑红花\\\"},{\\\"Role\\\":0,\\\"LawFirm\\\":\\\"江苏君合力律师事务所\\\",\\\"Name\\\":\\\"荆网娟\\\"}]\", \"amountinvolved_level\": \"4\", \"law_judge_party\": \"原告：江苏张家港农村商业银行股份有限公司丹阳支行。住所地丹阳市开发区丹桂路35号振业大厦，统一社会信用代码913211815855237037。 负责人：周晓华，该支行行长。 委托诉讼代理人：郑红花，江苏君合力律师事务所律师。 委托诉讼代理人：荆网娟，江苏君合力律师事务所律师。 被告：江苏创宏建筑有限公司，住所地丹阳市延陵镇麦溪联兴观庄，组织机构代码66490479-0。 法定代表人：赵金良，总经理。 被告：镇江市投资担保有限公司，住所地镇江市长江路77号。组织机构代码75643747-2。 法定代表人：邓北，总经理。 被告：江苏双丹投资有限公司，住所地丹阳市南郊312国道东侧。组织机构代码69548207-6。 法定代表人：张银钟，总经理。 被告：江苏丹东装饰有限公司，住所地丹阳市南郊312国道东侧。组织机构代码69548215-6。 法定代表人：王东兴，总经理。 被告：江苏银鑫房地产开发有限公司，住所地丹阳市南郊312国道东侧。组织机构代码69548209-2。 法定代表人：张银钟，总经理。 被告：江苏东尼园林工程有限公司，住所地丹阳市南郊312国道东侧。组织机构代码69548220-1。 法定代表人：张银钟，总经理。 被告：江苏东尼大酒店有限公司，住所地丹阳市南郊312国道东侧。组织机构代码69548211-3。 法定代表人：张银钟，总经理。 被告：东尼集团有限公司，住所地丹阳市南郊沪宁二级公路东侧。组织机构代码25371575-X。 法定代表人：张银钟，总经理。 被告：张银钟，男，汉族，****年**月**日生，住**。 被告：赵龙英，女，汉族，****年**月**日生，住**。 被告：赵金良，男，汉族，****年**月**日生，住**。 被告：眭月芳，女，汉族，****年**月**日生，住**。\", \"law_judge_trial\": \"原告江苏张家港农村商业银行股份有限公司丹阳支行（以下简称张家港银行丹阳支行）与江苏创宏建筑有限公司（以下简称创宏公司）、被告东尼集团有限公司（以下简称东尼集团公司）、镇江市投资担保有限公司（以下简称镇江投资公司）、江苏双丹投资有限公司（以下简称双丹投资公司）、江苏丹东装饰有限公司（以下简称丹东公司）、江苏银鑫房地产开发有限公司（以下简称银鑫公司）、江苏东尼园林工程有限公司（以下简称东尼园林公司）、江苏东尼大酒店有限公司（以下简称东尼东尼大酒店）、、张银钟、赵龙英、赵金良、眭月芳金融借款合同纠纷一案，本院于2018年5月22日立案受理后，依法适用简易程序公开开庭进行了审理。原告张家港银行丹阳支行委托诉讼代理人郑红花到庭参加诉讼，各被告经本院合法传唤，无正当理由未到庭参加诉讼。本案现已审理终结。\", \"law_plaintiff_request\": \"原告张家港银行丹阳支行向本院提出如下诉讼请求：1.判令被告创宏公司立即清偿原告借款本金550万元，期内利息163288.13元、截止2018月4月25日的逾期利息390077.18元，并自2018年4月26日起按年利率8.4825%承担利息、复利至债务清偿止；2.其余被告对上述借款本息承担连带清偿责任；3.案件受理费由被告承担。事实和理由：2016年6月30日，原告与被告创宏公司签订《流动资金借款合同》，约定由原告向被告创宏公司提供借款550万元，借款期限为2016年6月30日至2017年6月28日，年利率5.655%，借款逾期利率上浮50%，借款用途为借新还旧。同日，除创宏公司外的其余被告均与原告签订保证合同，承诺对上述借款本息及原告为实现债权的费用提供连带责任担保，保证期间为主债务到期之日起两年。原告于2016年6月30日向被告创宏公司发放借款550万元。嗣后，被告未按约归还借款本息，期内欠息163288.13元、截止2018月4月25日拖欠逾期利息390077.18元，本金也未能归还。\", \"law_defendant_arguing\": \"各被告均未作答辩。\", \"law_pre_plaintiff_request\": \"\", \"law_pre_defendant_arguing\": \"\", \"law_pre_court_review\": \"\", \"law_pre_court_identification\": \"\", \"law_appellant_request\": \"\", \"law_appellee_arguing\": \"\", \"law_current_review\": \"当事人围绕诉讼请求依法提交了证据，对当事人无异议的证据，本院予以确认并在卷佐证。现认定事实如下：2016年6月30日，原告与被告创宏公司签订《流动资金借款合同》，约定由原告向被告创宏公司提供借款550万元，借款期限为2016年6月30日至2017年6月28日，年利率5.655%，借款逾期利率上浮50%。同日，除创宏公司外的其余被告均与原告签订保证合同，承诺对上述借款本息及原告为实现债权的费用提供连带责任担保，保证期间为主债务到期之日起两年。原告于2016年6月30日向被告创宏公司发放借款550万元。嗣后，被告未按约归还借款本息，期内欠息163288.13元、截止2018月4月25日拖欠逾期利息390077.18元，本金也未能归还。 另查明，该笔借款用途为借新还旧，所归还的借款均由本案除创宏公司以外的其余被告提供连带责任担保。\", \"law_current_identification\": \"本院认为，合法的借贷关系受法律保护。被告创宏公司向原告借款550万元未能归还的事实清楚，证据充分，借款应当清偿。原告要求被告创宏公司给付期内利息163288.13元、截止2018月4月25日的逾期利息390077.18元，并自2018年4月26日起按年利率8.4825%承担利息、复利至债务清偿止，合乎约定，且未超出法定利率标准范围，本院予以支持。案涉借款由创宏公司以外的其余被告提供连带责任担保，且其余被告之前也均为所还借款提供连带责任担保，担保范围包括借款本息及原告实现债权费用等，原告要求担保人就上述借款本息承担连带清偿责任，本院予以支持。各被告经本院合法传唤，无正当理由拒不到庭参加诉讼，不影响本院依据查明的事实和相关的法律规定，对本案作出缺席判决。为维护当事人的合法权益，依照《中华人民共和国合同法》第二百零五条、第二百零六条、第二百零七条、《中华人民共和国担保法》第十八条、二十一条、《中华人民共和国民事诉讼法》第一百四十四条的规定，判决如下：\", \"law_execute_process\": \"\", \"law_judge_result\": \"一、被告江苏创宏建筑有限公司于本判决生效后十五日内偿还原告江苏张家港农村商业银行股份有限公司丹阳支行借款本金550万元，给付期内利息163288.13元、截止2018月4月25日的逾期利息390077.18元，并自2018年4月26日起按年利率8.4825%承担利息、复利至债务清偿止截止； 二、被告东尼集团有限公司、镇江市投资担保有限公司、江苏双丹投资有限公司、江苏丹东装饰有限公司、江苏银鑫房地产开发有限公司、江苏东尼园林工程有限公司、江苏东尼大酒店有限公司、张银钟、赵龙英、赵金良、眭月芳对上述借款本息承担连带清偿责任，担保人在承担连带清偿责任后有权向债务人追偿。 件受理费减半收取27087元，保全费5000元，合计32087元，由被告江苏创宏建筑有限公司负担，被告东尼集团有限公司、镇江市投资担保有限公司、江苏双丹投资有限公司、江苏丹东装饰有限公司、江苏银鑫房地产开发有限公司、江苏东尼园林工程有限公司、江苏东尼大酒店有限公司、张银钟、赵龙英、赵金良、眭月芳对上述费用承担连带给付责任（此费用原告已垫付，各被告应将其承担部分连同应归还借款本息一并给付原告）。 如果各被告未按本判决指定的期限履行给付金钱义务，应当按照《中华人民共和国民事诉讼法》第二百五十三条的规定，加倍支付迟延履行期间的债务利息。 如不服本判决，可在判决书送达之日起十五日内向本院递交上诉状，并按对方当事人的人数提出副本，上诉于江苏省镇江市中级人民法院，同时向该院预交上诉案件受理费。\", \"law_judge_collegiate_bench\": \"审判员张忻\", \"law_judge_appendix\": \"附：本判决适用法律条款 《中华人民共和国合同法》 第二百零五条借款人应当按照约定的期限支付利息。对支付利息的期限没有约定或者约定不明确，依照本法第六十一条的规定仍不能确定，借款期间不满一年的，应当在返还借款时一并支付；借款期间一年以上的，应当在每届满一年时支付，剩余期间不满一年的，应当在返还借款时一并支付。 第二百零六条借款人应当按照约定的期限返还借款。对借款期限没有约定或者约定不明确，依照本法第六十一条的规定仍不能确定的，借款人可以随时返还；贷款人可以催告借款人在合理期限内返还。 第二百零七条借款人未按照约定的期限返还借款的，应当按照约定或者国家有关规定支付逾期利息。 《中华人民共和国担保法》 第十八条当事人在保证合同中约定保证人与债务人对债务承担连带责任的，为连带责任保证。 连带责任保证的债务人在主合同规定的债务履行期限届满没有履行债务的，债权人可以要求债务人履行债务，也可以要求保证人在其保证范围内承担保证责任。 第二十一条保证担保的范围包括主债权及利息、违约金、损害赔偿金和实现债权的费用。保证合同另有约定的，按照约定。 当事人对保证担保的范围没有约定或者约定不明确的，保证人应当对全部债务承担责任。 《中华人民共和国民事诉讼法》 第一百四十四条被告经传票传唤，无正当理由拒不到庭的，或者未经法庭许可中途退庭的，可以缺席判决。\", \"qcc_law_judge_date\": \"二零一八年七月二十日\"}",
//
//                "江苏银鑫房地产开发有限公司,pa6faa4222132b1098fcf36672ff0ea7,0297ce55f043a697e9f69165aae19e65,f33ad3fae8cae6c067af6c3b18980670,875763855df7098d9ab5710748620f14,江苏东尼园林工程有限公司,东尼集团有限公司,赵龙英,江苏东尼大酒店有限公司,p017de7ccc7c6c56470a861ce6af30bc,江苏双丹投资有限公司,江苏丹东装饰有限公司,眭月芳,7d132116c262c10361e40fa3b85ab97c,赵金良,p58398600a087eb0aa8733fdb6246432,90a7225f9734b42a5886854a6d0a5dd7,p87780e10f5e4604bd1916fe3c4ee611,75b7b48eeaecc0010fc5b600d65f5d14,江苏创宏建筑有限公司,张银钟,镇江市投资担保有限公司,f1f6be7ebc7d7d5375bdcb2f8f9fab62,64ef46c79de15648e7fcd6db4e1a5c40"
//        ,"江苏张家港农村商业银行股份有限公司丹阳支行,43fd9b414fbb323bb21b8f5bdef85fb4"
//        ,""));
        System.out.println(findFirstJSONArray(JSON.parseObject("{\"role\":\"assistant\",\"content\":\"{\\\"\\\":[{\\\"抽查编号\\\": \\\"未提供\\\",\\\"被抽样单位名称\\\": \\\"未提供\\\",\\\"被抽样地址\\\": \\\"未提供\\\",\\\"生产企业名称\\\": \\\"未提供\\\",\\n\\\"生产企业地址\\\": \\\"未提供\\\",\\\"产品名称\\\": \\\"未提供\\\",\\\"产品类别名称\\\": \\\"未提供\\\",\\\"规格型号\\\": \\\"未提供\\\",\\\"商标\\\": \\\"未提供\\\",\\\"是否食品\\\": \\\"未提供\\\",\\\"产品等级\\\": \\\"未提供\\\",\\\"生产批次/日期\\\":\\n\\\"未提供\\\",\\\"抽查单位\\\": \\\"未提供\\\",\\\"承检机构\\\": \\\"未提供\\\",\\\"抽查日期\\\": \\\"未提供\\\",\\\"抽查类型\\\": \\\"未提供\\\",\\\"抽样来源\\\": \\\"未提供\\\",\\\"不合格项目\\\": \\\"未提供\\\",\\\"抽查值\\\": \\\"未提供\\\",\\n\\\"标准值\\\": \\\"未提供\\\",\\\"抽查结果\\\": \\\"未提供\\\"}]}\"}")));
    }

    private static JSONArray findFirstJSONArray(Object node) {
        if (node == null) {
            return null;
        }

        // 情况 1: 当前节点是 JSONArray，直接返回
        if (node instanceof JSONArray) {
            return (JSONArray) node;
        }

        // 情况 2: 当前节点是 JSONObject，递归检查其字段
        if (node instanceof JSONObject) {
            JSONObject jsonObject = (JSONObject) node;
            for (String key : jsonObject.keySet()) {
                Object value = jsonObject.get(key);
                JSONArray foundArray = findFirstJSONArray(value); // 递归检查值
                if (foundArray != null) {
                    return foundArray;
                }
            }
        }

        // 情况 3: 当前节点是 String，尝试解析为 JSON 并递归检查
        if (node instanceof String) {
            try {
                Object parsed = JSON.parse((String) node); // 尝试解析字符串
                return findFirstJSONArray(parsed); // 递归检查解析后的对象
            } catch (Exception e) {
                // 如果不是合法 JSON，忽略
            }
        }

        // 情况 4: 其他类型（如 Integer、Boolean 等），直接跳过
        return null;
    }}
