package com.qcc.udf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.inject.internal.cglib.core.$LocalVariablesSorter;
import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentLengthException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDTF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用于解析改版后的用户权益时间段
 */
public class OrderRightsReferenceUDTF extends GenericUDTF {


    static DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public StructObjectInspector initialize(StructObjectInspector argOIs) throws UDFArgumentException {
        if (argOIs.getAllStructFieldRefs().size() != 1) {
            throw new UDFArgumentLengthException("ExplodeRiskMap takes only one argument");
        }
        // 输出
        List<String> fieldNames = new ArrayList<String>(3);
        List<ObjectInspector> fieldOIs = new ArrayList<ObjectInspector>(3);
        fieldNames.add("order_code");
        fieldNames.add("rights_start_time");
        fieldNames.add("rights_end_time");
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        return ObjectInspectorFactory.getStandardStructObjectInspector(fieldNames, fieldOIs);
    }

    @Override
    public void process(Object[] objects) throws HiveException {
        JSONObject jsonObject = JSONObject.parseObject(objects[0].toString());
        JSONArray orders = jsonObject.getJSONArray("orders");
        LocalDateTime oldVipEndDateTime = jsonObject.getTimestamp("vip_end_time") == null ? null : jsonObject.getTimestamp("vip_end_time").toLocalDateTime();
        LocalDateTime oldSVipEndDateTime = jsonObject.getTimestamp("svip_end_time") == null ? null : jsonObject.getTimestamp("svip_end_time").toLocalDateTime();

        //判断是否是10.27改版之后的数据 改版之后数据不做处理
        List<Object> filter = orders.stream().filter(temp -> ((JSONObject) temp).containsKey("rights_start_time") && !"NULL".equals(((JSONObject) temp).getString("rights_start_time")))
                .collect(Collectors.toList());
        if (filter.size() == orders.size()){
            export(filter);
            return;
        }
       orders.removeAll(filter);

        ArrayList<LocalDateTime> vipEndTime = new ArrayList<LocalDateTime>();
        ArrayList<LocalDateTime> SvipStartTime = new ArrayList<LocalDateTime>();
        ArrayList<LocalDateTime> SvipEndTime = new ArrayList<LocalDateTime>();
        ArrayList<JSONObject> orderRight = new ArrayList<JSONObject>();
        //判断老权益有没有过期  如过期则置位null  6.10订单权益改版
        LocalDateTime date = LocalDateTime.of(2021, 6, 10, 0, 0, 0);
        if (oldVipEndDateTime != null) {
            if (oldVipEndDateTime.isAfter(date)) {
                vipEndTime.add(oldVipEndDateTime);
            }
        }

        if (oldSVipEndDateTime != null) {
            if (oldSVipEndDateTime.isAfter(date)) {
                SvipStartTime.add(date);
                SvipEndTime.add(oldSVipEndDateTime);
            }
        }

        //按照pay_time排序
        Map<Object, List<Object>> collect = orders.stream().sorted(Comparator.comparing(temp -> ((JSONObject) temp).getTimestamp("pay_time"))).collect(Collectors.groupingBy(temp -> ((JSONObject) temp).getString("goods_type")));

        //判断是否存在svip
        //优先筛选出svip订单  根据svip订单再确定vip订单的时间

        List<Object> SVIPList = collect.get("SVIP");
        List<Object> VIPList = collect.get("VIP");

        //svip 权益持续时间
        List<JSONObject> SVIPRightTimeList = new ArrayList<JSONObject>();

        if (oldSVipEndDateTime != null){
            JSONObject right = new JSONObject();
            right.put("order_code", null);
            right.put("rights_start_time", dateTimeFormatter.format(date));
            right.put("rights_end_time", dateTimeFormatter.format(oldSVipEndDateTime));
            orderRight.add(right);
            right.put("rights_start_dateTime", date);
            right.put("rights_end_dateTime", oldSVipEndDateTime);
            right.put("valid_period_months", Period.between(date.toLocalDate(),oldSVipEndDateTime.toLocalDate()).getMonths());
            right.put("real_give_days", 0);
            right.put("next", 0 );
            SVIPRightTimeList.add(right);
        }

        if (SVIPList != null && SVIPList.size() > 0) {
            int i ;
            if (oldSVipEndDateTime != null ){
                i = 1;
            }else {
                i = 0;
            }

            for (Object k : SVIPList) {
                JSONObject order = (JSONObject) k;
                JSONObject right = new JSONObject();
                String orderCode = order.getString("order_code");
                String payTime = order.getString("pay_time");
                String endTime = order.getString("end_time");
                Integer validPeriodMonths = order.getInteger("valid_period_months");
                Integer realGiveDays = order.getInteger("real_give_days");
                LocalDateTime payLocalDateTime = LocalDateTime.parse(payTime, dateTimeFormatter);
                LocalDateTime endLocalDateTime = LocalDateTime.parse(endTime, dateTimeFormatter);

                if (SvipEndTime.size() == 0) {
                    // 无svip权益  则下单时间则为svip开始时间
                    right.put("order_code", orderCode);
                    right.put("rights_start_time", payTime);
                    right.put("rights_end_time", endTime);
                    orderRight.add(right);
                    right.put("rights_start_dateTime", payLocalDateTime);
                    right.put("rights_end_dateTime", endLocalDateTime);
                    right.put("valid_period_months", validPeriodMonths);
                    right.put("real_give_days", realGiveDays);
                    SvipEndTime.add(endLocalDateTime);
                    SvipStartTime.add(payLocalDateTime);
                } else {
                    // 有svip权益
                    if (SvipEndTime.get(SvipEndTime.size() - 1).isBefore(payLocalDateTime)) {
                        //svip权益早于下单时间 说明已过期 则下单时间则为svip开始时间

                        right.put("order_code", orderCode);
                        right.put("rights_start_time", payTime);
                        right.put("rights_end_time", endTime);
                        orderRight.add(right);
                        right.put("rights_start_dateTime", payLocalDateTime);
                        right.put("rights_end_dateTime", endLocalDateTime);
                        right.put("valid_period_months", validPeriodMonths);
                        right.put("real_give_days", realGiveDays);
                        SvipStartTime.add(payLocalDateTime);
                        SvipEndTime.add(endLocalDateTime);
                    } else {
                        //svip权益晚于下单时间 说明未过期 则SVIP时间继续累加
                        LocalDateTime plus = SvipEndTime.get(SvipEndTime.size() - 1).plusMonths(validPeriodMonths).plusDays(realGiveDays);
                        right.put("order_code", orderCode);
                        right.put("rights_start_time", dateTimeFormatter.format(SvipEndTime.get(SvipEndTime.size() - 1)));
                        right.put("rights_end_time", dateTimeFormatter.format(plus));
                        orderRight.add(right);
                        right.put("rights_start_dateTime", SvipEndTime.get(SvipEndTime.size() - 1));
                        right.put("rights_end_dateTime", plus);
                        right.put("valid_period_months", validPeriodMonths);
                        right.put("real_give_days", realGiveDays);
                        SvipStartTime.add(SvipEndTime.get(SvipEndTime.size() - 1));
                        SvipEndTime.add(plus);
                    }
                }
                right.put("next", i );
                SVIPRightTimeList.add(right);
                i++;
            }
        }


        // vip  需要比较是否是 老权益  ，如果不是老权益 则需要一一比较svip权益
        if (VIPList != null && VIPList.size() > 0) {
            int i = 0;
            for (Object k : VIPList) {
                JSONObject order = (JSONObject) k;
                JSONObject right = new JSONObject();
                String orderCode = order.getString("order_code");
                String payTime = order.getString("pay_time");
                String endTime = order.getString("end_time");
                Integer validPeriodMonths = order.getInteger("valid_period_months");
                Integer realGiveDays = order.getInteger("real_give_days");
                LocalDateTime payLocalDateTime = LocalDateTime.parse(payTime, dateTimeFormatter);
                LocalDateTime endLocalDateTime = LocalDateTime.parse(endTime, dateTimeFormatter);

                LocalDateTime preVipEndTime = date;
                if (vipEndTime.size() == 1 && vipEndTime.get(0) == oldVipEndDateTime) {
                    //必定是第一单
                    preVipEndTime = oldVipEndDateTime;
                } else if (vipEndTime.size() > 0){
                    preVipEndTime = vipEndTime.get(i - 1);
                }


                if (vipEndTime.size() == 0 && SvipEndTime.size() == 0) {
                    //无vip svip权益 必定是第一单
                    right.put("order_code", orderCode);
                    right.put("rights_start_time", payTime);
                    right.put("rights_end_time", endTime);
                    orderRight.add(right);
                    vipEndTime.add(endLocalDateTime);

                } else if (vipEndTime.size() > 0 && SvipEndTime.size() == 0) {
                    //有 vip权益 无svip权益
                    if (preVipEndTime.isBefore(payLocalDateTime)) {
                        //vip权益过期
                        right.put("order_code", orderCode);
                        right.put("rights_start_time", payTime);
                        right.put("rights_end_time", endTime);
                        orderRight.add(right);
                        vipEndTime.add(endLocalDateTime);
                    } else {
                        //上一单权益 覆盖
                        LocalDateTime plus = preVipEndTime.plusMonths(validPeriodMonths).plusDays(realGiveDays);
                        right.put("order_code", orderCode);
                        right.put("rights_start_time", dateTimeFormatter.format(preVipEndTime));
                        right.put("rights_end_time", dateTimeFormatter.format(plus));
                        orderRight.add(right);
                        vipEndTime.add(plus);
                    }

                } else if (vipEndTime.size() == 0 && SvipEndTime.size() > 0) {
                    //无vip权益 svip权益
                    //svip分为 三种情况  为前中后
                    if (endLocalDateTime.isBefore(SvipStartTime.get(0))){
                        //vip在 svip之前
                        right.put("order_code", orderCode);
                        right.put("rights_start_time", payTime);
                        right.put("rights_end_time", endTime);
                        orderRight.add(right);
                        vipEndTime.add(endLocalDateTime);
                    }else  if (SvipEndTime.get(SvipEndTime.size() -1).isBefore(payLocalDateTime)){
                        //vip在svip之后
                        right.put("order_code", orderCode);
                        right.put("rights_start_time", payTime);
                        right.put("rights_end_time", endTime);
                        orderRight.add(right);
                        vipEndTime.add(endLocalDateTime);
                    }else {
                        //中 交集  确定previptime 时间   之前的时间段全部固定  循环处理  previp之后的时间
                        LocalDateTime endTemp = endLocalDateTime;
                        LocalDateTime startTemp = payLocalDateTime;
                        for (JSONObject temp : SVIPRightTimeList) {
                            LocalDateTime rights_start_dateTime = (LocalDateTime) temp.get("rights_start_dateTime");
                            LocalDateTime rights_end_dateTime = (LocalDateTime) temp.get("rights_end_dateTime");
                            Integer svipValidPeriodMonths = temp.getInteger("valid_period_months");
                            Integer svipRealGiveDays = temp.getInteger("real_give_days");
                            int next = temp.getInteger("next");
                           /* if (rights_start_dateTime.isAfter(preVipEndTime)){*/
                                //比较endTemp 和 preVipEndTime
                                if (preVipEndTime.isAfter(startTemp)){
                                    startTemp = preVipEndTime;
                                    endTemp = startTemp.plusMonths(validPeriodMonths).plusDays(realGiveDays);
                                }

                                if (endTemp.isBefore(rights_start_dateTime) ){
                                    //前
                                    right.put("order_code", orderCode);
                                    right.put("rights_start_time", dateTimeFormatter.format(startTemp));
                                    right.put("rights_end_time", dateTimeFormatter.format(endTemp));
                                    orderRight.add(right);
                                    vipEndTime.add(endTemp);
                                    break;
                                }else if(startTemp.isAfter(rights_end_dateTime)){
                                    //后
                                    right.put("order_code", orderCode);
                                    right.put("rights_start_time", dateTimeFormatter.format(startTemp));
                                    right.put("rights_end_time", dateTimeFormatter.format(endTemp));
                                    orderRight.add(right);
                                    vipEndTime.add(endTemp);
                                    break;
                                }else {
                                    // 交集
                                    if (startTemp.isBefore(rights_start_dateTime) && endTemp.isAfter(rights_start_dateTime)){
                                        //第一段
                                        right.put("order_code", orderCode);
                                        right.put("rights_start_time", dateTimeFormatter.format(startTemp));
                                        right.put("rights_end_time", dateTimeFormatter.format(rights_start_dateTime));
                                        orderRight.add(right);

                                        startTemp = rights_end_dateTime;
                                        //延迟权益
                                        endTemp = endTemp.plusMonths(svipValidPeriodMonths).plusDays(svipRealGiveDays);


                                    }else{
                                        // svip完全包含 vip
                                        startTemp = rights_end_dateTime;
                                        endTemp = startTemp.plusMonths(validPeriodMonths).plusDays(realGiveDays);
                                    }
                                    if ( next == SVIPRightTimeList.size() -1 ){
                                        JSONObject clone = (JSONObject) right.clone();
                                        clone.put("order_code", orderCode);
                                        clone.put("rights_start_time", dateTimeFormatter.format(startTemp));
                                        clone.put("rights_end_time", dateTimeFormatter.format(endTemp));
                                        orderRight.add(clone);
                                        vipEndTime.add(endTemp);
                                    }
                                }
                        }
                    }
                } else {
                    //有vip 有svip权益  先消耗svip时间  再算vip时间  同样分为前中后 只是需要考虑之前未结束的 vip svip
                    //因为多笔不连续的svip订单没法判断是否过期  还是先前中后
                    LocalDateTime plus = preVipEndTime.plusMonths(validPeriodMonths).plusDays(realGiveDays);
                    if (endLocalDateTime.isBefore(SvipStartTime.get(0)) && plus.isBefore(SvipStartTime.get(0))){
                        //前
                        if (preVipEndTime.isBefore(payLocalDateTime)) {
                            //vip 权益过期
                            right.put("order_code", orderCode);
                            right.put("rights_start_time", payTime);
                            right.put("rights_end_time", endTime);
                            orderRight.add(right);
                            vipEndTime.add(endLocalDateTime);
                        } else {
                            right.put("order_code", orderCode);
                            right.put("rights_start_time", dateTimeFormatter.format(preVipEndTime));
                            right.put("rights_end_time", dateTimeFormatter.format(plus));
                            orderRight.add(right);
                            vipEndTime.add(plus);
                        }

                    } else if (SvipEndTime.get(SvipEndTime.size() - 1).isBefore(payLocalDateTime) || SvipEndTime.get(SvipEndTime.size() - 1).isBefore(preVipEndTime)) {
                        //后
                        if (preVipEndTime == payLocalDateTime){
                            continue;
                        }else if (preVipEndTime.isBefore(payLocalDateTime)) {
                            //vip 权益过期
                            right.put("order_code", orderCode);
                            right.put("rights_start_time", payTime);
                            right.put("rights_end_time", endTime);
                            orderRight.add(right);
                            vipEndTime.add(endLocalDateTime);
                        } else {
                            right.put("order_code", orderCode);
                            right.put("rights_start_time", dateTimeFormatter.format(preVipEndTime));
                            right.put("rights_end_time", dateTimeFormatter.format(plus));
                            orderRight.add(right);
                            vipEndTime.add(plus);
                        }
                    } else {
                            //中 交集  确定previptime 时间   之前的时间段全部固定  循环处理  previp之后的时间
                            LocalDateTime endTemp = endLocalDateTime;
                            LocalDateTime startTemp = payLocalDateTime;
                            for (JSONObject temp : SVIPRightTimeList) {
                                LocalDateTime rights_start_dateTime = (LocalDateTime) temp.get("rights_start_dateTime");
                                LocalDateTime rights_end_dateTime = (LocalDateTime) temp.get("rights_end_dateTime");
                                Integer svipValidPeriodMonths = temp.getInteger("valid_period_months");
                                Integer svipRealGiveDays = temp.getInteger("real_give_days");
                                int next = temp.getInteger("next");

                                    //比较endTemp 和 preVipEndTime
                                    if (preVipEndTime.isAfter(startTemp)){
                                        if (preVipEndTime == oldVipEndDateTime){
                                            //老权益 直接到权益结束
                                            startTemp = preVipEndTime.plusMonths(svipValidPeriodMonths).plusDays(svipRealGiveDays);
                                            endTemp = startTemp.plusMonths(validPeriodMonths).plusDays(realGiveDays);
                                        }else {
                                            startTemp = preVipEndTime;
                                            endTemp = startTemp.plusMonths(validPeriodMonths).plusDays(realGiveDays);
                                        }

                                    }

                                    if (endTemp.isBefore(rights_start_dateTime) ){
                                        //前
                                        right.put("order_code", orderCode);
                                        right.put("rights_start_time", dateTimeFormatter.format(startTemp));
                                        right.put("rights_end_time", dateTimeFormatter.format(endTemp));
                                        orderRight.add(right);
                                        vipEndTime.add(endTemp);
                                        break;
                                    }else if(startTemp.isAfter(rights_end_dateTime)){
                                        //后
                                        //判断是否和下一个无交集
                                        if (next == SVIPRightTimeList.size() -1){
                                            right.put("order_code", orderCode);
                                            right.put("rights_start_time", dateTimeFormatter.format(startTemp));
                                            right.put("rights_end_time", dateTimeFormatter.format(endTemp));
                                            orderRight.add(right);
                                            vipEndTime.add(endTemp);
                                            break;
                                        }else if (next < SVIPRightTimeList.size() -1  && endTemp.isBefore((LocalDateTime) SVIPRightTimeList.get(next+1).get("rights_start_dateTime"))){
                                            right.put("order_code", orderCode);
                                            right.put("rights_start_time", dateTimeFormatter.format(startTemp));
                                            right.put("rights_end_time", dateTimeFormatter.format(endTemp));
                                            orderRight.add(right);
                                            vipEndTime.add(endTemp);
                                            break;
                                        }else {
                                            continue;
                                        }
                                    }else {
                                        // 交集
                                        if (startTemp.isBefore(rights_start_dateTime) && endTemp.isAfter(rights_start_dateTime)){
                                            //第一段
                                            right.put("order_code", orderCode);
                                            right.put("rights_start_time", dateTimeFormatter.format(startTemp));
                                            right.put("rights_end_time", dateTimeFormatter.format(rights_start_dateTime));
                                            orderRight.add(right);

                                            startTemp = rights_end_dateTime;
                                            //延迟权益
                                            endTemp = endTemp.plusMonths(svipValidPeriodMonths).plusDays(svipRealGiveDays);


                                        }else{
                                            // svip完全包含 vip
                                            startTemp = rights_end_dateTime;
                                            endTemp = startTemp.plusMonths(validPeriodMonths).plusDays(realGiveDays);
                                        }
                                        if ( next == SVIPRightTimeList.size() -1){
                                            JSONObject clone = (JSONObject) right.clone();
                                            clone.put("order_code", orderCode);
                                            clone.put("rights_start_time", dateTimeFormatter.format(startTemp));
                                            clone.put("rights_end_time", dateTimeFormatter.format(endTemp));
                                            orderRight.add(clone);
                                            vipEndTime.add(endTemp);
                                        }
                                    }

                            }
                    }
                }
                i++;
            }
        }


        List<Object> finalList = orderRight.stream().sorted(Comparator.comparing(temp -> temp.getTimestamp("rights_start_time"))).collect(Collectors.toList());
        //在加上新版本未处理的订单权益
        finalList.addAll(filter);
        export(finalList);

    }

    public void export(List<Object> order){
        System.out.println(order);
        order.forEach(temp -> {
            JSONObject json = (JSONObject) temp;
            String[] strs = new String[3];
            strs[0] = json.getString("order_code");
            strs[1] = json.getString("rights_start_time");
            strs[2] = json.getString("rights_end_time");
            try {
                if (strs[0] != null){
                    forward(strs);
                }

            } catch (HiveException e) {
                e.printStackTrace();
            }
        });
    }

    @Override
    public void close() throws HiveException {

    }

    /**
     * 静态方法，用于解析改版后的用户权益时间段
     */
    public static void main(String[] args) throws HiveException {
        Object[] item = new Object[1];
        //item[0] = "{\"vip_end_time\":\"2022-01-01 00:00:00\",\"svip_end_time\":\"NULL\",\"orders\":[{\"order_code\":\"1623806573509072\",\"order_total\":1.0,\"goods_type\":\"VIP\",\"pay_time\":\"2021-07-01 00:00:00\",\"end_time\":\"2022-07-01 00:00:00\",\"valid_period_months\":12},{\"order_code\":\"1623805619110054\",\"order_total\":1.0,\"goods_type\":\"SVIP\",\"pay_time\":\"2021-09-01 00:00:00\",\"end_time\":\"2022-09-01 00:00:00\",\"valid_period_months\":12}]}";
        //item[0] = "{\"vip_end_time\":\"2022-01-01 00:00:00\",\"svip_end_time\":\"NULL\",\"orders\":[{\"order_code\":\"1623806573509072\",\"order_total\":1.0,\"goods_type\":\"VIP\",\"pay_time\":\"2021-07-01 00:00:00\",\"end_time\":\"2022-07-01 00:00:00\",\"valid_period_months\":12},{\"order_code\":\"1623805619110054\",\"order_total\":1.0,\"goods_type\":\"SVIP\",\"pay_time\":\"2022-02-01 00:00:00\",\"end_time\":\"2023-02-01 00:00:00\",\"valid_period_months\":12}]}";
        //item[0] = "{\"vip_end_time\":\"2022-01-01 00:00:00\",\"svip_end_time\":\"NULL\",\"orders\":[{\"order_code\":\"1623806573509072\",\"order_total\":1.0,\"goods_type\":\"SVIP\",\"pay_time\":\"2021-07-01 00:00:00\",\"end_time\":\"2022-07-01 00:00:00\",\"valid_period_months\":12,\"real_give_days\":0},{\"order_code\":\"1623805619110054\",\"order_total\":1.0,\"goods_type\":\"VIP\",\"pay_time\":\"2021-09-01 00:00:00\",\"end_time\":\"2022-09-01 00:00:00\",\"valid_period_months\":12,\"real_give_days\":0}]}";
        //item[0] = "{\"vip_end_time\":\"NULL\",\"svip_end_time\":\"2022-01-01 00:00:00\",\"orders\":[{\"order_code\":\"1623806573509072\",\"order_total\":1.0,\"goods_type\":\"SVIP\",\"pay_time\":\"2021-07-01 00:00:00\",\"end_time\":\"2022-07-01 00:00:00\",\"valid_period_months\":12,\"real_give_days\":0},{\"order_code\":\"1623805619110054\",\"order_total\":1.0,\"goods_type\":\"VIP\",\"pay_time\":\"2021-09-01 00:00:00\",\"end_time\":\"2022-09-01 00:00:00\",\"valid_period_months\":12,\"real_give_days\":0}]}";
        //item[0]= "{\"vip_end_time\":\"2021-11-20 16:56:17\",\"svip_end_time\":\"2021-11-20 16:56:17\",\"orders\":[{\"order_code\":\"1653620864754122\",\"order_total\":360,\"goods_type\":\"VIP\",\"pay_time\":\"2022-05-27 11:07:56\",\"end_time\":\"2023-07-26 11:07:56\",\"valid_period_months\":12,\"real_give_days\":60},{\"order_code\":\"1638159087571333\",\"order_total\":980,\"goods_type\":\"SVIP\",\"pay_time\":\"2021-11-29 12:11:37\",\"end_time\":\"2022-05-29 12:11:37\",\"valid_period_months\":6,\"real_give_days\":0},{\"order_code\":\"1657618152743958\",\"order_total\":1800,\"goods_type\":\"SVIP\",\"pay_time\":\"2022-07-12 17:29:21\",\"end_time\":\"2023-07-12 17:29:21\",\"valid_period_months\":12,\"real_give_days\":0}]}";
        item[0] = "{\"vip_end_time\":\"NULL\",\"svip_end_time\":\"NULL\",\"orders\":[{\"order_code\":\"1597896014079379\",\"goods_type\":\"VIP\",\"pay_time\":\"2020-08-20 12:00:25\",\"end_time\":\"2021-08-20 12:00:25\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1630893512537591\",\"goods_type\":\"VIP\",\"pay_time\":\"2021-09-06 09:58:39\",\"end_time\":\"2022-11-05 09:58:39\",\"valid_period_months\":12,\"real_give_days\":60,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1667458907648503\",\"goods_type\":\"VIP\",\"pay_time\":\"2022-11-03 15:01:52\",\"end_time\":\"2024-01-02 15:01:52\",\"valid_period_months\":12,\"real_give_days\":60,\"rights_start_time\":\"2022-11-06 17:12:34\",\"rights_end_time\":\"2024-01-05 17:12:34\"}]}";
        OrderRightsReferenceUDTF udf = new OrderRightsReferenceUDTF();
        udf.process(item);
    }
}
