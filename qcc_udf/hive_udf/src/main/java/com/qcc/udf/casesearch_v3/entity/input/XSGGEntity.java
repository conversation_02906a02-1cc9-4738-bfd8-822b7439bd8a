package com.qcc.udf.casesearch_v3.entity.input;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.casesearch_v3.enums.CaseCategoryEnum;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 悬赏公告
 * <AUTHOR>
 * @date 2021年12月30日 11:38
 */
@Data
public class XSGGEntity extends BaseCaseEntity {

    /**
     * id : 00e10d07b9c892d687d7cb09090ac69f
     * companynames :
     * anno : （2014）顺庆执字第28号
     * provincecode :
     * courtname :
     * isvalid : 1
     * publish_date :
     * start_date :
     * end_date :
     * type : 悬赏公告
     * executor_keyno_array :
     * executor_adress : 阆中市水观镇老山寨村五组未履行金额为400000.00元及利息悬赏金按执行到位的10%奖励
     * party_info : [{"address":"阆中市水观镇老山寨村五组未履行金额为400000.00元及利息悬赏金按执行到位的10%奖励","birthday":"","gender":"","idcardno":"512930194701255071","nation":"","party":"","possible_address":""}]
     * execution_money :
     * outstanding_amount : 400000.00
     * reward_info : [{"reward_amount":"执行到位的10%","reward_conditions":""}]
     * judge :
     * judge_contact :
     */

    private String id;
    private String companynames;
    private String anno;
    private String provincecode;
    private String courtname;
    private int isvalid;
    private String publish_date;
    private String start_date;
    private String end_date;
    private String type;
    private String executor_keyno_array;
    private String executor_adress;
    private String party_info;
    private String execution_money;
    private String outstanding_amount;
    private String reward_info;
    private String judge;
    private String judge_contact;
    private String spider_id;

    private List<NameAndKeyNoEntity> nameandkeynoEntityList;
    private List<XSGGRewardInfo> xsggRewardInfoList;

    public static List<XSGGEntity> convert(List<String> jsonList) {
        List<XSGGEntity> list = new ArrayList<>();
        XSGGEntity entity = null;
        if(CollectionUtils.isEmpty(jsonList)){
            return list;
        }
        for (String json : jsonList) {
            if(Strings.isNullOrEmpty(json)){
                continue;
            }
            entity = JSON.parseObject(json, XSGGEntity.class);
            if(entity == null  || Strings.isNullOrEmpty(entity.getId())){
                continue;
            }

            String str = entity.getExecutor_keyno_array();

            if (!Strings.isNullOrEmpty(str)) {
                entity.setNameandkeynoEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getNameandkeynoEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                }
            }
            str = entity.getReward_info();

            if (!Strings.isNullOrEmpty(str)) {
                entity.setXsggRewardInfoList(JSON.parseArray(str, XSGGRewardInfo.class));
            }

            //公共字段赋值
            entity.setBaseCaseNo(entity.getAnno());
            entity.setBaseCaseCategoryEnum(CaseCategoryEnum.XSGG);
            if(!Strings.isNullOrEmpty(entity.getCompanynames())){
                entity.setBaseSearchWordSet(Arrays.stream(entity.getCompanynames().split(","))
                        .collect(Collectors.toSet()));
            }

            entity.setBaseCourt(entity.getCourtname());
            entity.setBaseProvinceCode(entity.getProvincecode());
            entity.setBaseNameKeyNoList(entity.getNameandkeynoEntityList());
            entity.setBaseId(entity.getBaseCaseCategoryEnum().getType()+"_"+entity.getId());
            String caseType= CommonV3Util.getCaseType(CommonV3Util.getCaseNo(entity.getBaseCaseNo()));
            //案件类型为空的数据直接过滤
            if(Strings.isNullOrEmpty(caseType)){
                continue;
            }
            list.add(entity);
        }
        return list;
    }

}
