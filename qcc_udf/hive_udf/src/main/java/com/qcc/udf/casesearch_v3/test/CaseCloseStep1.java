package com.qcc.udf.casesearch_v3.test;

import com.alibaba.fastjson.JSON;
import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.output.CaseListEntity;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022年07月06日 14:26
 */
public class CaseCloseStep1  extends UDF {
    public static void main(String[] args) {
        String json = "[{\"Defendant\":[],\"CfgsList\":[],\"SdggList\":[],\"XzcjList\":[],\"Court\":\"\",\"LatestTimestamp\":1293811200,\"ZxList\":[],\"XzcffList\":[],\"CfdfList\":[],\"HbcfList\":[],\"CaseList\":[],\"TrialRound\":\"民事一审\",\"Prosecutor\":[],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"SfpmList\":[],\"XdpgjgList\":[],\"AnNo\":\"（2011）未民一初字第816号\",\"CaseType\":\"民事案件\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[],\"SqtjList\":[]},{\"Defendant\":[{\"KeyNo\":\"\",\"Role\":\"被执行人\",\"Org\":-2,\"Name\":\"胡蔚玲\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"陕西省西安市未央区人民法院\",\"LatestTimestamp\":1339027200,\"ZxList\":[],\"XzcffList\":[],\"CfdfList\":[],\"HbcfList\":[],\"CaseList\":[{\"CaseType\":\"执行裁定书\",\"JudgeDate\":1339027200,\"Amt\":\"\",\"Id\":\"2411b6d1108eb0dedab5573dda9242790\",\"ResultType\":\"裁定结果\",\"DocType\":\"裁定日期\",\"IsValid\":1,\"Result\":\"终结（2011）未民一初字第00816号民事判决的执行。\",\"ShieldCaseFlag\":0}],\"TrialRound\":\"首次执行\",\"Prosecutor\":[{\"KeyNo\":\"\",\"Role\":\"申请执行人\",\"Org\":-2,\"Name\":\"张恒\"}],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2012）未执字第00606-2号\",\"CaseType\":\"执行案件\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"物权保护纠纷案件执行\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]}]";
        System.out.println(evaluate(json));
    }
    public static String evaluate(String json) {
        List<InfoListEntity> infoList = JSON.parseArray(json,InfoListEntity.class);
        InfoListEntity firstTrialRound = infoList.stream().sorted(Comparator.comparingLong(InfoListEntity::getLatestTimestamp)
                .reversed()).findFirst().get();
        List<CaseListEntity> caseList = firstTrialRound.getCaseList().stream()
                .filter(x->x.getShieldCaseFlag() == 1
                        || x.getDocType().equals("通知日期")
                        || x.getDocType().equals("调解日期")).collect(Collectors.toList());
        return JSON.toJSONString(caseList);
    }

}
