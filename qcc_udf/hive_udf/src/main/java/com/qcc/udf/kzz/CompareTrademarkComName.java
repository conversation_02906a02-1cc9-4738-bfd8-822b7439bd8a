package com.qcc.udf.kzz;

import com.hankcs.hanlp.HanLP;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Pattern;

/**
 * 对比商标关联主体名称是否变更
 * <AUTHOR>
 * @date 2021/10/9
 */
public class CompareTrademarkComName extends UDF {

    private static Pattern pattern = Pattern.compile("((\\(|（)[^(\\)|）)]*(\\)|）))");
    private static Pattern pattern1 = Pattern.compile("(\\[[^\\]]*\\])");

//    public static void main(String[] args) {
//        String companyName = "星河电子（宝安）有限公司[特控:92-登报]";
//        String trademarkCompanyName="星河电子（宝安）有限公司[特控:92-登报]";
//        int contentResult = evaluate(companyName,trademarkCompanyName);
//        System.out.println(contentResult);
//    }
    /**
     * 1-代表有变更
     * 0-代表没有变更
     * @param companyName
     * @param trademarkCompanyName
     * @return
     */
    public static int evaluate(String companyName,String trademarkCompanyName){
        int compareResult=0;
        try{
            if(StringUtils.isBlank(companyName) && StringUtils.isBlank(trademarkCompanyName)){
                compareResult=0;
            }else if(StringUtils.isBlank(companyName) && StringUtils.isNotBlank(trademarkCompanyName)){
                compareResult= 1;
            }
            if(StringUtils.isNotBlank(companyName) && StringUtils.isBlank(trademarkCompanyName)){
                compareResult= 1;
            }
            if(StringUtils.isNotBlank(companyName) && StringUtils.isNotBlank(trademarkCompanyName)){
                String simCompanyName = HanLP.convertToSimplifiedChinese(companyName);
                String simCompanyNameTemp = simCompanyName.replaceAll(pattern.pattern(),"");
                simCompanyNameTemp = simCompanyNameTemp.replaceAll(pattern1.pattern(),"");
                simCompanyNameTemp = simCompanyNameTemp.replaceAll("·|\\)|\\.| |‧|\\u2029|•|　|\\*","");

                if(trademarkCompanyName.contains("@")){
                    String[] tradeComNameArr = trademarkCompanyName.split("@");
                    for (String comName : tradeComNameArr) {
                        String simTrademarkCompanyName = HanLP.convertToSimplifiedChinese(comName);
                        //去除名称中的括号以及括号内的内容
                        String simTrademarkCompanyNameTemp = simTrademarkCompanyName.replaceAll(pattern.pattern(),"");
                        simTrademarkCompanyNameTemp = simTrademarkCompanyNameTemp.replaceAll("·|\\)|\\.| |‧|\\u2029|•|　|\\*","");

                        if(simCompanyNameTemp.equals(simTrademarkCompanyNameTemp)){
                            compareResult=0;
                        }else{
                            compareResult=1;
                            break;
                        }
                    }
                }else{

                    String simTrademarkCompanyName = HanLP.convertToSimplifiedChinese(trademarkCompanyName);
                    //去除名称中的括号以及括号内的内容
                    String simTrademarkCompanyNameTemp = simTrademarkCompanyName.replaceAll(pattern.pattern(),"");
                    simTrademarkCompanyNameTemp = simTrademarkCompanyNameTemp.replaceAll(pattern1.pattern(),"");
                    simTrademarkCompanyNameTemp = simTrademarkCompanyNameTemp.replaceAll("·|\\)|\\.| |‧|\\u2029|•|　|\\*","");

                    if(simCompanyNameTemp.equals(simTrademarkCompanyNameTemp)){
                        compareResult=0;
                    }else{
                        compareResult=1;
                    }
                }

            }
        }catch (Exception e){
            compareResult=0;
        }

        return compareResult;
    }
}
