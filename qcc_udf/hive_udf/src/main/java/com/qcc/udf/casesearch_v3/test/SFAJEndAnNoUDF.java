package com.qcc.udf.casesearch_v3.test;

import com.google.common.base.Strings;
import org.apache.commons.collections.CollectionUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021年04月20日 19:20
 */
public class SFAJEndAnNoUDF  extends UDF {
    public static Integer evaluate(String json)  {
        if(Strings.isNullOrEmpty(json)){
            return Integer.MAX_VALUE;
        }
        String[] arrays = json.split(",");
        List<Integer> anNoSet =new ArrayList<>();
        for (String anno : arrays) {
            Integer an = convertInt(anno.replace("号","").split("-")[0]);
            if(an != null){
                anNoSet.add(an);
            }

        }
        if(CollectionUtils.isNotEmpty(anNoSet))
            return anNoSet.stream().sorted().findFirst().get();
        else
            return Integer.MAX_VALUE;
    }

    static Integer convertInt(String num){
        try{
            return Integer.valueOf(num);
        }catch (Exception e){
            return null;
        }
    }

    public static void main(String[] args) {
        System.out.println(evaluate("17571号,17575号,35-37号"));
    }
}
