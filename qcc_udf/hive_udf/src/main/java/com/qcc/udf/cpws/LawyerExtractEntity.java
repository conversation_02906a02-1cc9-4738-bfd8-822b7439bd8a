package com.qcc.udf.cpws;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 裁判文书律所提取信息对象
 */
@Data
@AllArgsConstructor
@Builder
public class LawyerExtractEntity {
    // 律师名
    @JSONField(name = "LawyerList")
    private List<JSONObject> LawyerList;
    // 所属律所（没有可为空）
    @JSONField(name = "LawFirm_Name")
    private String LawFirm_Name;
    // 律所对应的keyNo
    @JSONField(name = "LawFirm_KeyNo")
    private String LawFirm_KeyNo;


    @Override
    public String toString() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("LawyerList", (LawyerList != null ? LawyerList : new ArrayList<>()));
        jsonObject.put("LawFirm_Name", (LawFirm_Name != null ? LawFirm_Name : ""));
        jsonObject.put("LawFirm_KeyNo", (LawFirm_KeyNo != null ? LawFirm_KeyNo : ""));
        return jsonObject.toJSONString();
    }
}