package com.qcc.udf.CommonService;


import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDF;

@Description(name = "HiveToHbase",
        value = "_FUNC_(String hbaseTableName, String rowKey, String columns, Object values...); - Return hbase_rowkey",
        extended = "Example:\n" +
                " > SELECT _FUNC_('hbase_table', 'column1,column2,...,columnn', column1,column2,...,columnn) FROM hive_table;")
public class HiveToHbase extends UDF {
    private static Configuration conf = HBaseConfiguration.create();
    private static Connection connection = null;
    private static HiveToHbase instance = null;
    private final static String HBASE_COLUMN_FAMILY_DATA = "data";//列簇

    public HiveToHbase() {
        /**
         * 创建hbase的数据库链接池
         */
        if (connection == null || connection.isClosed()) {
            try {
                conf.set("hbase.zookeeper.quorum", "zookeeper.ld-hadoop.com");
                conf.set("hbase.zookeeper.property.clientPort", "32181");
                connection = ConnectionFactory.createConnection(conf);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private static synchronized HiveToHbase getInstance() {
        if (instance == null) {
            instance = new HiveToHbase();
        }
        return instance;
    }


    /**
     * upsert hbase表
     *
     * @param hbaseTableName hbase表名
     * @param rowKey
     * @param columns        需要更新的字段，逗号分隔
     * @param values         字段对应值
     * @return
     */
    private boolean upsertHbase(String hbaseTableName, String rowKey, String[] columns, Object... values) {
        boolean result = false;
        try {
            Put put = new Put(rowKey.getBytes());
            for (int i = 1; i < values.length; i++) {
                put.addColumn(HBASE_COLUMN_FAMILY_DATA.getBytes(), columns[i].getBytes(), String.valueOf(values[i]).getBytes());
                //            System.out.println(i + ": " + columns[i]);
                //            System.out.println(values[i]);
            }
//        System.out.println(put.toJSON());
            connection.getTable(TableName.valueOf(hbaseTableName)).put(put);
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * @param hbaseTableName hbase表名
     * @param rowKey         rowkey
     * @param column         字段名，第一个为rowkey
     * @param values         字段值，第一个必须为rowkey
     * @return
     * @throws Exception
     */
    public static String evaluate(String hbaseTableName, String rowKey, String column, Object... values) throws InterruptedException {
        try {
            String result;
            if (StringUtils.isNotBlank(hbaseTableName) && column.contains(",")) {
                String[] columns = column.split(",");
                if (columns.length == values.length) {
                    HiveToHbase instance = HiveToHbase.getInstance();
                    if (!instance.upsertHbase(hbaseTableName, String.valueOf(rowKey), columns, values)) {
                        throw new InterruptedException("输入格式有误，请检查参数是否有误!!!");
                    }
                    result = "rowkey: " + rowKey;
                } else {
                    throw new InterruptedException("输入格式有误，请检查列名和列是否匹配!!!");
                }
            } else {
                throw new InterruptedException("输入格式有误，请检查表名和列名是否有误!!!");
            }
            return result;
        } catch (Exception e) {
            throw e;
        }
    }

//    public static void main(String[] args) throws IOException,InterruptedException {
//        HiveToHbase.getInstance().evaluate("test","1234", "key,name", 123, "f625a5b661058ba5082ca508f99ffe1b","123");
//    }

}
