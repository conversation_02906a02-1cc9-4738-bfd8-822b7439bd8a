package com.qcc.udf.risk_analysis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

public class GetSxListSqr extends UDF {

    public String evaluate(String keyNo, List<String> infoList) {
        JSONArray result = new JSONArray();
        JSONArray array = new JSONArray();
        if (infoList != null && infoList.size() > 0){
            Map<String, JSONObject> infoMap = new LinkedHashMap<>();
            for (String str : infoList){
                JSONObject json = JSONObject.parseObject(str);
                json.put("subkeyno", json.getString("subkeyno") == null ? "" : json.getString("subkeyno"));
                json.put("subname", json.getString("subname") == null ? "" : json.getString("subname"));

                String key = "";
                if (StringUtils.isNotEmpty(json.getString("subkeyno"))){
                    key = json.getString("subkeyno");
                }else if (StringUtils.isNotEmpty(json.getString("subname"))){
                    key = RiskAnalysisUtil.full2Half(json.getString("subname"));
                }

                if (StringUtils.isNotEmpty(key)){
                    if (infoMap.containsKey(key)){
                        JSONObject item = infoMap.get(key);
                        item.put("KeyNo", keyNo);
                        JSONObject sqrInfo = new JSONObject();
                        sqrInfo.put("KeyNo", json.getString("subkeyno"));
                        sqrInfo.put("Name", json.getString("subname"));
                        sqrInfo.put("Org", RiskAnalysisUtil.getOrgByKeyNo(sqrInfo.getString("KeyNo")));
                        item.put("NameKeyNo", sqrInfo);
                        item.put("Cnt", item.getInteger("Cnt") + 1);
                        item.put("Anno", item.getString("Anno").concat(",").concat(json.getInteger("publicdate").toString().concat("_").concat(json.getString("anno"))));
                        item.put("PublishDate", item.getString("PublishDate").concat(",").concat(json.getInteger("publicdate").toString()));
                        item.put("SortDate", json.getInteger("publicdate") > item.getInteger("SortDate") ? json.getInteger("publicdate") : item.getInteger("SortDate"));
                        item.put("OriginId", item.getString("OriginId").concat(",").concat(json.getString("id")));

                        infoMap.put(key, item);
                    }else{
                        JSONObject item = new JSONObject();
                        item.put("KeyNo", keyNo);
                        JSONObject sqrInfo = new JSONObject();
                        sqrInfo.put("KeyNo", json.getString("subkeyno"));
                        sqrInfo.put("Name", json.getString("subname"));
                        sqrInfo.put("Org", RiskAnalysisUtil.getOrgByKeyNo(sqrInfo.getString("KeyNo")));
                        item.put("NameKeyNo", sqrInfo);
                        item.put("Cnt", 1);
                        item.put("Anno", json.getInteger("publicdate").toString().concat("_").concat(json.getString("anno")));
                        item.put("PublishDate", json.getInteger("publicdate").toString());
                        item.put("SortDate", json.getInteger("publicdate"));
                        item.put("OriginId", json.getString("id"));

                        infoMap.put(key, item);
                    }
                }
            }

            Set<String> sqrSet = infoMap.keySet();
            for (String str : sqrSet){
                JSONObject jsonObject = infoMap.get(str);
                jsonObject.put("Id", RiskAnalysisUtil.ecodeByMD5(keyNo.concat(str).concat("_sqr")));
                array.add(jsonObject);
            }

            // 编辑时间和金额
            Iterator<Object> iterator = array.iterator();
            while (iterator.hasNext()){
                JSONArray annoArr = new JSONArray();
                JSONObject jsonObject = (JSONObject)iterator.next();
                // 日期：内部排序；外部排序取最新时间
                String[] annos = jsonObject.getString("Anno").split(",");
                List<String> annoList = new LinkedList<>();
                for (String str : annos){
                    annoList.add(str);
                }
                Collections.sort(annoList,((o1, o2) ->{
                    return o2.compareTo(o1);
                }));
                String anno = "";
                for (String str : annoList){
                    anno = anno.concat(",").concat(str.split("_")[1]);
                    JSONObject tmpJson = new JSONObject();
                    tmpJson.put("A", str.split("_")[1]);
                    tmpJson.put("B", Long.parseLong(str.split("_")[0]));
                    annoArr.add(tmpJson);
                }
                anno = anno.length() > 0 ? anno.substring(1) : anno;

                jsonObject.put("Anno", anno);

                // 日期：内部排序；外部排序取最新时间
                String[] dates = jsonObject.getString("PublishDate").split(",");
                List<String> dateList = new LinkedList<>();
                for (String str : dates){
                    dateList.add(str);
                }
                Collections.sort(dateList,((o1, o2) ->{
                    return o2.compareTo(o1);
                }));
                String date = "";
                for (String str : dateList){
                    date = date.concat(",").concat(str);
                }
                date = date.length() > 0 ? date.substring(1) : date;

                jsonObject.put("PublishDate", date);

                jsonObject.put("AnnoArr", annoArr);

                result.add(jsonObject);
            }
        }


        return array.toString();
    }

    public static void main(String[] args) {
        GetSxListSqr aa = new GetSxListSqr();
        List<String> infoList = JSON.parseArray("[\"{\\\"keyno\\\":\\\"00009d62d6ec2ffdf0d05ff2b4534d65\\\",\\\"id\\\":\\\"4c43e2ab7fcf5dc6f228988657b20ffa2\\\",\\\"anno\\\":\\\"（2017）鄂0804执357号\\\",\\\"publicdate\\\":\\\"1513094400\\\",\\\"subkeyno\\\":\\\"\\\",\\\"subname\\\":\\\"杨怀中\\\"}\"]", String.class);
        System.out.println(aa.evaluate("004b831ab1e5178f9638e62ff25998fd", infoList));
    }
}
