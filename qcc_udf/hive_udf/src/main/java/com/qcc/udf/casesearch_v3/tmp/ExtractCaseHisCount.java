package com.qcc.udf.casesearch_v3.tmp;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.output.*;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022年07月20日 9:38
 */
public class ExtractCaseHisCount extends UDF {

    public static void main(String[] args) {
        String json = "[{\"AmtInfo\":{\"139a68e3155ac4aea05ef720ebe28c0c\":{\"Amt\":\"30000.00\",\"IsValid\":\"1\",\"Type\":\"案件金额\"},\"p7dab314b227cf59edbf2207def6ea42\":{\"Amt\":\"30275\",\"IsValid\":\"0\",\"Type\":\"执行标的\"}},\"AnNoList\":\"（2018）鄂2822民初1075号,（2020）鄂2822执401号\",\"AnnoCnt\":2,\"CaseCnt\":2,\"CaseName\":\"刘小艳与刘光敏,建始县花果坪光敏购物广场,李玉安民间借贷纠纷的案件\",\"CaseNameClean\":\"刘**与刘光敏,建始县花果坪光敏购物广场,李**民间借贷纠纷的案件\",\"CaseReason\":\"民间借贷纠纷\",\"CaseRole\":\"[{\\\"D\\\":\\\"一审原告,首次执行申请执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2,\\\"P\\\":\\\"刘小艳\\\",\\\"R\\\":\\\"原告\\\",\\\"RL\\\":[{\\\"LR\\\":\\\"3\\\",\\\"R\\\":\\\"原告\\\",\\\"T\\\":\\\"一审\\\"},{\\\"R\\\":\\\"申请执行人\\\",\\\"T\\\":\\\"首次执行\\\"}]},{\\\"D\\\":\\\"一审被告,首次执行被执行人\\\",\\\"N\\\":\\\"p7dab314b227cf59edbf2207def6ea42\\\",\\\"O\\\":2,\\\"P\\\":\\\"刘光敏\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"LR\\\":\\\"4\\\",\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"},{\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"首次执行\\\"}]},{\\\"D\\\":\\\"一审被告\\\",\\\"N\\\":\\\"139a68e3155ac4aea05ef720ebe28c0c\\\",\\\"O\\\":0,\\\"P\\\":\\\"建始县花果坪光敏购物广场\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"LR\\\":\\\"11\\\",\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"}]},{\\\"D\\\":\\\"一审被告,首次执行被执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2,\\\"P\\\":\\\"李玉安\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"LR\\\":\\\"4\\\",\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"},{\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"首次执行\\\"}]}]\",\"CaseType\":\"执行案件,民事案件\",\"CfdfCnt\":0,\"CfgsCnt\":0,\"CfxyCnt\":0,\"CompanyKeywords\":\"139a68e3155ac4aea05ef720ebe28c0c,p7dab314b227cf59edbf2207def6ea42,刘光敏,刘小艳,建始县花果坪光敏购物广场,李玉安\",\"CourtList\":\"湖北省恩施土家族苗族自治州建始县人民法院\",\"EarliestDate\":1524672000,\"EarliestDateType\":\"民事一审|立案日期\",\"FyggCnt\":0,\"GqdjCnt\":0,\"GroupId\":\"dfc5595f370d7b5c798ab2d1fcb6bf7e\",\"HbcfCnt\":0,\"Id\":\"201ac528b6c12d9425ed4274cf900e1b\",\"InfoList\":[{\"AnNo\":\"（2018）鄂2822民初1075号\",\"CaseList\":[{\"Amt\":\"30000.00\",\"CaseType\":\"民事判决书\",\"DocType\":\"判决日期\",\"Id\":\"e3aa869f9e30628239e2a866ed0f4eeb0\",\"IsValid\":1,\"JudgeDate\":1531238400,\"Result\":\"一、被告刘光敏、李玉安于本判决生效之日起十日内偿还原告刘小艳借款本金30000.00元；  二、被告刘光敏、李玉安以10000.00元为本金自2015年5月1日起按照12%的年利率支付利息至该笔借款还清之日止；以20000.00为本金自2016年1月24日起按照12%的年利率支付利息至该笔借款还清之日止。\",\"ResultType\":\"判决结果\",\"ShieldCaseFlag\":0}],\"CaseReason\":\"民间借贷纠纷\",\"CaseType\":\"民事案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"湖北省恩施土家族苗族自治州建始县人民法院\",\"Defendant\":[{\"KeyNo\":\"p7dab314b227cf59edbf2207def6ea42\",\"LR\":\"4\",\"Name\":\"刘光敏\",\"Org\":2,\"Role\":\"被告\"},{\"KeyNo\":\"139a68e3155ac4aea05ef720ebe28c0c\",\"LR\":\"11\",\"Name\":\"建始县花果坪光敏购物广场\",\"Org\":0,\"Role\":\"被告\"},{\"KeyNo\":\"\",\"LR\":\"4\",\"Name\":\"李玉安\",\"Org\":-2,\"Role\":\"被告\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1531238400,\"LianList\":[{\"Id\":\"3c958a3d7f1f734208188e57efb4277d\",\"IsValid\":1,\"LianDate\":1524672000,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"刘小艳\",\"Org\":-2},{\"KeyNo\":\"\",\"Name\":\"李玉安\",\"Org\":-2},{\"KeyNo\":\"p7dab314b227cf59edbf2207def6ea42\",\"Name\":\"刘光敏\",\"Org\":2},{\"KeyNo\":\"139a68e3155ac4aea05ef720ebe28c0c\",\"Name\":\"建始县花果坪光敏购物广场\",\"Org\":0}]}],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"\",\"LR\":\"3\",\"LawFirmList\":[{\"LY\":[{\"N\":\"\",\"P\":\"樊启立\",\"R\":\"委托诉讼代理人\"}],\"N\":\"\",\"O\":-2,\"P\":\"\"}],\"Name\":\"刘小艳\",\"Org\":-2,\"Role\":\"原告\"}],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"民事一审\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]},{\"AnNo\":\"（2020）鄂2822执401号\",\"CaseList\":[{\"Amt\":\"\",\"CaseType\":\"执行裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"3e3a976a1e21c021a1d3ea5cbd8da17e0\",\"IsValid\":1,\"JudgeDate\":1592496000,\"Result\":\"终结本院（2020）鄂2822执401号案件的执行。\",\"ResultType\":\"裁定结果\",\"ShieldCaseFlag\":0}],\"CaseReason\":\"借款合同纠纷案件执行\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"湖北省恩施土家族苗族自治州建始县人民法院\",\"Defendant\":[{\"KeyNo\":\"p7dab314b227cf59edbf2207def6ea42\",\"Name\":\"刘光敏\",\"Org\":2,\"Role\":\"被执行人\"},{\"KeyNo\":\"\",\"Name\":\"李玉安\",\"Org\":-2,\"Role\":\"被执行人\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1592496000,\"LianList\":[{\"Id\":\"e6fc45c0ebf3327c4bb4a880df53b065\",\"IsValid\":1,\"LianDate\":1586793600,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"刘小艳\",\"Org\":-2},{\"KeyNo\":\"\",\"Name\":\"李玉安\",\"Org\":-2},{\"KeyNo\":\"p7dab314b227cf59edbf2207def6ea42\",\"Name\":\"刘光敏\",\"Org\":2}]}],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"\",\"Name\":\"刘小艳\",\"Org\":-2,\"Role\":\"申请执行人\"}],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"首次执行\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[{\"Biaodi\":\"30275\",\"Id\":\"2d4bbe9e4a99c6296e374618fa3d395c1\",\"IsValid\":0,\"LianDate\":1586793600,\"NameAndKeyNo\":[{\"KeyNo\":\"p7dab314b227cf59edbf2207def6ea42\",\"Name\":\"刘光敏\",\"Org\":2}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"刘小艳\",\"Org\":-2}]},{\"Biaodi\":\"30275\",\"Id\":\"9e9bed707298f7dbb2b6241fd91810c41\",\"IsValid\":0,\"LianDate\":1586793600,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"李玉安\",\"Org\":-2}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"刘小艳\",\"Org\":-2}]}]}],\"KtggCnt\":0,\"LastestDate\":1592496000,\"LastestDateType\":\"首次执行|裁定日期\",\"LatestTrialRound\":\"首次执行\",\"LawyerIds\":\",7e44d2b28371af364a2d6a459418961c\",\"LianCnt\":2,\"PcczCnt\":0,\"ProcuratorateList\":\"\",\"Province\":\"HUB\",\"SdggCnt\":0,\"Source\":\"OT\",\"SxCnt\":0,\"Tags\":\"2,4,12\",\"Type\":1,\"XgCnt\":0,\"XjpgCnt\":0,\"XzcfCnt\":0,\"ZbCnt\":0,\"ZxCnt\":2}]";
        System.out.println(evaluate(json));
    }

    public static String evaluate(String json) {
        List<LawSuitV3OutputEntity> outList = JSON.parseArray(json, LawSuitV3OutputEntity.class);
        List<Map<String, Object>> outMap = new ArrayList<>();
        if (outList != null) {
            for (LawSuitV3OutputEntity outputEntity : outList) {
                Map<String, Integer> result = new HashMap<>();
                List<InfoListEntity> infoList = outputEntity.getInfoList();
                for (InfoListEntity info : infoList) {
                    for (CaseListEntity item : info.getCaseList()) {
                        result.put("1" + item.getId(), item.getIsValid());
                    }
                    for (XGListEntity item : info.getXgList()) {
                        result.put("2" + item.getId(), item.getIsValid());
                    }
                    for (ZXListEntity item : info.getZxList()) {
                        result.put("3" + item.getId(), item.getIsValid());
                    }
                    for (SXListEntity item : info.getSxList()) {
                        result.put("4" + item.getId(), item.getIsValid());
                    }
                    for (XGListEntity item : info.getXgList()) {
                        result.put("5" + item.getId(), item.getIsValid());
                    }
                    for (KtggListEntity item : info.getKtggList()) {
                        result.put("6" + item.getId(), item.getIsValid());
                    }
                    for (LianListEntity item : info.getLianList()) {
                        result.put("7" + item.getId(), item.getIsValid());
                    }
                    for (SFPMListEntity item : info.getSfpmList()) {
                        result.put("8" + item.getId(), item.getIsValid());
                    }
                    for (ZbListEntity item : info.getZbList()) {
                        result.put("9" + item.getId(), item.getIsValid());
                    }
                    for (GqdjListEntity item : info.getGqdjList()) {
                        result.put("10" + item.getId(), item.getIsValid());
                    }
                    for (XZCJListEntity item : info.getXzcjList()) {
                        result.put("11" + item.getId(), item.getIsValid());
                    }
                    for (SQTJListEntity item : info.getSqtjList()) {
                        result.put("12" + item.getId(), item.getIsValid());
                    }
                    for (XDPGJGListEntity item : info.getXdpgjgList()) {
                        result.put("13" + item.getId(), item.getIsValid());
                    }
                    for (FyggListEntity item : info.getFyggList()) {
                        result.put("14" + item.getId(), item.getIsValid());
                    }
                    for (PcczListEntity item : info.getPcczList()) {
                        result.put("15" + item.getId(), item.getIsValid());
                    }
                    for (SdggListEntity item : info.getSdggList()) {
                        result.put("16" + item.getId(), item.getIsValid());
                    }
                }
                String id = outputEntity.getId();
                int inValidCount = Long.valueOf(result.values().stream().filter(x -> x.equals(0)).count()).intValue();
                int sumCount = result.size();
                Map<String, Object> out = new HashMap<>();
                out.put("id", id);
                out.put("count", sumCount);
                out.put("inValidCount", inValidCount);
                outMap.add(out);
            }
        }
        return JSON.toJSONString(outMap);
    }
}
