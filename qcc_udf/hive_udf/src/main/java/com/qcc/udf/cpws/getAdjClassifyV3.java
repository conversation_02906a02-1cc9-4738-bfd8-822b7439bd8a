package com.qcc.udf.cpws;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.LinkedHashSet;
import java.util.Set;

public class getAdjClassifyV3 extends UDF {
    public String evaluate(String caseName, String result, String caseNo) {
        String code = "";

        caseNo = caseNo == null ? "" : caseNo;
        caseName = caseName == null ? "" : caseName;
        result = result == null ? "" : CommonUtil.full2Half(result);

        Set<String> codeSet = new LinkedHashSet<>();
        if (result.contains("冻结") || result.contains("查封") || result.contains("扣押") ||
                result.contains("提取") || result.contains("扣留") || result.contains("保全") || caseName.contains("财产保全")){
            codeSet.add("保全");
        }
        if (result.contains("解除") && (result.contains("冻结") || result.contains("查封") || result.contains("扣押") ||
                result.contains("提取") || result.contains("扣留") || result.contains("保全"))){
            codeSet.add("解除保全");
        }
        if (result.contains("驳回") && result.contains("起诉")){
            codeSet.add("驳回起诉");
        }
        if (result.contains("驳回") && result.contains("上诉")){
            codeSet.add("驳回上诉");
        }
        if (result.contains("驳回") && result.contains("的申请")){
            codeSet.add("驳回申请");
        }
        if (result.contains("驳回") && result.contains("再审申请")){
            codeSet.add("驳回再审");
        }
        if (result.contains("驳回") && result.contains("复议")){
            codeSet.add("驳回复议");
        }
        if ((caseNo.contains("初") || caseNo.contains("受")) && result.contains("不予受理")){
            codeSet.add("不予受理");
        }
        if ((caseNo.contains("监") || caseNo.contains("终") || caseNo.contains("再")) && ((result.contains("撤销") && result.contains("裁定")) || result.contains("本院再审"))){
            codeSet.add("撤销裁定,本院再审");
        }
        if ((caseNo.contains("监") || caseNo.contains("终") || caseNo.contains("再")) && result.contains("撤销") && result.contains("指令") && (result.contains("重审") || result.contains("审理")) ){
            codeSet.add("撤销裁定,指令审理");
        }
        if ((caseNo.contains("监") || caseNo.contains("终") || caseNo.contains("再")) && result.contains("撤销") && result.contains("判决")){
            codeSet.add("撤销原判");
        }
        if ((caseNo.contains("监") || caseNo.contains("终") || caseNo.contains("再")) && result.contains("撤销") && result.contains("判决")){
            codeSet.add("撤销原判");
        }
        if ((caseNo.contains("监") || caseNo.contains("终") || caseNo.contains("再")) && result.contains("撤销") && result.contains("判决") && result.contains("驳回") && result.contains("起诉")){
            codeSet.add("撤销原判,驳回起诉");
        }
        if ((caseNo.contains("监") || caseNo.contains("终") || caseNo.contains("再")) && result.contains("撤销") && result.contains("判决") && result.contains("发回") && result.contains("重审")){
            codeSet.add("撤销原判,发回重审");
        }
        if ((caseNo.contains("监") || caseNo.contains("申") || caseNo.contains("再")) && result.contains("提审")){
            codeSet.add("提审");
        }
        if (result.contains("撤销") && (result.contains("仲裁") || result.contains("裁决")) ){
            codeSet.add("撤销仲裁");
        }
        if ((caseNo.contains("初") || caseNo.contains("管")) && (((result.contains("管辖权") && result.contains("异议")) || caseName.contains("管辖")))){
            codeSet.add("管辖权异议");
        }
        if (caseNo.contains("初") && (result.contains("处理") || result.contains("管辖") || result.contains("审理")) && result.contains("本案")){
            codeSet.add("移送管辖");
        }
        if ((caseNo.contains("初") || caseNo.contains("管")) && result.contains("驳回") && result.contains("管辖权") && result.contains("异议")){
            codeSet.add("驳回管辖权异议");
        }
        if (result.contains("中止诉讼") || result.contains("中止审理")){
            codeSet.add("中止诉讼");
        }
        if (result.contains("恢复") && (result.contains("审理") || result.contains("诉讼"))){
            codeSet.add("恢复诉讼");
        }
        if (result.contains("终结诉讼")){
            codeSet.add("终结诉讼");
        }
        if (result.contains("终结撤销")){
            codeSet.add("终结撤销");
        }
        if (result.contains("终结审查")){
            codeSet.add("终结审查");
        }
        if (result.contains("中止") && result.contains("执行")){
            codeSet.add("中止执行");
        }
        if (result.contains("终结") && result.contains("执行")){
            codeSet.add("终结执行");
        }
        if ((caseNo.contains("申") || caseNo.contains("提")) && result.contains("准许") && result.contains("撤回") && result.contains("再审申请")){
            codeSet.add("准许撤回再审申请");
        }
        if ((caseNo.contains("初")  || caseNo.contains("终") || caseNo.contains("特") ) && result.contains("准") && ((result.contains("撤回") && (result.contains("起诉") || result.contains("上诉")))) || result.contains("撤诉")){
            codeSet.add("准许撤诉");
        }
        if ((caseNo.contains("初")  || caseNo.contains("终") || caseNo.contains("特")) &&
                result.contains("本案") && (result.contains("按")  || result.contains("裁定") || result.contains("作")) &&
                ((result.contains("撤回") && (result.contains("起诉") || result.contains("上诉"))) || (result.contains("撤诉")) || (result.contains("自动放弃")) || (result.contains("自动撤回")))
                ){
            codeSet.add("按撤诉处理");
        }
        if (result.contains("应为") || result.contains("补正") || result.contains("更正") || caseName.contains("笔误")){
            codeSet.add("补正");
        }
        if (caseNo.contains("督") && result.contains("支付令")){
            codeSet.add("支付令");
        }
        if (caseNo.contains("督") && result.contains("终结") && result.contains("督促程序")){
            codeSet.add("督促程序终结");
        }
        if (caseNo.contains("保令")){
            codeSet.add("人身保护令");
        }
        if (caseNo.contains("破") && (result.contains("破产程序") || caseName.contains("破产") || caseName.contains("破产清算"))){
            codeSet.add("破产程序");
        }
        if (caseNo.contains("催") && (((result.contains("终止") || result.contains("破产")) && result.contains("公示催告程序")) || caseName.contains("公示催告"))){
            codeSet.add("公示催告");
        }
        if ((result.contains("并入") && (result.contains("案件审理") || result.contains("一并裁判") || result.contains("审理"))) || (result.contains("并案审理"))){
            codeSet.add("并案裁定");
        }
        if ((result.contains("转入") || result.contains("转为") || result.contains("转") || result.contains("适用")) && result.contains("普通程序")){
            codeSet.add("适用普通程序");
        }
        if ((result.contains("转入") || result.contains("转为") || result.contains("转") || result.contains("适用")) && result.contains("简易程序")){
            codeSet.add("适用简易程序");
        }
        if ((caseNo.contains("监") || caseNo.contains("申")) && result.contains("再审")){
            codeSet.add("再审裁定");
        }
        if (caseNo.contains("特") && (result.contains("拍卖") || result.contains("变卖")) && result.contains("所得") && result.contains("费")){
            codeSet.add("拍卖执行");
        }
        if ((caseNo.contains("调") || caseNo.contains("特")) && result.contains("调解") && result.contains("有效")) {
            codeSet.add("调解裁定");
        }

        if (codeSet.size() > 0){
            code = String.join(";", codeSet);
        }else{
            code = "其他";
        }

        return code;
    }

    public static void main(String[] args) {
        System.out.println(new getAdjClassifyV3().evaluate("", "冻结", ""));
    }
}
