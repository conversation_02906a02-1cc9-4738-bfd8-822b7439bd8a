package com.qcc.udf.temp;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.output.LawSuitV3OutputEntity;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 从文本中统计案号
 *
 * @Auther: wuql
 * @Date: 2020/11/17 10:00
 * @Description:
 */
public class CaseGetOthernamesUDF extends UDF {

    public static String evaluate(String caseRole, String companyKeywords) {
        // 获取other
        JSONObject jsonObject = getRoleName(caseRole, companyKeywords);
        return jsonObject.getString("O");
    }

    public static JSONObject getRoleName(String caseRole, String companyKeywords) {
        JSONObject result = new JSONObject();
        result.put("P", "");
        result.put("D", "");
        result.put("O", "");
        result.put("PE", new JSONArray());
        result.put("DE", new JSONArray());
        if (StringUtils.isEmpty(caseRole)) {
            return result;
        }

        Iterator<Object> it = JSONArray.parseArray(caseRole).iterator();
        Set<String> pSet = new LinkedHashSet<>();
        Set<String> dSet = new LinkedHashSet<>();
        Set<String> oSet = new LinkedHashSet<>();
        JSONArray peArray = new JSONArray();
        JSONArray deArray = new JSONArray();
        while (it.hasNext()) {
            JSONObject jsonObject = (JSONObject) it.next();
            String r = CommonUtil.full2Half(jsonObject.getString("R"));

            Set<String> beigaoSet = new LinkedHashSet<>();
            beigaoSet.add("被执行人");
            beigaoSet.add("被告");
            beigaoSet.add("被申请人");
            beigaoSet.add("被申请执行人");
            beigaoSet.add("原审被告");
            beigaoSet.add("被上诉人(原审被告)");
            beigaoSet.add("被上诉人(原审被告)");
            beigaoSet.add("上诉人(原审被告)");
            beigaoSet.add("被告(反诉原告)");
            beigaoSet.add("被告人");
            beigaoSet.add("上诉人(一审被告)");
            beigaoSet.add("被上诉人(一审被告)");
            beigaoSet.add("被上诉人");
            beigaoSet.add("上诉人(原审被告反诉原告)");
            beigaoSet.add("被告二");
            beigaoSet.add("被告一");
            beigaoSet.add("原告(被告)");
            beigaoSet.add("被申请人(一审被告二审被上诉人)");
            beigaoSet.add("被申请人(原审被告)");
            beigaoSet.add("再审申请人(一审被告二审上诉人)");
            beigaoSet.add("再审申请人(原审被告)");
            beigaoSet.add("被申请人(仲裁被申请人)");
            beigaoSet.add("被申请人(原被执行人)");
            beigaoSet.add("再审被申请人");
            beigaoSet.add("上诉人(原审被告原审原告)");
            beigaoSet.add("原审被告单位");
            beigaoSet.add("被起诉人");
            beigaoSet.add("被告单位");

            Set<String> yuangaoSet = new LinkedHashSet<>();
            yuangaoSet.add("申请执行人");
            yuangaoSet.add("原告");
            yuangaoSet.add("申请人");
            yuangaoSet.add("被上诉人(原审原告)");
            yuangaoSet.add("复议申请人");
            yuangaoSet.add("上诉人(原审原告)");
            yuangaoSet.add("原告(反诉被告)");
            yuangaoSet.add("被上诉人(一审原告)");
            yuangaoSet.add("上诉人(一审原告)");
            yuangaoSet.add("上诉人");
            yuangaoSet.add("(一审原告)");
            yuangaoSet.add("(原审原告反诉被告)");
            yuangaoSet.add("原审原告");
            yuangaoSet.add("再审申请人");
            yuangaoSet.add("被告(原告)");
            yuangaoSet.add("被申请人(原审原告)");
            yuangaoSet.add("附带民事诉讼原告人");
            yuangaoSet.add("复议申请人(原申请执行人)");
            yuangaoSet.add("再审申请人(一审原告二审上诉人)");
            yuangaoSet.add("再审申请人(原审原告)");
            yuangaoSet.add("申请再审人(一审原告二审上诉人)");
            yuangaoSet.add("二审上诉人");
            yuangaoSet.add("原告人");
            yuangaoSet.add("附带民事诉讼原告");
            yuangaoSet.add("上诉人(原审原告原审被告)");
            yuangaoSet.add("起诉人");
            yuangaoSet.add("申请人(仲裁申请人)");
            yuangaoSet.add("赔偿请求人");
            yuangaoSet.add("被上诉人(原审原告反诉被告)");
            yuangaoSet.add("申请追加人(申请执行人)");

            if (yuangaoSet.contains(r)) {
                if (StringUtils.isNotEmpty(jsonObject.getString("P"))) {
                    pSet.add(CommonUtil.full2Half(jsonObject.getString("P")));
                }
                if (StringUtils.isNotEmpty(jsonObject.getString("N"))) {
                    pSet.add(jsonObject.getString("N"));
                }
                peArray.add(jsonObject);
            } else if (beigaoSet.contains(r)) {
                if (StringUtils.isNotEmpty(jsonObject.getString("P"))) {
                    dSet.add(CommonUtil.full2Half(jsonObject.getString("P")));
                }
                if (StringUtils.isNotEmpty(jsonObject.getString("N"))) {
                    dSet.add(jsonObject.getString("N"));
                }
                deArray.add(jsonObject);
            } else {
                if (StringUtils.isNotEmpty(jsonObject.getString("P"))) {
                    oSet.add(CommonUtil.full2Half(jsonObject.getString("P")));
                }
                if (StringUtils.isNotEmpty(jsonObject.getString("N"))) {
                    oSet.add(jsonObject.getString("N"));
                }
            }
        }

        //companyKeywords比原被告其他当事人多的部分放入其他当事人
        if (StringUtils.isNotEmpty(companyKeywords)) {
            String[] strings = companyKeywords.split(",");
            for (String item : strings) {
                if (StringUtils.isEmpty(item)) {
                    continue;
                }
                if (pSet.contains(item) || dSet.contains(item) || oSet.contains(item)) {
                    continue;
                }
                oSet.add(item);
            }
        }

        result.put("P", String.join(",", pSet));
        result.put("D", String.join(",", dSet));
        result.put("O", String.join(",", oSet));
        result.put("PE", peArray);
        result.put("DE", deArray);

        return result;
    }


    public static void main(String[] args) {
        String caseRole = "[{\"D\":\"一审原告\",\"N\":\"1f2e66ba3af7141867c20ca4d53d29d2\",\"O\":0,\"P\":\"中国工商银行股份有限公司乐清支行\",\"R\":\"原告\",\"RL\":[{\"JR\":\"13\",\"LR\":\"8\",\"R\":\"原告\",\"T\":\"一审\"}]},{\"D\":\"一审被告,首次执行被执行人\",\"N\":\"p02815a9a526f73b7fefab452697ad74\",\"O\":2,\"P\":\"张微微\",\"R\":\"被告\",\"RL\":[{\"JR\":\"13\",\"LR\":\"9\",\"R\":\"被告\",\"T\":\"一审\"},{\"JR\":\"\",\"R\":\"被执行人\",\"T\":\"首次执行\"}]},{\"D\":\"一审被告,首次执行被执行人\",\"N\":\"9313e86d303d59b10e784c78e3bfc1f9\",\"O\":0,\"P\":\"新华电器集团有限公司\",\"R\":\"被告\",\"RL\":[{\"JR\":\"13\",\"LR\":\"9\",\"R\":\"被告\",\"T\":\"一审\"},{\"JR\":\"\",\"R\":\"被执行人\",\"T\":\"首次执行\"}]},{\"D\":\"一审被告,首次执行被执行人\",\"N\":\"263edbfd227d0413af387147fdaec1b6\",\"O\":0,\"P\":\"浙江精泰科技有限公司\",\"R\":\"被告\",\"RL\":[{\"JR\":\"13\",\"LR\":\"9\",\"R\":\"被告\",\"T\":\"一审\"},{\"JR\":\"\",\"R\":\"被执行人\",\"T\":\"首次执行\"}]},{\"D\":\"一审被告,首次执行被执行人\",\"N\":\"cbf0d77d9359b5746870a8f7dc878b77\",\"O\":0,\"P\":\"精益电器集团有限公司\",\"R\":\"被告\",\"RL\":[{\"JR\":\"13\",\"LR\":\"9\",\"R\":\"被告\",\"T\":\"一审\"},{\"JR\":\"\",\"R\":\"被执行人\",\"T\":\"首次执行\"}]},{\"D\":\"一审被告,首次执行被执行人\",\"N\":\"a4c57655104e8ac2548e4988132eca37\",\"O\":0,\"P\":\"耀华电器集团有限公司\",\"R\":\"被告\",\"RL\":[{\"JR\":\"13\",\"LR\":\"9\",\"R\":\"被告\",\"T\":\"一审\"},{\"JR\":\"\",\"R\":\"被执行人\",\"T\":\"首次执行\"}]},{\"D\":\"一审被告,首次执行被执行人\",\"N\":\"p6beca7fd79657b6e93873e8c1091343\",\"O\":2,\"P\":\"陈冬青\",\"R\":\"被告\",\"RL\":[{\"JR\":\"13\",\"LR\":\"9\",\"R\":\"被告\",\"T\":\"一审\"},{\"JR\":\"\",\"R\":\"被执行人\",\"T\":\"首次执行\"}]}]";
        String companyKeywords = "1f2e66ba3af7141867c20ca4d53d29d2,263edbfd227d0413af387147fdaec1b6,9313e86d303d59b10e784c78e3bfc1f9,a4c57655104e8ac2548e4988132eca37,cbf0d77d9359b5746870a8f7dc878b77,p02815a9a526f73b7fefab452697ad74,p6beca7fd79657b6e93873e8c1091343,中国工商银行股份有限公司乐清支行,乐清市精泰开关配件厂,乐清市精泰科技电气有限公司,张微微,新华电器集团有限公司,浙江新华电器集团有限责任公司,浙江精泰科技有限公司,浙江精益电器集团有限公司,浙江耀华集团有限公司,温州伟业电气有限公司,温州市成功机电物资有限公司柳市分公司,精益电器集团有限公司,耀华电器集团有限公司,陈冬青";
                String evaluate = evaluate(caseRole, companyKeywords);
        System.out.println(evaluate);
    }
}
