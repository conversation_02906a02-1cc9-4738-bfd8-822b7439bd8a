package com.qcc.udf.casesearch_v3.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.base.Objects;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import com.qcc.udf.casesearch_v3.CaseDateConvertUDF;
import com.qcc.udf.casesearch_v3.CaseNoCleanUDF;
import com.qcc.udf.casesearch_v3.entity.LawSuitV3Entity;
import com.qcc.udf.casesearch_v3.entity.input.*;
import com.qcc.udf.casesearch_v3.entity.output.*;
import com.qcc.udf.casesearch_v3.entity.tmp.CaseNoEntity;
import com.qcc.udf.casesearch_v3.enums.*;
import com.qcc.udf.court_notice.anUtils.MD5Util;
import com.qcc.udf.cpws.ExtractCaseTrialRoundUDF;
import com.qcc.udf.cpws.ExtractCaseTypeUDF;
import com.qcc.udf.cpws.ExtractCourtNameFromCaseNoUDF;
import com.qcc.udf.halfToFull;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Auther: zhangqiang
 * @Date: 2020/11/11 17:54
 * @Description:V3版公共方法
 */
public class CommonV3Util {
    /**
     * 排序
     * <p>
     * 减少因为乱序导致的分组差异
     *
     * @param input
     */
    public static void sort(LawSuitV3Entity input) {
        if (input != null) {
            if (input.getSxList() != null) {
                input.getSxList().sort(Comparator.comparing(SXEntity::getId));
            }
            if (input.getZxList() != null) {
                input.getZxList().sort(Comparator.comparing(ZXEntity::getId));
            }
            if (input.getXgList() != null) {
                input.getXgList().sort(Comparator.comparing(XGEntity::getId));
            }
            if (input.getCpwsList() != null) {
                input.getCpwsList().sort(Comparator.comparing(CPWSEntity::getId));
            }
            if (input.getPcczList() != null) {
                input.getPcczList().sort(Comparator.comparing(PCCZEntity::getId));
            }
            if (input.getZbList() != null) {
                input.getZbList().sort(Comparator.comparing(ZBEntity::getId));
            }
            if (input.getXjpgList() != null) {
                input.getXjpgList().sort(Comparator.comparing(XJPGEntity::getId));
            }
            if (input.getGqdjList() != null) {
                input.getGqdjList().sort(Comparator.comparing(GQDJEntity::getId));
            }
            if (input.getSdggList() != null) {
                input.getSdggList().sort(Comparator.comparing(SDGGEntity::getId));
            }
            if (input.getKtggList() != null) {
                input.getKtggList().sort(Comparator.comparing(KTGGEntity::getId));
            }
            if (input.getLianList() != null) {
                input.getLianList().sort(Comparator.comparing(LAEntity::getId));
            }
            if (input.getHbcfList() != null) {
                input.getHbcfList().sort(Comparator.comparing(HBCFEntity::getId));
            }
            if (input.getXzcfList() != null) {
                input.getXzcfList().sort(Comparator.comparing(XZCFEntity::getId));
            }
            if (input.getSfpmList() != null) {
                input.getSfpmList().sort(Comparator.comparing(SFPMEntity::getId));
            }
            if (input.getSqtjList() != null) {
                input.getSqtjList().sort(Comparator.comparing(SQTJEntity::getId));
            }
            if (input.getXzcjList() != null) {
                input.getXzcjList().sort(Comparator.comparing(XZCJEntity::getId));
            }
            if (input.getXdpgjgList() != null) {
                input.getXdpgjgList().sort(Comparator.comparing(XDPGJGEntity::getId));
            }
            if (input.getXsggList() != null) {
                input.getXsggList().sort(Comparator.comparing(XSGGEntity::getId));
            }

        }
    }


    /**
     * 老版拆分规则
     *
     * @param input
     * @return
     */
    public static List<LawSuitV3Entity> split_v0(LawSuitV3Entity input) throws InvocationTargetException, IllegalAccessException {
        //入参所有维度
        List<BaseCaseEntity> allList = buildInputList(input);
        //按照省份分组
        Map<String, List<BaseCaseEntity>> provinceGroupMap = allList.stream()
                .collect(Collectors.groupingBy(data -> getString(data.getBaseProvinceCode())));

        //按照当事人分组
        List<List<BaseCaseEntity>> groupList = new ArrayList<>();

        provinceGroupMap.forEach((k, v) -> {
            if (Strings.isNullOrEmpty(k)) {
                //没有省份的数据丢弃
            } else {
                groupList.add(v);
            }
        });
        List<LawSuitV3Entity> outList = new ArrayList<>();
        //分组输出数据
        for (List<BaseCaseEntity> group : groupList) {
            LawSuitV3Entity out = new LawSuitV3Entity();
            BeanUtils.copyProperties(out, input);

            out.setLianList(convertList(group.stream().filter(data -> CaseCategoryEnum.LA.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), LAEntity.class));
            out.setCpwsList(convertList(group.stream().filter(data -> CaseCategoryEnum.CPWS.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), CPWSEntity.class));
            out.setSxList(convertList(group.stream().filter(data -> CaseCategoryEnum.SX.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), SXEntity.class));
            out.setZxList(convertList(group.stream().filter(data -> CaseCategoryEnum.ZX.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), ZXEntity.class));
            out.setKtggList(convertList(group.stream().filter(data -> CaseCategoryEnum.KTGG.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), KTGGEntity.class));
            out.setSdggList(convertList(group.stream().filter(data -> CaseCategoryEnum.SDGG.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), SDGGEntity.class));
            out.setFyggList(convertList(group.stream().filter(data -> CaseCategoryEnum.FYGG.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), FYGGEntity.class));
            out.setPcczList(convertList(group.stream().filter(data -> CaseCategoryEnum.PCCZ.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), PCCZEntity.class));
            out.setXjpgList(convertList(group.stream().filter(data -> CaseCategoryEnum.XJPG.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), XJPGEntity.class));
            out.setZbList(convertList(group.stream().filter(data -> CaseCategoryEnum.ZB.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), ZBEntity.class));
            out.setXgList(convertList(group.stream().filter(data -> CaseCategoryEnum.XG.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), XGEntity.class));
            out.setGqdjList(convertList(group.stream().filter(data -> CaseCategoryEnum.GQDJ.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), GQDJEntity.class));
//
//            out.setHbcfList(convertList(group.stream().filter(data->CaseCategoryEnum.HBCF.equals(data.getBaseCaseCategoryEnum()))
//                    .collect(Collectors.toList()),HBCFEntity.class));
//            out.setXzcfList(convertList(group.stream().filter(data->CaseCategoryEnum.XZCF.equals(data.getBaseCaseCategoryEnum()))
//                    .collect(Collectors.toList()),XZCFEntity.class));
            out.setHbcfList(input.getHbcfList());
            out.setXzcfList(input.getXzcfList());

            out.setSfpmList(convertList(group.stream().filter(data -> CaseCategoryEnum.SFPM.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), SFPMEntity.class));
            out.setSqtjList(convertList(group.stream().filter(data -> CaseCategoryEnum.SQTJ.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), SQTJEntity.class));
            out.setXzcjList(convertList(group.stream().filter(data -> CaseCategoryEnum.XZCJ.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), XZCJEntity.class));
            out.setXdpgjgList(convertList(group.stream().filter(data -> CaseCategoryEnum.XDPGJG.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), XDPGJGEntity.class));
            outList.add(out);
        }

        return outList;
    }


    /**
     * 案件组装
     *
     * @param splitList
     */
    public static List<LawSuitV3OutputEntity> build(List<LawSuitV3Entity> splitList, String annoGourp) {
        //纯案号数据打标记
        markSimpleAnno(splitList, annoGourp);
        List<LawSuitV3OutputEntity> outList = new ArrayList<>();
        for (LawSuitV3Entity lawSuitV3Entity : splitList) {
            outList.add(clean(lawSuitV3Entity, annoGourp));
        }

        return outList;
    }

    /**
     * 标记纯案号数据
     */
    static void markSimpleAnno(List<LawSuitV3Entity> splitList, String annoGourp) {
        Set<String> annoGourpSet = Arrays.asList(annoGourp.split(",")).stream().collect(Collectors.toSet());
        Set<String> dealedAnnoSet = new HashSet<>();
        for (LawSuitV3Entity item : splitList) {
            List<BaseCaseEntity> subList = buildInputList(item);
            for (BaseCaseEntity entity : subList) {
                //只取第一个案号
                String caseNo = CaseNoCleanUDF.evaluate(getCaseNo(entity.getBaseCaseNo()));
                dealedAnnoSet.add(caseNo);
            }
        }
        //找出纯案号
        Set<String> simpleAnnoSet = new HashSet<>();
        for (String anno : annoGourpSet) {
            if (!dealedAnnoSet.contains(anno)) {
                simpleAnnoSet.add(anno);
            }
        }

        Iterator<String> iterator = simpleAnnoSet.iterator();
        while (iterator.hasNext()) {
            String next = iterator.next();
            loop1:
            for (LawSuitV3Entity item : splitList) {
                Set<String> simpleSet = item.getSimpleAnnoSet() == null ? new HashSet<>() : item.getSimpleAnnoSet();
                List<BaseCaseEntity> subList = buildInputList(item);
                for (BaseCaseEntity entity : subList) {
                    //在原始数据中标记纯案号，同一个纯案号无需在多个案件组内重复标记
                    if (entity.getBaseBeforeNoSet() != null && entity.getBaseBeforeNoSet().contains(next)) {
                        simpleSet.add(next);
                        iterator.remove();
                        item.setSimpleAnnoSet(simpleSet);
                        break loop1;
                    }
                }
            }
        }
    }

    /**
     * 組裝明细
     *
     * @param lawSuitV3Entity
     * @return
     */
    static LawSuitV3OutputEntity clean(LawSuitV3Entity lawSuitV3Entity, String annoGourp) {

        //案号组信息
        Set<String> annoGourpSet = Arrays.asList(annoGourp.split(",")).stream().collect(Collectors.toSet());

        //明细信息--InfoList
        List<BaseCaseEntity> allList = buildInputList(lawSuitV3Entity);
        //处罚类信息
        List<BaseCaseEntity> penaltyList = buildPenaltyInputList(lawSuitV3Entity);

        LawSuitV3OutputEntity outputEntity = new LawSuitV3OutputEntity();
        //按照案号分组
        Map<String, List<BaseCaseEntity>> caseNoMap = mergeByCaseNo(allList, annoGourpSet);

        //行政处罚&环保处罚不能独立成案件,需要过滤
        Set<String> deleteCaseNoSet = getNeedDeleteNos(caseNoMap.entrySet());
        deleteCaseNoSet.forEach(item -> caseNoMap.remove(item));


        //构建infoList
        Map<String, InfoListEntity> infoListMap = buildInfoList(caseNoMap);

        //添加纯案号数据
        if (CollectionUtils.isNotEmpty(lawSuitV3Entity.getSimpleAnnoSet())) {
            for (String anno : lawSuitV3Entity.getSimpleAnnoSet()) {
                if (infoListMap.get(anno) == null) {
                    InfoListEntity simpleData = buildSimpleAnnoData(anno, infoListMap);
                    if (simpleData != null) {
                        infoListMap.put(anno, simpleData);
                    }
                }
            }
        }


        List<InfoListEntity> infoList = infoListMap.values().stream().collect(Collectors.toList());
        //所有案子汇总--


        // 审理程序总数
        outputEntity.setAnnoCnt(infoListMap.size());
        // 关联裁判文书数
        outputEntity.setCaseCnt(lawSuitV3Entity.getCpwsList().size());
        // 关联被执行数
        outputEntity.setZxCnt(lawSuitV3Entity.getZxList().size());
        // 关联失信数
        outputEntity.setSxCnt(lawSuitV3Entity.getSxList().size());
        // 关联限高数
        outputEntity.setXgCnt(lawSuitV3Entity.getXgList().size());
        // 关联破产重整公告数
        outputEntity.setPcczCnt(lawSuitV3Entity.getPcczList().size());
        // 关联终本案件数
        outputEntity.setZbCnt(lawSuitV3Entity.getZbList().size());
        // 关联询价评估数
        outputEntity.setXjpgCnt(lawSuitV3Entity.getXjpgList().size());
        // 关联股权冻结数
        outputEntity.setGqdjCnt(lawSuitV3Entity.getGqdjList().size());
        // 关联送达公告数
        outputEntity.setSdggCnt(lawSuitV3Entity.getSdggList().size());
        // 关联法院公告数
        outputEntity.setFyggCnt(lawSuitV3Entity.getFyggList().size());
        // 关联开庭公告数
        outputEntity.setKtggCnt(lawSuitV3Entity.getKtggList().size());
        // 关联立案信息数
        outputEntity.setLianCnt(lawSuitV3Entity.getLianList().size());
        // 关联环保处罚数
        outputEntity.setHbcfCnt(lawSuitV3Entity.getHbcfList().size());
        // 关联行政处罚
        outputEntity.setXzcfCnt(lawSuitV3Entity.getXzcfList().size());

        // 关联司法拍卖
        outputEntity.setSfpmCnt(lawSuitV3Entity.getSfpmList().size());
        // 关联诉前调解
        outputEntity.setSqtjCnt(lawSuitV3Entity.getSqtjList().size());
        // 关联限制出境
        outputEntity.setXzcjCnt(lawSuitV3Entity.getXzcjList().size());
        // 关联选定评估机构
        outputEntity.setXdpgjgCnt(lawSuitV3Entity.getXdpgjgList().size());
        // 关联悬赏公告
        outputEntity.setXsggCnt(lawSuitV3Entity.getXsggList().size());

//        System.out.println(JSON.toJSONString(infoList));
        //infoList中的数据排序（LatestTimestamp 正排序）

        InfoListEntity firstTimeInfoEntity = null;
        if (CollectionUtils.isNotEmpty(infoList)) {
            //先从时间不为空的里取
            List<InfoListEntity> tmpList = infoList.stream()
                    .filter(data -> data.getLatestTimestamp() > 0L).collect(Collectors.toList());
            if (tmpList.size() > 0) {
                firstTimeInfoEntity = getFirstTimeInfoEntity(tmpList);
            }

            if (firstTimeInfoEntity == null) {
                //避免取到纯案件数据
                tmpList = infoList.stream()
                        .filter(data -> data.getSimpleAnnoYear() == null).collect(Collectors.toList());
                if (tmpList.size() > 0) {
                    firstTimeInfoEntity = getFirstTimeInfoEntity(tmpList);
                }
            }
        }


        Map<String, String> caseNameMap = buildCaseName(firstTimeInfoEntity);
        // 案件名称
        outputEntity.setCaseName(caseNameMap.getOrDefault("CaseName", ""));
        outputEntity.setCaseNameClean(caseNameMap.getOrDefault("CaseNameClean", ""));

        // 分组法院信息
        //GroupCourt
        // 所在省份编码
        String province = allList.stream().map(BaseCaseEntity::getBaseProvinceCode)
                .filter(data -> !Strings.isNullOrEmpty(data))
                .collect(Collectors.toSet())
                .stream().sorted()
                .collect(Collectors.joining(","));
        outputEntity.setProvince(province);


        // 案件类型
        outputEntity.setCaseType(getTags(caseNoMap.keySet()).stream().collect(Collectors.joining(",")));
        // 关联的公司或个人信息(搜索字段)
        Set<String> searchWordSet = new HashSet<>();
        for (BaseCaseEntity baseCaseEntity : allList) {
            if (CollectionUtils.isNotEmpty(baseCaseEntity.getBaseSearchWordSet())) {
                searchWordSet.addAll(baseCaseEntity.getBaseSearchWordSet());
            }

        }
        //搜索字段
        outputEntity.setCompanyKeywords(getCompanyKeywordsFromSearchWordSet(searchWordSet));

        // 相关案号
        outputEntity.setAnNoList(anno2Full(infoListMap.keySet().stream().sorted().collect(Collectors.joining(","))));
        // 从infoList中获取LastestTimeStamp最前一项的信息
        // 列表中的案由
        outputEntity.setCaseReason(firstTimeInfoEntity == null ? "" : firstTimeInfoEntity.getCaseReason());
        // 列表中的案件身份
        List<NameAndKeyNoEntity> caseRoleList = new ArrayList<>();
        caseRoleList.addAll(firstTimeInfoEntity == null ? new ArrayList<>() : firstTimeInfoEntity.getProsecutor());
        caseRoleList.addAll(firstTimeInfoEntity == null ? new ArrayList<>() : firstTimeInfoEntity.getDefendant());
        List<CaseRoleEntity> caseRole = convertNameKey2CaseRole(caseRoleList);


        // 相关法院
        outputEntity.setCourtList(infoList.stream()
                .map(InfoListEntity::getCourt)
                .filter(data -> !Strings.isNullOrEmpty(data))
                .collect(Collectors.toSet())
                .stream().sorted().collect(Collectors.joining(",")));
        // 相关检察院
        outputEntity.setProcuratorateList(infoList.stream()
                .map(InfoListEntity::getProcuratorate)
                .filter(data -> !Strings.isNullOrEmpty(data))
                .collect(Collectors.toSet())
                .stream().sorted().collect(Collectors.joining(",")));


        //审理经过
        // 所有时间节点的map集合（key-> 时间戳; value->表示当前的节点状态，审判程序 + "维度时间节点描述文案"(被执行人/失信人/限制高消费)发布日期 或 判决日期 或 裁定日期等）
        Map<Long, String> trialRoundDateNodeMap = trialRoundDateNodeMap(infoList);
        //生成最早/最晚审理经过
        Map<String, Object> extendTrialMap = extendTrialMap(trialRoundDateNodeMap);
        //前该案件最最早的数据对应时间
        outputEntity.setEarliestDate((Long) extendTrialMap.get("earliestDate"));
        //当前该案件最早的数据对应审理程序
        outputEntity.setEarliestDateType((String) extendTrialMap.get("earliestDateType"));
        outputEntity.setLastestDate((Long) extendTrialMap.get("lastestDate"));
        //当前该案件最晚的数据对应审理程序
        outputEntity.setLastestDateType((String) extendTrialMap.get("lastestDateType"));
        // 最新审理程序
        String latestTrialRound = (String) extendTrialMap.get("latestTrialRound");
        if (Strings.isNullOrEmpty(latestTrialRound)) {
            latestTrialRound = getLatestTrialRoundWithOutTime(infoList);
        }
        outputEntity.setLatestTrialRound(latestTrialRound);

        //案件金额
        Map<String, AmtInfo> amtInfo = buildAmtInfo(searchWordSet, lawSuitV3Entity.getZbList()
                , lawSuitV3Entity.getZxList(), lawSuitV3Entity.getCpwsList());
//        System.out.println(JSON.toJSONString(amtInfo, SerializerFeature.DisableCircularReferenceDetect));
        outputEntity.setAmtInfo(amtInfo);

        //纯案号数据不参与中间过程，但是参与最终排序
        for (InfoListEntity infoListEntity : infoList) {
            if (infoListEntity.getSimpleAnnoYear() != null) {
                infoListEntity.setLatestTimestamp(infoListEntity.getSimpleAnnoYear());
            }
        }
        //案件身份补充
        mergeCaseRoleAndTrialRound(caseRole, infoList);
        outputEntity.setCaseRole(JSON.toJSONString(caseRole));
        //生成其他案件标签
        outputEntity.setOtherTags(buildOtherTags(lawSuitV3Entity));

        //标签信息
        Set<Integer> tagsSet = buildCategoryTags(allList);

        //涉案环保处罚
        List<HbcfListEntity> hbcfList = convertList(editPenaltyItem(penaltyList, CaseCategoryEnum.HBCF, searchWordSet), HbcfListEntity.class);
        if (CollectionUtils.isNotEmpty(hbcfList)) {
            outputEntity.setHbcfList(hbcfList);
            tagsSet.add(CaseCategoryEnum.XZCF.getType());
        }
        //涉案行政处罚
        List<XZCFListEntity> xzcfList = convertList(editPenaltyItem(penaltyList, CaseCategoryEnum.XZCF, searchWordSet), XZCFListEntity.class);
        if (CollectionUtils.isNotEmpty(xzcfList)) {
            outputEntity.setXzcfList(xzcfList);
            tagsSet.add(CaseCategoryEnum.HBCF.getType());
        }
        outputEntity.setHbcfCnt(hbcfList.size());
        outputEntity.setXzcfCnt(xzcfList.size());


        //涉案商标/专利
        List<CPWSEntity> cpwss = lawSuitV3Entity.getCpwsList().stream().collect(Collectors.toList());
        List<TrademarkListEntity> trademarks = new ArrayList<>();
        cpwss.stream().filter(e -> e != null).filter(e -> CollectionUtils.isNotEmpty(e.getTmList())).map(CPWSEntity::getTmList).forEach(e -> {
            List<TrademarkListEntity> collect = e.stream().filter(item -> item != null).map(item -> {
                Long appDate = item.getAppDate();
                if (appDate == null) {
                    appDate = 0L;
                } else {
                    appDate = appDate / 1000;
                }
                item.setAppDate(appDate);
                TrademarkListEntity trademark = new TrademarkListEntity();
                BeanUtil.copyProperties(item, trademark, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                String nameAndKeyNo = item.getNameAndKeyNo();
                List<NameAndKeyNoEntity> nameAndKeyNos = new ArrayList<>();
                if (StringUtils.isNotBlank(nameAndKeyNo)) {
                    NameAndKeyNoEntity nameAndKeyNoEntity = JSON.parseObject(nameAndKeyNo, NameAndKeyNoEntity.class);
                    nameAndKeyNos.add(nameAndKeyNoEntity);
                }
                trademark.setNameAndKeyNo(nameAndKeyNos);
                return trademark;
            }).collect(Collectors.toList());
            trademarks.addAll(collect);
        });
        List<TrademarkListEntity> trademarkDis = trademarks.stream().distinct().collect(Collectors.toList());
        outputEntity.setSBList(trademarkDis);
        outputEntity.setSBCnt(trademarkDis.size());

        List<PatentListEntity> patents = new ArrayList<>();
        cpwss.stream().filter(e -> e != null).filter(e -> CollectionUtils.isNotEmpty(e.getPtList())).map(CPWSEntity::getPtList).forEach(e -> {
            List<PatentListEntity> collect = e.stream().filter(item -> item != null).map(item -> {
                Long applicationDate = item.getApplicationDate();
                if (applicationDate == null) {
                    applicationDate = 0L;
                } else {
                    applicationDate = applicationDate / 1000;
                }
                item.setApplicationDate(applicationDate);
                Long publicationDate = item.getPublicationDate();
                if (publicationDate == null) {
                    publicationDate = 0L;
                } else {
                    publicationDate = publicationDate / 1000;
                }
                item.setPublicationDate(publicationDate);

                PatentListEntity patent = new PatentListEntity();
                BeanUtil.copyProperties(item, patent, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                String nameAndKeyNo = item.getNameAndKeyNo();
                List<NameAndKeyNoEntity> nameAndKeyNos = Collections.EMPTY_LIST;
                if (StringUtils.isNotBlank(nameAndKeyNo)) {
                    nameAndKeyNos = JSON.parseArray(nameAndKeyNo, NameAndKeyNoEntity.class);
                }
                patent.setNameAndKeyNo(nameAndKeyNos);

                return patent;
            }).collect(Collectors.toList());
            patents.addAll(collect);
        });
        List<PatentListEntity> patentDis = patents.stream().distinct().collect(Collectors.toList());
        outputEntity.setZLList(patentDis);
        if (CollectionUtils.isEmpty(patentDis)) {
            outputEntity.setZLCnt(0);
        } else {
            outputEntity.setZLCnt(patentDis.stream().filter(e -> e.getIsUse()!= null && e.getIsUse().intValue() == 1).collect(Collectors.toList()).size());
        }

        //增量离线任务更新减少变化量

        if (CollectionUtils.isEmpty(trademarkDis)) {
            outputEntity.setSBList(null);
            outputEntity.setSBCnt(null);
        }
        if (CollectionUtils.isEmpty(patentDis)) {
            outputEntity.setZLList(null);
            outputEntity.setZLCnt(null);
        }

        String lawyerids = cpwss.stream()
                .filter(e -> e != null)
                .filter(e -> CollectionUtils.isNotEmpty(e.getLawyerList()))
                .map(CPWSEntity::getLawyerList)
                .flatMap(list -> list.stream().filter(ly -> ly != null).filter(ly -> StringUtils.isNotBlank(ly.getLawFirm()) && StringUtils.isNotBlank(ly.getName())))
                .map(ly -> MD5Util.ecodeByMD5(halfToFull.evaluate(ly.getLawFirm() + "|" + ly.getName()))).distinct().sorted().collect(Collectors.joining(","));
        //真实律师id
        String realLawIds = LawyerUtil.createCpwsLawyerIds(cpwss);
        //合并虚拟律师id和真实律师id（过渡兼容方案）
        lawyerids = LawyerUtil.mergeLawyerIds(lawyerids, realLawIds);

        outputEntity.setLawyerIds(lawyerids);
        if (StringUtils.isBlank(lawyerids)) {
            outputEntity.setLawyerIds(null);
        }

        if (CollectionUtils.isEmpty(lawSuitV3Entity.getSfpmList())) {
            outputEntity.setSfpmCnt(null);
        }
        if (CollectionUtils.isEmpty(lawSuitV3Entity.getSqtjList())) {
            outputEntity.setSqtjCnt(null);
        }
        if (CollectionUtils.isEmpty(lawSuitV3Entity.getXzcjList())) {
            outputEntity.setXzcjCnt(null);
        }
        if (CollectionUtils.isEmpty(lawSuitV3Entity.getXdpgjgList())) {
            outputEntity.setXdpgjgCnt(null);
        }
        if (CollectionUtils.isEmpty(lawSuitV3Entity.getXsggList())) {
            outputEntity.setXsggCnt(null);
        }


        //标签
        outputEntity.setTags(Joiner.on(",").skipNulls().join(tagsSet));

        outputEntity.setInfoList(infoList);

        return outputEntity;
    }

    /**
     * 编辑处罚类信息
     *
     * @param infoList
     * @param categoryEnum
     * @return
     */
    static List<BasePenaltyOutEntity> editPenaltyItem(List<BaseCaseEntity> infoList, CaseCategoryEnum categoryEnum, Set<String> searchWordSet) {
        List<BasePenaltyOutEntity> outList = new ArrayList<>();
        List<BaseCaseEntity> dealList = new ArrayList<>();
        Set<String> nameSet = new HashSet<>();
        for (String name : searchWordSet) {
            if (Strings.isNullOrEmpty(name)) {
                continue;
            }
            nameSet.add(full2Half(name));
        }
        for (BaseCaseEntity item : infoList) {
            if (categoryEnum.equals(item.getBaseCaseCategoryEnum())) {
                dealList.add(item);
            }
        }
        for (BaseCaseEntity item : dealList) {
            switch (item.getBaseCaseCategoryEnum()) {
                case HBCF:
                    HBCFEntity entity = convert(item, HBCFEntity.class);
                    //名称和当前案件没有交集
                    if (!judgeNameContains(nameSet, entity.getNameandkeynoEntityList())) {
                        continue;
                    }
                    HbcfListEntity outEntity = new HbcfListEntity();
                    outEntity.setId(entity.getId());
                    outEntity.setDocNo(entity.getCaseno());
                    outEntity.setPunishOffice(entity.getPunishgov());
                    outEntity.setPunishResult(entity.getPunishmentresult());
                    outEntity.setNameAndKeyNo(entity.getNameandkeynoEntityList());
                    outEntity.setPunishDate(entity.getPunishdate());
                    outEntity.setIllegalType(entity.getIllegaltype());
                    outEntity.setImplementation(entity.getImplementation());
                    outEntity.setIsValid(entity.getIsvalid());
                    outList.add(outEntity);
                    break;
                case XZCF:
                    XZCFEntity xzEntity = convert(item, XZCFEntity.class);
                    //名称和当前案件没有交集
                    if (!judgeNameContains(nameSet, xzEntity.getNameandkeynoEntityList())) {
                        continue;
                    }
                    //过滤出发单位是法院的数据
                    if (!Strings.isNullOrEmpty(xzEntity.getPunishoffice()) && xzEntity.getPunishoffice().contains("法院")) {
                        continue;
                    }
                    XZCFListEntity xzOutEntity = new XZCFListEntity();
                    xzOutEntity.setId(xzEntity.getId());
                    xzOutEntity.setDocNo(xzEntity.getDocno());
                    xzOutEntity.setPunishOffice(xzEntity.getPunishoffice());
                    xzOutEntity.setPunishResult(xzEntity.getPunishresult());
                    xzOutEntity.setNameAndKeyNo(xzEntity.getNameandkeynoEntityList());
                    xzOutEntity.setPunishDate(xzEntity.getPunishdate());
                    xzOutEntity.setIsValid(xzEntity.getIsvalid());
                    xzOutEntity.setPunishReason(xzEntity.getPunishreason());
                    xzOutEntity.setSource(0);
                    xzOutEntity.setSourceId(xzEntity.getSourceid());
                    xzOutEntity.setGroupId(xzEntity.getGroupid());
                    xzOutEntity.setGroupCount(xzEntity.getGroupcount());
                    xzOutEntity.setSourceName(SourceEnum.getSourceName(xzEntity.getSource()));
                    outList.add(xzOutEntity);
                    break;
            }
        }

        return outList;
    }

    /**
     * 判断名称发是否在set集合中
     *
     * @param nameSet
     * @param nameList
     * @return
     */
    static boolean judgeNameContains(Set<String> nameSet, List<NameAndKeyNoEntity> nameList) {
        if (CollectionUtils.isEmpty(nameList)) {
            return false;
        }
        for (NameAndKeyNoEntity item : nameList) {
            if (nameSet.contains(item.getKeyNo()) || nameSet.contains(full2Half(item.getName()))) {
                return true;
            }
        }
        return false;
    }


    /**
     * 身份汇总所有审理流程信息
     *
     * @param caseRoleList
     * @param infoList
     */
    public static void mergeCaseRoleAndTrialRound(List<CaseRoleEntity> caseRoleList, List<InfoListEntity> infoList) {
        for (CaseRoleEntity caseRole : caseRoleList) {
            String keyNo = caseRole.getN();
            String name = full2Half(caseRole.getP());
            List<CaseRoleSort> roleList = new ArrayList<>();
            CaseRoleSort roleSort;
            for (InfoListEntity infoListEntity : infoList) {
                String trialRound = infoListEntity.getTrialRound();
                List<NameAndKeyNoEntity> all = new ArrayList<>();
                all.addAll(infoListEntity.getProsecutor());
                all.addAll(infoListEntity.getDefendant());
                for (NameAndKeyNoEntity entity : all) {
                    //keyNo或者名称相同 就是同一个人
                    if ((!Strings.isNullOrEmpty(keyNo) && Objects.equal(keyNo, entity.getKeyNo())
                            || Objects.equal(name, full2Half(entity.getName())))) {
                        roleSort = new CaseRoleSort();
                        String role = entity.getRole();
                        role = full2Half(role).replaceAll("\\(.*\\)", "");
                        //通过审理流程返回特定Role名称
                        role = PartyDiffUtil.getSpecialRoleName(role, trialRound);
                        roleSort.setRole(role);
                        roleSort.setTrialRound(CaseRoleMappingUtil.getShortTrialRound(infoListEntity.getTrialRound()));
                        roleSort.setTimeStamp(infoListEntity.getLatestTimestamp());
                        if (infoListEntity.getLatestTimestamp() <= 0) {
                            if (roleSort.getTrialRound().contains("一审")) {
                                roleSort.setTimeStamp(-10);
                            }
                            if (roleSort.getTrialRound().contains("二审")) {
                                roleSort.setTimeStamp(-9);
                            }
                            if (roleSort.getTrialRound().contains("再审")) {
                                roleSort.setTimeStamp(-8);
                            }
                        }
                        roleSort.setLawsuitResult(entity.getLawsuitResult());
                        roleSort.setLawsuitResultV2(entity.getLawsuitResultV2());
                        roleList.add(roleSort);
                        break;
                    }
                }
            }
            //排序
            caseRole.setRoleList(roleList.stream().sorted(Comparator.comparing(CaseRoleSort::getTimeStamp)
                    .thenComparing(CaseRoleSort::getTrialRound).thenComparing(CaseRoleSort::getRole))
                    .collect(Collectors.toList()));
        }

        for (CaseRoleEntity roleEntity : caseRoleList) {
            Set<String> roundSet = new LinkedHashSet<>();
            for (CaseRoleSort caseRoleSort : roleEntity.getRoleList()) {
                roundSet.add(caseRoleSort.getTrialRound() + caseRoleSort.getRole());
            }
            String roleDesc = roundSet.stream()
                    .collect(Collectors.joining(","));
            roleEntity.setD(roleDesc);

            //案件身份描述汇总
            Map<String, CaseRoleSort> caseRoleSortMap = roleEntity.getRoleList().stream()
                    .collect(Collectors.groupingBy(data -> data.getTrialRound() + data.getRole()
                            , Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparingLong(CaseRoleSort::getTimeStamp))
                                    , Optional::get)));
            List<CaseRoleSort> roleList = caseRoleSortMap.values().stream().sorted(Comparator.comparing(CaseRoleSort::getTimeStamp)
                    .thenComparing(CaseRoleSort::getTrialRound).thenComparing(CaseRoleSort::getRole))
                    .collect(Collectors.toList());
            roleEntity.setRoleList(roleList);
        }

//        System.out.println(JSON.toJSONString(caseRoleList));
    }


    /**
     * 组装案件金额信息，不同keyNo当事人看到不同金额
     *
     * @return
     */
    static Map<String, AmtInfo> buildAmtInfo(Set<String> searchWordSet, List<ZBEntity> zbList, List<ZXEntity> zxList, List<CPWSEntity> cpwsList) {
        //当前企业/人员关联的未履行金额、执行标的优先级最高
        //用裁判文书的案件金额为所有人兜底
        //金额使用顺序 终本-未履行金额，终本-执行标底，被执行-执行标底，裁判文书金额
        //终本未履行,终本-执行标底
        List<AmtInfo> zb_wlx_list = new ArrayList<>();
        List<AmtInfo> zb_zxbd_list = new ArrayList<>();
        List<AmtInfo> zx_zxbd_list = new ArrayList<>();
        List<AmtInfo> cpws_je_list = new ArrayList<>();
        for (ZBEntity item : zbList) {
            for (NameAndKeyNoEntity nameKey : item.getBaseNameKeyNoList()) {
                if (!Strings.isNullOrEmpty(nameKey.getKeyNo())) {
                    BigDecimal failureactDec = getAmt(getString(item.getFailureact()));
                    if (failureactDec != null && BigDecimal.ZERO.compareTo(failureactDec) < 0) {
                        zb_wlx_list.add(new AmtInfo("未履行金额", getString(item.getFailureact()), getString(item.getIsvalid()), item.getJudgedate(), nameKey.getKeyNo()));
                    }
                    BigDecimal executeobjectDec = getAmt(getString(item.getExecuteobject()));
                    if (executeobjectDec != null && BigDecimal.ZERO.compareTo(executeobjectDec) < 0) {
                        zb_zxbd_list.add(new AmtInfo("执行标的", getString(item.getExecuteobject()), getString(item.getIsvalid()), item.getJudgedate(), nameKey.getKeyNo()));
                    }
                }

            }
        }

        for (ZXEntity item : zxList) {
            for (NameAndKeyNoEntity nameKey : item.getBaseNameKeyNoList()) {
                if (!Strings.isNullOrEmpty(nameKey.getKeyNo())) {
                    BigDecimal biaodi = getAmt(getString(item.getBiaodi()));
                    if (biaodi != null && BigDecimal.ZERO.compareTo(biaodi) < 0) {
                        zx_zxbd_list.add(new AmtInfo("执行标的", getString(item.getBiaodi()), getString(item.getIsvalid()), item.getLiandate(), nameKey.getKeyNo()));
                    }
                }
            }
        }

        //所有keyNo集合
        Set<String> keyNoSet = searchWordSet.stream().filter(CommonV3Util::isKeyword).collect(Collectors.toSet());
        //案件金额map
        Map<String, AmtInfo> amtInfo = new TreeMap<>();
        //终本-未履行金额获取(优先级-P1)
        Map<String, List<AmtInfo>> zb_wlx_map = zb_wlx_list.stream().collect(Collectors.groupingBy(AmtInfo::getKeyNo));
        zb_wlx_map.forEach((k, v) -> {
            //取时间最近的一次数据
            AmtInfo amtInfo1 = v.stream().sorted(Comparator.comparing(AmtInfo::getTimeStamp, Comparator.reverseOrder())
                    .thenComparing(AmtInfo::getAmt, Comparator.reverseOrder())).findFirst().get();
            amtInfo.put(k, amtInfo1);
            keyNoSet.remove(k);
        });

        //被执行人-执行标获取(优先级-P2)
        Map<String, List<AmtInfo>> zx_zxbd_map = zx_zxbd_list.stream().collect(Collectors.groupingBy(AmtInfo::getKeyNo));
        zx_zxbd_map.forEach((k, v) -> {
            if (amtInfo.get(k) == null) {
                //取时间最近的一次数据
                AmtInfo amtInfo1 = v.stream().sorted(Comparator.comparing(AmtInfo::getTimeStamp, Comparator.reverseOrder())
                        .thenComparing(AmtInfo::getAmt, Comparator.reverseOrder())).findFirst().get();
                amtInfo.put(k, amtInfo1);
                keyNoSet.remove(k);
            }
        });

        //终本-执行标获取(优先级-P3)
        Map<String, List<AmtInfo>> zb_zxbd_map = zb_zxbd_list.stream().collect(Collectors.groupingBy(AmtInfo::getKeyNo));
        zb_zxbd_map.forEach((k, v) -> {
            if (amtInfo.get(k) == null) {
                //取时间最近的一次数据
                AmtInfo amtInfo1 = v.stream().sorted(Comparator.comparing(AmtInfo::getTimeStamp, Comparator.reverseOrder())
                        .thenComparing(AmtInfo::getAmt, Comparator.reverseOrder())).findFirst().get();
                amtInfo.put(k, amtInfo1);
                keyNoSet.remove(k);
            }
        });


        //未匹配到的KeyNo 用裁判文书金额兜底
        for (CPWSEntity item : cpwsList) {
            BigDecimal biaodi = getAmt(getString(item.getAmountinvolved()));
            if (biaodi != null && BigDecimal.ZERO.compareTo(biaodi) < 0) {
                cpws_je_list.add(new AmtInfo("案件金额", getString(item.getAmountinvolved()), getString(item.getIsvalid()), item.getJudgedate(), ""));
            }
        }
        if (CollectionUtils.isNotEmpty(cpws_je_list)) {
            AmtInfo cpwsAmt = cpws_je_list.stream()
                    .sorted(Comparator.comparing(AmtInfo::getTimeStamp, Comparator.reverseOrder())
                            .thenComparing(AmtInfo::getAmt, Comparator.reverseOrder())).findFirst().get();
            for (String keyno : keyNoSet) {
                amtInfo.put(keyno, cpwsAmt);
            }
        }
        return amtInfo;
    }

    static BigDecimal getAmt(String money) {
        try {
            return new BigDecimal(money);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 组装纯案号数据
     *
     * @return
     */
    static InfoListEntity buildSimpleAnnoData(String anno, Map<String, InfoListEntity> infoListMap) {
        InfoListEntity infoListEntity = new InfoListEntity();
        //阶段描述
        String trialRound = new ExtractCaseTrialRoundUDF().evaluate(anno);
        if (Strings.isNullOrEmpty(trialRound)) {
            return null;
        }
        //案由(从后续审理程序中获取同案件类型的案由)
        String caseReason = "";
        //法院
        String court = new ExtractCourtNameFromCaseNoUDF().evaluate(anno);
        //案件类型
        String caseType = getCaseType(anno);

        long firstTime = -1;
        String year = ReUtil.get("\\d{4}", anno, 0);
        if (StringUtils.isNotBlank(year) && StringUtils.isNumeric(year)) {
            int yearNum = Integer.parseInt(year);
            if (yearNum > 1980 && yearNum <= DateUtil.thisYear() + 5) {
                DateTime dateTime = DateUtil.parse(year, "yyyy");
                firstTime = dateTime.getTime() / 1000;
            }
        }

        if (firstTime == -1L) {
            return null;
        }

        //非纯案号审理程序
        List<InfoListEntity> nextInfoList = infoListMap.values().stream()
                .sorted(Comparator.comparing(InfoListEntity::getLatestTimestamp))
                .collect(Collectors.toList());
        //案由(从后续审理程序中获取同案件类型的案由)
        for (InfoListEntity infoList : nextInfoList) {
            if (infoList.getLatestTimestamp() >= firstTime) {
                if (!Strings.isNullOrEmpty(caseType) && Objects.equal(infoList.getCaseType(), caseType)) {
                    caseReason = infoList.getCaseReason();
                    break;
                }
            }
        }
        List<NameAndKeyNoEntity> beforePro = new ArrayList<>();
        List<NameAndKeyNoEntity> beforeDef = new ArrayList<>();
        //1，后续审理程序的裁判文书。
        //2，裁判文书提到的审理程序，和当前案号的审理程序需要一致。
        for (InfoListEntity infoList : nextInfoList) {
            //后续+有裁判文书
            if (infoList.getLatestTimestamp() >= firstTime && CollectionUtils.isNotEmpty(infoList.getCaseList())) {
                List<NameAndKeyNoEntity> allList = new ArrayList<>();
                allList.addAll(infoList.getProsecutor());
                allList.addAll(infoList.getDefendant());
                for (NameAndKeyNoEntity entity : allList) {
                    RoleType roleType = SimpleCaseMappingUtil.getRole(entity.getRole(), trialRound);
                    if (roleType == null) {
                        continue;
                    }
                    NameAndKeyNoEntity copy = new NameAndKeyNoEntity();
                    try {
                        BeanUtils.copyProperties(copy, entity);
                    } catch (Exception e) {
                        continue;
                    }
                    copy.setRole(roleType.getRoleName());
                    copy.setLawsuitResult("");
                    if (RoleTypeEnum.PRO == roleType.getRoleType()) {
                        beforePro.add(copy);
                    }
                    if (RoleTypeEnum.DEF == roleType.getRoleType()) {
                        beforeDef.add(copy);
                    }
                }
                //只匹配某一个审理程序的数据
                if (CollectionUtils.isNotEmpty(beforePro) || CollectionUtils.isNotEmpty(beforeDef)) {
                    break;
                }
            }
        }

        //原告 //被告
        infoListEntity.setTrialRound(trialRound);
        infoListEntity.setAnno(anno2Full(anno));
        infoListEntity.setCaseReason(caseReason);
        infoListEntity.setCourt(getString(court));
        infoListEntity.setProsecutor(beforePro);
        infoListEntity.setDefendant(beforeDef);
        //抗诉/监督机关 -从裁判文书提取
        infoListEntity.setProcuratorate("");
        //从失信中获取执行依据文书号
        infoListEntity.setExecuteNo("");
        //案件类型
        infoListEntity.setCaseType(caseType);
        //编辑字段

        //该案号关联的所有维度的最晚时间(纯案件数据不参与最早最晚审理程序计算)
        infoListEntity.setLatestTimestamp(-1L);
        infoListEntity.setSimpleAnnoYear(firstTime);
        if (CollectionUtils.isEmpty(infoListEntity.getXsggList())) {
            infoListEntity.setXsggList(null);
        }

        return infoListEntity;
    }


    /**
     * 合并多维度入参信息
     *
     * @param lawSuitV3Entity
     * @return
     */
    static List<BaseCaseEntity> buildInputList(LawSuitV3Entity lawSuitV3Entity) {
        //明细信息--InfoList
        List<BaseCaseEntity> allList = new ArrayList<>();
        allList.addAll(lawSuitV3Entity.getLianList());
        allList.addAll(lawSuitV3Entity.getCpwsList());
        allList.addAll(lawSuitV3Entity.getSxList());
        allList.addAll(lawSuitV3Entity.getZxList());
        allList.addAll(lawSuitV3Entity.getKtggList());
        allList.addAll(lawSuitV3Entity.getSdggList());
        allList.addAll(lawSuitV3Entity.getFyggList());
        allList.addAll(lawSuitV3Entity.getPcczList());
        allList.addAll(lawSuitV3Entity.getXjpgList());
        allList.addAll(lawSuitV3Entity.getZbList());
        allList.addAll(lawSuitV3Entity.getXgList());
        allList.addAll(lawSuitV3Entity.getGqdjList());
//        allList.addAll(lawSuitV3Entity.getXzcfList());
//        allList.addAll(lawSuitV3Entity.getHbcfList());
        allList.addAll(lawSuitV3Entity.getSfpmList());
        allList.addAll(lawSuitV3Entity.getSqtjList());
        allList.addAll(lawSuitV3Entity.getXzcjList());
        allList.addAll(lawSuitV3Entity.getXdpgjgList());
        allList.addAll(lawSuitV3Entity.getXsggList());
        return allList;
    }

    /**
     * 合并多维度入参信息
     *
     * @param lawSuitV3Entity
     * @return
     */
    static List<BaseCaseEntity> buildPenaltyInputList(LawSuitV3Entity lawSuitV3Entity) {
        //明细信息--InfoList
        List<BaseCaseEntity> allList = new ArrayList<>();
        allList.addAll(lawSuitV3Entity.getHbcfList());
        //行政处罚需要更具group_id保留最新数据

        if (CollectionUtils.isNotEmpty(lawSuitV3Entity.getXzcfList())) {
//            List<XZCFEntity> xzcfList = Lists.newArrayList();
            Map<String, List<XZCFEntity>> stringMap = lawSuitV3Entity.getXzcfList().stream()
                    .collect(Collectors.groupingBy(XZCFEntity::getGroupid));
            for (List<XZCFEntity> value : stringMap.values()) {
                //取最新一条行政处罚
                allList.add(value.stream().sorted(Comparator.comparing(XZCFEntity::getUpdate_date).reversed())
                        .findFirst().get());
            }
        }

        return allList;
    }

    /**
     * 获取关联维度信息
     *
     * @param allList
     * @return
     */
    static Set<Integer> buildCategoryTags(List<BaseCaseEntity> allList) {
        return allList.stream()
                .map(BaseCaseEntity::getBaseCaseCategoryEnum)
                .map(CaseCategoryEnum::getType)
                .collect(Collectors.toSet());
    }

    /**
     * 删除不能独立成为案件的数据
     *
     * @param entry
     * @return
     */
    static Set<String> getNeedDeleteNos(Set<Map.Entry<String, List<BaseCaseEntity>>> entry) {
        Set<String> deleteCaseNoSet = new HashSet<>();
        for (Map.Entry<String, List<BaseCaseEntity>> listEntry : entry) {
            long totalCount = Integer.valueOf(listEntry.getValue().size()).longValue();
            long xzcfCount = listEntry.getValue().stream()
                    .filter(item -> CaseCategoryEnum.XZCF.equals(item.getBaseCaseCategoryEnum()))
                    .count();
            if (xzcfCount == totalCount) {
                deleteCaseNoSet.add(listEntry.getKey());
                continue;
            }
            long hbcfCount = listEntry.getValue().stream()
                    .filter(item -> CaseCategoryEnum.HBCF.equals(item.getBaseCaseCategoryEnum()))
                    .count();
            if (hbcfCount == totalCount) {
                deleteCaseNoSet.add(listEntry.getKey());
            }
        }
        return deleteCaseNoSet;
    }

    /**
     * NameAndKeyNoEntity 转 CaseRoleEntity
     *
     * @param nameAndKeyNoEntityList
     * @return
     */
    static List<CaseRoleEntity> convertNameKey2CaseRole(List<NameAndKeyNoEntity> nameAndKeyNoEntityList) {
        List<CaseRoleEntity> roleList = new ArrayList<>();
        CaseRoleEntity roleEntity;
        for (NameAndKeyNoEntity item : nameAndKeyNoEntityList) {
            roleEntity = new CaseRoleEntity();
            roleEntity.setP(item.getName());
            roleEntity.setR(item.getRole());
            roleEntity.setN(item.getKeyNo());
            roleEntity.setO(item.getOrg());
            roleList.add(roleEntity);
        }

        return roleList;
    }

    /**
     * 获取案件类型标签
     *
     * @param caseNoSet
     * @return
     */
    static Set<String> getTags(Set<String> caseNoSet) {
        Set<String> tagSet = new HashSet<>();
        for (String caseNo : caseNoSet) {
            tagSet.add(getCaseType(caseNo));
        }
        return tagSet.stream().filter(data -> !Strings.isNullOrEmpty(data)).collect(Collectors.toSet());
    }

    public static String getCaseType(String caseNo) {
        String caseType = extractCaseTypeUDF.evaluate(caseNo);
        if ("执行类案件".equals(caseType)) {
            caseType = "执行案件";
        }
        if ("非诉保全审查案件".equals(caseType)) {
            caseType = "保全案件";
        }
        return caseType;
    }


    /**
     * 构建单案号对象信息
     *
     * @param caseNoMap
     * @return
     */
    static Map<String, InfoListEntity> buildInfoList(Map<String, List<BaseCaseEntity>> caseNoMap) {
        Map<String, InfoListEntity> infoListMap = new HashMap<>();
        caseNoMap.forEach((key, value) -> {
            InfoListEntity infoListEntity = new InfoListEntity();
            //value排序后再使用
            value = value.stream().sorted(Comparator.comparing(BaseCaseEntity::getBaseId)).collect(Collectors.toList());
            //阶段描述
            String trialRound = new ExtractCaseTrialRoundUDF().evaluate(key);
            //案号
            String anno = key;
            //案由
            String caseReason = getCaseReason(value);
            //法院
            String court = getCourt(value);
            //原告 //被告

            Map<String, List<NameAndKeyNoEntity>> roleMap = PartyDiffUtil.getYgBgList(value, trialRound);

            List<NameAndKeyNoEntity> prosecutor = roleMap.get("prosecutor");
            List<NameAndKeyNoEntity> defendant = roleMap.get("defendant");


            infoListEntity.setTrialRound(trialRound);
            infoListEntity.setAnno(anno2Full(anno));
            infoListEntity.setCaseReason(caseReason);
            infoListEntity.setCourt(court);
            infoListEntity.setProsecutor(prosecutor);
            infoListEntity.setDefendant(defendant);
            //抗诉/监督机关 -从裁判文书提取
            infoListEntity.setProcuratorate(getProtestor(value));
            //从失信中获取执行依据文书号
            infoListEntity.setExecuteNo(getExecuteNo(value));
            //案件类型
            infoListEntity.setCaseType(getCaseType(key));

            //编辑字段
            infoListEntity.setSxList(convertList(editItem(value, CaseCategoryEnum.SX), SXListEntity.class));
            infoListEntity.setZxList(convertList(editItem(value, CaseCategoryEnum.ZX), ZXListEntity.class));
            infoListEntity.setXgList(convertList(editItem(value, CaseCategoryEnum.XG), XGListEntity.class));
            infoListEntity.setCaseList(convertList(editItem(value, CaseCategoryEnum.CPWS), CaseListEntity.class));
            infoListEntity.setZbList(convertList(editItem(value, CaseCategoryEnum.ZB), ZbListEntity.class));

            infoListEntity.setFyggList(convertList(editItem(value, CaseCategoryEnum.FYGG), FyggListEntity.class));
            infoListEntity.setGqdjList(convertList(editItem(value, CaseCategoryEnum.GQDJ), GqdjListEntity.class));
            infoListEntity.setKtggList(convertList(editItem(value, CaseCategoryEnum.KTGG), KtggListEntity.class));
            infoListEntity.setLianList(convertList(editItem(value, CaseCategoryEnum.LA), LianListEntity.class));
            infoListEntity.setPcczList(convertList(editItem(value, CaseCategoryEnum.PCCZ), PcczListEntity.class));
            infoListEntity.setSdggList(convertList(editItem(value, CaseCategoryEnum.SDGG), SdggListEntity.class));
            infoListEntity.setXjpgList(convertList(editItem(value, CaseCategoryEnum.XJPG), XjpgListEntity.class));
            infoListEntity.setXzcfList(new ArrayList<>());
            infoListEntity.setHbcfList(new ArrayList<>());
            infoListEntity.setSfpmList(convertList(editItem(value, CaseCategoryEnum.SFPM), SFPMListEntity.class));
            infoListEntity.setSqtjList(convertList(editItem(value, CaseCategoryEnum.SQTJ), SQTJListEntity.class));
            infoListEntity.setXzcjList(convertList(editItem(value, CaseCategoryEnum.XZCJ), XZCJListEntity.class));
            infoListEntity.setXdpgjgList(convertList(editItem(value, CaseCategoryEnum.XDPGJG), XDPGJGListEntity.class));
            infoListEntity.setXsggList(convertList(editItem(value, CaseCategoryEnum.XSGG), XSGGListEntity.class));
            //增量离线任务更新减少变化量
            if (CollectionUtils.isEmpty(infoListEntity.getSfpmList())) {
                infoListEntity.setSfpmList(null);
            }
            if (CollectionUtils.isEmpty(infoListEntity.getSqtjList())) {
                infoListEntity.setSqtjList(null);
            }
            if (CollectionUtils.isEmpty(infoListEntity.getXzcjList())) {
                infoListEntity.setXzcjList(null);
            }
            if (CollectionUtils.isEmpty(infoListEntity.getXdpgjgList())) {
                infoListEntity.setXdpgjgList(null);
            }
            if (CollectionUtils.isEmpty(infoListEntity.getXsggList())) {
                infoListEntity.setXsggList(null);
            }

            //仅有无内容裁判文书的的审理流程给数据打标签
            long noContentCaseCount = infoListEntity.getCaseList().stream()
                    .filter(x -> Objects.equal(1, x.getShieldCaseFlag())
                            && Objects.equal("民事调解书", x.getCaseType())).count();
            if (infoListEntity.getCaseList().size() == noContentCaseCount && noContentCaseCount > 0L) {
                //给原被告打标签（达成和解）
                for (NameAndKeyNoEntity item : prosecutor) {
                    item.setLawsuitResult("14");
                }
                for (NameAndKeyNoEntity item : defendant) {
                    item.setLawsuitResult("14");
                }
            }

            //所有维度列表汇总
            List<BaseCaseOutEntity> baseALlList = getAllList(infoListEntity);

            //该案号关联的所有维度的最晚时间
            infoListEntity.setLatestTimestamp(baseALlList.stream()
                    .sorted(Comparator.comparing(BaseCaseOutEntity::getTimeStamp).reversed())
                    .findFirst().get().getTimeStamp());


            infoListMap.put(key, infoListEntity);
        });
        return infoListMap;
    }

    /**
     * 获取裁判文书抗诉/监督机关
     *
     * @param value
     * @return
     */
    static String getProtestor(List<BaseCaseEntity> value) {
        List<BaseCaseEntity> cpwsList = value.stream().filter(item -> item.getBaseCaseCategoryEnum()
                .equals(CaseCategoryEnum.CPWS)).collect(Collectors.toList());
        Set<String> pNameSet = new HashSet<>();
        for (BaseCaseEntity baseCaseEntity : cpwsList) {
            CPWSEntity cpwsEntity = (CPWSEntity) baseCaseEntity;
            if (cpwsEntity.getProtestorganEntity() != null && !Strings.isNullOrEmpty(cpwsEntity.getProtestorganEntity().getName())) {
                pNameSet.add(cpwsEntity.getProtestorganEntity().getName());
            }
        }
        if (pNameSet.size() > 0) {
            return pNameSet.stream().findFirst().get();
        }
        return "";
    }

    /**
     * 从失信中获取执行依据文书号
     *
     * @param value
     * @return
     */
    static String getExecuteNo(List<BaseCaseEntity> value) {
        List<BaseCaseEntity> cpwsList = value.stream().filter(item -> item.getBaseCaseCategoryEnum()
                .equals(CaseCategoryEnum.SX)).collect(Collectors.toList());
        Set<String> noSet = new HashSet<>();
        for (BaseCaseEntity baseCaseEntity : cpwsList) {
            SXEntity sxEntity = (SXEntity) baseCaseEntity;
            if (!Strings.isNullOrEmpty(sxEntity.getExecuteno())) {
                noSet.add(sxEntity.getExecuteno());
            }
        }
        if (noSet.size() > 0) {
            return noSet.stream().findFirst().get();
        }
        return "";
    }

    /**
     * 生成审理经过
     *
     * @param infoListEntityList
     * @return
     */
    static Map<Long, String> trialRoundDateNodeMap(List<InfoListEntity> infoListEntityList) {
        // 所有时间节点的map集合（key-> 时间戳; value->表示当前的节点状态，审判程序 + "维度时间节点描述文案"(被执行人/失信人/限制高消费)发布日期 或 判决日期 或 裁定日期等）
        Map<Long, String> trialRoundDateNodeMap = new HashMap<>();

        List<String> list = new ArrayList<>();

        for (InfoListEntity infoListEntity : infoListEntityList) {
            //获取所有维度列表汇总
            List<BaseCaseOutEntity> allOutList = getAllList(infoListEntity);
            //TODO 没有时间的数据怎么处理？
            for (BaseCaseOutEntity item : allOutList) {
                if (item.getTimeStamp() != null && item.getTimeStamp() > 0L) {
                    trialRoundDateNodeMap.put(item.getTimeStamp(), infoListEntity.getTrialRound() + "|" + item.getTimeType());
                    list.add(item.getTimeStamp() + "_" + infoListEntity.getTrialRound() + "|" + item.getTimeType());
                }
            }
        }


        //排序
        Map<Long, String> sortedDateNodeMap = new LinkedHashMap<>();
        trialRoundDateNodeMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEachOrdered(e -> sortedDateNodeMap.put(e.getKey(), e.getValue()));

        return sortedDateNodeMap;
    }

    /**
     * 无法通过时间判断新审理经过的情况下，按照审理经过逻辑顺序获取
     *
     * @param infoListEntityList
     * @return
     */
    static String getLatestTrialRoundWithOutTime(List<InfoListEntity> infoListEntityList) {

        if (CollectionUtils.isNotEmpty(infoListEntityList) && infoListEntityList.size() == 1) {
            return infoListEntityList.get(0).getTrialRound();
        }
        return "";
    }

    static List<BaseCaseOutEntity> getAllList(InfoListEntity infoListEntity) {
        List<BaseCaseOutEntity> allOutList = new ArrayList<>();
        allOutList.addAll(infoListEntity.getLianList());
        allOutList.addAll(infoListEntity.getCaseList());
        allOutList.addAll(infoListEntity.getSxList());
        allOutList.addAll(infoListEntity.getZxList());
        allOutList.addAll(infoListEntity.getKtggList());
        allOutList.addAll(infoListEntity.getSdggList());
        allOutList.addAll(infoListEntity.getFyggList());
        allOutList.addAll(infoListEntity.getPcczList());
        allOutList.addAll(infoListEntity.getXjpgList());
        allOutList.addAll(infoListEntity.getZbList());
        allOutList.addAll(infoListEntity.getXgList());
        allOutList.addAll(infoListEntity.getGqdjList());
//        allOutList.addAll(infoListEntity.getXzcfList());
//        allOutList.addAll(infoListEntity.getHbcfList());

        if (CollectionUtils.isNotEmpty(infoListEntity.getSfpmList())) {
            allOutList.addAll(infoListEntity.getSfpmList());
        }
        if (CollectionUtils.isNotEmpty(infoListEntity.getSqtjList())) {
            allOutList.addAll(infoListEntity.getSqtjList());
        }
        if (CollectionUtils.isNotEmpty(infoListEntity.getXzcjList())) {
            allOutList.addAll(infoListEntity.getXzcjList());
        }
        if (CollectionUtils.isNotEmpty(infoListEntity.getXdpgjgList())) {
            allOutList.addAll(infoListEntity.getXdpgjgList());
        }
        if (CollectionUtils.isNotEmpty(infoListEntity.getXsggList())) {
            allOutList.addAll(infoListEntity.getXsggList());
        }

        return allOutList;
    }

    /**
     * 生成最早/最晚审理经过
     *
     * @param trialRoundDateNodeMap
     */
    static Map<String, Object> extendTrialMap(Map<Long, String> trialRoundDateNodeMap) {
        Long earliestDate = -1L;
        String earliestDateType = "";
        Long lastestDate = -1L;
        String lastestDateType = "";
        boolean flag = true;
        for (Map.Entry<Long, String> sortedDateNodeEntry : trialRoundDateNodeMap.entrySet()) {
            if (flag) {
                earliestDate = sortedDateNodeEntry.getKey();
                earliestDateType = sortedDateNodeEntry.getValue();
                flag = false;
            }
            lastestDate = sortedDateNodeEntry.getKey();
            lastestDateType = sortedDateNodeEntry.getValue();
        }
        earliestDateType = CommonV3Util.getDataTypeWithoutTrialRound(earliestDateType);
        if (trialRoundDateNodeMap.size() > 1) {
            lastestDateType = CommonV3Util.getDataTypeWithoutTrialRound(lastestDateType);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("earliestDate", earliestDate);
        result.put("earliestDateType", earliestDateType);
        result.put("lastestDate", lastestDate);
        result.put("lastestDateType", lastestDateType);
        String latestTrialRound = lastestDateType.split("\\|")[0];
        result.put("latestTrialRound", (latestTrialRound.endsWith("日期") || latestTrialRound.endsWith("时间")) ? "" : latestTrialRound);

        return result;
    }


    /**
     * 编辑维度输出字段
     *
     * @param infoList
     * @param categoryEnum
     * @return
     */
    static List<BaseCaseOutEntity> editItem(List<BaseCaseEntity> infoList, CaseCategoryEnum categoryEnum) {
        List<BaseCaseOutEntity> outList = new ArrayList<>();
        List<BaseCaseEntity> dealList = new ArrayList<>();
        for (BaseCaseEntity item : infoList) {
            if (categoryEnum.equals(item.getBaseCaseCategoryEnum())) {
                dealList.add(item);
            }
        }
        for (BaseCaseEntity item : dealList) {
            switch (item.getBaseCaseCategoryEnum()) {
                case SX:
                    SXEntity entity = convert(item, SXEntity.class);
                    SXListEntity outEntity = new SXListEntity();
                    outEntity.setId(entity.getId());
                    outEntity.setIsValid(Integer.valueOf(entity.getIsvalid()));

                    Long publishDate = entity.getPublicdate();
                    outEntity.setPublishDate(publishDate);
                    outEntity.setExecuteStatus(getString(entity.getExecutestatus()));
                    outEntity.setActionType(getString(entity.getActiontype()));
                    outEntity.setNameAndKeyNo(entity.getNameandkeynoEntityList());

                    outEntity.setTimeStamp(publishDate);
                    outEntity.setTimeType("失信人发布日期");
                    outList.add(outEntity);
                    break;
                case ZX:
                    ZXEntity zxEntity = convert(item, ZXEntity.class);
                    ZXListEntity zxListEntity = new ZXListEntity();
                    zxListEntity.setId(zxEntity.getId());
                    zxListEntity.setIsValid(Integer.valueOf(zxEntity.getIsvalid()));
                    zxListEntity.setNameAndKeyNo(zxEntity.getNameandkeynoEntityList());
                    Long lianDate = zxEntity.getLiandate();
                    zxListEntity.setLianDate(lianDate);
                    zxListEntity.setBiaodi(getString(zxEntity.getBiaodi()));
                    zxListEntity.setSqrNameAndKeyNo(zxEntity.getSqrinfoEntityList());

                    zxListEntity.setTimeStamp(lianDate);
                    zxListEntity.setTimeType("被执行人立案日期");
                    outList.add(zxListEntity);
                    break;
                case XG:
                    XGEntity xgEntity = convert(item, XGEntity.class);
                    XGListEntity xgListEntity = new XGListEntity();
                    xgListEntity.setId(xgEntity.getId());
                    xgListEntity.setIsValid(Integer.valueOf(xgEntity.getIsvalid()));
                    xgListEntity.setNameAndKeyNo(new ArrayList<>());
                    xgListEntity.setCompanyInfo(new ArrayList<>());
                    xgListEntity.setXglNameAndKeyNo(new ArrayList<>());
                    xgListEntity.setGlNameAndKeyNo(new ArrayList<>());

                    xgListEntity.setNameAndKeyNo(xgEntity.getXglArrayEntityList());
                    xgListEntity.setCompanyInfo(xgEntity.getXglArrayEntityList());

                    xgListEntity.setXglNameAndKeyNo(xgEntity.getXglArrayEntityList());
                    xgListEntity.setGlNameAndKeyNo(xgEntity.getGlArrayArrayEntityList());
                    xgListEntity.setSqrNameAndKeyNo(xgEntity.getSqrArrayEntityList());


                    xgListEntity.setPublishDate(xgEntity.getPublishdate());
                    xgListEntity.setTimeStamp(xgEntity.getPublishdate());
                    xgListEntity.setTimeType("限制高消费发布日期");
                    outList.add(xgListEntity);
                    break;

                case CPWS:
                    CPWSEntity cpwsEntity = convert(item, CPWSEntity.class);
                    outList.add(buildCPWSDetail(cpwsEntity));
                    break;
                case PCCZ:
                    PCCZEntity pcczEntity = convert(item, PCCZEntity.class);
                    PcczListEntity pcczListEntity = new PcczListEntity();
                    pcczListEntity.setId(pcczEntity.getId());
                    pcczListEntity.setIsValid(Integer.valueOf(pcczEntity.getIsvalid()));
                    String category = "破产";
                    String caseType = getString(pcczEntity.getCasetype());
                    if (StringUtils.isNotBlank(caseType) && !caseType.equals("案件")) {
                        category = caseType.replace("案件", "");
                    }
                    List<NameAndKeyNoEntity> proList = pcczEntity.getApplicantnameandkeynoEntityList();
                    String roleName = PartyDiffUtil.pcczCaseRoleMapping(pcczEntity.getCasetype(), 1);
                    pcczListEntity.setRoleName(roleName);
                    pcczListEntity.setNameAndKeyNo(proList);
                    pcczListEntity.setPublishDate(pcczEntity.getRiskdate());
                    pcczListEntity.setRelateCount(pcczEntity.getRelatecount());
                    pcczListEntity.setTimeStamp(pcczEntity.getRiskdate());
                    pcczListEntity.setTimeType(category + "公开日期");
                    outList.add(pcczListEntity);
                    break;

                case ZB:
                    ZBEntity zbEntity = convert(item, ZBEntity.class);
                    ZbListEntity zbListEntity = new ZbListEntity();

                    zbListEntity.setId(zbEntity.getId());
                    zbListEntity.setIsValid(Integer.valueOf(zbEntity.getIsvalid()));

                    zbListEntity.setNameAndKeyNo(zbEntity.getNameAndKeyNoEntityList());

                    Long endDate = zbEntity.getEnddate() == null ? -1 : zbEntity.getEnddate();
                    zbListEntity.setJudgeDate(endDate);
                    zbListEntity.setExecuteObject(getString(zbEntity.getExecuteobject()));
                    zbListEntity.setFailureAct(getString(zbEntity.getFailureact()));
                    zbListEntity.setTimeStamp(endDate);
                    zbListEntity.setTimeType("案件终本日期");
                    outList.add(zbListEntity);
                    break;

                case XJPG:
                    XJPGEntity xjpgEntity = convert(item, XJPGEntity.class);
                    //构建详情
                    outList.add(buildXJPGDetail(xjpgEntity));
                    break;
                case GQDJ:
                    GQDJEntity gqdjEntity = convert(item, GQDJEntity.class);
                    GqdjListEntity gqdjListEntity = new GqdjListEntity();
                    gqdjListEntity.setId(gqdjEntity.getId());
                    gqdjListEntity.setIsValid(1);
                    if (!Strings.isNullOrEmpty(gqdjEntity.getIsvalid())) {
                        gqdjListEntity.setIsValid(Integer.valueOf(gqdjEntity.getIsvalid()));
                    }
                    String gqdjCategory = StringUtils.isNotEmpty(gqdjEntity.getStatuesdetail()) ?
                            gqdjEntity.getStatuesdetail() : "冻结";
                    gqdjListEntity.setCategory("股权" + gqdjCategory);
                    gqdjListEntity.setNameAndKeyNo(gqdjEntity.getRelatedcompanyinfoEntityList());
                    Long gqdjPublishDate = gqdjEntity.getPublicdate();
                    gqdjListEntity.setPublishDate(gqdjPublishDate);
                    gqdjListEntity.setEquityAmount(getString(gqdjEntity.getEquityamount()));
                    //被执行人
                    gqdjListEntity.setZxNameAndKeyNo(gqdjEntity.getZxnameandkeynoEntityList());


                    gqdjListEntity.setTimeStamp(gqdjPublishDate);
                    gqdjListEntity.setTimeType("股权" + gqdjCategory + "公示日期");
                    if ("0".equals(gqdjEntity.getNoDetailFlag())) {
                        gqdjListEntity.setNoDetailFlag("0");
                    }

                    outList.add(gqdjListEntity);
                    break;
                case SDGG:
                    SDGGEntity sdggEntity = convert(item, SDGGEntity.class);
                    SdggListEntity sdggListEntity = new SdggListEntity();
                    sdggListEntity.setId(sdggEntity.getId());
                    sdggListEntity.setIsValid(Integer.valueOf(sdggEntity.getIsvalid()));
                    sdggListEntity.setPublishDate(sdggEntity.getPublishdate());
                    sdggListEntity.setNameAndKeyNo(sdggEntity.getNameandkeynoEntityList());
                    sdggListEntity.setRecipientList(CollectionUtils.isNotEmpty(sdggEntity.getRecipientEntityList())
                            ? sdggEntity.getRecipientEntityList() : null);

                    if (CollectionUtils.isNotEmpty(sdggListEntity.getNameAndKeyNo())) {
                        for (NameAndKeyNoEntity data : sdggListEntity.getNameAndKeyNo()) {
                            data.setRole(null);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(sdggListEntity.getRecipientList())) {
                        for (NameAndKeyNoEntity data : sdggListEntity.getRecipientList()) {
                            data.setRole(null);
                        }
                    }

                    sdggListEntity.setTimeStamp(sdggEntity.getPublishdate());
                    sdggListEntity.setTimeType("送达公告发布日期");
                    outList.add(sdggListEntity);
                    break;
                case FYGG:
                    FYGGEntity fyggEntity = convert(item, FYGGEntity.class);
                    FyggListEntity fyggListEntity = new FyggListEntity();
                    fyggListEntity.setId(fyggEntity.getId());
                    fyggListEntity.setIsValid(Integer.valueOf(fyggEntity.getIsvalid()));
                    Long fyggDate = fyggEntity.getPublishdate();
                    fyggListEntity.setPublishDate(fyggDate);
                    fyggListEntity.setNameAndKeyNo(fyggEntity.getNameandkeynoEntityList());
                    fyggListEntity.setCategory(fyggEntity.getCategory());
                    fyggListEntity.setRecipientList(CollectionUtils.isNotEmpty(fyggEntity.getRecipientEntityList())
                            ? fyggEntity.getRecipientEntityList() : null);

                    if (CollectionUtils.isNotEmpty(fyggListEntity.getNameAndKeyNo())) {
                        for (NameAndKeyNoEntity data : fyggListEntity.getNameAndKeyNo()) {
                            data.setRole(null);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(fyggListEntity.getRecipientList())) {
                        for (NameAndKeyNoEntity data : fyggListEntity.getRecipientList()) {
                            data.setRole(null);
                        }
                    }

                    fyggListEntity.setTimeStamp(fyggDate);
                    fyggListEntity.setTimeType("法院公告刊登日期");
                    outList.add(fyggListEntity);
                    break;
                case KTGG:
                    KTGGEntity ktggEntity = convert(item, KTGGEntity.class);
                    KtggListEntity ktggListEntity = new KtggListEntity();
                    ktggListEntity.setId(ktggEntity.getId());
                    ktggListEntity.setIsValid(Integer.valueOf(ktggEntity.getIsvalid()));
                    Long ktggDate = ktggEntity.getLiandate();
                    ktggListEntity.setOpenDate(ktggDate);
                    ktggListEntity.setNameAndKeyNo(ktggEntity.getNameandkeynoEntityList());
                    if (CollectionUtils.isNotEmpty(ktggListEntity.getNameAndKeyNo())) {
                        for (NameAndKeyNoEntity data : ktggListEntity.getNameAndKeyNo()) {
                            data.setRole(null);
                        }
                    }
                    //法庭
                    ktggListEntity.setExecuteUnite(getString(ktggEntity.getExecuteUnite()));

                    ktggListEntity.setTimeStamp(ktggDate);
                    ktggListEntity.setTimeType("开庭时间");
                    outList.add(ktggListEntity);
                    break;
                case LA:
                    LAEntity laEntity = convert(item, LAEntity.class);
                    LianListEntity lianListEntity = new LianListEntity();

                    lianListEntity.setId(laEntity.getId());
                    lianListEntity.setIsValid(Integer.valueOf(laEntity.getIsvalid()));
                    Long laDate = laEntity.getPunishdate();
                    lianListEntity.setLianDate(laDate);
                    lianListEntity.setNameAndKeyNo(laEntity.getNameandkeynoEntityList());
                    if (CollectionUtils.isNotEmpty(lianListEntity.getNameAndKeyNo())) {
                        for (NameAndKeyNoEntity data : lianListEntity.getNameAndKeyNo()) {
                            data.setRole(null);
                        }
                    }

                    lianListEntity.setTimeStamp(laDate);
                    lianListEntity.setTimeType("立案日期");
                    outList.add(lianListEntity);
                    break;
                case SQTJ:
                    SQTJEntity sqtjEntity = convert(item, SQTJEntity.class);
                    SQTJListEntity sqtjListEntity = new SQTJListEntity();

                    sqtjListEntity.setId(sqtjEntity.getId());
                    sqtjListEntity.setIsValid(Integer.valueOf(sqtjEntity.getIsvalid()));
                    Long sqtjDate = sqtjEntity.getPunishdate();
                    sqtjListEntity.setLianDate(sqtjDate);
                    sqtjListEntity.setNameAndKeyNo(sqtjEntity.getNameandkeynoEntityList());

                    sqtjListEntity.setTimeStamp(sqtjDate);
                    sqtjListEntity.setTimeType("立案日期");
                    outList.add(sqtjListEntity);
                    break;
                case HBCF:
                    break;
                case XZCF:
                    break;
                case SFPM:
                    SFPMEntity sfpmEntity = convert(item, SFPMEntity.class);
                    SFPMListEntity sfpmListEntity = new SFPMListEntity();

                    sfpmListEntity.setId(sfpmEntity.getId());
                    sfpmListEntity.setIsValid(sfpmEntity.getIsvalid());
                    sfpmListEntity.setOwnerKeyNoArray(sfpmEntity.getOwnerkeynoarrayEntityList());
                    sfpmListEntity.setLianDate(sfpmEntity.getLiandate());
                    sfpmListEntity.setYiwu(sfpmEntity.getYiwu());
                    sfpmListEntity.setAmountunit(sfpmEntity.getAmountunit());
                    sfpmListEntity.setEvaluationprice(sfpmEntity.getEvaluationprice());
                    sfpmListEntity.setName(sfpmEntity.getName());
                    sfpmListEntity.setBiaodi(sfpmEntity.getBiaodi());

                    sfpmListEntity.setTimeStamp(sfpmEntity.getLiandate());
                    sfpmListEntity.setTimeType("拍卖日期");
                    outList.add(sfpmListEntity);
                    break;
                case XZCJ:
                    XZCJEntity xzcjEntity = convert(item, XZCJEntity.class);
                    XZCJListEntity xzcjListEntity = new XZCJListEntity();
                    xzcjListEntity.setId(xzcjEntity.getId());
                    xzcjListEntity.setIsValid(xzcjEntity.getIsvalid());
                    xzcjListEntity.setPublishDate(xzcjEntity.getPublishdate());
                    xzcjListEntity.setRegisterdate(xzcjEntity.getRegisterdate());
                    xzcjListEntity.setExecutedamount(xzcjEntity.getExecutedamount());
                    xzcjListEntity.setPersLimitedList(xzcjEntity.getKeynojsonperslimitedEntityList());
                    xzcjListEntity.setExecutedList(xzcjEntity.getKeynojsonexecutedEntityList());
                    xzcjListEntity.setApplyList(xzcjEntity.getKeynojsonapplyEntityList());

                    xzcjListEntity.setTimeStamp(xzcjEntity.getPublishdate());
                    xzcjListEntity.setTimeType("限制出境发布日期");
                    outList.add(xzcjListEntity);
                    break;
                case XDPGJG:
                    XDPGJGEntity xdpgjgEntity = convert(item, XDPGJGEntity.class);
                    XDPGJGListEntity xdpgjgListEntity = new XDPGJGListEntity();
                    xdpgjgListEntity.setId(xdpgjgEntity.getId());
                    xdpgjgListEntity.setIsValid(xdpgjgEntity.getIsvalid());
                    xdpgjgListEntity.setLotteryDate(xdpgjgEntity.getLotterydate());
                    xdpgjgListEntity.setSubjectClass(xdpgjgEntity.getSubjectclass());
                    xdpgjgListEntity.setSubjectName(xdpgjgEntity.getSubjectname());
                    xdpgjgListEntity.setSubjectSubClass(xdpgjgEntity.getSubjectsubclass());
                    xdpgjgListEntity.setOwnerList(xdpgjgEntity.getKeynoaarrayownerEntityList());
                    xdpgjgListEntity.setAgencyList(xdpgjgEntity.getKeynoarrayagencyEntityList());

                    xdpgjgListEntity.setTimeStamp(xdpgjgEntity.getLotterydate());
                    xdpgjgListEntity.setTimeType("选定评估机构摇号日期");
                    outList.add(xdpgjgListEntity);
                    break;
                case XSGG:
                    XSGGEntity xsggEntity = convert(item, XSGGEntity.class);
                    XSGGListEntity xsggListEntity = new XSGGListEntity();
                    xsggListEntity.setId(xsggEntity.getId());
                    xsggListEntity.setIsValid(xsggEntity.getIsvalid());
                    xsggListEntity.setNameAndKeyNo(xsggEntity.getNameandkeynoEntityList());
                    xsggListEntity.setPublishDate(CaseDateConvertUDF.evaluate(xsggEntity.getPublish_date()));
                    xsggListEntity.setStartDate(CaseDateConvertUDF.evaluate(xsggEntity.getStart_date()));
                    xsggListEntity.setEndDate(CaseDateConvertUDF.evaluate(xsggEntity.getEnd_date()));
                    xsggListEntity.setType(xsggEntity.getType());
                    xsggListEntity.setExecutorAddress(xsggEntity.getExecutor_adress());
                    xsggListEntity.setExecutionMoney(xsggEntity.getExecution_money());
                    xsggListEntity.setOutstandingAmount(xsggEntity.getOutstanding_amount());
                    xsggListEntity.setJudge(xsggEntity.getJudge());
                    xsggListEntity.setJudgeContact(xsggEntity.getJudge_contact());
                    xsggListEntity.setRewardInfoList(xsggEntity.getXsggRewardInfoList());
                    xsggListEntity.setSpiderId(xsggEntity.getSpider_id());
                    xsggListEntity.setTimeStamp(xsggListEntity.getPublishDate());
                    xsggListEntity.setTimeType("财产悬赏公告更新日期");
                    outList.add(xsggListEntity);
                    break;
            }
        }


        return outList;
    }

    /**
     * 询价评估详情构建
     *
     * @param entity
     */
    static XjpgListEntity buildXJPGDetail(XJPGEntity entity) {
        XjpgListEntity outEntity = new XjpgListEntity();
        outEntity.setId(entity.getId());
        outEntity.setNameAndKeyNo(entity.getNameandkeynoEntityList());
        outEntity.setCategory(entity.getConfirmreferencemode());
        outEntity.setTarget(entity.getTarget());
        outEntity.setIsValid(Integer.valueOf(entity.getIsvalid()));
        // 标的物所有人
        outEntity.setTargetNameAndKeyNo(entity.getTargetcompanyEntityList());

        Long publishDate = entity.getPublicdate();
        outEntity.setPublishDate(publishDate);
        outEntity.setTimeStamp(publishDate);
        outEntity.setTimeType("询价评估公示日期");

        String xjResult = "";
        JSONArray priceArray = JSONArray.parseArray(entity.getEvaluationunit());
        if (priceArray != null && !priceArray.isEmpty() && priceArray.size() > 0) {
            Double priceMin = null;
            Double priceMax = null;
            Iterator<Object> it = priceArray.iterator();
            int i = 0;
            while (it.hasNext()) {
                JSONObject json = (JSONObject) it.next();
                if (i == 0) {
                    try {
                        priceMin = json.getDouble("EvaluationPrice");
                        priceMax = json.getDouble("EvaluationPrice");
                    } catch (Exception e) {
                        priceMin = null;
                        priceMax = null;
                    }
                } else {
                    try {
                        if (priceMin > json.getDouble("EvaluationPrice")) {
                            priceMin = json.getDouble("EvaluationPrice");
                        }
                        if (priceMax < json.getDouble("EvaluationPrice")) {
                            priceMax = json.getDouble("EvaluationPrice");
                        }
                    } catch (Exception e) {
                        priceMin = null;
                        priceMax = null;
                    }
                }
                i++;
            }

            if (priceMin != null && priceMax != null) {
                DecimalFormat df = new DecimalFormat("0.00");
                if (priceMin.doubleValue() == priceMax.doubleValue()) {
                    xjResult = df.format(priceMax);
                } else {
                    xjResult = df.format(priceMin).concat("~").concat(df.format(priceMax));
                }
            }
        }
        outEntity.setResult(xjResult);

        return outEntity;
    }

    /**
     * 裁判文书详情数据
     *
     * @param cpwsEntity
     * @return
     */
    static CaseListEntity buildCPWSDetail(CPWSEntity cpwsEntity) {
        CaseListEntity caseListEntity = new CaseListEntity();
        caseListEntity.setId(cpwsEntity.getId());
        caseListEntity.setIsValid(Integer.valueOf(cpwsEntity.getIsvalid()));
        Long judgeDate = cpwsEntity.getJudgedate();
        caseListEntity.setJudgeDate(judgeDate);
        String docType = getString(cpwsEntity.getDoctype());
        String caseNoType = getCaseType(cpwsEntity.getBaseCaseNo());
        String casePrefix = "";
        if (caseNoType.contains("民事")) {
            casePrefix = "民事";
        }
        if (caseNoType.contains("执行")) {
            casePrefix = "执行";
        }
        if (caseNoType.contains("保全")) {
            casePrefix = "保全";
        }
        if (caseNoType.contains("刑事")) {
            casePrefix = "刑事";
        }
        if (caseNoType.contains("行政")) {
            casePrefix = "行政";
        }
        if ("国家赔偿与司法救助案件".equals(caseNoType)
                || "国际司法协助案件".equals(caseNoType)
                || "司法制裁案件".equals(caseNoType)
                || "区际司法协助案件".equals(caseNoType)
                || "管辖案件".equals(caseNoType)) {
            casePrefix = caseNoType.replace("案件", "");
        }

        CaseDocTypeEnum caseDocTypeEnum = CaseDocTypeEnum.getCaseDocByCode(docType);
        if (caseDocTypeEnum == null || CaseDocTypeEnum.OTHER.equals(caseDocTypeEnum)) {
            //日期描述
            caseListEntity.setDocType("裁判日期");
            //日期描述
            caseListEntity.setTimeType("裁判日期");
            //文书类型
            caseListEntity.setCaseType("其他文书");
            //结果描述
            caseListEntity.setResultType("");
        } else {
            caseListEntity.setDocType(caseDocTypeEnum.getDateDesc());
            caseListEntity.setTimeType(caseDocTypeEnum.getDateDesc());
            caseListEntity.setCaseType(casePrefix + caseDocTypeEnum.getType());
            caseListEntity.setResultType(caseDocTypeEnum.getResultDesc());
        }

        //案件类型为空
        if (Strings.isNullOrEmpty(casePrefix)) {
            //日期描述
            caseListEntity.setDocType("裁判日期");
            //日期描述
            caseListEntity.setTimeType("裁判日期");
            //文书类型
            caseListEntity.setCaseType("裁判文书");
            //结果描述
            caseListEntity.setResultType("裁判结果");
        }

        caseListEntity.setTimeStamp(judgeDate);
        caseListEntity.setResult(cpwsEntity.getJudgeresult());
        caseListEntity.setAmt(getString(cpwsEntity.getAmountinvolved()));
        caseListEntity.setShieldCaseFlag(cpwsEntity.getShieldCaseFlag());
        return caseListEntity;
    }


    static Set<String> extendNameKey(List<NameAndKeyNoEntity> nameKeyList) {
        Set<String> keySet = new HashSet<>();
        for (NameAndKeyNoEntity nameAndKeyNoEntity : nameKeyList) {
            if (!Strings.isNullOrEmpty(nameAndKeyNoEntity.getName())) {
                keySet.add(nameAndKeyNoEntity.getName());
            }
            if (!Strings.isNullOrEmpty(nameAndKeyNoEntity.getKeyNo())) {
            }

        }
        return keySet;
    }

    /**
     * 原被告获取不到，尝试从当事人提取角色信息
     */
    public static List<NameAndKeyNoEntity> getPartyInfo(List<BaseCaseEntity> cpwsList,
                                                        List<BaseCaseEntity> ktggList,
                                                        List<BaseCaseEntity> laList,
                                                        List<BaseCaseEntity> sdggList,
                                                        List<BaseCaseEntity> fyggList,
                                                        List<BaseCaseEntity> sxList,
                                                        List<BaseCaseEntity> zxList,
                                                        List<BaseCaseEntity> xgList,
                                                        List<BaseCaseEntity> pcczList,
                                                        List<BaseCaseEntity> zbList,
                                                        List<BaseCaseEntity> xjpgList,
                                                        List<BaseCaseEntity> gqdjList,
                                                        List<BaseCaseEntity> xsggList) {
        List<NameAndKeyNoEntity> partyList = new LinkedList<>();
        if (CollectionUtils.isNotEmpty(cpwsList)) {
            for (BaseCaseEntity item : cpwsList) {
                if (CollectionUtils.isEmpty(partyList)) {
                    CPWSEntity cpwsEntity = convert(item, CPWSEntity.class);
                    partyList.addAll(getCommonRoleList(cpwsEntity.getNameAndKeyNoEntityList(), CaseRoleEnum.ROLE_X.getRoleName()));
                }
            }
        }

        if (CollectionUtils.isEmpty(partyList)
                && CollectionUtils.isNotEmpty(ktggList)) {
            for (BaseCaseEntity item : ktggList) {
                if (CollectionUtils.isEmpty(partyList)) {
                    KTGGEntity entity = convert(item, KTGGEntity.class);
                    partyList.addAll(getCommonRoleList(entity.getNameandkeynoEntityList(), CaseRoleEnum.ROLE_X.getRoleName()));
                }
            }
        }

        if (CollectionUtils.isEmpty(partyList)
                && CollectionUtils.isNotEmpty(laList)) {
            for (BaseCaseEntity item : laList) {
                if (CollectionUtils.isEmpty(partyList)) {
                    LAEntity entity = convert(item, LAEntity.class);
                    partyList.addAll(getCommonRoleList(entity.getNameandkeynoEntityList(), CaseRoleEnum.ROLE_X.getRoleName()));
                }
            }
        }

        if (CollectionUtils.isEmpty(partyList)
                && CollectionUtils.isNotEmpty(sdggList)) {
            for (BaseCaseEntity item : sdggList) {
                if (CollectionUtils.isEmpty(partyList)) {
                    SDGGEntity entity = convert(item, SDGGEntity.class);
                    partyList.addAll(getCommonRoleList(entity.getNameandkeynoEntityList(), CaseRoleEnum.ROLE_X.getRoleName()));
                }
            }
        }

        if (CollectionUtils.isEmpty(partyList)
                && CollectionUtils.isNotEmpty(fyggList)) {
            for (BaseCaseEntity item : fyggList) {
                if (CollectionUtils.isEmpty(partyList)) {
                    FYGGEntity entity = convert(item, FYGGEntity.class);
                    partyList.addAll(getCommonRoleList(entity.getNameandkeynoEntityList(), CaseRoleEnum.ROLE_X.getRoleName()));
                }
            }
        }

        //失信
        //被执行

        //限高
        //破产重组
        if (CollectionUtils.isEmpty(partyList)
                && CollectionUtils.isNotEmpty(pcczList)) {
            for (BaseCaseEntity item : pcczList) {
                if (CollectionUtils.isEmpty(partyList)) {
                    PCCZEntity entity = convert(item, PCCZEntity.class);
                    partyList.addAll(getCommonRoleList(entity.getNameandkeynoEntityList(), CaseRoleEnum.ROLE_X.getRoleName()));
                }
            }
        }

        //终本
        //询价评估
        //股权冻结
        //悬赏公告
        if (CollectionUtils.isEmpty(partyList)
                && CollectionUtils.isNotEmpty(xsggList)) {
            for (BaseCaseEntity item : xsggList) {
                if (CollectionUtils.isEmpty(partyList)) {
                    XSGGEntity entity = convert(item, XSGGEntity.class);
                    partyList.addAll(getCommonRoleList(entity.getNameandkeynoEntityList(), CaseRoleEnum.ROLE_E.getRoleName()));
                }
            }
        }

        return partyList;
    }

    /**
     * 获取法院信息
     * 1.裁判文书
     * 2.时间靠后（空值跳过，下一个）
     * 理论上，无论从哪取，结果应当一致
     *
     * @param infoList
     * @return
     */
    static String getCourt(List<BaseCaseEntity> infoList) {
        String court = "";
        List<BaseCaseEntity> cpwsList = new ArrayList<>();
        List<BaseCaseEntity> otherList = new ArrayList<>();
        for (BaseCaseEntity item : infoList) {
            if (CaseCategoryEnum.CPWS.equals(item.getBaseCaseCategoryEnum())) {
                cpwsList.add(item);
            } else {
                otherList.add(item);
            }
        }

        if (CollectionUtils.isNotEmpty(cpwsList)) {
            for (BaseCaseEntity baseCaseEntity : cpwsList) {
                if (Strings.isNullOrEmpty(court)) {
                    court = baseCaseEntity.getBaseCourt();
                }
            }
        }
        //从其他维度获取
        if (Strings.isNullOrEmpty(court)) {
            //TODO 暂时先随机取一个
            for (BaseCaseEntity baseCaseEntity : otherList) {
                if (Strings.isNullOrEmpty(court)) {
                    court = baseCaseEntity.getBaseCourt();
                }
            }
        }


        return court;
    }


    /**
     * 案由获取
     * 取值顺序：
     * 1.裁判文书
     * 2.开庭公告
     * 3.立案信息
     * 4.诉前调解
     * 5.送达公告
     * 6.法院公告
     *
     * @param infoList
     * @return
     */
    static String getCaseReason(List<BaseCaseEntity> infoList) {
        String caseReason = "";
        List<BaseCaseEntity> cpwsList = new ArrayList<>();
        List<BaseCaseEntity> ktggList = new ArrayList<>();
        List<BaseCaseEntity> sdggList = new ArrayList<>();
        List<BaseCaseEntity> fyggList = new ArrayList<>();
        List<BaseCaseEntity> lanList = new ArrayList<>();
        List<BaseCaseEntity> sqtjList = new ArrayList<>();
        for (BaseCaseEntity item : infoList) {
            if (CaseCategoryEnum.CPWS.equals(item.getBaseCaseCategoryEnum())) {
                cpwsList.add(item);
            }
            if (CaseCategoryEnum.KTGG.equals(item.getBaseCaseCategoryEnum())) {
                ktggList.add(item);
            }
            if (CaseCategoryEnum.LA.equals(item.getBaseCaseCategoryEnum())) {
                lanList.add(item);
            }
            if (CaseCategoryEnum.SQTJ.equals(item.getBaseCaseCategoryEnum())) {
                sqtjList.add(item);
            }
            if (CaseCategoryEnum.SDGG.equals(item.getBaseCaseCategoryEnum())) {
                sdggList.add(item);
            }
            if (CaseCategoryEnum.FYGG.equals(item.getBaseCaseCategoryEnum())) {
                fyggList.add(item);
            }
        }

        if (CollectionUtils.isNotEmpty(cpwsList)) {
            for (BaseCaseEntity baseCaseEntity : cpwsList) {
                if (Strings.isNullOrEmpty(caseReason)) {
                    caseReason = baseCaseEntity.getBaseCaseReason();
                }
            }
        }

        if (CollectionUtils.isNotEmpty(ktggList) && Strings.isNullOrEmpty(caseReason)) {
            for (BaseCaseEntity baseCaseEntity : ktggList) {
                if (Strings.isNullOrEmpty(caseReason)) {
                    caseReason = baseCaseEntity.getBaseCaseReason();
                }
            }

        }
        if (CollectionUtils.isNotEmpty(lanList) && Strings.isNullOrEmpty(caseReason)) {
            for (BaseCaseEntity baseCaseEntity : lanList) {
                if (Strings.isNullOrEmpty(caseReason)) {
                    caseReason = baseCaseEntity.getBaseCaseReason();
                }
            }

        }
        if (CollectionUtils.isNotEmpty(sqtjList) && Strings.isNullOrEmpty(caseReason)) {
            for (BaseCaseEntity baseCaseEntity : sqtjList) {
                if (Strings.isNullOrEmpty(caseReason)) {
                    caseReason = baseCaseEntity.getBaseCaseReason();
                }
            }

        }

        if (CollectionUtils.isNotEmpty(sdggList) && Strings.isNullOrEmpty(caseReason)) {
            for (BaseCaseEntity baseCaseEntity : sdggList) {
                if (Strings.isNullOrEmpty(caseReason)) {
                    caseReason = baseCaseEntity.getBaseCaseReason();
                }
            }
        }

        if (CollectionUtils.isNotEmpty(fyggList) && Strings.isNullOrEmpty(caseReason)) {
            for (BaseCaseEntity baseCaseEntity : fyggList) {
                if (Strings.isNullOrEmpty(caseReason)) {
                    caseReason = baseCaseEntity.getBaseCaseReason();
                }
            }
        }

        if ("其他".equals(caseReason)) {
            caseReason = "";
        }

        return caseReason;
    }


    /**
     * 案号合并
     *
     * @param list
     * @return
     */
    static Map<String, List<BaseCaseEntity>> mergeByCaseNo(List<BaseCaseEntity> list, Set<String> annoGourpSet) {
        Map<String, List<BaseCaseEntity>> caseNoMap = new HashMap();
        for (BaseCaseEntity item : list) {
            //只取第一个案号
//            String caseNo = getCaseNo(item.getBaseCaseNo());
            //TODO 多案号关联优化
            String caseNo = getCaseNo(item.getBaseCaseNo(), annoGourpSet);
            caseNo = CaseNoCleanUDF.evaluate(caseNo);
            if (!Strings.isNullOrEmpty(caseNo)) {
                List<BaseCaseEntity> tmpList = caseNoMap.get(caseNo);
                if (tmpList == null) {
                    tmpList = new ArrayList<>();
                }
                tmpList.add(item);
                caseNoMap.put(caseNo, tmpList);
            }

        }

        return caseNoMap;
    }

    /**
     * 构建案件名称
     *
     * @param firstTimeInfoEntity
     * @return
     */
    static Map<String, String> buildCaseName(InfoListEntity firstTimeInfoEntity) {
        Map<String, String> result = new HashMap<>();
        if (firstTimeInfoEntity == null) {
            result.put("CaseName", "");
            result.put("CaseNameClean", "");
            return result;
        }
        //案由
        String caseReason = getString(firstTimeInfoEntity.getCaseReason());
        //案件类型
        String caseType = getString(firstTimeInfoEntity.getCaseType());
        //原告
        List<NameAndKeyNoEntity> proList = firstTimeInfoEntity.getProsecutor();
        //被告
        List<NameAndKeyNoEntity> defList = firstTimeInfoEntity.getDefendant();

        int proSize = proList.size();
        int defSize = defList.size();
        if (proSize > 3) {
            proList = proList.subList(0, 3);
        }
        if (defSize > 3) {
            defList = defList.subList(0, 3);
        }

        //公诉机关(xxx检察院)
        long ppoProCount = proList.stream()
                .filter(x -> !Strings.isNullOrEmpty(x.getName()) && x.getName().contains("检察院"))
                .count();

        long ppoDefCount = defList.stream()
                .filter(x -> !Strings.isNullOrEmpty(x.getName()) && x.getName().contains("检察院"))
                .count();

        long ppoCount = ppoProCount + ppoDefCount;


        String prosecutor = proList.stream().map(NameAndKeyNoEntity::getName)
                .filter(item -> !Strings.isNullOrEmpty(item) && !item.contains("检察院"))
                .collect(Collectors.joining(",")) + (proSize > 3 ? "等" : "");

        String defendant = defList.stream().map(NameAndKeyNoEntity::getName)
                .filter(item -> !Strings.isNullOrEmpty(item) && !item.contains("检察院"))
                .collect(Collectors.joining(",")) + (defSize > 3 ? "等" : "");

        //原被告一样的数据先简单处理，A,B 与A这种情况暂未考虑
        if (Objects.equal(prosecutor, defendant)) {
            defendant = "";
        }

        String prefix = "";
        if (!Strings.isNullOrEmpty(prosecutor) && !Strings.isNullOrEmpty(defendant)) {
            prefix = prosecutor + "与" + defendant;
        } else if (Strings.isNullOrEmpty(prosecutor) && Strings.isNullOrEmpty(defendant)) {
            prefix = "";
        } else {
            prefix = prosecutor + defendant;
        }

        //生成屏蔽当事人信息的案件名称
        String prosecutorShow = proList.stream().map(x -> PersonNameHideUtil.nameEncrypt(x.getName(), x.getKeyNo()))
                .filter(item -> !Strings.isNullOrEmpty(item) && !item.contains("检察院"))
                .collect(Collectors.joining(",")) + (proSize > 3 ? "等" : "");

        String defendantShow = defList.stream().map(x -> PersonNameHideUtil.nameEncrypt(x.getName(), x.getKeyNo()))
                .filter(item -> !Strings.isNullOrEmpty(item) && !item.contains("检察院"))
                .collect(Collectors.joining(",")) + (defSize > 3 ? "等" : "");
        String prefixShow = "";
        if (!Strings.isNullOrEmpty(prosecutorShow) && !Strings.isNullOrEmpty(defendantShow)) {
            prefixShow = prosecutorShow + "与" + defendantShow;
        } else if (Strings.isNullOrEmpty(prosecutorShow) && Strings.isNullOrEmpty(defendantShow)) {
            prefixShow = "";
        } else {
            prefixShow = prosecutorShow + defendantShow;
        }

        result.put("CaseName", prefix + (Strings.isNullOrEmpty(caseReason) ? caseType : caseReason + (ppoCount > 0 ? "案件" : "的案件")));
        result.put("CaseNameClean", prefixShow + (Strings.isNullOrEmpty(caseReason) ? caseType : caseReason + (ppoCount > 0 ? "案件" : "的案件")));

        return result;
    }

    static ExtractCaseTypeUDF extractCaseTypeUDF = new ExtractCaseTypeUDF();

    /**
     * 獲取案号
     *
     * @param anno
     * @return
     */
    public static String getCaseNo(String anno) {
        //获取案号
        String baseCaseNo = getString(anno);

        //获取案号对应案件类型
//        Set<String> caseTypeSet = new HashSet<>();
        //案号汇总(之一，之二 特殊处理)
        Set<String> baseCaseNoSet = new HashSet<>();
        String[] baseCaseNoArray = baseCaseNo.split(",");
        for (String caseNo : baseCaseNoArray) {
//            caseTypeSet.add(extractCaseTypeUDF.evaluate(caseNo));
            baseCaseNoSet.add(full2Half(caseNo.split("之")[0]));
        }
        //只取第一个案号
        String caseNo = baseCaseNoSet.stream().sorted(Comparator.reverseOrder()).findFirst().get();
        return caseNo;
    }


    /**
     * 拆分案件 获取所有案号
     *
     * @param anno
     * @return
     */
    public static Set<String> getAllCaseNo(String anno) {
        //获取案号
        String baseCaseNo = getString(anno);

        //获取案号对应案件类型
//        Set<String> caseTypeSet = new HashSet<>();
        //案号汇总(之一，之二 特殊处理)
        Set<String> baseCaseNoSet = new HashSet<>();
        String[] baseCaseNoArray = baseCaseNo.split(",");
        for (String caseNo : baseCaseNoArray) {
            //加入案号清洗
            String no = full2Half(CaseNoCleanUDF.evaluate(caseNo.split("之")[0]));
            if (!Strings.isNullOrEmpty(no)) {
                baseCaseNoSet.add(no);
            }

        }
        return baseCaseNoSet;
    }


    /**
     * 获取裁判文书身份信息
     *
     * @param names
     * @param caseRoleEntityList
     * @return
     */
    static List<NameAndKeyNoEntity> getCPWSRoleList(String names, List<CaseRoleEntity> caseRoleEntityList) {
        List<NameAndKeyNoEntity> resultList = new ArrayList<>();
        if (Strings.isNullOrEmpty(names)) {
            return resultList;
        }
        Set<String> nameSet = Arrays.stream(names.split(","))
                .collect(Collectors.toSet());
        for (CaseRoleEntity item : caseRoleEntityList) {
            if (nameSet.contains(item.getP())) {
                NameAndKeyNoEntity nameAndKeyNoEntity = new NameAndKeyNoEntity(item.getP(), item.getN()
                        , getOrgByKeyNo(item.getN(), item.getP()), item.getR(), item.getT(), item.getLawFirmList(), item.getJR());
                resultList.add(nameAndKeyNoEntity);
            }
        }
        return resultList;
    }


    /**
     * 获取通用身份
     *
     * @param list * @param roleName
     * @return
     */
    static List<NameAndKeyNoEntity> getCommonRoleList(List<NameAndKeyNoEntity> list, String roleName) {
        List<NameAndKeyNoEntity> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return resultList;
        }
        for (NameAndKeyNoEntity item : list) {
            String role = Strings.isNullOrEmpty(item.getRole()) ? roleName : item.getRole();
            resultList.add(new NameAndKeyNoEntity(item.getName(), item.getKeyNo(), getOrgByKeyNo(item.getKeyNo(), item.getName()), role, item.getLawsuitResult(), null, item.getLawsuitResultV2()));
        }
        return resultList;
    }

    public static String full2Half(Object input) {
        if (input == null || "".equals(input)) {
            return "";
        }
        char c[] = input.toString().toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == '\u3000') {
                c[i] = ' ';
            } else if (c[i] > '\uFF00' && c[i] < '\uFF5F') {
                c[i] = (char) (c[i] - 65248);

            }
        }
        String returnString = new String(c);

        return returnString;
    }

    public static String getString(Object object) {
        return object == null ? "" : object.toString();
    }

    /**
     * 类型转换
     *
     * @param entity
     * @param type
     * @param <T>
     * @return
     */
    static <T> T convert(BaseCaseEntity entity, Class<T> type) {
        return (T) entity;
    }

    /**
     * 类型转换
     *
     * @param list
     * @param type
     * @param <T>
     * @return
     */
    static <T> List<T> convertList(List list, Class<T> type) {
        List<T> outList = new ArrayList<>();
        for (Object entity : list) {
            outList.add((T) entity);
        }
        return outList;
    }


    /**
     * 根据KeyNo获取Org
     * 当keyNo为空时，根据name长度做二次判断：
     * 1 长度 > 4时，返回-1
     * 2 长度 <= 4时，返回-2
     *
     * @param keyNo
     * @param name
     * @return
     */
    public static int getOrgByKeyNo(String keyNo, String name) {
        keyNo = getString(keyNo);
        name = getString(name);
        if (StringUtils.isBlank(keyNo)) {
            return (name.length() > 4 ? -1 : -2);
        }

        int org = 0;
        if (keyNo.startsWith("s")) {
            org = 1;
        } else if (keyNo.startsWith("h")) {
            org = 3;
        } else if (keyNo.startsWith("t")) {
            org = 5;
        } else if (keyNo.startsWith("g") || keyNo.startsWith("x") || keyNo.startsWith("w") || keyNo.startsWith("j")) {
            org = 4;
        } else if (keyNo.startsWith("y")) {
            org = 7;
        } else if (keyNo.startsWith("o")) {
            org = 8;
        } else if (keyNo.startsWith("z")) {
            org = 9;
        } else if (keyNo.startsWith("p")) {
            org = 2;
        }
        return org;
    }

    /**
     * 将日期字符串转换为时间戳（精度到秒）
     *
     * @param inputDate
     * @return
     */
    public static Long parseDateToTimeStamp(String inputDate) {
        Long timestamp = -1L;
        try {
            if (StringUtils.isNotBlank(inputDate)) {

                try {
                    if (inputDate.contains("T")) {
                        DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
                        timestamp = df.parse(inputDate).getTime() / 1000;
                    }
                } catch (Exception ex) {
                }

                if (timestamp == -1L) {
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    timestamp = format.parse(inputDate).getTime() / 1000;
                }
            }
        } catch (Exception e) {
        }
        return timestamp;
    }

    /**
     * 时间节点文案信息的首字符为"|"时，剔除该字符
     *
     * @param dataType
     * @return
     */
    public static String getDataTypeWithoutTrialRound(String dataType) {
        if (StringUtils.isNotBlank(dataType) && dataType.startsWith("|")) {
            return dataType.substring(1);
        } else {
            return dataType;
        }
    }

    /**
     * 从searchWordSet中汇总所有的搜索关键词信息
     *
     * @param searchWordSet
     * @return
     */
    public static String getCompanyKeywordsFromSearchWordSet(Set<String> searchWordSet) {
        List<String> companyKeyWords = searchWordSet.stream()
                .filter(StringUtils::isNotBlank)
                .filter(e -> e.length() > 4 || (e.length() <= 4 && !e.contains("某")))
                .sorted().collect(Collectors.toList());
        if (companyKeyWords != null && companyKeyWords.size() > 0) {
            return StringUtils.join(companyKeyWords, ",");
        } else {
            return "";
        }
    }

    /**
     * 案号转全角括号
     *
     * @param anno
     * @return
     */
    public static String anno2Full(String anno) {
        if (Strings.isNullOrEmpty(anno)) {
            return "";
        }
        return anno.replace("(", "（").replace(")", "）");
    }

    public static Boolean isKeyword(String str) {
        if (str.length() != 32) {
            return false;
        }
        String regex = "^[a-zA-Z0-9]+$";
        Pattern pattern = Pattern.compile(regex);
        Matcher match = pattern.matcher(str);
        return match.matches();
    }

    /**
     * 生成案件其他标签信息
     */
    private static String buildOtherTags(LawSuitV3Entity lawSuitV3Entity) {
        Set<String> tags = new HashSet<>();

        //行政许可标签
        boolean xzxk = false;
        List<CPWSEntity> cpwsList = lawSuitV3Entity.getCpwsList();
        if (CollectionUtils.isNotEmpty(cpwsList)) {
            for (CPWSEntity item : cpwsList) {
                if (safeContains(item.getNameandkeyno(), "行政许可", "")) {
                    continue;
                }
//                1，裁判文书的“案由”包含“行政许可”；
//                2，裁判文书的“标题”包含“行政许可”；
//                3，裁判文书的“审理经过”包含“行政许可”；
//                4，裁判文书的“判决结果”包含“行政许可”。
                if (safeContains(item.getCasereason(), "行政许可", "行政许可服务中心")
                        || safeContains(item.getCasename(), "行政许可", "行政许可服务中心")
                        || safeContains(item.getTrialprocess(), "行政许可", "行政许可服务中心")
                        || safeContains(item.getJudgeresult(), "行政许可", "行政许可服务中心")) {
                    xzxk = true;
                    break;
                }
            }
        }
        if (xzxk) {
            tags.add(OtherTagEnum.XZXK.getType());
        }
        if (CollectionUtils.isEmpty(tags)) {
            return null;
        }
        return tags.stream().sorted().collect(Collectors.joining(","));
    }

    /**
     * 安全包含
     *
     * @param content
     * @param target
     * @return
     */
    static boolean safeContains(String content, String target, String exclude) {
        content = getString(content);
        target = getString(target);
        exclude = getString(exclude);
        return content.contains(target) && !content.contains(exclude);
    }

    /**
     * 获取案号
     *
     * @param anno
     * @return
     */
    public static String getCaseNo(String anno, Set<String> groupAnNoSet) {
        //获取案号
        String baseCaseNo = getString(anno);

        Set<String> baseCaseNoSet = new HashSet<>();
        String[] baseCaseNoArray = baseCaseNo.split(",");
        for (String caseNo : baseCaseNoArray) {
//            caseTypeSet.add(extractCaseTypeUDF.evaluate(caseNo));
            baseCaseNoSet.add(full2Half(caseNo.split("之")[0]));
        }
        Set<String> result = new HashSet<>();
        for (String caseNo : baseCaseNoSet) {
            String cleanCaseNo = CaseNoCleanUDF.evaluate(caseNo);
            if (groupAnNoSet.contains(cleanCaseNo)) {
                result.add(caseNo);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            //只取第一个案号
            return baseCaseNoSet.stream().sorted(Comparator.reverseOrder()).findFirst().get();
        } else {
            //案号案件类型排序，其实就是优先返回执行类型案件
            List<CaseNoEntity> caseNoEntityList = new ArrayList<>();
            result.forEach(v -> caseNoEntityList.add(CaseNoEntity.builder().caseNo(v).build()));
            return caseNoEntityList.stream()
                    .sorted(Comparator.comparing(CaseNoEntity::getScore).thenComparing(CaseNoEntity::getCaseNo))
                    .findFirst().get().getCaseNo();
        }

    }

    /**
     * Desc:获取计算caseRole的对象 (排序后，第一个为管辖的则取下一个，所有都是管辖的，则取第一个)
     *
     * */
    public static InfoListEntity getFirstTimeInfoEntity(List<InfoListEntity> tmpList) {
        InfoListEntity result = null;
        if (tmpList.size() > 1) {
            List<InfoListEntity> sortList = tmpList.stream().sorted(Comparator.comparingLong(InfoListEntity::getLatestTimestamp)).collect(Collectors.toList());
            for (InfoListEntity item : sortList) {
                if (item.getTrialRound().contains("管辖")) {
                    continue;
                }
                result = item;
                break;
            }
            if (result == null) {
                result = sortList.get(0);
            }
        } else {
            result = tmpList.get(0);
        }
        return result;
    }

    public static void main(String[] args) {
        String groupANno = "(2018)浙1004执608号,(2017)浙1004民初9642号";
        String anno = "（2017）浙1004民初9642号,（2018）浙1004执608号";

        System.out.println(getCaseNo(anno, Sets.newHashSet(groupANno.split(","))));


    }
}
