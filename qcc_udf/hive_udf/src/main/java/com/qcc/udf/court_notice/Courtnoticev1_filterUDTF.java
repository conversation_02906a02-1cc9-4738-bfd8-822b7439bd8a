package com.qcc.udf.court_notice;

import com.qcc.udf.court_notice.anCleanMethods.CleanCourtUtil;
import com.qcc.udf.court_notice.anUtils.Constants;
import com.qcc.udf.court_notice.anUtils.MD5Util;
import com.qcc.udf.court_notice.anUtils.Util;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentLengthException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDTF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
* 新数据源ods_company_risk.t_court_hear_anno
**/
public class Courtnoticev1_filterUDTF extends GenericUDTF{
    String sourceFrom;
    String party;
    Object content;
    StringBuffer sb = new StringBuffer();
    boolean delFlag;
    String content_tmp;
    long courtyear;
    static String strNull = null;
    String prosecutor;
    String defendant;
    String execute_gov;
    Calendar cal;
    String[] inschema
            = new String[]{ "execute_gov", "execute_unite", "open_time", "schedule_time", "undertake_department", "chief_judge", "case_no",
            "case_reason", "prosecutor", "defendant", "party", "datatype", "source_from", "isvalid", "delflag", "created_date", "updated_date","content","is_struct","annoid"};
    String[] outschema
            =new String[]{"id","liandate","casereason","prosecutorlist_os","defendantlist_os","executegov","caseno","courtyear","type","province","name","companynames","isvalid",
            "party","datatype","prosecutorlist","defendantlist","execute_unite","schedule_time","undertake_department","chief_judge","created_date","updated_date","key_no","content","is_struct","partycontext"};
    Map<String, Integer> inschemap = new HashMap<>();
    Map<String, Integer> outschemap = new HashMap<>();
    @Override
    public void close() throws HiveException {
        // TODO Auto-generated method stub

    }

    @Override
    public void process(Object[] args) throws HiveException {
        try {
            Log logger= LogFactory.getLog(Courtnoticev1_filterUDTF.class.getName());
            inschemap.clear();
            outschemap.clear();
            prosecutor=null;
            defendant=null;
            party=null;
            content=null;
            for (int i = 0; i < inschema.length; i++) {
                inschemap.put(inschema[i], i);
            }
            for (int i = 0; i < outschema.length; i++) {
                outschemap.put(outschema[i], i);
            }
            //初始化
            Object[] values = new Object[args.length];
            content=args[inschemap.get("content")];
            for (int i = 0; i < args.length; i++) {
                if (Util.notEmpty(args[i])) {
                    args[i]=Util.full2Half(args[i].toString());
                    if("null".equals(args[i])) {
                        args[i] = null;
                    }
                }

                values[i] = args[i];
            }
            Object[] output = new Object[outschema.length];
            if(Util.notEmpty(values[inschemap.get("prosecutor")])) {
                prosecutor = Util.remove(values[inschemap.get("prosecutor")].toString());
            }
            if(Util.notEmpty(values[inschemap.get("defendant")])) {
                defendant = Util.remove(values[inschemap.get("defendant")].toString());
            }
            //清洗content
            if (Util.isEmpty(values[inschemap.get("source_from")])) {
                sourceFrom = null;
            } else {
                sourceFrom = values[inschemap.get("source_from")].toString();
            }
            //省份
            sourceFrom = null;
            if (!Util.isEmpty(values[inschemap.get("source_from")])) {
                sourceFrom = values[inschemap.get("source_from")].toString();
                if (sourceFrom.contains("网")) {
                    sourceFrom = null;
                }
            }
            String courtname=Util.isEmpty(values[inschemap.get("execute_gov")])?"":values[inschemap.get("execute_gov")].toString();

            //todo s1.单独从特殊区域当事人中清洗原告被告
            if (Util.notEmpty(values[inschemap.get("party")])) {
                party = values[inschemap.get("party")].toString();
                if("广东广州区县".equals(sourceFrom)||"云浮市中级人民法院".equals(courtname)||"广东省佛山市中级人民法院".equals(courtname)){
                    prosecutor="";
                    defendant="";
                    logger.info("party1:"+party);
                    for(String split:party.split(";")){
                        int extracttag=0;
                        for(String word2:"被上诉人|原审被告|被告|原审被告|被申请执行人|被申诉人|被申请人|债务人".split("\\|")){
                            if(split.contains(word2)){
                                String defendant1 = Util.parseRegex(split, "(被上诉人|原审被告|被告|原审被告|被申请执行人|被申诉人|被申请人|债务人)[:：]([^;； ]+)", 2);
                                defendant1=Util.deal_com_singlename(defendant1);
                                defendant=defendant+defendant1+",";
                                extracttag=1;
                                break;
                            }
                        }
                        if(extracttag==1) {
                            continue;
                        }
                        for(String word1:"上诉人|申诉人|原告|原审原告|申请执行人|申请人|债权人".split("\\|")){
                            if(split.contains(word1)){
                                String prosecutor1 = Util.parseRegex(split, "(上诉人|申诉人|原告|原审原告|申请执行人|申请人|债权人)[:：]([^;； ]+)", 2);
                                prosecutor1=Util.deal_com_singlename(prosecutor1);
                                prosecutor=prosecutor+prosecutor1+",";
                                break;
                            }
                        }

                    }
                    logger.info("party2:"+party);
                    logger.info("defendant:"+defendant+"---prosecutor"+prosecutor);
                    if(prosecutor.endsWith(",")){
                        prosecutor=prosecutor.substring(0,prosecutor.length()-1);
                    }
                    if(defendant.endsWith(",")){
                        defendant=defendant.substring(0,defendant.length()-1);
                    }

                }else if("广东揭阳".equals(sourceFrom)&&(party.contains(":")||party.contains("："))){
                    prosecutor="";
                    defendant="";
                    for(String split:party.split(",")){
                        int extracttag=0;
                        for(String word2:"被上诉人|原审被告|被告|原审被告|被申请执行人|被申诉人|被申请人|债务人".split("\\|")){
                            if(split.contains(word2)){
                                String defendant1 = Util.parseRegex(split, "(被上诉人|原审被告|被告|原审被告|被申请执行人|被申诉人|被申请人|债务人)[:：]([^;； ]+)", 2);
                                defendant1=Util.deal_com_singlename(defendant1);
                                defendant=defendant+defendant1+",";
                                extracttag=1;
                                break;
                            }
                        }
                        if(extracttag==1) {
                            continue;
                        }
                        for(String word1:"上诉人|申诉人|原告|原审原告|申请执行人|申请人|债权人".split("\\|")){
                            if(split.contains(word1)){
                                String prosecutor1 = Util.parseRegex(split, "(上诉人|申诉人|原告|原审原告|申请执行人|申请人|债权人)[:：]([^;； ]+)", 2);
                                prosecutor1=Util.deal_com_singlename(prosecutor1);
                                prosecutor=prosecutor+prosecutor1+",";
                                break;
                            }
                        }
                    }
                    if(prosecutor.endsWith(",")){
                        prosecutor=prosecutor.substring(0,prosecutor.length()-1);
                    }
                    if(defendant.endsWith(",")){
                        defendant=defendant.substring(0,defendant.length()-1);
                    }
                }else{
                        prosecutor = Util.parseRegex(party, "(上诉人|申诉人|原告|原审原告|申请执行人|申请人|债权人)[:：]([^;； ]+)", 2);
                        defendant = Util.parseRegex(party, "(被上诉人|原审被告|被告|原审被告|被申请执行人|被申诉人|被申请人|债务人)[:：]([^;； ]+)", 2);
                        if (Util.isEmpty(prosecutor) && Util.isEmpty(defendant) && party.contains("【")) {
                            party = party.replace("【", "").replace("】", "、").replace(" ", "");
                            if (party.endsWith("、、")) {
                                party = party.substring(0, party.length() - 2);
                            } else if (party.endsWith("、")) {
                                party = party.substring(0, party.length() - 1);
                            }
                        }
                        if (Util.isEmpty(prosecutor) && Util.isEmpty(defendant)) {
                            //深圳原告被告的处理
                            // 只处理原告和被告
                            String[] defendantWords = {"被告人 ：", "被告 ：", "被申请人 ：", "被申请执行人 ：", "被申请再审人 ："};
                            String[] prosecutorWords = {"原告人 ：", "原告 ：", "申请人 ：", "申请执行人 ：", "申请再审人 ："};
                            for (String string : party.replaceAll(":", "：").split("__spliter__")) {
                                if (string.indexOf("被告") >= 0 || string.indexOf("被申请") >= 0) {
                                    for (String dwords : defendantWords) {
                                        if (string.indexOf(dwords) >= 0) {
                                            if (string.indexOf(dwords) >= 0) {
                                                if (defendant != null && defendant.length() > 0) {
                                                    defendant = defendant + ",";
                                                } else {
                                                    defendant = "";
                                                }
                                                String singleName = string.substring(string.indexOf(dwords) + dwords.length()).trim()
                                                        .replace(" ", ",");
                                                singleName=Util.deal_com_singlename(singleName);
                                                defendant = defendant + singleName;
                                                continue;
                                            }
                                        }
                                    }

                                } else if (string.indexOf("原告") >= 0 || string.indexOf("申请") >= 0) {
                                    for (String pwords : prosecutorWords) {
                                        if (string.indexOf(pwords) >= 0) {
                                            if (prosecutor != null && prosecutor.length() > 0) {
                                                prosecutor = prosecutor + ",";
                                            } else {
                                                prosecutor = "";
                                            }
                                            String singleName = string.substring(string.indexOf(pwords) + pwords.length()).trim()
                                                    .replace(" ", ",");
                                            singleName=Util.deal_com_singlename(singleName);
                                            prosecutor = prosecutor + singleName;
                                            continue;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            sb.setLength(0);
            if (Util.notEmpty(prosecutor) ) {
                sb.append(prosecutor).append(",");
            }
            if(Util.notEmpty(defendant)){
                sb.append(defendant);
            }
            party=sb.toString();
            if(Util.notEmpty(party)&&party.endsWith(",")){
                party=party.substring(0,party.length()-1);
            }else{
                party=null;
            }
            //todo s2.单独从特殊区域清洗所有字段
            int cleanflag=0;
            if("广东东莞".equals(sourceFrom)){
                content_tmp=null;
                if(Util.notEmpty(values[inschemap.get("content")])&&values[inschemap.get("content")].toString().contains("案号案由适用程序当事人开庭地点承办部门审判长承办人书记员预计开庭时间预计结束时间")){
                    content_tmp=values[inschemap.get("content")].toString().replaceAll("案号案由适用程序当事人开庭地点承办部门审判长承办人书记员预计开庭时间预计结束时间","");

                    values[inschemap.get("case_no")] = CleanCourtUtil.getCaseNo(content_tmp);
                    content_tmp=content_tmp.replace(CleanCourtUtil.getCaseNo(content_tmp),"");
                    values[inschemap.get("case_reason")]=content_tmp.split("[0-9]")[0];
                    content_tmp=content_tmp.replace(content_tmp.split("[0-9]")[0],"");
                    Matcher matcher= Pattern.compile("(\\d\\d/\\d\\d/[\\d]{4} \\d\\d:\\d\\d)").matcher(content_tmp);
                    if(matcher.find()){
                        SimpleDateFormat sdf=new SimpleDateFormat("dd/MM/yyyy hh:mm");
                        try {
                            values[inschemap.get("open_time")]=String.valueOf(sdf.parse(matcher.group(1)).getTime());
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                    }
                    cleanflag=1;
                }

            }
            //todo 如果没有s2阶段（cleanflag==0）并且公告不为空,then执行规则清洗
            if (cleanflag==0&&Util.notEmpty(values[inschemap.get("content")])) {
                if(Pattern.compile("^、.*$|^[0-9]+、+\\t*.*$").matcher(content.toString()).matches()) {
                    content= content.toString().replaceFirst("[0-9]+、+\\t*|、", "");
                    values[inschemap.get("content")]=content;
                }
                if (Util.isEmpty(values[inschemap.get("execute_gov")])) {
                    values[inschemap.get("execute_gov")] = CleanCourtUtil.getExecuteGov(values[inschemap.get("content")].toString(), sourceFrom);
                }
                //执行法庭
                if (Util.isEmpty(values[inschemap.get("execute_unite")])) {
                    values[inschemap.get("execute_unite")] = CleanCourtUtil.getExecuteUnite(values[inschemap.get("content")].toString(), sourceFrom);
                }

                //开庭时间
                if (Util.isEmpty(values[inschemap.get("open_time")])) {
                    values[inschemap.get("open_time")] = CleanCourtUtil.getOpneTime(values[inschemap.get("content")].toString());
                }
                //被告
                if (Util.isEmpty(defendant)) {
                        defendant = CleanCourtUtil.getDefendant(values[inschemap.get("content")].toString());
                }
                //原告
                if (Util.isEmpty(prosecutor)) {
                    prosecutor = CleanCourtUtil.getProsecutor(values[inschemap.get("content")].toString());
                }
                //当事人
                if (Util.isEmpty(party)) {
                    party = CleanCourtUtil.getParty(values[inschemap.get("content")].toString());
                }
                if(values[inschemap.get("content")].toString().contains("小米科技有限责任公司诉沧州市东塑颐和商城有限公司")) {
                    prosecutor="小米科技有限责任公司";
                    defendant="沧州市东塑颐和商城有限公司";
                    party="小米科技有限责任公司,沧州市东塑颐和商城有限公司";
                }
                //案由
                if (Util.isEmpty(values[inschemap.get("case_reason")])) {
                    values[inschemap.get("case_reason")] = CleanCourtUtil.getCaseReason(values[inschemap.get("content")].toString());
                }
                //案号
                if (Util.isEmpty(values[inschemap.get("case_no")])) {
                    values[inschemap.get("case_no")] = CleanCourtUtil.getCaseNo(values[inschemap.get("content")].toString());
                }
            }


            delFlag = false;
            if (Util.notEmpty(values[inschemap.get("delflag")]) && Integer.parseInt(values[inschemap.get("delflag")].toString()) == 1) {
                delFlag = true;
                return;
            }

            //增加过滤条件：没有案由也没有案号的 数据过滤掉
            if (Util.isEmpty(values[inschemap.get("case_reason")]) && Util.isEmpty(values[inschemap.get("case_no")]) && Util.isEmpty(values[inschemap.get("open_time")])) {
                return;
            }
            //初始化
            //result = Util.iniReduceOutPutVal(result);

            //提取开庭时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
            try {
                output[outschemap.get("liandate")] = Util.isEmpty(values[inschemap.get("open_time")]) ? null : sdf.format(Util.getDate(values[inschemap.get("open_time")].toString()));
            } catch (Exception e) {
                output[outschemap.get("liandate")] = null;
            }
            //案由
            output[outschemap.get("casereason")] = values[inschemap.get("case_reason")];
            //原告/上诉人

            if (!Util.isEmpty(prosecutor)) {
                prosecutor = prosecutor.replace("null", "");
            }
            if (Util.isEmpty(prosecutor)) {
                prosecutor = null;
            }
            output[outschemap.get("prosecutorlist")] = prosecutor;
            //开放搜索字段
            output[outschemap.get("prosecutorlist_os")] = prosecutor;
            //被告/被上诉人
            if (!Util.isEmpty(defendant)) {
                defendant = defendant.replace("null", "");
            }
            if (Util.isEmpty(defendant)) {
                defendant = null;
            }
            output[outschemap.get("defendantlist")] = defendant;
            //开放搜索字段
            output[outschemap.get("defendantlist_os")] = defendant;
            //法院
            execute_gov = null;
            if (!Util.isEmpty(values[inschemap.get("execute_gov")])) {
                execute_gov = values[inschemap.get("execute_gov")].toString().replace("*", "");
            }
            output[outschemap.get("executegov")] = execute_gov;
            //案号
            output[outschemap.get("caseno")] = values[inschemap.get("case_no")];
            //开庭日期的年
            cal = Calendar.getInstance();
            try {
                //courtyear = cal.setTime(maxRecord.getDatetime("liandate"));
                cal.setTime(sdf.parse(output[outschemap.get("liandate")].toString()));
                courtyear = cal.get(Calendar.YEAR);
                output[outschemap.get("courtyear")] = courtyear;
            } catch (Exception e) {
                output[outschemap.get("courtyear")] = null;
            }
            //类型（暂时定为5）
            output[outschemap.get("type")] = Long.valueOf(Constants.COURT_NOTICE_TYPE);

            output[outschemap.get("province")] = sourceFrom;

            //公司名称和id(调用以前的公司曾用名接口，查询公司的包含id，这个任务值为空，另有任务去关联公司名)
            output[outschemap.get("companynames")] = "";
            output[outschemap.get("isvalid")] = values[inschemap.get("isvalid")];

            //详情字段
            //法庭
            output[outschemap.get("execute_unite")] = values[inschemap.get("execute_unite")];
            //排期
            output[outschemap.get("schedule_time")] = Util.isEmpty(values[inschemap.get("schedule_time")]) ? null : sdf.format(Util.getDate(values[inschemap.get("schedule_time")].toString()));
            //承办部门
            output[outschemap.get("undertake_department")] = values[inschemap.get("undertake_department")];
            //审判长/主审人
            if (Util.isEmpty(values[inschemap.get("chief_judge")])) {
                output[outschemap.get("chief_judge")] = values[inschemap.get("chief_judge")];
            } else {
                output[outschemap.get("chief_judge")] = values[inschemap.get("chief_judge")].toString().replace("null", "");
            }
            //created_date
            output[outschemap.get("created_date")] = Util.isEmpty(values[inschemap.get("created_date")]) ? null : values[inschemap.get("created_date")].toString().substring(0, 19);
            //updated_date
            output[outschemap.get("updated_date")] = Util.isEmpty(values[inschemap.get("updated_date")]) ? null : values[inschemap.get("updated_date")].toString().substring(0, 19);
            output[outschemap.get("party")] = party;
            output[outschemap.get("datatype")] = values[inschemap.get("datatype")];
            output[outschemap.get("key_no")] = values[inschemap.get("annoid")];
            output[outschemap.get("content")] = content;
            output[outschemap.get("is_struct")]="true";
            if(Util.notEmpty(content)) {
                output[outschemap.get("partycontext")] = CleanCourtUtil.getPartyContext(content.toString());
            }
            if (Util.isEmpty(output[outschemap.get("executegov")])
                    && Util.isEmpty(output[outschemap.get("execute_unite")])
                    && Util.isEmpty(output[outschemap.get("liandate")])
                    && Util.isEmpty(output[outschemap.get("prosecutorlist_os")])
                    && Util.isEmpty(output[outschemap.get("defendantlist_os")]) ) {
                return;
            }
            if(Util.isEmpty(output[outschemap.get("prosecutorlist_os")]) && Util.isEmpty(output[outschemap.get("defendantlist_os")])){
                output[outschemap.get("id")] = MD5Util.ecodeByMD5(
                        (Util.isEmpty(output[outschemap.get("caseno")]) ? "" : output[outschemap.get("caseno")].toString())
                                + (Util.isEmpty(output[outschemap.get("casereason")]) ? "" : output[outschemap.get("casereason")].toString())
                                + (Util.isEmpty(output[outschemap.get("executegov")]) ? "" : output[outschemap.get("executegov")].toString())
                                + (Util.isEmpty(output[outschemap.get("execute_unite")]) ? "" : output[outschemap.get("execute_unite")].toString())
                                + (Util.isEmpty(output[outschemap.get("liandate")]) ? "" : output[outschemap.get("liandate")].toString())
                                + (Util.isEmpty(output[outschemap.get("content")]) ? "" : output[outschemap.get("content")].toString())
                ) + Constants.COURT_NOTICE_TYPE;
            }else{
                output[outschemap.get("id")] = MD5Util.ecodeByMD5(
                        (Util.isEmpty(output[outschemap.get("caseno")]) ? "" : output[outschemap.get("caseno")].toString())
                                + (Util.isEmpty(output[outschemap.get("casereason")]) ? "" : output[outschemap.get("casereason")].toString())
                                + (Util.isEmpty(output[outschemap.get("executegov")]) ? "" : output[outschemap.get("executegov")].toString())
                                + (Util.isEmpty(output[outschemap.get("execute_unite")]) ? "" : output[outschemap.get("execute_unite")].toString())
                                + (Util.isEmpty(output[outschemap.get("liandate")]) ? "" : output[outschemap.get("liandate")].toString())
                                + (Util.isEmpty(output[outschemap.get("prosecutorlist_os")]) ? "" : output[outschemap.get("prosecutorlist_os")].toString())
                                + (Util.isEmpty(output[outschemap.get("defendantlist_os")]) ? "" : output[outschemap.get("defendantlist_os")].toString())
                ) + Constants.COURT_NOTICE_TYPE;
            }

            output[outschemap.get("name")]=CleanCourtRelatedNames(prosecutor,defendant,party);
            forward(output);
            /**
             * 说明：公司名要包含原告、被告，多个公司名以逗号隔开，
             * 又考虑到别的任务要关联公司名，
             * 所以采用一个公司名称输出一条记录的做法，
             * 在关联公司名的任务中进行整合
             */

        } catch (Exception e) {


        }
    }


    @Override
    public StructObjectInspector initialize(ObjectInspector[] args)
            throws UDFArgumentException {
        if (args.length != 20) {
            throw new UDFArgumentLengthException(
                    "Courtnotice_filterUDTF takes only 20 argument");
        }
        ArrayList<String> fieldNames = new ArrayList<String>();
        ArrayList<ObjectInspector> fieldOIs = new ArrayList<ObjectInspector>();
        for(int i=0;i<outschema.length;i++){
            fieldNames.add(outschema[i].toString());
            fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        }
        return ObjectInspectorFactory.getStandardStructObjectInspector(
                fieldNames, fieldOIs);
    }

    static String CleanCourtRelatedNames( String prosecutor, String defendant, String party){
        Set<String> nameset = new TreeSet<>();
        nameset.clear();
        if (!Util.isEmpty(prosecutor)) {
            String key = prosecutor;
            String s;
            for (String name : Util.split(key)) {
                //处理尾部为等的名称:王燕等
                if (!Util.isEmpty(name) && name.endsWith("等")) {
                    name = name.substring(0, name.length() - 1);
                }

                //处理类似：张涌立与李红杰
                if (!Util.isEmpty(name) && name.contains("与")) {
                    if (name.contains(s = "与") || name.contains(s = ",") || name.contains(s = "、")) {
                        for (String str : name.split(s)) {
                            nameset.add(str);
                        }
                    }
                } else {
                    nameset.add(name);
                }

            }
        }
        if (!Util.isEmpty(defendant)) {
            String key = defendant;
            String s;
            for (String name : Util.split(key)) {
                //处理尾部为等的名称:王燕等
                if (!Util.isEmpty(name) && name.endsWith("等")) {
                    name = name.substring(0, name.length() - 1);
                }

                //处理类似：张涌立与李红杰
                if (!Util.isEmpty(name) && name.contains("与")) {
                    if (name.contains(s = "与") || name.contains(s = ",") || name.contains(s = "、")) {
                        for (String str : name.split(s)) {
                            nameset.add(str);
                        }
                    }
                } else {
                    nameset.add(name);
                }
            }
        }
        if(defendant==null&&prosecutor==null &&party!=null){
            String key = party;
            String s;
            for (String name : Util.split(key)) {
                //处理尾部为等的名称:王燕等
                if (!Util.isEmpty(name) && name.endsWith("等")) {
                    name = name.substring(0, name.length() - 1);
                }

                //处理类似：张涌立与李红杰
                if (!Util.isEmpty(name) && name.contains("与")) {
                    if (name.contains(s = "与") || name.contains(s = ",") || name.contains(s = "、")) {
                        for (String str : name.split(s)) {
                            nameset.add(str);
                        }
                    }
                } else {
                    nameset.add(name);
                }
            }
        }
        if(nameset.isEmpty()){
            return strNull;
        }else{
            return nameset.toString().
                    replaceAll("\\[|\\]", "").
                    replaceAll(", ", ",");//去除空格
        }
    }
/*
    public static void main(String[] args) throws HiveException {
        String str="1、\t本院定于二〇一八年一月十七日15:35到17:30在陈家桥法庭第七审判庭开庭审理(2017)渝0106民初14441号原告重庆市中铨汽车零部件有限责任公司诉被告东风小康汽车有限公司买卖合同纠纷一案。\t\t\t重庆市沙坪坝区人民法院 承办人：张振华\n";
        System.out.println(str.replaceFirst("[0-9]+、+\\t*",""));

    }*/
}