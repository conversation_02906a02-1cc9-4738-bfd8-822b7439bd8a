package com.qcc.udf.liu;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RemoveIdCard2 extends UDF {

    private final static String REGEXP_BIRTH_ALL_DAY = "(\\d{2,4}年|[零一二三四五六七八九十\\d]{4}年)(\\d{1,2}月|[一二三四五六七八九十]{1,2}月)(\\d{1,2}|[零一二三四五六七八九十\\d]{1,3})";
    private final static String REGEXP_BIRTH_WITHOUT_DAY = "(\\d{2,4}年|[零一二三四五六七八九十\\d]{4}年)(\\d{1,2}月|[一二三四五六七八九十]{1,2}月)";
    private final static String IDENTIFICATION_CARD_REGEX = "[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]";

    public static String evaluate(String info) throws Exception{
        if (StringUtils.isEmpty(info)){
            return "";
        }else{
            return replaceIDCardInfo(info);
        }
    }

    public static String replaceIDCardInfo(String content) {
        if (!StringUtils.isEmpty(content)) {
            if (!StringUtils.isEmpty(content)) {
                if (Pattern.compile(REGEXP_BIRTH_ALL_DAY).matcher(content).find()) {
                    content = content
                            .replaceAll("生于"+REGEXP_BIRTH_ALL_DAY, "生于****年**月**")
                            .replaceAll(REGEXP_BIRTH_ALL_DAY+"日出生", "****年**月**日出生")
                            .replaceAll(REGEXP_BIRTH_ALL_DAY+"号出生", "****年**月**号出生")
                            .replaceAll(REGEXP_BIRTH_ALL_DAY+"日生", "****年**月**日生")
                            .replaceAll(REGEXP_BIRTH_ALL_DAY+"号生", "****年**月**号生")
                            .replaceAll("出生日期"+REGEXP_BIRTH_ALL_DAY, "出生日期****年**月**");
                } else if (Pattern.compile(REGEXP_BIRTH_WITHOUT_DAY).matcher(content).find()) {
                    content = content
                            .replaceAll("生于"+REGEXP_BIRTH_WITHOUT_DAY, "生于****年**月")
                            .replaceAll(REGEXP_BIRTH_WITHOUT_DAY+"出生", "****年**月出生")
                            .replaceAll(REGEXP_BIRTH_WITHOUT_DAY+"出于", "****年**月出于")
                            .replaceAll(REGEXP_BIRTH_WITHOUT_DAY+"生", "****年**月生")
                            .replaceAll("出生日期"+REGEXP_BIRTH_WITHOUT_DAY, "出生日期****年**月");
                }
            }

            Pattern pattern = Pattern.compile(IDENTIFICATION_CARD_REGEX);
            Matcher matcher = pattern.matcher(content);
            if (matcher.find()) {
                int cnt = matcher.groupCount();
                for (int i = 0; i < cnt ; i++){
                    content = content.replaceAll(matcher.group(), "***");
                    Matcher matcher1 = pattern.matcher(content);
                    if (matcher1.find()){
                        content = content.replaceAll(matcher1.group(), "***");
                    }
                }
            }
        }
        return content;
    }

    public static void main(String[] args) {
        try {
            System.out.println(evaluate("ank\">管美玲</a>，女，汉族 身份证号码：370728197911236824  电话：15763697922 地址：诸城市密州街道东杨家岭村华能金昊。</span></p><p style=\"margif"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
