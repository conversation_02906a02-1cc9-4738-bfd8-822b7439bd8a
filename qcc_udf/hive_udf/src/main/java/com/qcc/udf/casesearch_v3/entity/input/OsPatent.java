package com.qcc.udf.casesearch_v3.entity.input;

import com.alibaba.fastjson.annotation.JSONField;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2018/9/14 14:36
 * @Description:
 */
@Data
public class OsPatent {
    @JSONField(name = "Id")
    private String id;
    @JSONField(name = "PublicationDate")
    private Long publicationDate;
    @JSONField(name = "ApplicationDate")
    private Long applicationDate;
    @JSONField(name = "PublicationNumber")
    private String publicationNumber;
    @JSONField(name = "ApplicationNumber")
    private String applicationNumber;
    @JSONField(name = "InventorList")
    private String inventorList;
    @JSONField(name = "AssigneeList")
    private String assigneeList;
    @JSONField(name = "KindCode")
    private String kindCode;
    @JSONField(name = "Title")
    private String title;
    @JSONField(name = "Agency")
    private String agency;
   /* @J<PERSON><PERSON>ield(name = "IpcList")
    private String ipcList;*/
//    @J<PERSON><PERSON>ield(name = "Cites")
//    private String cites;
    @JSONField(name = "Images")
    private String images;
    @JSONField(name = "LegalStatus")
    private String legalStatus;
//    @JSONField(name = "LegalStatusDate")
//    private LocalDateTime legalStatusDate;
    @JSONField(name = "Type")
    private Integer type = 4;
    @JSONField(name = "CompanyKeywords")
    private String companyKeywords;

    /**
     * 历史专利所有权人KeyNo（不包含当前专利权人）
     */
//    @JSONField(name = "HistoryKeyNos")
//    private String historyKeyNos;

//    @JSONField(name = "publicationyear")
//    private Integer publicationyear;
//    @JSONField(name = "AppDateKind")
//    private String appDateKind;
//    @JSONField(name = "PubDateKind")
//    private String pubDateKind;
    @JSONField(name = "NameAndKeyNo")
    private String nameAndKeyNo;
    @JSONField(name = "IsValid")
    private Integer isValid = 1;

    /**
     * 法律状态两级分类（用英文逗号间隔）
     */
    @JSONField(name = "Status")
    private String status;

    /**
     * 所属公司集团Id
     */
//    @JSONField(name = "GroupId")
//    private String groupId = "";

    /**
     * 是否使用标志
     */
    @JSONField(name = "IsUse")
    private Integer isUse;



}
