package com.qcc.udf.casesearch_v3;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

/**
 * <AUTHOR>
 */
public class getSxAmt extends UDF {
    public static String evaluate(String param) {
        JSONArray result = new JSONArray();

        if (StringUtils.isNotEmpty(param)){
            Map<String, String> sxMap = new LinkedHashMap<>();
            JSONArray array = JSONObject.parseObject(param).getJSONArray("InfoList");

            // 1.寻找同案号的被执行金额数据
            Set<String> addIdSet = new LinkedHashSet<>();
            if (array != null && !array.isEmpty()){
                Iterator<Object> it = array.iterator();
                while (it.hasNext()){
                    // 待补充的失信数据
                    Map<String, String> sxIdMap = new LinkedHashMap<>();
                    JSONObject json = (JSONObject) it.next();
                    getInfoFromArray(json.getJSONArray("SxList"), sxIdMap);

                    // 同案号被执行/终本数据
                    Map<String, String> amtMap = new LinkedHashMap<>();
                    getAmtFromList(json.getJSONArray("ZbList"), "FailureAct", amtMap);
                    getAmtFromList(json.getJSONArray("ZxList"), "Biaodi", amtMap);

                    if (sxIdMap.size() > 0 && amtMap.size() > 0){
                        Set<String> keySet = sxIdMap.keySet();
                        for (String str : keySet){
                            String keyNo = sxIdMap.get(str);
                            if (amtMap.containsKey(keyNo) && !addIdSet.contains(str)){
                                JSONObject item = new JSONObject();
                                item.put("Id", str);
                                item.put("Amt", amtMap.get(keyNo));

                                if(!"0".equals(amtMap.get(keyNo))){
                                    result.add(item);
                                }

                                addIdSet.add(str);
                            }
                        }
                    }
                }
            }

            // 2.寻找案件金额
            // 获取被执行/终本的所有数据
            if (array != null && !array.isEmpty()){
                Iterator<Object> it = array.iterator();
                while (it.hasNext()){
                    JSONObject json = (JSONObject) it.next();
                    getInfoFromArray(json.getJSONArray("SxList"), sxMap);
                }
            }
            if (sxMap.size() > 0){
                // 案件金额
                Map<String, JSONObject> amtMap = JSONObject.parseObject(JSONObject.parseObject(param).getString("AmtInfo"), Map.class);
                if (amtMap != null && !amtMap.isEmpty() && amtMap.size() > 0){
                    Set<String> keySet = amtMap.keySet();

                    Set<String> sxSet = sxMap.keySet();
                    for (String str : sxSet){
                        String keyNo = sxMap.get(str);
                        if (keySet.contains(keyNo) && !addIdSet.contains(str)){
                            JSONObject json = new JSONObject();
                            json.put("Id", str);
                            json.put("Amt", amtMap.get(keyNo).getString("Amt"));

                            result.add(json);

                            addIdSet.add(str);
                        }
                    }
                }
            }

        }

        return result.toString();
    }

    public static void getInfoFromArray(JSONArray array, Map<String, String> infoMap){
        if (array != null && !array.isEmpty() && array.size() > 0){
            Iterator<Object> it = array.iterator();
            while (it.hasNext()){
                JSONObject json = (JSONObject) it.next();

                JSONArray jsonArray = json.getJSONArray("NameAndKeyNo");
                if (jsonArray != null && jsonArray.size() > 0){
                    Iterator<Object> subIt = jsonArray.iterator();
                    while(subIt.hasNext()){
                        JSONObject nameJson = (JSONObject)subIt.next();
                        String keyNo = StringUtils.isEmpty(nameJson.getString("KeyNo")) ? nameJson.getString("Name") : nameJson.getString("KeyNo");

                        if (StringUtils.isNotEmpty(keyNo)){
                            infoMap.put(json.getString("Id"), keyNo);
                        }

                    }
                }
            }
        }
    }

    public static void getAmtFromList(JSONArray array, String field, Map<String, String> infoMap){
        if (array != null && !array.isEmpty() && array.size() > 0){
            Iterator<Object> it = array.iterator();
            while (it.hasNext()){
                JSONObject json = (JSONObject) it.next();

                if (StringUtils.isNotEmpty(json.getString(field))){
                    JSONArray jsonArray = json.getJSONArray("NameAndKeyNo");
                    if (jsonArray != null && jsonArray.size() > 0){
                        Iterator<Object> subIt = jsonArray.iterator();
                        while(subIt.hasNext()){
                            JSONObject nameJson = (JSONObject)subIt.next();
                            String keyNo = StringUtils.isEmpty(nameJson.getString("KeyNo")) ? nameJson.getString("Name") : nameJson.getString("KeyNo");

                            if (StringUtils.isNotEmpty(keyNo) && !infoMap.containsKey(keyNo)){
                                infoMap.put(keyNo, json.getString(field));
                            }
                        }
                    }
                }
            }
        }
    }

    public static void main(String[] args) {
        System.out.println(evaluate("{\"KtggCnt\":1,\"LastestDateType\":\"恢复执行|限制高消费发布日期\",\"XjpgCnt\":0,\"ZxCnt\":6,\"CfgsCnt\":0,\"LastestDate\":1624982400,\"AmtInfo\":{\"p512019a2985e81150796e736753134a\":{\"Type\":\"执行标的\",\"Amt\":\"120000\",\"IsValid\":\"0\"},\"p59cd7a2a0917ae82a1328de715f9f61\":{\"Type\":\"执行标的\",\"Amt\":\"120000\",\"IsValid\":\"0\"}},\"EarliestDate\":1583890380,\"Source\":\"OT\",\"AnnoCnt\":3,\"EarliestDateType\":\"民事一审|开庭时间\",\"XzcfCnt\":0,\"CompanyKeywords\":\"***,p512019a2985e81150796e736753134a,p59cd7a2a0917ae82a1328de715f9f61,刘梅,熊远志,蔡克选,陈静\",\"AnNoList\":\"（2020）陕1026执349号,（2020）陕1026民初51号,（2021）陕1026执恢10号\",\"GqdjCnt\":0,\"XgCnt\":6,\"Tags\":\"1,2,3,4,11\",\"FyggCnt\":0,\"ZbCnt\":0,\"LatestTrialRound\":\"恢复执行\",\"CfdfCnt\":0,\"CaseName\":\"蔡克选与刘梅,熊远志,陈静买卖合同纠纷的案件\",\"CfxyCnt\":0,\"SxCnt\":6,\"Province\":\"SAX\",\"GroupId\":\"153d0196ca59b58f4004372fad7543a1\",\"LianCnt\":0,\"CaseCnt\":3,\"HbcfCnt\":0,\"PcczCnt\":0,\"Type\":1,\"CaseType\":\"执行案件,民事案件\",\"ProcuratorateList\":\"\",\"CaseRole\":\"[{\\\"D\\\":\\\"一审原告,首次执行申请执行人,恢复执行申请执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2,\\\"P\\\":\\\"蔡克选\\\",\\\"R\\\":\\\"原告\\\",\\\"RL\\\":[{\\\"R\\\":\\\"原告\\\",\\\"T\\\":\\\"一审\\\"},{\\\"R\\\":\\\"申请执行人\\\",\\\"T\\\":\\\"首次执行\\\"},{\\\"R\\\":\\\"申请执行人\\\",\\\"T\\\":\\\"恢复执行\\\"}]},{\\\"D\\\":\\\"一审被告,首次执行被执行人,恢复执行被执行人\\\",\\\"N\\\":\\\"p59cd7a2a0917ae82a1328de715f9f61\\\",\\\"O\\\":2,\\\"P\\\":\\\"刘梅\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"},{\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"首次执行\\\"},{\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"恢复执行\\\"}]},{\\\"D\\\":\\\"一审被告,首次执行被执行人,恢复执行被执行人\\\",\\\"N\\\":\\\"p512019a2985e81150796e736753134a\\\",\\\"O\\\":2,\\\"P\\\":\\\"熊远志\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"},{\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"首次执行\\\"},{\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"恢复执行\\\"}]},{\\\"D\\\":\\\"一审被告\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2,\\\"P\\\":\\\"陈静\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"}]}]\",\"CaseReason\":\"买卖合同纠纷\",\"CourtList\":\"陕西省商洛市柞水县人民法院\",\"Id\":\"13b819a8c275972bfb54674376567d62\",\"InfoList\":[{\"Defendant\":[{\"KeyNo\":\"\",\"Role\":\"被执行人\",\"Org\":-2,\"Name\":\"***\"},{\"KeyNo\":\"p59cd7a2a0917ae82a1328de715f9f61\",\"Role\":\"被执行人\",\"Org\":2,\"Name\":\"刘梅\"},{\"KeyNo\":\"p512019a2985e81150796e736753134a\",\"Role\":\"被执行人\",\"Org\":2,\"Name\":\"熊远志\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"陕西省商洛市柞水县人民法院\",\"LatestTimestamp\":1600358400,\"ZxList\":[{\"LianDate\":1591200000,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"蔡克选\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"p59cd7a2a0917ae82a1328de715f9f61\",\"Org\":2,\"Name\":\"刘梅\"}],\"Id\":\"000009178f041c4dc2853ee8a0b84bc31\",\"Biaodi\":\"141690\",\"IsValid\":0},{\"LianDate\":1591200000,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"蔡克选\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"陈静\"}],\"Id\":\"60ea52f0a6f398308dbd89baa96e314b1\",\"Biaodi\":\"141690\",\"IsValid\":0},{\"LianDate\":1591200000,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"蔡克选\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"p512019a2985e81150796e736753134a\",\"Org\":2,\"Name\":\"熊远志\"}],\"Id\":\"ba1393b3b0a01a7edcfc2ab00b4b4e601\",\"Biaodi\":\"141690\",\"IsValid\":0}],\"XzcffList\":[],\"CfdfList\":[],\"HbcfList\":[],\"CaseList\":[{\"CaseType\":\"执行裁定书\",\"JudgeDate\":1600358400,\"Amt\":\"\",\"Id\":\"31a26f3506b38b6ee0573755c9cdb6000\",\"ResultType\":\"裁定结果\",\"DocType\":\"裁定日期\",\"IsValid\":1,\"Result\":\"终结（2020）陕1026执349号案件的执行。\",\"ShieldCaseFlag\":0}],\"TrialRound\":\"首次执行\",\"Prosecutor\":[{\"KeyNo\":\"\",\"Role\":\"申请执行人\",\"Org\":-2,\"Name\":\"蔡克选\"}],\"ZbList\":[],\"ExecuteNo\":\"（2020）陕1026民初51号\",\"SxList\":[{\"PublishDate\":1596556800,\"ActionType\":\"违反财产报告制度\",\"NameAndKeyNo\":[{\"KeyNo\":\"p59cd7a2a0917ae82a1328de715f9f61\",\"Org\":2,\"Name\":\"刘梅\"}],\"ExecuteStatus\":\"全部未履行\",\"Id\":\"000009178f041c4dc2853ee8a0b84bc32\",\"IsValid\":0},{\"PublishDate\":1596988800,\"ActionType\":\"违反财产报告制度\",\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"陈静\"}],\"ExecuteStatus\":\"全部未履行\",\"Id\":\"60ea52f0a6f398308dbd89baa96e314b2\",\"IsValid\":0},{\"PublishDate\":1596556800,\"ActionType\":\"违反财产报告制度\",\"NameAndKeyNo\":[{\"KeyNo\":\"p512019a2985e81150796e736753134a\",\"Org\":2,\"Name\":\"熊远志\"}],\"ExecuteStatus\":\"全部未履行\",\"Id\":\"ba1393b3b0a01a7edcfc2ab00b4b4e602\",\"IsValid\":0}],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2020）陕1026执349号\",\"CaseType\":\"执行案件\",\"LianList\":[],\"XgList\":[{\"PublishDate\":1592841600,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"蔡克选\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"陈静\"}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"陈静\"}],\"Id\":\"1d48e4595ab229004593408210de1fac\",\"CompanyInfo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"陈静\"}],\"GlNameAndKeyNo\":[],\"IsValid\":0},{\"PublishDate\":1592841600,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"蔡克选\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"p59cd7a2a0917ae82a1328de715f9f61\",\"Org\":2,\"Name\":\"刘梅\"}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"p59cd7a2a0917ae82a1328de715f9f61\",\"Org\":2,\"Name\":\"刘梅\"}],\"Id\":\"3f33cb20b2cfdd3a08b368cec76146e5\",\"CompanyInfo\":[{\"KeyNo\":\"p59cd7a2a0917ae82a1328de715f9f61\",\"Org\":2,\"Name\":\"刘梅\"}],\"GlNameAndKeyNo\":[],\"IsValid\":0},{\"PublishDate\":1592841600,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"蔡克选\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"p512019a2985e81150796e736753134a\",\"Org\":2,\"Name\":\"熊远志\"}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"p512019a2985e81150796e736753134a\",\"Org\":2,\"Name\":\"熊远志\"}],\"Id\":\"834464ad531b9e56714d6c5b7adb26b0\",\"CompanyInfo\":[{\"KeyNo\":\"p512019a2985e81150796e736753134a\",\"Org\":2,\"Name\":\"熊远志\"}],\"GlNameAndKeyNo\":[],\"IsValid\":0}],\"CaseReason\":\"买卖合同纠纷案件执行\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]},{\"Defendant\":[{\"KeyNo\":\"p59cd7a2a0917ae82a1328de715f9f61\",\"Role\":\"被告\",\"Org\":2,\"Name\":\"刘梅\"},{\"KeyNo\":\"p512019a2985e81150796e736753134a\",\"Role\":\"被告\",\"Org\":2,\"Name\":\"熊远志\"},{\"KeyNo\":\"\",\"Role\":\"被告\",\"Org\":-2,\"Name\":\"陈静\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"陕西省商洛市柞水县人民法院\",\"LatestTimestamp\":1583942400,\"ZxList\":[],\"XzcffList\":[],\"CfdfList\":[],\"HbcfList\":[],\"CaseList\":[{\"CaseType\":\"民事调解书\",\"JudgeDate\":1583942400,\"Amt\":\"\",\"Id\":\"9b2f3f32eab9d567948183ae177e5a8f0\",\"ResultType\":\"调解结果\",\"DocType\":\"调解日期\",\"IsValid\":1,\"Result\":\"\",\"ShieldCaseFlag\":1}],\"TrialRound\":\"民事一审\",\"Prosecutor\":[{\"KeyNo\":\"\",\"Role\":\"原告\",\"Org\":-2,\"Name\":\"蔡克选\"}],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2020）陕1026民初51号\",\"CaseType\":\"民事案件\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"买卖合同纠纷\",\"KtggList\":[{\"ExecuteUnite\":\"第三审判庭\",\"OpenDate\":1583890380,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"蔡克选\"},{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"陈静\"},{\"KeyNo\":\"p59cd7a2a0917ae82a1328de715f9f61\",\"Org\":2,\"Name\":\"刘梅\"},{\"KeyNo\":\"p512019a2985e81150796e736753134a\",\"Org\":2,\"Name\":\"熊远志\"}],\"Id\":\"3ad08a638e58a23fb91a4461cf9fee82\",\"IsValid\":1}],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]},{\"Defendant\":[{\"KeyNo\":\"\",\"Role\":\"被执行人\",\"Org\":-2,\"Name\":\"***\"},{\"KeyNo\":\"p59cd7a2a0917ae82a1328de715f9f61\",\"Role\":\"被执行人\",\"Org\":2,\"Name\":\"刘梅\"},{\"KeyNo\":\"p512019a2985e81150796e736753134a\",\"Role\":\"被执行人\",\"Org\":2,\"Name\":\"熊远志\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"陕西省商洛市柞水县人民法院\",\"LatestTimestamp\":1624982400,\"ZxList\":[{\"LianDate\":1611676800,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"蔡克选\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"陈静\"}],\"Id\":\"3c08d9826cbd23941bfddb290cfc0cc61\",\"Biaodi\":\"120000\",\"IsValid\":0},{\"LianDate\":1611676800,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"蔡克选\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"p59cd7a2a0917ae82a1328de715f9f61\",\"Org\":2,\"Name\":\"刘梅\"}],\"Id\":\"949ca77778a71dfd97abb6c4ad8afa041\",\"Biaodi\":\"120000\",\"IsValid\":0},{\"LianDate\":1611676800,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"蔡克选\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"p512019a2985e81150796e736753134a\",\"Org\":2,\"Name\":\"熊远志\"}],\"Id\":\"96d49746bb72267d73eb0ed6269185941\",\"Biaodi\":\"120000\",\"IsValid\":0}],\"XzcffList\":[],\"CfdfList\":[],\"HbcfList\":[],\"CaseList\":[{\"CaseType\":\"执行裁定书\",\"JudgeDate\":1624982400,\"Amt\":\"\",\"Id\":\"784dc74a590a785fb4fc07df6f841d7c0\",\"ResultType\":\"裁定结果\",\"DocType\":\"裁定日期\",\"IsValid\":1,\"Result\":\"终结本次执行程序。\",\"ShieldCaseFlag\":0}],\"TrialRound\":\"恢复执行\",\"Prosecutor\":[{\"KeyNo\":\"\",\"Role\":\"申请执行人\",\"Org\":-2,\"Name\":\"蔡克选\"}],\"ZbList\":[],\"ExecuteNo\":\"（2020）陕1026民初51号\",\"SxList\":[{\"PublishDate\":1621267200,\"ActionType\":\"违反财产报告制度\",\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"陈静\"}],\"ExecuteStatus\":\"全部未履行\",\"Id\":\"3c08d9826cbd23941bfddb290cfc0cc62\",\"IsValid\":1},{\"PublishDate\":1621267200,\"ActionType\":\"违反财产报告制度\",\"NameAndKeyNo\":[{\"KeyNo\":\"p59cd7a2a0917ae82a1328de715f9f61\",\"Org\":2,\"Name\":\"刘梅\"}],\"ExecuteStatus\":\"全部未履行\",\"Id\":\"949ca77778a71dfd97abb6c4ad8afa042\",\"IsValid\":1},{\"PublishDate\":1621267200,\"ActionType\":\"隐匿财产规避执行,违反财产报告制度\",\"NameAndKeyNo\":[{\"KeyNo\":\"p512019a2985e81150796e736753134a\",\"Org\":2,\"Name\":\"熊远志\"}],\"ExecuteStatus\":\"全部未履行\",\"Id\":\"96d49746bb72267d73eb0ed6269185942\",\"IsValid\":1}],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2021）陕1026执恢10号\",\"CaseType\":\"执行案件\",\"LianList\":[],\"XgList\":[{\"PublishDate\":1624982400,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"蔡克选\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"陈静\"}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"陈静\"}],\"Id\":\"149f6efc007f3d38a15b0020567d5c4c\",\"CompanyInfo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"陈静\"}],\"GlNameAndKeyNo\":[],\"IsValid\":1},{\"PublishDate\":1624982400,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"蔡克选\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"p59cd7a2a0917ae82a1328de715f9f61\",\"Org\":2,\"Name\":\"刘梅\"}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"p59cd7a2a0917ae82a1328de715f9f61\",\"Org\":2,\"Name\":\"刘梅\"}],\"Id\":\"d53d810040907c24d442a5dc51b8c90c\",\"CompanyInfo\":[{\"KeyNo\":\"p59cd7a2a0917ae82a1328de715f9f61\",\"Org\":2,\"Name\":\"刘梅\"}],\"GlNameAndKeyNo\":[],\"IsValid\":1},{\"PublishDate\":1624982400,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"蔡克选\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"p512019a2985e81150796e736753134a\",\"Org\":2,\"Name\":\"熊远志\"}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"p512019a2985e81150796e736753134a\",\"Org\":2,\"Name\":\"熊远志\"}],\"Id\":\"dcedd8e5d963c5f83571692552b24897\",\"CompanyInfo\":[{\"KeyNo\":\"p512019a2985e81150796e736753134a\",\"Org\":2,\"Name\":\"熊远志\"}],\"GlNameAndKeyNo\":[],\"IsValid\":1}],\"CaseReason\":\"买卖合同纠纷案件执行\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]}],\"SdggCnt\":0} "));
    }
}
