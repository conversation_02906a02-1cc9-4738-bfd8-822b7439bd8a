package com.qcc.udf.lawer;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * <AUTHOR>
 * @date ：Created in 2021/09/13 18:52
 * @description ：
 */
public class GetUsefulDataWithAuth extends UDF {

    public static String evaluate(String param, String defaultData, Integer auth) {
        String result = "";
        if (StringUtils.isEmpty(defaultData)) {
            defaultData = "";
        }
        result = defaultData;
        if (StringUtils.isEmpty(param)) {
            return result;
        }
        String[] paramStr = param.split(",");
        //如果是认证的，则取第一个值
        if (auth != null && auth.intValue() == 1) {
            result = paramStr[0];
        } else {
            for(String item: paramStr) {
                if (StringUtils.isNotEmpty(item) && !item.equals(defaultData)) {
                    result = item;
                    break;
                }
            }
        }
        return result;
    }


    public static void main(String[] args) {
        String param = ",2,3,4";
        String defaultData = "1";
        Integer auth = null;
        System.out.println("值为=" + evaluate(param,defaultData,auth));
    }

}
