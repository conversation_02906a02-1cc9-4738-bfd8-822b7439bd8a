package com.qcc.udf.casesearch_v3.entity.input;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.qcc.udf.casesearch_v3.enums.CaseCategoryEnum;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: zhangqiang
 * @Date: 2020/11/11 17:54
 * @Description:股权冻结
 */
@Data
public class GQDJEntity extends BaseCaseEntity {

    private String id;
    private String anno;
    private String companynames;
    private String isvalid;
    private String courtname;
    private String provincecode;

    private long publicdate;
    private String statuesdetail;
    private String equityamount;
    private String relatedcompanyinfo;
    private String statuestype;
    private String nameandkeyno;
    private String zxnameandkeyno;
    //无详情数据标识 0-无detail 1-有detail
    private String noDetailFlag;

    private List<NameAndKeyNoEntity> relatedcompanyinfoEntityList;
    private List<NameAndKeyNoEntity> zxnameandkeynoEntityList;


    public static List<GQDJEntity> convert(List<String> jsonList) {
        List<GQDJEntity> list = new ArrayList<>();
        GQDJEntity entity = null;
        if (CollectionUtils.isEmpty(jsonList)) {
            return list;
        }
        for (String json : jsonList) {
            if (parquet.Strings.isNullOrEmpty(json)) {
                continue;
            }
            entity = JSON.parseObject(json, GQDJEntity.class);

            if (entity == null || Strings.isNullOrEmpty(entity.getId())) {
                continue;
            }

            String str = entity.getRelatedcompanyinfo();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setRelatedcompanyinfoEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getRelatedcompanyinfoEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                }
            }
            Set<String> baseSearchWord = new HashSet<>();
             str = entity.getZxnameandkeyno();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setZxnameandkeynoEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getZxnameandkeynoEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                    if(!Strings.isNullOrEmpty(namekey.getName())){
                        baseSearchWord.add(namekey.getName());
                    }
                    if(!Strings.isNullOrEmpty(namekey.getKeyNo())){
                        baseSearchWord.add(namekey.getKeyNo());
                    }
                }
            }


            //公共字段赋值
            entity.setBaseCaseNo(entity.getAnno());
            entity.setBaseCaseCategoryEnum(CaseCategoryEnum.GQDJ);
            entity.setBaseSearchWordSet(baseSearchWord);
//            if(!Strings.isNullOrEmpty(entity.getCompanynames())){
//                entity.setBaseSearchWordSet(Arrays.stream(entity.getCompanynames().split(","))
//                        .collect(Collectors.toSet()));
//            }

            //股权冻结关联只用被执行人数据
            if(entity.getZxnameandkeynoEntityList() != null){
                for (NameAndKeyNoEntity nameAndKeyNoEntity : entity.getZxnameandkeynoEntityList()) {

                }
            }


            entity.setBaseCaseReason("");
            entity.setBaseCourt(entity.getCourtname());
            List<NameAndKeyNoEntity> baseNameKeyNoList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(entity.getRelatedcompanyinfoEntityList())) {
                baseNameKeyNoList.addAll(entity.getRelatedcompanyinfoEntityList());
            }
            //被执行人
            if (CollectionUtils.isNotEmpty(entity.getZxnameandkeynoEntityList())) {
                baseNameKeyNoList.addAll(entity.getZxnameandkeynoEntityList());
            }
            entity.setBaseNameKeyNoList(baseNameKeyNoList);
            entity.setBaseId(entity.getBaseCaseCategoryEnum().getType()+"_"+entity.getId());
            entity.setBaseProvinceCode(entity.getProvincecode());

            String caseType= CommonV3Util.getCaseType(CommonV3Util.getCaseNo(entity.getBaseCaseNo()));
            //案件类型为空的数据直接过滤
            if(parquet.Strings.isNullOrEmpty(caseType)){
                continue;
            }
            list.add(entity);
        }
        return list;
    }
}

