package com.qcc.udf.kzz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;

/**
 * 招投标解析中标金额
 *
 * <AUTHOR>
 * @date 2022/4/1
 */
public class GetTenderWtbAmount extends UDF {
    public static double evaluate(String content, String keyNo) {
        double amount = 0;
        try {
            if (StringUtils.isNotBlank(content) && StringUtils.length(content) > 32 && StringUtils.isNotBlank(keyNo)) {
                JSONArray jsonArray = JSON.parseArray(content);
                if (!jsonArray.isEmpty()) {
                    for (Object obj : jsonArray) {
                        JSONObject json = (JSONObject) obj;
                        String originalAmt = json.getString("OriginalAmt");
                        String targetKeyNo = json.getString("KeyNo");
                        if (StringUtils.isNotBlank(originalAmt) && keyNo.equals(targetKeyNo)) {
                            String value = originalAmt.replaceAll("[\\u4e00-\\u9fa5￥$]", "");
                            amount = amount + new BigDecimal(value).divide(BigDecimal.valueOf(10000)).stripTrailingZeros().doubleValue();
                        }
                    }
                }
            }
        } catch (Exception e) {

        }
        return amount;
    }
//    public static void main(String[] args){
//        String content ="[\n" +
//                "  {\n" +
//                "    \"Amt\": \"19.948万\",\n" +
//                "    \"KeyNo\": \"041f26161fd1c83be08337aa5a00bfff\",\n" +
//                "    \"Name\": \"日照正华建筑有限公司\",\n" +
//                "    \"Org\": 0,\n" +
//                "    \"OriginalAmt\": \"199480.0人民币\",\n" +
//                "    \"PackageName\": \"三庄中心卫生院职工周转房改造\",\n" +
//                "    \"PackageNumber\": \"标包B\"\n" +
//                "  },\n" +
//                "  {\n" +
//                "    \"Amt\": \"7.45万\",\n" +
//                "    \"KeyNo\": \"041f26161fd1c83be08337aa5a00bfff\",\n" +
//                "    \"Name\": \"日照正华建筑有限公司\",\n" +
//                "    \"Org\": 0,\n" +
//                "    \"OriginalAmt\": \"74500.0人民币\",\n" +
//                "    \"PackageName\": \"三庄中心卫生院门诊楼病房楼外墙漆刷\",\n" +
//                "    \"PackageNumber\": \"标包A\"\n" +
//                "  }\n" +
//                "]";
//        double tels =  evaluate(content,"041f26161fd1c83be08337aa5a00bfff");
//        System.out.println(tels);
//    }
}
