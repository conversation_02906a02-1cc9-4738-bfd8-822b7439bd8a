package com.qcc.udf.enums;

/**
 * 处罚机构分类
 */
public enum PunishGovCodeEnum {
    FINANCE_GOV("A01", "金融监管"),
    EXCHANGE_GOV("A02", "交易所"),
    ASSOCIATION_GOV("A03", "行业协会"),
    GOVERMMENT_GOV("A04", "政府单位"),
    COURT_GOV("A05", "法院"),
    DEFAULT("", "其他"),
    ;


    private String code;
    private String punishName;

    PunishGovCodeEnum(String code, String punishName) {
        this.code = code;
        this.punishName = punishName;
    }

    public String getCode() {
        return code;
    }

    public String getPunishName() {
        return punishName;
    }

}
