package com.qcc.udf.risk_analysis;

import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * @Auther: liulh
 * @Date: 2020/9/3 20:33
 * @Description:
 */
public class getLianCourtLevel extends UDF {
    public static String  evaluate(String info) {
        String result = "1";

        if (StringUtils.isEmpty(info)){
            return "";
        }

        if (info.contains("高级人民法院")){
            result = "4";
        }

        if (info.contains("中级人民法院")){
            result = "3";
        }

        if (info.contains("区人民法院") || info.contains("县人民法院") || info.contains("市人民法院")){
            result = "2";
        }

        return result;
    }
}
