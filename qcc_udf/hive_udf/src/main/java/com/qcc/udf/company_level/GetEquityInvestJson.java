package com.qcc.udf.company_level;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.*;

/**
 * 返回对外投资json
 */
public class GetEquityInvestJson extends UDF {

//    public static void main(String[] args) {
//        System.out.println(ParseToDecimal(0.0000025 * 100));
//    }

    public static String evaluate(String keyNo, String companyName, int detailCount, List<String> levelArray) throws Exception {

        final ExecutorService exec = Executors.newFixedThreadPool(1);
        String obj = "{\"KeyNo\":\"" + keyNo + "\"}";
        Callable<String> call = () -> GetResult(keyNo, companyName, detailCount, levelArray);
        Future<String> future = exec.submit(call);
        try {
            obj = future.get(500, TimeUnit.SECONDS);//设置超时时间
        } catch (InterruptedException e) {

        } catch (ExecutionException e) {

        } catch (TimeoutException e) {
        }
        exec.shutdown();
        return obj;
    }

    private static String GetResult(String keyNo, String companyName, int detailCount, List<String> levelArray) throws Exception {
        List<JSONObject> investList = new ArrayList<>();
        for (String s : levelArray) {
            investList.add(ParseInvestObj(s));
        }
        JSONObject result = new JSONObject();
        result.put("KeyNo", keyNo);
        result.put("Name", companyName);
        result.put("DetailCount", detailCount);
        List<JSONObject> detailList = GetEquityShareDetail(keyNo, investList, 1);
        result.put("EquityShareDetail", detailList);
        return JSON.toJSONString(result, SerializerFeature.WriteMapNullValue);
    }


    /**
     * 获取层级对外投资结构
     *
     * @param path
     * @param investList
     * @param level
     * @return
     * @throws Exception
     */
    private static List<JSONObject> GetEquityShareDetail(String path, List<JSONObject> investList, int level) throws Exception {
        List<JSONObject> result = new ArrayList<>();
        try {
            if (investList.isEmpty()) return result;
            List<JSONObject> levelList = new ArrayList<>();
            List<JSONObject> levelInvestList = new ArrayList<>();
            for (JSONObject jsObj : investList) {
                if (jsObj.getInteger("Level") == level && jsObj.getString("Path").equals(path))
                    levelList.add(jsObj);
                else if (jsObj.getInteger("Level") > level) levelInvestList.add(jsObj);
            }
            if (levelList.isEmpty()) return result;
            //stockPercent、stockRightNum、keyNo倒叙
            Collections.sort(levelList, (a, b) -> {
                int res = 0;
                if (a.getDoubleValue("StockPercent") > b.getDoubleValue("StockPercent"))
                    res = -1;
                else if (a.getDoubleValue("StockPercent") < b.getDoubleValue("StockPercent"))
                    res = 1;
                else if (a.getString("StockRightNum").compareTo(b.getString("StockRightNum")) > 0)
                    res = -1;
                else if (a.getString("StockRightNum").compareTo(b.getString("StockRightNum")) < 0)
                    res = 1;
                else if (a.getString("KeyNo").compareTo(b.getString("KeyNo")) > 0)
                    res = -1;
                else if (a.getString("KeyNo").compareTo(b.getString("KeyNo")) < 0)
                    res = 1;
                return res;
            });
            for (JSONObject jsonObject : levelList) {
                JSONObject obj = new JSONObject();
                obj.put("KeyNo", jsonObject.get("KeyNo"));
                obj.put("Name", jsonObject.get("Name"));
                obj.put("CompanyCode", jsonObject.get("CompanyCode"));
                obj.put("Percent", ParseToDecimal(jsonObject.getDoubleValue("StockPercent") * 100) + "%");
                obj.put("PercentTotal", ParseToDecimal(jsonObject.getDoubleValue("PercentTotal") * 100) + "%");
                obj.put("Level", jsonObject.getIntValue("Level"));
                obj.put("ShouldCapi", jsonObject.get("ShouldCapi"));
                obj.put("StockRightNum", jsonObject.get("StockRightNum"));
                obj.put("DetailCount", jsonObject.getIntValue("DetailCount"));
                obj.put("Tags", jsonObject.get("Tags"));
                obj.put("ShortStatus", jsonObject.get("ShortStatus"));
                if (jsonObject.get("RegistCapi") != null) {
                    obj.put("RegistCapi", jsonObject.get("RegistCapi"));
                }
                StringBuilder sbStr = new StringBuilder();
                String levelPath = GenearteMD5(ParseToString(jsonObject.get("Path")) + "-" + ParseToString(jsonObject.get("KeyNo")));
                List<JSONObject> detailList = new ArrayList<>();
                if (level < 5) {
                    int curLevel = level + 1;
                    detailList = GetEquityShareDetail(levelPath, levelInvestList, curLevel);
                }
                obj.put("DetailList", detailList);
                result.add(obj);
            }
            return result;
        } catch (Exception e) {
            return result;
        }
    }

    /**
     * @param levelStr investkeyno, investname, investcode, investcount, stockpercent, stockpercent_total, shouldcapi, stockrightnum, totallevel, keyno_path 以s01分割）
     * @return
     * @throws Exception
     */
    private static JSONObject ParseInvestObj(String levelStr) throws Exception {
        try {
            String[] levelArray = levelStr.split("ss01", -1);
            JSONObject outData = new JSONObject();
            outData.put("KeyNo", levelArray[0]);
            outData.put("Name", levelArray[1]);
            outData.put("CompanyCode", levelArray[2]);
            outData.put("DetailCount", levelArray[3]);
            outData.put("StockPercent", levelArray[4]);
            outData.put("PercentTotal", levelArray[5]);
            outData.put("ShouldCapi", levelArray[6]);
            outData.put("StockRightNum", levelArray[7]);
            outData.put("Level", levelArray[8]);
            outData.put("Path", levelArray[9]);
            outData.put("Tags", levelArray[10].equals("") ? new ArrayList<>() : JSONArray.parseArray(levelArray[10]));
            outData.put("ShortStatus", levelArray[11]);
            if (levelArray.length == 13) {
                outData.put("RegistCapi", levelArray[12]);
            }
            return outData;
        } catch (Exception e) {
            return new JSONObject();
        }
    }

    /**
     * Object 转 String
     *
     * @param obj
     * @return
     */
    private static String ParseToString(Object obj) {
        try {
            if (obj == null) return "";
            return obj.toString();
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * string转deciaml
     *
     * @param str
     * @return
     */
    private static String ParseToDecimal(Object str) {
        try {
            DecimalFormat df = new DecimalFormat("#0.0000");
            return subZeroAndDot(df.format(str));
        } catch (Exception e) {
            return "0";
        }
    }
    /**
     * 使用java正则表达式去掉多余的.与0
     *
     * @param data
     * @return
     */
    public static String subZeroAndDot(String data) {
        if (data.indexOf(".") > 0) {
            data = data.replaceAll("0+?$", "");//去掉多余的0
            data = data.replaceAll("[.]$", "");//如最后一位是.则去掉
        }
        return data;
    }
    /**
     * 生成md5
     *
     * @param s
     * @return
     */
    public static String GenearteMD5(String s) {
        if (s == null) {
            return null;
        }
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(s.getBytes());
            byte[] md5hash = md.digest();
            StringBuilder builder = new StringBuilder();
            for (byte b : md5hash) {
                builder.append(Integer.toString((b & 0xff) + 0x100, 16).substring(1));
            }
            return new String(builder.toString());
        } catch (NoSuchAlgorithmException nsae) {
            return "";
        }
    }
}

