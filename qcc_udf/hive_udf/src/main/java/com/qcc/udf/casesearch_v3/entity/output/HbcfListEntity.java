package com.qcc.udf.casesearch_v3.entity.output;

import lombok.Data;
import com.alibaba.fastjson.annotation.JSONField;

import java.util.LinkedList;
import java.util.List;

@Data
public class HbcfListEntity  extends BasePenaltyOutEntity{
    //违法类型
    @JSONField(name = "IllegalType")
    private String illegalType="";
    //执行情况
    @JSONField(name = "Implementation")
    private String implementation="";
}
