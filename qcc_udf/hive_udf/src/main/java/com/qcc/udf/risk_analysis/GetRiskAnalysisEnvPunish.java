package com.qcc.udf.risk_analysis;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 企业风险分析UDF：环保处罚维度风险分析结果统计
 * ---------------------------------------------------------------------------------------------------------
 * create temporary function getRiskAnalysisEnvPunish as 'com.ld.qianjg.analysis.GetRiskAnalysisEnvPunishUDF' using jar 'hdfs:///user/qianjg/my-udf.jar';
 * ---------------------------------------------------------------------------------------------------------
 */
public class GetRiskAnalysisEnvPunish extends UDF {

    public String evaluate(String input) {
        JSONObject countInfoJson = new JSONObject();
        try {
            /**
             * 步骤：（统计三个信息）
             * 1 处罚总量
             * 2 根据执行情况做分组统计（空值不参与统计）
             * 3 根据违法类型做分组统计（空值不参与统计）
             * 4 所有id逗号分隔拼接
             */
            JSONArray infoJsonArray = JSONObject.parseArray(input);

            Map<String, Long> illegalTypeCountMap = infoJsonArray.stream()
                    .map(p -> (JSONObject) p)
                    .filter(p -> StringUtils.isNotBlank(p.getString("illegaltype")))
                    .collect(Collectors.groupingBy(p -> p.getString("illegaltype"), Collectors.counting()));

            Map<String, String> illegalTypeIdsMap = infoJsonArray.stream()
                    .map(p -> (JSONObject) p)
                    .filter(p -> StringUtils.isNotBlank(p.getString("illegaltype")))
                    .collect(Collectors.groupingBy(p -> p.getString("illegaltype"), Collectors.mapping(p -> p.getString("id"), Collectors.joining(","))));

            Map<String, Long> implementationCountMap = infoJsonArray.stream()
                    .map(p -> (JSONObject) p)
                    .filter(p -> StringUtils.isNotBlank(p.getString("implementation")))
                    .collect(Collectors.groupingBy(p -> p.getString("implementation"), Collectors.counting()));

            Map<String, String> implementationIdsMap = infoJsonArray.stream()
                    .map(p -> (JSONObject) p)
                    .filter(p -> StringUtils.isNotBlank(p.getString("implementation")))
                    .collect(Collectors.groupingBy(p -> p.getString("implementation"), Collectors.mapping(p -> p.getString("id"), Collectors.joining(","))));

            List<String> illegalTypeCount = illegalTypeCountMap.keySet().stream()
                    .map(p -> StringUtils.join(Arrays.asList(p, "#FGF#", illegalTypeCountMap.get(p), "#FGF#", illegalTypeIdsMap.get(p)), ""))
                    .collect(Collectors.toList());
            List<String> implementationCount = implementationCountMap.keySet().stream()
                    .map(p -> StringUtils.join(Arrays.asList(p, "#FGF#", implementationCountMap.get(p), "#FGF#", implementationIdsMap.get(p)), ""))
                    .collect(Collectors.toList());

            countInfoJson.put("A", infoJsonArray.size());
            countInfoJson.put("B", illegalTypeCount);
            countInfoJson.put("C", implementationCount);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return countInfoJson.toJSONString();
    }

    public static void main(String[] args) {
        String input = "[" +
                "{\"id\":\"3336a0f2d1776191b23953cfcf7e01bf\",\"illegaltype\":\"《中华人民共和国大气污染防治法》第十八条关于向大气排放污染物的，应当符合大气污染物排放标准的规定。\",\"implementation\":\"其它\"}," +
                "{\"id\":\"63a2fb82a3196fd9f527f7a1a3dbeb51\",\"illegaltype\":\"其它\",\"implementation\":\"其它\"}," +
                "{\"id\":\"72c8daee2fe2c28a671bf855ce540958\",\"illegaltype\":\"其它\",\"implementation\":\"其它\"}" +
                "]";
        String result = new GetRiskAnalysisEnvPunish().evaluate(input);
        System.out.println(result);
    }
}
