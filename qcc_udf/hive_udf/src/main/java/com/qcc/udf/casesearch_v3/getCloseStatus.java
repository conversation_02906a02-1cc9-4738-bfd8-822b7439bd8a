package com.qcc.udf.casesearch_v3;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class getCloseStatus extends UDF {
    public static String evaluate(String param) {
        JSONArray result = new JSONArray();
        if (StringUtils.isNotEmpty(param)){
            JSONArray array = JSONObject.parseObject(param).getJSONArray("InfoList");
            if (array != null && !array.isEmpty()){
                Iterator<Object> it = array.iterator();
                while (it.hasNext()){
                    JSONObject json = (JSONObject) it.next();
                    getInfoFromArray(json.getJSONArray("SxList"), result, "sx");
                    getInfoFromArray(json.getJSONArray("ZxList"), result, "zx");
                    getInfoFromArray(json.getJSONArray("XgList"), result, "xg");
                    getInfoFromArray(json.getJSONArray("ZbList"), result, "zb");
                    getInfoFromArray(json.getJSONArray("CaseList"), result, "cpws");
                    getInfoFromArray(json.getJSONArray("LianList"), result, "lian");
                    getInfoFromArray(json.getJSONArray("SqtjList"), result, "sqtj");
                    getInfoFromArray(json.getJSONArray("FyggList"), result, "fygg");
                    getInfoFromArray(json.getJSONArray("KtggList"), result, "ktgg");
                    getInfoFromArray(json.getJSONArray("SdggList"), result, "sdgg");
                    getInfoFromArray(json.getJSONArray("XzcjList"), result, "xzcj");
                    getInfoFromArray(json.getJSONArray("PcczList"), result, "pccz");
                    getInfoFromArray(json.getJSONArray("XjpgList"), result, "xjpg");
                    getInfoFromArray(json.getJSONArray("XdpgjgList"), result, "xdpgjg");
                    getInfoFromArray(json.getJSONArray("SfpmList"), result, "sfpm");
                    getInfoFromArray(json.getJSONArray("GqdjList"), result, "gqdj");
                }
            }
        }

        return result.toString();
    }

    public static void getInfoFromArray(JSONArray array, JSONArray result, String type){
        if (array != null && !array.isEmpty() && array.size() > 0){
            Iterator<Object> it = array.iterator();
            while (it.hasNext()){
                JSONObject json = (JSONObject) it.next();
                String keynos = "";
                if ("gqdj".equals(type)){
                    Set<String> keyNoSet = new LinkedHashSet<>();
                    JSONArray keyNoArray = json.getJSONArray("NameAndKeyNo");
                    if (keyNoArray != null && !keyNoArray.isEmpty() && keyNoArray.size() > 0){
                        Iterator<Object> sub = keyNoArray.iterator();
                        while (sub.hasNext()){
                            JSONObject myJson = (JSONObject)sub.next();
                            if (StringUtils.isNotEmpty(myJson.getString("KeyNo"))){
                                keyNoSet.add(myJson.getString("KeyNo"));
                            }
                        }
                    }
                    keyNoArray = json.getJSONArray("ZxNameAndKeyNo");
                    if (keyNoArray != null && !keyNoArray.isEmpty() && keyNoArray.size() > 0){
                        Iterator<Object> sub = keyNoArray.iterator();
                        while (sub.hasNext()){
                            JSONObject myJson = (JSONObject)sub.next();
                            if (StringUtils.isNotEmpty(myJson.getString("KeyNo"))){
                                keyNoSet.add(myJson.getString("KeyNo"));
                            }
                        }
                    }
                    keynos = String.join(",", keyNoSet);
                }

                //if (json.getInteger("IsValid") == 1){
                     JSONObject jsonObject = new JSONObject();
                     jsonObject.put("Id", json.getString("Id"));
                     jsonObject.put("Type", type);
                     jsonObject.put("KeyNos", keynos);

                     result.add(jsonObject);
                //}
            }
        }
    }
}
