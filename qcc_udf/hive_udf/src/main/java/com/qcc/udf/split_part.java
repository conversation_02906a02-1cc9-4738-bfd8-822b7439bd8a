package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

public class split_part extends UDF{
    public String evaluate(String str,String separator,int start){
        if(str!=null){
            String[] tmp = str.split(separator);
            if(start<tmp.length){
                return tmp[start];
            }else{
                return "";
            }
        }else{
            return "";
        }
    }

    public String evaluate(String str,String separator,int start,int end){
        if(str!=null){
            String[] tmp = str.split(separator);
            if(end>tmp.length){
                end = tmp.length;
            }
            if(start>end){
                return "";

            }else{
                StringBuilder  result = new StringBuilder();
                for(int i=0;i<end;i++){
                    if(i==(start-1)){
                        result.append(tmp[i]);
                    }else if(i>(start-1)){
                        result.append(separator + tmp[i]);
                    }
                }
                return result.toString();
            }
        }else{
            return "";
        }
    }
}
