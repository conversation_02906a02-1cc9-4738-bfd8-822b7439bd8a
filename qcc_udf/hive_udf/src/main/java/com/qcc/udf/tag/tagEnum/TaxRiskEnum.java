package com.qcc.udf.tag.tagEnum;

import com.qcc.udf.tag.TagEntity;
import com.qcc.udf.tag.interfaces.TagEnumInterface;

/**
 * 税务风险标签枚举
 */
public enum TaxRiskEnum implements TagEnumInterface {
    SW01("虚开发票","SW01"),
    SW02("欠税","SW02"),
    SW03("纳税非正常户","SW03"),
    SW04("偷税、逃税、抗税","SW04"),
    SW05("非法取得进项发票","SW05"),
    SW06("未按规定申报","SW06"),
    SW07("未按期申报","SW07"),
    SW08("少缴税款","SW08"),
    SW09("纳税人延期缴纳税款申请","SW09"),
    SW10("涉税刑事案件","SW10"),
    SW99("其他","SW99"),
    ;


    private String name;
    private String code;

    TaxRiskEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    @Override
    public TagEntity getTagEntity() {
        return new TagEntity(this.code,this.name);
    }
}
