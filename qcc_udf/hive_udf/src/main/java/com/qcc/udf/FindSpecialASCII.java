package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class FindSpecialASCII extends UDF {
    public String evaluate(String args) {
        //将String先转换为ASCII
        byte[] b = args.getBytes(StandardCharsets.US_ASCII);
        //ASCII控制字符 为0-31和127
        List<Integer> specialASCII = IntStream.rangeClosed(0, 32).boxed().collect(Collectors.toList());
        //DEL (delete)
        specialASCII.add(127);
        ArrayList<String> strList = new ArrayList<>();
        for (int i : specialASCII) {
            byte[] enp = new byte[]{(byte) i};
            String str = new String(enp);

            if (args.indexOf(str) > 0) {
                strList.add(String.valueOf(i));
            }
        }
        if (strList.isEmpty()) {
            return "";
        }
        return String.join(",", strList);

    }



}
