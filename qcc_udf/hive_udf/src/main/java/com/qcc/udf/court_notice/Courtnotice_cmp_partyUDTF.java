package com.qcc.udf.court_notice;

import com.qcc.udf.court_notice.anCleanMethods.CleanCourtPartyFromEs;
import com.qcc.udf.court_notice.anUtils.Util;
import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentLengthException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDTF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;

import java.util.ArrayList;
import java.util.Set;
import java.util.TreeSet;


/**
* 比较ES提取的当事人
**/
public class Courtnotice_cmp_partyUDTF extends GenericUDTF {
    static String strNull = null;
    static String[] outputschema=new String[]{"id","party_hdfs","prosecutor","defendant","party","names"};
    @Override
    public void process(Object[] objects) throws HiveException {
        String id="";
        String party_hdfs="";
        String party_hdfs2="";
        String party_es="";
        if(objects[0]!=null){
            party_hdfs=objects[0].toString();
            party_hdfs2=party_hdfs;
        }
        if(objects[1]!=null){
            party_es=objects[1].toString();
        }
        if(objects[2]!=null){
            id=objects[2].toString();
        }
        String[] names_es;
        if("".equals(party_es)){
            names_es=null;
        }else {
            names_es = party_es.split("es_$$_es");
        }
        String[] flag=new String[names_es.length];
        for(int i=0;i<flag.length;i++){
            flag[i]="flag_"+i+"_flag";
            if(party_hdfs.contains(names_es[i])){
                party_hdfs=party_hdfs.replaceFirst(names_es[i],flag[i]);
            }
        }
        //重新清洗当事人
        String party="";
        party = party_hdfs.replaceAll("(被上诉人|被申诉人|抗诉|原告：|被告：|被告人|原告人|被申请人)", "").replaceAll("被告|原告|申诉人|上诉人|申请人", "").replaceAll("(与|诉|及)", ",").replaceAll("，|、|;|；",",");
        if (party.startsWith(",")) {
            party = party.substring(1, party.length());
        }
        String[] party_array=party.split(",");
        for(int j=0;j<party_array.length;j++){
            for(int i=0;i<flag.length;i++){
                if(party_array[j].contains(flag[i])&& !party_array[j].equals(flag[i])){
                    party_array[j]=flag[i];
                    break;
                }
            }
        }
        party="";
        for(int i=0;i<party_array.length;i++){
            party+=","+party_array[i];
        }
        party=party.substring(1, party.length());

        String prosecutor;
        String defendant;
        prosecutor = CleanCourtPartyFromEs.getProsecutor(party_hdfs);
        defendant = CleanCourtPartyFromEs.getDefendant(party_hdfs);
        //去除flag符号
        for(int i=0;i<flag.length;i++){
            if(party!=null &&party.contains(flag[i])){
                party = party.replaceFirst(flag[i],names_es[i]);
            }
            if(prosecutor!=null && prosecutor.contains(flag[i])){
                prosecutor = prosecutor.replaceFirst(flag[i],names_es[i]);
            }
            if(defendant!=null && defendant.contains(flag[i])){
                defendant = defendant.replaceFirst(flag[i],names_es[i]);
            }
        }
        String names = "";
        names = CleanCourtRelatedNames(prosecutor,defendant,party);

        System.out.println(id+" "+party_hdfs2+" "+prosecutor+" "+defendant+" "+party+" "+names);
        forward(new Object[]{id,party_hdfs2,prosecutor,defendant,party,names});





    }
    static String CleanCourtRelatedNames( String prosecutor, String defendant, String party){
        Set<String> nameset = new TreeSet<>();
        nameset.clear();
        if (!Util.isEmpty(prosecutor)) {
            String key = prosecutor;
            String s;
            for (String name : Util.split(key)) {
                //处理尾部为等的名称:王燕等
                if (!Util.isEmpty(name) && name.endsWith("等")) {
                    name = name.substring(0, name.length() - 1);
                }

                //处理类似：张涌立与李红杰
                if (!Util.isEmpty(name) && name.contains("与")) {
                    if (name.contains(s = "与") || name.contains(s = ",") || name.contains(s = "、")) {
                        for (String str : name.split(s)) {
                            nameset.add(str);
                        }
                    }
                } else {
                    nameset.add(name);
                }

            }
        }
        if (!Util.isEmpty(defendant)) {
            String key = defendant;
            String s;
            for (String name : Util.split(key)) {
                //处理尾部为等的名称:王燕等
                if (!Util.isEmpty(name) && name.endsWith("等")) {
                    name = name.substring(0, name.length() - 1);
                }

                //处理类似：张涌立与李红杰
                if (!Util.isEmpty(name) && name.contains("与")) {
                    if (name.contains(s = "与") || name.contains(s = ",") || name.contains(s = "、")) {
                        for (String str : name.split(s)) {
                            nameset.add(str);
                        }
                    }
                } else {
                    nameset.add(name);
                }
            }
        }
        if(defendant==null&&prosecutor==null &&party!=null){
            String key = party;
            String s;
            for (String name : Util.split(key)) {
                //处理尾部为等的名称:王燕等
                if (!Util.isEmpty(name) && name.endsWith("等")) {
                    name = name.substring(0, name.length() - 1);
                }

                //处理类似：张涌立与李红杰
                if (!Util.isEmpty(name) && name.contains("与")) {
                    if (name.contains(s = "与") || name.contains(s = ",") || name.contains(s = "、")) {
                        for (String str : name.split(s)) {
                            nameset.add(str);
                        }
                    }
                } else {
                    nameset.add(name);
                }
            }
        }
        if(nameset.isEmpty()){
            return strNull;
        }else{
            return nameset.toString().
                    replaceAll("\\[|\\]", "").
                    replaceAll(", ", ",");//去除空格
        }
    }
    @Override
    public void close() throws HiveException {

    }

    @Override
    public StructObjectInspector initialize(ObjectInspector[] args)
            throws UDFArgumentException {
        if (args.length != 3) {
            throw new UDFArgumentLengthException(
                    "Courtnotice_filterUDTF takes only 2 argument");
        }
        ArrayList<String> fieldNames = new ArrayList<String>();
        ArrayList<ObjectInspector> fieldOIs = new ArrayList<ObjectInspector>();

        for(int i=0;i<outputschema.length;i++){
            fieldNames.add(outputschema[i].toString());
            fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        }
        return ObjectInspectorFactory.getStandardStructObjectInspector(
                fieldNames, fieldOIs);

    }

/*    public static void main(String[] args) throws HiveException {
        String str="1";
        System.out.println(Util.notEmpty(str));
    }*/
}
