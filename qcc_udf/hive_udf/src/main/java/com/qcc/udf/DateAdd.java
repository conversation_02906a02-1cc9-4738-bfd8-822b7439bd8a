package com.qcc.udf;

import jodd.util.StringUtil;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * @Auther: shengr
 * @Date: 2022/05/25
 * @Description: 此UDF函数 是为了修正hive中的date_add函数而编写的
 */
public class DateAdd extends UDF {
    public String evaluate(String str, int reg) throws ParseException {
        if (StringUtil.isEmpty(str)) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        Date date = sdf.parse(str);
        calendar.setTime(date);
        calendar.add(Calendar.DATE, reg);
        str = (new SimpleDateFormat("yyyy-MM-dd")).format(calendar.getTime());
        return str;
    }

    public static void main(String[] args) throws ParseException {

        /*SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();  // 默认是当前日期
        //获取系统日期
        Calendar rightNow = Calendar.getInstance();
        // 指定一个日期
        Date date = sdf.parse("2021-02-16 13:24:16");
       // 对 calendar 设置为 date 所定的日期
        calendar.setTime(date);
        //系统日期减少或者增加几天
        calendar.add(Calendar.DATE, 366);
        System.out.println(sdf.format(calendar.getTime()));*/
        DateAdd s = new DateAdd();
        String evaluate = s.evaluate("2020-05-40", 5);
        System.out.println(evaluate);
    }
}