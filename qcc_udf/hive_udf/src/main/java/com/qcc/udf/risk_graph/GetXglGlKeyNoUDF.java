package com.qcc.udf.risk_graph;


import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.risk_analysis.entity.NameKeyDto;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 解析限高令关联人员信息
 * @date 2022/11/16 13:56
 */
public class GetXglGlKeyNoUDF extends UDF {

    private static final String KEY_NO = "KeyNo";
    private static final String NAME = "Name";
    private static final String STOCK_NAME = "StockName";

    public static String evaluate(String glName, String opera, String partners, String employees) {
        if (StringUtils.isNotBlank(glName)) {
            List<NameKeyDto> nameKeyDtos = new ArrayList<>();
            if (StringUtils.isNotBlank(opera)) {
                buildMemberInfos(NAME, KEY_NO, JSONObject.parseObject(opera), nameKeyDtos);
            }
            if (StringUtils.isNotBlank(partners)) {
                Iterator<Object> iterator = JSONObject.parseArray(partners).iterator();
                while (iterator.hasNext()) {
                    buildMemberInfos(STOCK_NAME, KEY_NO, (JSONObject) iterator.next(), nameKeyDtos);
                }
            }
            if (StringUtils.isNotBlank(employees)) {
                Iterator<Object> iterator = JSONObject.parseArray(employees).iterator();
                while (iterator.hasNext()) {
                    buildMemberInfos(NAME, KEY_NO, (JSONObject) iterator.next(), nameKeyDtos);
                }
            }
            Map<String, Long> keyNoMap = nameKeyDtos.stream().filter(x -> StringUtils.equals(glName, x.getName()) && StringUtils.isNotBlank(x.getKeyNo())).collect(Collectors.groupingBy(NameKeyDto::getKeyNo, Collectors.counting()));
            if (MapUtils.isEmpty(keyNoMap) || (MapUtils.isNotEmpty(keyNoMap) && keyNoMap.size() > 1)) {
                return StringUtils.EMPTY;
            }
            Optional<String> first = keyNoMap.keySet().stream().filter(Objects::nonNull).findFirst();
            if (first.isPresent()) {
                return first.get();
            }
        }
        return StringUtils.EMPTY;
    }

    public static List<NameKeyDto> buildMemberInfos(String nameField, String keyNoField, JSONObject jsonObject, List<NameKeyDto> nameKeyDtos) {
        if (jsonObject != null && jsonObject.size() > 0) {
            String name = jsonObject.getString(nameField);
            String keyNo = jsonObject.getString(keyNoField);
            nameKeyDtos.add(new NameKeyDto(keyNo, name, 2));
        }
        return nameKeyDtos;
    }

    public static void main(String[] args) {
        System.out.println(evaluate(
                "关德格吉乐胡",
                "{\"Org\":-1,\"KeyNo\":null,\"Name\":\"关德格吉乐胡\",\"HasImage\":false,\"CompanyCount\":0,\"OperType\":4}",
                "[]",
                "[]"));
    }
}
