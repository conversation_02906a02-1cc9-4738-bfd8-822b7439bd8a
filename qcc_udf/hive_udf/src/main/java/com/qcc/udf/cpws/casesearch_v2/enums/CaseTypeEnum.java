package com.qcc.udf.cpws.casesearch_v2.enums;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * 案件类型
 */
public enum CaseTypeEnum {
    MS("ms","民事案件"),
    ZX("zx","执行案件"),
    XS("xs","刑事案件"),
    XZ("xz","行政案件"),
    BQ("bq","保全案件"),
    GX("gx","管辖案件")
    ;


    private String code;
    private String name;

    CaseTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }


    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    private static final Map<String, CaseTypeEnum> lookup = new HashMap<String, CaseTypeEnum>();

    static {
        for (CaseTypeEnum e : EnumSet.allOf(CaseTypeEnum.class)) {
            lookup.put(e.code, e);
        }
    }

    public static CaseTypeEnum find(String code) {
        CaseTypeEnum data = lookup.get(code);
        if (code == null) {
            return null;
        }
        return data;
    }
}
