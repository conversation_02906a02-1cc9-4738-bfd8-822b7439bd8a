package com.qcc.udf.company;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class getLinkedKeyNo extends UDF {

    public String evaluate(String source) {
        if (StringUtils.isEmpty(source) || !source.contains("-")) {
            return "";
        }

        List<String> result = new ArrayList<>();

        List<String> list = Arrays.asList(source.split("-"));
        for (int i = 0; i < list.size() - 1; i++) {
            result.add(list.get(i) + "-" + list.get(i + 1));
        }

        return result.stream().collect(Collectors.joining(","));
    }

    public static void main(String[] args) {
        getLinkedKeyNo model = new getLinkedKeyNo();
        String result = model.evaluate("a-b-c-d");
    }
}
