package com.qcc.udf.casesearch_v3.util;

import com.google.common.base.Objects;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.qcc.udf.casesearch_v3.CaseNoCleanUDF;
import com.qcc.udf.casesearch_v3.entity.LawSuitV3Entity;
import com.qcc.udf.casesearch_v3.entity.input.*;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.casesearch_v3.enums.CaseCategoryEnum;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.qcc.udf.casesearch_v3.util.CommonV3Util.*;

/**
 * 案件拆分类
 * <AUTHOR>
 * @date 2021年05月24日 10:59
 */
public class GroupSplitUtil {

    /**
     * 案件拆分
     *
     * @param input
     */
    public static List<LawSuitV3Entity> split_v2(LawSuitV3Entity input) throws InvocationTargetException, IllegalAccessException {
        //入参所有维度
        List<BaseCaseEntity> allList = buildInputList(input);
        //按照当事人分组
        List<List<BaseCaseEntity>> groupList = groupInputList(allList);

        //拆完的数据可能还需要再聚合
        List<List<BaseCaseEntity>> reGroupList = reGroup(groupList);


        List<LawSuitV3Entity> outList = new ArrayList<>();
        //分组输出数据
        for (List<BaseCaseEntity> group : reGroupList) {
            LawSuitV3Entity out =new LawSuitV3Entity();
            BeanUtils.copyProperties(out,input);


            out.setLianList(convertList(group.stream().filter(data-> CaseCategoryEnum.LA.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), LAEntity.class));
            out.setCpwsList(convertList(group.stream().filter(data-> CaseCategoryEnum.CPWS.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), CPWSEntity.class));
            out.setSxList(convertList(group.stream().filter(data-> CaseCategoryEnum.SX.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), SXEntity.class));
            out.setZxList(convertList(group.stream().filter(data-> CaseCategoryEnum.ZX.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), ZXEntity.class));
            out.setKtggList(convertList(group.stream().filter(data-> CaseCategoryEnum.KTGG.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), KTGGEntity.class));
            out.setSdggList(convertList(group.stream().filter(data-> CaseCategoryEnum.SDGG.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), SDGGEntity.class));
            out.setFyggList(convertList(group.stream().filter(data-> CaseCategoryEnum.FYGG.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), FYGGEntity.class));
            out.setPcczList(convertList(group.stream().filter(data-> CaseCategoryEnum.PCCZ.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), PCCZEntity.class));
            out.setXjpgList(convertList(group.stream().filter(data-> CaseCategoryEnum.XJPG.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), XJPGEntity.class));
            out.setZbList(convertList(group.stream().filter(data-> CaseCategoryEnum.ZB.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), ZBEntity.class));
            out.setXgList(convertList(group.stream().filter(data-> CaseCategoryEnum.XG.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), XGEntity.class));
            out.setGqdjList(convertList(group.stream().filter(data-> CaseCategoryEnum.GQDJ.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), GQDJEntity.class));

            out.setHbcfList(input.getHbcfList());
            out.setXzcfList(input.getXzcfList());

            out.setSfpmList(convertList(group.stream().filter(data-> CaseCategoryEnum.SFPM.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), SFPMEntity.class));
            out.setSqtjList(convertList(group.stream().filter(data-> CaseCategoryEnum.SQTJ.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), SQTJEntity.class));
            out.setXzcjList(convertList(group.stream().filter(data-> CaseCategoryEnum.XZCJ.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), XZCJEntity.class));
            out.setXdpgjgList(convertList(group.stream().filter(data-> CaseCategoryEnum.XDPGJG.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), XDPGJGEntity.class));
            out.setXsggList(convertList(group.stream().filter(data-> CaseCategoryEnum.XSGG.equals(data.getBaseCaseCategoryEnum()))
                    .collect(Collectors.toList()), XSGGEntity.class));
            outList.add(out);
        }


        return outList;
    }


    /**
     * 安全包含
     * @param content
     * @param target
     * @return
     */
    static boolean safeContains(String content,String target,String exclude){
        content = getString(content);
        target = getString(target);
        exclude = getString(exclude);
        return content.contains(target) && !content.contains(exclude);
    }


    /**
     * 生成当事人数组
     *
     * @param nameList
     * @return
     */
    static Set<String> buildNameKeySet(List<NameAndKeyNoEntity> nameList, boolean containsMou) {
        Set<String> nameKeySet = new HashSet<>();
        if (CollectionUtils.isEmpty(nameList)) {
            return nameKeySet;
        }
        for (NameAndKeyNoEntity keyNoEntity : nameList) {
            if (Strings.isNullOrEmpty(keyNoEntity.getKeyNo())) {
                if(!Strings.isNullOrEmpty(keyNoEntity.getName())){
                    if(containsMou){
                        nameKeySet.add(keyNoEntity.getName().substring(0,1));
                    }else{
                        nameKeySet.add(keyNoEntity.getName());
                    }
                }

            } else {
                nameKeySet.add(keyNoEntity.getKeyNo());
                //人名的情况下 可能存在A案件有keyNo, b案件没有keyNo的情况
                if("p".equals(keyNoEntity.getKeyNo().substring(0,1))){
                    if(containsMou){
                        nameKeySet.add(keyNoEntity.getName().substring(0,1));
                    }else{
                        nameKeySet.add(keyNoEntity.getName());
                    }
                }else{
                    //相同公司名也会存在部分维度没有KeyNo的情况
                    nameKeySet.add(keyNoEntity.getName());
                }
            }
        }
        return nameKeySet;
    }


    /**
     * 入参数据按照当事人分组
     *
     * @return
     */
    static List<List<BaseCaseEntity>> groupInputList(List<BaseCaseEntity> allList) {
        //生成主键，当事人关系map
        Map<String, Set<String>> pk_nameMap = new HashMap<>();
        Map<String, BaseCaseEntity> pk_entityMap = new HashMap<>();

        //无当事人裁判文书信息
        Map<String,String> noNameKeyMap = new HashMap<>();

        boolean containsMou = false;

        for (BaseCaseEntity item : allList) {
            if(CollectionUtils.isNotEmpty(item.getBaseNameKeyNoList())){
                boolean bl = item.getBaseNameKeyNoList().stream().map(NameAndKeyNoEntity::getName)
                        .filter(GroupSplitUtil::isMouMouPerson)
                        .findAny().isPresent();
                if(bl){
                    containsMou = true;
                }
            }
            if(CollectionUtils.isEmpty(item.getBaseNameKeyNoList())){
                noNameKeyMap.put(item.getBaseId(),item.getBaseCaseNo());
            }

        }


        for (BaseCaseEntity item : allList) {
            List<NameAndKeyNoEntity> nameList = item.getBaseNameKeyNoList();
            Set<String> nameKeySet = buildNameKeySet(nameList,containsMou);
            pk_nameMap.put(item.getBaseId(), nameKeySet);

            pk_entityMap.put(item.getBaseId(), item);
        }
        //生成所有关系路径
        Set<Set<String>> groupList = new HashSet<>();
        pk_nameMap.forEach((k1, v1) -> {
            Set<String> group = new HashSet<>();
            pk_nameMap.forEach((k2, v2) -> {
                if (isJoin(v1, v2)) {
                    group.add(k2);
                }
            });
            //没有同组数据, 自己成为一组
            if (group.size() == 0) {
                group.add(k1);
            }
            groupList.add(group);
        });

//        System.err.println(JSON.toJSONString(groupList));

        //遍历生成关系组
        Set<String> idSet = pk_entityMap.keySet();
        List<Set<String>> list = new ArrayList<>();
        for (String id : idSet) {
            //判断id是否在已知组内
            long markFlag = list.stream().filter(item -> item.contains(id)).count();
            if (markFlag > 0) {
                continue;
            }
            //遍历所有路径 合并有交集数据
            Set<String> group = new HashSet<>();
            for (Set<String> subSet : groupList) {
                if (subSet.contains(id) || isJoin(group, subSet)) {
                    group.addAll(subSet);
                }
            }
            list.add(group);
//            System.err.println(id + "_" + group);
        }
        List<List<BaseCaseEntity>> outList = new ArrayList<>();
        for (Set<String> subSet : list) {
            if(subSet.size() == 1){
                String pk = subSet.stream().collect(Collectors.joining(","));
                //丢弃无当事人裁判文书独自成组案件
                if(noNameKeyMap.containsKey(pk)){
                    continue;
                }else{
                    outList.add(subSet.stream().map(item -> pk_entityMap.get(item)).collect(Collectors.toList()));
                }
            }else{
                outList.add(subSet.stream().map(item -> pk_entityMap.get(item)).collect(Collectors.toList()));
            }
        }

        //未能挂靠成功的数据
        List<BaseCaseEntity> noBindList = new ArrayList<>();

        //很多裁判文书没有当事人信息 这部分数据最好找一个同案号的案子挂靠上去,或者为beforecaseno 也可以故靠
        noNameKeyMap.forEach((k,v)->{
            boolean bindSuccess = false;
            String caseNo = cleanCaseNo(v);
            //根据案号挂号靠案件
            for (List<BaseCaseEntity> item : outList) {
                if(item.stream().filter(data->isCaseNoJoin(cleanCaseNo(data.getBaseCaseNo()),caseNo)).findAny().isPresent()){
                    item.add(pk_entityMap.get(k));
                    bindSuccess = true;
                    break;
                }
            }
            //挂靠案件失败,根据或者为beforecaseno挂号靠案件
            if(!bindSuccess){
                for (List<BaseCaseEntity> item : outList) {
                    if(!Strings.isNullOrEmpty(caseNo)
                            && item.stream().filter(data->data.getBaseBeforeNoSet()!= null
                            && data.getBaseBeforeNoSet().contains(caseNo)).findAny().isPresent()){
                        item.add(pk_entityMap.get(k));
                        bindSuccess = true;
                        break;
                    }
                }
            }
            //挂靠案件失败
            if(!bindSuccess){
                noBindList.add(pk_entityMap.get(k));
            }
        });

        //挂靠失败的数据按照省份成组
        //按照省份分组
        Map<String, List<BaseCaseEntity>> provinceGroupMap = noBindList.stream()
                .collect(Collectors.groupingBy(data -> getString(data.getBaseProvinceCode())));

        //按照当事人分组
        List<List<BaseCaseEntity>> noBindGroupList = new ArrayList<>();
        provinceGroupMap.forEach((k,v)->{
            if(Strings.isNullOrEmpty(k)){
                //没有省份的数据丢弃
            }else{
                noBindGroupList.add(v);
            }
        });

        //挂靠失败的案子重新放入分组
        outList.addAll(noBindGroupList);

        return outList;
    }

    /**
     * 判断案号是否有交集
     * @param caseNoA
     * @param caseNoB
     * @return
     */
    static boolean isCaseNoJoin(String caseNoA,String caseNoB){
        Set<String> caseNoASet = Sets.newHashSet(caseNoA.split(","));
        Set<String> caseNoBSet = Sets.newHashSet(caseNoB.split(","));

        return isJoin(caseNoASet,caseNoBSet);
    }

    static String cleanCaseNo(String caseNo){
        return full2Half(CaseNoCleanUDF.evaluate(getString(caseNo).split("之")[0]));
    }


    /**
     * 判断两个集合是否有交集
     *
     * @param aSet
     * @param bSet
     * @return
     */
    static boolean isJoin(Set<String> aSet, Set<String> bSet) {
        long aInbCount = aSet.stream().filter(item -> bSet.contains(item) ).count();
        long bInaCount = bSet.stream().filter(item -> aSet.contains(item)).count();
        return aInbCount > 0 || bInaCount > 0;
    }


    /**
     * 判断是否某
     * @param name
     * @return
     */
    public static boolean isMouMouPerson(String name){
        if(Strings.isNullOrEmpty(name)){
            return false;
        }
        if(name.length()<=4 &&
                (name.contains("某")
                        || name.contains("*")
                        || name.contains("X")
                        || name.contains("x")
                        || name.contains("ｘ")
                        || name.contains("Ｘ"))){
            return true;
        }
        return false;
    }

    /**
     * 按照一定规则再聚合
     * @param groupList
     */
    static List<List<BaseCaseEntity>> reGroup(List<List<BaseCaseEntity>> groupList){
        List<List<BaseCaseEntity>> originalList = Lists.newArrayList(groupList);
        List<List<BaseCaseEntity>> newGroupList = new ArrayList<>();
        Set<String> marked = new HashSet<>();
        for (List<BaseCaseEntity> item : groupList) {
            Set<String> baseIdSet = item.stream().map(BaseCaseEntity::getBaseId).collect(Collectors.toSet());
            long isMark = marked.stream().filter(data->baseIdSet.contains(data)).count();
            if(isMark>0){
                continue;
            }

            List<BaseCaseEntity> subList = new ArrayList<>();
            for (List<BaseCaseEntity> sub : originalList) {
                //符合特定规则的数据可以被聚合
                if(specialGroupRule1(item,sub)
                        ||specialGroupRule2(item,sub)
                        ||specialGroupRule3(item,sub)
                        ||specialGroupRule4(item,sub)){
                    subList.addAll(sub);
                }

            }


            //当前分组
            List<BaseCaseEntity> gpList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(subList)){
                for (BaseCaseEntity sub : subList) {
                    if(!baseIdSet.contains(sub.getBaseId())){
                        gpList.add(sub);
                    }
                }
            }
            gpList.addAll(item);
            newGroupList.add(gpList);

            //标记为已处理
            for (List<BaseCaseEntity> f1 : newGroupList) {
                for (BaseCaseEntity f2 : f1) {
                    marked.add(f2.getBaseId());
                }
            }

        }
        return newGroupList;
//        System.err.println(newGroupList);
    }

    /**
     * 金额浮动在百分比内
     * @param amt1
     * @param amt2
     * @param percent
     * @return
     */
    static boolean amtFloatRule(String amt1,String amt2,BigDecimal percent){
        try{
            List<BigDecimal> list = Lists.newArrayList(new BigDecimal(amt1),new BigDecimal(amt2)).stream().sorted().collect(Collectors.toList());

            BigDecimal amtA = list.get(0);
            BigDecimal amtB = list.get(1);
            return amtB.subtract(amtA).compareTo(amtA.multiply(percent))<=0;

        }catch (Exception e){

        }
        return Objects.equal(amt1,amt2);
    }

    //特殊聚合规则
    static boolean specialGroupRule1(List<BaseCaseEntity> item, List<BaseCaseEntity> sub){
        //被执行执行标地+法院+时间一样
        List<ZXEntity> zxList = convertList(item.stream()
                .filter(data-> CaseCategoryEnum.ZX.equals(data.getBaseCaseCategoryEnum())).collect(Collectors.toList()), ZXEntity.class);
        List<ZXEntity> subZxList =  convertList(sub.stream()
                .filter(data-> CaseCategoryEnum.ZX.equals(data.getBaseCaseCategoryEnum())).collect(Collectors.toList()), ZXEntity.class);

        for (ZXEntity zx : zxList) {
            for (ZXEntity subZx : subZxList) {
                if(amtFloatRule(zx.getBiaodi(),subZx.getBiaodi(),new BigDecimal("0.05"))
                        && Objects.equal(zx.getBaseCaseNo(),subZx.getBaseCaseNo())
                        && Objects.equal(zx.getBaseCourt(),subZx.getBaseCourt())
                        && Objects.equal(zx.getLiandate(),subZx.getLiandate())){
                    return true;
                }
            }
        }
        return false;
    }
    static  boolean specialGroupRule2(List<BaseCaseEntity> item, List<BaseCaseEntity> sub){
        //失信 法院+依据文书号+时间一样
        List<SXEntity> sxList = convertList(item.stream()
                .filter(data-> CaseCategoryEnum.SX.equals(data.getBaseCaseCategoryEnum())).collect(Collectors.toList()), SXEntity.class);

        List<SXEntity> subSxList =  convertList(sub.stream()
                .filter(data-> CaseCategoryEnum.SX.equals(data.getBaseCaseCategoryEnum())).collect(Collectors.toList()), SXEntity.class);

        for (SXEntity sx : sxList) {
            for (SXEntity subSx : subSxList) {
                if(Objects.equal(sx.getExecuteno(),subSx.getExecuteno())
                        && Objects.equal(sx.getBaseCaseNo(),sx.getBaseCaseNo())
                        && Objects.equal(sx.getBaseCourt(),subSx.getBaseCourt())
                        && Objects.equal(sx.getLiandate(),subSx.getLiandate())){
                    return true;
                }
            }
        }

        return false;
    }
    static  boolean specialGroupRule3(List<BaseCaseEntity> item, List<BaseCaseEntity> sub){
        //终本，同案号，同法院，同日期，同执行标的，AB合并
        List<ZBEntity> sxList = convertList(item.stream()
                .filter(data-> CaseCategoryEnum.ZB.equals(data.getBaseCaseCategoryEnum())).collect(Collectors.toList()), ZBEntity.class);

        List<ZBEntity> subSxList =  convertList(sub.stream()
                .filter(data-> CaseCategoryEnum.ZB.equals(data.getBaseCaseCategoryEnum())).collect(Collectors.toList()), ZBEntity.class);

        for (ZBEntity zb : sxList) {
            for (ZBEntity subZb : subSxList) {
                if(Objects.equal(zb.getBaseCaseNo(),subZb.getBaseCaseNo())
//                        && Objects.equal(zb.getExecuteobject(),subZb.getExecuteobject())
                        && Objects.equal(zb.getBaseCourt(),subZb.getBaseCourt())
                        && Objects.equal(zb.getJudgedate(),subZb.getJudgedate())){
                    return true;
                }
            }
        }

        return false;
    }
    static  boolean specialGroupRule4(List<BaseCaseEntity> item, List<BaseCaseEntity> sub){
        //限高，同案号，同法院，同日期，AB合并
        List<XGEntity> sxList = convertList(item.stream()
                .filter(data-> CaseCategoryEnum.XG.equals(data.getBaseCaseCategoryEnum())).collect(Collectors.toList()), XGEntity.class);

        List<XGEntity> subSxList =  convertList(sub.stream()
                .filter(data-> CaseCategoryEnum.XG.equals(data.getBaseCaseCategoryEnum())).collect(Collectors.toList()), XGEntity.class);

        for (XGEntity xg : sxList) {
            for (XGEntity subXg : subSxList) {
                if(Objects.equal(xg.getBaseCaseNo(),subXg.getBaseCaseNo())
                        && Objects.equal(xg.getBaseCourt(),subXg.getBaseCourt())
                        && Objects.equal(xg.getJudgedate(),subXg.getJudgedate())){
                    return true;
                }
            }
        }

        return false;
    }



    public static void main(String[] args) {
        String str = "被申请人（一审原告、二审被上诉人）";
        String role1 = str.substring(0, str.indexOf("（"));
        String role2 =  str.substring( str.indexOf("（")+1, str.indexOf("）"));
        System.out.println(role1);
        System.out.println(role2);

        System.out.println(safeContains("张三","行政许可",""));

        amtFloatRule("132778.00","135390.00",new BigDecimal("0.03"));
    }
}
