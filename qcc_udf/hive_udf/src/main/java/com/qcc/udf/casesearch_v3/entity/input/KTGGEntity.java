package com.qcc.udf.casesearch_v3.entity.input;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.casesearch_v3.enums.CaseCategoryEnum;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: zhangqiang
 * @Date: 2020/11/11 17:54
 * @Description:开庭公告
 */
@Data
public class KTGGEntity  extends BaseCaseEntity {


    private String id;
    private String anno;
    private String companynames;
    private String isvalid;
    private String courtname;
    private String provincecode;


    private long liandate;
    private String casereason;

    private String nameandkeyno;
    private String prosecutorlist;
    private String defendantlist;
    //法庭
    private String executeUnite;

    private List<NameAndKeyNoEntity> nameandkeynoEntityList;
    private List<NameAndKeyNoEntity> prosecutorlistoEntityList;
    private List<NameAndKeyNoEntity> defendantlistoEntityList;

    public static List<KTGGEntity> convert(List<String> jsonList) {
        List<KTGGEntity> list = new ArrayList<>();
        KTGGEntity entity = null;
        if(CollectionUtils.isEmpty(jsonList)){
            return list;
        }
        for (String json : jsonList) {
            if(Strings.isNullOrEmpty(json)){
                continue;
            }
            entity = JSON.parseObject(json, KTGGEntity.class);

            if(entity == null  || Strings.isNullOrEmpty(entity.getId())){
                continue;
            }

            String str = entity.getNameandkeyno();
            Map<String,String> nameRoleMap = new HashMap<>();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setNameandkeynoEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getNameandkeynoEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                    namekey.setLawFirmList(null);
                    nameRoleMap.put(namekey.getName(),namekey.getRole());
                }
            }

            str = entity.getProsecutorlist();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setProsecutorlistoEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getProsecutorlistoEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                    namekey.setRole(nameRoleMap.getOrDefault(namekey.getName(),""));
                    namekey.setLawFirmList(null);
                }
            }

            str = entity.getDefendantlist();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setDefendantlistoEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getDefendantlistoEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                    namekey.setRole(nameRoleMap.getOrDefault(namekey.getName(),""));
                    namekey.setLawFirmList(null);
                }
            }

            //公共字段赋值
            entity.setBaseCaseNo(entity.getAnno());
            entity.setBaseCaseCategoryEnum(CaseCategoryEnum.KTGG);
            if(!Strings.isNullOrEmpty(entity.getCompanynames())){
                entity.setBaseSearchWordSet(Arrays.stream(entity.getCompanynames().split(","))
                        .collect(Collectors.toSet()));
            }

            entity.setBaseCaseReason(entity.getCasereason());
            entity.setBaseCourt(entity.getCourtname());
            entity.setBaseProvinceCode(entity.getProvincecode());
            entity.setBaseNameKeyNoList(entity.getNameandkeynoEntityList());
            entity.setBaseId(entity.getBaseCaseCategoryEnum().getType()+"_"+entity.getId());
            String caseType= CommonV3Util.getCaseType(CommonV3Util.getCaseNo(entity.getBaseCaseNo()));
            //案件类型为空的数据直接过滤
            if(parquet.Strings.isNullOrEmpty(caseType)){
                continue;
            }
            list.add(entity);
        }
        return list;
    }

}
