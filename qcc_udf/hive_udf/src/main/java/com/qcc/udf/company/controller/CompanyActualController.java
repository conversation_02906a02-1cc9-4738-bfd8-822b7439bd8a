package com.qcc.udf.company.controller;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Auther: nixb
 * @Date: 2020/9/24 17:29
 * @Description:
 */


public class CompanyActualController {
    public String pkeyno;

    public String pcompanyname;

    public BigDecimal stockpercent;

    public String keynopath;

    public Integer isoper;

    public Integer isgp;

    public String gppath;

    public boolean hasimage;

    public List<ActaulDataExtend> dataextend;

    public Integer flag;

    public String getPkeyno() {
        return pkeyno;
    }

    public void setPkeyno(String pkeyno) {
        this.pkeyno = pkeyno;
    }

    public String getPcompanyname() {
        return pcompanyname;
    }

    public void setPcompanyname(String pcompanyname) {
        this.pcompanyname = pcompanyname;
    }

    public BigDecimal getStockpercent() {
        return stockpercent;
    }

    public void setStockpercent(BigDecimal stockpercent) {
        this.stockpercent = stockpercent;
    }

    public String getKeynopath() {
        return keynopath;
    }

    public void setKeynopath(String keynopath) {
        this.keynopath = keynopath;
    }

    public Integer getIsoper() {
        return isoper;
    }

    public void setIsoper(Integer isoper) {
        this.isoper = isoper;
    }

    public Integer getIsgp() {
        return isgp;
    }

    public void setIsgp(Integer isgp) {
        this.isgp = isgp;
    }

    public String getGppath() {
        return gppath;
    }

    public void setGppath(String gppath) {
        this.gppath = gppath;
    }

    public boolean getHasimage() {
        return hasimage;
    }

    public void setHasimage(boolean hasimage) {
        this.hasimage = hasimage;
    }

    public boolean isHasimage() {
        return hasimage;
    }

    public List<ActaulDataExtend> getDataextend() {
        return dataextend;
    }

    public void setDataextend(List<ActaulDataExtend> dataextend) {
        this.dataextend = dataextend;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }
}
