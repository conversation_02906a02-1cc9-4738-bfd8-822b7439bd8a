package com.qcc.udf.temp;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date ：Created in 2020/2/26 16:39
 * @description：
 */
public class CommonUtil {
	public static String removeSpecialContent(String param) {
		if (param == null || param.trim().length() == 0) {
			return "";
		}
		if (isNumber(param)) {
			return "";
		}
		String key[] = new String[]{"无", "0", "个体", "暂无", "-", "暂缺", "空", "\"空\"", "NULL", "null", "\"null\"", "NaN", "NaT"};
		for (String string : key) {
			if (param.trim().equals(string)) {
				return "";
			}
		}

		return param;
	}

	public static String removeSpecialContentWithoutNum(String param) {
		if (param == null || param.trim().length() == 0) {
			return "";
		}
		String key[] = new String[]{"无", "0", "个体", "暂无", "-", "暂缺", "空", "\"空\"", "NULL", "null", "\"null\"", "NaN", "NaT"};
		for (String string : key) {
			if (param.trim().equals(string)) {
				return "";
			}
		}

		return param;
	}

	public static Boolean isNumber(String str) {
		String regex = "^[0-9]+$";
		Pattern pattern = Pattern.compile(regex);
		Matcher match = pattern.matcher(str);
		return match.matches();
	}

	/** 全角转半角 */
	public static String full2Half(String input) {
		char[] c = input.toCharArray();

		for(int i = 0; i < c.length; ++i) {
			if (c[i] == 12288) {
				c[i] = ' ';
			} else if (c[i] > '\uff00' && c[i] < '｟') {
				c[i] -= 'ﻠ';
			}
		}

		return new String(c);
	}

	/**
	 * @param param 传入参数
	 * @return String 处理后的结果
	 */
	public static String getCompanyNameByName(Object param) {
		// 如果全空，则返回空值
		if (param == null || "".equals(param)) {
			return "";
		}
		// 全部英文的情况下，返回英文括号
		Matcher m = Pattern.compile("[\u4e00-\u9fa5]").matcher(param.toString());
		if (!m.find()) {
			return param.toString().replace("（", "(").replace("）", ")");
		} else {
			// 其他情况，返回中文括号
			return param.toString().replace("(", "（").replace(")", "）");
		}
	}

	/**
	 * 判断是否符合正则
	 * @param regex
	 * @param date
	 * @return
	 */
	public static boolean isMatchRegx(String regex,String date) {
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(date);
		return matcher.matches();
	}

	public static synchronized String getProvinceCodeByName(String name) {
		switch (name) {
			case "北京":
				return "BJ";
			case "天津":
				return "TJ";
			case "河北":
				return "HB";
			case "山西":
				return "SX";
			case "内蒙古":
				return "NMG";
			case "辽宁":
				return "LN";
			case "吉林":
				return "JL";
			case "黑龙江":
				return "HLJ";
			case "上海":
				return "SH";
			case "江苏":
				return "JS";
			case "浙江":
				return "ZJ";
			case "安徽":
				return "AH";
			case "福建":
				return "FJ";
			case "江西":
				return "JX";
			case "山东":
				return "SD";
			case "河南":
				return "HEN";
			case "湖北":
				return "HUB";
			case "湖南":
				return "HUN";
			case "广东":
				return "GD";
			case "广西":
				return "GX";
			case "海南":
				return "HAIN";
			case "重庆":
				return "CQ";
			case "四川":
				return "SC";
			case "贵州":
				return "GZ";
			case "云南":
				return "YN";
			case "西藏":
				return "XZ";
			case "陕西":
				return "SAX";
			case "甘肃":
				return "GS";
			case "青海":
				return "QH";
			case "宁夏":
				return "NX";
			case "新疆":
				return "XJ";
			default:
				return "CN";
		}
	}

	/**
	 * 去除html标签
	 * @param content
	 * @return
	 */
	public static String cleanHtmlTag(String content) {
		if (org.apache.commons.lang.StringUtils.isNotBlank(content)) {

			//html转义符转化为标签对
			content = replaceHtml(content);

			return content
					.replaceAll("<[a-zA-Z].*?>", "")
					.replaceAll("</[a-zA-Z].*?>", "").trim();
		}
		return content;
	}

	/**
	 * 替换掉html转义字符
	 *
	 * @param content 带有html转义字符的内容
	 * @return 去除转义后的内容
	 */
	public static String replaceHtml(String content) {
		content = content.replace("&#34;", "\"");
		content = content.replace("&quot;", "\"");
		content = content.replace("&#38;", "&");
		content = content.replace("&amp;", "&");
		content = content.replace("&#60;", "<");
		content = content.replace("&lt;", "<");
		content = content.replace("&#62;", ">");
		content = content.replace("&gt;", ">");
		content = content.replace("&#160;", "");
		content = content.replace("&nbsp;", "");
		return content;
	}

	/**
	 * 根据areaCode 获取 provinceCode
	 * @param areaCode
	 * @return
	 */
	public static  String getProvinceCodeByAreaCode(String areaCode) {
		if ("".equals(areaCode) || areaCode.length() < 6) {
			return "";
		}
		String pronvinceCode = areaCode.substring(0,2);
		switch (pronvinceCode) {
			case "11":          // 11   北京
				return "BJ";
			case "12":          // 12   天津
				return "TJ";
			case "13":          // 13   河北
				return "HB";
			case "14":          // 14   山西
				return "SX";
			case "15":          // 15   内蒙古
				return "NMG";
			case "21":          // 21   辽宁
				return "LN";
			case "22":          // 22   吉林
				return "JL";
			case "23":          // 23   黑龙江
				return "HLJ";
			case "31":          // 31   上海
				return "SH";
			case "32":          // 32   江苏
				return "JS";
			case "33":          // 33   浙江
				return "ZJ";
			case "34":          // 34   安徽
				return "AH";
			case "35":          // 35   福建
				return "FJ";
			case "36":          // 36   江西
				return "JX";
			case "37":          // 37   山东
				return "SD";
			case "41":          // 41   河南
				return "HEN";
			case "42":          // 42   湖北
				return "HUB";
			case "43":          // 43   湖南
				return "HUN";
			case "44":          // 44   广东
				return "GD";
			case "45":          // 45   广西
				return "GX";
			case "46":          // 46   海南
				return "HAIN";
			case "50":          // 50   重庆
				return "CQ";
			case "51":          // 51   四川
				return "SC";
			case "52":          // 52   贵州
				return "GZ";
			case "53":          // 53   云南
				return "YN";
			case "54":          // 54   西藏
				return "XZ";
			case "61":          // 61   陕西
				return "SAX";
			case "62":          // 62   甘肃
				return "GS";
			case "63":          // 63   青海
				return "QH";
			case "64":          // 64   宁夏
				return "NX";
			case "65":          // 65   新疆
				return "XJ";
			default:
				return "";
		}
	}

	/**
	 * 身份证打码  3201111111110317 -> ****
	 * @param content
	 * @return
	 */
	public static String encryIdcard(String content){
		if(content != null){
			Pattern titlePattern = Pattern.compile("[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]");
			Matcher matcher = titlePattern.matcher(content);
			while (matcher.find()){
				content = content.replace(matcher.group(),"****");
			}
		}
		return  content;
	}

	/**
	 * 安全toString
	 *
	 * @param obj
	 * @return
	 */
	public static String convertString(Object obj) {
		return obj == null ? "" : obj.toString().trim();
	}

	public static String cleanBlank(String context) {
		context = context.replaceAll("\r\n|\n", "");
		context = context.replaceAll(" | |　", "");
		return context;
	}

	public static String removeSpecial(String context) {
		context = context.replaceAll("[\\*|/|@|#|￥|%|&|？|\\.|。|，|；|;|！|~|\\+|‘|’|\"|']", "");
		return context;
	}

	public static String cleanPunct(String context) {
		context = CommonUtil.full2Half(context);
		context = removeSpecial(context);
		context = context.replaceAll("\\[|【|〔|﹝|<", "(");
		context = context.replaceAll("\\]|】|〕|﹞|>", ")");
		context =cleanBlank(context);
		return context;
	}

	public static void main(String[] args) {
		String context ="深南临地审﹝2020﹞0223号";
//		String s = cleanPunct(context);
		int source =1;
		String groupContent ="深地名许字ns201810129号6bc7e7ccdb755391651316a0227c059b20180408";
		String join = StringUtils.join(String.valueOf(source), groupContent);
		System.out.println(join);
		String md5= MD5Util.encode(join);
		System.out.println(md5);
	}
}
