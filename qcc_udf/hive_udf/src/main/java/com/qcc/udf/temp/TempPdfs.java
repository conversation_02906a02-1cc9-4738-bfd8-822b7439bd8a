package com.qcc.udf.temp;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.List;

/**
 *
 *
 *  测试使用  md5
 * <AUTHOR>
 *
 */
public class TempPdfs extends UDF {
    public String evaluate(String s) throws NoSuchAlgorithmException {
        if (StringUtils.isNotEmpty(s)){
            List<String> exts = Arrays.asList("xls", "xlsx", "doc", "docx");
            if (s.contains("pdf")){
                boolean f=false;
                for (String ext : exts) {
                    if (s.contains(ext)) {
                        f=true;
                        break;
                    }
                }
                if (f){
                    return "1";
                }
            }
        }
        return "0";
    }

    public static void main(String[] args) throws NoSuchAlgorithmException {
        TempPdfs tempMD5Util = new TempPdfs();
        System.out.println(tempMD5Util.evaluate("https://qccdata.qichacha.com/CaseSupervisePunish/4c614c40fc61d03f4905e6386b52c63d.xlsx.pdf"));

    }
}
