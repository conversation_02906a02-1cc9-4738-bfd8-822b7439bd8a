package com.qcc.udf.product;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

public class ProductFirstItemUDF extends UDF {
    public static String evaluate(String name) {
        if (StringUtils.isEmpty(name)) {
            return "";
        }

        char[] charArray = name.toCharArray();
        HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();
        //设置大小写格式
        defaultFormat.setCaseType(HanyuPinyinCaseType.UPPERCASE);
        //设置声调格式：
        defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);

        //匹配中文,非中文转换会转换成null
        if (Character.toString(charArray[0]).matches("[\\u4E00-\\u9FA5]+")) {
            String[] hanyuPinyinStringArray = new String[0];
            try {
                hanyuPinyinStringArray = PinyinHelper.toHanyuPinyinStringArray(charArray[0], defaultFormat);
            } catch (BadHanyuPinyinOutputFormatCombination badHanyuPinyinOutputFormatCombination) {
                badHanyuPinyinOutputFormatCombination.printStackTrace();
            }
            if (hanyuPinyinStringArray != null && hanyuPinyinStringArray.length > 0) {
                return hanyuPinyinStringArray[0].substring(0, 1);
            }
        } else {
            String s = String.valueOf(charArray[0]).toUpperCase();
            if (s.matches("[A-Z]")) {
                return s;
            }
        }
        return "0";
    }

    public static void main(String[] args) {
        System.out.println(ProductFirstItemUDF.evaluate("斯马特"));
    }
}
