package com.qcc.udf;

import com.alibaba.fastjson.JSONObject;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by xpc on 2018/10/23.
 */
public class CompanyTagsCount extends UDF {
    /**
     * @param keyNo   公司keyNo
     * @param datastr 公司新闻数组
     * @return json
     */
    public static String evaluate(String keyNo, String datastr) throws Exception {
        Map<String, List<JSONObject>> year_month = new HashMap<>();
        Map<String, List<JSONObject>> year_month_day = new HashMap<>();
        List<JSONObject> all_datas = new ArrayList<>();
        try {
            String[] data = datastr.split("\\|\\|");
            for (String d : data) {
                String[] ds = d.split(",");
                String pt = ds[0];
                JSONObject j = new JSONObject();
                String[] pts = pt.split("-");
                String year = "" + pts[0];
                String month = "" + pts[1];
                String day = "" + pts[2];
                j.put("Year", year);
                j.put("Month", month);
                j.put("Day", day);
                j.put("Code", ds[1]);
                j.put("Count", ds[2]);

                all_datas.add(j);
                year_month.put(year + "-" + month, new ArrayList<>());
                year_month_day.put(year + "-" + month + "-" + day, new ArrayList<>());
            }

            for (JSONObject d : all_datas) {
                JSONObject o = new JSONObject();
                o.put("Code", d.get("Code"));
                o.put("Count", Integer.parseInt(d.getString("Count")));
                year_month_day.get(d.getString("Year") + "-" + d.getString("Month") + "-" + d.getString("Day")).add(o);
            }

            year_month_day.forEach((k, v) -> {
                String[] ymd = k.split("-");
                JSONObject o = new JSONObject();
                o.put("Day", Integer.parseInt(ymd[2]));
                o.put("TypeAggList", v);
                year_month.get(ymd[0] + "-" + ymd[1]).add(o);
            });

            List<JSONObject> list = new ArrayList<>();
            year_month.forEach((k, v) -> {
                JSONObject o = new JSONObject();
                String[] ym = k.split("-");
                o.put("Year", Integer.parseInt(ym[0]));
                o.put("Month", Integer.parseInt(ym[1]));
                o.put("DayAggList", v);
                list.add(o);
            });

            JSONObject c = new JSONObject();
            c.put("KeyNo", keyNo);
            c.put("MonthList", list);

            return c.toJSONString();
        } catch (Exception e) {
            JSONObject j = new JSONObject();
            j.put("KeyNo", keyNo);
            j.put("MonthList", new ArrayList());

            return j.toJSONString();
        }
    }
}
