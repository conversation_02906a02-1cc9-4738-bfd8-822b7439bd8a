package com.qcc.udf;

import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONTokener;

public class GetJsonArraySparkSql extends UDF {

    public static String evaluate(String jsonArrayStr) throws JSONException {

        if (StringUtils.isBlank(jsonArrayStr) || StringUtils.isEmpty(jsonArrayStr)) {
            return null;
        }
        try {

            byte[] enp = new byte[]{(byte) 1};
            String sepcialWords = new String(enp);
            jsonArrayStr = jsonArrayStr.replace(sepcialWords, "");
            String textList = new String();

            if (!jsonArrayStr.trim().startsWith("[")) {
                textList = jsonArrayStr;
            } else {


                JSONArray jsonArray = new JSONArray(new JSONTokener(jsonArrayStr));

                for (int i = 0; i < jsonArray.length(); i++) {
                    if (i == jsonArray.length() - 1) {
                        textList += jsonArray.getJSONObject(i).toString();
                    } else {
                        textList += jsonArray.getJSONObject(i).toString() + sepcialWords;
                    }
                }

            }
            return textList;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }
}
