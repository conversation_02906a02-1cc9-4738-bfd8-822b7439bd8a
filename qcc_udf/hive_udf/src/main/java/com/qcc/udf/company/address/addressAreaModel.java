package com.qcc.udf.company.address;

import java.util.List;
public class addressAreaModel {
    private String content;
    private String city;
    private List<Countys> countys;
    private List<String> title;
    public void setContent(String content) {
        this.content = content;
    }
    public String getContent() {
        return content;
    }

    public void setCity(String city) {
        this.city = city;
    }
    public String getCity() {
        return city;
    }

    public void setCountys(List<Countys> countys) {
        this.countys = countys;
    }
    public List<Countys> getCountys() {
        return countys;
    }

    public void setTitle(List<String> title) {
        this.title = title;
    }
    public List<String> getTitle() {
        return title;
    }
    public class Countys {

        private String content;
        private List<String> title;
        public void setContent(String content) {
            this.content = content;
        }
        public String getContent() {
            return content;
        }

        public void setTitle(List<String> title) {
            this.title = title;
        }
        public List<String> getTitle() {
            return title;
        }
    }
}
