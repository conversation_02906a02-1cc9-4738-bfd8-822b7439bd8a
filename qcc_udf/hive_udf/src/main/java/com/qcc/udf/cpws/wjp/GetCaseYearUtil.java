package com.qcc.udf.cpws.wjp;

import com.qcc.udf.cpws.CommonUtil;
import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2024/2/18
 **/
public class GetCaseYearUtil {


    public static String evaluate(String caseNo, String trialDate, String publishdate) {
        if (StringUtils.isNotEmpty(trialDate) && trialDate.contains("月") && (trialDate.contains("日") || trialDate.contains("号"))){
            try{
                int caseYear = getYearFromCaseNo(caseNo);
                String date = getYmdFromDate(trialDate);
                if (caseYear > 0){
                    int year = 0;
                    if (StringUtils.isNotEmpty(publishdate) && publishdate.length() == 8){
                        int punishYear = Integer.parseInt(publishdate.substring(0,4));
                        if ((punishYear - caseYear) == 1 && (date.startsWith("1-") || date.startsWith("2-") || date.startsWith("01-") || date.startsWith("02-"))){                            year = punishYear;
                        }
                        if ((punishYear - caseYear) != 1){
                            year = caseYear;
                        }
                    }else{
                        year = caseYear;
                    }

                    // 有年份的情况下
                    if (year > 0){
                        Date dt = CommonDateCleanUtil.convertDate(String.valueOf(year).concat("-").concat(date).trim());
                        if (dt != null) {
                            return parseDateToStr(dt, "yyyy-MM-dd HH:mm:ss");
                        }
                    }
                }
            }catch (Exception ex){

            }
        }

        return null;
    }

    public static int getYearFromCaseNo(String param){
        int result = 0;

        if (StringUtils.isNotEmpty(param)){
            Pattern extractPattern = Pattern.compile("(（[0-9]{4}）)");
            Matcher matcher = extractPattern.matcher(param.replace("(", "（").replace(")", "）"));
            if (matcher.find()){
                result = Integer.parseInt(matcher.group().replace("（", "").replace("）", ""));
            }
        }

        return result;
    }

    public static String getYmdFromDate(String param){
        String result = "";

        if (StringUtils.isNotEmpty(param)){
            param = CommonUtil.full2Half(param);
            param = param.replace("上午", " ");
            param = param.replace(" ","");
            param = param.replace("(星期一)", " ");
            param = param.replace("(星期二)", " ");
            param = param.replace("(星期三)", " ");
            param = param.replace("(星期四)", " ");
            param = param.replace("(星期五)", " ");
            param = param.replace("(星期六)", " ");
            param = param.replace("(星期日)", " ");
            param = param.replace("星期一", " ");
            param = param.replace("星期二", " ");
            param = param.replace("星期三", " ");
            param = param.replace("星期四", " ");
            param = param.replace("星期五", " ");
            param = param.replace("星期六", " ");
            param = param.replace("星期日", " ");
            param = param.replace("下午1:", " 13:");
            param = param.replace("下午2:", " 14:");
            param = param.replace("下午3:", " 15:");
            param = param.replace("下午4:", " 16:");
            param = param.replace("下午5:", " 17:");
            param = param.replace("下午6:", " 18:");
            param = param.replace("下午7:", " 19:");
            param = param.replace("下午8:", " 20:");
            param = param.replace("下午9:", " 21:");
            param = param.replace("下午10:", " 22:");
            param = param.replace("下午11:", " 23:");
            param = param.replace("下午12:", " 24:");
            param = param.replace("下午", " ");
            param = param.replace("日", " ");
            param = param.replace("月", "-");
            param = param.replace("分", " ");
            param = param.replace("时", ":");
            param = param.replace("(一)", " ");
            param = param.replace("(二)", " ");
            param = param.replace("(三)", " ");
            param = param.replace("(四)", " ");
            param = param.replace("(五)", " ");
            param = param.replace("(六)", " ");
            param = param.replace("(七)", " ");
            param = param.replace("(八)", " ");
            param = param.replace("(九)", " ");
            param = param.replace("\t","");
            param = param.replace("号", " ");
            param = param.replace("  ", " ").replace("  ", " ").replace("  ", " ").replace("  ", " ");
            if (param.startsWith(" ")){
                param = param.substring(1);
            }
            param = param.trim();

            if (param.contains("--")){
                param = param.split("--")[0];
            }

            result = param;

        }

        return result;
    }

    public static String parseDateToStr(Date time, String timeFromat) {
        DateFormat dateFormat = new SimpleDateFormat(timeFromat);
        return dateFormat.format(time);
    }
}
