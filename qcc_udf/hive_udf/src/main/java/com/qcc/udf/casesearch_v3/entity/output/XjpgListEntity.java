package com.qcc.udf.casesearch_v3.entity.output;

import lombok.Data;
import com.alibaba.fastjson.annotation.JSONField;

import java.util.LinkedList;
import java.util.List;

@Data
public class XjpgListEntity  extends BaseCaseOutEntity{
     @JSONField(name = "Id")
    private String id = "";
     @JSONField(name = "NameAndKeyNo")
    private List<NameAndKeyNoEntity> nameAndKeyNo = new LinkedList<>();
     @JSONField(name = "Category")
    private String category = "";
     @JSONField(name = "Target")
    private String target = "";
     @JSONField(name = "PublishDate")
    private Long publishDate = 0L;
     @JSONField(name = "IsValid")
    private Integer isValid = 0;
     @JSONField(name = "TargetNameAndKeyNo")
    private List<NameAndKeyNoEntity> targetNameAndKeyNo = new LinkedList<>();
     @JSONField(name = "Result")
    private String result = "";
}
