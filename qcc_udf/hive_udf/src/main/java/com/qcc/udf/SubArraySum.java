package com.qcc.udf;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;

public class SubArraySum extends UDF {
    public String evaluate(String value) {
        if (StringUtils.isEmpty(value)) {
            return "0";
        }

        String[] strArr = value.split(",");
        BigDecimal sumValue = new BigDecimal(0);
        for (String str : strArr) {
            BigDecimal duValue;
            try {
                duValue = new BigDecimal(str);
            } catch (Exception e) {
                duValue = new BigDecimal(0);
            }
            sumValue = sumValue.add(duValue);
        }
        String result = String.valueOf(sumValue);
        if (StringUtils.isNotEmpty(result)) {
            result = result.replaceAll("\\.0+$", "");
        }
        return result;
    }

//    public static void main(String[] args) {
//        SubArraySum model = new SubArraySum();
//        String a1 = model.evaluate("0");
//        String a2 = model.evaluate("-12");
//        String a3 = model.evaluate("");
//        String a = model.evaluate("900");
//        String b = model.evaluate("900.0");
//        String c = model.evaluate("900.000");
//        String d = model.evaluate("900.12");
//        String e = model.evaluate("900.123");
//        String f = model.evaluate("900.1234");
//        String f1 = model.evaluate("900.");
//        String f2 = model.evaluate("82820862.72");
//        String f3 = model.evaluate("2577455.88,16152.55,106391.57");
//    }
}