package com.qcc.udf.casesearch_v3;

import cn.hutool.core.util.ReUtil;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import com.qcc.udf.cpws.ExtractCaseTypeUDF;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;
import parquet.Strings;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Auther: zhanqgiang
 * @Date: 2020/11/19 10:00
 * @Description: 案号简单清洗
 */
public class CaseNoCleanUDF extends UDF {
    static ExtractCaseTypeUDF CaseTypeUDF = new ExtractCaseTypeUDF();
    public static String evaluate(String caseNo) {
        if (Strings.isNullOrEmpty(caseNo)) {
            return "";
        }
        caseNo = CommonV3Util.full2Half(caseNo);
        caseNo = caseNo.replaceAll(" | |　", "");
        caseNo = caseNo.replaceAll("\\[|【|〔|﹝|<", "(");
        caseNo = caseNo.replaceAll("\\]|】|〕|﹞|>", ")");
        caseNo = caseNo.replaceAll("\r\n|\n", "");
        //TODO 去除脏字符:`.^★;
        caseNo = caseNo.replaceAll("`|\\.|\\^|★", "");
        //针对之一之二或编号处理：（2018）定民二初字第0040370号 ---> （2018）定民二初字第40370号
        //TODO 按照顿号分割后是否是标准案号，不是则不拆分
        boolean dunHaoFlag = canSplitDunHao(caseNo);
        String split = ",";
        if(dunHaoFlag){
            split = split+"|、";
        }
        String[] annos = caseNo.split(split);
//        String[] annos = caseNo.split(",");
        Set<String> annoSet = Arrays.stream(annos)
                .filter(anno -> StringUtils.isNotBlank(anno))
                .map(anno -> numberHandle(anno))
                .map(anno -> fixErrPrefix(anno))
                .collect(Collectors.toSet());

        return String.join(",", annoSet);
    }

    static boolean canSplitDunHao(String caseNo){
        String[] annos = caseNo.split("、");
        if(annos.length==1){
            return true;
        }
        for (String anno : annos) {
            if(anno.length()<10|| Strings.isNullOrEmpty(CaseTypeUDF.evaluate(anno))){
                return false;
            }
        }

        return true;
    }

    private static int getAnnoYear(String anno) {
        String year = ReUtil.get("\\d{4}", anno, 0);
        if (StringUtils.isNotBlank(year) && StringUtils.isNumeric(year)) {
            return Integer.parseInt(year);
        }
        return 0;
    }

    private static String numberHandle(String annoRes) {
        if (annoRes.contains("之")) {
            annoRes = annoRes.substring(0, annoRes.lastIndexOf("之"));
        }
        String rule6 = ".*\\d+号?$";
        boolean match = ReUtil.isMatch(rule6, annoRes);
        if (!match) {
            return annoRes;
        }
        annoRes = new StringBuffer(annoRes).reverse().toString();
        String numberRes = ReUtil.get("\\d+", annoRes, 0);
        numberRes = numberRes.replaceAll("0+$", "");
        annoRes = numberRes + ReUtil.delPre("\\d+", annoRes);
        annoRes = new StringBuffer(annoRes).reverse().toString();
        return annoRes.endsWith("号") ? annoRes : annoRes + "号";
    }


    public static String fixErrPrefix(String anno) {
        int year = getAnnoYear(anno);
        //15年之前案号不处理
        if(year<2016){
            return anno;
        }
        if (anno.length() < 6) {
            return anno;
        }
        //(2021苏0585民初8267号
        String prefix = anno.substring(0, 6);
        boolean match = ReUtil.isMatch("\\(\\d{4}[\\u4e00-\\u9fa5]", prefix);
        String newAnno = "";
        if (match) {
            newAnno = anno.substring(0, 5) + ")" + anno.substring(5);
        }


        //2021)苏0585民初8267号
        prefix = anno.substring(0, 5);
        match = ReUtil.isMatch("\\d{4}\\)", prefix);
        if (match && "".equals(newAnno)) {
            newAnno = "(" + prefix + anno.substring(5);
        }

        //2021年苏0585民初8267号
        match = ReUtil.isMatch("\\d{4}\\年", prefix);
        if (match && "".equals(newAnno)) {
            newAnno = "(" + prefix.substring(0, 4) + ")" + anno.substring(5);
        }

        //2021苏0585民初8267号
        prefix = anno.substring(0, 4);
        match = ReUtil.isMatch("\\d{4}", prefix);
        if (match && "".equals(newAnno)) {
            newAnno = "(" + prefix + ")" + anno.substring(4);
        }
        if ("".equals(newAnno)) {
            newAnno = anno;
        }

        return newAnno;

    }

    public static void main(String[] args) {

        System.out.println(evaluate("(2015川1403民初140号"));
//        System.out.println(evaluate("2015年川1403民初140号"));
//        System.out.println(evaluate("2015)川1403民初140号"));
    }

}
