package com.qcc.udf;


import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.TypeAdapter;
import com.google.gson.internal.LinkedTreeMap;
import com.google.gson.reflect.TypeToken;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;
import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentLengthException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDTF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class json_tuple extends GenericUDTF{

    @Override
    public void close() throws HiveException {}

    @Override
    public StructObjectInspector initialize(ObjectInspector[] args)
            throws UDFArgumentException {
        if (args.length != 9) {
            throw new UDFArgumentLengthException("ExplodeMap takes only 9 argument");
        }

        ArrayList<String> fieldNames = new ArrayList<String>();
        ArrayList<ObjectInspector> fieldOIs = new ArrayList<ObjectInspector>();
        for(int i=0;i<8;i++){
            fieldNames.add("col"+i);
            fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        }

        return ObjectInspectorFactory.getStandardStructObjectInspector(fieldNames,fieldOIs);
    }

    @SuppressWarnings("unchecked")
    @Override
    public void process(Object[] args) throws HiveException {
        try{
            GsonBuilder builder = new GsonBuilder();
            builder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {}.getType(), new MapTypeAdapter());
            Gson gson = builder.create();
            Map<String, Object> json = new HashMap<String, Object>();
            json = gson.fromJson(args[0].toString(), new TypeToken<Map<String,Object>>() {}.getType());
            String[] resultlist = new String[8];
            ArrayList<ArrayList<String>> innerlist = new ArrayList<ArrayList<String>>();
            int num = 0;
            String flag = "";
            int tagtmp = 0;
            for(int i=1;i<args.length;i++){
                String result = null;
                String resulttmp = null;
                if(args[i].toString().contains("[]")){
                    String[] keys = args[i].toString().split("\\[\\]");
                    ArrayList<Map<String, Object>> jsonlist = new ArrayList<Map<String, Object>>();
                    if(keys[0].contains(".")){
                        String[] keylist = keys[0].toString().split("\\.");
                        Map<String, Object> jsondata = json;
                        for(int m=0;m<keylist.length;m++){
                            if(jsondata.containsKey(keylist[m])){
                                if(m==(keylist.length-1)){
                                    jsonlist = (ArrayList<Map<String, Object>>) jsondata.get(keylist[m]);
                                }else{
                                    jsondata = (Map<String, Object>) jsondata.get(keylist[m]);
                                }
                            }else{
                                jsonlist = null;
                                break;
                            }
                        }
                    }else{
                        jsonlist = (ArrayList<Map<String, Object>>) json.get(keys[0]);
                    }
                    if(jsonlist!=null){
                        if(keys[1].contains(".")){
                            tagtmp = keys[1].split("\\.").length;
                            for (int j=0;j<jsonlist.size();j++){
                                if(jsonlist.get(j)!=null){
                                    Map<String, Object> jsontmp = (Map<String, Object>) jsonlist.get(j);
                                    ArrayList<String> tmplist = new ArrayList<String>();
                                    for(String key:keys[1].split("\\.")){
                                        if(jsontmp.containsKey(key)){
                                            result = jsontmp.get(key)==null?null:gson.toJson(jsontmp.get(key));
                                        }else{
                                            result = null;
                                        }
                                        if(result!=null&&result.startsWith("\"")){
                                            result =  result.replace("\"", "");
                                        }
                                        tmplist.add(result);
                                    }
                                    innerlist.add(tmplist);
                                }
                            }
                            flag = "2";
                        }else{
                            for (int j=0;j<jsonlist.size();j++){
                                if(jsonlist.get(j)!=null){
                                    Map<String, Object> jsontmp = (Map<String, Object>) jsonlist.get(j);
                                    ArrayList<String> tmplist = new ArrayList<String>();
                                    if(jsontmp.containsKey(keys[1])){
                                        result = jsontmp.get(keys[1])==null?null:gson.toJson(jsontmp.get(keys[1]));
                                    }else{
                                        result = null;
                                    }
                                    if(result!=null&&result.startsWith("\"")){
                                        result =  result.replace("\"", "");
                                    }
                                    tmplist.add(result);
                                    innerlist.add(tmplist);
                                    flag = "1";
                                }
                            }
                        }
                    }
                }else{
                    if(args[i].toString().contains(".")){
                        Map<String, Object> jsondata = json;
                        long tag = 0;
                        String[] keylist = args[i].toString().split("\\.");
                        for(int m=0;m<(keylist.length-1);m++){
                            if(jsondata.containsKey(keylist[m])){
                                jsondata = (Map<String, Object>) jsondata.get(keylist[m]);
                            }else{
                                tag = 1;
                                break;
                            }
                        }
                        if(tag==0){
                            result = gson.toJson(jsondata.get(keylist[keylist.length-1]));
                            if(result!=null&&result.startsWith("\"")){
                                result =  result.replace("\"", "");
                            }
                        }else{
                            result = null;
                        }
                    }else{
                        if(json.containsKey(args[i].toString())){
                            result = gson.toJson(json.get(args[i].toString()));
                            if(result!=null&&result.startsWith("\"")){
                                result =  result.replace("\"", "");
                            }
                        }else{
                            result = null;
                        }
                    }
                    if(result != null){
                        resulttmp = result;
                    }else{
                        resulttmp = null;
                    }
                    resultlist[num++] = resulttmp;
                }
            }
            if("1".equals(flag)){
                for(int i=0;i<innerlist.size();i++){
                    String[] tmp = {resultlist[0],resultlist[1],resultlist[2],resultlist[3],resultlist[4],resultlist[5],resultlist[6],innerlist.get(i).get(0)};
                    forward(tmp);
                }
            }else if("2".equals(flag)){
                switch(tagtmp-1){
                    case 1:
                        for(int i=0;i<innerlist.size();i++){
                            String[] tmp = {resultlist[0],resultlist[1],resultlist[2],resultlist[3],resultlist[4],resultlist[5],innerlist.get(i).get(0),innerlist.get(i).get(1)};
                            forward(tmp);
                        }
                        break;
                    case 2:
                        for(int i=0;i<innerlist.size();i++){
                            String[] tmp = {resultlist[0],resultlist[1],resultlist[2],resultlist[3],resultlist[4],innerlist.get(i).get(0),innerlist.get(i).get(1),innerlist.get(i).get(2)};
                            forward(tmp);
                        }
                        break;
                    case 3:
                        for(int i=0;i<innerlist.size();i++){
                            String[] tmp = {resultlist[0],resultlist[1],resultlist[2],resultlist[3],innerlist.get(i).get(0),innerlist.get(i).get(1),innerlist.get(i).get(2),innerlist.get(i).get(3)};
                            forward(tmp);
                        }
                        break;
                    case 4:
                        for(int i=0;i<innerlist.size();i++){
                            String[] tmp = {resultlist[0],resultlist[1],resultlist[2],innerlist.get(i).get(0),innerlist.get(i).get(1),innerlist.get(i).get(2),innerlist.get(i).get(3),innerlist.get(i).get(4)};
                            forward(tmp);
                        }
                        break;
                    case 5:
                        for(int i=0;i<innerlist.size();i++){
                            String[] tmp = {resultlist[0],resultlist[1],innerlist.get(i).get(0),innerlist.get(i).get(1),innerlist.get(i).get(2),innerlist.get(i).get(3),innerlist.get(i).get(4),innerlist.get(i).get(5)};
                            forward(tmp);
                        }
                        break;
                    case 6:
                        for(int i=0;i<innerlist.size();i++){
                            String[] tmp = {resultlist[0],innerlist.get(i).get(0),innerlist.get(i).get(1),innerlist.get(i).get(2),innerlist.get(i).get(3),innerlist.get(i).get(4),innerlist.get(i).get(5),innerlist.get(i).get(6)};
                            forward(tmp);
                        }
                        break;
                }
            }else{
                String[] tmp = {resultlist[0],resultlist[1],resultlist[2],resultlist[3],resultlist[4],resultlist[5],resultlist[6],resultlist[7]};
                forward(tmp);
            }
        }catch(Exception e){
            //String[] tmp1 = {"3","3","3","4","5","6","7","8"};
            //forward(tmp1);
        }
    }

    public static class MapTypeAdapter extends TypeAdapter<Object> {

        @Override
        public Object read(JsonReader in) throws IOException {
            JsonToken token = in.peek();
            switch (token) {
                case BEGIN_ARRAY:
                    List<Object> list = new ArrayList<Object>();
                    in.beginArray();
                    while (in.hasNext()) {
                        list.add(read(in));
                    }
                    in.endArray();
                    return list;

                case BEGIN_OBJECT:
                    Map<String, Object> map = new LinkedTreeMap<String, Object>();
                    in.beginObject();
                    while (in.hasNext()) {
                        map.put(in.nextName(), read(in));
                    }
                    in.endObject();
                    return map;

                case STRING:
                    return in.nextString();

                case NUMBER:

                    double dbNum = in.nextDouble();

                    if (dbNum > Long.MAX_VALUE) {
                        return dbNum;
                    }

                    long lngNum = (long) dbNum;
                    if (dbNum == lngNum) {
                        return lngNum;
                    } else {
                        return dbNum;
                    }

                case BOOLEAN:
                    return in.nextBoolean();

                case NULL:
                    in.nextNull();
                    return null;

                default:
                    throw new IllegalStateException();
            }
        }
        @Override
        public void write(JsonWriter out, Object value) throws IOException {
        }
    }

}
