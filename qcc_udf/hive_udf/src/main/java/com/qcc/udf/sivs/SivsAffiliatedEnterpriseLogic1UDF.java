package com.qcc.udf.sivs;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.sivs.entity.CompanyDetailEntity;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 投资机构清洗UDF：下属企业表逻辑1的新增关联公司的判断逻辑
 * ---------------------------------------------------------------------------------------------------------
 * create temporary function sivsAffiliatedLogic1 as 'com.qcc.udf.sivs.SivsAffiliatedEnterpriseLogic1UDF' using jar 'hdfs:///data/hive/udf/qcc_udf.jar';
 */
public class SivsAffiliatedEnterpriseLogic1UDF extends UDF {
    private static List<String> InValidCompanyStatusList = Arrays.asList(
            "注销","吊销","筹建","清算","停业","撤销",   // 大陆企业的无效公司状态
            "已告解散", "休止活动", "已终止营业地点", "不再是独立的实体" // 香港企业的无效公司状态
    );


    /**
     * 判断该条链路是否满足根据实际控制人穿透自增逻辑要求
     * 不满足的情况汇总：
     * 1 有基金标签
     * 2 存在与基准公司keyNo不一致的机构标签（keyNo关联的时IDG资本，当链路中的某个公司关联的为非IDG资本的机构时即为不满足的条件）
     * 3 有产品标签
     * 4 shortstatus给出的是无效的公司状态（'注销','吊销','筹建','清算','停业','撤销'）
     * 5 是过期公司（isexpire='true'）
     *
     * @param companyDetailJsonArray 该链路所涉及到的所有公司的基本信息（包括状态，标签，公司名，注册时间等）
     * @param institutionCode 疑似关联的投资机构code
     * @return 1 -> 关联 0-> 不关联
     *
     * link
     * 36f9ba93c3489862950878ef8fcf4125,6a5d88b39f7d74ecad0fe72561fd2743,cdf6ba936b965f2db6413c375e837ce1,000438bf090e941d959594a5162b3337
     * company_controller_link_join_affilaited_company_logic1_prepare.institution_keyno
     * fc9e62695def29ccdb9eb3fed5b4c8c8
     * company_controller_link_join_affilaited_company_logic1_prepare.institution_name
     * 北京国有资本
     *
     */
    public String evaluate(String companyDetailJsonArray, String institutionCode) {
        String result = "0";
        try {
            if (StringUtils.isNotBlank(institutionCode)) {
                // 获取所有的公司详情信息映射集合
                Map<String, CompanyDetailEntity> companyDetailMap = getCompanyDetailMap(companyDetailJsonArray);
                if (CollectionUtils.isNotEmpty(companyDetailMap.keySet())) {
                    Boolean isPassFlag = Boolean.TRUE;
                    for (String keyNo : companyDetailMap.keySet()) {
                        CompanyDetailEntity companyDetailEntity = companyDetailMap.get(keyNo);

                        Boolean conditionByPeFundTag = (StringUtils.isBlank(companyDetailEntity.getTags())
                                || companyDetailEntity.getTags().equals("[]")
                                || !companyDetailEntity.getTags().equals("私募基金")) ? Boolean.TRUE : Boolean.FALSE;
                        Boolean conditionByInstitutionTag = (StringUtils.isBlank(companyDetailEntity.getInvestList())
                                || companyDetailEntity.getInvestList().equals("[]")
                                || companyDetailEntity.getInvestList().contains(institutionCode)) ? Boolean.TRUE : Boolean.FALSE;
                        Boolean conditionByProductTag = StringUtils.isBlank(companyDetailEntity.getProduct()) ? Boolean.TRUE : Boolean.FALSE;
                        Boolean conditionByShortStatus = !InValidCompanyStatusList.contains(companyDetailEntity.getShortStatus()) ? Boolean.TRUE : Boolean.FALSE;
                        Boolean conditionByExpire = companyDetailEntity.getIsExpire().equals("false") ? Boolean.TRUE : Boolean.FALSE;
                        if (conditionByPeFundTag == false || conditionByInstitutionTag == false || conditionByProductTag == false
                                || conditionByShortStatus == false || conditionByExpire == false) {
                            isPassFlag = Boolean.FALSE;
                            break;
                        }
                    }

                    if (isPassFlag == Boolean.TRUE) {
                        result = "1";
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return result;
    }

    private static Map<String, CompanyDetailEntity> getCompanyDetailMap(String infoArray) {
        Map<String, CompanyDetailEntity> companyDetailMap = new HashMap<>();
        try {
            if (StringUtils.isNotBlank(infoArray)) {
                JSONArray jsonArray = JSONArray.parseArray(infoArray);
                if (CollectionUtils.isNotEmpty(jsonArray)) {
                    for (int i=0; i<jsonArray.size(); i++) {
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        String name = jsonObject.getString("name");
                        String keyNo = jsonObject.getString("keyno");
                        String isExpire = jsonObject.getString("isexpired");
                        String investList = jsonObject.getString("investlist");
                        String product = jsonObject.getString("product");
                        String shortStatus = jsonObject.getString("shortstatus");
                        String tags = jsonObject.getString("tags");

                        Long startDate = 0L;
                        try {
                            if (keyNo.startsWith("h")) {
                                Matcher hkStartDateMatcher = Pattern.compile("(\\d{4})年(\\d{1,2})月(\\d{1,2})日").matcher(jsonObject.getString("startdate"));
                                if (hkStartDateMatcher.find()) {
                                    String yearInfo = hkStartDateMatcher.group(1);
                                    String monthInfo = hkStartDateMatcher.group(2);
                                    String monthInfoFinal = monthInfo.length() == 1 ? "0"+monthInfo : monthInfo;
                                    String dayInfo = hkStartDateMatcher.group(3);

                                    DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                                    try {
                                        LocalDateTime localDateTime =
                                                LocalDateTime.parse((StringUtils.join(Arrays.asList(yearInfo,monthInfoFinal,dayInfo), "-") + " 00:00:00"), df);
                                        if (localDateTime.getYear() >= 1990 && localDateTime.getYear() <= LocalDateTime.now().getYear()) {
                                            startDate = localDateTime.toEpochSecond(ZoneOffset.of("+8"));
                                        }
                                    } catch (Exception ignored) {
                                    }
                                } else if (keyNo.startsWith("t")) {

                                } else {
                                    Matcher startDateMatcher = Pattern.compile("\\d{10}").matcher(jsonObject.getString("startdate"));
                                    if (startDateMatcher.find()) {
                                        startDate = Long.parseLong(startDateMatcher.group(0));
                                    }
                                }
                            }
                        } catch (Exception ex) {
                        }

                        CompanyDetailEntity companyDetailEntity = CompanyDetailEntity.builder()
                                .name(name).keyNo(keyNo).isExpire(isExpire).investList(investList).product(product).shortStatus(shortStatus)
                                .tags(tags).startDate(startDate).build();
                        companyDetailMap.put(keyNo, companyDetailEntity);
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return companyDetailMap;
    }

    public static void main(String[] args) {
        String infoArray = "[{\"name\":\"国联中蓉（成都）股权投资基金合伙企业（有限合伙）\",\"startdate\":\"{\\\"$numberLong\\\":\\\"1617033600\\\"}\",\"product\":\"\",\"shortstatus\":\"存续\",\"investlist\":\"[{\\\"InvestId\\\":\\\"4c26774d852f62440fc746ea4cdd57f6\\\",\\\"Count\\\":0,\\\"InvestName\\\":\\\"国联产业投资\\\"}]\",\"tags\":\"[{\\\"DataExtend\\\":\\\"{\\\\\\\"Id\\\\\\\":\\\\\\\"71526783300c1b58e32a6d84ee11c227\\\\\\\",\\\\\\\"ProductId\\\\\\\":\\\\\\\"681c298507f5050880104145c5298f89\\\\\\\",\\\\\\\"Id2\\\\\\\":\\\\\\\"4c26774d852f62440fc746ea4cdd57f6\\\\\\\",\\\\\\\"ProductId2\\\\\\\":\\\\\\\"7167add8b2e677e4528e309cefd16174\\\\\\\"}\\\",\\\"Type\\\":209,\\\"TradingPlaceCode\\\":null,\\\"ShortName\\\":null,\\\"TradingPlaceName\\\":null,\\\"Name\\\":\\\"私募基金\\\"},{\\\"DataExtend\\\":\\\"4c26774d852f62440fc746ea4cdd57f6\\\",\\\"Type\\\":208,\\\"TradingPlaceCode\\\":null,\\\"ShortName\\\":null,\\\"TradingPlaceName\\\":null,\\\"Name\\\":\\\"国联产业投资\\\"}]\",\"isexpired\":\"false\",\"keyno\":\"d409dbf0cc3d637d0223f242d6eda37f\"},{\"name\":\"四川华盛能源发展集团有限公司\",\"startdate\":\"{\\\"$numberLong\\\":\\\"749664000\\\"}\",\"product\":\"{\\\"FinancingDate\\\":{\\\"$numberLong\\\":\\\"1618156800\\\"},\\\"CompatCount\\\":20,\\\"RoundDesc\\\":\\\"并购\\\",\\\"Amount\\\":\\\"未披露\\\",\\\"Round\\\":\\\"并购\\\",\\\"_id\\\":\\\"69d422162b5e4c3bbbd4b7ed4eb26ec7\\\",\\\"FinancingCount\\\":1,\\\"Name\\\":\\\"四川华盛石油\\\",\\\"Logo\\\":\\\"\\\"}\",\"shortstatus\":\"存续\",\"investlist\":\"\",\"tags\":\"[{\\\"DataExtend\\\":\\\"\\\",\\\"Type\\\":505,\\\"TradingPlaceCode\\\":\\\"\\\",\\\"ShortName\\\":\\\"\\\",\\\"TradingPlaceName\\\":\\\"\\\",\\\"Name\\\":\\\"小微企业\\\"},{\\\"DataExtend\\\":\\\"69d422162b5e4c3bbbd4b7ed4eb26ec7\\\",\\\"Type\\\":3,\\\"TradingPlaceCode\\\":null,\\\"ShortName\\\":\\\"四川华盛石油\\\",\\\"TradingPlaceName\\\":null,\\\"Name\\\":\\\"并购\\\"},{\\\"DataExtend\\\":\\\"69d422162b5e4c3bbbd4b7ed4eb26ec7\\\",\\\"Type\\\":4,\\\"TradingPlaceCode\\\":\\\"0d6ed55c9e4e08cbc650ffb07fd15980\\\",\\\"ShortName\\\":null,\\\"TradingPlaceName\\\":null,\\\"Name\\\":\\\"传统行业\\\"},{\\\"DataExtend\\\":\\\"69d422162b5e4c3bbbd4b7ed4eb26ec7\\\",\\\"Type\\\":4,\\\"TradingPlaceCode\\\":\\\"9030fa0f911595eb1dfb3d92f5e01665\\\",\\\"ShortName\\\":null,\\\"TradingPlaceName\\\":null,\\\"Name\\\":\\\"能源服务\\\"},{\\\"DataExtend\\\":\\\"69d422162b5e4c3bbbd4b7ed4eb26ec7\\\",\\\"Type\\\":4,\\\"TradingPlaceCode\\\":\\\"c4cf232b5045c2641aa036d542f70ed9\\\",\\\"ShortName\\\":null,\\\"TradingPlaceName\\\":null,\\\"Name\\\":\\\"燃气\\\"},{\\\"DataExtend\\\":null,\\\"Type\\\":622,\\\"TradingPlaceCode\\\":null,\\\"ShortName\\\":null,\\\"TradingPlaceName\\\":null,\\\"Name\\\":\\\"国有企业\\\"}]\",\"isexpired\":\"false\",\"keyno\":\"6f0da0742dd15f4f0522f3374c9c3db7\"},{\"name\":\"平果市新能燃气有限责任公司\",\"startdate\":\"{\\\"$numberLong\\\":\\\"1242230400\\\"}\",\"product\":\"\",\"shortstatus\":\"存续\",\"investlist\":\"\",\"tags\":\"[{\\\"DataExtend\\\":\\\"\\\",\\\"Type\\\":505,\\\"TradingPlaceCode\\\":\\\"\\\",\\\"ShortName\\\":\\\"\\\",\\\"TradingPlaceName\\\":\\\"\\\",\\\"Name\\\":\\\"小微企业\\\"},{\\\"DataExtend\\\":null,\\\"Type\\\":622,\\\"TradingPlaceCode\\\":null,\\\"ShortName\\\":null,\\\"TradingPlaceName\\\":null,\\\"Name\\\":\\\"国有企业\\\"}]\",\"isexpired\":\"false\",\"keyno\":\"0036a3b23d21dc52c9f3d7a19da68b7e\"},{\"name\":\"广西华油新能能源股份有限公司\",\"startdate\":\"{\\\"$numberLong\\\":\\\"1289145600\\\"}\",\"product\":\"\",\"shortstatus\":\"存续\",\"investlist\":\"\",\"tags\":\"[{\\\"DataExtend\\\":\\\"\\\",\\\"Type\\\":505,\\\"TradingPlaceCode\\\":\\\"\\\",\\\"ShortName\\\":\\\"\\\",\\\"TradingPlaceName\\\":\\\"\\\",\\\"Name\\\":\\\"小微企业\\\"},{\\\"DataExtend\\\":null,\\\"Type\\\":622,\\\"TradingPlaceCode\\\":null,\\\"ShortName\\\":null,\\\"TradingPlaceName\\\":null,\\\"Name\\\":\\\"国有企业\\\"}]\",\"isexpired\":\"false\",\"keyno\":\"5e94474fdd720104b1a6fd8e1ad8a9cb\"},{\"name\":\"四川华油集团有限责任公司\",\"startdate\":\"{\\\"$numberLong\\\":\\\"962380800\\\"}\",\"product\":\"\",\"shortstatus\":\"存续\",\"investlist\":\"\",\"tags\":\"[{\\\"DataExtend\\\":null,\\\"Type\\\":622,\\\"TradingPlaceCode\\\":null,\\\"ShortName\\\":null,\\\"TradingPlaceName\\\":null,\\\"Name\\\":\\\"国有企业\\\"}]\",\"isexpired\":\"false\",\"keyno\":\"a613e17f624ec224fbf3e27904712b9d\"}] ";
        String instituionCode = "4c26774d852f62440fc746ea4cdd57f6";
        String result = new SivsAffiliatedEnterpriseLogic1UDF().evaluate(infoArray, instituionCode);
        System.out.println(result);
    }
}
