package com.qcc.udf;

import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import com.qcc.udf.enums.PunishGovEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;
import parquet.Strings;

import java.util.Arrays;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Auther: zhanqgiang
 * @Date: 2020/11/19 10:00
 * @Description: 案号简单清洗
 */
public class PunishCleanUDF extends UDF {

    public static String evaluate(String offices) {
        if (Strings.isNullOrEmpty(offices)) {
            return "";
        }
        String punishCode = "";
        if (StringUtils.isNotBlank(offices)){
            Set<String> codeSet = Arrays.asList(offices.split(",")).stream().filter(str -> StringUtils.isNotBlank(str)).map(str -> {
                PunishGovEnum punishGovEnum = PunishGovEnum.find(str);
                return punishGovEnum.getGovCodeEnum().getCode();
            }).filter(str -> StringUtils.isNotBlank(str)).collect(Collectors.toSet());

            if (CollectionUtils.isNotEmpty(codeSet)){
                punishCode = StringUtils.join(codeSet,",");
            }
        }

        return punishCode;
    }



    public static void main(String[] args) {
        System.out.println(evaluate("陇西高速交警"));
    }

}
