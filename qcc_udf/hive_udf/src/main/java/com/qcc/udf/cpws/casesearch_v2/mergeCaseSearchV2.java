package com.qcc.udf.cpws.casesearch_v2;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: liulh
 * @Date: 2020/6/11 17:54
 * @Description:
 */
public class mergeCaseSearchV2 extends UDF {
    public static String evaluate(List<String> infoList) {
        JSONObject jsonObject = new JSONObject();
        Collections.sort(infoList);
        // 数据合并
        Map<String, JSONObject> infoMap = new HashMap<>(16);
        for (String str : infoList){
            JSONObject json = JSONObject.parseObject(str);

            int allcnt = getAllcnt(json);

            jsonObject.put("KtggCnt", jsonObject.getIntValue("KtggCnt") + json.getIntValue("KtggCnt"));
            jsonObject.put("ZxCnt", jsonObject.getIntValue("ZxCnt") + json.getIntValue("ZxCnt"));
            jsonObject.put("XjpgCnt", jsonObject.getIntValue("XjpgCnt") + json.getIntValue("XjpgCnt"));
            jsonObject.put("CfgsCnt", jsonObject.getIntValue("CfgsCnt") + json.getIntValue("CfgsCnt"));
            jsonObject.put("AnnoCnt", jsonObject.getIntValue("AnnoCnt") + json.getIntValue("AnnoCnt"));
            jsonObject.put("GqdjCnt", jsonObject.getIntValue("GqdjCnt") + json.getIntValue("GqdjCnt"));
            jsonObject.put("XgCnt", jsonObject.getIntValue("XgCnt") + json.getIntValue("XgCnt"));
            jsonObject.put("FyggCnt", jsonObject.getIntValue("FyggCnt") + json.getIntValue("FyggCnt"));
            jsonObject.put("ZbCnt", jsonObject.getIntValue("ZbCnt") + json.getIntValue("ZbCnt"));
            jsonObject.put("CfdfCnt", jsonObject.getIntValue("CfdfCnt") + json.getIntValue("CfdfCnt"));
            jsonObject.put("CfxyCnt", jsonObject.getIntValue("CfxyCnt") + json.getIntValue("CfxyCnt"));
            jsonObject.put("SxCnt", jsonObject.getIntValue("SxCnt") + json.getIntValue("SxCnt"));
            jsonObject.put("LianCnt", jsonObject.getIntValue("LianCnt") + json.getIntValue("LianCnt"));
            jsonObject.put("CaseCnt", jsonObject.getIntValue("CaseCnt") + json.getIntValue("CaseCnt"));
            jsonObject.put("PcczCnt", jsonObject.getIntValue("PcczCnt") + json.getIntValue("PcczCnt"));
            jsonObject.put("HbcfCnt", jsonObject.getIntValue("HbcfCnt") + json.getIntValue("HbcfCnt"));
            jsonObject.put("SdggCnt", jsonObject.getIntValue("SdggCnt") + json.getIntValue("SdggCnt"));
            // CompanyKeywords
            jsonObject.put("CompanyKeywords", mergeItem(jsonObject.getString("CompanyKeywords"), json.getString("CompanyKeywords")));
            // AnNoList
            jsonObject.put("AnNoList", mergeItem(jsonObject.getString("AnNoList"), json.getString("AnNoList")));
            // GroupCourt
            jsonObject.put("GroupCourt", mergeItem(jsonObject.getString("GroupCourt"), json.getString("GroupCourt")));
            // ProcuratorateList
            jsonObject.put("ProcuratorateList", mergeItem(jsonObject.getString("ProcuratorateList"), json.getString("ProcuratorateList")));
            // CourtList
            jsonObject.put("CourtList", mergeItem(jsonObject.getString("CourtList"), json.getString("CourtList")));
            // CaseType
            jsonObject.put("CaseType", mergeItem(jsonObject.getString("CaseType"), json.getString("CaseType")));
            // Province
            jsonObject.put("Province", mergeItem(jsonObject.getString("Province"), json.getString("Province")));
            if (allcnt>0 &&(jsonObject.getInteger("EarliestDate") == null ||jsonObject.getInteger("EarliestDate") > json.getInteger("EarliestDate"))){
                // CaseName
                jsonObject.put("CaseName", json.getString("CaseName"));
                // CaseRole
                jsonObject.put("CaseRole", json.getString("CaseRole"));
                // CaseReason
                jsonObject.put("CaseReason", json.getString("CaseReason"));


                jsonObject.put("EarliestDate", json.getInteger("EarliestDate"));
            }

            if (json.getString("CaseType").equals("民事案件")){
                getAllListInfo(json.getJSONArray("InfoList"), 1, infoMap);
            }else{
                getAllListInfo(json.getJSONArray("InfoList"), 2, infoMap);
            }
        }

        List<JSONObject> myInfoList = infoMap.values().stream().collect(Collectors.toList());
        sortList(jsonObject, myInfoList);
        compareAllRound(jsonObject);

        return jsonObject.toString();
    }

    private static int getAllcnt(JSONObject json) {
        return json.getIntValue("KtggCnt")+json.getIntValue("ZxCnt")+json.getIntValue("XjpgCnt")+json.getIntValue("CfgsCnt")+
        json.getIntValue("GqdjCnt")+json.getIntValue("XgCnt")+json.getIntValue("FyggCnt")+json.getIntValue("ZbCnt")
        +json.getIntValue("CfdfCnt")+json.getIntValue("CfxyCnt")+json.getIntValue("SxCnt")+json.getIntValue("LianCnt")
        +json.getIntValue("CaseCnt")+json.getIntValue("PcczCnt")+json.getIntValue("HbcfCnt")+json.getIntValue("SdggCnt");
    }

    public static String mergeItem(String param1, String param2){
        String result = "";
        param1 = param1 == null ? "" : param1;
        param2 = param2 == null ? "" : param2;
        String[] companykeywordsZx = param1.split(",");
        String[] companykeywordsMs = param2.split(",");
        Set<String> keywordSet = new LinkedHashSet<>();
        for (String str : companykeywordsZx){
            keywordSet.add(str.replace(" ", "").replace("　", ""));
        }
        for (String str : companykeywordsMs){
            keywordSet.add(str.replace(" ", "").replace("　", ""));
        }
        for (String str : keywordSet){
            if (StringUtils.isNotEmpty(str)){
                result = result.concat(",").concat(str);
            }
        }
        result = result.length() > 0 ? result.substring(1) : result;

        return result;
    }

    public static void compareAllRound(JSONObject jsonObject){
        JSONArray infoList = jsonObject.getJSONArray("InfoList");
        String latestTrialRound = "";
        String lastestDateType = "";
        String earliestDateType = "";
        long lastestDate = 0;
        long earliestDate = 0;

        latestTrialRound = CommonV2Util.getLatestTrialRoundFromInfoList(infoList);

        Map<Long, String> trialRoundDateNodeMap = new HashMap<>();
        Iterator<Object> it = infoList.iterator();
        while (it.hasNext()){
            JSONObject json = (JSONObject) it.next();
            String anNo = json.getString("AnNo");
            trialRoundDateNodeMap.put(json.getLong("LatestTimestamp"),json.getString("TrialRound"));
            getInfoDate(json.getJSONArray("SxList"), 1, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("ZxList"), 2, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("XgList"), 3, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("CaseList"), 4, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("PcczList"), 5, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("ZbList"), 6, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("XjpgList"), 7, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("GqdjList"), 8, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("SdggList"), 9, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("FyggList"), 10, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("KtggList"), 11, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("LianList"), 12, anNo, trialRoundDateNodeMap);
        }

        Map<Long, String> sortedDateNodeMap = new LinkedHashMap<>();
        trialRoundDateNodeMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEachOrdered(e -> sortedDateNodeMap.put(e.getKey(), e.getValue()));

        boolean flag = true;
        for (Map.Entry<Long, String> sortedDateNodeEntry : sortedDateNodeMap.entrySet()) {
            if (flag) {
                earliestDate = sortedDateNodeEntry.getKey();
                earliestDateType = sortedDateNodeEntry.getValue();
                flag = false;
            }
            lastestDate = sortedDateNodeEntry.getKey();
            lastestDateType = sortedDateNodeEntry.getValue();
        }

        jsonObject.put("EarliestDate", earliestDate);
        jsonObject.put("EarliestDateType", CommonV2Util.getDataTypeWithoutTrialRound(earliestDateType));
        if (sortedDateNodeMap.size() > 1) {
            jsonObject.put("LastestDateType", CommonV2Util.getDataTypeWithoutTrialRound(lastestDateType));
            jsonObject.put("LastestDate", lastestDate);
        }
        jsonObject.put("LatestTrialRound", latestTrialRound);
    }

    public static void getInfoDate(JSONArray array, int type, String anNo, Map<Long, String> trialRoundDateNodeMap){
        if (array != null && !array.isEmpty()){
            Iterator<Object> itWd = array.iterator();
            while (itWd.hasNext()){
                JSONObject itemJson = (JSONObject)itWd.next();
                GetDetailInfoZXV2UDF.editItemJsonConn(itemJson, type, trialRoundDateNodeMap, anNo);
            }
        }
    }

    public static void sortList(JSONObject jsonObject, List<JSONObject> infoList){
        // 排序
        Collections.sort(infoList, (a1, a2)->{
            int sort1 = a1.getLong("sort1").compareTo(a2.getLong("sort1"));
            if (sort1 == 0){
                return a1.getInteger("sort2").compareTo(a2.getInteger("sort2"));
            }
            return sort1;
        });

        JSONArray newArray = new JSONArray();
        for (JSONObject jsonObject1 : infoList){
            jsonObject1.remove("sort1");
            jsonObject1.remove("sort2");
            newArray.add(jsonObject1);
        }

        jsonObject.put("InfoList", newArray);
    }

    public static void getAllListInfo(JSONArray array, int type, Map<String, JSONObject> infoMap){
        Iterator<Object> it = array.iterator();
        while (it.hasNext()){
            Map<Long, String> trialRoundDateNodeMap = new HashMap<>();
            JSONObject json = (JSONObject) it.next();
            String anNo = json.getString("AnNo");
            getInfoDate(json.getJSONArray("SxList"), 1, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("ZxList"), 2, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("XgList"), 3, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("CaseList"), 4, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("PcczList"), 5, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("ZbList"), 6, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("XjpgList"), 7, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("GqdjList"), 8, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("SdggList"), 9, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("FyggList"), 10, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("KtggList"), 11, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("LianList"), 12, anNo, trialRoundDateNodeMap);

            Set<Long> timeSet = trialRoundDateNodeMap.keySet();
            long firstTime = 0;
            int idx = 0;
            for (Long item : timeSet){
                if (idx == 0){
                    firstTime = item.longValue();
                }else{
                    if (item.longValue() < firstTime){
                        firstTime = item.longValue();
                    }
                }
                idx++;
            }

            if (firstTime == 0) {
                String rule2 = "\\d{4}";
                String year = ReUtil.get(rule2, anNo, 0);
                if (StringUtils.isNotBlank(year) && StringUtils.isNumeric(year)) {
                    int yearNum = Integer.parseInt(year);
                    if (yearNum > 1980 &&yearNum <=DateUtil.thisYear()+5) {
                        DateTime dateTime = DateUtil.parse(year, "yyyy");
                        firstTime = dateTime.getTime() / 1000;
                    }
                }
            }

            json.put("sort1", firstTime);
            json.put("sort2", type);
            //中英文括号，统一为中文括号
            String anNoKey = anNo.replace("(", "（").replace(")", "）");
            infoMap.put(anNoKey,json);
        }
    }

    public static void main(String[] args) {
        List<String> infoList = new LinkedList<>();
//        String str1 = "{\"ZxCnt\":0,\"XjpgCnt\":0,\"KtggCnt\":0,\"LastestDateType\":\"\",\"CfgsCnt\":0,\"LastestDate\":-1,\"EarliestDate\":1579017600,\"AnnoCnt\":2,\"EarliestDateType\":\"首次执行|裁定日期\",\"CompanyKeywords\":\"\",\"AnNoList\":\"（2020）豫0425执115号之七,（2020）豫0425执115号之二\",\"GqdjCnt\":0,\"GroupCourt\":\"郏县人民法院\",\"XgCnt\":0,\"FyggCnt\":0,\"ZbCnt\":0,\"LatestTrialRound\":\"首次执行\",\"CfdfCnt\":0,\"CfxyCnt\":0,\"CaseName\":\"超其它类型纠纷\",\"SxCnt\":0,\"Province\":\"HEN\",\"LianCnt\":0,\"CaseCnt\":2,\"PcczCnt\":0,\"HbcfCnt\":0,\"CaseType\":\"执行案件\",\"ProcuratorateList\":\"\",\"CaseRole\":\"[{\\\"P\\\":\\\"超\\\",\\\"R\\\":\\\"被执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2}]\",\"CaseReason\":\"其它类型纠纷\",\"CourtList\":\"郏县人民法院\",\"InfoList\":[{\"Defendant\":[{\"Role\":\"被执行人\",\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"超\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"郏县人民法院\",\"ZxList\":[],\"LatestTimestamp\":1579017600,\"HbcfList\":[],\"CfdfList\":[],\"CaseList\":[{\"JudgeDate\":1579017600,\"Id\":\"f12dae5fee09c936bdd03455cb45849c0\",\"DocType\":\"执行裁定日期\",\"IsValid\":1}],\"TrialRound\":\"首次执行\",\"Prosecutor\":[],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2020）豫0425执115号之七\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"其它类型纠纷\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]},{\"Defendant\":[{\"Role\":\"被执行人\",\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"超\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"郏县人民法院\",\"ZxList\":[],\"LatestTimestamp\":1579017600,\"HbcfList\":[],\"CfdfList\":[],\"CaseList\":[{\"JudgeDate\":1579017600,\"Id\":\"0614bccf8c5d8d6ddd04d23be0c321370\",\"DocType\":\"执行裁定日期\",\"IsValid\":1}],\"TrialRound\":\"首次执行\",\"Prosecutor\":[],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2020）豫0425执115号之二\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"其它类型纠纷\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]}],\"SdggCnt\":0}";
//        String str2 ="{\"ZxCnt\":0,\"XjpgCnt\":0,\"KtggCnt\":0,\"LastestDateType\":\"\",\"CfgsCnt\":0,\"LastestDate\":-1,\"EarliestDate\":1579017600,\"AnnoCnt\":2,\"EarliestDateType\":\"首次执行|裁定日期\",\"CompanyKeywords\":\"\",\"AnNoList\":\"（2020）豫0425执115号之二,（2020）豫0425执115号之六\",\"GqdjCnt\":0,\"GroupCourt\":\"郏县人民法院\",\"XgCnt\":0,\"FyggCnt\":0,\"ZbCnt\":0,\"LatestTrialRound\":\"首次执行\",\"CfdfCnt\":0,\"CfxyCnt\":0,\"CaseName\":\"超其它类型纠纷\",\"SxCnt\":0,\"Province\":\"HEN\",\"LianCnt\":0,\"CaseCnt\":2,\"PcczCnt\":0,\"HbcfCnt\":0,\"CaseType\":\"执行案件\",\"ProcuratorateList\":\"\",\"CaseRole\":\"[{\\\"P\\\":\\\"超\\\",\\\"R\\\":\\\"被执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2}]\",\"CaseReason\":\"其它类型纠纷\",\"CourtList\":\"郏县人民法院\",\"InfoList\":[{\"Defendant\":[{\"Role\":\"被执行人\",\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"超\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"郏县人民法院\",\"ZxList\":[],\"LatestTimestamp\":1579017600,\"HbcfList\":[],\"CfdfList\":[],\"CaseList\":[{\"JudgeDate\":1579017600,\"Id\":\"0614bccf8c5d8d6ddd04d23be0c321370\",\"DocType\":\"执行裁定日期\",\"IsValid\":1}],\"TrialRound\":\"首次执行\",\"Prosecutor\":[],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2020）豫0425执115号之二\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"其它类型纠纷\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]},{\"Defendant\":[{\"Role\":\"被执行人\",\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"超\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"郏县人民法院\",\"ZxList\":[],\"LatestTimestamp\":1579017600,\"HbcfList\":[],\"CfdfList\":[],\"CaseList\":[{\"JudgeDate\":1579017600,\"Id\":\"e98b466f1a95a77f0ae085dc7f53ca6c0\",\"DocType\":\"执行裁定日期\",\"IsValid\":1}],\"TrialRound\":\"首次执行\",\"Prosecutor\":[],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2020）豫0425执115号之六\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"其它类型纠纷\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]}],\"SdggCnt\":0}";
//        String str3 ="{\"ZxCnt\":0,\"XjpgCnt\":0,\"KtggCnt\":0,\"LastestDateType\":\"\",\"CfgsCnt\":0,\"LastestDate\":-1,\"EarliestDate\":1579017600,\"AnnoCnt\":2,\"EarliestDateType\":\"首次执行|裁定日期\",\"CompanyKeywords\":\"\",\"AnNoList\":\"（2020）豫0425执115号之四,（2020）豫0425执115号之二\",\"GqdjCnt\":0,\"GroupCourt\":\"郏县人民法院\",\"XgCnt\":0,\"FyggCnt\":0,\"ZbCnt\":0,\"LatestTrialRound\":\"首次执行\",\"CfdfCnt\":0,\"CfxyCnt\":0,\"CaseName\":\"超其它类型纠纷\",\"SxCnt\":0,\"Province\":\"HEN\",\"LianCnt\":0,\"CaseCnt\":2,\"PcczCnt\":0,\"HbcfCnt\":0,\"CaseType\":\"执行案件\",\"ProcuratorateList\":\"\",\"CaseRole\":\"[{\\\"P\\\":\\\"超\\\",\\\"R\\\":\\\"被执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2}]\",\"CaseReason\":\"其它类型纠纷\",\"CourtList\":\"郏县人民法院\",\"InfoList\":[{\"Defendant\":[{\"Role\":\"被执行人\",\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"超\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"郏县人民法院\",\"ZxList\":[],\"LatestTimestamp\":1579017600,\"HbcfList\":[],\"CfdfList\":[],\"CaseList\":[{\"JudgeDate\":1579017600,\"Id\":\"f0f7ce4f5e3ec21efadda5225b328ca30\",\"DocType\":\"执行裁定日期\",\"IsValid\":1}],\"TrialRound\":\"首次执行\",\"Prosecutor\":[],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2020）豫0425执115号之四\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"其它类型纠纷\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]},{\"Defendant\":[{\"Role\":\"被执行人\",\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"超\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"郏县人民法院\",\"ZxList\":[],\"LatestTimestamp\":1579017600,\"HbcfList\":[],\"CfdfList\":[],\"CaseList\":[{\"JudgeDate\":1579017600,\"Id\":\"0614bccf8c5d8d6ddd04d23be0c321370\",\"DocType\":\"执行裁定日期\",\"IsValid\":1}],\"TrialRound\":\"首次执行\",\"Prosecutor\":[],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2020）豫0425执115号之二\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"其它类型纠纷\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]}],\"SdggCnt\":0}";
//        String str4 ="{\"ZxCnt\":0,\"XjpgCnt\":0,\"KtggCnt\":0,\"LastestDateType\":\"\",\"CfgsCnt\":0,\"LastestDate\":-1,\"EarliestDate\":1579017600,\"AnnoCnt\":2,\"EarliestDateType\":\"首次执行|裁定日期\",\"CompanyKeywords\":\"\",\"AnNoList\":\"(2020)豫0425执115号之二,（2020）豫0425执115号之五\",\"GqdjCnt\":0,\"GroupCourt\":\"郏县人民法院\",\"XgCnt\":0,\"FyggCnt\":0,\"ZbCnt\":0,\"LatestTrialRound\":\"首次执行\",\"CfdfCnt\":0,\"CfxyCnt\":0,\"CaseName\":\"超其它类型纠纷\",\"SxCnt\":0,\"Province\":\"HEN\",\"LianCnt\":0,\"CaseCnt\":2,\"PcczCnt\":0,\"HbcfCnt\":0,\"CaseType\":\"执行案件\",\"ProcuratorateList\":\"\",\"CaseRole\":\"[{\\\"P\\\":\\\"超\\\",\\\"R\\\":\\\"被执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2}]\",\"CaseReason\":\"其它类型纠纷\",\"CourtList\":\"郏县人民法院\",\"InfoList\":[{\"Defendant\":[{\"Role\":\"被执行人\",\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"超\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"郏县人民法院\",\"ZxList\":[],\"LatestTimestamp\":1579017600,\"HbcfList\":[],\"CfdfList\":[],\"CaseList\":[{\"JudgeDate\":1579017600,\"Id\":\"0614bccf8c5d8d6ddd04d23be0c321370\",\"DocType\":\"执行裁定日期\",\"IsValid\":1}],\"TrialRound\":\"首次执行\",\"Prosecutor\":[],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2020）豫0425执115号之二\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"其它类型纠纷\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]},{\"Defendant\":[{\"Role\":\"被执行人\",\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"超\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"郏县人民法院\",\"ZxList\":[],\"LatestTimestamp\":1579017600,\"HbcfList\":[],\"CfdfList\":[],\"CaseList\":[{\"JudgeDate\":1579017600,\"Id\":\"22d6fc45eac96ef5c24954d46f967f500\",\"DocType\":\"执行裁定日期\",\"IsValid\":1}],\"TrialRound\":\"首次执行\",\"Prosecutor\":[],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2020）豫0425执115号之五\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"其它类型纠纷\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]}],\"SdggCnt\":0}";

        String ms ="{\"AnNoList\":\"（2020）鲁0214民初212号\",\"AnnoCnt\":1,\"CaseCnt\":0,\"CaseType\":\"民事案件\",\"CfdfCnt\":0,\"CfgsCnt\":0,\"CfxyCnt\":0,\"CourtList\":\"青岛市城阳区人民法院\",\"EarliestDate\":-1,\"FyggCnt\":0,\"GqdjCnt\":0,\"HbcfCnt\":0,\"InfoList\":[{\"AnNo\":\"（2020）鲁0214民初212号\",\"CaseList\":[],\"CaseReason\":\"\",\"CaseType\":\"民事案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"青岛市城阳区人民法院\",\"Defendant\":[],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":-1,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"民事一审\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]}],\"KtggCnt\":0,\"LastestDate\":-1,\"LastestDateType\":\"民事一审\",\"LatestTrialRound\":\"民事一审\",\"LianCnt\":0,\"PcczCnt\":0,\"SdggCnt\":0,\"SxCnt\":0,\"XgCnt\":0,\"XjpgCnt\":0,\"XzcfCnt\":0,\"ZbCnt\":0,\"ZxCnt\":0,\"type\":0}";
        String ms1 ="{\"AnNoList\":\"（2019）鲁0214民初7442号\",\"AnnoCnt\":1,\"CaseCnt\":0,\"CaseType\":\"民事案件\",\"CfdfCnt\":0,\"CfgsCnt\":0,\"CfxyCnt\":0,\"CourtList\":\"青岛市城阳区人民法院\",\"EarliestDate\":-1,\"FyggCnt\":0,\"GqdjCnt\":0,\"HbcfCnt\":0,\"InfoList\":[{\"AnNo\":\"（2019）鲁0214民初7442号\",\"CaseList\":[],\"CaseReason\":\"\",\"CaseType\":\"民事案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"青岛市城阳区人民法院\",\"Defendant\":[],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":-1,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"民事一审\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]}],\"KtggCnt\":0,\"LastestDate\":-1,\"LastestDateType\":\"民事一审\",\"LatestTrialRound\":\"民事一审\",\"LianCnt\":0,\"PcczCnt\":0,\"SdggCnt\":0,\"SxCnt\":0,\"XgCnt\":0,\"XjpgCnt\":0,\"XzcfCnt\":0,\"ZbCnt\":0,\"ZxCnt\":0,\"type\":0}";
        String zx ="{\"ZxCnt\":1,\"XjpgCnt\":0,\"KtggCnt\":0,\"LastestDateType\":\"\",\"CfgsCnt\":0,\"LastestDate\":-1,\"EarliestDate\":1600272000,\"AnnoCnt\":1,\"EarliestDateType\":\"首次执行|被执行人立案日期\",\"CompanyKeywords\":\"王大伟\",\"AnNoList\":\"（2020）鲁0214执3306号\",\"GqdjCnt\":0,\"GroupCourt\":\"青岛市城阳区人民法院\",\"XgCnt\":0,\"FyggCnt\":0,\"ZbCnt\":0,\"LatestTrialRound\":\"首次执行\",\"CfdfCnt\":0,\"CfxyCnt\":0,\"CaseName\":\"王大伟被申请执行案件\",\"SxCnt\":0,\"Province\":\"SD\",\"LianCnt\":0,\"CaseCnt\":0,\"PcczCnt\":0,\"HbcfCnt\":0,\"CaseType\":\"执行案件\",\"ProcuratorateList\":\"\",\"CaseRole\":\"[{\\\"P\\\":\\\"王大伟\\\",\\\"R\\\":\\\"被执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-1}]\",\"CaseReason\":\"\",\"CourtList\":\"青岛市城阳区人民法院\",\"InfoList\":[{\"Defendant\":[{\"KeyNo\":\"\",\"Role\":\"被执行人\",\"Org\":-1,\"Name\":\"王大伟\"}],\"CfgsList\":[],\"SdggList\":[],\"ZxList\":[{\"LianDate\":1600272000,\"SqrNameAndKeyNo\":[],\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-1,\"Name\":\"王大伟\"}],\"Id\":\"81b03176cf499f428709485de429b8811\",\"IsValid\":1,\"Biaodi\":\"0\"}],\"Court\":\"青岛市城阳区人民法院\",\"LatestTimestamp\":1600272000,\"HbcfList\":[],\"CfdfList\":[],\"TrialRound\":\"首次执行\",\"CaseList\":[],\"Prosecutor\":[],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2020）鲁0214执3306号\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]}],\"SdggCnt\":0}";
        infoList.add(zx);
        infoList.add(ms);
        infoList.add(ms1);

        //        infoList.add(str1);
//        infoList.add(str2);
//        infoList.add(str3);
//        infoList.add(str4);
        System.out.println(evaluate(infoList));
    }

}
