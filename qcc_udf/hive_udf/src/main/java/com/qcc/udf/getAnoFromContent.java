package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.HashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 从文本中提取案号,多个案号用逗号(,)分隔
 *
 * <AUTHOR>
 * @date ：Created in 2019/12/6 11:32
 * @description：
 */

@Description(name = "getAnoFromContent", value = "String input); - Return String")
public class getAnoFromContent extends UDF {
    public String evaluate(String input) {
        if (input == null || "".equals(input)) {
            return "";
        }

        Pattern extractPattern = Pattern.compile("(（[0-9]{4}）[\\u4e00-\\u9fa5]+.+?号(之[一二三四五六七八九十])*)");
        Matcher matcher = extractPattern.matcher(
                input.replace("(", "（").replace(")", "）"));
        Set<String> linkedCaseNoList = new HashSet<>();
        while (matcher.find()) {
            linkedCaseNoList.add(matcher.group(0));
        }
        return linkedCaseNoList.stream().collect(Collectors.joining(","));
    }

    public static void main(String[] args) {
        getAnoFromContent getAnoFromContent = new getAnoFromContent();
        System.out.println(getAnoFromContent.evaluate("<p><a href=\\\"https://www.qcc.com/firm_f3719ce909c32755b6b4b833f9c0fe04.html\\\" target=\\\"_blank\\\">重庆市永川区四晋泰商贸有限责任公司</a>,<a href=\\\"https://www.qcc.com/firm_6893322f33ba60a82a8a003a26741079.html\\\" target=\\\"_blank\\\">重庆二贰八电子商务有限公司</a>,<a href=\\\"https://www.qcc.com/firm_9d9a87a1a29b8e596f66b12e64eeab03.html\\\" target=\\\"_blank\\\">北京金邦辉月商贸有限公司</a>,<a href=\\\"https://www.qcc.com/firm_55246b5cfae15de8534910b0be1b2438.html\\\" target=\\\"_blank\\\">泽融(福建)投资发展有限公司</a>,<a href=\\\"https://www.qcc.com/firm_e483c5278afca287fa3823fe221a2cb5.html\\\" target=\\\"_blank\\\">大连东方亚都置业有限公司</a>：<a href=\\\"https://www.qcc.com/firm_34bccb84d8b0cf136b2e76dab5c034f5.html\\\" target=\\\"_blank\\\">重庆理文卫生用纸制造有限公司</a>就(2022)京0105民初59779号民事判决书提起上诉，本院已受理。现依法向你公告送达上诉状副本。自公告之日起经过30日，即视为送达。提出答辩状的期限为公告期满后15日内。逾期本院将依法审理。</p>"));
    }
}
