package com.qcc.udf.competition;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/06/07 11:05
 * @description ：根据行业新标签判断竞争关系
 */
public class GetCompetitionFromIndustry extends UDF {

    /**
     *
     * @return 0:不是竞争关系  1：是竞争关系
     *
     * */
    public Double evaluate(String industryItem, String industryOther) {
        if (StringUtils.isEmpty(industryItem) || StringUtils.isEmpty(industryOther)) {
            return 0.0;
        }
        List<String> industryListItem = Lists.newArrayList(industryItem.split(","));
        List<String> industryListOther = Lists.newArrayList(industryOther.split(","));

        int itemCount = industryListItem.size();
        int otherCount = industryListOther.size();

        int lowCount = itemCount > otherCount ? otherCount :itemCount;

        int similarCount = 0;
        for(String industry : industryListItem) {
            if (industryOther.contains(industry)) {
                similarCount +=1;
            }
        }

        double similarPer = (similarCount * 1.0)/lowCount;
        BigDecimal bigDecimal = new BigDecimal(similarPer);
        double result = bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();

        return result;
    }

    public static void main(String[] args) {
        GetCompetitionFromIndustry getCompetitionFromIndustry = new GetCompetitionFromIndustry();
        String one = "ensh.20.2.7,ensh.17.9.2,ensh.20.11.3,ensh.20.2.1,ensh.20.12.1,ensh.20.11.5";
        String two = "ensh.20.2.7,ensh.17.9.0,ensh.20.2.3,ensh.17.9.4,ensh.17.9.5";
        System.out.println(getCompetitionFromIndustry.evaluate(one, two));
    }
}
