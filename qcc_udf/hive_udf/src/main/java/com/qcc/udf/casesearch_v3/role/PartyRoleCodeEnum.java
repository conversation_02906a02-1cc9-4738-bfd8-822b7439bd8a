package com.qcc.udf.casesearch_v3.role;

/**
 * 身份code
 */
public enum PartyRoleCodeEnum {
    YG(11),
    SQZXR(12),
    <PERSON>(13),
    <PERSON><PERSON><PERSON>(14),
    <PERSON><PERSON>(21),
    <PERSON><PERSON><PERSON><PERSON>(22),
    <PERSON><PERSON>(23),
    <PERSON><PERSON><PERSON>(24),
    <PERSON><PERSON>(91),
    QT(99),
    DEFAULT(-1)
    ;


    private int code;

    PartyRoleCodeEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }


    public static int findCode(String role) {
        if (role == null) {
            return PartyRoleCodeEnum.QT.code;
        }
        PartyRoleCodeEnum partyRoleCodeEnum = PartyRoleNameEnum.find(role);
        if (partyRoleCodeEnum == DEFAULT){
            partyRoleCodeEnum = PartyRoleNamelikeEnum.find(role);
        }
        return partyRoleCodeEnum.code;
    }

    public static void main(String[] args) {
        String role ="被申请人（原审原告）";
        System.out.println(findCode(role));
    }

}
