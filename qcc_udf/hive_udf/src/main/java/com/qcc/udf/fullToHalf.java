package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

public class fullToHalf extends UDF {

    public static   String evaluate(String fullStr)  throws Exception{
        /**
         * 全角转半角
         * @param fullStr 全角字符
         * @return 返回半角字符
         */

        if (fullStr == null || fullStr.trim().length() == 0) {
            return "";
        }
        char[] c = fullStr.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] >= 65281 && c[i] <= 65374) {
                c[i] = (char) (c[i] - 65248);
            } else if (c[i] == 12288) {
                c[i] = (char) 32;
            }
        }
        return new String(c);
    }
}
