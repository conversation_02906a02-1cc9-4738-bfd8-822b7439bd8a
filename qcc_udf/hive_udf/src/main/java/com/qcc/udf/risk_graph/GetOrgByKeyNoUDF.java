package com.qcc.udf.risk_graph;

import com.qcc.udf.namekeyno.NameUtil;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * <AUTHOR>
 * @desc
 * @date 2021/3/4
 */
public class GetOrgByKeyNoUDF extends UDF {
    public int evaluate(String keyNo) {
        return NameUtil.getOrgByKeyNo(keyNo);
    }

    public static void main(String[] args) {
        String keyno ="";
        GetOrgByKeyNoUDF removeNumberLongUDF = new GetOrgByKeyNoUDF();
        int output = removeNumberLongUDF.evaluate(keyno);
        System.out.println(output);
    }

}