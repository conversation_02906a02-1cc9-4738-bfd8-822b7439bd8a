package com.qcc.udf.company.controller;

public class CompanyOperList {
    private Integer Org;

    private String KeyNo;

    private String Name;

    private boolean HasImage;

    private Integer CompanyCount;

    public Integer getOrg() {
        return Org;
    }

    public void setOrg(Integer org) {
        Org = org;
    }

    public String getKeyNo() {
        return KeyNo;
    }

    public void setKeyNo(String keyNo) {
        KeyNo = keyNo;
    }

    public String getName() {
        return Name;
    }

    public void setName(String name) {
        Name = name;
    }

    public boolean isHasImage() {
        return HasImage;
    }

    public void setHasImage(boolean hasImage) {
        HasImage = hasImage;
    }

    public Integer getCompanyCount() {
        return CompanyCount;
    }

    public void setCompanyCount(Integer companyCount) {
        CompanyCount = companyCount;
    }
}
