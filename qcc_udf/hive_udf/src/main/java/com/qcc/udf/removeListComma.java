package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

public class removeListComma extends UDF {

    /**
     * 移除逗号
     * @param param 字符串
     * @return 返回处理后的字符串
     */

    public static  String evaluate(String param){
        if (param == null || param.trim().length() == 0) {
            return "";
        }
        String[] names = param.split(",");
        for(String str : names){
            System.out.println(str);
        }
        String result = "";
        for(String str : names){
            if(str != null && !"".equals(str)){
                result = result + str + ",";
            }
        }
        return result.length() > 0 ? result.substring(0,result.length() - 1) : "";

    }

}
