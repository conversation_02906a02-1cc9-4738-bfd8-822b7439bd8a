package com.qcc.udf.cpws.casesearch_v2;

import cn.hutool.core.util.ReUtil;
import com.alibaba.fastjson.JSON;
import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.output.LawSuitV3OutputEntity;
import com.qcc.udf.cpws.casesearch_v2.enums.ProvinceEnEnum;
import com.qcc.udf.cpws.casesearch_v2.util.CaseTrialRoundUtil;
import com.qcc.udf.cpws.casesearch_v2.util.CaseTypeUtil;
import com.qcc.udf.cpws.casesearch_v2.util.CourtNameFromCaseNoUtil;
import com.qcc.udf.cpws.casesearch_v2.util.SbcToDbc;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;

/**
 *  根据案号获取省份编码
 * @Auther: wuql
 * @Date: 2020/11/18 10:00
 * @Description:
 */
public class GetProvinceCodeUdf extends UDF {
    public static String evaluate(String anno)  {
        String provinceCode ="";
        anno = SbcToDbc.convertSBCToDBC(anno);
        String rule ="[\\u4e00-\\u9fa5]\\d{0,4}";
        String caseNoChar = ReUtil.get(rule, anno, 0);
        if (StringUtils.isBlank(caseNoChar)){
            return provinceCode;
        }
        //省份
        ProvinceEnEnum provinceEnEnum = ProvinceEnEnum.find(caseNoChar.substring(0, 1));
        if (provinceEnEnum!=null){
            provinceCode = provinceEnEnum.getCode();
        }

        return provinceCode;
    }

    public static void main(String[] args) {
        String anno ="(2006)10";
        String evaluate = evaluate(anno);
        System.out.println(evaluate);
    }
}
