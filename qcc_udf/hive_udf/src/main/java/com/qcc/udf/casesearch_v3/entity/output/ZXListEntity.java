package com.qcc.udf.casesearch_v3.entity.output;

import lombok.Data;
import com.alibaba.fastjson.annotation.JSONField;

import java.util.LinkedList;
import java.util.List;

@Data
public class ZXListEntity extends BaseCaseOutEntity {
    @JSONField(name = "Id")
    private String id = "";
    @JSONField(name = "NameAndKeyNo")
    private List<NameAndKeyNoEntity> nameAndKeyNo = new LinkedList<>();
    @JSONField(name = "LianDate")
    private Long lianDate = 0L;
    @JSONField(name = "IsValid")
    private Integer isValid = 0;
    @JSONField(name = "Biaodi")
    private String biaodi = "";
    @JSONField(name = "SqrNameAndKeyNo")
    private List<NameAndKeyNoEntity> sqrNameAndKeyNo = new LinkedList<>();
}
