package com.qcc.udf.cpws.wjp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.temp.MD5Util;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * UDF version of CleanCpwsDimensionSqrInfoMap
 */
public class CleanCpwsDimensionSqrInfoUDF extends UDF {

    public static String evaluate(String id, String doctype, String caserolegroupbyrolename,
                                  String caseno, Integer isvalid, Integer delflag, String defendant) {

        try {
            // Skip if conditions not met
            if (StringUtils.isEmpty(doctype) || !doctype.equals("adj")
                    || StringUtils.isEmpty(caserolegroupbyrolename)
                    || StringUtils.isEmpty(caseno) || !caseno.contains("执")) {
                return null;
            }

            List<BaseDimensionSqrInfo> baseDimensionSqrInfos = getSqrFromCpws(
                    id, isvalid, delflag, caseno, defendant, caserolegroupbyrolename);

            if (CollectionUtils.isNotEmpty(baseDimensionSqrInfos)) {
                return JSON.toJSONString(baseDimensionSqrInfos);
            }

        } catch (Exception e) {
            return null;
        }
        return null;
    }

    private static List<BaseDimensionSqrInfo> getSqrFromCpws(String dimensionId, int dataStatus,
                                                             Integer delflag, String caseNo, String defendant, String caserolegroupbyrolename) {

        List<BaseDimensionSqrInfo> baseDimensionSqrInfos = new ArrayList<>();

        if (StringUtils.isNotEmpty(caseNo) && StringUtils.isNotEmpty(caserolegroupbyrolename)) {
            // Get defendant names
            String defendantNames = "";
            if (StringUtils.isNotBlank(defendant) && defendant.length() < 1000) {
                JSONArray defendantArray = JSON.parseArray(defendant);
                defendantNames = defendantArray.stream()
                        .map(obj -> (JSONObject) obj)
                        .map(def -> {
                            String name = def.getString("Name");
                            String keyNo = def.getString("KeyNo");
                            return name + "," + keyNo;
                        }).sorted()
                        .collect(Collectors.joining(","));
            }

            Set<String> caseNoStandards = CaseNoCleanUtil.getCommonHandler(caseNo);
            JSONArray jsonArrays = JSON.parseArray(caserolegroupbyrolename);
//[{"KeyNo":"4841c7c69fee7b06e5546527b45e7097","Name":"深圳市万象美物业管理有限公司海公馆分公司","Org":0,"Role":"原告","RoleTag":0},{"KeyNo":"","Name":"马爽","Org":-1,"Role":"被告","RoleTag":1}]
            for (Object o : jsonArrays) {
                JSONObject o1 = (JSONObject) o;
                if (o1.getString("Role").equals("申请执行人")) {
                    for (String caseNoStandard : caseNoStandards) {
                        if (caseNoStandard.length() >= 60) {
                            continue;
                        }

                        BaseDimensionSqrInfo baseItem = new BaseDimensionSqrInfo();
                        baseItem.setDataStatus(delflag == 1 ? 2 : dataStatus);
                        baseItem.setDimensionId(dimensionId);
                        baseItem.setDimensionType(3);
                        baseItem.setCaseNo(caseNoStandard);
                        baseItem.setCaseNoMd5(MD5Util.encode(caseNoStandard));
                        baseItem.setSqrKeyNo(o1.getOrDefault("KeyNo", "").toString());
                        baseItem.setSqrName(o1.getOrDefault("Name", "").toString());
                        baseItem.setSqrOrg(o1.getInteger("Org"));
                        baseItem.setDefendants(defendantNames);
                        baseItem.setId(getNewId(baseItem));

                        baseDimensionSqrInfos.add(baseItem);


                    }
                }
            }
        }
        return baseDimensionSqrInfos;
    }

    private static String getNewId(BaseDimensionSqrInfo baseItem) {
        String key = baseItem.getDimensionId() + "_" + baseItem.getDimensionType() + "_"
                + baseItem.getCaseNo() + "_"
                + (StringUtils.isEmpty(baseItem.getSqrKeyNo()) ? baseItem.getSqrName() : baseItem.getSqrKeyNo());
        return MD5Util.encode(key);
    }

    public static void main(String[] args) {
        System.out.println(evaluate("2172b3f26d2a8ebe7a99fc87f59d108a", "adj", "[{\"KeyNo\":\"\",\"Name\":\"张正平\",\"Org\":-1,\"Role\":\"申请执行人\",\"RoleTag\":0},{\"KeyNo\":\"044c5857774bd2c97f04c224c9397dee\",\"Name\":\"什邡市响簧煤炭有限责任公司\",\"Org\":0,\"Role\":\"被执行人\",\"RoleTag\":1}]"
                , "(2017)川0682执219号", 1, 0, "[{\"KeyNo\":\"044c5857774bd2c97f04c224c9397dee\",\"Name\":\"什邡市响簧煤炭有限责任公司\",\"Org\":0,\"Role\":\"被执行人\",\"RoleTag\":1}]"));
    }
}