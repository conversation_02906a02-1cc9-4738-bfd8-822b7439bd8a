package com.qcc.udf.risk_analysis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class getSxExecuteStatus extends UDF {

    public String evaluate(List<String> infoList) {
        JSONArray array= new JSONArray();

        Map<String, Integer> map = new LinkedHashMap<>();
        for (String str : infoList){
            if (!map.containsKey(str)){
                map.put(str, 1);
            }else{
                map.put(str, map.get(str) + 1);
            }
        }

        Set<String> idSet = map.keySet();
        for (String str : idSet){
            array.add(str.concat("#FGF#").concat(map.get(str).toString()));
        }

        return array.toString();
    }

    public static void main(String[] args) {
        getSxExecuteStatus aa = new getSxExecuteStatus();
        List<String> infoList = JSON.parseArray("[\"3\",\"1\",\"3\"]", String.class);
        System.out.println(aa.evaluate(infoList));
    }
}
