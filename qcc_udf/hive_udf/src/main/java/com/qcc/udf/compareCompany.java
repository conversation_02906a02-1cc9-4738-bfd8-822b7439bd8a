package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class compareCompany extends UDF {

    public static List<String> evaluate(String outOper,String usOper,String outStatus,String usStatus,
                                       String outRegis,String ourReg,String outAddress,String usAddress) {
        List<String> tempst=new ArrayList<String>();
        try {

            String tOper="";
            String tStatus="";
            String result="false";
            JSONObject jsStr =  new JSONObject(outOper);
            if(jsStr.has("Name"))tOper=jsStr.getString("Name");
            List<String> outoper=getChinese(tOper);
            List<String> ouroper=getChinese(usOper);
            if(!compare(outoper,ouroper))result="true";

            if(outStatus.equals("1"))tStatus="存续";
            else if(outStatus.equals("3"))tStatus="注销";
            else if(outStatus.equals("2"))tStatus="吊销，未注销";
            else if(outStatus.equals("22"))tStatus="注销";
            else if(outStatus.equals("4"))tStatus="注销";
            else if(outStatus.equals("21"))tStatus="吊销，未注销";
            else if(outStatus.equals("9"))tStatus="存续";
            else if(outStatus.equals("5"))tStatus="存续";
            else if(outStatus.equals("8"))tStatus="注销";
            if((!usStatus.contains("销")||!usStatus.contains("停业"))&&tStatus.contains("销"))result="true";
            if(ourReg!=null)ourReg=GetNum(ourReg);
            if(outRegis!=null)outRegis=GetNum(outRegis);
            double our=0;
            double out=0;
            if(ourReg.length()>0)our=Double.parseDouble(ourReg);
            if(outRegis.length()>0)out=Double.parseDouble(outRegis);
            if(our!=out&&our*10000!=out&&out*10000!=our)result="true";
            List<String> outAddL=getChinese(outAddress);
            List<String> ourAddL=getChinese(usAddress);
            if(!compare(outAddL,ourAddL))result="true";
            tempst.add(tOper);
            tempst.add(usOper);
            tempst.add(tStatus);
            tempst.add(usStatus);
            tempst.add(outRegis);
            tempst.add(ourReg);
            tempst.add(outAddress);
            tempst.add(usAddress);
            tempst.add(result);
        }
        catch (Exception e) {
//                System.out.println("error message==="+e.toString()+"==="+eid);
        }

        return tempst;
    }

    public  static List<String> getChinese(String paramValue) {
        List<String> tempst=new ArrayList<String>();
        String regex = "([\u4e00-\u9fa5]+)";
        String str = "";
        Matcher matcher = Pattern.compile(regex).matcher(paramValue);
        while (matcher.find()) {
            tempst.add(matcher.group(0));
        }
        return tempst;
    }

    public static  <T extends Comparable<T>> boolean compare(List<T> a, List<T> b) {
        if(a.size() != b.size())
            return false;
        for(int i=0;i<a.size();i++){
            if(!a.get(i).equals(b.get(i)))
                return false;
        }
        return true;
    }

    public static String GetNum(String temp) {
        String r = "-*\\d+(\\.\\d+)?";
        Pattern p = Pattern.compile(r);
        Matcher m = p.matcher(temp);
        String number = "";
        while (m.find()) {
            number += m.group(0);
        }

        return number;
    }
}
