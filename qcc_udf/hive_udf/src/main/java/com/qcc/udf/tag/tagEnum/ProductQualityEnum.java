package com.qcc.udf.tag.tagEnum;

import com.qcc.udf.tag.TagEntity;
import com.qcc.udf.tag.interfaces.TagEnumInterface;

/**
 * 产品质量问题标签枚举
 */
public enum ProductQualityEnum implements TagEnumInterface {
    ZL01("无证生产","ZL01"),
    ZL02("添加违禁品","ZL02"),
    ZL03("重大质量安全事故","ZL03"),
    ZL04("产品召回","ZL04"),
    ZL05("检验不合格","ZL05"),
    ZL06("假冒伪劣产品","ZL06"),
    ZL07("虚假宣传","ZL07"),
    ZL99("其他质量问题","ZL99"),
    ;


    private String name;
    private String code;

    ProductQualityEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    @Override
    public TagEntity getTagEntity() {
        return new TagEntity(this.code,this.name);
    }
}
