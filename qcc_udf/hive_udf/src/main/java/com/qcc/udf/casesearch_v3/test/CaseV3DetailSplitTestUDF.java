package com.qcc.udf.casesearch_v3.test;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.output.LawSuitV3OutputEntity;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.casesearch_v3.util.CaseSplitUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021年06月17日 13:54
 */
public class CaseV3DetailSplitTestUDF  extends UDF {
    public static String evaluate(String json){
        LawSuitV3OutputEntity entity  = JSON.parseObject(json,LawSuitV3OutputEntity.class);
        List<TmpEntity1> roundList = new ArrayList<>();
        if(entity.getInfoList() != null){
            int idx = 0;
            for (InfoListEntity info : entity.getInfoList()) {
                String trialRound = info.getTrialRound();
                List<NameAndKeyNoEntity> proList = info.getProsecutor();
                List<NameAndKeyNoEntity> defList = info.getDefendant();
                roundList.add(new TmpEntity1(idx,trialRound,proList,defList));
                idx++;
            }
        }
        Map<String,List<TmpEntity1>> roundMap = roundList.stream()
                .collect(Collectors.groupingBy(TmpEntity1::getTrialRound));

        List<TmpEntity1> ysList = roundMap.getOrDefault("民事一审",new ArrayList<>());


        List<TmpEntity1> esList = roundMap.getOrDefault("民事二审",new ArrayList<>());

        Map<String,Boolean> resultList = new HashMap<>();
        resultList.put("ysMatch",matchRule(ysList));
        resultList.put("esMatch",matchRule(esList));

        return JSON.toJSONString(resultList);
    }

    static boolean matchRule( List<TmpEntity1> roundList){
        boolean match = true;
        if(CollectionUtils.isEmpty(roundList) || roundList.size() == 1){
            return false;
        }
        for (TmpEntity1 item : roundList) {
            Set<String> defSet = buildNameKeySet(item.getDefList(),false);
            Set<String> proSet = buildNameKeySet(item.getProList(),false);
            boolean defMatch = true;
            boolean proMatch = false;
            for (TmpEntity1 sub : roundList) {
                if(item.getIdx() == sub.getIdx()){
                    continue;
                }
                Set<String> defSubSet = buildNameKeySet(sub.getDefList(),false);
                Set<String> proSubSet = buildNameKeySet(sub.getProList(),false);
                if(!isJoin(defSet,defSubSet)){
                    defMatch = false;
                    break;
                }
                if(isJoin(proSet,proSubSet)){
                    proMatch = true;
                    break;
                }
            }
            //被告重叠数据
            if(!(defMatch && !proMatch)){
                match = false;
//                System.out.println("");
            }
        }

        return match;
    }


    /**
     * 判断两个集合是否有交集
     *
     * @param aSet
     * @param bSet
     * @return
     */
    static boolean isJoin(Set<String> aSet, Set<String> bSet) {
        long aInbCount = aSet.stream().filter(item -> bSet.contains(item) ).count();
        long bInaCount = bSet.stream().filter(item -> aSet.contains(item)).count();
        return aInbCount > 0 || bInaCount > 0;
    }

    /**
     * 生成当事人数组
     *
     * @param nameList
     * @return
     */
    static Set<String> buildNameKeySet(List<NameAndKeyNoEntity> nameList, boolean containsMou) {
        Set<String> nameKeySet = new HashSet<>();
        if (CollectionUtils.isEmpty(nameList)) {
            return nameKeySet;
        }
        for (NameAndKeyNoEntity keyNoEntity : nameList) {
            if (Strings.isNullOrEmpty(keyNoEntity.getKeyNo())) {
                if(!Strings.isNullOrEmpty(keyNoEntity.getName())){
                    if(containsMou){
                        nameKeySet.add(keyNoEntity.getName().substring(0,1));
                    }else{
                        nameKeySet.add(keyNoEntity.getName());
                    }
                }

            } else {
                nameKeySet.add(keyNoEntity.getKeyNo());
                //人名的情况下 可能存在A案件有keyNo, b案件没有keyNo的情况
                if("p".equals(keyNoEntity.getKeyNo().substring(0,1))){
                    if(containsMou){
                        nameKeySet.add(keyNoEntity.getName().substring(0,1));
                    }else{
                        nameKeySet.add(keyNoEntity.getName());
                    }
                }
            }
        }
        return nameKeySet;
    }

    public static void main(String[] args) {
        List<String> list = load();
//        System.out.println(list);
        List<LawSuitV3OutputEntity> outList = JSON.parseArray(list.get(0),LawSuitV3OutputEntity.class);
        for (LawSuitV3OutputEntity item : outList) {
            System.out.println(evaluate(JSON.toJSONString(item)));;
        }
    }

    public static List<String> load(){
        List<String> list = new ArrayList<>();
        try (InputStream is = CaseSplitUtil.class.getResourceAsStream("/test_sfaj.csv")) {
            BufferedReader br = new BufferedReader(new InputStreamReader(is));
            String line;
            while ((line = br.readLine()) != null) {
                if(!Strings.isNullOrEmpty(line)){
                    list.add(line);
                }
            }
            br.close();
        }catch (Exception e){

        }

        return list;

    }
}

@Data
@AllArgsConstructor
@NoArgsConstructor
class TmpEntity1{
    private int idx;
    private String trialRound;
    private List<NameAndKeyNoEntity> proList;
    private List<NameAndKeyNoEntity> defList;
}
