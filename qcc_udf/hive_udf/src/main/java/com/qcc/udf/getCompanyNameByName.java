package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Auther: wangbin
 * @Date: 2019/8/23 10:52
 * @Description:公司名中括号修正。包含英文名则修改成英文括号，包含中文名则修改为中文括号。
 */
public class getCompanyNameByName extends UDF {
    public static String evaluate(Object param) {
        // 如果全空，则返回空值
        if (param == null || "".equals(param)) {
            return "";
        }
        // 全部英文的情况下，返回英文括号
        Matcher m = Pattern.compile("[\u4e00-\u9fa5]").matcher(param.toString());
        if (!m.find()) {
            return param.toString().replace("（", "(").replace("）", ")");
        } else {
            // 其他情况，返回中文括号
            return param.toString().replace("(", "（").replace(")", "）");
        }
    }
}
