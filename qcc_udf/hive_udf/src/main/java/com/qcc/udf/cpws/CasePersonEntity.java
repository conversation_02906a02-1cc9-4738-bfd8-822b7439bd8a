package com.qcc.udf.cpws;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * 文书涉案人信息存储对象
 */
@Data
public class CasePersonEntity {
    // 涉案人姓名
    private String name;
    // 涉案角色（原告/被告/原告-法定代表人/被告-法定代表人）
    private String role;
    // 出生年月日（格式为：yyyy-MM-dd）
    private String birthDay;
    // 地址信息（需要标记地址的类型：现住地/籍贯地/所在地...）
    private JSONObject address;
    // 当个人信息出现在原被告公司下的"法定代表人"位置时，原被告位置处的公司名
    private String relatedCompanyName;
}