package com.qcc.udf.cpws;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 裁判文书清洗UDF：提取本案件关联的其它裁判文书案号
 * ---------------------------------------------------------------------------------------------------------
 * create temporary function extractBeforeCaseNo as 'com.qcc.udf.cpws.ExtractBeforeCaseNoUDF' using jar 'hdfs:///data/hive/udf/qcc_udf.jar';
 * ---------------------------------------------------------------------------------------------------------
 * select extractCaseTrialRound (trialProcess, caseNo, caseType, trialRound);
 * 结果: 'caseNo1,caseNo2,CaseNo3...'
 */
public class ExtractBeforeCaseNoUDF extends UDF {

    /**
     * 从审判经过信息中提取与该案件关联的其它案号信息（多个案号用,分隔，没有则返回空）
     * @param trialProcess  审判经过
     * @param caseNo        当前案件的案号
     * @param caseType      案件类型
     * @param trialRound    审判程序
     * @return
     */
    public String evaluate(String trialProcess, String caseNo, String caseType, String trialRound) {
        try {
            if (StringUtils.isNotBlank(trialProcess) && StringUtils.isNotBlank(caseNo)
                    && StringUtils.isNotBlank(caseType) && StringUtils.isNotBlank(trialRound)) {
                /**
                 * 提取逻辑描述：
                 * 1 转为全角括号
                 * 2 正则提取段落中的案号  (（[0-9]{4}）[\u4e00-\u9fa5]+.+?号(之[一二三四五六七八九十])*)
                 * 3 提取到的案号列表提取字数较少的案号以及与自身重复的案号
                 * 4 返回去重后的结果
                 *
                 * 注：目前的需求中提取
                 *     - 执行案件
                 *     - 行政案件
                 *     - 民事案件, 民事一审程序不需要提取beforeCaseNo
                 */
                if (caseType.equals("zx")
                        || caseType.equals("xz")
                        || (caseType.equals("ms") && !trialRound.equals("民事一审"))) {
                    Pattern extractPattern = Pattern.compile("(（[0-9]{4}）[\\u4e00-\\u9fa5]+.+?号(之[一二三四五六七八九十])*)");
                    Matcher matcher = extractPattern.matcher(
                            trialProcess.replace("(", "（").replace(")", "）"));
                    List<String> linkedCaseNoList = new ArrayList<>();
                    while (matcher.find()) {
                        linkedCaseNoList.add(matcher.group(0));
                    }

                    final String compareCaseNo = caseNo.replace("(", "（").replace(")", "）");
                    Set<String> beforeCaseNoSet = linkedCaseNoList.stream()
                            .filter(e -> !e.equals(compareCaseNo))
                            .filter(e -> !e.contains("第xx号"))
                            .filter(e -> !e.contains("["))
                            .collect(Collectors.toSet());
                    return StringUtils.join(beforeCaseNoSet, ",");
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return "";
    }
}
