package com.qcc.udf.casesearch_v3;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.LinkedHashSet;
import java.util.Set;
import java.util.TreeSet;

public class mergeInfo extends UDF {

    public String evaluate(String anno, String annos) {
       String result = "";

       Set<String> oldSet = new LinkedHashSet<>();
       String[] aa = anno.split(",");
       for (String str : aa){
           oldSet.add(str);
       }

       if (StringUtils.isNotEmpty(annos)){
           Set<String> annoSet = new LinkedHashSet<>();
           String[] arr = annos.split(",");
           for (String str : arr){
               if (StringUtils.isNotEmpty(str) && !oldSet.contains(str) && !" ".equals(str)){
                   annoSet.add(str);
               }
           }

           if (annoSet.size() > 0){
               Set<String> sortSet = new TreeSet<String>((o1, o2) -> o1.compareTo(o2));
               sortSet.addAll(annoSet);

               for (String str : sortSet){
                   result = result.concat(",").concat(str);
               }

               result = result.length() > 0 ? result.substring(1) : result;
           }
       }

       return result;
    }
}
