package com.qcc.udf;

import jodd.util.URLDecoder;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.net.URLEncoder;

/**
 * @Auther: wangbin
 * @Date: 2019/5/31 11:20
 * @Description: 编码和解码
 */
public class DecodeUtils extends UDF {

    public static String evaluate(String... param) {
        //传一个参数解码;传两个参数编码（第二参数随意传）
        try{
            if (param.length == 1) {
                return decode(param[0]);
            } else {
                return encode(param[0]);
            }
        }catch (Exception e){
            e.printStackTrace();
            return "";
        }

    }

    public static String decode(String param) {
        //采用UTF-8字符集进行解码
        if (param == null) {
            return "";
        }
        String decode = URLDecoder.decode(param, "UTF-8");
        return decode.replace("<em>", "").replace("</em>", "");
    }

    public static String encode(String param) {
        //采用utf-8字符集编码
        if (param == null) {
            return "";
        }
        try {
            return URLEncoder.encode(param, "UTF-8");
        } catch (Exception e) {
            return "";
        }
    }

}
