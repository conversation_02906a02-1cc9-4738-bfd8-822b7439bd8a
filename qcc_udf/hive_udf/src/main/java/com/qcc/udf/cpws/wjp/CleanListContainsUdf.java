package com.qcc.udf.cpws.wjp;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class CleanListContainsUdf extends UDF {
    public static String evaluate(String names, String name) {
        if (StringUtils.isNotEmpty(names) && StringUtils.isNotEmpty(name)) {
            // 去掉字符串的方括号
            names = names.substring(1, names.length() - 1);

            // 使用 split 方法按逗号分割字符串
            String[] array = names.split(",");

            // 使用 stream 处理空格并转换为列表
            List<String> list = Arrays.stream(array)
                    .map(String::trim)
                    .collect(Collectors.toList());
            if (list.contains(name)) {
                return "true";
            }
        }
        return "false";
    }

    public static void main(String[] args) {
        System.out.println(evaluate("[]", "14"));
    }
}
