package com.qcc.udf.risk_analysis;

public enum PromoterTypeEnum {
    COMPANY(0, "公司"),
    ORG(1, "社会组织"),
    EMPLOYEE(2, "主要人员"),
    HKCOMPANY(3, "香港公司"),
    GOVERNMENT(4, "政府机构和学校"),
    TWCOMPANY(5, "台湾公司"),
    PEFUNDPRODUCT(6, "私募基金产品"),
    HOSPITAL(7, "医院"),
    OVERSEA(8, "海外公司"),
    OVERSEA2(9, "海外公司"),
    FUND(10, "基金会"),
    INSTITUTION(11, "事业单位"),
    LAWOFFICE(12, "律师事务所"),
    COMPANYWITHOUTKEYNO(-1, "无法判断"),
    PERSONWITHOUTCERNO(-2, "没有Id的人名"),
    OTHER(-3, "其他");

    private Integer type;
    private String name;

    private PromoterTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public static PromoterTypeEnum getByType(int type) {
        PromoterTypeEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            PromoterTypeEnum typeEnum = var1[var3];
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }

        return null;
    }

    public Integer getType() {
        return this.type;
    }

    public String getName() {
        return this.name;
    }
}
