package com.qcc.udf.trade_mark;

import com.qcc.udf.trade_mark.util.EncryptCoder;
import org.apache.hadoop.hive.ql.exec.UDF;

public class TmImageUDF extends UDF {

    public String evaluate(String regNo, String isHasImage, String isSmallSize){
        String baseUrl = "http://tm-image.qichacha.com/";
        String regno = EncryptCoder.EncodeMd5Double(regNo);
        return "true".equals(isHasImage) ? ("true".equals(isSmallSize) ? baseUrl + regno + ".jpg@100h_160w_1l_50q" : baseUrl + regno + ".jpg") : "";

    }

}
