package com.qcc.udf.casesearch_v3;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.base.Strings;
import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.output.LawSuitV3OutputEntity;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.casesearch_v3.entity.output.SXListEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

public class getSXActionType extends UDF {
    public static String evaluate(String param) {
       List<SXActionType> sxIdActionType = new ArrayList<>();
        if(Strings.isNullOrEmpty(param)){
            return JSON.toJSONString(sxIdActionType);
        }

        try{
            List<LawSuitV3OutputEntity> list = JSONArray.parseArray(param,LawSuitV3OutputEntity.class);
            if(CollectionUtils.isNotEmpty(list)){
                for (LawSuitV3OutputEntity outputEntity : list) {
                    if(CollectionUtils.isNotEmpty(outputEntity.getInfoList())){
                        String id = outputEntity.getId();
                        List<SXListEntityTmp> sxListEntityList = new ArrayList<>();
                        for (InfoListEntity infoListEntity : outputEntity.getInfoList()) {
                            if(CollectionUtils.isNotEmpty(infoListEntity.getSxList())){
                                for (SXListEntity sxListEntity : infoListEntity.getSxList()) {
                                    SXListEntityTmp data = new SXListEntityTmp();
                                    data.setId(sxListEntity.getId());
                                    data.setActionType(sxListEntity.getActionType());
                                    sxListEntityList.add(data);
                                }
                            }
                        }
                        if(sxListEntityList.size()>0){
                            sxIdActionType.add(new SXActionType(id,sxListEntityList));
                        }
                    }
                }
            }
        }catch (Exception e){

        }


        return JSON.toJSONString(sxIdActionType);
    }

    public static void main(String[] args) {
        String str = "[{\"AmtInfo\":{\"0d82fbf0673b2a0560b261cd257dd936\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"2479f17d842298e1e055ee9c9600e516\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"24beed6281c99e7ce5509fc81d52b0f2\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"30e9dc3ea3bb4313ae7454324bb224bf\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"413bfb6e7063b1fd8ccb9ee0190001ad\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"4a55ae61361970e3f69d8ff028df85b8\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"5aed962d8769767646054bd59a30dd7d\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"75ca0997d3ae599cb28260ea2fade040\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"7efe928d3c89b15502d0ebd447539724\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"82166feaaa33dc17a32273764edacfbd\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"9022a1feef1cf5ddbf3488841efa128c\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"9a7df66c6036690ecad1606fc5f585f0\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"9bcdff96e037b54d1be4f8de5b7a0207\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"9f139dcbbaaac7e69d1330bad32ebd51\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"a0742d602a939af6cc83a3b240a62a55\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"ab2a783d1a645f776de95d05fa4cb60e\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"ad25046edd7f16f311d3f912b6d0b874\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"c63b36fd59b28629348af3da8550483c\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"d18184a9f74d48e5357483c51a7610ab\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"dc3e7b90ad1119a630de16056f415a09\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"e17ebfcbb8e589465bf185bcb0bee6a0\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"e4726457dfbcdd8b2643a6e5c4fa6fbc\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"f3e6f6695b06d5ad8cb8f0846e990f93\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"f818b00a2d59bfcd41e8876cc6119bc7\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"fb69c2d5a3e9ee12fae07f4ce7d8287b\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"fd88f69e8eb968ade324f6ff5d148501\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"pebc4effe3d8f5d92065812267cff88e\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"},\"pf4ac39aba0d0f543d958c3c19db97a9\":{\"Amt\":\"2514300\",\"Type\":\"执行标的\"}},\"AnNoList\":\"（2016）晋0108执192号\",\"AnnoCnt\":1,\"CaseCnt\":0,\"CaseName\":\"李怀增,李晓林,林州建总建筑工程有限公司执行案件\",\"CaseReason\":\"\",\"CaseRole\":\"[{\\\"D\\\":\\\"首次执行被执行人\\\",\\\"N\\\":\\\"pebc4effe3d8f5d92065812267cff88e\\\",\\\"O\\\":2,\\\"P\\\":\\\"李怀增\\\",\\\"R\\\":\\\"被执行人\\\"},{\\\"D\\\":\\\"首次执行被执行人\\\",\\\"N\\\":\\\"pf4ac39aba0d0f543d958c3c19db97a9\\\",\\\"O\\\":2,\\\"P\\\":\\\"李晓林\\\",\\\"R\\\":\\\"被执行人\\\"},{\\\"D\\\":\\\"首次执行被执行人\\\",\\\"N\\\":\\\"2479f17d842298e1e055ee9c9600e516\\\",\\\"O\\\":0,\\\"P\\\":\\\"林州建总建筑工程有限公司\\\",\\\"R\\\":\\\"被执行人\\\"}]\",\"CaseType\":\"执行案件\",\"CfdfCnt\":0,\"CfgsCnt\":0,\"CfxyCnt\":0,\"CompanyKeywords\":\"0d82fbf0673b2a0560b261cd257dd936,2479f17d842298e1e055ee9c9600e516,24beed6281c99e7ce5509fc81d52b0f2,30e9dc3ea3bb4313ae7454324bb224bf,413bfb6e7063b1fd8ccb9ee0190001ad,4a55ae61361970e3f69d8ff028df85b8,5aed962d8769767646054bd59a30dd7d,75ca0997d3ae599cb28260ea2fade040,7efe928d3c89b15502d0ebd447539724,82166feaaa33dc17a32273764edacfbd,9022a1feef1cf5ddbf3488841efa128c,9a7df66c6036690ecad1606fc5f585f0,9bcdff96e037b54d1be4f8de5b7a0207,9f139dcbbaaac7e69d1330bad32ebd51,a0742d602a939af6cc83a3b240a62a55,ab2a783d1a645f776de95d05fa4cb60e,ad25046edd7f16f311d3f912b6d0b874,c63b36fd59b28629348af3da8550483c,d18184a9f74d48e5357483c51a7610ab,dc3e7b90ad1119a630de16056f415a09,e17ebfcbb8e589465bf185bcb0bee6a0,e4726457dfbcdd8b2643a6e5c4fa6fbc,f3e6f6695b06d5ad8cb8f0846e990f93,f818b00a2d59bfcd41e8876cc6119bc7,fd88f69e8eb968ade324f6ff5d148501,pebc4effe3d8f5d92065812267cff88e,pf4ac39aba0d0f543d958c3c19db97a9,李怀增,李晓林,林州市建筑(集团)总公司,林州建总建筑工程有限公司,林州建总建筑工程有限公司临汾分公司,林州建总建筑工程有限公司云南分公司,林州建总建筑工程有限公司佛山分公司,林州建总建筑工程有限公司南京分公司,林州建总建筑工程有限公司双鸭山分公司,林州建总建筑工程有限公司太原分公司,林州建总建筑工程有限公司孝义分公司,林州建总建筑工程有限公司安徽分公司,林州建总建筑工程有限公司山西分公司,林州建总建筑工程有限公司徐州分公司,林州建总建筑工程有限公司成都一分公司,林州建总建筑工程有限公司泸州分公司,林州建总建筑工程有限公司洛阳分公司,林州建总建筑工程有限公司海南分公司,林州建总建筑工程有限公司淮北分公司,林州建总建筑工程有限公司福建分公司,林州建总建筑工程有限公司西安分公司,林州建总建筑工程有限公司贵州分公司,林州建总建筑工程有限公司辽宁分公司,林州建总建筑工程有限公司郑州第一分公司,林州建总建筑工程有限公司重庆分公司,林州建总建筑工程有限公司重庆江北分公司,林州建总建筑工程有限公司银川分公司,林州建总建筑工程有限公司长春分公司,林州建总建筑工程有限公司长治分公司,林州建总建筑工程有限公司青海分公司,林州建总建筑工程有限公司鹤岗分公司,梁强,梁敏捷\",\"CourtList\":\"山西省太原市尖草坪区人民法院\",\"EarliestDate\":1460304000,\"EarliestDateType\":\"首次执行|被执行人立案日期\",\"FyggCnt\":0,\"GqdjCnt\":0,\"GroupId\":\"003907b84bd3191cb5cfb5eb22b2f89d\",\"HbcfCnt\":0,\"Id\":\"eb34e446668687cf6c213ea703caf7dc\",\"InfoList\":[{\"AnNo\":\"（2016）晋0108执192号\",\"CaseList\":[],\"CaseReason\":\"\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"山西省太原市尖草坪区人民法院\",\"Defendant\":[{\"KeyNo\":\"pebc4effe3d8f5d92065812267cff88e\",\"Name\":\"李怀增\",\"Org\":2,\"Role\":\"被执行人\"},{\"KeyNo\":\"pf4ac39aba0d0f543d958c3c19db97a9\",\"Name\":\"李晓林\",\"Org\":2,\"Role\":\"被执行人\"},{\"KeyNo\":\"2479f17d842298e1e055ee9c9600e516\",\"Name\":\"林州建总建筑工程有限公司\",\"Org\":0,\"Role\":\"被执行人\"}],\"ExecuteNo\":\"2014尖商初字第127号\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1533139200,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[],\"SdggList\":[],\"SxList\":[{\"ActionType\":\"其他有履行能力而拒不履行生效法律文书确定义务\",\"ExecuteStatus\":\"全部未履行\",\"Id\":\"a6273a7e81291dc49c811b1fa1a9fb902\",\"IsValid\":0,\"NameAndKeyNo\":[{\"KeyNo\":\"pf4ac39aba0d0f543d958c3c19db97a9\",\"Name\":\"李晓林\",\"Org\":2}],\"PublishDate\":1464796800},{\"ActionType\":\"其他有履行能力而拒不履行生效法律文书确定义务\",\"ExecuteStatus\":\"全部未履行\",\"Id\":\"bc08a5dabb42ce12b906ec518d548d5b2\",\"IsValid\":0,\"NameAndKeyNo\":[{\"KeyNo\":\"2479f17d842298e1e055ee9c9600e516\",\"Name\":\"林州建总建筑工程有限公司\",\"Org\":0}],\"PublishDate\":1464796800},{\"ActionType\":\"其他有履行能力而拒不履行生效法律文书确定义务\",\"ExecuteStatus\":\"全部未履行\",\"Id\":\"cb43e07f4f33b147ecbd69f6d89a30962\",\"IsValid\":0,\"NameAndKeyNo\":[{\"KeyNo\":\"pebc4effe3d8f5d92065812267cff88e\",\"Name\":\"李怀增\",\"Org\":2}],\"PublishDate\":1464796800}],\"TrialRound\":\"首次执行\",\"XgList\":[{\"CompanyInfo\":[{\"KeyNo\":\"\",\"Name\":\"梁敏捷\",\"Org\":-2}],\"GlNameAndKeyNo\":[],\"Id\":\"5007d197f99c8b64a987d49b276b9a58\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"梁敏捷\",\"Org\":-2}],\"PublishDate\":1533139200,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"王慧东\",\"Org\":-2}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"梁敏捷\",\"Org\":-2}]},{\"CompanyInfo\":[{\"KeyNo\":\"\",\"Name\":\"梁强\",\"Org\":-2}],\"GlNameAndKeyNo\":[],\"Id\":\"ac4a64f37d39bb85b34d68114b081da4\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"梁强\",\"Org\":-2}],\"PublishDate\":1533139200,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"王慧东\",\"Org\":-2}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"梁强\",\"Org\":-2}]}],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[{\"Biaodi\":\"2514300\",\"Id\":\"1f602b2ec3a05a87626bbea3243166241\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"5aed962d8769767646054bd59a30dd7d\",\"Name\":\"林州建总建筑工程有限公司西安分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"227aeef1e71f579d5faa9db76147b0331\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"ab2a783d1a645f776de95d05fa4cb60e\",\"Name\":\"林州建总建筑工程有限公司淮北分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"26822ce07b145efd83c33084355bc6d51\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"fb69c2d5a3e9ee12fae07f4ce7d8287b\",\"Name\":\"林州建总建筑工程有限公司洛阳分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"35f061a25f3b8dcf62e9296f2125d8231\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"4a55ae61361970e3f69d8ff028df85b8\",\"Name\":\"林州建总建筑工程有限公司山西分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"4a58f8defd085b833b3beb607faf604d1\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"c63b36fd59b28629348af3da8550483c\",\"Name\":\"林州建总建筑工程有限公司福建分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"4d6fec549ed86bc8ac2d139be39b2ee21\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"0d82fbf0673b2a0560b261cd257dd936\",\"Name\":\"林州建总建筑工程有限公司长春分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"5e9e31c4b3d85c00a1f17804a52c7bc91\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"7efe928d3c89b15502d0ebd447539724\",\"Name\":\"林州建总建筑工程有限公司青海分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"63fd33d3b087832b09ee5d17936aa01b1\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"ad25046edd7f16f311d3f912b6d0b874\",\"Name\":\"林州建总建筑工程有限公司重庆分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"6746fb5fde8c52ed44a3eccc45c0d5891\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"林州建总建筑工程有限公司孝义分公司\",\"Org\":-1}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"6efe2872cca24b9ab2aca47fa3aedd7d1\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"75ca0997d3ae599cb28260ea2fade040\",\"Name\":\"林州建总建筑工程有限公司成都一分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"71f4c1a890037bf80241a4be5077ab1f1\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"82166feaaa33dc17a32273764edacfbd\",\"Name\":\"林州建总建筑工程有限公司安徽分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"73593a2672e6e1b77600ccdd7645d1d11\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"d18184a9f74d48e5357483c51a7610ab\",\"Name\":\"林州建总建筑工程有限公司贵州分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"7b03c844f84825e2cb8f034550124b081\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"30e9dc3ea3bb4313ae7454324bb224bf\",\"Name\":\"林州建总建筑工程有限公司重庆江北分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"7bf0d19d0fc30080e913fd642166cf4f1\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"9bcdff96e037b54d1be4f8de5b7a0207\",\"Name\":\"林州建总建筑工程有限公司海南分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"80cad93c59780abd61a0f13d0ee529731\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"9022a1feef1cf5ddbf3488841efa128c\",\"Name\":\"林州建总建筑工程有限公司云南分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"8238d172637f7d3ce726828a14d3144d1\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"9f139dcbbaaac7e69d1330bad32ebd51\",\"Name\":\"林州建总建筑工程有限公司鹤岗分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"9824765aa61cb526efa5be962e45c0b71\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"dc3e7b90ad1119a630de16056f415a09\",\"Name\":\"林州建总建筑工程有限公司太原分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"9b7bbb488829cb805005a55f53e795f01\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"f818b00a2d59bfcd41e8876cc6119bc7\",\"Name\":\"林州建总建筑工程有限公司南京分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"a6273a7e81291dc49c811b1fa1a9fb901\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"pf4ac39aba0d0f543d958c3c19db97a9\",\"Name\":\"李晓林\",\"Org\":2}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"b6a075c8cb56c529397b2536e93fdc691\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"24beed6281c99e7ce5509fc81d52b0f2\",\"Name\":\"林州建总建筑工程有限公司银川分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"bc08a5dabb42ce12b906ec518d548d5b1\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"2479f17d842298e1e055ee9c9600e516\",\"Name\":\"林州建总建筑工程有限公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"cb43e07f4f33b147ecbd69f6d89a30961\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"pebc4effe3d8f5d92065812267cff88e\",\"Name\":\"李怀增\",\"Org\":2}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"d6461785e48c61113e195d36c9c3504a1\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"f3e6f6695b06d5ad8cb8f0846e990f93\",\"Name\":\"林州建总建筑工程有限公司佛山分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"d6ac9efe8a49339a0c707ad92fc634531\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"e17ebfcbb8e589465bf185bcb0bee6a0\",\"Name\":\"林州建总建筑工程有限公司辽宁分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"ddc9a46631ae9f5f9725bcf1de32b2e91\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"e4726457dfbcdd8b2643a6e5c4fa6fbc\",\"Name\":\"林州建总建筑工程有限公司临汾分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"ea5319c655a1413d0d353ddb49eb0b321\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"fd88f69e8eb968ade324f6ff5d148501\",\"Name\":\"林州建总建筑工程有限公司郑州第一分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"eb652a21cf99dd19b77750ee9d684bae1\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"413bfb6e7063b1fd8ccb9ee0190001ad\",\"Name\":\"林州建总建筑工程有限公司双鸭山分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"f76b353e39866bc0fba331de6db6e8c21\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"a0742d602a939af6cc83a3b240a62a55\",\"Name\":\"林州建总建筑工程有限公司徐州分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"fc158c56626c1f56eaeaf478be6264da1\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"9a7df66c6036690ecad1606fc5f585f0\",\"Name\":\"林州建总建筑工程有限公司泸州分公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"2514300\",\"Id\":\"fd310ccf00ab2407bcfa554678d1c2c91\",\"IsValid\":0,\"LianDate\":1460304000,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"林州建总建筑工程有限公司长治分公司\",\"Org\":-1}],\"SqrNameAndKeyNo\":[]}]}],\"KtggCnt\":0,\"LastestDate\":1533139200,\"LastestDateType\":\"首次执行|限制高消费发布日期\",\"LatestTrialRound\":\"首次执行\",\"LianCnt\":0,\"PcczCnt\":0,\"ProcuratorateList\":\"\",\"Province\":\"SX\",\"SdggCnt\":0,\"Source\":\"OT\",\"SxCnt\":3,\"Tags\":\"1,2,3\",\"Type\":1,\"XgCnt\":2,\"XjpgCnt\":0,\"XzcfCnt\":0,\"ZbCnt\":0,\"ZxCnt\":30}] ";
        System.out.println(evaluate(str));
        System.out.println(evaluate(""));
    }
}
@Data
@AllArgsConstructor
class SXActionType{
    private String id;
    List<SXListEntityTmp> list;
}
@Data
class SXListEntityTmp{
    @JSONField(name = "Id")
    private String id = "";
    @JSONField(name = "ActionType")
    private String actionType = "";
}
