package com.qcc.udf.kzz;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * 获取荣誉资质名称
 *
 * <AUTHOR>
 * @date 2022/3/9
 */
public class GetCompanyTechName extends UDF {
    /**
     * 获取荣誉资质名称
     *
     * @param code
     */
    public static String evaluate(String code) {
        String name = "";
        if (StringUtils.isNotBlank(code)) {
            switch (code) {
                case "1":
                    name = "高新技术企业";
                    break;
                case "2":
                    name = "独角兽企业";
                    break;
                case "3":
                    name = "瞪羚企业认定";
                    break;
                case "4":
                    name = "雏鹰公司";
                    break;
                case "5":
                    name = "创新型科技企业";
                    break;
                case "6":
                    name = "科技小巨人企业";
                    break;
                case "7":
                    name = "专精特新企业";
                    break;
                case "8":
                    name = "企业技术中心";
                    break;
                case "9":
                    name = "科技企业孵化器";
                    break;
                case "10":
                    name = "技术创新示范企业";
                    break;
                case "11":
                    name = "科技型中小企业";
                    break;
                case "12":
                    name = "众创空间";
                    break;
                case "13":
                    name = "“隐形冠军”企业";
                    break;
                case "14":
                    name = "技术先进型服务企业";
                    break;
                case "15":
                    name = "民营科技企业";
                    break;
                case "16":
                    name = "牛羚企业";
                    break;
                case "17":
                    name = "专精特新”小巨人“企业";
                    break;
                default:
                    break;

            }
        }

        return name;
    }
}
