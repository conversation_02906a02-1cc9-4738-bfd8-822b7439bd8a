package com.qcc.udf.casesearch_v3.entity.output;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class CaseListEntity extends BaseCaseOutEntity{
     @JSONField(name = "Id")
    private String id = "";
     @JSONField(name = "DocType")
    private String docType = "";
     @JSONField(name = "JudgeDate")
    private Long judgeDate = 0L;
     @JSONField(name = "IsValid")
    private Integer isValid = 0;
     @JSONField(name = "ResultType")
    private String resultType = "";
     @JSONField(name = "Result")
    private String result = "";
     @JSONField(name = "Amt")
    private String amt = "";
    /**
     * 文书类型
     */
    @JSONField(name = "CaseType")
    private String caseType = "";

    /*该文书是否为无内容文书（1：是 0：否）*/
    @JSONField(name = "ShieldCaseFlag")
    private Integer shieldCaseFlag = 0;
}
