package com.qcc.udf.casesearch_v3.entity.input;

import com.alibaba.fastjson.annotation.JSONField;
import com.qcc.udf.casesearch_v3.entity.output.CaseRoleSort;
import lombok.Data;

import java.util.List;

/**
 * @Auther: <PERSON><PERSON>qiang
 * @Date: 2020/11/11 17:54
 * @Description:人与角色对应关系
 */
@Data
public class CaseRoleEntity {

    /**
     * P : 陈桂香
     * R : 申请执行人
     * N :
     * O : -2
     */

    @JSONField(name = "P")
    private String P;
    @JSONField(name = "R")
    private String R;
    @JSONField(name = "N")
    private String N;
    @JSONField(name = "O")
    private int O;

    /**
     * 所有身份汇总描述
     */
    @JSONField(name = "D")
    private String D;

    @JSONField(name = "RL")
    List<CaseRoleSort> roleList;

    @JSONField(serialize = false)
    private String T;

    @JSONField(name = "LF")
    private List<CPWSLawyerFirmGroupInfo> LawFirmList;

    @JSONField(serialize = false)
    private String JR;
}
