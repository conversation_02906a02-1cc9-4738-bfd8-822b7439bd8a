package com.qcc.udf.casesearch_v3.role;


import com.qcc.udf.cpws.ExtractCaseTrialRoundUDF;
import org.apache.commons.lang3.StringUtils;

import java.util.EnumSet;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 处罚机构分类
 */
public enum CaseNoTrialRoundEnum {
    DEFAULT_BLANK("其他", "原告/上诉人", "被告/被上诉人", "第三人"),
    DEFAULT_NULL("null", "原告/上诉人", "被告/被上诉人", "第三人"),
    MSYS("民事一审", "原告", "被告", "第三人"),
    SCZX("首次执行", "申请执行人", "被执行人", "第三人"),
    XSYS("刑事一审", "原告", "被告", "第三人"),
    MSES("民事二审", "上诉人", "被上诉人", "第三人"),
    HFZX("恢复执行", "申请执行人", "被执行人", "第三人"),
    CCBQZX("财产保全执行", "申请执行人", "被执行人", "第三人"),
    TBCX("特别程序", "特别程序申请人", "特别程序被申请人", "第三人"),
    XFYZXBG("刑罚与执行变更", "申请人", "被申请人", "第三人"),
    CCBQ("财产保全", "申请人", "被申请人", "第三人"),
    XZYS("行政一审", "原告", "被告", "第三人"),
    FSXZSQZXSC("非诉行政行为申请执行审查", "申请人", "被申请人", "第三人"),
    MSSQZSSC("民事申请再审审查", "申请人", "被申请人", "第三人"),
    ZXYY("执行异议", "申请人", "被申请人", "第三人"),
    SQTJ("诉前调解", "原告", "被告", "第三人"),
    XZES("行政二审", "上诉人", "被上诉人", "第三人"),
    XSES("刑事二审", "上诉人", "被上诉人", "第三人"),
    SQZFLSC("申请支付令审查", "申请人", "被申请人", "第三人"),
    MSZS("民事再审", "再审申请人", "再审被申请人", "第三人"),
    MSGXSS("民事管辖上诉", "上诉人", "被上诉人", "第三人"),
    XZSQZSSC("行政申请再审审查", "再审申请人", "再审被申请人", "第三人"),
    ZXFY("执行复议", "申请人", "被申请人", "第三人"),
    XSSPJD("刑事审判监督", "公诉机关", "被告人", "第三人"),
    PC("破产", "申请人", "被申请人", "第三人"),
    MSGX("民事管辖", "原告", "被告", "第三人"),
    CG("催告", "申请人", "被申请人", "第三人"),
    XZPCYS("行政赔偿一审", "原告", "被告", "第三人"),
    QTZX("其他执行", "申请执行人", "被执行人", "第三人"),
    MSYZQZSSC("民事依职权再审审查", "再审申请人", "再审被申请人", "第三人"),
    ZXJD("执行监督", "申请执行人", "被执行人", "第三人"),
    SFZCSC("司法制裁审查", "当事人", "被执行人", "第三人"),
    XSGX("刑事管辖", "原告", "被告", "第三人"),
    PCSQ("破产申请", "申请人", "被申请人", "第三人"),
    XZPCES("行政赔偿二审", "上诉人", "被上诉人", "第三人"),
    ZSSFJZ("执行司法救助", "申请人", "被申请人", "第三人"),
    QZQS("强制清算", "申请人", "被申请人", "第三人"),
    QTXS("其他刑事", "公诉机关", "被告人", "第三人"),
    XZGX("行政管辖", "原告", "被告", "第三人"),
    PCYYHSLPC("赔偿委员会审理赔偿", "赔偿请求人", "赔偿义务机关", "第三人"),
    MSKYZSSC("民事抗诉再审审查", "再审申请人", "再审被申请人", "第三人"),
    QTMS("其他民事", "原告", "被告", "第三人"),
    DSRCXZS("第三人撤销之诉", "原告", "被告", "第三人"),
    XZZS("行政再审", "再审申请人", "再审被申请人", "第三人"),
    XZYZQZSSC("行政依职权再审审查", "再审申请人", "再审被申请人", "第三人"),
    FYZWPCYWJGZP("法院作为赔偿义务机关自赔", "赔偿请求人", "赔偿义务机关", "第三人"),
    QZYL("强制医疗", "申请人", "被申请人", "第三人"),
    XZPCSQZSSC("行政赔偿申请再审审查", "再审申请人", "再审被申请人", "第三人"),
    QZQSSQSC("强制清算申请审查", "申请人", "被申请人", "第三人"),
    ZSXT("执行协调", "申请执行人", "被执行人", "第三人"),
    FSXZXWSQZXSCFY("非诉行政行为申请执行审查复议", "申请人", "被申请人", "第三人"),
    SFPCJDSC("司法赔偿监督审查", "赔偿请求人", "赔偿义务机关", "第三人"),
    CRYZXSQSC("承认与执行申请审查", "申请人", "被申请人", "第三人"),
    QTXZ("其他行政", "原告", "被告", "第三人"),
    MSSFJZ("民事司法救助", "申请人", "被申请人", "第三人"),
    SFZCFY("司法制裁复议", "申请人", "被申请人", "第三人"),
    XSFH("刑事复核", "公诉机关", "被告人", "第三人"),
    ZJBQ("证据保全", "申请人", "被申请人", "第三人"),
    PCSS("破产上诉", "上诉人", "被上诉人", "第三人"),
    SDWS("送达文书", "当事人", "被申请人", "第三人"),
    XSSFJZ("刑事司法救助", "申请人", "被申请人", "第三人"),
    XWBQ("行为保全", "申请人", "被申请人", "第三人"),
    MSTBCXJD("民事特别程序监督", "申请人", "被申请人", "第三人"),
    SSXFSFJZ("涉诉信访司法救助", "申请人", "被申请人", "第三人"),
    QTSFJZ("其他司法救助", "申请人", "被申请人", "第三人"),
    RKYZXSQSC("认可与执行申请审查", "申请人", "被申请人", "第三人"),
    XZKSZSSC("行政抗诉再审审查", "再审申请人", "再审被申请人", "第三人"),
    XZPCGX("行政赔偿管辖", "原告", "被告", "第三人"),
    ZFLJD("支付令监督", "申请人", "被申请人", "第三人"),
    XZGXSS("行政管辖上诉", "上诉人", "被上诉人", "第三人"),
    XZPCZS("行政赔偿再审", "再审申请人", "再审被申请人", "第三人"),
    XZSFJZ("行政司法救助", "申请人", "被申请人", "第三人"),
    SFPCJDBYCS("司法赔偿监督本院赔偿委员会重审", "申请人", "被申请人", "第三人"),
    XWBQFY("行为保全复议", "申请人", "被申请人", "第三人"),
    SFPCJDSJFYCS("司法赔偿监督上级法院赔偿委员会重审", "申请人", "被申请人", "第三人"),
    PCJD("破产监督", "申请人", "被申请人", "第三人"),
    GJPCSFJZ("国家赔偿司法救助", "申请人", "被申请人", "第三人"),
    QTPC("其他赔偿", "申请人", "被申请人", "第三人"),
    SQMSWFSD("申请没收违法所得", "申请人", "被申请人", "第三人"),
    DCQZ("调查取证", "申请人", "被申请人", "第三人"),
    QZQSSS("强制清算上诉", "上诉人", "被上诉人", "第三人"),
    XZPCYZQZSSC("行政赔偿依职权再审审查", "再审申请人", "再审被申请人", "第三人"),
    XZPCGXSS("行政赔偿管辖上诉", "上诉人", "被上诉人", "第三人"),
    RKYZXSCFY("认可与执行审查复议", "申请人", "被申请人", "第三人"),
    XZPCKSZSSC("行政赔偿抗诉再审审查", "再审申请人", "再审被申请人", "第三人"),
    RKYZXSCQT("认可与执行审查其他", "申请人", "被申请人", "第三人"),
    TZZXSX("停止执行死刑", "当事人", "当事人", "第三人"),
    YD("引渡", "当事人", "当事人", "第三人"),
    ;

    private String trialRound;
    private String pltfName;
    private String defdName;
    private String tdptName;

    CaseNoTrialRoundEnum(String trialRound, String pltfName, String defdName, String tdptName) {
        this.trialRound = trialRound;
        this.pltfName = pltfName;
        this.defdName = defdName;
        this.tdptName = tdptName;
    }

    private static final Map<String, CaseNoTrialRoundEnum> lookup = new LinkedHashMap<>();

    static {
        EnumSet.allOf(CaseNoTrialRoundEnum.class).stream().forEach(e -> {
                    lookup.put(e.trialRound, e);
                }
        );
    }

    public static CaseNoTrialRoundEnum find(String trialRound) {
        if (StringUtils.isBlank(trialRound)) {
            return CaseNoTrialRoundEnum.DEFAULT_BLANK;
        }
        for (Map.Entry<String, CaseNoTrialRoundEnum> entry : lookup.entrySet()) {
            if (Objects.equals(trialRound,entry.getKey())) {
                return entry.getValue();
            }
        }
        return CaseNoTrialRoundEnum.DEFAULT_BLANK;
    }

    public String findRole(int roleTag) {
        if (roleTag == 0) {
            return this.pltfName;
        }
        if (roleTag == 1) {
            return this.defdName;
        }
        if (roleTag == 2) {
            return this.tdptName;
        }
        return "当事人";
    }

    static  ExtractCaseTrialRoundUDF extractCaseTrialRoundUDF = new ExtractCaseTrialRoundUDF();
    public static CaseNoTrialRoundEnum getCaseNoTrialRoundEnum(String caseNo) {
        CaseNoTrialRoundEnum caseNoTrialRoundEnum = null;
        if (StringUtils.isBlank(caseNo)) {
           return CaseNoTrialRoundEnum.DEFAULT_BLANK;
        }

        if (caseNo.contains(",")){
            return CaseNoTrialRoundEnum.DEFAULT_BLANK;
        }

        String trialRound = extractCaseTrialRoundUDF.evaluate(caseNo);
        caseNoTrialRoundEnum = CaseNoTrialRoundEnum.find(trialRound);

        return caseNoTrialRoundEnum;
    }

    public static void main(String[] args) {
        String caseNo = "（2015）朝执他字第4号";
        System.out.println(getCaseNoTrialRoundEnum(caseNo));
    }
}
