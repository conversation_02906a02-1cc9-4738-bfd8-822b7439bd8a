package com.qcc.udf.cpws.wjp;

import com.google.common.base.Strings;
import com.qcc.udf.cpws.CommonUtil;
import com.qcc.udf.tax.DateUtil;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date ：Created in 2020/8/19 10:20
 * @description：通用时间清洗类
 */
public class CommonDateCleanUtil {
    static Map<String, String> CHINESE_NUM_MAP = new LinkedHashMap<>();
    static List<Pattern> PATTERN_LIST = new ArrayList<>();
    // 定义工作时间范围
    private static LocalTime WORK_START_TIME;
    private static LocalTime WORK_END_TIME;

    static {

        CHINESE_NUM_MAP.put("一十一", "11");
        CHINESE_NUM_MAP.put("一十二", "12");
        CHINESE_NUM_MAP.put("一十三", "13");
        CHINESE_NUM_MAP.put("一十四", "14");
        CHINESE_NUM_MAP.put("一十五", "15");
        CHINESE_NUM_MAP.put("一十六", "16");
        CHINESE_NUM_MAP.put("一十七", "17");
        CHINESE_NUM_MAP.put("一十八", "18");
        CHINESE_NUM_MAP.put("一十九", "19");
        CHINESE_NUM_MAP.put("二?一", "201");
        CHINESE_NUM_MAP.put("二十一", "21");
        CHINESE_NUM_MAP.put("二十二", "22");
        CHINESE_NUM_MAP.put("二十三", "23");
        CHINESE_NUM_MAP.put("二十四", "24");
        CHINESE_NUM_MAP.put("二十五", "25");
        CHINESE_NUM_MAP.put("二十六", "26");
        CHINESE_NUM_MAP.put("二十七", "27");
        CHINESE_NUM_MAP.put("二十八", "28");
        CHINESE_NUM_MAP.put("二十九", "29");
        CHINESE_NUM_MAP.put("三十一", "31");
        CHINESE_NUM_MAP.put("三十二", "32");
        CHINESE_NUM_MAP.put("三十三", "33");
        CHINESE_NUM_MAP.put("三十四", "34");
        CHINESE_NUM_MAP.put("三十五", "35");
        CHINESE_NUM_MAP.put("三十六", "36");
        CHINESE_NUM_MAP.put("三十七", "37");
        CHINESE_NUM_MAP.put("三十八", "38");
        CHINESE_NUM_MAP.put("三十九", "39");
        CHINESE_NUM_MAP.put("四十一", "41");
        CHINESE_NUM_MAP.put("四十二", "42");
        CHINESE_NUM_MAP.put("四十三", "43");
        CHINESE_NUM_MAP.put("四十四", "44");
        CHINESE_NUM_MAP.put("四十五", "45");
        CHINESE_NUM_MAP.put("四十六", "46");
        CHINESE_NUM_MAP.put("四十七", "47");
        CHINESE_NUM_MAP.put("四十八", "48");
        CHINESE_NUM_MAP.put("四十九", "49");
        CHINESE_NUM_MAP.put("五十一", "51");
        CHINESE_NUM_MAP.put("五十二", "52");
        CHINESE_NUM_MAP.put("五十三", "53");
        CHINESE_NUM_MAP.put("五十四", "54");
        CHINESE_NUM_MAP.put("五十五", "55");
        CHINESE_NUM_MAP.put("五十六", "56");
        CHINESE_NUM_MAP.put("五十七", "57");
        CHINESE_NUM_MAP.put("五十八", "58");
        CHINESE_NUM_MAP.put("五十九", "59");
        CHINESE_NUM_MAP.put("十一", "11");
        CHINESE_NUM_MAP.put("十二", "12");
        CHINESE_NUM_MAP.put("十三", "13");
        CHINESE_NUM_MAP.put("十四", "14");
        CHINESE_NUM_MAP.put("十五", "15");
        CHINESE_NUM_MAP.put("十六", "16");
        CHINESE_NUM_MAP.put("十七", "17");
        CHINESE_NUM_MAP.put("十八", "18");
        CHINESE_NUM_MAP.put("十九", "19");
        CHINESE_NUM_MAP.put("二十", "20");
        CHINESE_NUM_MAP.put("三十", "30");
        CHINESE_NUM_MAP.put("四十", "40");
        CHINESE_NUM_MAP.put("五十", "50");
        CHINESE_NUM_MAP.put("十", "10");
        CHINESE_NUM_MAP.put("一", "1");
        CHINESE_NUM_MAP.put("壹", "1");
        CHINESE_NUM_MAP.put("二", "2");
        CHINESE_NUM_MAP.put("三", "3");
        CHINESE_NUM_MAP.put("四", "4");
        CHINESE_NUM_MAP.put("五", "5");
        CHINESE_NUM_MAP.put("六", "6");
        CHINESE_NUM_MAP.put("七", "7");
        CHINESE_NUM_MAP.put("八", "8");
        CHINESE_NUM_MAP.put("九", "9");
        CHINESE_NUM_MAP.put("零", "0");
        CHINESE_NUM_MAP.put("〇", "0");
        CHINESE_NUM_MAP.put("O", "0");
        CHINESE_NUM_MAP.put("○", "0");
        CHINESE_NUM_MAP.put("Ο", "0");


        PATTERN_LIST.add(Pattern.compile("\\d{4}-\\d{1,2}-\\d{1,2} \\d{1,2}:\\d{1,2}:\\d{1,2}"));
        PATTERN_LIST.add(Pattern.compile("\\d{4}-\\d{1,2}-\\d{1,2} \\d{1,2}:\\d{1,2}"));
        PATTERN_LIST.add(Pattern.compile("\\d{4}-\\d{1,2}-\\d{1,2} \\d{1,2}"));
        PATTERN_LIST.add(Pattern.compile("\\d{4}-\\d{1,2}-\\d{1,2}"));

        WORK_START_TIME = LocalTime.of(7, 0);
        WORK_END_TIME = LocalTime.of(18, 0);
    }


    public static String procesTime(String trialDate) {
        trialDate = trialDate.replaceAll("年", "-")
                .replaceAll("月", "-")
                .replaceAll("日", "")
                .replaceAll("上午", "09:00")  // 上午给9点
                .replaceAll("下午", "14:00"); // 下午给14:00

        return trialDate;
    }

    /**
     * 通用日期清洗
     *
     * @param data
     * @return
     */
    public static String cleanDate(String data, String timeFromat) {
        if (Strings.isNullOrEmpty(data)) {
            return "";
        }

        String dataNew = CommonUtil.full2Half(data);
        dataNew = dataNew.toUpperCase();
        String fmtData = fmtData(dataNew, timeFromat);
        if (fmtData != null) {
            return fmtData;
        }
        dataNew = dataNew.replaceAll("\\(星期([一二三四五六天日])\\)", "");
        dataNew = convertChineseDate(dataNew);
        dataNew = dataNew.replace("元月", "1月");
        dataNew = dataNew.replaceAll("[  的]", "");
        dataNew = dataNew.replace("时间:", " ");
        dataNew = dataNew.replace("日", "日 ");
        dataNew = dataNew.replace("整", "00");
        dataNew = dataNew.replaceAll("[年月]", "-");
        dataNew = dataNew.replaceAll(":$", "");
        if (((dataNew.matches(".*(下午|PM|pm).*")
                && (!dataNew.endsWith("下午"))) || dataNew.matches(".*上午.*(.).*")) && !dataNew.contains(",")) {
            dataNew = DateCleanerUDF3.evaluate(dataNew);
        }

        if (StringUtils.isEmpty(dataNew)) {
            return "";
        }
        dataNew = dataNew.replaceAll("[时点分]", ":");

        dataNew = dataNew.replaceAll("[/\\\\.]", "-");
        if (StringUtils.isNotBlank(dataNew) && dataNew.endsWith("上午")) {
            dataNew = dataNew.replaceAll("上午|AM", "09:00");
        }
        dataNew = dataNew.replaceAll("日|分|上午|中午|AM", " ");
        //处理带下午的时间
        dataNew = dealAfterNoonTime(dataNew);
        if (StringUtils.isNotBlank(dataNew) && dataNew.endsWith("下午")) {
            dataNew = dataNew.replaceAll("下午|PM", "14:00");
        }

        dataNew = dataNew.replaceAll("下午|PM", " ");
        dataNew = dataNew.replace("  ", " ");
        dataNew = dataNew.replace("  ", " ");
        dataNew = dataNew.replaceAll("[至到]", ";");
        if (dataNew.length() < 4) {
            return "";
        }
        if (dataNew.endsWith(":")) {
            dataNew = dataNew + "00";
        }
        dataNew = dataNew.split(";")[0].trim();
        Date date = convertDate(dataNew);

        if (date == null) {
            dataNew = findDateStr(dataNew);
            date = convertDate(dataNew.trim());
        }

        if (date != null) {
            return DateUtil.parseDateToStr(date, timeFromat);
        }

        return "";
    }

    public static String fmtData(String date, String timeFromat) {
        date = date.replaceAll("/|\\\\|\\.", "-");
        date = date.replaceAll(" | ", " ");
        if (date.contains("上午") || date.contains("下午") || date.contains("AM") || date.contains("PM")) {
            return null;
        }
        date = date.replace("  ", " ");
        date = date.replace("  ", " ");
        date = findDateStr(date);
        Date fmtData = convertDate(date);
        if (fmtData != null) {
            return DateUtil.parseDateToStr(fmtData, timeFromat);
        }
        return null;
    }


    /**
     * 转为标准时间
     *
     * @param str
     * @return
     */
    static Date convertDate(String str) {
        Date date = convertStr2Date(str);
        if (date != null) {
            //时间大于当前时间+5年，认为是错误数据
            if (date.getYear() > (new Date()).getYear() + 5) {
                return null;
            }
            //时间小于1990年之前的，也认为是错误数据
            if (date.getYear() + 1900 < 1980) {
                return null;
            }
        }

        return date;
    }

    public static Date convertStr2Date(String dateStr) {
        //yyyy-MM-dd HH:mm:ss
        if (isRegularRegx("\\d{4}-\\d{1,2}-\\d{1,2} \\d{1,2}:\\d{1,2}:\\d{1,2}", dateStr)) {
            return DateUtil.parseStrToDate(dateStr, DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS);
        }
        //yyyy-MM-dd HH:mm
        if (isRegularRegx("\\d{4}-\\d{1,2}-\\d{1,2} \\d{1,2}:\\d{1,2}", dateStr)) {
            return DateUtil.parseStrToDate(dateStr, DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI);
        }
        //yyyy-MM-dd
        if (isRegularRegx("\\d{4}-\\d{1,2}-\\d{1,2}", dateStr)) {
            return DateUtil.parseStrToDate(dateStr, DateUtil.DATE_FORMAT_YYYY_MM_DD);
        }
        //MM-dd-yyyy HH:mm:ss
        if (isRegularRegx("\\d{1,2}-\\d{1,2}-\\d{4} \\d{1,2}:\\d{1,2}:\\d{1,2}", dateStr)) {
            return DateUtil.parseStrToDate(dateStr, "MM-dd-yyyy HH:mm:ss");
        }
        //MM-dd-yyyy HH:mm
        if (isRegularRegx("\\d{1,2}-\\d{1,2}-\\d{4} \\d{1,2}:\\d{1,2}", dateStr)) {
            return DateUtil.parseStrToDate(dateStr, "MM-dd-yyyy HH:mm");
        }
        //MM-dd-yyyy
        if (isRegularRegx("\\d{1,2}-\\d{1,2}-\\d{4}", dateStr)) {
            return DateUtil.parseStrToDate(dateStr, "MM-dd-yyyy");
        }
        //yyyy/MM/dd HH:mm:ss
        if (isRegularRegx("\\d{4}/\\d{1,2}/\\d{1,2} \\d{1,2}:\\d{1,2}:\\d{1,2}", dateStr)) {
            return DateUtil.parseStrToDate(dateStr, "yyyy/MM/dd HH:mm:ss");
        }
        //yyyy/MM/dd HH:mm
        if (isRegularRegx("\\d{4}/\\d{1,2}/\\d{1,2} \\d{1,2}:\\d{1,2}", dateStr)) {
            return DateUtil.parseStrToDate(dateStr, "yyyy/MM/dd HH:mm");
        }

        //yyyy/MM/dd
        if (isRegularRegx("\\d{4}/\\d{1,2}/\\d{1,2}", dateStr)) {
            return DateUtil.parseStrToDate(dateStr, DateUtil.DATE_FORMAT_YYYYMMDD);
        }

        //MM/dd/yyyy HH:mm:ss
        if (isRegularRegx("\\d{1,2}/\\d{1,2}/\\d{4} \\d{1,2}:\\d{2}:\\d{2}", dateStr)) {
            return DateUtil.parseStrToDate(dateStr, "MM/dd/yyyy HH:mm:ss");
        }
        //MM/dd/yyyy HH:mm
        if (isRegularRegx("\\d{1,2}/\\d{1,2}/\\d{4} \\d{1,2}:\\d{2}", dateStr)) {
            return DateUtil.parseStrToDate(dateStr, "MM/dd/yyyy HH:mm");
        }

        //MM/dd/yyyy
        if (isRegularRegx("\\d{1,2}/\\d{1,2}/\\d{4}", dateStr)) {
            return DateUtil.parseStrToDate(dateStr, "MM/dd/yyyy");
        }

        //yyyyMMddHH:mm:ss
        if (isRegularRegx("\\d{4}\\d{1,2}\\d{1,2}\\d{1,2}:\\d{1,2}:\\d{1,2}", dateStr)) {
            return DateUtil.parseStrToDate(dateStr, "yyyyMMddHH:mm:ss");
        }
        //yyyyMMddHH:mm
        if (isRegularRegx("\\d{4}\\d{1,2}\\d{1,2}\\d{1,2}:\\d{1,2}", dateStr)) {
            return DateUtil.parseStrToDate(dateStr, "yyyyMMddHH:mm");
        }
        //yyyyMMdd
        if (isRegularRegx("\\d{4}\\d{1,2}\\d{1,2}", dateStr)) {
            return DateUtil.parseStrToDate(dateStr, "yyyyMMdd");
        }
        return null;
    }

    /**
     * 是否合法正则
     *
     * @param regex
     * @param date
     * @return
     */
    public static boolean isRegularRegx(String regex, String date) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(date);
        return matcher.matches();
    }

    /**
     * 处理带下午的数据
     *
     * @param dataNew
     * @return
     */
    static String dealAfterNoonTime(String dataNew) {
        int afterNoon = dataNew.indexOf("下午");
        if (afterNoon > 0) {
            dataNew = dealAfterTime(dataNew, afterNoon);
        } else {
            afterNoon = dataNew.indexOf("PM");
            if (afterNoon > 0) {
                dataNew = dealAfterTime(dataNew, afterNoon);
            }
        }

        return dataNew;
    }

    private static String dealAfterTime(String dataNew, int afterNoon) {
        String afterStr = dataNew.substring(afterNoon);
        int mhIdx = afterStr.indexOf(":");
        if (mhIdx > 0) {
            String afterTime = afterStr.substring(2, mhIdx);
            Integer time = tryParseInt(afterTime);
            //小于12点 需要+12转为24小时制
            if (time != null && time < 12) {
                dataNew = dataNew.replace(afterStr.substring(0, mhIdx), " " + String.valueOf(time + 12));
            }
        }
        return dataNew;
    }

    static Integer tryParseInt(String data) {
        try {
            return Integer.valueOf(data);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 找出包含时间的字符串
     *
     * @param str
     * @return
     */
    static String findDateStr(String str) {
        for (Pattern pattern : PATTERN_LIST) {
            Matcher matcher = pattern.matcher(str);
            if (matcher.find()) {
                return matcher.group();
            }
        }
        return str;
    }

    static String convertChineseDate(String date) {
        for (Map.Entry<String, String> entry : CHINESE_NUM_MAP.entrySet()) {
            date = date.replace(entry.getKey(), entry.getValue());
        }
        return date;
    }

    /***
     * @description 检查时间是否在工作时间内
     * @param date
     * @return boolean
     */
    public static boolean isWorkingTime(Date date) {
        if (date == null) {
            return false;
        }
        try {
            // 将 Date 转换为 LocalTime
            LocalTime localTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalTime();
            return localTime.isAfter(WORK_START_TIME) && localTime.isBefore(WORK_END_TIME);
        } catch (DateTimeParseException e) {
            // 处理时间解析异常
            return false;
        }
    }

    /***
     * @description 判断两个日期是否为同一天
     * @param date1
     * @param date2
     * @return boolean
     */
    public static boolean isSameDay(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return false;
        }
        try {
            LocalDate localDate1 = date1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate localDate2 = date2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            return localDate1.isEqual(localDate2);
        } catch (Exception e) {
            // 处理时间解析异常
            return false;
        }
    }
}
