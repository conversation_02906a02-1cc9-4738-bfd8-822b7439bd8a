package com.qcc.udf.tag.tagEnum;

import com.qcc.udf.tag.TagEntity;
import com.qcc.udf.tag.interfaces.TagEnumInterface;

/**
 * 劳动争议标签枚举
 */
public enum LaborDisputeEnum implements TagEnumInterface {
    LD02("劳动合同争议","LD02"),
    LD04("工伤赔偿","LD04"),
    LD05("非法雇佣童工","LD05"),
    LD06("拖欠工资","LD06"),
    LD07("社保纠纷","LD07"),
    LD08("虚构劳动关系","LD08"),
    LD09("违规收费","LD09"),
    LD99("其他劳动争议","LD99"),
    ;


    private String name;
    private String code;

    LaborDisputeEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    @Override
    public TagEntity getTagEntity() {
        return new TagEntity(this.code,this.name);
    }
}
