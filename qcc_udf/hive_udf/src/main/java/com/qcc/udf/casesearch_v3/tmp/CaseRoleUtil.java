package com.qcc.udf.casesearch_v3.tmp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Objects;
import com.google.common.base.Strings;
import com.qcc.udf.casesearch_v3.entity.input.CaseRoleEntity;
import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.output.CaseRoleSort;
import com.qcc.udf.casesearch_v3.entity.output.LawSuitV3OutputEntity;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.cpws.CommonUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

public class CaseRoleUtil {

    public static JSONObject evaluate(LawSuitV3OutputEntity detailInfo) {
        JSONObject res = new JSONObject();
        res.put("Old", new JSONArray());
        res.put("New", new JSONArray());

        JSONArray caseRoleNew = new JSONArray();
        if (detailInfo != null) {
            // 旧信息
            Set<String> keyNoSetOld = new LinkedHashSet<>();

            JSONArray caseRoleOld = JSON.parseArray(detailInfo.getCaseRole());
            if (caseRoleOld != null && !caseRoleOld.isEmpty()) {
                Iterator<Object> it = caseRoleOld.iterator();
                while (it.hasNext()) {
                    JSONObject jsonObject = (JSONObject) it.next();
                    String keyNo = jsonObject.getString("N");
                    String name = jsonObject.getString("P");

                    if (StringUtils.isNotEmpty(keyNo)) {
                        keyNoSetOld.add(keyNo);
                    }
                    if (StringUtils.isNotEmpty(name)) {
                        keyNoSetOld.add(name);
                    }

                    caseRoleNew.add(jsonObject);
                }
            }
            // 新信息
            List<InfoListEntity> infoList = detailInfo.getInfoList();
            if (infoList != null && !infoList.isEmpty()) {
                List<InfoListEntity> t2 = infoList.stream().sorted(Comparator.comparing(InfoListEntity::getLatestTimestamp)).collect(Collectors.toList());
                for (InfoListEntity item : t2) {
                    // 原告
                    List<NameAndKeyNoEntity> yuangaoArray = item.getProsecutor();
                    if (yuangaoArray != null && !yuangaoArray.isEmpty()) {
                        editItemV2(keyNoSetOld, yuangaoArray, caseRoleNew);
                    }

                    // 被告
                    List<NameAndKeyNoEntity> beigaoArray = item.getDefendant();
                    if (beigaoArray != null && !beigaoArray.isEmpty()) {
                        editItemV2(keyNoSetOld, beigaoArray, caseRoleNew);
                    }
                }
            }

            // List<CaseRoleEntity>
            List<CaseRoleEntity> p1 = JSONArray.parseArray(caseRoleNew.toString(), CaseRoleEntity.class);
//            List<InfoListEntity> p2 = JSONArray.parseArray(infoList.toString(), InfoListEntity.class);
            mergeCaseRoleAndTrialRound(p1, infoList);

            JSONArray result = new JSONArray();
            for (CaseRoleEntity item : p1) {
                JSONObject json = new JSONObject();
                json.put("P", item.getP());
                json.put("R", item.getR());
                json.put("D", item.getD());
                json.put("N", item.getN());
                json.put("O", item.getO());
                json.put("RL", item.getRoleList());

                result.add(json);
            }

            res.put("New", result);
        }

        return res;
    }

    public static void editItemV2(Set<String> keyNoSetOld, List<NameAndKeyNoEntity> array, JSONArray caseRoleNew) {
        for (NameAndKeyNoEntity sub : array) {
            String keyNo = sub.getKeyNo() == null ? "" : sub.getKeyNo();
            String name = sub.getName() == null ? "" : sub.getName();
            String role = sub.getRole() == null ? "" : sub.getRole();

            if (StringUtils.isNotEmpty(keyNo)) {
                if (!keyNoSetOld.contains(keyNo)) {
                    //新增
                    JSONObject item = new JSONObject();
                    item.put("P", name);
                    item.put("N", keyNo);
                    item.put("O", getOrgByKeyNo(keyNo, name));
                    item.put("R", role);
                    item.put("D", "");
                    caseRoleNew.add(item);

                    keyNoSetOld.add(keyNo);
                    keyNoSetOld.add(name);
                }
            } else {
                /*if (!keyNoSetOld.contains(name)){
                    //新增
                    JSONObject item = new JSONObject();
                    item.put("P", name);
                    item.put("N", keyNo);
                    item.put("O", getOrgByKeyNo(keyNo, name));
                    item.put("R", role);
                    item.put("D", "");
                    caseRoleNew.add(item);

                    keyNoSetOld.add(name);
                }*/
            }
        }
    }

    public static int getOrgByKeyNo(String keyNo, String name) {
        if (StringUtils.isBlank(keyNo)) {
            return (name.length() > 4 ? -1 : -2);
        }

        int org = 0;
        if (keyNo.startsWith("s")) {
            org = 1;
        } else if (keyNo.startsWith("h")) {
            org = 3;
        } else if (keyNo.startsWith("t")) {
            org = 5;
        } else if (keyNo.startsWith("g") || keyNo.startsWith("x") || keyNo.startsWith("w") || keyNo.startsWith("j")) {
            org = 4;
        } else if (keyNo.startsWith("y")) {
            org = 7;
        } else if (keyNo.startsWith("o")) {
            org = 8;
        } else if (keyNo.startsWith("z")) {
            org = 9;
        } else if (keyNo.startsWith("p")) {
            org = 2;
        }
        return org;
    }

    public static void mergeCaseRoleAndTrialRound(List<CaseRoleEntity> caseRoleList, List<InfoListEntity> infoList) {
        for (CaseRoleEntity caseRole : caseRoleList) {
            String keyNo = caseRole.getN();
            String name = CommonUtil.full2Half(caseRole.getP());
            List<CaseRoleSort> roleList = new ArrayList<>();
            CaseRoleSort roleSort;
            for (InfoListEntity infoListEntity : infoList) {
                List<NameAndKeyNoEntity> all = new ArrayList<>();
                all.addAll(infoListEntity.getProsecutor());
                all.addAll(infoListEntity.getDefendant());
                for (NameAndKeyNoEntity entity : all) {
                    //keyNo或者名称相同 就是同一个人
                    if ((!Strings.isNullOrEmpty(keyNo) && Objects.equal(keyNo, entity.getKeyNo())
                            || Objects.equal(name, CommonUtil.full2Half(entity.getName())))) {
                        roleSort = new CaseRoleSort();
                        String role = entity.getRole();
                        role = CommonUtil.full2Half(role).replaceAll("\\(.*\\)", "");
                        roleSort.setRole(role);
                        roleSort.setTrialRound(getShortTrialRound(infoListEntity.getTrialRound()));
                        roleSort.setTimeStamp(infoListEntity.getLatestTimestamp());
                        if (infoListEntity.getLatestTimestamp() <= 0) {
                            if (roleSort.getTrialRound().contains("一审")) {
                                roleSort.setTimeStamp(-10);
                            }
                            if (roleSort.getTrialRound().contains("二审")) {
                                roleSort.setTimeStamp(-9);
                            }
                            if (roleSort.getTrialRound().contains("再审")) {
                                roleSort.setTimeStamp(-8);
                            }
                        }
                        //TODO 判决结果
                        roleSort.setLawsuitResult(entity.getLawsuitResult());
                        roleList.add(roleSort);
                        break;
                    }
                }
            }
            //排序
            caseRole.setRoleList(roleList.stream().sorted(Comparator.comparing(CaseRoleSort::getTimeStamp)
                    .thenComparing(CaseRoleSort::getTrialRound).thenComparing(CaseRoleSort::getRole))
                    .collect(Collectors.toList()));
        }

        for (CaseRoleEntity roleEntity : caseRoleList) {
            Set<String> roundSet = new LinkedHashSet<>();
            for (CaseRoleSort caseRoleSort : roleEntity.getRoleList()) {
                roundSet.add(caseRoleSort.getTrialRound() + caseRoleSort.getRole());
            }
            String roleDesc = roundSet.stream()
                    .collect(Collectors.joining(","));
            roleEntity.setD(roleDesc);

            //案件身份描述汇总
            Map<String, CaseRoleSort> caseRoleSortMap = roleEntity.getRoleList().stream()
                    .collect(Collectors.groupingBy(data -> data.getTrialRound() + data.getRole()
                            , Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparingLong(CaseRoleSort::getTimeStamp))
                                    , Optional::get)));
            List<CaseRoleSort> roleList = caseRoleSortMap.values().stream().sorted(Comparator.comparing(CaseRoleSort::getTimeStamp)
                    .thenComparing(CaseRoleSort::getTrialRound).thenComparing(CaseRoleSort::getRole))
                    .collect(Collectors.toList());
            roleEntity.setRoleList(roleList);
        }


    }

    public static String getShortTrialRound(String trialRound) {
        if (trialRound.contains("一审")) {
            return "一审";
        }
        if (trialRound.contains("二审")) {
            return "二审";
        }
        if (trialRound.contains("管辖上诉")) {
            return "管辖上诉";
        }
        if (trialRound.contains("申请再审审查")) {
            return "申请再审审查";
        }
        if (trialRound.contains("刑事审判监督")) {
            return "审判监督";
        }
        if (trialRound.contains("管辖")) {
            return "管辖";
        }
        if (trialRound.contains("依职权再审审查")) {
            return "依职权再审审查";
        }
        if (trialRound.contains("再审")) {
            return "再审";
        }
        if (trialRound.contains("刑事复核")) {
            return "复核";
        }
        if (trialRound.contains("民事特别程序监督")) {
            return "特别程序监督";
        }
        if (trialRound.contains("抗诉再审审查")) {
            return "抗诉再审审查";
        }
        return trialRound;
    }

}
