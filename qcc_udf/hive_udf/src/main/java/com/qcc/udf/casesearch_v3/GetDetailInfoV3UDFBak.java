package com.qcc.udf.casesearch_v3;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.qcc.udf.casesearch_v3.entity.LawSuitV3Entity;
import com.qcc.udf.casesearch_v3.entity.input.CaseRoleEntity;
import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.output.*;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import com.qcc.udf.casesearch_v3.util.GroupSplitUtil;
import com.qcc.udf.court_notice.anUtils.MD5Util;
import org.apache.commons.collections.CollectionUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021年11月25日 19:20
 */
public class GetDetailInfoV3UDFBak extends UDF {
    public static String evaluate(String annoId, String annoGourp, int type, String source, List<String> sourceSxList, List<String> sourceZxList, List<String> sourceXgList,
                                  List<String> sourceCaseList, List<String> sourcePcczList, List<String> sourceZbList,
                                  List<String> sourceXjpgList, List<String> sourceGqdjList, List<String> sourceSdggList,
                                  List<String> sourceFyggList, List<String> sourceKtggList, List<String> sourceLianList,
                                  List<String> sourceHbcfList, List<String> sourceXzcfList
            , List<String> sourceSfpmList, List<String> sourceSqtjList, List<String> sourceXzcjList, List<String> sourceXdpgjgList
            ,List<String> sourceXsggList)
            throws InvocationTargetException, IllegalAccessException {

        //每天变更过多原因
        //caseRole当事人发生变化-进来的所有维度更具id排序

        List<LawSuitV3OutputEntity> outList;
        //标记删除的组，不需要处理
        if(type == 2){
            outList = Lists.newArrayList(new LawSuitV3OutputEntity());
        } else{
            //实体映射
            LawSuitV3Entity input = new LawSuitV3Entity();
            input.convert(sourceSxList, sourceZxList, sourceXgList, sourceCaseList, sourcePcczList, sourceZbList, sourceXjpgList
                    , sourceGqdjList, sourceSdggList, sourceFyggList, sourceKtggList, sourceLianList, sourceHbcfList, sourceXzcfList
                    ,sourceSfpmList,sourceSqtjList,sourceXzcjList,sourceXdpgjgList,sourceXsggList);
            //step1. 排序(减少对比数据时产生的变更)
            CommonV3Util.sort(input);
            //step2. 拆分,过滤案件
//            List<LawSuitV3Entity> caseSplitList = CommonV3Util.split_v0(input);
            List<LawSuitV3Entity> caseSplitList = GroupSplitUtil.split_v2(input);
            //step3. 组装数据
            outList = CommonV3Util.build(caseSplitList,annoGourp);

            //step4.出参简单排序
            for (LawSuitV3OutputEntity outputEntity : outList) {
                List<InfoListEntity> infoList = outputEntity.getInfoList().stream()
                        .sorted(Comparator.comparing(InfoListEntity::getAnno)).collect(Collectors.toList());
                //所有小list也需要排序
                for (InfoListEntity infoListEntity : infoList) {
                    //失信
                    infoListEntity.setSxList(infoListEntity.getSxList().stream()
                            .sorted(Comparator.comparing(SXListEntity::getId)).collect(Collectors.toList()));
                    //执行
                    infoListEntity.setZxList(infoListEntity.getZxList().stream()
                            .sorted(Comparator.comparing(ZXListEntity::getId)).collect(Collectors.toList()));
                    //限高
                    infoListEntity.setXgList(infoListEntity.getXgList().stream()
                            .sorted(Comparator.comparing(XGListEntity::getId)).collect(Collectors.toList()));

                    //裁判文书
                    infoListEntity.setCaseList(infoListEntity.getCaseList().stream()
                            .sorted(Comparator.comparing(CaseListEntity::getId)).collect(Collectors.toList()));

                    //立案
                    infoListEntity.setLianList(infoListEntity.getLianList().stream()
                            .sorted(Comparator.comparing(LianListEntity::getId)).collect(Collectors.toList()));

                    //法院公告
                    infoListEntity.setFyggList(infoListEntity.getFyggList().stream()
                            .sorted(Comparator.comparing(FyggListEntity::getId)).collect(Collectors.toList()));

                    //开庭公告
                    infoListEntity.setKtggList(infoListEntity.getKtggList().stream()
                            .sorted(Comparator.comparing(KtggListEntity::getId)).collect(Collectors.toList()));

                    //送达公告
                    infoListEntity.setSdggList(infoListEntity.getSdggList().stream()
                            .sorted(Comparator.comparing(SdggListEntity::getId)).collect(Collectors.toList()));

                    //破产重组
                    infoListEntity.setPcczList(infoListEntity.getPcczList().stream()
                            .sorted(Comparator.comparing(PcczListEntity::getId)).collect(Collectors.toList()));

                    //股权冻结
                    infoListEntity.setGqdjList(infoListEntity.getGqdjList().stream()
                            .sorted(Comparator.comparing(GqdjListEntity::getId)).collect(Collectors.toList()));

                    //询价评估
                    infoListEntity.setXjpgList(infoListEntity.getXjpgList().stream()
                            .sorted(Comparator.comparing(XjpgListEntity::getId)).collect(Collectors.toList()));
                    //终本
                    infoListEntity.setZbList(infoListEntity.getZbList().stream()
                            .sorted(Comparator.comparing(ZbListEntity::getId)).collect(Collectors.toList()));

                    //司法拍卖
                    if(CollectionUtils.isNotEmpty(infoListEntity.getSfpmList())){
                        infoListEntity.setSfpmList(infoListEntity.getSfpmList().stream()
                                .sorted(Comparator.comparing(SFPMListEntity::getId)).collect(Collectors.toList()));
                    }

                    //诉前调解
                    if(CollectionUtils.isNotEmpty(infoListEntity.getSqtjList())){
                        infoListEntity.setSqtjList(infoListEntity.getSqtjList().stream()
                                .sorted(Comparator.comparing(SQTJListEntity::getId)).collect(Collectors.toList()));
                    }
                    //限制出境
                    if(CollectionUtils.isNotEmpty(infoListEntity.getXzcjList())) {
                        infoListEntity.setXzcjList(infoListEntity.getXzcjList().stream()
                                .sorted(Comparator.comparing(XZCJListEntity::getId)).collect(Collectors.toList()));
                    }
                    //选定评估机构
                    if(CollectionUtils.isNotEmpty(infoListEntity.getXdpgjgList())) {
                        infoListEntity.setXdpgjgList(infoListEntity.getXdpgjgList().stream()
                                .sorted(Comparator.comparing(XDPGJGListEntity::getId)).collect(Collectors.toList()));
                    }
                    //悬赏公告
                    if(CollectionUtils.isNotEmpty(infoListEntity.getXsggList())) {
                        infoListEntity.setXsggList(infoListEntity.getXsggList().stream()
                                .sorted(Comparator.comparing(XSGGListEntity::getId)).collect(Collectors.toList()));
                    }
                }

                //行政处罚
                if(CollectionUtils.isNotEmpty(outputEntity.getXzcfList())){
                    outputEntity.setXzcfList(outputEntity.getXzcfList().stream()
                            .sorted(Comparator.comparing(XZCFListEntity::getId)).collect(Collectors.toList()));
                }
                //环保处罚
                if(CollectionUtils.isNotEmpty(outputEntity.getHbcfList())){
                    outputEntity.setHbcfList(outputEntity.getHbcfList().stream()
                            .sorted(Comparator.comparing(HbcfListEntity::getId)).collect(Collectors.toList()));
                }
                //商标
                if(CollectionUtils.isNotEmpty(outputEntity.getSBList())){
                    outputEntity.setSBList(outputEntity.getSBList().stream()
                            .sorted(Comparator.comparing(TrademarkListEntity::getId)).collect(Collectors.toList()));
                }
                //专利
                if(CollectionUtils.isNotEmpty(outputEntity.getZLList())){
                    outputEntity.setZLList(outputEntity.getZLList().stream()
                            .sorted(Comparator.comparing(PatentListEntity::getId)).collect(Collectors.toList()));
                }
                outputEntity.setInfoList(infoList);

                //生成系列案件标识
//                outputEntity.setGroupMark(CaseSearchUtil.buildSeriesGroupId(outputEntity));
//                outputEntity.setAnNoEnd(CaseSearchUtil.getAnNoEnd(outputEntity.getAnNoList()));

            }
        }

        //step5.生成主键
        for (LawSuitV3OutputEntity item : outList) {
            item.setGroupId(annoId);//组id
            item.setType(type);//操作类型 0-更新 1-新增  2-删除
            item.setSource(source);//来源 RT-实时计算 OT-离线任务
            if(type!=2){
                //组id+最小案号+当事人+省份
                String id = annoId
                        +item.getAnNoList().split(",")[0]
                        + JSONArray.parseArray(item.getCaseRole(), CaseRoleEntity.class)
                        .stream().map(CaseRoleEntity::getP).collect(Collectors.toSet())
                        .stream().sorted().collect(Collectors.joining(","))
                        +item.getProvince();
                item.setId(MD5Util.ecodeByMD5(id));

//                if(Strings.isNullOrEmpty(item.getGroupMark())){
//                    item.setGroupMark(item.getId());
//                }
            }
        }
        if(type!=2){
            Map<String,List<LawSuitV3OutputEntity>> mapList = outList.stream().collect(Collectors
                    .groupingBy(LawSuitV3OutputEntity::getId));
            //防止ID重复
            for (List<LawSuitV3OutputEntity> value : mapList.values()) {
                if(value.size()>1){
                    value.sort(Comparator.comparing(LawSuitV3OutputEntity::getCompanyKeywords));
                    for (int i = 0; i < value.size(); i++) {
                        if(i>0){
                            value.get(i).setId(MD5Util.ecodeByMD5(value.get(i).getId()+"_"+i));
                        }
                    }
                }
            }
            outList = outList.stream()
                    .sorted(Comparator.comparing(LawSuitV3OutputEntity::getId))
                    .collect(Collectors.toList());
        }

        //生成空案件的数据需要标记删除
        if(CollectionUtils.isEmpty(outList)){
            LawSuitV3OutputEntity item = new LawSuitV3OutputEntity();
            item.setGroupId(annoId);//组id
            item.setType(2);//操作类型 0-更新 1-新增  2-删除
            item.setSource(source);//来源 RT-实时计算 OT-离线任务
            outList.add(item);
        }

        //TODO 生成案件状态
//        GetCaseStatusLevelUDF.evaluate(outList);

        return JSON.toJSONString(outList, SerializerFeature.DisableCircularReferenceDetect);
    }


}
