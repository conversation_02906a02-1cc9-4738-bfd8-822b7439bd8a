package com.qcc.udf.CommonService;

import com.hankcs.hanlp.HanLP;
import org.apache.hadoop.hive.ql.exec.UDF;

public class SimpleChineseTransChinese extends UDF {
//    public static void main(String[] args) {
//        System.out.println(evaluate("德国大众(香港)石化有限公司",1));
//        System.out.println(evaluate("德国大众(香港)石化有限公司",2));
//    }
    /**
     * @param str
     * @param type 1:简体转繁体 2：繁体转简体
     * @return
     */
    public static String evaluate(String str, int type) {
        String result = "";
        if (type == 1) {
            result = HanLP.convertToTraditionalChinese(str);
        } else if (type == 2) {
            result = HanLP.convertToSimplifiedChinese(str);
        }
        return result;
    }
}
