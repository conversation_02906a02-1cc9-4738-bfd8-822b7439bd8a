package com.qcc.udf.casesearch_v3.util;

import com.qcc.udf.casesearch_v3.entity.input.*;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.court_notice.anUtils.MD5Util;
import com.qcc.udf.halfToFull;
import org.apache.commons.collections.CollectionUtils;
import org.apache.directory.api.util.Strings;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021年09月26日 15:49
 */
public class LawyerUtil {
    /**
     * 获取律师真实id
     *
     * @param infoList
     * @return
     */
    public static String createLawyerIds(List<InfoListEntity> infoList) {
        Set<String> lawyerIdSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(infoList)) {
            for (InfoListEntity info : infoList) {
                List<NameAndKeyNoEntity> allList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(info.getProsecutor())) {
                    allList.addAll(info.getProsecutor());
                }
                if (CollectionUtils.isNotEmpty(info.getDefendant())) {
                    allList.addAll(info.getDefendant());
                }
                for (NameAndKeyNoEntity nameKey : allList) {
                    if (CollectionUtils.isNotEmpty(nameKey.getLawFirmList())) {
                        for (CPWSLawyerFirmGroupInfo lawyer : nameKey.getLawFirmList()) {
                            if (CollectionUtils.isNotEmpty(lawyer.getLY())) {
                                for (CPWSLawyerGroupInfo lawInfo : lawyer.getLY()) {
                                    if (Strings.isNotEmpty(lawInfo.getLawyerKeyNo())) {
                                        lawyerIdSet.add(lawInfo.getLawyerKeyNo());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return lawyerIdSet.stream().sorted().collect(Collectors.joining(","));
    }

    public static String mergeLawyerIds(String oldIds, String newIds) {
        String[] idsOld = (oldIds == null ? "" : oldIds).split(",");
        String[] idsNew = (newIds == null ? "" : newIds).split(",");
        Set<String> lawyerIdSet = new HashSet<>();
        for (String id : idsOld) {
            lawyerIdSet.add(id);
        }
        for (String id : idsNew) {
            lawyerIdSet.add(id);
        }

        return lawyerIdSet.stream().sorted().collect(Collectors.joining(","));
    }

    /**
     * 从裁判文书获取所有律师信息
     *
     * @param cpwsList
     * @return
     */
    public static String createCpwsLawyerIds(List<CPWSEntity> cpwsList) {
        Set<String> lawyerIdSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(cpwsList)) {
            List<CPWSCaseRoleLawJudgeParty> lawListAll = new ArrayList<>();
            for (CPWSEntity cpwsEntity : cpwsList) {
                List<CPWSCaseRoleLawJudgeParty> lawList = cpwsEntity.getCaseRoleLawJudgePartyEntityList();
                if (CollectionUtils.isNotEmpty(lawList)) {
                    lawListAll.addAll(lawList);
                }
            }
            for (CPWSCaseRoleLawJudgeParty lawParty : lawListAll) {
                if (CollectionUtils.isNotEmpty(lawParty.getLawFirmList())) {
                    for (CPWSLawFirmInfo cpwsLawFirmInfo : lawParty.getLawFirmList()) {
                        if (CollectionUtils.isNotEmpty(cpwsLawFirmInfo.getLawyerList())){
                            for (CPWSLawyerInfo cpwsLawyerInfo : cpwsLawFirmInfo.getLawyerList()) {
                                if(Strings.isNotEmpty(cpwsLawyerInfo.getLawyer_KeyNo())){
                                    lawyerIdSet.add(cpwsLawyerInfo.getLawyer_KeyNo());
                                }
                                //保留虚拟id与盛栋兼容
                                if(Strings.isNotEmpty(cpwsLawyerInfo.getLawyer_Name()) && Strings.isNotEmpty(cpwsLawFirmInfo.getLawFirm_Name()) ){
                                    String lawyerId = MD5Util.ecodeByMD5(halfToFull
                                            .evaluate(cpwsLawFirmInfo.getLawFirm_Name() + "|" + cpwsLawyerInfo.getLawyer_Name()));
                                    lawyerIdSet.add(lawyerId);
                                }
                            }
                        }
                    }
                }
            }
        }
        return lawyerIdSet.stream().sorted().collect(Collectors.joining(","));
    }

    public static void main(String[] args) {
        System.out.println(mergeLawyerIds("",""));
    }
}
