package com.qcc.udf.cpws.wjp;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 名称相似度判断
 * 根据拼音判断 去处音调是否同音
 * 字符差异两个字以内 比如江苏省苏州市企查查和 江苏苏州企查查
 */
public class PinyinSentenceSimilarity  extends UDF {

    private static final HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();

    static {
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE); // 不带声调
    }

    // 获取字符的拼音（如果是汉字）
    private static String getPinyin(char c) {
        try {
            String[] pinyins = PinyinHelper.toHanyuPinyinStringArray(c, format);
            if (pinyins != null && pinyins.length > 0) {
                return pinyins[0]; // 返回第一个拼音（多音字取第一个）
            }
        } catch (BadHanyuPinyinOutputFormatCombination e) {
            e.printStackTrace();
        }
        return String.valueOf(c); // 非汉字返回原字符
    }

    // 判断两个字是否同音
    private static boolean isSamePinyin(char c1, char c2) {
        if (c1 == c2) return true;
        return getPinyin(c1).equals(getPinyin(c2));
    }

    // 计算相似度（允许最多maxErrors个差异）
    public static boolean evaluate(String s1, String s2, int maxErrors) {
        int len1 = s1.length();
        int len2 = s2.length();

        // 长度差异不能超过maxErrors
        if (Math.abs(len1 - len2) > maxErrors) {
            return false;
        }

        int errors = 0;
        int i = 0, j = 0;

        while (i < len1 && j < len2) {
            char c1 = s1.charAt(i);
            char c2 = s2.charAt(j);

            if (isSamePinyin(c1, c2)) {
                i++;
                j++;
            } else {
                errors++;
                if (errors > maxErrors) return false;

                // 尝试三种情况：跳过s1的字符、跳过s2的字符、或当作不同字符但计入错误
                boolean option1 = (i + 1 < len1) && isSamePinyin(s1.charAt(i + 1), c2);
                boolean option2 = (j + 1 < len2) && isSamePinyin(c1, s2.charAt(j + 1));

                if (option1 && option2) {
                    // 两种跳法都可以，选择错误较少的一种
                    int remaining1 = calculateRemainingErrors(s1, s2, i + 1, j, maxErrors - errors);
                    int remaining2 = calculateRemainingErrors(s1, s2, i, j + 1, maxErrors - errors);

                    if (remaining1 >= remaining2) {
                        i++;
                    } else {
                        j++;
                    }
                } else if (option1) {
                    i++;
                } else if (option2) {
                    j++;
                } else {
                    i++;
                    j++;
                }
            }
        }

        // 处理剩余未比较的字符（计入错误）
        errors += (len1 - i) + (len2 - j);
        return errors <= maxErrors;
    }

    // 计算剩余部分的可能错误数
    private static int calculateRemainingErrors(String s1, String s2, int i, int j, int remainingErrors) {
        int len1 = s1.length();
        int len2 = s2.length();
        int errors = 0;

        while (i < len1 && j < len2 && errors <= remainingErrors) {
            char c1 = s1.charAt(i);
            char c2 = s2.charAt(j);

            if (isSamePinyin(c1, c2)) {
                i++;
                j++;
            } else {
                errors++;
                if (i + 1 < len1 && isSamePinyin(s1.charAt(i + 1), c2)) {
                    i++;
                } else if (j + 1 < len2 && isSamePinyin(c1, s2.charAt(j + 1))) {
                    j++;
                } else {
                    i++;
                    j++;
                }
            }
        }

        errors += (len1 - i) + (len2 - j);
        return errors;
    }

    public static void main(String[] args) {
        String s1 = "北京博螯纵横网络科技有限公司";
        String s2 = "北京博鳌纵横网络科技有限公司"; // "我"和"窝"同音，"吃"和"迟"同音

        System.out.println(evaluate(s1, s2, 2)); // true
        System.out.println(evaluate(s1, s2, 1)); // false

    }
}