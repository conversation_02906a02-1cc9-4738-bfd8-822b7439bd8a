package com.qcc.udf;

/**
 * @Auther: nixb
 * @Date: 2019/11/21 10:33
 * @Description:
 */

import com.alibaba.fastjson.JSONObject;
import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.List;

@Description(name = "IsPartnerContains",
        value = "_FUNC_(String partners1, String partners2) - Returns Int Value"
)
public class IsPartnerContains extends UDF {
    public int evaluate(String partners1, String partners2) {
        int result = 0;

        try {
            if (IsNullOrWhiteSpace(partners1) || IsNullOrWhiteSpace(partners2)) {
                return result;
            }

            List<PartnerInfo> partnerList1 = JSONObject.parseArray(partners1, PartnerInfo.class);
            List<PartnerInfo> partnerList2 = JSONObject.parseArray(partners2, PartnerInfo.class);

            if (partnerList1 != null && partnerList1.size() > 0 && partnerList2 != null && partnerList2.size() > 0 && partnerList1.size() >= partnerList2.size()) {
                if (partnerList1.size() == 1 && partnerList2.size() == 1 && (partnerList1.get(0).Org == 2 || partnerList1.get(0).Org == -2) && (partnerList2.get(0).Org == 2 || partnerList2.get(0).Org == -2)) {
                    if (!IsNullOrWhiteSpace(partnerList1.get(0).getKeyNo()) && !IsNullOrWhiteSpace(partnerList2.get(0).getKeyNo()) && partnerList1.get(0).getKeyNo().equals(partnerList2.get(0).getKeyNo())) {
                        result = 1;
                    }
                } else if (partnerList2.size() > 1) {
                    boolean flag = true;

                    for (PartnerInfo item2 : partnerList2) {
                        boolean isExist = false;
                        for (PartnerInfo item1 : partnerList1) {
                            if (!IsNullOrWhiteSpace(item2.getKeyNo()) && !IsNullOrWhiteSpace(item1.getKeyNo()) && item2.getKeyNo().equals(item1.getKeyNo())) {
                                isExist = true;
                                break;
                            }
                        }

                        if (!isExist) {
                            flag = false;
                            break;
                        }
                    }

                    if (flag) {
                        result = 1;
                    }
                }
            }

        } catch (Exception e) {
            result = -1;
        }

        return result;
    }

    public static boolean IsNullOrWhiteSpace(String str) {
        if (str == null || "".equals(str)) {
            return true;
        } else {
            return false;
        }
    }

    public static String Full2Half(String input) {
        if (IsNullOrWhiteSpace(input)) {
            return "";
        }

        char c[] = input.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == '\u3000') {
                c[i] = ' ';
            } else if (c[i] > '\uFF00' && c[i] < '\uFF5F') {
                c[i] = (char) (c[i] - 65248);

            }
        }
        return new String(c).replace(" ", "");
    }

    public static class PartnerInfo {
        public String KeyNo;
        public String StockName;
        public int Org;

        public String getKeyNo() {
            return KeyNo;
        }

        public void setKeyNo(String keyNo) {
            KeyNo = keyNo;
        }

        public String getStockName() {
            return StockName;
        }

        public void setStockName(String stockName) {
            StockName = stockName;
        }

        public int getOrg() {
            return Org;
        }

        public void setOrg(int org) {
            Org = org;
        }
    }

//    public static void main(String[] args) {
//        String str1 = "[{\"KeyNo\":\"d004ad36d5a6d7fe9606908954d3572a\",\"StockName\":\"福建华闽进出口有限公司\",\"StockPercent\":\"39.84%\",\"Org\":0,\"StockRightNum\":\"34064825\",\"HasImage\":true},{\"KeyNo\":\"pd3be8d3c775e8e8f214e22ce8c9a760\",\"StockName\":\"张元启\",\"StockPercent\":\"13.25%\",\"Org\":2,\"StockRightNum\":\"11327401\",\"HasImage\":false},{\"KeyNo\":\"be9c445bbc25c448160b62598bb34653\",\"StockName\":\"福建华兴创业投资有限公司\",\"StockPercent\":\"8.79%\",\"Org\":0,\"StockRightNum\":\"7514933\",\"HasImage\":true},{\"KeyNo\":\"16cdcc75a9b19a942c50ac53c2ca3eba\",\"StockName\":\"福建华兴润明创业投资有限公司\",\"StockPercent\":\"2.34%\",\"Org\":0,\"StockRightNum\":\"2000000\",\"HasImage\":true},{\"KeyNo\":\"36525b5087a56efcf136fc5b82a25ae9\",\"StockName\":\"明溪县盛嘉创业投资企业(有限合伙)\",\"StockPercent\":\"2.05%\",\"Org\":0,\"StockRightNum\":\"1749000\",\"HasImage\":false},{\"KeyNo\":\"pe945ce76eba6c0044017c6cfbe8b93a\",\"StockName\":\"武哨红\",\"StockPercent\":\"2.03%\",\"Org\":2,\"StockRightNum\":\"1737093\",\"HasImage\":false},{\"KeyNo\":\"p07765cad3cefcdc4f1219e387e72877\",\"StockName\":\"顾建东\",\"StockPercent\":\"1.91%\",\"Org\":2,\"StockRightNum\":\"1636463\",\"HasImage\":false},{\"KeyNo\":\"524d54f141eb846b81a7f06e2452885f\",\"StockName\":\"福建省融信汇达资产管理有限公司-融信汇达-谷雨新三板投资基金\",\"StockPercent\":\"1.8%\",\"Org\":6,\"StockRightNum\":\"1538000\",\"HasImage\":false},{\"KeyNo\":\"124c43201636aab18214a64f9e42bc01\",\"StockName\":\"福建华兴新兴创业投资有限公司\",\"StockPercent\":\"1.75%\",\"Org\":0,\"StockRightNum\":\"1500000\",\"HasImage\":true},{\"KeyNo\":\"prdb4c4a8d12e4887779ed6e005f9801\",\"StockName\":\"沈琴华\",\"StockPercent\":\"1.75%\",\"Org\":2,\"StockRightNum\":\"1500000\",\"HasImage\":false}]";
//        String str2 = "[{\"Org\":2,\"StockName\":\"张元启\",\"HasImage\":false,\"StockPercent\":\"86.18%\",\"CompanyCount\":11,\"ShouldCapi\":\"3118\",\"InvestType\":null,\"KeyNo\":\"pd3be8d3c775e8e8f214e22ce8c9a760\",\"ShoudDate\":null,\"IdentifyType\":\"非公示项\",\"CapiDate\":null,\"StockType\":\"自然人股东\",\"IdentifyNo\":null,\"RealCapi\":\"3118\",\"InvestName\":null},{\"Org\":2,\"StockName\":\"周卫标\",\"HasImage\":false,\"StockPercent\":\"13.82%\",\"CompanyCount\":3,\"ShouldCapi\":\"500\",\"InvestType\":null,\"KeyNo\":\"pe292cf75a72180142b0781063291ae1\",\"ShoudDate\":null,\"IdentifyType\":\"非公示项\",\"CapiDate\":null,\"StockType\":\"自然人股东\",\"IdentifyNo\":null,\"RealCapi\":\"500\",\"InvestName\":null}]";
//        //String str1="[{\"Org\":2,\"StockName\":\"耿学静\",\"HasImage\":false,\"StockPercent\":\"70.00%\",\"CompanyCount\":1,\"ShouldCapi\":\"210\",\"InvestType\":\"货币\",\"KeyNo\":\"p7579f5ab61404e5bc287e6f98290ab8\",\"ShoudDate\":\"2020-12-31\",\"IdentifyType\":\"非公示项\",\"CapiDate\":null,\"StockType\":\"自然人股东\",\"IdentifyNo\":null,\"RealCapi\":null,\"InvestName\":null},{\"Org\":2,\"StockName\":\"耿静\",\"HasImage\":false,\"StockPercent\":\"70.00%\",\"CompanyCount\":1,\"ShouldCapi\":\"210\",\"InvestType\":\"货币\",\"KeyNo\":\"p6579f5ab61404e5bc287e6f98290ab8\",\"ShoudDate\":\"2020-12-31\",\"IdentifyType\":\"非公示项\",\"CapiDate\":null,\"StockType\":\"自然人股东\",\"IdentifyNo\":null,\"RealCapi\":null,\"InvestName\":null},{\"Org\":2,\"StockName\":\"耿2静\",\"HasImage\":false,\"StockPercent\":\"70.00%\",\"CompanyCount\":1,\"ShouldCapi\":\"210\",\"InvestType\":\"货币\",\"KeyNo\":\"p6579f5ab61404e1bc287e6f98290ab8\",\"ShoudDate\":\"2020-12-31\",\"IdentifyType\":\"非公示项\",\"CapiDate\":null,\"StockType\":\"自然人股东\",\"IdentifyNo\":null,\"RealCapi\":null,\"InvestName\":null}]";
//        IsPartnerContains info = new IsPartnerContains();
//        int result = info.evaluate(str1, str2);
//    }
}
