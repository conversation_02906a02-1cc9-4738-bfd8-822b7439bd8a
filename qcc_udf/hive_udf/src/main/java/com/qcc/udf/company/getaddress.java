package com.qcc.udf.company;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.URI;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.hive.ql.exec.UDF;

public class getaddress extends UDF{
    private JsonArray jsondata = getjson("area.json");
    private JsonArray jsondata2 = getjson("regex.json");
    private static String Error ="";
    private String evaluateinner(String s){
        try {
            if(jsondata==null){
                return "jsondata"+Error;
            }
            if(s != null && s.length()>0){
                boolean flag = false;
                String result = null;
                for(JsonElement dictcity:jsondata){
                    for(JsonElement city:dictcity.getAsJsonObject().get("title").getAsJsonArray()){
                        if(s.contains(city.getAsString())){
                            String head = s.split(city.getAsString())[0];
                            if(!(head.contains("区") || head.contains("市") || head.contains("县"))){
                                flag = true;
                                String[] tmps = s.split(city.getAsString());
                                if(tmps.length>1){
                                    s = tmps[tmps.length-1];
                                }
                            }
                        }
                        result = dictcity.getAsJsonObject().get("content").getAsString()+",";
                        if(s != null && s.length()>0){
                            boolean flag1 = false;
                            for(JsonElement countys:dictcity.getAsJsonObject().get("countys").getAsJsonArray()){
                                for(JsonElement county:countys.getAsJsonObject().get("title").getAsJsonArray()){
                                    if(!flag && !(county.getAsString().endsWith("区") || county.getAsString().endsWith("市") || county.getAsString().endsWith("县"))){
                                        continue;
                                    }
                                    if(s.contains(county.getAsString())){
                                        flag = true;
                                        flag1 = true;
                                        result += countys.getAsJsonObject().get("content").getAsString();
                                        String[] tmps = s.split(county.getAsString());
                                        if(tmps.length>1){
                                            s = tmps[tmps.length-1];
                                        }
                                        if((s.startsWith("路") || s.startsWith("街")) && !(county.getAsString().endsWith("区") || county.getAsString().endsWith("市") || county.getAsString().endsWith("县"))){
                                            s = county.getAsString() + s;
                                        }
                                    }
                                    if(flag1){
                                        break;
                                    }
                                }
                                if(flag1){
                                    break;
                                }
                            }
                        }
                        if(flag){
                            break;
                        }
                    }
                    if(flag){
                        break;
                    }
                }
                if(flag && result!=null){
                    result += GetFormatAddress(s.replace(",", ""));
                    return result;
                }else{
                    return null;
                }
            }else{
                return null;
            }
        } catch (Exception e) {
            return  e.getMessage();
        }
    }

    public String evaluate(String s,String c){
        if(c == null || c.length()==0){
            return evaluateinner(s);
        }else{
            if(s != null && s.length()>0){
                boolean flag = false;
                String result = null;
                for(JsonElement dictcity:jsondata){
                    for(JsonElement city:dictcity.getAsJsonObject().get("title").getAsJsonArray()){
                        if(city.getAsString().contains(c)){
                            flag = true;
                            result = dictcity.getAsJsonObject().get("content").getAsString()+",";
                            String[] tmps = s.split(city.getAsString());
                            if(tmps.length>1){
                                s = tmps[tmps.length-1];
                            }
                            if(s != null && s.length()>0){
                                boolean flag1 = false;
                                for(JsonElement countys:dictcity.getAsJsonObject().get("countys").getAsJsonArray()){
                                    for(JsonElement county:countys.getAsJsonObject().get("title").getAsJsonArray()){
                                        if(!flag && !(county.getAsString().endsWith("区") || county.getAsString().endsWith("市") || county.getAsString().endsWith("县"))){
                                            continue;
                                        }
                                        if(s.contains(county.getAsString())){
                                            flag = true;
                                            flag1 = true;
                                            result += countys.getAsJsonObject().get("content").getAsString();
                                            tmps = s.split(county.getAsString());
                                            if(tmps.length>1){
                                                s = tmps[tmps.length-1];
                                            }
                                            if((s.startsWith("路") || s.startsWith("街")) && !(county.getAsString().endsWith("区") || county.getAsString().endsWith("市") || county.getAsString().endsWith("县"))){
                                                s = county.getAsString() + s;
                                            }
                                        }
                                        if(flag1){
                                            break;
                                        }
                                    }
                                    if(flag1){
                                        break;
                                    }
                                }
                            }
                        }
                        if(flag){
                            break;
                        }
                    }
                    if(flag){
                        break;
                    }
                }
                if(flag && result!=null){
                    result += GetFormatAddress(s.replace(",", ""));
                    return result;
                }else{
                    return null;
                }
            }else{
                return null;
            }
        }
    }

    private static JsonArray getjson(String s){
        JsonArray result=null;
        BufferedReader infile;
        try {
//            infile = new BufferedReader(new InputStreamReader(new FileInputStream("F://"+s)));

            String url = "hdfs:///user/commondata/baseclean/udf_getaddress/" + s;
            Configuration conf = new Configuration();
            FileSystem fs = FileSystem.get(new URI(url), conf);
            infile = new BufferedReader(new InputStreamReader(fs.open(new Path(url))));

            String line = null;
            StringBuilder build = new StringBuilder();
            while((line=infile.readLine())!=null){
                build.append(line.replaceAll("\t|\r|\n| ", ""));
            }
            Gson gson = new GsonBuilder().create();
            result = gson.fromJson(build.toString(), JsonArray.class);
            infile.close();
        } catch (Exception e) {
            Error = e.getMessage();
        }
        return result;
    }

    private String GetFormatAddress(String address) {
        String Result = "";
        int index = 0;
        for (JsonElement item : jsondata2) {
            index++;
            String reg = item.getAsJsonObject().get("reg").getAsString();
            String notreg = item.getAsJsonObject().get("notreg").getAsString();
            JsonElement arrarjson = item.getAsJsonObject().get("replacereg");
            JsonArray array = arrarjson == null ? null : arrarjson.getAsJsonArray();
            String regstr = GetFormatAddressInner(reg, address);
            if ("".equals(notreg) || !Pattern.compile(notreg).matcher(regstr).matches()) {
                address = address.replace(regstr, "");
                if (array != null && !array.isJsonNull()) {
                    for (JsonElement repitem : array) {
                        String repstr = repitem.getAsJsonObject().get("A").getAsString();
                        String resstr = repitem.getAsJsonObject().get("B").getAsString();
                        regstr = regstr.replaceAll(repstr, resstr);
                    }
                }
                Result += "," + regstr;
                if (regstr == "" && jsondata2.size() == index) {
                    Result += address;
                    address = "";
                }
            }
            else
            {
                Result += ",";
            }
        }
        Result += "," + address;
        return Result;
    }

    private String GetFormatAddressInner(String regex, String address) {
        // 创建 Pattern 对象
        Pattern r = Pattern.compile(regex);
        Matcher m = r.matcher(address);
        if (m.find()) {
            return m.group();
        }
        return "";
    }

//    public static void main(String[] args) {
//        getaddress c = new getaddress();
//        String a = c.evaluate("北京市朝阳区南郎家园18号楼102室", "北京市");
//        //System.out.println( c.GetFormatAddress("西二旗中路33号院6号楼6层006号"));
//        System.out.println(a);
//    }
}
