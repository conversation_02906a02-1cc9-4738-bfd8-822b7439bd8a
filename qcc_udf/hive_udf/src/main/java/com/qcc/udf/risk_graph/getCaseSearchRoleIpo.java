package com.qcc.udf.risk_graph;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.court_notice.anUtils.MD5Util;
import com.qcc.udf.court_notice.anUtils.Util;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Auther: liulh
 * @Date: 2020/6/2 20:33
 * @Description:
 */
public class getCaseSearchRoleIpo extends UDF {
    public static String  evaluate(String id,String type, String roleArray, String keywords) {
        JSONArray result = new JSONArray();

        if (StringUtils.isEmpty(roleArray)){
            return "";
        }

        Set<JSONObject> roleSetP = new LinkedHashSet<>();
        Set<JSONObject> roleSetD = new LinkedHashSet<>();
        JSONArray caseRoleJsonArray = JSONArray.parseArray(roleArray);
        Iterator<Object> it = caseRoleJsonArray.iterator();
        while(it.hasNext()){
            JSONObject jsonObject = (JSONObject)it.next();
            if (StringUtils.isNotEmpty(jsonObject.getString("P"))){
                String role = getCaseRoleCode(Util.full2Half(jsonObject.getString("R")));
                if (role.contains("P")){
                    roleSetP.add(jsonObject);
                }
                if (role.contains("D")){
                    roleSetD.add(jsonObject);
                }
            }
        }

        if (roleSetP.size() > 0 && roleSetD.size() > 0){
            for (JSONObject str : roleSetP){
                for (JSONObject sub : roleSetD){
                    String strKey = "";
                    if (StringUtils.isNotEmpty(keywords) && StringUtils.isNotEmpty(str.getString("N"))){
                        if (keywords.contains(str.getString("N"))){
                            strKey = str.getString("N");
                        }
                    }
                    String subKey = "";
                    if (StringUtils.isNotEmpty(keywords) && StringUtils.isNotEmpty(sub.getString("N"))){
                        if (keywords.contains(sub.getString("N"))){
                            subKey = sub.getString("N");
                        }
                    }

                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("Id", id);
                    jsonObject.put("Typ", type.contains("民事") ? "ms" : "xz");
                    jsonObject.put("Pname", str.getString("P"));
                    jsonObject.put("PkeyNo", strKey);
                    String pid = jsonObject.getString("PkeyNo");
                    if (StringUtils.isEmpty(pid)){
                        if (jsonObject.getString("Pname").length() < 6){
                            pid = "rp".concat(MD5Util.ecodeByMD5(Util.full2Half(id.concat(jsonObject.getString("Pname")))).substring(2));
                        }else {
                            pid = "rp".concat(MD5Util.ecodeByMD5(Util.full2Half(id.concat(jsonObject.getString("Pname")))).substring(2));
                        }
                    }
                    jsonObject.put("PkeyNoId", pid);
                    jsonObject.put("Dname", sub.getString("P"));
                    jsonObject.put("DkeyNo", subKey);
                    String did = jsonObject.getString("DkeyNo");
                    if (StringUtils.isEmpty(did)){
                        if (jsonObject.getString("Dname").length() < 6){
                            did = "rp".concat(MD5Util.ecodeByMD5(Util.full2Half(id.concat(jsonObject.getString("Dname")))).substring(2));
                        }else {
                            did = "rp".concat(MD5Util.ecodeByMD5(Util.full2Half(id.concat(jsonObject.getString("Dname")))).substring(2));
                        }
                    }
                    jsonObject.put("DkeyNoId", did);

                    result.add(jsonObject);
                }
            }
        }

        return result.isEmpty() || result.size() == 0 ? "" : result.toString();
    }

    public static String getCaseRoleCode(String caseRole){
        String result = "";
        if (StringUtils.isEmpty(caseRole)){
            return "";
        }

        Pattern p1 = Pattern.compile("(被执行人)|(被告)|(被申请人)|(被申请执行人)|(原审被告)|(被上诉人\\(原审被告\\))|(上诉人\\(原审被告\\))|(被告\\(反诉原告\\))|(被告人)|(上诉人\\(一审被告\\))|" +
                "(被上诉人\\(一审被告\\))|(被上诉人)|(上诉人\\(原审被告反诉原告\\))|(被告二)|(被告一)|(原告\\(被告\\))|(被申请人\\(一审被告二审被上诉人\\))|(被申请人\\(原审被告\\))|(再审申请人\\(一审被告二审上诉人\\))|" +
                "(再审申请人\\(原审被告\\))|(被申请人\\(仲裁被申请人\\))|(被申请人\\(原被执行人\\))|(再审被申请人)|(上诉人\\(原审被告原审原告\\))");
        Matcher m1 = p1.matcher(caseRole);
        if (m1.matches()) {
            result = "D";
        }

        Pattern p2 = Pattern.compile("(申请执行人)|(原告)|(申请人)|(被上诉人\\(原审原告\\))|(复议申请人)|(上诉人\\(原审原告\\))|(原告\\(反诉被告\\))|(上诉人)|(被上诉人\\(一审原告\\))|(上诉人\\(一审原告\\))|(被上诉人\\(原审原告反诉被告\\))|" +
                "(原审原告)|(再审申请人)|(被告\\(原告\\))|(被申请人\\(原审原告\\))|(附带民事诉讼原告人)|(复议申请人\\(原申请执行人\\))|(再审申请人\\(一审原告二审上诉人\\))|(再审申请人\\(原审原告\\))|(申请再审人\\(一审原告二审上诉人\\))|" +
                "(二审上诉人)|(原告人)|(附带民事诉讼原告)|(上诉人\\(原审原告原审被告\\))|(起诉人)|(申请人\\(仲裁申请人\\))|(赔偿请求人)");
        Matcher m2 = p2.matcher(caseRole);
        if (m2.matches()) {
            result = "P";
        }

        return result;
    }

    public static void main(String[] args) {
        System.out.println(evaluate("id1", "民事案件","[{\"D\":\"一审原告\",\"N\":\"e922107a0ffcb04e3386930ae4f6dff2\",\"O\":0,\"P\":\"山东双木林电器有限公司\",\"R\":\"原告\",\"RL\":[{\"R\":\"原告\",\"T\":\"一审\"}]},{\"D\":\"一审被告\",\"N\":\"df7ea3a61f721d6eabdfff8523c2b0f3\",\"O\":0,\"P\":\"中国建筑第八工程局有限公司\",\"R\":\"被告\",\"RL\":[{\"R\":\"被告\",\"T\":\"一审\"}]},{\"D\":\"一审被告\",\"N\":\"a0fac8af143ecbd6580296169ef6e1d6\",\"O\":0,\"P\":\"中建八局第一建设有限公司\",\"R\":\"被告\",\"RL\":[{\"R\":\"被告\",\"T\":\"一审\"}]},{\"D\":\"一审被告\",\"N\":\"e61ea62bae4d97287027ec24dc33e9f9\",\"O\":0,\"P\":\"济南万达城建设有限公司\",\"R\":\"被告\",\"RL\":[{\"R\":\"被告\",\"T\":\"一审\"}]},{\"D\":\"一审被告\",\"N\":\"1439908d73485afcee9d7040a16a31bb\",\"O\":0,\"P\":\"济南汇创装饰工程有限公司\",\"R\":\"被告\",\"RL\":[{\"R\":\"被告\",\"T\":\"一审\"}]}]",
                "1439908d73485afcee9d7040a16a31bb,a0fac8af143ecbd6580296169ef6e1d6,df7ea3a61f721d6eabdfff8523c2b0f3,e61ea62bae4d97287027ec24dc33e9f9,e922107a0ffcb04e3386930ae4f6dff2,中国建筑第八工程局有限公司,中建八局第一建设有限公司,山东双木林电器有限公司,济南万达城建设有限公司,济南汇创装饰工程有限公司"));
        //System.out.println("123".substring(2));
    }
}
