package com.qcc.udf.lawer;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * <AUTHOR>
 * @date ：Created in 2021/12/08 14:14
 * @description ：
 */
public class GetAddressInfo  extends UDF {

    private static final String PROVINCE = "省";
    private static final String CITY = "市";

    /**
     * Desc:根据关键词获取省市区信息
     * @param address 地址
     * @param keyWord 关键词 province-省份名称 code-省份code city-城市名称 area-地区
     *
     * */
    public static String evaluate(String address, String keyWord) {
        String result = "";

        String province = "";
        String city = "";
        String area = "";

        if (StringUtils.isNotEmpty(address)) {

            if (address.contains(PROVINCE)) {
                String[] str1 = address.split(PROVINCE);
                province = str1[0];
                String cityAndArea = address.substring(address.indexOf(PROVINCE) + 1);
                if (StringUtils.isNotEmpty(cityAndArea)) {
                    String[] str2 = cityAndArea.split(CITY);
                    city = str2[0] + CITY;
                    area= cityAndArea.replace(city, "");
                }
            } else {
                String[] str1 = address.split(CITY);
                province = str1[0];
                String cityAndArea = address.substring(address.indexOf(CITY) + 1);
                if (StringUtils.isNotEmpty(cityAndArea)) {
                    String[] str2 = cityAndArea.split(CITY);
                    city = str2[0] + CITY;
                    area= cityAndArea.replace(city, "");
                }
            }
        }

        switch (keyWord) {
            case "province":
                result = province;
                break;
            case "code":
                result = getProvinceCode(province);
                break;
            case "city":
                result = city;
                break;
            case "area":
                result = area;
                break;
            default:
                result = province;
                break;
        }
        return result;
    }

    public static void main(String[] args) {
        String message = "江苏省泰州市姜堰区";
        System.out.println(evaluate(message, "province"));
        System.out.println(evaluate(message, "code"));
        System.out.println(evaluate(message, "city"));
        System.out.println(evaluate(message, "area"));

    }

    private static String getProvinceCode(String param) {
        String result = "";
        if (StringUtils.isEmpty(param)) {
            return result;
        }
        if (param.contains("河北")) {
            result = "HB";
        } else if (param.contains("山西")) {
            result = "SX";
        } else if (param.contains("辽宁")) {
            result = "LN";
        } else if (param.contains("吉林")) {
            result = "JL";
        } else if (param.contains("黑龙江")) {
            result = "HLJ";
        } else if (param.contains("江苏")) {
            result = "JS";
        } else if (param.contains("浙江")) {
            result = "ZJ";
        } else if (param.contains("安徽")) {
            result = "AH";
        } else if (param.contains("福建")) {
            result = "FJ";
        } else if (param.contains("江西")) {
            result = "JX";
        } else if (param.contains("山东")) {
            result = "SD";
        } else if (param.contains("台湾")) {
            result = "TW";
        } else if (param.contains("河南")) {
            result = "HEN";
        } else if (param.contains("湖北")) {
            result = "HUB";
        } else if (param.contains("湖南")) {
            result = "HUN";
        } else if (param.contains("广东")) {
            result = "GD";
        } else if (param.contains("海南")) {
            result = "HAIN";
        } else if (param.contains("四川")) {
            result = "SC";
        } else if (param.contains("贵州")) {
            result = "GZ";
        } else if (param.contains("云南")) {
            result = "YN";
        } else if (param.contains("陕西")) {
            result = "SAX";
        } else if (param.contains("甘肃")) {
            result = "GS";
        } else if (param.contains("青海")) {
            result = "QH";
        } else if (param.contains("北京")) {
            result = "BJ";
        } else if (param.contains("天津")) {
            result = "TJ";
        } else if (param.contains("上海")) {
            result = "SH";
        } else if (param.contains("重庆")) {
            result = "CQ";
        } else if (param.contains("内蒙古")) {
            result = "NMG";
        } else if (param.contains("广西")) {
            result = "GX";
        } else if (param.contains("宁夏")) {
            result = "NX";
        } else if (param.contains("西藏")) {
            result = "XZ";
        } else if (param.contains("新疆")) {
            result = "XJ";
        } else if (param.contains("香港")) {
            result = "HK";
        } else if (param.contains("澳门")) {
            result = "AM";
        }
        return result;
    }
}
