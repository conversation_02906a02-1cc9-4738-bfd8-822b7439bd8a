package com.qcc.udf.risk_analysis;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

public class GetRoleNameFromRoleTag extends UDF {

    public String evaluate(String roleTag) {
        String result = "";
        if (StringUtils.isNotEmpty(roleTag)) {
            if (roleTag.equals("11")){
                result= "原告";
            } else if (roleTag.equals("12")){
                result= "申请执行人";
            } else if (roleTag.equals("13")){
                result= "上诉人";
            } else if (roleTag.equals("14")){
                result= "申请人";
            } else if (roleTag.equals("21")){
                result= "被告";
            } else if (roleTag.equals("22")){
                result= "被执行人";
            } else if (roleTag.equals("23")){
                result= "被上诉人";
            } else if (roleTag.equals("24")){
                result= "被申请人";
            }
        }
        return result;
    }

    public static void main(String[] args) {
        GetRoleNameFromRoleTag aa = new GetRoleNameFromRoleTag();
        System.out.println(aa.evaluate("12"));
    }
}
