package com.qcc.udf.kzz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 分析股东股权变化
 * <AUTHOR>
 * @date 2022/4/22
 */
public class GetPartnerChange extends UDF {
    // 百分比正则


    /**
     * 0-未知,1-上升,2-降低,3-退出
     * @param content
     * @return
     */
    public static String evaluate(String content) {
        Pattern pattern1 =Pattern.compile("\\d+\\.?\\d*%\\(((\\+|\\-)(\\d+\\.?\\d*)%)\\)");
        Pattern pattern6 =Pattern.compile("\\d+\\.?\\d*\\(((\\+|\\-)(\\d+\\.?\\d*)%)\\)");
        Pattern pattern7 =Pattern.compile("\\d+\\.?\\d*%\\(((\\+|\\-)(\\d+\\.?\\d*))\\)");
        Pattern pattern8 =Pattern.compile("\\d+\\.?\\d*\\(((\\+|\\-)(\\d+\\.?\\d*))\\)(( )?</em>( )?)?%");
        Pattern pattern2 =Pattern.compile("百分比: (\\d+\\.?\\d*)%?(<em>)?【新增】");
        Pattern pattern4 =Pattern.compile("出资比例:(\\d+\\.?\\d*)%?(<em>)?【新增】");
        Pattern pattern5 =Pattern.compile("(\\d+\\.?\\d*)%【新增】");
        AnalyseResult result = new AnalyseResult();
        result.setFlag("0");
        result.setPercent("");
        try{
            if(StringUtils.isNotBlank(content)){
                ChangeDetail changeDetail = JSON.parseObject(content,ChangeDetail.class);
                if(null!=changeDetail){
                    if(StringUtils.isNotBlank(changeDetail.getPercentDiff())){

                        String percent = changeDetail.getPercentDiff().replaceAll("\\+|\\-|%","");
                        if(changeDetail.getPercentDiff().contains("-")){
                            result.setFlag("2");
                        }else{
                            result.setFlag("1");
                        }
                        result.setPercent(percent);
                    }else{
                        String percent = "";
                        if(changeDetail.getContent().contains("【新增】")){
                            //新增条件下,提取百分比
                            Matcher matcher2 = pattern2.matcher(changeDetail.getContent());
                            if(matcher2.find()){
                                percent = matcher2.group(1);
                            }
                            Matcher matcher4 = pattern4.matcher(changeDetail.getContent());
                            if(matcher4.find()){
                                percent = matcher4.group(1);
                            }
                            Matcher matcher5 = pattern5.matcher(changeDetail.getContent());
                            if(matcher5.find()){
                                percent = matcher5.group(1);
                            }
                            if(StringUtils.isNotBlank(percent)){
                                result.setFlag("1");
                            }
                        }else if(changeDetail.getContent().contains("退出)")){
                            result.setFlag("3");
                        }else{
                            //没有明确新增/退出情况下,提取百分比
                            Matcher matcher1 =  pattern1.matcher(changeDetail.getContent());

                            String flag ="";
                            if(matcher1.find()){
                                flag = matcher1.group(2);
                                percent = matcher1.group(3);
                            }
                            Matcher matcher6 =  pattern6.matcher(changeDetail.getContent());
                            if(matcher6.find()){
                                flag = matcher6.group(2);
                                percent = matcher6.group(3);
                            }
                            Matcher matcher7 =  pattern7.matcher(changeDetail.getContent());
                            if(matcher7.find()){
                                flag = matcher7.group(2);
                                percent = matcher7.group(3);
                            }
                            Matcher matcher8 =  pattern8.matcher(changeDetail.getContent());
                            if(matcher8.find()){
                                flag = matcher8.group(2);
                                percent = matcher8.group(3);
                            }

                            if(StringUtils.isNotBlank(flag) && flag.contains("-")){
                                result.setFlag("2");
                            }else if(StringUtils.isNotBlank(flag)){
                                result.setFlag("1");
                            }
                        }
                        result.setPercent(percent);
                    }
                }
            }


        }catch (Exception e){

        }
        return JSON.toJSONString(result);
    }

//    public static void main(String[] args) {
////        File file =new File("C:\\Users\\<USER>\\Desktop\\股比测试.txt");
////        List<String> list= FileUtils.readLines(file);
//        String content ="{\"KeyNo\":\"p395e427251bc95bea51e71cf539be0c\",\"Org\":2,\"Content\":\"名称:许必银,出资额:500,出资比例:<em>6.816632(-0.009%)<\\/em>\",\"Name\":\"许必银\"}";
////        for (String content : list) {
//            String percent = evaluate(content);
//            System.out.println(percent+"-"+content);
////        }
//
//    }

    private static class ChangeDetail{
        @JSONField(name = "Content")
        private String content;
        @JSONField(name = "PercentDiff")
        private String percentDiff;

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getPercentDiff() {
            return percentDiff;
        }

        public void setPercentDiff(String percentDiff) {
            this.percentDiff = percentDiff;
        }
    }

    private static class AnalyseResult{
        private String flag;
        private String percent;

        public String getFlag() {
            return flag;
        }

        public void setFlag(String flag) {
            this.flag = flag;
        }

        public String getPercent() {
            return percent;
        }

        public void setPercent(String percent) {
            this.percent = percent;
        }
    }
}
