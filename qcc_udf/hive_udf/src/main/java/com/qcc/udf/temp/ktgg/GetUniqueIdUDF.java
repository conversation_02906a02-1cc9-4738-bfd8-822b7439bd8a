package com.qcc.udf.temp.ktgg;

import com.alibaba.fastjson.JSON;
import com.qcc.udf.casesearch_v3.role.NameKeyNoEntity;
import com.qcc.udf.temp.MD5Util;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 从文本中统计案号
 *
 * @Auther: wuql
 * @Date: 2020/11/17 10:00
 * @Description:
 */
public class GetUniqueIdUDF extends UDF {
    public static final String NULL_ARRAY_STRING = "[]";

    public static String evaluate(String id, String caseNo, String sessionDate, String courtCode, String keynoArray) {
        //唯一uniqueId
        String uniqueIdStr = (caseNo == null ? "" : caseNo) + (sessionDate == null ? "" : sessionDate) + (courtCode == null ? "" : courtCode);
        if (StringUtils.isBlank(uniqueIdStr)) {
            uniqueIdStr = id;
        } else if (StringUtils.isBlank(caseNo)) {
            String partys = getNameKeyNoEntitys(keynoArray)
                    .stream()
                    .filter(e -> e != null)
                    .map(NameKeyNoEntity::getName)
                    .sorted().collect(Collectors.joining(","));
            uniqueIdStr = uniqueIdStr + partys;
        }

        return MD5Util.encode(uniqueIdStr);
    }

    public static List<NameKeyNoEntity> getNameKeyNoEntitys(String nameAndKeyNo) {
        List<NameKeyNoEntity> nameAndKeyNoList = new ArrayList<>();
        if (StringUtils.isNotEmpty(nameAndKeyNo) && !NULL_ARRAY_STRING.equals(nameAndKeyNo)) {
            nameAndKeyNoList = JSON.parseArray(nameAndKeyNo, NameKeyNoEntity.class);
        }
        return nameAndKeyNoList;
    }


    public static void main(String[] args) {
        String id = "9deaf7464b324e2e71146e1b026e9c9c";
        String caseNo = "（2020）闽0102民初7246号";
        String sessionDate = "20200806";
        String courtCode = "350102";
        String keynoArray = "[{\"J\":\"6\",\"KeyNo\":\"\",\"Name\":\"林位财\",\"Org\":-1,\"Role\":\"原告\",\"RoleTag\":0,\"RoleType\":11,\"ShowName\":\"林**\",\"Source\":1,\"T\":\"7\"},{\"J\":\"22\",\"KeyNo\":\"e9c29e8806f1f0e1fa18d2aa9b090a18\",\"Name\":\"福州市完圣美健身服务有限公司\",\"Org\":0,\"Role\":\"被告\",\"RoleTag\":1,\"RoleType\":21,\"ShowName\":\"福州市完圣美健身服务有限公司\",\"Source\":1,\"T\":\"2\"}]";
        System.out.println(evaluate(id, caseNo, sessionDate, courtCode, keynoArray));
    }
}
