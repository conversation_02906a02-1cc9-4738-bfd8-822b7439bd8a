package com.qcc.udf.casesearch_v3.entity.output;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class XZCJListEntity extends BaseCaseOutEntity{
    @JSONField(name = "Id")
    private String id = "";
    @JSONField(name = "IsValid")
    private Integer isValid = 0;
    @JSONField(name = "PublishDate")
    private Long publishDate = 0L;

    /**
     *立案时间
     */
    @JSONField(name = "RegisterDate")
    private long registerdate;
    /**
     *执行标的金额
     */
    @JSONField(name = "ExecutedAmount")
    private String executedamount;

    /**
     *限制出境对象json
     */
    @JSONField(name = "PersLimitedList")
    private List<NameAndKeyNoEntity> persLimitedList;
    /**
     *被执行人json
     */
    @JSONField(name = "ExecutedList")
    private List<NameAndKeyNoEntity> executedList;
    /**
     *申请执行人json
     */
    @JSONField(name = "ApplyList")
    private List<NameAndKeyNoEntity> applyList;
}
