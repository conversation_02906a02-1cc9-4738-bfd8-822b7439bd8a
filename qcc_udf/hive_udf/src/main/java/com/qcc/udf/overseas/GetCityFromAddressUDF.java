package com.qcc.udf.overseas;


import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 业务UDF（海外企业）从公司地址中获取城市信息
 * ---------------------------------------------------------------------------------------------------------
 * create temporary function getCityFromAddress as 'com.qcc.udf.overseas。GetCityFromAddressUDF' using jar 'hdfs:///data/hive/udf/qcc_udf.jar';
 * ---------------------------------------------------------------------------------------------------------
 * select getCityFromAddress ('118 PALL MALL, ST JAMES, LONDON, SW1Y 5ED', '032');
 * 结果: London SW
 */
public class GetCityFromAddressUDF extends UDF {
    private final static Map<String, String> englandCityMap;

    static {
        englandCityMap = new HashMap<>();
        try {
            InputStream is = GetCityFromAddressUDF.class.getResourceAsStream("/england-city-map.csv");
            BufferedReader br = new BufferedReader(new InputStreamReader(is));
            String line;
            while ((line = br.readLine()) != null) {
                String[] splits = line.split(" ", 2);
                String abbr = splits[0].trim().toUpperCase();
                String city = splits[1].trim().toUpperCase();
                englandCityMap.put(abbr, city);
            }
            br.close();
            is.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }


    public String evaluate(String input, String countryCode) {
        try {
            if (StringUtils.isNotBlank(input) && StringUtils.isNotBlank(countryCode)) {
                switch (countryCode) {
                    case  "032":
                        return extractEnglandCity(input);

                    default:
                        return "";
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return "";
    }

    /**
     * 提取英国城市名
     */
    private static String extractEnglandCity(String input) throws Exception {
        if (StringUtils.isNotBlank(input)) {
            String[] splits = input.split(", ");
            if (splits != null && splits.length > 0) {
                Matcher matcher = Pattern.compile("([A-Za-z]{1,2}).*").matcher(splits[splits.length - 1]);
                if (matcher != null && matcher.matches() && matcher.groupCount() == 1) {
                    String city = englandCityMap.get(matcher.group(1).toUpperCase());
                    return StringUtils.isNotBlank(city) ? city : "";
                }
            }
        }
        return "";
    }
}
