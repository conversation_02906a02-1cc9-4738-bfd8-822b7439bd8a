package com.qcc.udf.casesearch_v3.test;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.qcc.udf.casesearch_v3.entity.LawSuitV3Entity;
import com.qcc.udf.casesearch_v3.entity.input.CaseRoleEntity;
import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.output.CaseListEntity;
import com.qcc.udf.casesearch_v3.entity.output.LawSuitV3OutputEntity;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import org.apache.commons.collections.CollectionUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021年04月08日 14:12
 */
public class SFAJCaseRoleTestUDF  extends UDF {

    public static String evaluate(String detail,String json)  {
        if(Strings.isNullOrEmpty(detail)){
            if(Strings.isNullOrEmpty(json)){
                return "";
            }
            try{
                List<CaseRoleEntity> caseRoleList = JSON.parseArray(json,CaseRoleEntity.class);
                Set<String> nameSet = new HashSet<>();
                if(CollectionUtils.isNotEmpty(caseRoleList)){
                    for (CaseRoleEntity caseRoleInfo : caseRoleList) {
                        String name = CommonV3Util.getString(caseRoleInfo.getP());
                        name = CommonV3Util.full2Half(name);
                        if(name.endsWith("某")){
                            return "";
                        }
                        if(name.contains("某甲")||name.contains("某乙")||name.contains("某丙")||name.contains("某丁")) {
                            return "";
                        }
                        nameSet.add(name);
                    }

                    return nameSet.stream().sorted().collect(Collectors.joining(","));
                }
            }catch (Exception e){

            }
        }else{
            try{
                Set<String> caseIdSet = new HashSet<>();
                LawSuitV3OutputEntity entity = JSON.parseObject(detail,LawSuitV3OutputEntity.class);
                if(entity != null && entity.getInfoList() != null){
                    for (InfoListEntity item : entity.getInfoList()) {
                        if(item.getCaseList() != null){
                            for (CaseListEntity cpws : item.getCaseList()) {
                                caseIdSet.add(cpws.getId());
                            }
                        }
                    }
                }
                return caseIdSet.stream().sorted().collect(Collectors.joining(","));
            }catch (Exception e){

            }
            return "";
        }


        return "";

    }

    public static void main(String[] args) {
        String detail = "{\"AmtInfo\":{\"094f64554e3b296afc2ee52f02e2a384\":{\"Amt\":\"889346.00\",\"IsValid\":\"1\",\"Type\":\"案件金额\"},\"9804c4f57fb81337d29045869a77458a\":{\"Amt\":\"889346.00\",\"IsValid\":\"1\",\"Type\":\"案件金额\"}},\"AnNoList\":\"（2018）沪72民初1196号\",\"AnnoCnt\":1,\"CaseCnt\":1,\"CaseName\":\"上海星明国际物流有限公司与上海乾昊物流有限公司合同纠纷的案件\",\"CaseReason\":\"合同纠纷\",\"CaseRole\":\"[{\\\"D\\\":\\\"一审原告\\\",\\\"N\\\":\\\"094f64554e3b296afc2ee52f02e2a384\\\",\\\"O\\\":0,\\\"P\\\":\\\"上海星明国际物流有限公司\\\",\\\"R\\\":\\\"原告\\\"},{\\\"D\\\":\\\"一审被告\\\",\\\"N\\\":\\\"9804c4f57fb81337d29045869a77458a\\\",\\\"O\\\":0,\\\"P\\\":\\\"上海乾昊物流有限公司\\\",\\\"R\\\":\\\"被告\\\"}]\",\"CaseType\":\"民事案件\",\"CfdfCnt\":0,\"CfgsCnt\":0,\"CfxyCnt\":0,\"CompanyKeywords\":\"094f64554e3b296afc2ee52f02e2a384,9804c4f57fb81337d29045869a77458a,上海乾昊物流有限公司,上海富侨物流有限公司,上海星明国际物流有限公司\",\"CourtList\":\"上海海事法院\",\"EarliestDate\":1529560800,\"EarliestDateType\":\"民事一审|开庭时间\",\"FyggCnt\":0,\"GqdjCnt\":0,\"GroupId\":\"39339e48b3e9e6133e1b6d09b495eb07\",\"HbcfCnt\":0,\"Id\":\"057cee9882f30a2f8a14f31136dad0d9\",\"InfoList\":[{\"AnNo\":\"（2018）沪72民初1196号\",\"CaseList\":[{\"Amt\":\"889346.00\",\"CaseType\":\"民事判决书\",\"DocType\":\"判决日期\",\"Id\":\"9bcf29a2d34804f5903bd0c9155ad8db0\",\"IsValid\":1,\"JudgeDate\":1539273600,\"Result\":\"被告上海乾昊物流有限公司应于本判决生效之日起十日内向原告上海星明国际物流有限公司支付代理费人民币889346元及该款自2017年10月1日起至本判决生效之日止按中国人民银行同期贷款利率计算的利息损失。\",\"ResultType\":\"判决结果\",\"ShieldCaseFlag\":0}],\"CaseReason\":\"合同纠纷\",\"CaseType\":\"民事案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"上海海事法院\",\"Defendant\":[{\"KeyNo\":\"9804c4f57fb81337d29045869a77458a\",\"Name\":\"上海乾昊物流有限公司\",\"Org\":0,\"Role\":\"被告\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[{\"ExecuteUnite\":\"第一法庭\",\"Id\":\"3b188c36721f499fb76054ffe55cdea15\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"094f64554e3b296afc2ee52f02e2a384\",\"Name\":\"上海星明国际物流有限公司\",\"Org\":0},{\"KeyNo\":\"9804c4f57fb81337d29045869a77458a\",\"Name\":\"上海乾昊物流有限公司\",\"Org\":0}],\"OpenDate\":1529560800},{\"ExecuteUnite\":\"第二法庭\",\"Id\":\"d4acff82a0671b5cc2a35171eb0d44945\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"094f64554e3b296afc2ee52f02e2a384\",\"Name\":\"上海星明国际物流有限公司\",\"Org\":0},{\"KeyNo\":\"9804c4f57fb81337d29045869a77458a\",\"Name\":\"上海乾昊物流有限公司\",\"Org\":0}],\"OpenDate\":1536888600}],\"LatestTimestamp\":1539273600,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"094f64554e3b296afc2ee52f02e2a384\",\"Name\":\"上海星明国际物流有限公司\",\"Org\":0,\"Role\":\"原告\"}],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"民事一审\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]}],\"KtggCnt\":2,\"LastestDate\":1539273600,\"LastestDateType\":\"民事一审|判决日期\",\"LatestTrialRound\":\"民事一审\",\"LianCnt\":0,\"PcczCnt\":0,\"ProcuratorateList\":\"\",\"Province\":\"SH\",\"SdggCnt\":0,\"Source\":\"OT\",\"SxCnt\":0,\"Tags\":\"4,11\",\"Type\":1,\"XgCnt\":0,\"XjpgCnt\":0,\"XzcfCnt\":0,\"ZbCnt\":0,\"ZxCnt\":0}";
        System.out.println(evaluate(null,"[{\"D\":\"破产申请人\",\"N\":\"8352e83c9901ebd4d2a454bd083a1209\",\"O\":0,\"P\":\"浙江家园门业有限公司\",\"R\":\"申请人\"},{\"D\":\"破产申请人\",\"N\":\"8352e83c9901ebd4d2a454bd083a1209\",\"O\":0,\"P\":\"浙江家园门业有限公司\",\"R\":\"被申请人\"}]"));
    }
}
