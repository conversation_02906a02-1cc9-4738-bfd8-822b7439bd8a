package com.qcc.udf.risk_graph;

import lombok.Data;

/**
 * <AUTHOR>
 * @desc
 * @date 2021/3/4
 */

@Data
public class RiskRelationBaseResult {
    /**
     * 维度名称
     * eg：JYYC
     * */
    private String name;

    /**
     * 起点id
     * eg：rrfe32b788389b087056bc5d037fd804
     * */
    private String startid;

    /**
     * 终点id
     * eg：00000d43bf66c456534a50a9697aed7d
     * */
    private String endid;

    /**
     * id集合,英文逗号分割
     * eg：a23d15b9d7af46baf2edb7318a692e57,000113feaa34205a5617d8118f713633
     * */
    private String ids;

    /**
     * 起点终点id的MD5
     * eg：456d15b9d7af46baf2edb7318a692e59
     * */
    private String semd5;

    /**
     * 金额1
     * */
    private String prop2;

    /**
     * 金额2
     * */
    private String prop3;

    /**
     * 金额2
     * */
    private String info;
}
