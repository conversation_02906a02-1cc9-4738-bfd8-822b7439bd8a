package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by xpc on 2018/8/10.
 */
public class RegexStartWith extends UDF {
    public  static  boolean evaluate(String keyno, String regex){
        if(regex.equals("")) {
            regex = "^[0-9|a-f|j|w]+";
        }
        Pattern p = Pattern.compile(regex);
        Matcher m = p.matcher(keyno);
        return m.find();
    }
}
