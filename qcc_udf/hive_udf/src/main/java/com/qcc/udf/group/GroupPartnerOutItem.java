package com.qcc.udf.group;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class GroupPartnerOutItem {

    @JSONField(name = "KeyNo")
    private String keyNo = "";

    @JSONField(name = "Name")
    private String name = "";

    @JSONField(name = "Org")
    private Integer org;

    @JSONField(name = "HasImage")
    private Boolean hasImage;

    @JSONField(name = "StockPercent")
    private Double stockPercent;

    @JSONField(name = "IsGP")
    private Integer isGP;

    @JSONField(name = "DataType")
    private Integer datatype;
}
