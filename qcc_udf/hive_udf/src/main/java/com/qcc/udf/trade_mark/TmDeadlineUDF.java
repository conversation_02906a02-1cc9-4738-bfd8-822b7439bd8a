package com.qcc.udf.trade_mark;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

public class TmDeadlineUDF extends UDF {
    public static int evaluate(long time) {
        if (time == 0) {
            return 0;
        }
        long today = LocalDateTime.parse(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " 00:00:00",
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).toEpochSecond(ZoneOffset.of("+8"));
        long distance = time - today;
        if (distance == 365 * 86400) {
            /**
             * 一年
             */
            return 1;
        }
        if (distance == 86400 * 30 * 6) {
            /**
             * 六个月
             */
            return 2;
        }
        if (distance == 86400 * 7) {
            /**
             * 一周
             */
            return 3;
        }
        if (distance == -86400 * 30) {
            /**
             * 到期一个月
             */
            return 4;
        }
        return 0;
    }
}
