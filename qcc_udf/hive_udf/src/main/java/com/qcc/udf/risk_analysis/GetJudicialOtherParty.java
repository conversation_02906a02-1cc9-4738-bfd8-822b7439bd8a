package com.qcc.udf.risk_analysis;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.risk_analysis.entity.NameKeyDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/06/01 10:53
 * @description ：获取其他当事人
 */
public class GetJudicialOtherParty extends UDF {

    /**
     * Desc:获取其他当事人
     * @param prosecutorKeyNoArray 原告
     * @param appelleeKeyNoArray 被告
     * @param thirdPartyKeyNoArray 第三人
     * @param keyNoArray 所有当事人
     *
     * */
    public String evaluate(String prosecutorKeyNoArray, String appelleeKeyNoArray, String thirdPartyKeyNoArray, String keyNoArray) {

        //原被告名称集合
        List<String> nameList = new ArrayList<>();
        //解析原告
        if (StringUtils.isNotEmpty(prosecutorKeyNoArray) && !prosecutorKeyNoArray.equals("[]")) {
            List<NameKeyDto> plaintiffList = JSONArray.parseArray(prosecutorKeyNoArray, NameKeyDto.class);
            plaintiffList.stream().forEach(e -> nameList.add(e.getName()));
        }

        //解析被告
        if (StringUtils.isNotEmpty(appelleeKeyNoArray) && !appelleeKeyNoArray.equals("[]")) {
            List<NameKeyDto> defendantList = JSONArray.parseArray(appelleeKeyNoArray, NameKeyDto.class);
            defendantList.stream().forEach(e -> nameList.add(e.getName()));
        }

        //解析第三人
        if (StringUtils.isNotEmpty(thirdPartyKeyNoArray) && !thirdPartyKeyNoArray.equals("[]")) {
            List<NameKeyDto> defendantList = JSONArray.parseArray(thirdPartyKeyNoArray, NameKeyDto.class);
            defendantList.stream().forEach(e -> nameList.add(e.getName()));
        }

        //解析所有当事人
        List<NameKeyDto> keyNoArrayList;
        if (StringUtils.isEmpty(keyNoArray) || keyNoArray.equals("[]")) {
            keyNoArrayList = new ArrayList<>();
        } else {
            keyNoArrayList = JSONArray.parseArray(keyNoArray, NameKeyDto.class);
        }

        //解析第三人
        List<NameKeyDto> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(keyNoArrayList)) {
            for(NameKeyDto item : keyNoArrayList) {
                String name = item.getName();
                if (!nameList.contains(name)) {
                    result.add(item);
                }
            }
        }

        return JSONObject.toJSONString(result);
    }

    public static void main(String[] args) {
        String prosecutorKeyNoArray = "[{\"KeyNo\":\"2dd385936348e2dfd5865990b804f905\",\"Org\":0,\"Name\":\"成都市渣渣建材有限公司\"}]";
        String appelleeKeyNoArray = "[{\"KeyNo\":\"5b77f0133b6037daabb751d62c6f86de\",\"Org\":0,\"Name\":\"四川省圣民建筑劳务有限责任公司\"}]";
        String thirdPartyKeyNoArray = "[{\"KeyNo\":\"5b77f0133b6037daabb751d62c6f86de\",\"Org\":0,\"Name\":\"四川省圣民建筑劳务有限责任公司\"}]";
        String keyNoArray = "[{\"KeyNo\":\"2dd385936348e2dfd5865990b804f905\",\"Org\":0,\"Name\":\"成都市渣渣建材有限公司\"},{\"KeyNo\":\"5b77f0133b6037daabb751d62c6f86de\",\"Org\":0,\"Name\":\"四川省圣民建筑劳务有限责任公司\"},{\"KeyNo\":\"4e2e73528137cd469fcd2beb298628fc\",\"Org\":0,\"Name\":\"交通银行股份有限公司四川省分行\"}]";
        GetJudicialOtherParty getJudicialThirdParty = new GetJudicialOtherParty();
        System.out.println(getJudicialThirdParty.evaluate(prosecutorKeyNoArray,appelleeKeyNoArray,thirdPartyKeyNoArray, keyNoArray));
    }
}
