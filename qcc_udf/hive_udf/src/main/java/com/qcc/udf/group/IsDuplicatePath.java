package com.qcc.udf.group;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class IsDuplicatePath extends UDF {
//    public static void main(String[] args) {
//        String msg = "h0ec988b545a9722f64762e7266346f9-aaa-fdafdas-fdaf-aaa";
//        Boolean result = evaluate(msg);
//        System.out.printf(result.toString());
//    }

    public static Boolean evaluate(String path) {
        boolean iFlag = false;
        try {
            if (StringUtils.isNotBlank(path) && path.contains("-")) {
                List<String> nodeList = Arrays.asList(path.split("-", -1));
                if (CollectionUtils.isNotEmpty(nodeList) && nodeList.size() >= 2) {
                    long cnt = nodeList.stream().distinct().count();
                    if (nodeList.size() != cnt) {
                        iFlag = true;
                    }
                }
            }
        } catch (Exception e) {
        }
        return iFlag;
    }
}