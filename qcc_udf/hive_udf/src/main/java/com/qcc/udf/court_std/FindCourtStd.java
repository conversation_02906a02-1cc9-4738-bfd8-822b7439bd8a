package com.qcc.udf.court_std;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Objects;
import com.google.common.base.Strings;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021年05月18日 10:14
 */
public class FindCourtStd extends UDF {
    /**
     *
     * @param courtName 法院名称换取的法院
     * @param courtCode 法院名称换取的法院code
     * @param courtLevel 法院名称换取的法院courtLevel
     * @param anNoCourtName 案号换取的法院名称
     * @param aNnoCourtCode 案号换取的法院code
     * @param anNoCourtLevel 案号换取的法院courtLevel
     * @return
     */
    public static String evaluate(String courtName, String courtCode,String courtLevel
            ,String anNoCourtName,String aNnoCourtCode,String anNoCourtLevel) {
        Map<String,String> map = new HashMap<>();
        courtName = Strings.isNullOrEmpty(courtName)?"":courtName;
        courtCode = Strings.isNullOrEmpty(courtCode)?"":courtCode;
        courtLevel = Strings.isNullOrEmpty(courtLevel)?"":courtLevel;
        anNoCourtName = Strings.isNullOrEmpty(anNoCourtName)?"":anNoCourtName;
        aNnoCourtCode = Strings.isNullOrEmpty(aNnoCourtCode)?"":aNnoCourtCode;
        anNoCourtLevel = Strings.isNullOrEmpty(anNoCourtLevel)?"":anNoCourtLevel;
        if(!Strings.isNullOrEmpty(courtName) && Strings.isNullOrEmpty(anNoCourtName)){
            return stringFy(courtName,courtCode);
        }
        if(!Strings.isNullOrEmpty(anNoCourtName) && Strings.isNullOrEmpty(courtName)){
            return stringFy(anNoCourtName,aNnoCourtCode);
        }
        if(Strings.isNullOrEmpty(anNoCourtName) && Strings.isNullOrEmpty(courtName)){
            return stringFy("","");
        }

        //原始法院和爬虫法院code头两位数字一样，取案号法院code，反之则通过层级获取
        if(courtCode.length()>3 && aNnoCourtCode.length()>3){
            if(Objects.equal(courtCode.substring(0,2),aNnoCourtCode.substring(0,2))
                && !aNnoCourtCode.endsWith("0000")){
                return stringFy(anNoCourtName,aNnoCourtCode);
            }
        }

        //level越大  层级越细
        int courtLevelInt = courtLevel(courtLevel);
        int aNnoCourtLevelInt = courtLevel(anNoCourtLevel);

        if(courtLevelInt == aNnoCourtLevelInt){
            return stringFy(courtName,courtCode);
        }
        if(courtLevelInt>aNnoCourtLevelInt){
            return stringFy(courtName,courtCode);
        }
        if(aNnoCourtLevelInt>courtLevelInt){
            return stringFy(anNoCourtName,aNnoCourtCode);
        }


        return JSON.toJSONString(map);
    }

    static String stringFy(String courtName,String courtCode){
        Map<String,String> map = new HashMap<>();
        map.put("name",courtName);
        map.put("code",courtCode);
        return JSON.toJSONString(map);
    }

    static int courtLevel(String courtLevel){
        try {
            return Integer.valueOf(courtLevel).intValue();
        }catch (Exception e){
            return 0;
        }
    }

    /**
     * 法院code结尾0的数量
     * @param courtCode
     * @return
     */
    static int endZeroCount(String courtCode){
        int count = 0;
        //字符串反转
        courtCode = new StringBuilder(courtCode).reverse().toString();
        char[] array = courtCode.toCharArray();
        for (char ch : array) {
            if('0' == ch){
                count ++;
            }else{
                return count;
            }
        }

        return count;
    }

    public static void main(String[] args) {
        System.out.println(evaluate("瓢虫法院","540301","5","案号法院","4400000","4"));
//        System.out.println(endZeroCount("120001"));
//        System.out.println(357226066-73900000);
//        System.out.println(357226066-283326066);
    }
}
