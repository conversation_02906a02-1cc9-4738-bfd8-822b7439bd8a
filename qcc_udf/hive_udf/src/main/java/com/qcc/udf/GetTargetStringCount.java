package com.qcc.udf;

import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;


/**
 * @Auther: wangbin
 * @Date: 2019/7/11 11:45
 * @Description:统计目标字符在字符串中出现几次的
 */
public class GetTargetStringCount extends UDF {

    public static int evaluate(String str,String targetstr){
        if(str == null || str == "" || targetstr =="")
            return -1;

        if(!str.contains(targetstr))
            return -1;

        int count = 0;
        while(true)
        {
            count= count+1;
            str = StringUtils.replaceOnce(str,targetstr,"");

            if(!str.contains(targetstr))
                break;
        }

        return count;
    }

}
