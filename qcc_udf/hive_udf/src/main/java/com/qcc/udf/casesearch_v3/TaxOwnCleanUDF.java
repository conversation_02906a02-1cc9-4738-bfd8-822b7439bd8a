package com.qcc.udf.casesearch_v3;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class TaxOwnCleanUDF  extends UDF {
    public static String evaluate(List<String> doubleList) {
        if(CollectionUtils.isEmpty(doubleList)){
            return "";
        }
        List<TaxOwnDoubleEntity> list = new ArrayList<>();
        for (String item : doubleList) {
            TaxOwnDoubleEntity entity =JSON.parseObject(item,TaxOwnDoubleEntity.class);
            list.add(entity);
        }
        List<String> sortIdList = list.stream().sorted(Comparator.comparing(TaxOwnDoubleEntity::getPublishdate,Comparator.reverseOrder())
                .thenComparing(TaxOwnDoubleEntity::getId))
        .map(TaxOwnDoubleEntity::getId).collect(Collectors.toList());
        return sortIdList.subList(1,sortIdList.size()).stream().collect(Collectors.joining(","));
    }


    public static void main(String[] args) {
//        System.out.println(evaluate("(2007)昌 0107执 字12号之一"));
        List<String> list = Lists.newArrayList();
        list.add("{\n" +
                "    \"id\":\"1\",\n" +
                "    \"publishdate\":20\n" +
                "}");
        list.add("{\n" +
                "    \"id\":\"2\",\n" +
                "    \"publishdate\":10\n" +
                "}");
        System.out.println(evaluate(list));
    }
}
@Data
class  TaxOwnDoubleEntity{
    private long publishdate;
    private String id;
}