package com.qcc.udf.risk_analysis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

public class getXgListV2 extends UDF {

    public String evaluate(String keyNo, List<String> infoList, int type) {
        JSONArray result = new JSONArray();
        JSONArray array = new JSONArray();
        if (infoList != null && infoList.size() > 0){
            Map<String, JSONObject> infoMap = new LinkedHashMap<>();
            for (String str : infoList){
                JSONObject json = JSONObject.parseObject(str);
                if (type == 1){
                    json.put("subkeyno", json.getString("glkeyno") == null ? "" : json.getString("glkeyno"));
                    json.put("subname", json.getString("glname") == null ? "" : json.getString("glname"));
                }
                if (type == 2){
                    json.put("subkeyno", json.getString("sqrkeyno") == null ? "" : json.getString("sqrkeyno"));
                    json.put("subname", json.getString("sqrname") == null ? "" : json.getString("sqrname"));
                }
                if (type == 3){
                    json.put("subkeyno", json.getString("xglkeyno") == null ? "" : json.getString("xglkeyno"));
                    json.put("subname", json.getString("xglname") == null ? "" : json.getString("xglname"));
                }
                if (StringUtils.isEmpty(json.getString("subkeyno")) && StringUtils.isEmpty(json.getString("subname"))){
                    continue;
                }

                String key = "";
                if (StringUtils.isNotEmpty(json.getString("subkeyno"))){
                    key = json.getString("subkeyno");
                }else if (StringUtils.isNotEmpty(json.getString("subname"))){
                    key = RiskAnalysisUtil.full2Half(json.getString("subname"));
                }

                if (StringUtils.isNotEmpty(key)){
                    if (infoMap.containsKey(key)){
                        JSONObject item = infoMap.get(key);
                        item.put("KeyNo", keyNo);
                        item.put("Type", type);
                        JSONObject sqrInfo = new JSONObject();
                        sqrInfo.put("KeyNo", json.getString("subkeyno"));
                        sqrInfo.put("Name", json.getString("subname"));
                        sqrInfo.put("Org", RiskAnalysisUtil.getOrgByKeyNo(sqrInfo.getString("KeyNo")));
                        item.put("NameKeyNo", sqrInfo);
                        item.put("Cnt", item.getInteger("Cnt") + 1);
                        item.put("Anno", item.getString("Anno").concat(",").concat(json.getInteger("judgedate").toString().concat("_").concat(json.getString("anno"))));
                        item.put("SortDate", json.getInteger("judgedate") > item.getInteger("SortDate") ? json.getInteger("judgedate") : item.getInteger("SortDate"));
                        item.put("OriginId", item.getString("OriginId").concat(",").concat(json.getString("id")));

                        infoMap.put(key, item);
                    }else{
                        JSONObject item = new JSONObject();
                        item.put("KeyNo", keyNo);
                        item.put("Type", type);
                        JSONObject sqrInfo = new JSONObject();
                        sqrInfo.put("KeyNo", json.getString("subkeyno"));
                        sqrInfo.put("Name", json.getString("subname"));
                        sqrInfo.put("Org", RiskAnalysisUtil.getOrgByKeyNo(sqrInfo.getString("KeyNo")));
                        item.put("NameKeyNo", sqrInfo);
                        item.put("Cnt", 1);
                        item.put("Anno", json.getInteger("judgedate").toString().concat("_").concat(json.getString("anno")));
                        item.put("SortDate", json.getInteger("judgedate"));
                        item.put("OriginId", json.getString("id"));

                        infoMap.put(key, item);
                    }

                }
            }

            Set<String> sqrSet = infoMap.keySet();
            for (String str : sqrSet){
                JSONObject jsonObject = infoMap.get(str);
                jsonObject.put("Id", RiskAnalysisUtil.ecodeByMD5(keyNo.concat(String.valueOf(type)).concat(str)));
                array.add(jsonObject);
            }

            // 编辑时间和金额
            Iterator<Object> iterator = array.iterator();
            while (iterator.hasNext()){
                JSONArray annoArr = new JSONArray();
                JSONObject jsonObject = (JSONObject)iterator.next();
                // 日期：内部排序；外部排序取最新时间
                String[] annos = jsonObject.getString("Anno").split(",");
                List<String> annoList = new LinkedList<>();
                for (String str : annos){
                    annoList.add(str);
                }
                Collections.sort(annoList,((o1, o2) ->{
                    return o2.compareTo(o1);
                }));
                String anno = "";
                for (String str : annoList){
                    anno = anno.concat(",").concat(str.split("_")[1]);
                    JSONObject tmpJson = new JSONObject();
                    tmpJson.put("A", str.split("_")[1]);
                    tmpJson.put("B", Long.parseLong(str.split("_")[0]));
                    annoArr.add(tmpJson);
                }
                anno = anno.length() > 0 ? anno.substring(1) : anno;

                jsonObject.put("Anno", anno);
                jsonObject.put("AnnoArr", annoArr);

                result.add(jsonObject);
            }
        }


        return array.toString();
    }

    public static void main(String[] args) {
        getXgListV2 aa = new getXgListV2();
        List<String> infoList = JSON.parseArray("[\"{\\\"keyno\\\":\\\"\\\",\\\"id\\\":\\\"307a12f554f2817031dc3f4a1c683499\\\",\\\"anno\\\":\\\"（2019）渝0112执13730号\\\",\\\"judgedate\\\":\\\"1567612800\\\",\\\"publishdate\\\":\\\"1568044800\\\",\\\"nameandkeyno\\\":\\\"[{\\\\\\\"KeyNo\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"Name\\\\\\\":\\\\\\\"洪代明\\\\\\\",\\\\\\\"Org\\\\\\\":-2},{\\\\\\\"KeyNo\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"Name\\\\\\\":\\\\\\\"李天云\\\\\\\",\\\\\\\"Org\\\\\\\":-1}]\\\",\\\"xglkeyno\\\":\\\"\\\",\\\"xglname\\\":\\\"洪代明\\\",\\\"glkeyno\\\":\\\"\\\",\\\"glname\\\":\\\"\\\",\\\"sqrkeyno\\\":\\\"\\\",\\\"sqrname\\\":\\\"李天云\\\"}\",\"{\\\"keyno\\\":\\\"\\\",\\\"id\\\":\\\"ee395fb9a779307776c70be2af4eb1ff\\\",\\\"anno\\\":\\\"（2019）渝0112执2805号\\\",\\\"judgedate\\\":\\\"1550764800\\\",\\\"publishdate\\\":\\\"1551196800\\\",\\\"nameandkeyno\\\":\\\"[{\\\\\\\"KeyNo\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"Name\\\\\\\":\\\\\\\"洪代明\\\\\\\",\\\\\\\"Org\\\\\\\":-2},{\\\\\\\"KeyNo\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"Name\\\\\\\":\\\\\\\"段辉全\\\\\\\",\\\\\\\"Org\\\\\\\":-1}]\\\",\\\"xglkeyno\\\":\\\"\\\",\\\"xglname\\\":\\\"洪代明\\\",\\\"glkeyno\\\":\\\"\\\",\\\"glname\\\":\\\"\\\",\\\"sqrkeyno\\\":\\\"\\\",\\\"sqrname\\\":\\\"段辉全\\\"}\",\"{\\\"keyno\\\":\\\"\\\",\\\"id\\\":\\\"e6185e1563eb0b48063f4171aa1763f5\\\",\\\"anno\\\":\\\"（2019）渝0112执恢2067号\\\",\\\"judgedate\\\":\\\"1562515200\\\",\\\"publishdate\\\":\\\"1571846400\\\",\\\"nameandkeyno\\\":\\\"[{\\\\\\\"KeyNo\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"Name\\\\\\\":\\\\\\\"洪代明\\\\\\\",\\\\\\\"Org\\\\\\\":-2},{\\\\\\\"KeyNo\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"Name\\\\\\\":\\\\\\\"李元治\\\\\\\",\\\\\\\"Org\\\\\\\":-1}]\\\",\\\"xglkeyno\\\":\\\"\\\",\\\"xglname\\\":\\\"洪代明\\\",\\\"glkeyno\\\":\\\"\\\",\\\"glname\\\":\\\"\\\",\\\"sqrkeyno\\\":\\\"\\\",\\\"sqrname\\\":\\\"李元治\\\"}\"] ", String.class);
        System.out.println(aa.evaluate("00085812081520d0c1d3df40e42cf9f1", infoList, 3));
    }
}
