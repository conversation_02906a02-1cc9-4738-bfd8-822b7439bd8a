package com.qcc.udf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.cpws.casesearch_v2.CommonV2Util;
import com.qcc.udf.cpws.casesearch_v2.GetDetailInfoZXV2UDF;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

/**
 * @Auther: liulh
 * @Date: 2020/6/11 17:54
 * @Description:
 */
public class mergePersonId extends UDF {
    public static String evaluate(String caseSearchId, List<String> infoList) {
        JSONArray result = new JSONArray();

        // 公司不计算，多个personid不计算，没有personid不计算
        Map<String, String> nameMap = new LinkedHashMap<>();
        for (String str : infoList) {
            JSONObject json = JSONObject.parseObject(str);
            JSONArray array = json.getJSONArray("nameandkeyno");
            if (array != null && !array.isEmpty()){
                Iterator<Object> it = array.iterator();
                while (it.hasNext()){
                    JSONObject item = (JSONObject)it.next();
                    String keyNo = item.getString("KeyNo");
                    String name = item.getString("Name");
                    if (keyNo.startsWith("p")){
                        if(nameMap.containsKey(name)){
                            if (!nameMap.get(name).equals(keyNo)){
                                nameMap.remove(name);
                            }
                        }else{
                            nameMap.put(name, keyNo);
                        }
                    }
                }
            }
        }

        if (nameMap.size() > 0){
            for (String str : infoList) {
                JSONObject json = JSONObject.parseObject(str);
                String wdid = json.getString("wdid");
                String type = json.getString("type");

                JSONArray array = json.getJSONArray("nameandkeyno");
                if (array != null && !array.isEmpty()){
                    Iterator<Object> it = array.iterator();
                    while (it.hasNext()){
                        JSONObject item = (JSONObject)it.next();
                        String keyNo = item.getString("KeyNo");
                        String name = item.getString("Name");

                        if (StringUtils.isEmpty(keyNo) && nameMap.containsKey(name)){
                            JSONObject sub = new JSONObject();
                            sub.put("CaseSearchId", caseSearchId);
                            sub.put("Wdid", wdid);
                            sub.put("Type", type);
                            sub.put("Name", name);
                            sub.put("KeyNo", nameMap.get(name));

                            result.add(sub);
                        }
                    }
                }
            }
        }


        return result.size() > 0 ? result.toString() : "";
    }

    public static void main(String[] args) {
        List<String> infoList = new LinkedList<>();
        infoList.add("{\"id\":\"00004e1b744078ca0baebd6c2bc40454\",\"wdid\":\"eca66c7cb11a07d9949874149b5841a62\",\"type\":\"sx\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"p123456789\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"周庆洋\\\"}]\"}");
        infoList.add("{\"id\":\"test001\",\"wdid\":\"eca66c7cb11a07d9949874149b5841a62\",\"type\":\"zx\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"周庆洋\\\"}]\"}");

        System.out.println(evaluate("cs1", infoList));
    }

}
