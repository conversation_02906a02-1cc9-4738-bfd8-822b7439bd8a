package com.qcc.udf.company;

import com.google.common.base.Objects;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2022-10-14 10:56
 */
public class getCompanyLevelDivide3 extends UDF {

    private static final String COMPANY_T_LARGE = "大型企业";
    private static final String COMPANY_T_MIDDLE = "中型企业";
    private static final String COMPANY_T_SMALL = "小型企业";
    private static final String COMPANY_T_MICRO = "微型企业";
    private static final String COMPANY_T_UNKNOW = "未知";

    private static final Map<String, String> COMPANY_T_DOWN_MAP = new ImmutableMap.Builder<String, String>()
            .put(COMPANY_T_LARGE, COMPANY_T_MIDDLE).put(COMPANY_T_MIDDLE, COMPANY_T_SMALL).put(COMPANY_T_SMALL, COMPANY_T_MICRO).put(COMPANY_T_MICRO, COMPANY_T_MICRO).build();

    private static final CompanyClassFyQFK classFyQFK = new CompanyClassFyQFK();


    private List<String> getIndustryValueConfList(int employeeCount, int revenueNum, int totalAssertNum) {
        ArrayList<String> industryValueList = new ArrayList<>();
        if (employeeCount > 0) {
            industryValueList.add("x");
        }
        if (revenueNum > 0) {
            industryValueList.add("y");
        }
        if (totalAssertNum > 0) {
            industryValueList.add("z");

        }
        return industryValueList;
    }

    private String getCompanyTypeWithOneTarget(String industryCode, String industryCodeThree, int employeeCountReal, int revenueNum, int totalAssertNum, String targetValue) {
        String companyType2 = StringUtils.EMPTY;

        if (Objects.equal("x", targetValue)) {
            companyType2 = classFyQFK.getCompanyTypeByClassifyTwoAndEmployee(industryCode, industryCodeThree, Double.parseDouble(String.valueOf(employeeCountReal)));
        } else if (Objects.equal("y", targetValue)) {
            companyType2 = classFyQFK.getCompanyTypeWithRevenueNum(industryCode, industryCodeThree, Double.parseDouble(String.valueOf(revenueNum)));
        } else if (Objects.equal("z", targetValue)) {
            companyType2 = classFyQFK.getCompanyTypeWithTotal(industryCode, industryCodeThree, Double.parseDouble(String.valueOf(totalAssertNum)));
        }
        if (Objects.equal("出错", companyType2) || Objects.equal("待定", companyType2)) {
            companyType2 = StringUtils.EMPTY;
        }
        return companyType2;
    }

    /**
     * 获取企业划分
     *
     * @param companyType      根据财务数据计算的企业规模
     * @param registCapi       注册资本
     * @param isSm             是否为小微企业
     * @param isFin            是否为金融企业
     * @param hasFinancialData 是否为有公司财务数据
     * @param econKind         企业类型
     * @param startDate        成立日期
     * @param isZjtx           是否是专精特新中小企业、专精特新小巨人企业、科技型中小企业
     * @param employeeCount    是否有企业人数
     * @param tags             企业标签
     * @param baseFincial      是否是企业维度下的财务数据
     * @return
     */
    public String evaluate(String companyType, double registCapi, int isSm, int isFin, int hasFinancialData, String econKind, String startDate, int isZjtx, int employeeCount, String tags, int baseFincial, String companyTypeWithoutEmap, String industryCode, String industryThree, int employeeCountReal, int revenue_num, int total_assets_num) {
        //区分数据是否有效，当前行业数据判断条件都有
        List<String> industryValueConfList = getIndustryValueConfList(employeeCountReal, revenue_num, total_assets_num);
        List<String> industryConf = classFyQFK.getIndustryConf(industryCode, industryThree);
        List<String> array1 = Arrays.asList("上市公司");
        List<String> array2 = Arrays.asList("IPO", "上市辅导标签", "融资标签", "有近三年的百强榜单", "公布的央企国企");
        List<String> array3 = Arrays.asList("专精特新中小企业", "专精特新小巨人企业", "科技型中小企业");
        //所有的配置都包含
        boolean dataMatchConf = CollectionUtils.isNotEmpty(industryValueConfList) && industryValueConfList.containsAll(industryConf);
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyyMMdd");
        if (isFin == 1 && StringUtils.isNotEmpty(companyType)) {
            return companyType;
        }
        List<String> existsConf = industryConf.stream().filter(industryValueConfList::contains).collect(Collectors.toList());
        //满足所有条件
        if (dataMatchConf && StringUtils.isNotEmpty(companyType)) {
            if (hasFinancialData == 1 && baseFincial == 1 && tags.contains("上市公司") && StringUtils.isNotEmpty(companyType)) {
                return companyType;
            }
            //有效财务数据，有部分标签
            if (hasFinancialData == 1 && baseFincial == 1) {
                //存在ipo相关标签
                return getCompanyType111(companyType, registCapi, econKind, tags, array2, isFin);
            }
            //其他来源财务数据，符合标签判断
            if (hasFinancialData == 1 && baseFincial == 0) {
                return getCompanyType101(companyType, registCapi, isSm, isFin, econKind, tags, array2, array3);
            }

        } else if (CollectionUtils.isNotEmpty(industryConf) && StringUtils.isNotEmpty(companyType) && existsConf.size() == 1) {

            //有配置行业，但是不满足完全包含配置，即缺失一种或者都缺失，缺失一种的在这里处理
            String companyType2 = getCompanyTypeWithOneTarget(industryCode, industryThree, employeeCountReal, revenue_num, total_assets_num, existsConf.get(0));
            //官方来源的财务数据
            if (hasFinancialData == 1 && baseFincial == 1 && StringUtils.isNotEmpty(companyType2)) {
                return getCompanyType110(companyType2, registCapi, isFin, econKind, tags, array2, array3);
            }
            //其他来源的财务数据
            if (hasFinancialData == 1 && baseFincial == 0 && StringUtils.isNotEmpty(companyType2)) {
                return getCompanyType100(companyType2, tags, array2, array3);
            }
        }


        if ((StringUtils.isEmpty(startDate)) || (StringUtils.isNotEmpty(startDate) && startDate.compareTo(df.format(LocalDateTime.now().minusYears(1))) < 0)) {
            return getCompanyTypeBeforeOneYear(registCapi, isSm, isFin, tags, industryCode, industryThree, employeeCountReal, array2, array3);

        }
        if (StringUtils.isNotEmpty(startDate) && startDate.compareTo(df.format(LocalDateTime.now().minusYears(1))) >= 0) {
            return getStartLessThanOneYear(companyType, registCapi, isSm, isFin, tags, array2, array3);
        }
        return COMPANY_T_UNKNOW;
    }

    private String getStartLessThanOneYear(String companyType, double registCapi, int isSm, int isFin, String tags, List<String> array2, List<String> array3) {
        if (array2.stream().anyMatch(tags::contains)) {
            if (COMPANY_T_LARGE.equals(companyType) || COMPANY_T_MIDDLE.equals(companyType)) {
                return COMPANY_T_MIDDLE;
            } else {
                return getCompanyTypeByRegistCapi(registCapi, isFin);
            }
        } else if (array3.stream().anyMatch(tags::contains)) {
            return getCompanyTypeByRegistCapi(registCapi, isFin);

        } else if (isSm == 1 || tags.contains("小微企业")) {
            String companyTSmall = getSmallMicroWithRegistCapi(registCapi, isFin);
            if (companyTSmall != null) {
                return companyTSmall;
            }
        }

        String companyTypeByRegi = getCompanyTypeByRegi(registCapi, 10000, 500, 100);

        if (Objects.equal(COMPANY_T_MICRO, companyTypeByRegi) || Objects.equal(COMPANY_T_SMALL, companyTypeByRegi)) {
            return companyTypeByRegi;
        }
        return COMPANY_T_UNKNOW;
    }

    private String getCompanyTypeBeforeOneYear(double registCapi, int isSm, int isFin, String tags, String industryCode, String industryThree, int employeeCountReal, List<String> array2, List<String> array3) {
        if (tags.contains("上市公司") || array2.stream().anyMatch(tags::contains)) {
            if ((isFin == 0 && registCapi > 10000) || (isFin == 1 && registCapi > 10000000)) {
                return COMPANY_T_LARGE;
            }
            return COMPANY_T_MIDDLE;
        }
        if (array3.stream().anyMatch(tags::contains)) {
            //非金融业
            if (isFin == 0) {

                String evaluate = classFyQFK.getCompanyTypeByClassifyTwoAndEmployee(industryCode, industryThree, Double.parseDouble(String.valueOf(employeeCountReal)));
                if (COMPANY_T_LARGE.equals(evaluate) || COMPANY_T_MIDDLE.equals(evaluate)) {
                    return COMPANY_T_MIDDLE;
                }
                if (registCapi <= 500) {
                    return COMPANY_T_SMALL;
                }
                return COMPANY_T_MIDDLE;

            } else {
                if (registCapi <= 1000000) {
                    return COMPANY_T_SMALL;
                }
                return COMPANY_T_MIDDLE;

            }

        }
        if (isSm == 1 || tags.contains("小微企业")) {
            if (array3.stream().noneMatch(tags::contains)) {
                String companyTSmall = getSmallMicroWithRegistCapi(registCapi, isFin);
                if (companyTSmall != null) {
                    return companyTSmall;
                }
            }
        }

        if (isFin == 1) {
            return getCompanyTypeByRegi(registCapi, 10000000, 1000000, 100000);
        } else {
            String companyTypeByRegi = getCompanyTypeByRegi(registCapi, 10000, 500, 100);
            //根据注册资本判断，如果是小微直接使用
            if (COMPANY_T_SMALL.equals(companyTypeByRegi) || COMPANY_T_MICRO.equals(companyTypeByRegi)) {
                return companyTypeByRegi;
            } else {

                if (classFyQFK.getCompanyNeedEmployee(industryCode, industryThree) && employeeCountReal > 0) {
                    //根据行业取到对应行业的人数限制，如果满足，则以当前结果，否则降级
                    String companyTypeByClassifyTwoAndEmployee = classFyQFK.getCompanyTypeByClassifyTwoAndEmployee(industryCode, industryThree, Double.parseDouble(String.valueOf(employeeCountReal)));
                    if (Objects.equal(companyTypeByRegi, companyTypeByClassifyTwoAndEmployee)) {
                        return companyTypeByRegi;
                    }
                    return COMPANY_T_DOWN_MAP.getOrDefault(companyTypeByRegi, COMPANY_T_UNKNOW);

                }
                return companyTypeByRegi;
            }
        }
    }

    private String getSmallMicroWithRegistCapi(double registCapi, int isFin) {
        if (isFin == 0) {
            if (registCapi > 100) {
                return COMPANY_T_SMALL;
            } else if (registCapi <= 100) {
                return COMPANY_T_MICRO;
            }
        }
        if (registCapi > 100000) {
            return COMPANY_T_SMALL;
        } else if (registCapi <= 100000) {
            return COMPANY_T_MICRO;
        }
        return null;
    }

    private String getCompanyType100(String companyType, String tags, List<String> array2, List<String> array3) {

        if (tags.contains("上市公司") || array2.stream().anyMatch(tags::contains) || array3.stream().anyMatch(tags::contains)) {
            //财务数据符合中大型企业，则处理成为“中型”；否则默认为“小型”

            if (COMPANY_T_LARGE.equals(companyType) || COMPANY_T_MIDDLE.equals(companyType)) {
                return COMPANY_T_MIDDLE;
            }
            return COMPANY_T_SMALL;
        }

        return COMPANY_T_DOWN_MAP.get(companyType);
    }

    private String getCompanyType101(String companyType, double registCapi, int isSm, int isFin, String econKind, String tags, List<String> array2, List<String> array3) {
        if (array2.stream().anyMatch(tags::contains)) {
            if (COMPANY_T_LARGE.equals(companyType) || COMPANY_T_MIDDLE.equals(companyType)) {
                return companyType;
            }
            if (COMPANY_T_SMALL.equals(companyType)) {
                return COMPANY_T_MIDDLE;
            }
            if (COMPANY_T_MICRO.equals(companyType)) {
                return COMPANY_T_SMALL;
            }
        }
        if (array3.stream().anyMatch(tags::contains)) {
            if (COMPANY_T_MICRO.equals(companyType) || COMPANY_T_SMALL.equals(companyType)) {
                return COMPANY_T_SMALL;
            }
            return companyType;

        } else {

            if (StringUtils.isNotEmpty(econKind) && Pattern.matches(".*(个人独资|自然人独资|合作社|专业合作|全民所有|集体所有|集体企业|集体经济).*", econKind) && COMPANY_T_LARGE.equals(companyType)) {
                return COMPANY_T_MIDDLE;
                //非金融企业判断为大型企业，需要满足注册资本
            }
            //有财务数据，有员工数，判断不是大型企业，如果有小微企业标签，则将companyType降级
            else if (isSm == 1 && !(COMPANY_T_MICRO.equals(companyType) || COMPANY_T_SMALL.equals(companyType))) {
                return COMPANY_T_DOWN_MAP.getOrDefault(companyType, COMPANY_T_UNKNOW);
            } else if (isFin == 0 && COMPANY_T_LARGE.equals(companyType)) {
                if (registCapi <= 10000) {
                    return COMPANY_T_MIDDLE;
                } else {
                    return COMPANY_T_LARGE;
                }
            }
            return companyType;

        }
    }

    private String getCompanyType110(String companyType, double registCapi, int isFin, String econKind, String tags, List<String> array2, List<String> array3) {


        if (array2.stream().anyMatch(tags::contains)) {
            if (StringUtils.isNotEmpty(econKind) && Pattern.matches(".*(个人独资|自然人独资|合作社|专业合作|全民所有|集体所有|集体企业|集体经济).*", econKind) && COMPANY_T_LARGE.equals(companyType)) {
                return COMPANY_T_MIDDLE;
            } else if ((COMPANY_T_LARGE.equals(companyType) && isFin == 0)) {
                if (registCapi <= 10000) {
                    return COMPANY_T_MIDDLE;
                }
            }
            return companyType;
        } else if (array3.stream().anyMatch(tags::contains)) {
            if (COMPANY_T_LARGE.equals(companyType) || COMPANY_T_MIDDLE.equals(companyType)) {
                return COMPANY_T_MIDDLE;
            }
            return COMPANY_T_SMALL;
        }
        return COMPANY_T_DOWN_MAP.getOrDefault(companyType, COMPANY_T_UNKNOW);
    }


    private String getCompanyType111(String companyType, double registCapi, String econKind, String tags, List<String> array2, int isFin) {
        if (array2.stream().anyMatch(tags::contains)) {
            if (COMPANY_T_LARGE.equals(companyType) || COMPANY_T_MIDDLE.equals(companyType)) {
                return companyType;
            }
            if (COMPANY_T_SMALL.equals(companyType)) {
                return COMPANY_T_MIDDLE;
            }
            if (COMPANY_T_MICRO.equals(companyType)) {
                return COMPANY_T_SMALL;
            }

        }
        if (StringUtils.isNotEmpty(econKind) && Pattern.matches(".*(个人独资|自然人独资|合作社|专业合作|全民所有|集体所有|集体企业|集体经济).*", econKind) && COMPANY_T_LARGE.equals(companyType)) {
            return COMPANY_T_MIDDLE;
        } else if ((COMPANY_T_LARGE.equals(companyType) && isFin == 0)) {
            if (registCapi <= 10000) {
                return COMPANY_T_MIDDLE;
            }
        }
        return companyType;


    }

    private String getCompanyTypeByRegistCapi(double regist, int isFin) {

        if (isFin == 1) {
            if (regist <= 1000000) {
                return COMPANY_T_SMALL;
            }
            return COMPANY_T_MIDDLE;
        } else {
            if (regist <= 500) {
                return COMPANY_T_SMALL;
            }
            return COMPANY_T_MIDDLE;

        }
    }

    private String getCompanyTypeByRegi(double registCapi, int i, int b, int i2) {
        if (registCapi > i) {
            return "大型企业";
        } else if (registCapi > b) {
            return "中型企业";
        } else if (registCapi > i2) {
            return "小型企业";
        } else {
            return "微型企业";
        }
    }


    public static void main(String[] args) {
        getCompanyLevelDivide3 getCompanyLevelDivide = new getCompanyLevelDivide3();
        String evaluate = getCompanyLevelDivide.evaluate("微型企业", 14800.0, 1, 0, 1, "有限责任公司（外国法人独资）",
                "1995-05-05", 1, 0, "有近三年的百强榜单,小微企业", 0, "大型企业", "33", "331", 0, 65, 264);
        System.out.println(evaluate);

    }

}
