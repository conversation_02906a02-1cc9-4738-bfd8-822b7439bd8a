package com.qcc.udf.tax;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.qcc.udf.kzz.RegexHelper;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParsePosition;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/6/22
 **/
public class MyTaxArrUtil {
    /**
     * 通用字段处理方法
     * 所有字段去掉前后空格
     * null、--、/、空字符统一成空字符
     * 中文括号转英文
     */
    public static String commonFilter(String input) {
        if (StringUtils.isEmpty(input)) {
            return "";
        }
        String notBlank = ReUtil.replaceAll(input, Pattern.compile("\\s| "), "");
        if (notBlank.equals("null") || notBlank.equals("--") || notBlank.equals("/")) {
            return "";
        } else {
            return notBlank.replace("（", "(").replace("）", ")");
        }
    }

    /**
     * 解析 正文的段落
     */
    public static List<String> extractContent(String s) {
        String content = JSON.parseObject(s).getString("Content");
        List<String> splitArray = Arrays.stream(content.split("<p><br/></p>|\\n|<br/>")).collect(Collectors.toList());
        List<String> exNames = new ArrayList<>();
        for (String s1 : splitArray) {
            String s2 = ReUtil.replaceAll(s1, Pattern.compile("<[^>]+>"), "")
                    .replace("\\t", "").replace("\\n", "").replace("\\r", "");
            String s3 = ReUtil.replaceAll(s2, Pattern.compile("\\s| "), "");
            if (s3.startsWith("附件")) {
                break;
            }
            if (!StrUtil.isBlank(s3)) {
                exNames.add(s3);
            }
        }
        return exNames;
    }



    public static String setPubLevel2(String website) {
        String s = commonFilter(website);
        if (s.contains("省")) {
            if (s.contains("市")) {
                if (s.contains("区") || s.contains("县")) {
                    return "区县级";
                }
                return "地市级";
            }
            return "省部级";
        }
        return "";
    }

    /**
     * 设置纳税人识别号
     */
    public static String setTaxNo(String taxpayernumber) {
        if (StringUtils.isEmpty(taxpayernumber))
            return "";
        if (taxpayernumber.length() > 18) {
            return "";
        }
        String s = commonFilter(taxpayernumber);
        if (!ReUtil.contains(Pattern.compile("\\d|[a-zA-Z]"), s)) {
            return "";
        }
        if (s.startsWith("L")) {
            return taxpayernumber.substring(1);
        }
        return s;
    }

    public static String setTaxType(String taxpayertype) {
        String s = commonFilter(taxpayertype);
        if (ReUtil.contains(Pattern.compile("\\d|[a-zA-Z]"), s)) {
            return "";
        }
        return s;
    }

    public static String setLegalPer(String legal) {
        String s = commonFilter(legal);
        if (!Validator.hasChinese(s) && !ReUtil.contains("[a-zA-z]", s)) {
            return "";
        }
        if (s.length() < 2) {
            return "";
        }
        if (Validator.hasChinese(s)) {
            return s.replaceAll("\\s", "");
        }
        return s;
    }


    public static String extractPeriod(String taxtitle) {
        if (StringUtils.isEmpty(taxtitle)) return "";
        String s = ReUtil.get(Pattern.compile("((\\d)(.*?)(期|号))"), taxtitle, 1);
        return StringUtils.isEmpty(s) ? "" : s;
    }

    public static String extStartDate(String taxtitle) {
        if (StringUtils.isEmpty(taxtitle)) return "";

        String year = ReUtil.get(Pattern.compile("(\\d{4})年"), taxtitle, 1);
        String season = ReUtil.get(Pattern.compile("年((.*?)(季度|年))"), taxtitle, 1);

        if (StringUtils.isEmpty(year) || StringUtils.isEmpty(season)) return "";

        StringBuffer sb = new StringBuffer(year);
        if (season.contains("季度")) {
            if (season.contains("二") || season.contains("2")) {
                sb.append("0401");
            } else if (season.contains("三") || season.contains("3")) {
                sb.append("0701");
            } else if (season.contains("四") || season.contains("4")) {
                sb.append("1001");
            } else {
                sb.append("0101");
            }
        } else if (season.contains("上半")) {
            sb.append("0101");
        } else if (season.contains("下半")) {
            sb.append("0701");
        }
        return sb.toString();
    }

    public static String extEndDate(String taxtitle) {
        if (StringUtils.isEmpty(taxtitle)) return "";
        String year = ReUtil.get(Pattern.compile("(\\d{4})年"), taxtitle, 1);
        String season = ReUtil.get(Pattern.compile("年((.*?)(季度|年))"), taxtitle, 1);
        if (StringUtils.isEmpty(year) || StringUtils.isEmpty(season)) return "";

        StringBuffer sb = new StringBuffer(year);
        if (season.contains("季度")) {
            if (season.contains("二") || season.contains("2")) {
                sb.append("0630");
            } else if (season.contains("三") || season.contains("3")) {
                sb.append("0930");
            } else if (season.contains("四") || season.contains("4")) {
                sb.append("1231");
            } else {
                sb.append("0331");
            }
        } else if (season.contains("上半")) {
            sb.append("0630");
        } else if (season.contains("下半")) {
            sb.append("1231");
        }
        return sb.toString();
    }


    /**
     * 处理Str 逗号 金额
     *
     * @param str
     * @return
     */
    public static BigDecimal revertBigDecimal(String str) {
        if (StringUtils.isEmpty(str)) {
            return new BigDecimal(0);
        }
        str = str.replaceAll(",| ", "");
        if (!isNumeric(str) || str.length()>12){
            return new BigDecimal(0);
        }

        DecimalFormat format = new DecimalFormat();
        format.setParseBigDecimal(true);
        ParsePosition position = new ParsePosition(0);
        BigDecimal parse = (BigDecimal) format.parse(str, position);
        if (str.length() == position.getIndex()) {
            return parse;
        }
        return new BigDecimal(0);
    }

    public static boolean isNumeric(String str) {
        Pattern pattern = Pattern.compile("[0-9\\.]*");
        Matcher isNum = pattern.matcher(str);
        if (!isNum.matches()) {
            return false;
        }
        return true;
    }

    /**
     * 标准化 税种 参数 前置处理
     *
     * @param taxtypes
     * @return
     */
    public static String preHandleTaxType(String taxtypes) {
        //前后去除空格
//        return taxtypes.trim().replaceAll("(,|，| )", "、");
        return taxtypes.trim();
    }


    public static String extAddress(String s2) {
        if (!Validator.hasChinese(s2) && !ReUtil.contains("[a-zA-Z]", s2)) {
            return "";
        }
        if (s2.length() < 5) {
            return "";
        }
        return s2;
    }

    public static String extProvince(String s2) {
        if (!Validator.hasChinese(s2) && !ReUtil.contains("[a-zA-Z]", s2)) {
            return "";
        }
        if (s2.length() < 2) {
            return "";
        }
        return s2;
    }


    public static String extIdNo(String s2) {
        if (StringUtils.isEmpty(s2))
            return "";
        if (s2.length() > 18) {
            return "";
        } else {
            if (!ReUtil.contains("\\d", s2)) {
                return "";
            }
            if (s2.length() < 5) {
                return "";
            }
            return s2;
        }
    }

    /**
     * 价格转string
     */
    public static String bigDecimalToString(BigDecimal price) {
        if (price == null) {
            return "";
        } else {
            String priceTemp = price.setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString();
            if (priceTemp.endsWith(".00")) {
                return priceTemp.substring(0, priceTemp.indexOf("."));
            } else {
                return priceTemp;
            }
        }
    }




    public static Map<String, String> PRO_EN_MAP = new LinkedHashMap<>();

    static {
        PRO_EN_MAP.put("AH", "安徽");
        PRO_EN_MAP.put("BJ", "北京");
        PRO_EN_MAP.put("CQ", "重庆");
        PRO_EN_MAP.put("FJ", "福建");
        PRO_EN_MAP.put("GD", "广东");
        PRO_EN_MAP.put("GS", "甘肃");
        PRO_EN_MAP.put("GX", "广西");
        PRO_EN_MAP.put("GZ", "贵州");
        PRO_EN_MAP.put("HAIN", "海南");
        PRO_EN_MAP.put("HB", "湖北");
        PRO_EN_MAP.put("HEN", "河南");
        PRO_EN_MAP.put("HLJ", "黑龙江");
        PRO_EN_MAP.put("HUB", "湖北");
        PRO_EN_MAP.put("HUN", "湖南");
        PRO_EN_MAP.put("JL", "吉林");
        PRO_EN_MAP.put("JS", "江苏");
        PRO_EN_MAP.put("JX", "江西");
        PRO_EN_MAP.put("LN", "辽宁");
        PRO_EN_MAP.put("NMG", "内蒙古");
        PRO_EN_MAP.put("NX", "宁夏");
        PRO_EN_MAP.put("QH", "青海");
        PRO_EN_MAP.put("SAX", "陕西");
        PRO_EN_MAP.put("SC", "四川");
        PRO_EN_MAP.put("SD", "山东");
        PRO_EN_MAP.put("XJ", "新疆");
        PRO_EN_MAP.put("XZ", "西藏");
        PRO_EN_MAP.put("YN", "云南");
        PRO_EN_MAP.put("ZJ", "浙江");
        PRO_EN_MAP.put("SH", "上海");
    }

    public static String getChineseFromAlf(String code) {
        return PRO_EN_MAP.get(code) == null ? "" : PRO_EN_MAP.get(code);
    }

    public static boolean isAmountStr(String s) {
        return Validator.isNumber(s.replaceAll(",|，", ""));
    }




    public static String full2Half(String input) {
        char c[] = input.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == '\u3000') {
                c[i] = ' ';
            } else if (c[i] > '\uFF00' && c[i] < '\uFF5F') {
                c[i] = (char) (c[i] - 65248);

            }
        }
        String returnString = new String(c);

        return returnString;
    }

    /**
     * 中文英文和数字
     */
    public static final Pattern CHINESE_AND_CHAR_PATTERN = Pattern.compile("([\\u4e00-\\u9fa5A-Za-z]+)");
    /**
     * 验证字符串是否包含文字，字母其中一种
     * @param value  判断字符串
     * @return true 包含，否 不包含
     */
    public static  boolean checkChineseChar(String value) {
        return !Objects.equals(RegexHelper.getMatchCount(CHINESE_AND_CHAR_PATTERN.pattern(), value), 0);
    }

}
