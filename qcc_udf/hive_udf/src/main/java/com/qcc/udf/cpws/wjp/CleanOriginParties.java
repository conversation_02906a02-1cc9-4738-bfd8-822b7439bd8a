package com.qcc.udf.cpws.wjp;

import com.qcc.udf.tax.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.HashSet;
import java.util.Set;

public class CleanOriginParties extends UDF {

    public static String evaluate(String originParties, String id) {

        Set<String> resp = new HashSet<>();
        String repStr = "";
        String updateTime = "1970-01-01 00:00:00";
        if (StringUtils.isNotEmpty(originParties)) {
            String[] appllors = originParties.split("==");
            for (String s : appllors) {

                String[] idsName = s.split("#——#");
                String uid = idsName[0];
                String Names = idsName[1];
                String time = idsName[2];

                Set<String> or = cleanOriginParties(Names);
                if (CollectionUtils.isEmpty(or)) {
                    continue;
                }
                if (or.size() > resp.size()) {
                    resp = or;
                    repStr = Names;
                } else if (or.size() == resp.size()) {
                    int orLength = String.join("", or).length();
                    int respLength = String.join("", resp).length();
                    if (orLength > respLength) {
                        resp = or;
                        repStr = Names;
                    } else if (orLength == respLength) {
                        if (DateUtil.parseStrToDate(time, DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS)
                                .compareTo(DateUtil.parseStrToDate(updateTime, DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS)) > 0) {
                            updateTime = time;
                            resp = or;
                            repStr = Names;
                        }

                    }
                }
            }
        }
        return repStr;
    }

    public static Set<String> cleanOriginParties(String originParties) {
        Set<String> appellor = new HashSet<>();
        if (StringUtils.isNotEmpty(originParties)) {
            String[] opSplit = originParties.split("[,，]");
            for (String s : opSplit) {
                s = s.replace("(", "（").replace(")", "）").replace("\"", "").replace("[", "").replace("]", "");
                if (s.length() < 2) {
                    continue;
                }
                if (s.matches(".*[某×xX*ｘＸ✘✖ΧχⅹХх].*")) {
                    continue;
                }
                if (s.contains("执行案外人") || s.contains("立案庭") || s.contains("审判庭") || s.contains("败诉的") ||
                        s.contains("已预交") || s.contains("刑庭") || s.contains("本院") || s.equals("汽汽车金融有限公司")
                        || s.equals("汽-大众汽车有限公司") || s.equals("三)") || s.equals("基于该宣传广告") || s.equals("上述损失总计") || s.equals("信息")
                        || s.contains("法律") || s.contains("律师") || s.equals("人的)")
                        || s.equals("不同意离婚)")
                        || s.equals("反诉人））")
                        || s.equals("为实现债权）")
                        || s.equals("经追收无果）")
                        || s.equals("表示确无财产可供执行)")
                        || s.equals("接收房屋后）")
                        || s.equals("基本情况）")
                        || s.equals("经催收无果 ）")
                        || s.equals("p&gt  ）")
                        || s.equals("名称）")
                        || s.equals("公司）")
                        || s.equals("下同）)")
                        || s.equals("各方当事人）")
                        || s.equals("健康权）")
                        || s.equals("意见&lt）")
                        || s.equals("故意伤害罪）")
                        || s.equals("出具欠条后）")
                        || s.equals("基本）")) {
                    continue;
                }
                if (s.contains("（原")) {
                    s = (s.split("\\（原")[0]);
                }
                if (s.contains("（下称")) {
                    s = (s.split("\\（下称")[0]);
                }
                if (s.contains("&times")) {
                    s = (s.replace("&times", ""));
                }
                if (s.contains("＆ｌｄｑｕｏ")) {
                    s = (s.replace("＆ｌｄｑｕｏ", ""));
                }
                if (s.contains("＆ｔｉｍｅｓ")) {
                    s = (s.replace("＆ｔｉｍｅｓ", ""));
                }
                if (s.contains("被执行人")) {
                    s = (s.replace("被执行人", ""));
                }
                if (s.contains("执行人")) {
                    s = (s.replace("执行人", ""));
                }
                if (s.contains("公民身份号码")) {
                    s = (s.replace("公民身份号码", ""));
                }
                if (s.contains("公民")) {
                    s = (s.replace("公民", ""));
                }
                if (s.contains("身份证号")) {
                    s = (s.replace("身份证号", ""));
                }
                //司|行|队|会|院|局|部|社|厂|厅|所|店|中心|政府|企业|基地|超市|处|矿|室|场|校|城|园|馆|站|组|庭|台|学|吧|庄|户|段|团|村|房|人|家|坊|公寓|库
                if (s.length() > 5) {
                    if (s.contains("·")
                            || s.endsWith("司") || s.endsWith("行") || s.endsWith("队") || s.endsWith("会")
                            || s.endsWith("院") || s.endsWith("局") || s.endsWith("部") || s.endsWith("社")
                            || s.endsWith("厂") || s.endsWith("厅") || s.endsWith("所") || s.endsWith("店")
                            || s.endsWith("中心") || s.endsWith("政府") || s.endsWith("企业") || s.endsWith("基地")
                            || s.endsWith("超市") || s.endsWith("处") || s.endsWith("矿") || s.endsWith("室")
                            || s.endsWith("场") || s.endsWith("校") || s.endsWith("城") || s.endsWith("园")
                            || s.endsWith("馆") || s.endsWith("站") || s.endsWith("组") || s.endsWith("庭")
                            || s.endsWith("台") || s.endsWith("学") || s.endsWith("吧") || s.endsWith("庄")
                            || s.endsWith("户") || s.endsWith("段") || s.endsWith("团") || s.endsWith("村")
                            || s.endsWith("房") || s.endsWith("人") || s.endsWith("家") || s.endsWith("坊")
                            || s.endsWith("公寓") || s.endsWith("库")
                    ) {
                        appellor.add(s);
                    }
                } else if (s.length() > 1) {

                    appellor.add(s);
                }
            }

        }
        return appellor;
    }


    public static void main(String[] args) {
        System.out.println(evaluate("e50ff282078b4141aef3353151a93ff70##,\"中国##财产保险股份有限公司潍坊中心支公司\"],[\"夏##\"##2024-04-22 11:22:30.0==eaf61c8b9c6c47edafb8ebc2b8122e5c0##,\"中国##财产保险股份有限公司潍坊中心支公司\"],[\"夏##\"##2024-04-21 09:04:46.0,e50ff282078b4141aef3353151a93ff70",
                "d30bb26029ec067f3a5d22ca6e4780190"));
    }
}
