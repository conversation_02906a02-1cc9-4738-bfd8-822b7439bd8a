package com.qcc.udf.product;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2019-11-22 11:31
 * @Description 新动态新闻详情表
 */
@Data
public class RiskChangeListNews implements Serializable {

    public RiskChangeListNews(){
    }

    /**
     * 主键（KeyNo、CompanyName、Category、ObjectId、BeforeContent、AfterContent、ChangeExtend、ChangeStatus）
     */
    @JSONField(name = "Id")
    private String id;

    /**
     * 公司\人员key
     */
    @JSONField(name = "KeyNo")
    private String keyNo;

    /**
     * 公司\人员名称
     */
    @JSONField(name = "Name")
    private String name;

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 风险等级（1高风险，2风险，3警示，4提示） 新闻设为空
     */
    @JSONField(name = "RiskLevel")
    private Integer riskLevel;

    /**
     * 数据类型,新闻：积极62 中立66 消极67
     */
    @JSONField(name = "Category")
    private Integer category = 66;

    /**
     * 新闻不用
     */
    @JSONField(name = "BeforeContent")
    private String beforeContent = "";

    /**
     * 新闻不用
     */
    @JSONField(name = "AfterContent")
    private String afterContent = "";

    /**
     * 变更扩展字段
     */
    @JSONField(name = "ChangeExtend")
    private String changeExtend;

    /**
     * 外键Id
     */
    @JSONField(name = "ObjectId")
    private String objectId;

    /**
     * 变更状态（0更新1新增2删除）
     */
    @JSONField(name = "ChangeStatus")
    private Integer changeStatus = 1;

    /**
     * 变更时间
     */
    @JSONField(name = "ChangeDate")
    private LocalDateTime changeDate;

    /**
     * 1公司，2人
     */
    @JSONField(name = "DataType")
    private Integer dataType = 1;

    /**
     * 变更扩展字段
     */
    @JSONField(name = "Extend1")
    private String extend1 = "";

    /**
     * 变更扩展字段  "hot" 代表热点新闻
     */
    @JSONField(name = "Extend2")
    private String extend2 = "";

    @JSONField(name = "IsValid")
    private Integer isValid = 1;

    /**
     * 新增时间
     */
    @JSONField(name = "CreateDate")
    private LocalDateTime createDate = LocalDateTime.now();

    @JSONField(name = "UpdateDate")
    private LocalDateTime updateDate = LocalDateTime.now();

    /**
     * 1重要 0默认
     */
    @JSONField(name = "ImportanceFlag")
    private Integer importanceFlag = 0;

    /**
     * 1风险 0默认
     */
    @JSONField(name = "IsRisk")
    private Integer isRisk = 0;

    /**
     * 1风险 0默认
     */
    @JSONField(name = "NegFlag")
    private Integer negFlag = 0;

    /**
     * 动态聚合id
     */
    @JSONField(name = "GroupMd5")
    private String groupMd5;

}
