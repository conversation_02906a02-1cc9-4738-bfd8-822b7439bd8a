package com.qcc.udf.casesearch_v3.tmp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.qcc.udf.casesearch_v3.entity.input.CaseRoleEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.qcc.udf.casesearch_v3.tmp.CaseDebtGroupUDF.convertDecimal;

/**
 * <AUTHOR>
 * @date 2022年06月07日 17:49
 */
public class CaseDebtCaseUDF extends UDF {
    public static void main(String[] args) {
        String json = "[{\"keyno\":\"8983fed53526b75ede4f646f7e048eee\",\"money\":\"701364814\",\"sx_money\":null,\"caseid\":\"********************************\",\"proinfo\":\"[{\\\"N\\\":\\\"022a6e75940b66332d6f7131900b0569\\\",\\\"O\\\":0,\\\"P\\\":\\\"中国民生银行股份有限公司青岛分行\\\"}]\",\"type\":\"CASE-2\",\"casereason\":\"金融借款合同纠纷\"}, {\"keyno\":\"8983fed53526b75ede4f646f7e048eee\",\"money\":\"\",\"sx_money\":null,\"caseid\":\"1fbffac7739e00e816cbecdbef97782e\",\"proinfo\":\"[]\",\"type\":\"XG\",\"casereason\":\"\"}, {\"keyno\":\"8983fed53526b75ede4f646f7e048eee\",\"money\":\"\",\"sx_money\":null,\"caseid\":\"6521ff1ebd0a379119b7c477cfb0e5d0\",\"proinfo\":\"[]\",\"type\":\"XG\",\"casereason\":\"\"}, {\"keyno\":\"8983fed53526b75ede4f646f7e048eee\",\"money\":\"\",\"sx_money\":null,\"caseid\":\"fcf8545df61b517bc3a76eafe58aefd9\",\"proinfo\":\"[{\\\"N\\\":\\\"4500c1c252e664b1ebf9f5041fa4779e\\\",\\\"O\\\":0,\\\"P\\\":\\\"中信银行股份有限公司呼和浩特分行\\\"}]\",\"type\":\"XG\",\"casereason\":\"信用证纠纷\"}, {\"keyno\":\"8983fed53526b75ede4f646f7e048eee\",\"money\":\"\",\"sx_money\":\"153117272\",\"caseid\":\"5cea1842e22a298468cb03856f04d377\",\"proinfo\":\"[]\",\"type\":\"SX\",\"casereason\":\"\"}]";
        System.out.println(evaluate(JSONArray.parseArray(json, String.class)));
    }

    public static String evaluate(List<String> jsonList) {
        Set<String> caseIdSet = new HashSet<>();
        List<CaseRoleEntity> allRoleList = new ArrayList<>();
        BigDecimal sumMoney = BigDecimal.ZERO;
        for (String str : jsonList) {
            JSONObject json = JSON.parseObject(str);
            String keyno = json.getString("keyno");
            String type = json.getString("type");
            String money = json.getString("money");
            String sx_money = json.getString("sx_money");
            String caseid = json.getString("caseid");
            String caseReason = json.getString("casereason");
            if (!CaseDebtUDF.REASON_SET.contains(caseReason)) {
                continue;
            }
            String proinfo = json.getString("proinfo");
            proinfo = proinfo == null ? "" : proinfo;
            if (proinfo.contains(keyno)) {
                continue;
            }
            BigDecimal moneyDic = convertDecimal(money);
            if ("SX".equals(type)) {
                moneyDic = convertDecimal(sx_money);
            }
            sumMoney = sumMoney.add(moneyDic);
            List<CaseRoleEntity> roleList = JSON.parseArray(proinfo, CaseRoleEntity.class);
            for (CaseRoleEntity caseRoleEntity : roleList) {
                caseRoleEntity.setT(caseid);
                caseRoleEntity.setD(moneyDic.toString());
            }
            allRoleList.addAll(roleList);
            caseIdSet.add(caseid);
        }

        Map<String, List<CaseRoleEntity>> keyNoDataList = allRoleList.stream()
                .filter(x -> !Strings.isNullOrEmpty(x.getN())).collect(Collectors.groupingBy(x -> x.getP()));
        Map<String, List<CaseRoleEntity>> noKeyNoDataList = allRoleList.stream()
                .filter(x -> Strings.isNullOrEmpty(x.getN())).collect(Collectors.groupingBy(x -> x.getP()));

        List<CaseDebtGroupSum> sumList = new ArrayList<>();
        keyNoDataList.forEach((k, v) -> {
            BigDecimal money = v.stream().map(x -> convertDecimal(x.getD())).reduce(BigDecimal.ZERO, BigDecimal::add);

            CaseDebtGroupSum sum = new CaseDebtGroupSum(v.get(0), money, v.size(),
                    v.stream().map(x -> x.getT()).sorted().collect(Collectors.toCollection(LinkedHashSet::new)));
            sumList.add(sum);
        });
        noKeyNoDataList.forEach((k, v) -> {
            BigDecimal money = v.stream().map(x -> convertDecimal(x.getD())).reduce(BigDecimal.ZERO, BigDecimal::add);

            CaseDebtGroupSum sum = new CaseDebtGroupSum(v.get(0), money, v.size(),
                    v.stream().map(x -> x.getT()).sorted().collect(Collectors.toCollection(LinkedHashSet::new)));
            sumList.add(sum);
        });

        Map<String, Object> result = new HashMap<>();
        result.put("ids", caseIdSet.stream().sorted().collect(Collectors.toCollection(LinkedHashSet::new)));
        result.put("case_count", caseIdSet.size());
        result.put("group_list", sumList);
        result.put("sum_money", sumMoney);

        return JSON.toJSONString(result);
    }
}

@Data
@AllArgsConstructor
@NoArgsConstructor
class CaseDebtGroupSum {
    private CaseRoleEntity nameKeyNo;
    private BigDecimal money;
    private int count;
    private Set<String> idList;
}