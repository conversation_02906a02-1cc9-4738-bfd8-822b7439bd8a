package com.qcc.udf.casesearch_v3.entity.output;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AmtInfo {
    /**
     * 金额类型
     */
    @JSONField(name = "Type")
    private String type;
    /**
     * 案件金额
     */
    @JSONField(name = "Amt")
    private String amt;

    /**
     * 案件金额状态(0-历史 1-有效)
     */
    @JSONField(name = "IsValid")
    private String isValid;

    /**
     * 时间戳（中间计算用）
     */
    @JSONField(serialize = false)
    private Long timeStamp;
    @JSONField(serialize = false)
    private String keyNo;
}
