package com.qcc.udf.casesearch_v3.util;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.qcc.udf.casesearch_v3.entity.input.*;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.casesearch_v3.entity.tmp.RiskLawsKeyNoArray;
import com.qcc.udf.casesearch_v3.enums.CaseCategoryEnum;
import com.qcc.udf.casesearch_v3.enums.CaseRoleEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021年07月15日 15:02
 */
public class PartyDiffUtil {
    /**
     * 生成单个审理程序原被告信息
     *
     * @param infoList
     * @param trialRound
     * @return
     */
    public static Map<String, List<NameAndKeyNoEntity>> getYgBgList(List<BaseCaseEntity> infoList, String trialRound) {
        List<NameAndKeyNoEntity> proList = new LinkedList<>();
        List<NameAndKeyNoEntity> defList = new LinkedList<>();

        List<BaseCaseEntity> cpwsList = new ArrayList<>();
        List<BaseCaseEntity> ktggList = new ArrayList<>();
        List<BaseCaseEntity> laList = new ArrayList<>();
        List<BaseCaseEntity> sdggList = new ArrayList<>();
        List<BaseCaseEntity> fyggList = new ArrayList<>();

        List<BaseCaseEntity> sxList = new ArrayList<>();
        List<BaseCaseEntity> zxList = new ArrayList<>();
        List<BaseCaseEntity> xgList = new ArrayList<>();
        List<BaseCaseEntity> pcczList = new ArrayList<>();
        List<BaseCaseEntity> zbList = new ArrayList<>();
        List<BaseCaseEntity> xjpgList = new ArrayList<>();
        List<BaseCaseEntity> gqdjList = new ArrayList<>();

        List<BaseCaseEntity> sfpmList = new ArrayList<>();
        List<BaseCaseEntity> sqtjList = new ArrayList<>();
        List<BaseCaseEntity> xzcjList = new ArrayList<>();
        List<BaseCaseEntity> xdpgjgList = new ArrayList<>();
        List<BaseCaseEntity> xsggList = new ArrayList<>();


        for (BaseCaseEntity item : infoList) {
            if (CaseCategoryEnum.CPWS.equals(item.getBaseCaseCategoryEnum())) {
                cpwsList.add(item);
            }
            if (CaseCategoryEnum.KTGG.equals(item.getBaseCaseCategoryEnum())) {
                ktggList.add(item);
            }
            if (CaseCategoryEnum.LA.equals(item.getBaseCaseCategoryEnum())) {
                laList.add(item);
            }
            if (CaseCategoryEnum.SDGG.equals(item.getBaseCaseCategoryEnum())) {
                sdggList.add(item);
            }
            if (CaseCategoryEnum.FYGG.equals(item.getBaseCaseCategoryEnum())) {
                fyggList.add(item);
            }

            if (CaseCategoryEnum.SX.equals(item.getBaseCaseCategoryEnum())) {
                sxList.add(item);
            }
            if (CaseCategoryEnum.ZX.equals(item.getBaseCaseCategoryEnum())) {
                zxList.add(item);
            }
            if (CaseCategoryEnum.XG.equals(item.getBaseCaseCategoryEnum())) {
                xgList.add(item);
            }
            if (CaseCategoryEnum.PCCZ.equals(item.getBaseCaseCategoryEnum())) {
                pcczList.add(item);
            }
            if (CaseCategoryEnum.ZB.equals(item.getBaseCaseCategoryEnum())) {
                zbList.add(item);
            }
            if (CaseCategoryEnum.XJPG.equals(item.getBaseCaseCategoryEnum())) {
                xjpgList.add(item);
            }
            if (CaseCategoryEnum.GQDJ.equals(item.getBaseCaseCategoryEnum())) {
                gqdjList.add(item);
            }
            if (CaseCategoryEnum.SFPM.equals(item.getBaseCaseCategoryEnum())) {
                sfpmList.add(item);
            }
            if (CaseCategoryEnum.SQTJ.equals(item.getBaseCaseCategoryEnum())) {
                sqtjList.add(item);
            }
            if (CaseCategoryEnum.XZCJ.equals(item.getBaseCaseCategoryEnum())) {
                xzcjList.add(item);
            }
            if (CaseCategoryEnum.XDPGJG.equals(item.getBaseCaseCategoryEnum())) {
                xdpgjgList.add(item);
            }
            if (CaseCategoryEnum.XSGG.equals(item.getBaseCaseCategoryEnum())) {
                xsggList.add(item);
            }
        }


        //单个审理流程是否都由破产组成(破产原被告需要单独处理)
        boolean isOnlyPCCZ = pcczList.size() == infoList.size();

        if (CollectionUtils.isNotEmpty(cpwsList)) {
            Map<String, List<CPWSLawsuitResult>> allResultMap = new HashMap<>();
            //当事人数量倒序 案件日期倒序 获取到最完整的案件当事人
            cpwsList = cpwsList.stream()
                    .sorted(Comparator.comparing(BaseCaseEntity::getRolePartyCount, Comparator.reverseOrder())
                            .thenComparing(BaseCaseEntity::getCaseTimeStamp, Comparator.reverseOrder()))
                    .collect(Collectors.toList());

            for (BaseCaseEntity item : cpwsList) {
                CPWSEntity cpwsEntity = CommonV3Util.convert(item, CPWSEntity.class);

                if (CollectionUtils.isEmpty(proList) && CollectionUtils.isEmpty(defList)) {
                    proList.addAll(CommonV3Util.getCPWSRoleList(cpwsEntity.getProsecutor(), cpwsEntity.getCaseRoleEntityList()));
                    defList.addAll(CommonV3Util.getCPWSRoleList(cpwsEntity.getDefendant(), cpwsEntity.getCaseRoleEntityList()));
                }

                //补充所有裁判文书判决结果信息
                if (CollectionUtils.isNotEmpty(cpwsEntity.getCaseRoleEntityList())) {
                    for (CPWSLawsuitResult result : cpwsEntity.getLawsuitResultEntityList()) {
                        //判决日期 判决结果标签排序用
                        result.setTimeStamp(cpwsEntity.getJudgedate());
                        if (!Strings.isNullOrEmpty(result.getN())) {
                            List<CPWSLawsuitResult> tagList = allResultMap.get(result.getN());
                            if (tagList == null) {
                                tagList = new ArrayList<>();
                            }
                            tagList.add(result);
                            allResultMap.put(result.getN(), tagList);
                        }
                        if (!Strings.isNullOrEmpty(result.getP()) && !Strings.isNullOrEmpty(result.getP().trim())) {
                            String key = CommonV3Util.full2Half(result.getP().trim());
                            List<CPWSLawsuitResult> tagList = allResultMap.get(key);
                            if (tagList == null) {
                                tagList = new ArrayList<>();
                            }
                            tagList.add(result);
                            allResultMap.put(key, tagList);
                        }
                    }
                }

//                System.out.println(allResultMap);
            }

            for (NameAndKeyNoEntity entity : proList) {
                String key = CommonV3Util.full2Half(entity.getName().trim());
                List<CPWSLawsuitResult> tagList = allResultMap.get(key);
                if (CollectionUtils.isNotEmpty(tagList)) {
                    //按判决日期升序获取判决结果标签v1
                    Set<String> tagSet = tagList.stream().sorted(Comparator.comparing(CPWSLawsuitResult::getTimeStamp))
                            .map(CPWSLawsuitResult::getT)
                            .collect(Collectors.toCollection(LinkedHashSet::new));
                    entity.setLawsuitResult(tagSet.stream().collect(Collectors.joining(",")));
                    //按判决日期升序获取判决结果标签v2
                    Set<String> tagSetV2 = tagList.stream().filter(e -> StringUtils.isNotEmpty(e.getJR()) && !e.getJR().equals("null")).sorted(Comparator.comparing(CPWSLawsuitResult::getTimeStamp))
                            .map(CPWSLawsuitResult::getJR)
                            .collect(Collectors.toCollection(LinkedHashSet::new));
                    entity.setLawsuitResultV2(tagSetV2.stream().collect(Collectors.joining(",")));
                }

            }
            for (NameAndKeyNoEntity entity : defList) {
                String key = CommonV3Util.full2Half(entity.getName().trim());
                List<CPWSLawsuitResult> tagList = allResultMap.get(key);
                if (CollectionUtils.isNotEmpty(tagList)) {
                    //按判决日期升序获取判决结果标签v1
                    Set<String> tagSet = tagList.stream().sorted(Comparator.comparing(CPWSLawsuitResult::getTimeStamp))
                            .map(CPWSLawsuitResult::getT)
                            .collect(Collectors.toCollection(LinkedHashSet::new));
                    entity.setLawsuitResult(tagSet.stream().collect(Collectors.joining(",")));
                    //按判决日期升序获取判决结果标签v2
                    Set<String> tagSetV2 = tagList.stream().filter(e -> StringUtils.isNotEmpty(e.getJR()) && !e.getJR().equals("null")).sorted(Comparator.comparing(CPWSLawsuitResult::getTimeStamp))
                            .map(CPWSLawsuitResult::getJR)
                            .collect(Collectors.toCollection(LinkedHashSet::new));
                    entity.setLawsuitResultV2(tagSetV2.stream().collect(Collectors.joining(",")));
                }
            }
        }
        if (CollectionUtils.isEmpty(proList) && CollectionUtils.isEmpty(defList)) {
            List<NameAndKeyNoEntity> proAllList = new ArrayList<>();
            List<NameAndKeyNoEntity> defAllList = new ArrayList<>();

            for (BaseCaseEntity item : ktggList) {
                KTGGEntity entity = CommonV3Util.convert(item, KTGGEntity.class);
                proAllList.addAll(CommonV3Util.getCommonRoleList(entity.getProsecutorlistoEntityList(), CaseRoleEnum.ROLE_A.getRoleName()));
                defAllList.addAll(CommonV3Util.getCommonRoleList(entity.getDefendantlistoEntityList(), CaseRoleEnum.ROLE_B.getRoleName()));
            }

            for (BaseCaseEntity item : laList) {
                if (CollectionUtils.isEmpty(proList) && CollectionUtils.isEmpty(defList)) {
                    LAEntity entity = CommonV3Util.convert(item, LAEntity.class);
                    proAllList.addAll(CommonV3Util.getCommonRoleList(entity.getProsecutorEntityList(), CaseRoleEnum.ROLE_A.getRoleName()));
                    defAllList.addAll(CommonV3Util.getCommonRoleList(entity.getDefendantEntityList(), CaseRoleEnum.ROLE_B.getRoleName()));
                }
            }

            for (BaseCaseEntity item : sqtjList) {
                if (CollectionUtils.isEmpty(proList) && CollectionUtils.isEmpty(defList)) {
                    SQTJEntity entity = CommonV3Util.convert(item, SQTJEntity.class);
                    proAllList.addAll(CommonV3Util.getCommonRoleList(entity.getProsecutorEntityList(), CaseRoleEnum.ROLE_A.getRoleName()));
                    defAllList.addAll(CommonV3Util.getCommonRoleList(entity.getDefendantEntityList(), CaseRoleEnum.ROLE_B.getRoleName()));
                }
            }

            for (BaseCaseEntity item : sdggList) {
                if (CollectionUtils.isEmpty(proList) && CollectionUtils.isEmpty(defList)) {
                    SDGGEntity entity = CommonV3Util.convert(item, SDGGEntity.class);
                    proAllList.addAll(CommonV3Util.getCommonRoleList(entity.getProsecutorlistoEntityList(), CaseRoleEnum.ROLE_A.getRoleName()));
                    defAllList.addAll(CommonV3Util.getCommonRoleList(entity.getDefendantlistoEntityList(), CaseRoleEnum.ROLE_B.getRoleName()));
                }
            }

            for (BaseCaseEntity item : fyggList) {
                if (CollectionUtils.isEmpty(proList) && CollectionUtils.isEmpty(defList)) {
                    FYGGEntity entity = CommonV3Util.convert(item, FYGGEntity.class);
                    proAllList.addAll(CommonV3Util.getCommonRoleList(entity.getProsecutorlistoEntityList(), CaseRoleEnum.ROLE_A.getRoleName()));
                    defAllList.addAll(CommonV3Util.getCommonRoleList(entity.getDefendantlistoEntityList(), CaseRoleEnum.ROLE_B.getRoleName()));
                }
            }

            for (BaseCaseEntity item : fyggList) {
                if (CollectionUtils.isEmpty(proList) && CollectionUtils.isEmpty(defList)) {
                    FYGGEntity entity = CommonV3Util.convert(item, FYGGEntity.class);
                    proAllList.addAll(CommonV3Util.getCommonRoleList(entity.getProsecutorlistoEntityList(), CaseRoleEnum.ROLE_A.getRoleName()));
                    defAllList.addAll(CommonV3Util.getCommonRoleList(entity.getDefendantlistoEntityList(), CaseRoleEnum.ROLE_B.getRoleName()));
                }
            }

            for (BaseCaseEntity item : sxList) {
                SXEntity entity = CommonV3Util.convert(item, SXEntity.class);
                List<NameAndKeyNoEntity> roleList = CommonV3Util.getCommonRoleList(entity.getNameandkeynoEntityList(), CaseRoleEnum.ROLE_C.getRoleName());
                defAllList.addAll(roleList);
            }

            for (BaseCaseEntity item : zxList) {
                ZXEntity entity = CommonV3Util.convert(item, ZXEntity.class);
                List<NameAndKeyNoEntity> roleList = CommonV3Util.getCommonRoleList(entity.getNameandkeynoEntityList(), CaseRoleEnum.ROLE_C.getRoleName());
                defAllList.addAll(roleList);
            }

            for (BaseCaseEntity item : xgList) {
                XGEntity entity = CommonV3Util.convert(item, XGEntity.class);
                List<NameAndKeyNoEntity> roleList = CommonV3Util.getCommonRoleList(entity.getXglArrayEntityList(), CaseRoleEnum.ROLE_C.getRoleName());
                defAllList.addAll(roleList);

            }

            //破产重组存在申请人被申请人重叠的情况
            List<NameAndKeyNoEntity> pcczProAllList = new ArrayList<>();
            List<NameAndKeyNoEntity> pcczDefAllList = new ArrayList<>();

            for (BaseCaseEntity item : pcczList) {
                if (CollectionUtils.isEmpty(proList) && CollectionUtils.isEmpty(defList)) {
                    PCCZEntity entity = CommonV3Util.convert(item, PCCZEntity.class);
                    String caseType = entity.getCasetype();
                    List<NameAndKeyNoEntity> pro = CommonV3Util.getCommonRoleList(entity.getApplicantnameandkeynoEntityList(), CaseRoleEnum.ROLE_D.getRoleName());
                    List<NameAndKeyNoEntity> def = CommonV3Util.getCommonRoleList(entity.getRespondentnameandkeynoEntityList(), CaseRoleEnum.ROLE_E.getRoleName());
                    for (NameAndKeyNoEntity nk : pro) {
                        nk.setRole(pcczCaseRoleMapping(caseType, 1));
                    }
                    for (NameAndKeyNoEntity nk : def) {
                        nk.setRole(pcczCaseRoleMapping(caseType, 2));
                    }
                    if (CollectionUtils.isEmpty(pcczProAllList) && CollectionUtils.isEmpty(pcczDefAllList)) {
                        pcczProAllList.addAll(pro);
                        pcczDefAllList.addAll(def);
                    }
                    proAllList.addAll(pro);
                    defAllList.addAll(def);
                }
            }

            //终本
            if (CollectionUtils.isEmpty(proList) && CollectionUtils.isEmpty(defList)
                    && CollectionUtils.isNotEmpty(zbList)) {
                for (BaseCaseEntity item : zbList) {
                    if (CollectionUtils.isEmpty(proList) && CollectionUtils.isEmpty(defList)) {
                        ZBEntity entity = CommonV3Util.convert(item, ZBEntity.class);
                        defAllList.addAll(CommonV3Util.getCommonRoleList(entity.getNameAndKeyNoEntityList(), CaseRoleEnum.ROLE_C.getRoleName()));
                    }
                }
            }

            for (BaseCaseEntity item : xjpgList) {
                if (CollectionUtils.isEmpty(proList) && CollectionUtils.isEmpty(defList)) {
                    XJPGEntity entity = CommonV3Util.convert(item, XJPGEntity.class);
                    defAllList.addAll(CommonV3Util.getCommonRoleList(entity.getNameandkeynoEntityList(), CaseRoleEnum.ROLE_X.getRoleName()));
                }
            }

            for (BaseCaseEntity item : gqdjList) {
                if (CollectionUtils.isEmpty(proList) && CollectionUtils.isEmpty(defList)) {
                    GQDJEntity entity = CommonV3Util.convert(item, GQDJEntity.class);
                    defAllList.addAll(CommonV3Util.getCommonRoleList(entity.getZxnameandkeynoEntityList(), CaseRoleEnum.ROLE_C.getRoleName()));
                }
            }

            for (BaseCaseEntity item : sfpmList) {
                if (CollectionUtils.isEmpty(proList) && CollectionUtils.isEmpty(defList)) {
                    SFPMEntity entity = CommonV3Util.convert(item, SFPMEntity.class);
                    defAllList.addAll(CommonV3Util.getCommonRoleList(entity.getOwnerkeynoarrayEntityList(), CaseRoleEnum.ROLE_X.getRoleName()));
                }
            }

            for (BaseCaseEntity item : xzcjList) {
                if (CollectionUtils.isEmpty(proList) && CollectionUtils.isEmpty(defList)) {
                    XZCJEntity entity = CommonV3Util.convert(item, XZCJEntity.class);

                    //被执行人
                    List<NameAndKeyNoEntity> executedList = CommonV3Util.getCommonRoleList(entity.getKeynojsonexecutedEntityList()
                            , CaseRoleEnum.ROLE_C.getRoleName());
                    defList.addAll(executedList);

                    //限制出境对象
                    List<NameAndKeyNoEntity> limitedList = CommonV3Util.getCommonRoleList(entity.getKeynojsonperslimitedEntityList()
                            , CaseRoleEnum.ROLE_X.getRoleName());
                    //排除限制出境对象中的被执行人
                    Set<String> set = new HashSet<>();
                    for (NameAndKeyNoEntity data : executedList) {
                        if (!Strings.isNullOrEmpty(data.getKeyNo())) {
                            set.add(data.getKeyNo());
                        }
                        if (!Strings.isNullOrEmpty(data.getName())) {
                            set.add(data.getName());
                        }
                    }
                    for (NameAndKeyNoEntity data : limitedList) {
                        if (set.contains(data.getKeyNo()) || set.contains(data.getName())) {
                            continue;
                        }
                        defAllList.add(data);
                    }
                }
            }

//            for (BaseCaseEntity item : xdpgjgList) {
//                if (CollectionUtils.isEmpty(proList) && CollectionUtils.isEmpty(defList)) {
//                    XDPGJGEntity entity = CommonV3Util.convert(item, XDPGJGEntity.class);
//                    proAllList.addAll(CommonV3Util.getCommonRoleList(entity.getKeynoaarrayownerEntityList(), CaseRoleEnum.ROLE_X.getRoleName()));
//                }
//            }

            for (NameAndKeyNoEntity data : proAllList) {
                if (Strings.isNullOrEmpty(data.getKeyNo())) {
                    data.setKeyNo("");
                }
            }

            for (NameAndKeyNoEntity data : defAllList) {
                if (Strings.isNullOrEmpty(data.getKeyNo())) {
                    data.setKeyNo("");
                }
            }

            RiskLawsKeyNoArray riskLawsKeyNoArray = riskLawsKeyNoDistinct(proAllList, defAllList, new ArrayList<>());
            proList = riskLawsKeyNoArray.getPlaintiff();
            defList = riskLawsKeyNoArray.getDefendant();

            if (isOnlyPCCZ) {
                proList = pcczProAllList;
                defList = pcczDefAllList;
            }

        }

        //份匹配失败需要再处理(获取当事人信息)
        if (CollectionUtils.isEmpty(proList) && CollectionUtils.isEmpty(defList)) {
            List<NameAndKeyNoEntity> partyList = CommonV3Util.getPartyInfo(cpwsList, ktggList, laList, sdggList, fyggList, sxList, zxList,
                    xgList, pcczList, zbList, xjpgList, gqdjList, xsggList);
            proList.addAll(partyList);
        }

        Map<String, List<NameAndKeyNoEntity>> map = new HashMap<>();
        //按照姓名排序,避免增量更新过多
        proList.sort(Comparator.comparing(a -> CommonV3Util.getString(a.getName())));
        defList.sort(Comparator.comparing(a -> CommonV3Util.getString(a.getName())));
        for (NameAndKeyNoEntity data : proList) {
            if (CollectionUtils.isEmpty(data.getLawFirmList())) {
                data.setLawFirmList(null);
            }
        }
        for (NameAndKeyNoEntity data : defList) {
            if (CollectionUtils.isEmpty(data.getLawFirmList())) {
                data.setLawFirmList(null);
            }
        }


        //生成打码当事人映射关系
        //http://doc.greatld.com/pages/viewpage.action?pageId=34071011
        Map<String, Map<String, NameAndKeyNoEntity>> nameMapping = getAllRoleInfo(proList, defList, cpwsList, ktggList, laList, sqtjList, sdggList, fyggList, sxList, zxList,
                xgList, pcczList, zbList, xjpgList, gqdjList, sfpmList, xzcjList);
        Map<String, NameAndKeyNoEntity> proMapping = nameMapping.get("proMapping");
        Map<String, NameAndKeyNoEntity> defMapping = nameMapping.get("defMapping");
        for (NameAndKeyNoEntity item : proList) {
            NameAndKeyNoEntity nk = proMapping.get(item.getName());
            if (nk != null) {
                item.setName(nk.getName());
                item.setKeyNo(nk.getKeyNo());
            }
            if (item.getKeyNo() == null) {
                item.setKeyNo("");
            }
        }
        for (NameAndKeyNoEntity item : defList) {
            NameAndKeyNoEntity nk = defMapping.get(item.getName());
            if (nk != null) {
                item.setName(nk.getName());
                item.setKeyNo(nk.getKeyNo());
            }
            if (item.getKeyNo() == null) {
                item.setKeyNo("");
            }
        }
        //存在重复数据的可以去重，不然会影响最终当事人生成排序
        if (proList.stream().collect(Collectors.groupingBy(NameAndKeyNoEntity::getName)).size() != proList.size()) {
            proList = proList.stream().collect(Collectors.groupingBy(NameAndKeyNoEntity::getName,
                    Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(NameAndKeyNoEntity::getKeyNo).reversed())
                            , Optional::get))).values().stream().collect(Collectors.toList());
        }
        if (defList.stream().collect(Collectors.groupingBy(NameAndKeyNoEntity::getName)).size() != defList.size()) {
            defList = defList.stream().collect(Collectors.groupingBy(NameAndKeyNoEntity::getName,
                    Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(NameAndKeyNoEntity::getKeyNo).reversed())
                            , Optional::get))).values().stream().collect(Collectors.toList());
        }

        // 5.***霞、某某霞，也做去重处理
        proList = mergeXXName(proList);
        defList = mergeXXName(defList);

        //优化二审、再审Role名称
        //https://thoughts.teambition.com/workspaces/5e82de85394160001ab827ed/docs/619b31a9ed59a900015babd2
        proList = reNameRoleName(proList, trialRound);
        defList = reNameRoleName(defList, trialRound);

        map.put("prosecutor", proList);
        map.put("defendant", defList);

        return map;
    }

    public static List<NameAndKeyNoEntity> mergeXXName(List<NameAndKeyNoEntity> nameKeyList) {
        Map<String, List<NameAndKeyNoEntity>> mapping = new HashMap<>();
        for (NameAndKeyNoEntity item : nameKeyList) {
            String name = item.getName().replaceAll("某|\\*|X|x|ｘ|Ｘ", "");
            if (!Objects.equals(name, item.getName())) {
                List<NameAndKeyNoEntity> list = mapping.getOrDefault(name, new ArrayList<>());
                list.add(item);
                mapping.put(name, list);
            }
        }
        Map<String, NameAndKeyNoEntity> shortNameMapping = new HashMap<>();
        mapping.forEach((k, v) -> {
            if (v.size() > 1) {
                shortNameMapping.put(k, v.stream().sorted(Comparator.comparing(NameAndKeyNoEntity::getName)).findFirst().get());
            }
        });
        for (NameAndKeyNoEntity item : nameKeyList) {
            String name = item.getName().replaceAll("某|\\*|X|x|ｘ|Ｘ", "");
            NameAndKeyNoEntity nameKey = shortNameMapping.get(name);
            if (nameKey != null) {
                item.setName(nameKey.getName());
                item.setKeyNo(nameKey.getKeyNo());
                item.setOrg(nameKey.getOrg());
            }
        }
        //存在映射关系排序后出餐
        if (shortNameMapping.size() > 0) {
            return nameKeyList.stream().collect(Collectors.groupingBy(NameAndKeyNoEntity::getName,
                    Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(NameAndKeyNoEntity::getKeyNo).reversed())
                            , Optional::get))).values().stream().collect(Collectors.toList());
        } else {
            return nameKeyList;
        }

    }

    /**
     * 破产崇祯身份映射
     *
     * @param caseType
     * @param type
     * @return
     */
    public static String pcczCaseRoleMapping(String caseType, int type) {
        Set<String> role1 = Sets.newHashSet("破产审查案件", "破产案件", "强制清算申请审查案件", "强制清算案件");
        Set<String> role2 = Sets.newHashSet("强制清算上诉案件", "破产上诉案件");
        Set<String> role3 = Sets.newHashSet("破产监督案件", "强制清算监督案件");
        if (role1.contains(caseType.trim()) || Strings.isNullOrEmpty(caseType)) {
            if (type == 1) {
                return CaseRoleEnum.ROLE_D.getRoleName();
            } else {
                return CaseRoleEnum.ROLE_E.getRoleName();
            }
        }
        if (role2.contains(caseType.trim())) {
            if (type == 1) {
                return "上诉人";
            } else {
                return "被上诉人";
            }
        }
        if (role3.contains(caseType.trim())) {
            if (type == 1) {
                return "原审申请人";
            } else {
                return "原审被申请人";
            }
        }

        //兜底
        if (type == 1) {
            return CaseRoleEnum.ROLE_D.getRoleName();
        } else {
            return CaseRoleEnum.ROLE_E.getRoleName();
        }

    }

    /**
     * 优化案件身份展示
     *
     * @param nameKeyNoList
     * @param trialRound
     * @return
     */
    public static List<NameAndKeyNoEntity> reNameRoleName(List<NameAndKeyNoEntity> nameKeyNoList, String trialRound) {
        if (CollectionUtils.isEmpty(nameKeyNoList)) {
            return nameKeyNoList;
        }
        for (NameAndKeyNoEntity item : nameKeyNoList) {
            item.setRole(getSpecialRoleName(item.getRole(), trialRound));
        }
        return nameKeyNoList;

    }

    /**
     * 根据审理流程返回特殊Role名称
     *
     * @param role
     * @param trialRound
     * @return
     */
    public static String getSpecialRoleName(String role, String trialRound) {
        if (trialRound != null && trialRound.contains("二审")) {
            if ("原告".equals(role)) {
                return "上诉人";
            }
            if ("被告".equals(role)) {
                return "被上诉人";
            }
        }
        if (trialRound != null && trialRound.contains("再审")) {
            if ("原告".equals(role)) {
                return "申请人";
            }
            if ("被告".equals(role)) {
                return "被申请人";
            }
        }
        return role;
    }

    public static RiskLawsKeyNoArray riskLawsKeyNoDistinct(List<NameAndKeyNoEntity> plaintiff, List<NameAndKeyNoEntity> defendant, List<NameAndKeyNoEntity> thirdParty) {
        List<NameAndKeyNoEntity> similarList = new ArrayList();
        List<String> similarKeyList = new ArrayList();

        //去除某某
        plaintiff = plaintiff.stream().filter(data -> !isMouMouPerson(data.getName()) && !Strings.isNullOrEmpty(data.getName())).collect(Collectors.toList());
        defendant = defendant.stream().filter(data -> !isMouMouPerson(data.getName()) && !Strings.isNullOrEmpty(data.getName())).collect(Collectors.toList());
        thirdParty = thirdParty.stream().filter(data -> !isMouMouPerson(data.getName()) && !Strings.isNullOrEmpty(data.getName())).collect(Collectors.toList());


        Map<String, NameAndKeyNoEntity> plaintiffMap = (Map) plaintiff.stream().collect(Collectors.toMap((k) -> {
            return combineKey(k);
        }, Function.identity(), (key1, key2) -> {
            //优先取更加明确的Role信息
            if (Objects.equals(key1.getRole(), CaseRoleEnum.ROLE_D.getRoleName())) {
                return key1;
            }
            if (Objects.equals(key2.getRole(), CaseRoleEnum.ROLE_D.getRoleName())) {
                return key2;
            }
            return key2;
        }));

        Map<String, NameAndKeyNoEntity> defendantMap = (Map) defendant.stream().collect(Collectors.toMap((k) -> {
            return combineKey(k);
        }, Function.identity(), (key1, key2) -> {
            //优先取更加明确的Role信息
            if (Objects.equals(key1.getRole(), CaseRoleEnum.ROLE_C.getRoleName())
                    || Objects.equals(key1.getRole(), CaseRoleEnum.ROLE_E.getRoleName())) {
                return key1;
            }
            if (Objects.equals(key2.getRole(), CaseRoleEnum.ROLE_C.getRoleName())
                    || Objects.equals(key2.getRole(), CaseRoleEnum.ROLE_E.getRoleName())) {
                return key2;
            }
            return key2;
        }));
        Map<String, NameAndKeyNoEntity> thirdPartyMap = (Map) thirdParty.stream().collect(Collectors.toMap((k) -> {
            return combineKey(k);
        }, Function.identity(), (key1, key2) -> {
            return key2;
        }));
        List<NameAndKeyNoEntity> plaintiffNew = new ArrayList();
        plaintiffMap.forEach((k, v) -> {
            NameAndKeyNoEntity item = v;
            String key = combineKey(item);
            if (!defendantMap.containsKey(key) && !thirdPartyMap.containsKey(key)) {
                plaintiffNew.add(item);
            } else if (!similarKeyList.contains(key)) {
                similarKeyList.add(key);
                similarList.add(item);
            }

        });
        List<NameAndKeyNoEntity> defendantNew = new ArrayList();
        defendantMap.forEach((k, v) -> {
            NameAndKeyNoEntity item = v;
            String key = combineKey(item);
            if (!similarKeyList.contains(key)) {
                if (thirdPartyMap.containsKey(key)) {
                    similarKeyList.add(key);
                    similarList.add(item);
                } else {
                    defendantNew.add(item);
                }
            }

        });
        List<NameAndKeyNoEntity> thirdPartyNew = new ArrayList();
        thirdPartyMap.forEach((k, v) -> {
            NameAndKeyNoEntity item = v;
            String key = combineKey(item);
            if (!similarKeyList.contains(key)) {
                thirdPartyNew.add(item);
            }

        });

        //过滤同名字的人  保留有keyNo那个
        Map<String, NameAndKeyNoEntity> plaintiffInfoMap = plaintiffNew.stream().collect(Collectors.groupingBy(NameAndKeyNoEntity::getName
                , Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(NameAndKeyNoEntity::getKeyNo))
                        , Optional::get)));
        List<NameAndKeyNoEntity> plaintiffFinal = new ArrayList<>(plaintiffInfoMap.values());

        Map<String, NameAndKeyNoEntity> defendantInfoMap = defendantNew.stream().collect(Collectors.groupingBy(NameAndKeyNoEntity::getName
                , Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(NameAndKeyNoEntity::getKeyNo))
                        , Optional::get)));
        List<NameAndKeyNoEntity> defendantFinal = new ArrayList<>(defendantInfoMap.values());

        Map<String, NameAndKeyNoEntity> thirdPartyInfoMap = thirdPartyNew.stream().collect(Collectors.groupingBy(NameAndKeyNoEntity::getName
                , Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(NameAndKeyNoEntity::getKeyNo))
                        , Optional::get)));
        List<NameAndKeyNoEntity> thirdPartyFinal = new ArrayList<>(thirdPartyInfoMap.values());


        List<NameAndKeyNoEntity> keyNoArray = new ArrayList();
        if (CollectionUtils.isNotEmpty(plaintiffFinal)) {
            keyNoArray.addAll(plaintiffFinal);
        }

        if (CollectionUtils.isNotEmpty(defendantFinal)) {
            keyNoArray.addAll(defendantFinal);
        }

        if (CollectionUtils.isNotEmpty(thirdPartyFinal)) {
            keyNoArray.addAll(thirdPartyFinal);
        }

        if (CollectionUtils.isNotEmpty(similarList)) {
            keyNoArray.addAll(similarList);
        }

        return RiskLawsKeyNoArray.builder().plaintiff(plaintiffFinal).defendant(defendantFinal).thirdParty(thirdPartyFinal).keyNoArray(keyNoArray).build();
    }

    /**
     * 判断是否某
     *
     * @param name
     * @return
     */
    public static boolean isMouMouPerson(String name) {
        if (Strings.isNullOrEmpty(name)) {
            return false;
        }
        if (name.length() <= 4 && isUnknownName(name)) {
            return true;
        }
        return false;
    }

    public static String combineKey(NameAndKeyNoEntity NameAndKeyNoEntity) {
        return NameAndKeyNoEntity.getKeyNo() + "|" + NameAndKeyNoEntity.getName();
    }

    /**
     * 聚合所有当事人尝试解码当事人
     */
    public static Map<String, Map<String, NameAndKeyNoEntity>> getAllRoleInfo(List<NameAndKeyNoEntity> proList,
                                                                              List<NameAndKeyNoEntity> defdentList,
                                                                              List<BaseCaseEntity> cpwsList,
                                                                              List<BaseCaseEntity> ktggList,
                                                                              List<BaseCaseEntity> laList,
                                                                              List<BaseCaseEntity> sqtjList,
                                                                              List<BaseCaseEntity> sdggList,
                                                                              List<BaseCaseEntity> fyggList,
                                                                              List<BaseCaseEntity> sxList,
                                                                              List<BaseCaseEntity> zxList,
                                                                              List<BaseCaseEntity> xgList,
                                                                              List<BaseCaseEntity> pcczList,
                                                                              List<BaseCaseEntity> zbList,
                                                                              List<BaseCaseEntity> xjpgList,
                                                                              List<BaseCaseEntity> gqdjList,
                                                                              List<BaseCaseEntity> sfpmList,
                                                                              List<BaseCaseEntity> xzcjList) {
        //所有原被告集合(用于替换打码名字)
        List<NameAndKeyNoEntity> proAllList = new ArrayList<>();
        List<NameAndKeyNoEntity> defAllList = new ArrayList<>();

        for (BaseCaseEntity item : cpwsList) {
            CPWSEntity cpwsEntity = CommonV3Util.convert(item, CPWSEntity.class);
            distinctAdd(proAllList, (CommonV3Util.getCPWSRoleList(cpwsEntity.getProsecutor(), cpwsEntity.getCaseRoleEntityList())));
            distinctAdd(defAllList, CommonV3Util.getCPWSRoleList(cpwsEntity.getDefendant(), cpwsEntity.getCaseRoleEntityList()));
        }

        for (BaseCaseEntity item : ktggList) {
            KTGGEntity entity = CommonV3Util.convert(item, KTGGEntity.class);
            distinctAdd(proAllList, CommonV3Util.getCommonRoleList(entity.getProsecutorlistoEntityList(), CaseRoleEnum.ROLE_A.getRoleName()));
            distinctAdd(defAllList, CommonV3Util.getCommonRoleList(entity.getDefendantlistoEntityList(), CaseRoleEnum.ROLE_B.getRoleName()));
        }

        for (BaseCaseEntity item : laList) {
            LAEntity entity = CommonV3Util.convert(item, LAEntity.class);
            distinctAdd(proAllList, CommonV3Util.getCommonRoleList(entity.getProsecutorEntityList(), CaseRoleEnum.ROLE_A.getRoleName()));
            distinctAdd(defAllList, CommonV3Util.getCommonRoleList(entity.getDefendantEntityList(), CaseRoleEnum.ROLE_B.getRoleName()));
        }

        for (BaseCaseEntity item : sqtjList) {
            SQTJEntity entity = CommonV3Util.convert(item, SQTJEntity.class);
            distinctAdd(proAllList, CommonV3Util.getCommonRoleList(entity.getProsecutorEntityList(), CaseRoleEnum.ROLE_A.getRoleName()));
            distinctAdd(defAllList, CommonV3Util.getCommonRoleList(entity.getDefendantEntityList(), CaseRoleEnum.ROLE_B.getRoleName()));
        }

        for (BaseCaseEntity item : sdggList) {
            SDGGEntity entity = CommonV3Util.convert(item, SDGGEntity.class);
            distinctAdd(proAllList, CommonV3Util.getCommonRoleList(entity.getProsecutorlistoEntityList(), CaseRoleEnum.ROLE_A.getRoleName()));
            distinctAdd(defAllList, CommonV3Util.getCommonRoleList(entity.getDefendantlistoEntityList(), CaseRoleEnum.ROLE_B.getRoleName()));
        }

        for (BaseCaseEntity item : fyggList) {
            FYGGEntity entity = CommonV3Util.convert(item, FYGGEntity.class);
            distinctAdd(proAllList, CommonV3Util.getCommonRoleList(entity.getProsecutorlistoEntityList(), CaseRoleEnum.ROLE_A.getRoleName()));
            distinctAdd(defAllList, CommonV3Util.getCommonRoleList(entity.getDefendantlistoEntityList(), CaseRoleEnum.ROLE_B.getRoleName()));
        }


        for (BaseCaseEntity item : sxList) {
            SXEntity entity = CommonV3Util.convert(item, SXEntity.class);
            List<NameAndKeyNoEntity> roleList = CommonV3Util.getCommonRoleList(entity.getNameandkeynoEntityList(), CaseRoleEnum.ROLE_C.getRoleName());
            distinctAdd(defAllList, roleList);
        }

        for (BaseCaseEntity item : zxList) {
            ZXEntity entity = CommonV3Util.convert(item, ZXEntity.class);
            List<NameAndKeyNoEntity> roleList = CommonV3Util.getCommonRoleList(entity.getNameandkeynoEntityList(), CaseRoleEnum.ROLE_C.getRoleName());
            distinctAdd(defAllList, roleList);
        }

        for (BaseCaseEntity item : xgList) {
            XGEntity entity = CommonV3Util.convert(item, XGEntity.class);
            List<NameAndKeyNoEntity> roleList = CommonV3Util.getCommonRoleList(entity.getXglArrayEntityList(), CaseRoleEnum.ROLE_C.getRoleName());
            defAllList.addAll(roleList);

        }


        for (BaseCaseEntity item : pcczList) {
            PCCZEntity entity = CommonV3Util.convert(item, PCCZEntity.class);
            String caseType = entity.getCasetype();
            List<NameAndKeyNoEntity> pro = CommonV3Util.getCommonRoleList(entity.getApplicantnameandkeynoEntityList(), CaseRoleEnum.ROLE_D.getRoleName());
            List<NameAndKeyNoEntity> def = CommonV3Util.getCommonRoleList(entity.getRespondentnameandkeynoEntityList(), CaseRoleEnum.ROLE_E.getRoleName());
            distinctAdd(proAllList, pro);
            distinctAdd(defAllList, def);
        }

        for (BaseCaseEntity item : zbList) {
            ZBEntity entity = CommonV3Util.convert(item, ZBEntity.class);
            distinctAdd(defAllList, CommonV3Util.getCommonRoleList(entity.getNameAndKeyNoEntityList(), CaseRoleEnum.ROLE_C.getRoleName()));
        }

        for (BaseCaseEntity item : xjpgList) {
            XJPGEntity entity = CommonV3Util.convert(item, XJPGEntity.class);
            distinctAdd(defAllList, CommonV3Util.getCommonRoleList(entity.getNameandkeynoEntityList(), CaseRoleEnum.ROLE_X.getRoleName()));
        }

        for (BaseCaseEntity item : gqdjList) {
            GQDJEntity entity = CommonV3Util.convert(item, GQDJEntity.class);
            distinctAdd(defAllList, CommonV3Util.getCommonRoleList(entity.getZxnameandkeynoEntityList(), CaseRoleEnum.ROLE_C.getRoleName()));
        }

        for (BaseCaseEntity item : sfpmList) {
            SFPMEntity entity = CommonV3Util.convert(item, SFPMEntity.class);
            distinctAdd(proAllList, CommonV3Util.getCommonRoleList(entity.getOwnerkeynoarrayEntityList(), CaseRoleEnum.ROLE_X.getRoleName()));
        }

        for (BaseCaseEntity item : xzcjList) {
            XZCJEntity entity = CommonV3Util.convert(item, XZCJEntity.class);
            List<NameAndKeyNoEntity> defList = new ArrayList<>();
            //被执行人
            List<NameAndKeyNoEntity> executedList = CommonV3Util.getCommonRoleList(entity.getKeynojsonexecutedEntityList()
                    , CaseRoleEnum.ROLE_C.getRoleName());
            defList.addAll(executedList);

            //限制出境对象
            List<NameAndKeyNoEntity> limitedList = CommonV3Util.getCommonRoleList(entity.getKeynojsonperslimitedEntityList()
                    , CaseRoleEnum.ROLE_X.getRoleName());
            //排除限制出境对象中的被执行人
            Set<String> set = new HashSet<>();
            for (NameAndKeyNoEntity data : executedList) {
                if (!Strings.isNullOrEmpty(data.getKeyNo())) {
                    set.add(data.getKeyNo());
                }
                if (!Strings.isNullOrEmpty(data.getName())) {
                    set.add(data.getName());
                }
            }
            for (NameAndKeyNoEntity data : limitedList) {
                if (set.contains(data.getKeyNo()) || set.contains(data.getName())) {
                    continue;
                }
                defAllList.add(data);
            }
        }

        //去重聚合
        Map<String, NameAndKeyNoEntity> proGroup = proAllList.stream().collect(Collectors.groupingBy(NameAndKeyNoEntity::getName,
                Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(NameAndKeyNoEntity::getKeyNo).reversed())
                        , Optional::get)));
        Map<String, NameAndKeyNoEntity> defGroup = defAllList.stream().collect(Collectors.groupingBy(NameAndKeyNoEntity::getName,
                Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(NameAndKeyNoEntity::getKeyNo).reversed())
                        , Optional::get)));
        Map<String, NameAndKeyNoEntity> proMapping = findSimilarityName(proList,proGroup);
        Map<String, NameAndKeyNoEntity> defMapping = findSimilarityName(defdentList,defGroup);


        Map<String, Map<String, NameAndKeyNoEntity>> out = new HashMap<>();
        out.put("proMapping", proMapping);
        out.put("defMapping", defMapping);
        return out;
    }

    /***
     * 打码当事人解码
     * @param group
     * @return
     */
    public static Map<String, NameAndKeyNoEntity> findSimilarityName(List<NameAndKeyNoEntity> originalList,Map<String, NameAndKeyNoEntity> group) {
        Map<String, List<NameAndKeyNoEntity>> mappingList = new HashMap<>();
        Map<String, NameAndKeyNoEntity> resultMap = new HashMap<>();
        //已知名称的姓名
        Set<String> correctNameSet  = originalList.stream().filter(x -> !isUnknownName(x.getName()))
                .map(y -> y.getName()).collect(Collectors.toSet());

        group.forEach((k, v) -> {
            if (isUnknownName(k)) {
                if (k.contains("公司")) {
                    if (k.length() > 4) {
                        List<NameAndKeyNoEntity> matchNameList = new ArrayList<>();
                        group.forEach((x, y) -> {
                            if (!isUnknownName(x) && !correctNameSet.contains(x)) {
//                                List<String> list = Arrays.asList(x.split(""));
                                long sameCount = Arrays.asList(k.split("")).stream()
                                        .filter(a -> x.contains(a)).count();
                                //此处其实可以再加一个相似度算法 择优挑选，但是我不做
                                if (sameCount >= 4) {
                                    matchNameList.add(y);
                                }
                            }
                        });
                        mappingList.put(k, matchNameList);
                    }
                } else {
                    String prefix = k.substring(0, 1);
                    //前缀是否匹配成功
                    boolean match = false;
                    if (!isUnknownName(prefix)) {
                        List<NameAndKeyNoEntity> matchNameList = new ArrayList<>();
                        group.forEach((x, y) -> {
                            String prefixInner = x.substring(0, 1);
                            if (!isUnknownName(x)
                                    && !correctNameSet.contains(x)
                                    && Objects.equals(prefixInner, prefix)
                                    && x.length() == k.length()) {
                                matchNameList.add(y);
                            }
                        });
                        //匹配失败，放宽长度限制再来一次
                        if (CollectionUtils.isEmpty(matchNameList)) {
                            group.forEach((x, y) -> {
                                String prefixInner = x.substring(0, 1);
                                if (!isUnknownName(x)
                                        && !correctNameSet.contains(x)
                                        && Objects.equals(prefixInner, prefix)
                                        && Math.abs(x.length() - k.length()) < 2) {
                                    matchNameList.add(y);
                                }
                            });
                        }
                        mappingList.put(k, matchNameList);
                        match = CollectionUtils.isEmpty(matchNameList) ? false : true;
                    }
                    //通过结尾再匹配
                    String end = k.substring(k.length() - 1);
                    if (!match) {
                        if (!isUnknownName(end)) {
                            List<NameAndKeyNoEntity> matchNameList = new ArrayList<>();
                            if (CollectionUtils.isEmpty(matchNameList)) {
                                group.forEach((x, y) -> {
                                    String endInner = x.substring(x.length() - 1);
                                    if (!isUnknownName(x)
                                            && !correctNameSet.contains(x)
                                            && Objects.equals(endInner, end)
                                            && Math.abs(x.length() - k.length()) < 2) {
                                        matchNameList.add(y);
                                    }
                                });
                            }
                            mappingList.put(k, matchNameList);
                        }
                    }
                }
            }
        });

        //处理 张某一 张某二 张大 张傻 映射
        Map<String, Set<String>> sameGroupMap = new HashMap<>();
        mappingList.forEach((k, v) -> {
            if (v.size() > 1) {
                String key = JSON.toJSONString(v);
                Set<String> nameSet = sameGroupMap.getOrDefault(key, new HashSet<>());
                nameSet.add(k);
                sameGroupMap.put(JSON.toJSONString(v), nameSet);
            }
        });

        sameGroupMap.forEach((k, v) -> {
            List<NameAndKeyNoEntity> list = JSON.parseArray(k, NameAndKeyNoEntity.class).stream()
                    .sorted(Comparator.comparing(NameAndKeyNoEntity::getName)).collect(Collectors.toList());
            //形成对等映射关系既可以指鹿为马张冠李戴
            if (list.size() == v.size()) {
                List<String> vList = Lists.newArrayList(v);
                for (int i = 0; i < vList.size(); i++) {
                    //覆盖原本多关系
                    mappingList.put(vList.get(i), Lists.newArrayList(list.get(i)));
                }
            }
        });

        //去除关联不等于1的数据
        mappingList.forEach((k, v) -> {
            if (v.size() == 1) {
                resultMap.put(k, v.get(0));
            }
        });
        return resultMap;
    }

    public static void distinctAdd(List<NameAndKeyNoEntity> source, List<NameAndKeyNoEntity> target) {
        source.addAll(target);
        for (NameAndKeyNoEntity item : source) {
            if (item.getKeyNo() == null) {
                item.setKeyNo("");
            }
        }
        Map<String, NameAndKeyNoEntity> defGroup = source.stream().collect(Collectors.groupingBy(NameAndKeyNoEntity::getName,
                Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(NameAndKeyNoEntity::getKeyNo).reversed())
                        , Optional::get)));
        source = defGroup.values().stream().collect(Collectors.toList());
    }

    public static boolean isUnknownName(String name) {
        return name.contains("某")
                || name.contains("*")
                || name.contains("X")
                || name.contains("x")
                || name.contains("ｘ")
                || name.contains("Ｘ");
    }

    public static void main(String[] args) {
        String json = "[{\"KeyNo\":\"\",\"Name\":\"某某林\",\"Org\":-2,\"Role\":\"被执行人\"},{\"KeyNo\":\"\",\"Name\":\"**林\",\"Org\":-2,\"Role\":\"被执行人\"}]";
        List<NameAndKeyNoEntity> list = JSON.parseArray(json, NameAndKeyNoEntity.class);
        mergeXXName(list);
        System.out.println(1);
    }
}
