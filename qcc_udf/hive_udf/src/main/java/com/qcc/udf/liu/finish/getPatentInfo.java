package com.qcc.udf.liu.finish;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.court_notice.anUtils.MD5Util;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

/**
 * <AUTHOR>
 */
public class getPatentInfo extends UDF {
    public static String evaluate(String id, String zblist, String detailinfo) {
        JSONArray result = new JSONArray();

        JSONArray caseRole = new JSONArray();
        JSONArray resultArray = new JSONArray();
        if (StringUtils.isNotEmpty(detailinfo)){
            JSONObject jsonObject = JSONObject.parseObject(detailinfo);
            caseRole = jsonObject.getJSONArray("CaseRole");

            Iterator<Object> iterator = jsonObject.getJSONArray("InfoList").iterator();
            while (iterator.hasNext()){
                JSONObject info = (JSONObject)iterator.next();

                JSONArray caseArray = info.getJSONArray("CaseList");
                if (caseArray != null && !caseArray.isEmpty() && caseArray.size() > 0){
                    Iterator<Object> it = caseArray.iterator();
                    while (it.hasNext()){
                        JSONObject json = (JSONObject)it.next();

                        JSONObject item = new JSONObject();
                        item.put("LatestTimestamp", info.getLongValue("LatestTimestamp"));
                        item.put("TrialRound", info.getString("TrialRound"));
                        item.put("CaseId", json.getString("Id"));
                        item.put("Result", json.getString("Result"));

                        resultArray.add(item);
                    }
                }

            }

        }

        if (StringUtils.isNotEmpty(zblist)){
            JSONArray jsonArray = JSONArray.parseArray(zblist);
            Iterator<Object> iterator = jsonArray.iterator();
            while (iterator.hasNext()){
                JSONObject jsonObject = (JSONObject)iterator.next();

                JSONArray array = jsonObject.getJSONArray("NameAndKeyNo");
                if (array != null && !array.isEmpty() && array.size() > 0){
                    Iterator<Object> it = array.iterator();
                    while (it.hasNext()) {
                        JSONObject json = (JSONObject) it.next();

                        JSONObject info = new JSONObject();
                        info.put("CaseSearchId", id);
                        info.put("PatentId", jsonObject.getString("Id"));
                        info.put("PatentNo", jsonObject.getString("PublicationNumber"));
                        info.put("AssigneeInfo", json);
                        info.put("CaseRole", caseRole);
                        info.put("ReusltInfo", resultArray);

                        result.add(info);
                    }
                }
            }
        }
        return result.toString();
    }

    public static void main(String[] args) {
        System.out.println(evaluate("id123",
                "[{\"Agency\":\"\",\"ApplicationDate\":1424966400,\"ApplicationNumber\":\"CN201520117954.0\",\"AssigneeList\":\"厦门市富桥机械有限公司\",\"CompanyKeywords\":\"2b35d0723463d4abd8d9080d136e4b43,厦门市富桥机械有限公司\",\"Id\":\"96732fb7b17893d4722b071607c15c59\",\"Images\":\"https://patent-image.qichacha.com/img/dc40a6b931eb6f5fbc3d76bdf4883b2.jpg\",\"InventorList\":\"刘勇富;曹永剑;钟维海\",\"IsValid\":1,\"KindCode\":\"3\",\"LegalStatus\":\"SW007\",\"NameAndKeyNo\":[{\"KeyNo\":\"2b35d0723463d4abd8d9080d136e4b43\",\"Name\":\"厦门市富桥机械有限公司\",\"Org\":0}],\"PublicationDate\":1436284800,\"PublicationNumber\":\"CN204453700U\",\"Status\":\"ZT002,ZT002001\",\"Title\":\"一种智能全自动上板机\",\"Type\":4}]",
                "{\"AmtInfo\":{},\"AnNoList\":\"（2022）最高法知民终1689号,（2022）闽02民初111号,（2023）闽02执504号\",\"AnnoCnt\":3,\"CaseCnt\":1,\"CaseName\":\"厦门市富桥科技有限公司与福建省泉州市荣佳石业有限公司侵害实用新型专利权纠纷的案件\",\"CaseNameClean\":\"厦门市富桥科技有限公司与福建省泉州市荣佳石业有限公司侵害实用新型专利权纠纷的案件\",\"CaseReason\":\"侵害实用新型专利权纠纷\",\"CaseRole\":\"[{\\\"D\\\":\\\"一审原告,二审被上诉人\\\",\\\"N\\\":\\\"2b35d0723463d4abd8d9080d136e4b43\\\",\\\"O\\\":0,\\\"P\\\":\\\"厦门市富桥科技有限公司\\\",\\\"R\\\":\\\"原告\\\",\\\"RL\\\":[{\\\"R\\\":\\\"原告\\\",\\\"T\\\":\\\"一审\\\"},{\\\"JR\\\":\\\"22\\\",\\\"LR\\\":\\\"2\\\",\\\"R\\\":\\\"被上诉人\\\",\\\"T\\\":\\\"二审\\\"}]},{\\\"D\\\":\\\"一审被告,二审上诉人,首次执行被执行人\\\",\\\"N\\\":\\\"c7db751f67d0df92e47ef797f0aace0f\\\",\\\"O\\\":0,\\\"P\\\":\\\"福建省泉州市荣佳石业有限公司\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"},{\\\"JR\\\":\\\"15\\\",\\\"LR\\\":\\\"1\\\",\\\"R\\\":\\\"上诉人\\\",\\\"T\\\":\\\"二审\\\"},{\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"首次执行\\\"}]}]\",\"CaseType\":\"执行案件,民事案件\",\"CfdfCnt\":0,\"CfgsCnt\":0,\"CfxyCnt\":0,\"CompanyKeywords\":\"2b35d0723463d4abd8d9080d136e4b43,c7db751f67d0df92e47ef797f0aace0f,厦门市富桥科技有限公司,福建省泉州市荣佳石业有限公司\",\"CourtList\":\"最高人民法院,福建省厦门市中级人民法院\",\"EarliestDate\":1654654800,\"EarliestDateType\":\"民事一审|开庭时间\",\"FyggCnt\":0,\"GqdjCnt\":0,\"GroupId\":\"de8cb67350ade10b391f0014acdf6d2e\",\"HbcfCnt\":0,\"Id\":\"fa06015cb359210aa61facd3231c71d1\",\"InfoList\":[{\"AnNo\":\"（2022）最高法知民终1689号\",\"CaseList\":[{\"Amt\":\"\",\"CaseType\":\"民事判决书\",\"DocType\":\"判决日期\",\"Id\":\"e73f40dd4d483dc123228e86fe9754180\",\"IsValid\":1,\"JudgeDate\":1670428800,\"Result\":\"驳回上诉，维持原判。\",\"ResultType\":\"判决结果\",\"ShieldCaseFlag\":0}],\"CaseReason\":\"侵害实用新型专利权纠纷\",\"CaseType\":\"民事案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"最高人民法院\",\"Defendant\":[{\"JR\":\"22\",\"KeyNo\":\"2b35d0723463d4abd8d9080d136e4b43\",\"LR\":\"2\",\"LawFirmList\":[{\"LY\":[{\"N\":\"4dfdf82e5123ebfe1ab6c5f14298ebe7\",\"P\":\"张亚玲\",\"R\":\"委托诉讼代理人\"},{\"N\":\"6d4525847c041711fabb293b001334a0\",\"P\":\"颜邕睿\",\"R\":\"委托诉讼代理人\"}],\"N\":\"w3f9442e6ffafc3202c31e12a1900081\",\"O\":4,\"P\":\"北京德恒（厦门）律师事务所\"}],\"Name\":\"厦门市富桥科技有限公司\",\"Org\":0,\"Role\":\"被上诉人（原审原告）\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1670428800,\"LianList\":[{\"Id\":\"e73f40dd4d483dc123228e86fe975418\",\"IsValid\":1,\"LianDate\":-1,\"NameAndKeyNo\":[{\"KeyNo\":\"c7db751f67d0df92e47ef797f0aace0f\",\"Name\":\"福建省泉州市荣佳石业有限公司\",\"Org\":0},{\"KeyNo\":\"2b35d0723463d4abd8d9080d136e4b43\",\"Name\":\"厦门市富桥科技有限公司\",\"Org\":0}]}],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"JR\":\"15\",\"KeyNo\":\"c7db751f67d0df92e47ef797f0aace0f\",\"LR\":\"1\",\"LawFirmList\":[{\"LY\":[{\"N\":\"7525b2427888b0ed20764cfd8c8ed976\",\"P\":\"彭金荣\",\"R\":\"委托诉讼代理人\"},{\"N\":\"cfcc171cd01e91833470c1efb21f33e1\",\"P\":\"赖非洪\",\"R\":\"委托诉讼代理人\"}],\"N\":\"w2dca936c7a7f95330194287bdf5cd96\",\"O\":4,\"P\":\"北京盈科（泉州）律师事务所\"}],\"Name\":\"福建省泉州市荣佳石业有限公司\",\"Org\":0,\"Role\":\"上诉人（原审被告）\"}],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"民事二审\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]},{\"AnNo\":\"（2022）闽02民初111号\",\"CaseList\":[],\"CaseReason\":\"侵害实用新型专利权纠纷\",\"CaseType\":\"民事案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"福建省厦门市中级人民法院\",\"Defendant\":[{\"KeyNo\":\"c7db751f67d0df92e47ef797f0aace0f\",\"Name\":\"福建省泉州市荣佳石业有限公司\",\"Org\":0,\"Role\":\"被告\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[{\"ExecuteUnite\":\"知识产权法庭\",\"Id\":\"5bca91f95043264c3e396fef32b9a4be\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"2b35d0723463d4abd8d9080d136e4b43\",\"Name\":\"厦门市富桥科技有限公司\",\"Org\":0},{\"KeyNo\":\"c7db751f67d0df92e47ef797f0aace0f\",\"Name\":\"福建省泉州市荣佳石业有限公司\",\"Org\":0}],\"OpenDate\":-1},{\"ExecuteUnite\":\"知识产权第三法庭\",\"Id\":\"819b7bdec5731e162cab56434b3924ef\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"2b35d0723463d4abd8d9080d136e4b43\",\"Name\":\"厦门市富桥科技有限公司\",\"Org\":0},{\"KeyNo\":\"c7db751f67d0df92e47ef797f0aace0f\",\"Name\":\"福建省泉州市荣佳石业有限公司\",\"Org\":0}],\"OpenDate\":1654654800}],\"LatestTimestamp\":1654654800,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"2b35d0723463d4abd8d9080d136e4b43\",\"Name\":\"厦门市富桥科技有限公司\",\"Org\":0,\"Role\":\"原告\"}],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"民事一审\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]},{\"AnNo\":\"（2023）闽02执504号\",\"CaseList\":[],\"CaseReason\":\"\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"福建省厦门市中级人民法院\",\"Defendant\":[{\"KeyNo\":\"c7db751f67d0df92e47ef797f0aace0f\",\"Name\":\"福建省泉州市荣佳石业有限公司\",\"Org\":0,\"Role\":\"被执行人\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1678723200,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"首次执行\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[{\"Biaodi\":\"0\",\"Id\":\"ee3b4c456ad8f98c5eb87da36350f9f01\",\"IsValid\":1,\"LianDate\":1678723200,\"NameAndKeyNo\":[{\"KeyNo\":\"c7db751f67d0df92e47ef797f0aace0f\",\"Name\":\"福建省泉州市荣佳石业有限公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]}]}],\"KtggCnt\":2,\"LastestDate\":1678723200,\"LastestDateType\":\"首次执行|被执行人立案日期\",\"LatestTrialRound\":\"首次执行\",\"LawyerIds\":\"0009cc36083f409761177893cec51b3f,4195a6e6a2ebbd2bfead649134bbb40c,4dfdf82e5123ebfe1ab6c5f14298ebe7,6d4525847c041711fabb293b001334a0,7525b2427888b0ed20764cfd8c8ed976,b95420b8cb9a05c2727b1c83e71c601e,bdbd4be1d22976bd17fb80c0094be50c,cfcc171cd01e91833470c1efb21f33e1\",\"LianCnt\":1,\"PcczCnt\":0,\"ProcuratorateList\":\"\",\"Province\":\"BJ,FJ\",\"SdggCnt\":0,\"Source\":\"OT\",\"SxCnt\":0,\"Tags\":\"2,4,11,12\",\"Type\":1,\"XgCnt\":0,\"XjpgCnt\":0,\"XzcfCnt\":0,\"ZLCnt\":0,\"ZLList\":[{\"Agency\":\"\",\"ApplicationDate\":1424966400,\"ApplicationNumber\":\"CN201520117954.0\",\"AssigneeList\":\"厦门市富桥机械有限公司\",\"CompanyKeywords\":\"2b35d0723463d4abd8d9080d136e4b43,厦门市富桥机械有限公司\",\"Id\":\"96732fb7b17893d4722b071607c15c59\",\"Images\":\"https://patent-image.qichacha.com/img/dc40a6b931eb6f5fbc3d76bdf4883b2.jpg\",\"InventorList\":\"刘勇富;曹永剑;钟维海\",\"IsValid\":1,\"KindCode\":\"3\",\"LegalStatus\":\"SW007\",\"NameAndKeyNo\":[{\"KeyNo\":\"2b35d0723463d4abd8d9080d136e4b43\",\"Name\":\"厦门市富桥机械有限公司\",\"Org\":0}],\"PublicationDate\":1436284800,\"PublicationNumber\":\"CN204453700U\",\"Status\":\"ZT002,ZT002001\",\"Title\":\"一种智能全自动上板机\",\"Type\":4}],\"ZbCnt\":0,\"ZxCnt\":1}"));
    }
}
