package com.qcc.udf.temp;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 *
 *
 *  测试使用  md5
 * <AUTHOR>
 *
 */
public class TempMD5Util extends UDF {
    private static final char[] DIGITS_LOWER = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
    private static final ThreadLocal<MessageDigest> MESSAGE_DIGEST_LOCAL = new ThreadLocal<MessageDigest>() {
        protected MessageDigest initialValue() {
            try {
                return MessageDigest.getInstance("MD5");
            } catch (NoSuchAlgorithmException var2) {
                return null;
            }
        }
    };
    public String evaluate(String s) throws NoSuchAlgorithmException {
        if (StringUtils.isNotEmpty(s)){
            String[] split = s.split(",");
            return encode(split[0], split[1]);
        }
        return "";
    }
    public static String encode(String... content) throws NoSuchAlgorithmException {
        String val = String.join("|", content);
        val = StringUtils.isEmpty(val) ? "" : val.replace(" ", "").replace("　", "");
        val = CommonUtil.getCompanyNameByName(val);
        if (StringUtils.isNotBlank(val)) {
            val = val.replaceAll("[\n`~!@#$%^&*+=|{}':;',\\[\\].<>/?~！@#￥%……&*——+|{}【】‘；：”“’。， 、？]", "");
        }
        val = val.toUpperCase();
        return md5Hex(val.getBytes(StandardCharsets.UTF_8));
    }



    public static void main(String[] args) throws NoSuchAlgorithmException {

        TempMD5Util tempMD5Util = new TempMD5Util();
        System.out.println(tempMD5Util.evaluate("123,45"));

    }



    public static String md5Hex(byte[] bytes) throws NoSuchAlgorithmException {
        String var2;
        try {
            MessageDigest messageDigest = (MessageDigest)MESSAGE_DIGEST_LOCAL.get();
            if (messageDigest == null) {
                throw new NoSuchAlgorithmException("MessageDigest get MD5 instance error");
            }

            var2 = encodeHexString(messageDigest.digest(bytes));
        } finally {
            MESSAGE_DIGEST_LOCAL.remove();
        }

        return var2;
    }

    public static String encodeHexString(byte[] bytes) {
        int l = bytes.length;
        char[] out = new char[l << 1];
        int i = 0;

        for(int var4 = 0; i < l; ++i) {
            out[var4++] = DIGITS_LOWER[(240 & bytes[i]) >>> 4];
            out[var4++] = DIGITS_LOWER[15 & bytes[i]];
        }

        return new String(out);
    }

}
