package com.qcc.udf.company;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * <AUTHOR>
 * @date 2022-11-30 11:59
 */
public class calculateSimilarity extends UDF {
    /**
     * 计算字符串相似度
     *
     * @param x
     * @param y
     * @return
     */
    public Double evaluate(String x, String y) {
        x = full2Half(x).replace(" ", "").toLowerCase();
        y = full2Half(y).replace(" ", "").toLowerCase();
        if (StringUtils.isEmpty(x) && StringUtils.isEmpty(y)) {
            return 1d;
        }

        if (StringUtils.isEmpty(x) || StringUtils.isEmpty(y)) {
            return 0d;
        }

        if (x.length() < 10) {
            x = StringUtils.leftPad(x, 10, '0');
        }

        if (y.length() < 10) {
            y = StringUtils.leftPad(y, 10, '0');
        }

        double maxLength = Double.max(x.length(), y.length());
        if (maxLength > 0) {
            // 如果需要，可以选择忽略大小写
            return (maxLength - StringUtils.getLevenshteinDistance(x, y)) / maxLength;
        }
        return 0d;
    }

    /**
     * 全角转半角
     *
     * @param input 输入
     * @return 半角文本
     */
    private static String full2Half(String input) {
        if (StringUtils.isEmpty(input)) {
            return "";
        }

        char c[] = input.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == '\u3000') {
                c[i] = ' ';
            } else if (c[i] > '\uFF00' && c[i] < '\uFF5F') {
                c[i] = (char) (c[i] - 65248);
            }
        }
        return new String(c);
    }

    public static void main(String[] args) {
        double aa = new calculateSimilarity().evaluate("Techie Delight", "Tech Delight");
        double bb = new calculateSimilarity().evaluate("Techie Delight", "");
        double cc = new calculateSimilarity().evaluate("", "");
        double dd = new calculateSimilarity().evaluate("abc", "ABC");
        double ee = new calculateSimilarity().evaluate("ab c", "ABC");
        double ff = new calculateSimilarity().evaluate("acb", "ABC");
        double gg = new calculateSimilarity().evaluate("123456789", "1234567890");
    }
}
