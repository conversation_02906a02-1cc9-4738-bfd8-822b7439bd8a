package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;
import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDFUtils;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorConverter;


@Description(name = "character_length,char_length",
        value = "_FUNC_(str | binary) - Returns the number of characters in str or binary data",
        extended = "Example:\n"
                + "  > SELECT _FUNC_('안녕하세요') FROM src LIMIT 1;\n" + "  5")
public class CharacterSize extends UDF {

    private transient PrimitiveObjectInspectorConverter.StringConverter stringConverter;
    private transient boolean isInputString;

    public Integer evaluate(String arguments) throws HiveException {
        byte[] data = null;
        if (arguments == null) {
            return null;
        }
        data = arguments.getBytes();
        int len = data.length;
        return len;
    }

}
