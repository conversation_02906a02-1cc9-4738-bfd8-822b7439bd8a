package com.qcc.udf.overseas;

import com.qcc.udf.overseas.constant.Time;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 业务UDF（海外企业）将日本纪年改为时间戳
 * ---------------------------------------------------------------------------------------------------------
 * add jar hdfs://ldh/data/hive/udf/qcc_udf.jar;
 * create temporary function ConvertJapaneseDate as 'com.qcc.udf.overseas.ConvertJapaneseDate';
 * ---------------------------------------------------------------------------------------------------------
 * select ConvertJapaneseDate ('平成30年6月22日');
 * 1529596800000
 */
public class ConvertJapaneseDate extends UDF {

    public String evaluate(String dateInput) {
        /**
         * 换算关系
         * 平成   ->  x+1988
         * 令和   ->  x+2018
         */
        String output = "";
        try {
            if (StringUtils.isNotBlank(dateInput)) {
                String[] splits = dateInput.split("年");
                if (splits != null && splits.length == 2) {
                    Matcher matcher = Pattern.compile("[0-9]{1,3}").matcher(splits[0]);
                    if (matcher.find()) {
                        int year = Integer.parseInt(matcher.group(0));
                        if (splits[0].contains("平成")) {
                            output = CommonUtil.getTimestampFromDateField(
                                    StringUtils.join(1988 + year, "年", splits[1]), Time.Formatter_T6);
                        } else if (splits[0].contains("令和")) {
                            output = CommonUtil.getTimestampFromDateField(
                                    StringUtils.join(2018 + year, "年", splits[1]), Time.Formatter_T6);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return output;
    }

//    public static void main(String[] args) {
//        List<String> dateList = Arrays.asList(
//                "平成31年3月29日",
//                "平成27年11月13日",
//                "平成27年12月18日",
//                "平成27年10月30日",
//                "平成30年4月10日",
//                "平成27年10月30日",
//                "平成27年10月30日",
//                "平成28年5月13日",
//                "平成30年6月1日",
//                "平成27年11月13日",
//                "平成30年9月26日",
//                "平成27年11月20日",
//                "平成27年11月13日",
//                "平成27年10月28日"
//        );
//
//        for (String date : dateList) {
//            System.out.println(date + "\t" + new ConvertJapaneseDate().evaluate(date));
//        }
//    }
}
