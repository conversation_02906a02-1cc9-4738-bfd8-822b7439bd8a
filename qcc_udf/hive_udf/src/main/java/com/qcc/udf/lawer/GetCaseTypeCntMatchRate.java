package com.qcc.udf.lawer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;

/**
 * 获取指定占比执行案件条件
 * <AUTHOR>
 * @date 2021年09月28日 17:55
 */
public class GetCaseTypeCntMatchRate extends UDF {
    public static boolean evaluate(String caseTypeJson, String rate) {
        JSONArray array = JSON.parseArray(caseTypeJson);
        BigDecimal sumCnt = BigDecimal.ZERO;
        BigDecimal zxCnt = BigDecimal.ZERO;
        for (Object obj : array) {
            JSONObject json = (JSONObject) obj;
            String name = json.getString("name");
            String cnt = json.getString("cnt");
            if(name.contains("6")){
                zxCnt = zxCnt.add(new BigDecimal(cnt));
            }
            sumCnt = sumCnt.add(new BigDecimal(cnt));
        }

        BigDecimal div = zxCnt.divide(sumCnt,2,BigDecimal.ROUND_HALF_UP);
        if(div.multiply(new BigDecimal(100)).compareTo(new BigDecimal(rate))>=0){
            return true;
        }

        return false;
    }

    public static void main(String[] args) {
        String json = "[{\"name\":\"1_6\",\"cnt\":7,\"casesearchids\":\"\"},{\"name\":\"1\",\"cnt\":4,\"casesearchids\":\"\"},{\"name\":\"2_6\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"3\",\"cnt\":1,\"casesearchids\":\"\"}]";
        System.out.println(evaluate(json, "70"));
    }
}
