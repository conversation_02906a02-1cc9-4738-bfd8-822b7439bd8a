package com.qcc.udf.personrisk;

import cn.hutool.core.comparator.CompareUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.List;

public class getPersonLasestDate extends UDF {
    public static String evaluate(List<String> roleList) {
        JSONArray result = new JSONArray();
        if (CollectionUtils.isEmpty(roleList)) {
            return "";
        }
        // Group by 只有一条数据时候不处理
        if (roleList.size() == 1) {
            result.add(roleList.get(0));
        } else {
            int index = -1;
            String endate = null;
            for (int i = 0; i < roleList.size(); i++) {
                JSONObject json = JSONObject.parseObject(roleList.get(i));
                // 取最大结束时间
                if (StringUtils.isEmpty(endate) || CompareUtil.compare(json.getString("enddate"), endate) > 0) {
                    endate = json.getString("enddate");
                    index = i;
                }
                // 结束时间是0 表示至今 取0
                if ("0".equals(json.getString("enddate"))) {
                    endate = json.getString("enddate");
                    index = i;
                    break;
                }
            }
            result.add(roleList.get(index));
        }
        return result.isEmpty() || result.size() == 0 ? "" : result.toString();
    }


}
