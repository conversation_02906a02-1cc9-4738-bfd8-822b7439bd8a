package com.qcc.udf.product;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.net.MalformedURLException;
import java.net.URL;

/**
 * 获取网址host 地址
 * <AUTHOR>
 */
public class DomainUDF extends UDF {
    public static String evaluate(String web) {
        if (StringUtils.isEmpty(web)) {
            return "";
        }
        if (!web.startsWith("http://") && !web.startsWith("https://")) {
            if (web.startsWith("//")) {
                web = "http:" + web;
            } else {
                web = "http://" + web;
            }
        }
        try {
            URL url = new URL(web);
            return url.getHost();
        } catch (MalformedURLException e) {
        }
        return "";
    }


    public static void main(String[] args) {
        System.out.println(DomainUDF.evaluate("www.qcc.com/jjdf/df"));
    }
}
