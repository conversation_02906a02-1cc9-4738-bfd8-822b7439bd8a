package com.qcc.udf.cpws.casesearch_v2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

/**
 * @Auther: liulh
 * @Date: 2020/6/11 17:54
 * @Description:
 */
public class sortDetailInfo extends UDF {
    public static String evaluate(String info) {
        // 传入参数为空，则返回空json
        if (StringUtils.isEmpty(info)){
            return new JSONObject().toString();
        }

        JSONObject json = JSONObject.parseObject(info);
        json.put("CaseRole", jsonArraySort(json.getJSONArray("CaseRole"), "P"));

        JSONArray infoList = json.getJSONArray("InfoList");
        JSONArray infoListNew = new JSONArray();
        Iterator<Object> it = infoList.iterator();
        while (it.hasNext()){
            JSONObject jsonObject = (JSONObject)it.next();
            jsonObject.put("Defendant", jsonArraySort(jsonObject.getJSONArray("Defendant"), "Name"));
            jsonObject.put("Prosecutor", jsonArraySort(jsonObject.getJSONArray("Prosecutor"), "Name"));


            jsonObject.put("CfgsList", jsonArraySort(jsonObject.getJSONArray("CfgsList"), "Id"));
            jsonObject.put("SdggList", jsonArraySort(jsonObject.getJSONArray("SdggList"), "Id"));
            jsonObject.put("ZxList", jsonArraySort(jsonObject.getJSONArray("ZxList"), "Id"));
            jsonObject.put("HbcfList", jsonArraySort(jsonObject.getJSONArray("HbcfList"), "Id"));
            jsonObject.put("CfdfList", jsonArraySort(jsonObject.getJSONArray("CfdfList"), "Id"));
            jsonObject.put("CaseList", jsonArraySort(jsonObject.getJSONArray("CaseList"), "Id"));
            jsonObject.put("ZbList", jsonArraySort(jsonObject.getJSONArray("ZbList"), "Id"));
            jsonObject.put("SxList", jsonArraySort(jsonObject.getJSONArray("SxList"), "Id"));
            jsonObject.put("FyggList", jsonArraySort(jsonObject.getJSONArray("FyggList"), "Id"));
            jsonObject.put("XjpgList", jsonArraySort(jsonObject.getJSONArray("XjpgList"), "Id"));
            jsonObject.put("LianList", jsonArraySort(jsonObject.getJSONArray("LianList"), "Id"));
            jsonObject.put("XgList", jsonArraySort(jsonObject.getJSONArray("XgList"), "Id"));
            jsonObject.put("KtggList", jsonArraySort(jsonObject.getJSONArray("KtggList"), "Id"));
            jsonObject.put("PcczList", jsonArraySort(jsonObject.getJSONArray("PcczList"), "Id"));
            jsonObject.put("GqdjList", jsonArraySort(jsonObject.getJSONArray("GqdjList"), "Id"));
            jsonObject.put("CfxyList", jsonArraySort(jsonObject.getJSONArray("CfxyList"), "Id"));

            infoListNew.add(jsonObject);
        }
        json.put("InfoList", infoListNew);

        return json.toString();
    }


    public static JSONArray jsonArraySort(JSONArray jsonArr, String columnName) {
        JSONArray sortedJsonArray = new JSONArray();
        if (jsonArr != null){
            try {

                List<JSONObject> jsonValues = new ArrayList<JSONObject>();
                for (int i = 0; i < jsonArr.size(); i++) {
                    jsonValues.add(jsonArr.getJSONObject(i));
                }
                if (jsonValues.size() > 1){
                    Collections.sort(jsonValues, new Comparator<JSONObject>() {
                        private final String KEY_NAME = columnName;
                        @Override
                        public int compare(JSONObject a, JSONObject b) {
                            String valA = new String();
                            String valB = new String();
                            try {
                                // 这里是a、b需要处理的业务，需要根据你的规则进行修改。
                                valA = a.getString(KEY_NAME);
                                valB = b.getString(KEY_NAME);
                            } catch (JSONException e) {
                            }
                            return valA.compareTo(valB);
                        }
                    });
                }
                for (int i = 0; i < jsonArr.size(); i++) {
                    sortedJsonArray.add(jsonValues.get(i));
                }
            }catch (Exception e){

            }
        }
        return sortedJsonArray;
    }

    public static void main(String[] args) {
        System.out.println(evaluate("{\"KtggCnt\":2,\"ZxCnt\":1,\"XjpgCnt\":0,\"LastestDateType\":\"首次执行|失信人发布日期\",\"CfgsCnt\":0,\"LastestDate\":1594915200,\"EarliestDate\":1572397800,\"AnnoCnt\":2,\"EarliestDateType\":\"民事一审|开庭时间\",\"CompanyKeywords\":\"p5553c2f4ed17711d31466d03726c78a,吕静,02a799617f934e60573f4caa11071f80,何爱滨\",\"AnNoList\":\"（2020）渝0107执3082号,（2019）渝0107民初11373号\",\"GqdjCnt\":0,\"GroupCourt\":\"重庆市九龙坡区人民法院\",\"XgCnt\":1,\"FyggCnt\":0,\"ZbCnt\":0,\"LatestTrialRound\":\"首次执行\",\"CfdfCnt\":0,\"CfxyCnt\":0,\"CaseName\":\"民间借贷纠纷\",\"SxCnt\":1,\"Province\":\"CQ\",\"LianCnt\":0,\"CaseCnt\":0,\"PcczCnt\":0,\"HbcfCnt\":0,\"ProcuratorateList\":\"\",\"CaseType\":\"执行案件,民事案件\",\"CaseRole\":[{\"R\":\"原告\"},{\"R\":\"被告\"}],\"CaseReason\":\"民间借贷纠纷\",\"CourtList\":\"重庆市九龙坡区人民法院\",\"InfoList\":[{\"Defendant\":[{\"Role\":\"被告\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"重庆市九龙坡区人民法院\",\"ZxList\":[],\"LatestTimestamp\":1572397800,\"HbcfList\":[],\"CfdfList\":[],\"TrialRound\":\"民事一审\",\"CaseList\":[],\"Prosecutor\":[{\"Role\":\"原告\"}],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2019）渝0107民初11373号\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"民间借贷纠纷\",\"KtggList\":[{\"OpenDate\":1572397800,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-1,\"Name\":\"何爱滨\"},{\"KeyNo\":\"p5553c2f4ed17711d31466d03726c78a\",\"Org\":2,\"Name\":\"吕静\"}],\"Id\":\"c633bebe3702d3ec544b858cdafb48835\",\"IsValid\":1},{\"OpenDate\":-1,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"何爱滨\"},{\"KeyNo\":\"\",\"Name\":\"吕静\"}],\"Id\":\"cc1fcdd1f72d5f61f03a38263db07f7c5\",\"IsValid\":1}],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]},{\"Defendant\":[{\"KeyNo\":\"p5553c2f4ed17711d31466d03726c78a\",\"Role\":\"被执行人\",\"Org\":2,\"Name\":\"吕静\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"重庆市九龙坡区人民法院\",\"ZxList\":[{\"LianDate\":1590940800,\"NameAndKeyNo\":[{\"KeyNo\":\"p5553c2f4ed17711d31466d03726c78a\",\"Org\":2,\"Name\":\"吕静\"}],\"Id\":\"0982a532d455ad1ff5f5fc821582129d1\",\"IsValid\":1}],\"LatestTimestamp\":1594915200,\"HbcfList\":[],\"CfdfList\":[],\"TrialRound\":\"首次执行\",\"CaseList\":[],\"Prosecutor\":[],\"ZbList\":[],\"ExecuteNo\":\"（2019）渝0107民初11373号\",\"SxList\":[{\"PublishDate\":1594915200,\"NameAndKeyNo\":[{\"KeyNo\":\"p5553c2f4ed17711d31466d03726c78a\",\"Org\":2,\"Name\":\"吕静\"}],\"Id\":\"0982a532d455ad1ff5f5fc821582129d2\",\"IsValid\":1}],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2020）渝0107执3082号\",\"XgList\":[{\"PublishDate\":1591891200,\"NameAndKeyNo\":{\"KeyNo\":\"p5553c2f4ed17711d31466d03726c78a\",\"Org\":2,\"Name\":\"吕静\"},\"XglNameAndKeyNo\":[{\"KeyNo\":\"p5553c2f4ed17711d31466d03726c78a\",\"Org\":2,\"Name\":\"吕静\"}],\"Id\":\"b1821a26ab657ee81ab53de3d28f9245\",\"CompanyInfo\":{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"\"},\"GlNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"\"}],\"IsValid\":1}],\"LianList\":[],\"CaseReason\":\"\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]}],\"SdggCnt\":0}"));



//        List<String> tmpList = new LinkedList<>();
//        tmpList.add("王超");
//        tmpList.add("上海长建机械设备租赁有限公司");
//        tmpList.add("舒小慧");
//
//        Collections.sort(tmpList, new Comparator<String>() {
//            @Override
//            public int compare(String a, String b) {
//                return a.compareTo(b);
//            }
//        });
//
//        System.out.println(tmpList);
    }
}
