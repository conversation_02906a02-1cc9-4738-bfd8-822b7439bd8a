package com.qcc.udf.cpws.casereason;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date ：Created in 2022/12/08
 * @description ：获得裁判文书标签
 */
public class CaseLabelUtil {

    public static String getCaseLabel(String reasonCode, String relatedTags,
                                      String content, String nameAnd<PERSON>ey<PERSON><PERSON>, String defendant, String prosecutor) {
        String caseLabel = "";
        if (StringUtils.isEmpty(reasonCode)) {
            return caseLabel;
        }
        // 读取txt
        List<CaseLabel> txtList = MatchExcelCode.getTxtList();
        // 涉外纠纷idList
        List<String> swList = txtList.stream().filter(
                it -> it.getFirstLabel().equals("涉外纠纷")).map(it -> it.getId()).collect(Collectors.toList());
        // 不正当竞争idList
        List<String> jzList = txtList.stream().filter(
                it -> it.getFirstLabel().equals("不正当竞争")).map(it -> it.getId()).collect(Collectors.toList());

        Boolean success = true;
        if (reasonCode.equals("B,B06")) {
            /*
             * 劳动争议：-- B,B06 特征词包含劳动争议（特征词relatedtags）
             */
            success = StringUtils.isNotEmpty(relatedTags) && relatedTags.contains("劳动争议");

        } else if (reasonCode.equals("B,B05")) {
            /*
             * 知识产权纠纷：-- B,B05 正文含”商标“or”专利“or”著作权“or“知识产权”（正文--case_main_body_sync）
             */
            success = StringUtils.isNotEmpty(content) &&
                    (content.contains("商标") || content.contains("专利") ||
                            content.contains("著作权") || content.contains("知识产权"));

        } else if (swList.contains(reasonCode)) {
            /*
             * 涉外纠纷：当事人中存在外企（全部kyenob开头字母为t h l z）
             */
            if (StringUtils.isEmpty(nameAndKeyNo)) {
                success = false;
            } else {
                try {
                    Set<String> keyNo1 = JSON.parseArray(nameAndKeyNo, Map.class).stream().map(
                            it -> it.get("KeyNo").toString()).collect(Collectors.toSet());
                    success = keyNo1.stream().anyMatch(it -> StringUtils.isNotEmpty(it) &&
                            (it.startsWith("t") || it.startsWith("h") || it.startsWith("l") || it.startsWith("z")));
                } catch (Exception e) {
                    success = false;
                }
            }
        } else if (jzList.contains(reasonCode)) {
            /*
             * 不正当竞争：原告与被告均含企业keyno （原告prosecutor，被告defendant）
             */
            Pattern compile = Pattern.compile("[0-9A-Za-z]{32}");
            success = StringUtils.isNotEmpty(defendant) && compile.matcher(defendant).find()
                    && StringUtils.isNotEmpty(prosecutor) && compile.matcher(prosecutor).find();

        } else if (reasonCode.equals("C,C02,C0203,C020305") ||
                reasonCode.equals("C,C02,C0208,C020801") ||
                reasonCode.equals("C,C02,C0208,C020802")) {
            /* 股权纠纷这三类暂缓 */
            success = false;
        }
        List<CaseLabel> caseLabels = new ArrayList<>();
        if (success) {
            Map<String, List<CaseLabel>> collect = txtList.stream().collect(Collectors.groupingBy(it -> it.getId()));
            caseLabels = collect.get(reasonCode);
        }

        if (caseLabels != null && caseLabels.size() > 0) {
            for (CaseLabel c : caseLabels) {
                if (StringUtils.isNotEmpty(c.getFirstLabel())) {
                    String code1 = CaseLabelEnum.getCodeByDesc(c.getFirstLabel());
                    caseLabel += code1 + ",";
                }
                if (StringUtils.isNotEmpty(c.getSecondLabel())) {
                    String code2 = CaseLabelEnum.getCodeByDesc(c.getSecondLabel());
                    caseLabel += code2 + ",";
                }
            }
        }
        if (StringUtils.isNotEmpty(caseLabel)) {
            caseLabel = caseLabel.substring(0, caseLabel.length() - 1);
        }
        return caseLabel;
    }
}
