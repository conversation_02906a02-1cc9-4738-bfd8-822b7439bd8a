package com.qcc.udf.overseas;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * 海外离线数据匹配时处理企业名称
 * <AUTHOR>
 * @date 2023/2/6
 */
public class OverseaHandleCompName extends UDF {
    public static String evaluate(String compName) {
        String result="";
        if(StringUtils.isNotBlank(compName)){
            String tempName = StringUtils.lowerCase(compName);
            if(tempName.endsWith("limited")){
                tempName = tempName.replace("limited","ltd");
            }
            if(tempName.endsWith(" h.k. ")){
                tempName = tempName.replace(" h.k. "," hongkong ");
            }
            if(tempName.endsWith(" h.k ")){
                tempName = tempName.replace(" h.k "," hongkong ");
            }
            if(tempName.endsWith(" hk ")){
                tempName = tempName.replace(" hk "," hongkong ");
            }
            if(tempName.contains("company")){
                String[] array = tempName.split(" ");
                if(null != array && array.length>3){
                    String tempStr = array[array.length-2];
                    if("company".contains(tempStr)){
                        tempName = tempName.replace("company","co");
                    }
                }
            }
            result = StringUtils.lowerCase(tempName.replaceAll("\\.|'|\\-| |\\(|\\)|/",""));

        }
        return result;
    }

//    public static void main(String[] args){
//        String content="FESCO INTEGRATED TRANSPORT O/B DENIM HOUSE INTERNATIONAL TRADING COMPANY LIMITED";
//        String result = evaluate(content);
//        System.out.println(result);
//    }
}
