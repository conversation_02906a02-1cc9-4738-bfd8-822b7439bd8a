package com.qcc.udf.risk_analysis;

import com.alibaba.fastjson.JSON;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2020/8/10 15:26
 * @description：风险分析，JSONArray按照某个参数排序
 */
public class sortRiskAnalysisList extends UDF {
	/**
	 * JSONArray按照某个参数排序
	 *
	 * @param list  入参JSONArray
	 * @param sortKey 排序参数名称
	 * @return
	 */
	public static String evaluate(List<String> list, String sortKey) {
		//入参 List<List<String>>
		List<Map<String, Object>> outList = new ArrayList<>();
		for (String item : list) {
			List<String> itemList = JSON.parseArray(item, String.class);
			for (String data : itemList) {
				outList.add(JSON.parseObject(data, Map.class));
			}
		}

		//排序
		outList.sort((a, b) -> {
			Object a1 = a.get(sortKey);
			Object a2 = b.get(sortKey);
			if (a1 != null && a2 != null) {
				return a2.toString().compareTo(a1.toString());
			}
			return 0;
		});

		return JSON.toJSONString(outList);
	}

	public static void main(String[] args) {
		String str = "[\"[{\\\"A\\\":\\\"（2019）粤0304破申15号\\\",\\\"B\\\":1559577600}]\",\"[{\\\"A\\\":\\\"（2019）粤0304破14号\\\",\\\"B\\\":1560268800}]\"]";
		List<String> list  = JSON.parseArray(str,String.class);

		System.out.println(evaluate(list,"B"));
	}
}
