package com.qcc.udf.casesearch_v3.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.base.Strings;
import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.output.*;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class CaseV3GetDebtInfo extends UDF {
    public static String evaluate(String json) throws InvocationTargetException, IllegalAccessException {
        LawSuitV3OutputEntity jsonEntity = JSON.parseObject(json,LawSuitV3OutputEntity.class);
        //当前企业/人员关联的未履行金额、执行标的优先级最高
        //用裁判文书的案件金额为所有人兜底
        //金额使用顺序 终本-未履行金额，终本-执行标底，被执行-执行标底，裁判文书金额
        //终本未履行,终本-执行标底

        List<ZbListEntity> zbList = new ArrayList<>();
        List<ZXListEntity> zxList = new ArrayList<>();
        List<CaseListEntity> cpwsList = new ArrayList<>();

        List<DebtInfo> zbDebtList = new ArrayList<>();
        List<DebtInfo> zxDebtList = new ArrayList<>();
        List<DebtInfo> cpwsDebtList = new ArrayList<>();

        Map<String,String> anNoMap = new HashMap<>();
        Map<String,CaseRoleInfo> cpwsRoleMap = new HashMap<>();
        Map<String,CaseRoleInfo> zxRoleMap = new HashMap<>();
        Map<String,CaseRoleInfo> zbRoleMap = new HashMap<>();
        for (InfoListEntity infoList : jsonEntity.getInfoList()) {
            zbList.addAll(infoList.getZbList());
            zxList.addAll(infoList.getZxList());
            cpwsList.addAll(infoList.getCaseList());

            for (ZbListEntity data : infoList.getZbList()) {
                anNoMap.put("ZB_"+data.getId(),infoList.getAnno());
                zbRoleMap.put("ZB_"+data.getId(),new CaseRoleInfo(infoList.getProsecutor(),infoList.getDefendant()));
            }
            for (ZXListEntity data : infoList.getZxList()) {
                anNoMap.put("ZX_"+data.getId(),infoList.getAnno());
                zxRoleMap.put("ZX_"+data.getId(),new CaseRoleInfo(infoList.getProsecutor(),infoList.getDefendant()));
            }
            for (CaseListEntity data : infoList.getCaseList()) {
                anNoMap.put("CPWS_"+data.getId(),infoList.getAnno());
                cpwsRoleMap.put("CPWS_"+data.getId(),new CaseRoleInfo(infoList.getProsecutor(),infoList.getDefendant()));
            }
        }

        for (ZbListEntity item : zbList) {
            BigDecimal amount = null;
            String amountType = "";

            BigDecimal failureactDec = getAmt(item.getFailureAct());
            BigDecimal executeobjectDec = getAmt(item.getExecuteObject());
            if(failureactDec != null && BigDecimal.ZERO.compareTo(failureactDec)<0){
                amount = failureactDec;
                amountType = "ZB_未履行金额";

            }
            if(amount  ==  null ){
                if(executeobjectDec != null && BigDecimal.ZERO.compareTo(executeobjectDec)<0){
                    amount = executeobjectDec;
                    amountType = "ZB_执行标的";

                }
            }
            if(amount == null){
                continue;
            }
            for (NameAndKeyNoEntity nameKey : item.getNameAndKeyNo()) {
                DebtInfo debtInfo = new DebtInfo();
                debtInfo.setZqrKeyNo("");
                debtInfo.setZqrName("");
                debtInfo.setZwrKeyNo(nameKey.getKeyNo());
                debtInfo.setZwrName(nameKey.getName());
                debtInfo.setAmount(amount);
                debtInfo.setAmountType(amountType);
                debtInfo.setIsValid(item.getIsValid());
                debtInfo.setTimeStamp(item.getJudgeDate() == null ? 0:item.getJudgeDate());
                debtInfo.setWId(item.getId());
                debtInfo.setAnNo(anNoMap.get("ZB_"+item.getId()));
                zbDebtList.add(debtInfo);
            }
        }

        for (ZXListEntity item : zxList) {
            BigDecimal amount = null;
            String amountType = "";

            BigDecimal biaodi = getAmt(item.getBiaodi());
            if(biaodi != null && BigDecimal.ZERO.compareTo(biaodi)<0){
                amount = biaodi;
                amountType = "ZX_执行标的";
            }
            if(amount == null){
                continue;
            }
            for (NameAndKeyNoEntity nameKey : item.getNameAndKeyNo()) {
                //申请人为空
                if(CollectionUtils.isEmpty(item.getSqrNameAndKeyNo())){
                    DebtInfo debtInfo = new DebtInfo();
                    debtInfo.setZqrKeyNo("");
                    debtInfo.setZqrName("");
                    debtInfo.setZwrKeyNo(nameKey.getKeyNo());
                    debtInfo.setZwrName(nameKey.getName());
                    debtInfo.setAmount(amount);
                    debtInfo.setAmountType(amountType);
                    debtInfo.setIsValid(item.getIsValid());
                    debtInfo.setTimeStamp(item.getLianDate() == null ? 0:item.getLianDate());
                    debtInfo.setWId(item.getId());
                    debtInfo.setAnNo(anNoMap.get("ZX_"+item.getId()));
                    zxDebtList.add(debtInfo);
                }else{
                    for (NameAndKeyNoEntity zqr : item.getSqrNameAndKeyNo()) {
                        DebtInfo debtInfo = new DebtInfo();
                        debtInfo.setZqrKeyNo(zqr.getKeyNo());
                        debtInfo.setZqrName(zqr.getName());
                        debtInfo.setZwrKeyNo(nameKey.getKeyNo());
                        debtInfo.setZwrName(nameKey.getName());
                        debtInfo.setAmount(amount);
                        debtInfo.setAmountType(amountType);
                        debtInfo.setIsValid(item.getIsValid());
                        debtInfo.setTimeStamp(item.getLianDate() == null ? 0:item.getLianDate());
                        debtInfo.setWId(item.getId());
                        debtInfo.setAnNo(anNoMap.get("ZX_"+item.getId()));
                        zxDebtList.add(debtInfo);
                    }
                }

            }
        }

        for (DebtInfo debtInfo : zxDebtList) {
            if (Strings.isNullOrEmpty(debtInfo.getZqrName()) && Strings.isNullOrEmpty(debtInfo.getZqrName())) {
                //申请人为空的被执行 补充申请人信息
                List<DebtInfo> sameZwrZXList = new ArrayList<>();
                for (DebtInfo zx : zxDebtList) {
                    if (zx.sameZwr(debtInfo.getZwrKeyNo(), debtInfo.getZwrName())
                            && !Strings.isNullOrEmpty(zx.getZqrName())
                            && !Strings.isNullOrEmpty(zx.getZqrName())
                    ) {
                        sameZwrZXList.add(zx);
                    }
                }
                if (CollectionUtils.isNotEmpty(sameZwrZXList)) {
                    DebtInfo sameZX = sameZwrZXList.stream()
                            .sorted(Comparator.comparing(DebtInfo::getTimeStamp).reversed()).findFirst().get();
                    debtInfo.setZqrKeyNo(sameZX.getZqrKeyNo());
                    debtInfo.setZqrName(sameZX.getZqrName());

                }
            }
        }

        Iterator<DebtInfo> infoIterator = zxDebtList.iterator();
        List<DebtInfo> tmpList = new ArrayList<>();
        //被执行使用同案号原告作补充申请人
//        for (DebtInfo debtInfo : zxDebtList) {
        while (infoIterator.hasNext()){
            DebtInfo debtInfo = infoIterator.next();
            if (Strings.isNullOrEmpty(debtInfo.getZqrName()) && Strings.isNullOrEmpty(debtInfo.getZqrName())) {
                CaseRoleInfo caseRoleInfo = zxRoleMap.get("ZX_"+debtInfo.getWId());
                Map<String, List<NameAndKeyNoEntity>> roleMap  = getYgBgList(caseRoleInfo);
                List<NameAndKeyNoEntity> ygList = roleMap.get("ygList");
                List<NameAndKeyNoEntity> bgList = roleMap.get("bgList");

                //原被告有一个为空 认为债权关系无效
                if(CollectionUtils.isEmpty(ygList) || CollectionUtils.isEmpty(bgList)){
                    continue;
                }
                //债务人 = 被告
                long sameBgCount = bgList.stream().filter(bg->debtInfo.sameZwr(bg.getKeyNo(),bg.getName())).count();
                if(sameBgCount > 0){
                    infoIterator.remove();
                    for (NameAndKeyNoEntity yg : ygList) {
                        DebtInfo copy = new  DebtInfo();
                        BeanUtils.copyProperties(copy,debtInfo);
                        copy.setZqrKeyNo(yg.getKeyNo());
                        copy.setZqrName(yg.getName());
                        tmpList.add(copy);
                    }
                }
            }
        }

        //添加补充完申请人的执行信息
        zxDebtList.addAll(tmpList);


        //按时间倒叙 去重的时候会保留最新的执行
        zxDebtList.sort(Comparator.comparing(DebtInfo::getTimeStamp).reversed());

        for (DebtInfo zb : zbDebtList) {
            //找到终本 同案 被执行的 疑似申请执行人
            List<DebtInfo> sameAnNoZXList = new ArrayList<>();
            for (DebtInfo zx : zxDebtList) {
                if(Objects.equals(zb.getAnNo(),zx.getAnNo())){
                    if(zx.sameZwr(zb.getZwrKeyNo(),zb.getZwrName())
                            && !Strings.isNullOrEmpty(zx.getZqrName())){
                        sameAnNoZXList.add(zx);
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(sameAnNoZXList)){
                DebtInfo sameZX = sameAnNoZXList.stream()
                        .sorted(Comparator.comparing(DebtInfo::getTimeStamp).reversed()).findFirst().get();

                zb.setZqrKeyNo(sameZX.getZqrKeyNo());
                zb.setZqrName(sameZX.getZqrName());
            }
        }

        infoIterator = zbDebtList.iterator();
        tmpList = new ArrayList<>();
        //使用同案号原告作补充申请人
        while (infoIterator.hasNext()){
            DebtInfo zb = infoIterator.next();
            if (Strings.isNullOrEmpty(zb.getZqrName()) && Strings.isNullOrEmpty(zb.getZqrName())) {
                CaseRoleInfo caseRoleInfo = zbRoleMap.get("ZB_"+zb.getWId());
                Map<String, List<NameAndKeyNoEntity>> roleMap  = getYgBgList(caseRoleInfo);
                List<NameAndKeyNoEntity> ygList = roleMap.get("ygList");
                List<NameAndKeyNoEntity> bgList = roleMap.get("bgList");

                //原被告有一个为空 认为债权关系无效
                if(CollectionUtils.isEmpty(ygList) || CollectionUtils.isEmpty(bgList)){
                    continue;
                }
                //债务人 = 被告
                long sameBgCount = bgList.stream().filter(bg->zb.sameZwr(bg.getKeyNo(),bg.getName())).count();
                if(sameBgCount > 0){
                    infoIterator.remove();
                    for (NameAndKeyNoEntity yg : ygList) {
                        DebtInfo copy = new  DebtInfo();
                        BeanUtils.copyProperties(copy,zb);
                        copy.setZqrKeyNo(yg.getKeyNo());
                        copy.setZqrName(yg.getName());
                        tmpList.add(copy);
                    }
                }
            }
        }

        //添加补充完申请人的执行信息
        zbDebtList.addAll(tmpList);


        List<DebtInfo> markedDebtList = new ArrayList<>();
        infoIterator= zbDebtList.iterator();
        while (infoIterator.hasNext()){
            DebtInfo debtInfo = infoIterator.next();
            if(!Strings.isNullOrEmpty(debtInfo.getZqrKeyNo()) || !Strings.isNullOrEmpty(debtInfo.getZqrName())){
                markedDebtList.add(debtInfo);
                infoIterator.remove();
            }
        }

        infoIterator= zxDebtList.iterator();
        while (infoIterator.hasNext()){
            DebtInfo debtInfo = infoIterator.next();
            long sameCount = markedDebtList.stream()
                    .filter(data->data.sameZqr(debtInfo.getZqrKeyNo(),debtInfo.getZqrName())
                            && data.sameZwr(debtInfo.getZwrKeyNo(),debtInfo.getZwrName())).count();
            if(sameCount>0L){
                infoIterator.remove();
            }else{
                //未和终本匹配的被执行单独成关系
                if(!Strings.isNullOrEmpty(debtInfo.getZqrKeyNo()) || !Strings.isNullOrEmpty(debtInfo.getZqrName())){
                    infoIterator.remove();
                    markedDebtList.add(debtInfo);
                }
            }
        }

//        System.out.println("zbDebtList："+JSONArray.toJSONString(zbDebtList));
//        System.out.println("zxDebtList："+JSONArray.toJSONString(zxDebtList));
//        System.out.println("markedDebtList："+JSONArray.toJSONString(markedDebtList));

        for (CaseListEntity item : cpwsList) {
            BigDecimal amount = getAmt(item.getAmt());
            if(amount == null){
                continue;
            }
            String amountType = "CPWS_案件金额";
            CaseRoleInfo caseRoleInfo = cpwsRoleMap.get("CPWS_"+item.getId());
            Map<String, List<NameAndKeyNoEntity>> roleMap  = getYgBgList(caseRoleInfo);
            List<NameAndKeyNoEntity> ygList = roleMap.get("ygList");
            List<NameAndKeyNoEntity> bgList = roleMap.get("bgList");

            //原被告有一个为空 认为债权关系无效
            if(CollectionUtils.isEmpty(ygList) || CollectionUtils.isEmpty(bgList)){
                continue;
            }

            for (NameAndKeyNoEntity yg : ygList) {
                for (NameAndKeyNoEntity bg : bgList) {
                    // 判断裁判文书数据是否在已经标记的 终本+被执行中
                    long sameCount = markedDebtList.stream()
                            .filter(data->data.sameZwr(bg.getKeyNo(),bg.getName())).count();

//                    System.out.println("sameCount:"+sameCount);

                    //补充裁判文书债务关系
                    if(sameCount == 0){
                        DebtInfo debtInfo = new DebtInfo();
                        debtInfo.setZqrKeyNo(yg.getKeyNo());
                        debtInfo.setZqrName(yg.getName());
                        debtInfo.setZwrKeyNo(bg.getKeyNo());
                        debtInfo.setZwrName(bg.getName());
                        debtInfo.setAmount(amount);
                        debtInfo.setAmountType(amountType);
                        debtInfo.setIsValid(item.getIsValid());
                        debtInfo.setTimeStamp(item.getJudgeDate() == null ? 0:item.getJudgeDate());
                        debtInfo.setWId(item.getId());
                        debtInfo.setAnNo(anNoMap.get("CPWS_"+item.getId()));
                        cpwsDebtList.add(debtInfo);
                    }
                }
            }

        }

//        System.out.println("cpwsDebtList："+JSONArray.toJSONString(cpwsDebtList));

        //所有债务关系
        List<DebtInfo> allDebtList = new ArrayList<>();
        //汇总有债权人keyNo的数据
        allDebtList.addAll(cpwsDebtList.stream()
                .filter(data->!Strings.isNullOrEmpty(data.getZqrKeyNo())).collect(Collectors.toList()));
        allDebtList.addAll(markedDebtList.stream()
                .filter(data->!Strings.isNullOrEmpty(data.getZqrKeyNo())).collect(Collectors.toList()));

//        System.out.println("allDebtList："+JSONArray.toJSONString(allDebtList));

        //分组债务关系
        Map<String,List<DebtInfo>> debtGroupMap = allDebtList.stream().collect(Collectors.groupingBy(DebtInfo::getMark));

//        System.out.println("debtGroupMap："+JSONArray.toJSONString(debtGroupMap));

        //最终债务关系
        List<DebtInfo> finalDebtList = new ArrayList<>();

        debtGroupMap.forEach((k,v)->{
            DebtInfo debtInfo = v.stream()
                    .sorted(Comparator.comparing(DebtInfo::getTimeStamp).reversed()
                            .thenComparing(DebtInfo::getAmount,Comparator.reverseOrder())).findFirst().get();
            if(debtInfo.getIsValid() == 1){
                finalDebtList.add(debtInfo);
            }
        });

//        System.out.println("finalDebtList："+JSONArray.toJSONString(finalDebtList));

        Map<String,String> zqrNameMap = new HashMap<>();
        for (DebtInfo debtInfo : finalDebtList) {
            zqrNameMap.put(debtInfo.getZqrKeyNo(),debtInfo.getZqrName());
        }

        Map<String,List<DebtInfo>> zqrGroupMap = finalDebtList.stream().collect(Collectors.groupingBy(DebtInfo::getZqrKeyNo));
//        System.out.println("zqrGroupMap："+JSONArray.toJSONString(zqrGroupMap));
        List< Map<String,Object>> outList = new ArrayList<>();
        zqrGroupMap.forEach((k,v)->{
            String zqrName = zqrNameMap.get(k);
            List<ZwrDebtInfo> list = new ArrayList<>();
            Set<String> amountInfo = new HashSet<>();
            List<AmountType> amountTypeList = new ArrayList<>();
            for (DebtInfo debtInfo : v) {
                ZwrDebtInfo debt = new ZwrDebtInfo();
                debt.setAmount(debtInfo.getAmount());
                debt.setAmountType(debtInfo.getAmountType());
                debt.setZwrKeyNo(debtInfo.getZwrKeyNo());
                debt.setZwrName(debtInfo.getZwrName());
                amountInfo.add(debtInfo.getAmount().toString());
                amountTypeList.add(new AmountType(debtInfo.getAmount(),debtInfo.getTimeStamp()));
                list.add(debt);
            }
            //按时间倒序取最新金额
           BigDecimal latestAmount =  amountTypeList.stream()
                    .sorted(Comparator.comparing(AmountType::getTimeStamp).reversed().thenComparing(AmountType::getAmount))
                    .findFirst().get().getAmount();
            Map<String,Object> finalGroupMap = new HashMap<>();
            finalGroupMap.put("zqrInfo",k+","+zqrName);
            finalGroupMap.put("zwrInfo",list);
            finalGroupMap.put("amountInfo",amountInfo.stream().sorted().collect(Collectors.joining(",")));
            finalGroupMap.put("amountCount",amountInfo.size());
            finalGroupMap.put("latestAmount",latestAmount);
            outList.add(finalGroupMap);
        });


        return JSONArray.toJSONString(outList);
    }

    /**
     * 生成原被告信息
     * @param caseRoleInfo
     * @return
     */
    static Map<String, List<NameAndKeyNoEntity>> getYgBgList(CaseRoleInfo caseRoleInfo){
        Map<String, List<NameAndKeyNoEntity>> result = new HashMap<>();
        List<NameAndKeyNoEntity> ygList = new ArrayList<>();
        List<NameAndKeyNoEntity> bgList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(caseRoleInfo.getProsecutor())){
            for (NameAndKeyNoEntity entity : caseRoleInfo.getProsecutor()) {
                if(isYg(entity.getRole())){
                    ygList.add(entity);
                    continue;
                }
                if(isBg(entity.getRole())){
                    bgList.add(entity);
                    continue;
                }
            }
        }
        if(CollectionUtils.isNotEmpty(caseRoleInfo.getDefendant())){
            for (NameAndKeyNoEntity entity : caseRoleInfo.getDefendant()) {
                if(isYg(entity.getRole())){
                    ygList.add(entity);
                    continue;
                }
                if(isBg(entity.getRole())){
                    bgList.add(entity);
                    continue;
                }
            }
        }

        result.put("ygList",ygList);
        result.put("bgList",bgList);
        return result;
    }

    static BigDecimal getAmt(String money){
        try{
            return new BigDecimal(money);
        }catch (Exception e){
            return null;
        }
    }

    static boolean isYg(String name){
        Pattern p2 = Pattern.compile("(申请执行人)|(原告)|(申请人)|(被上诉人\\(原审原告\\))|(复议申请人)|(上诉人\\(原审原告\\))|(原告\\(反诉被告\\))|(上诉人)|(被上诉人\\(一审原告\\))|(上诉人\\(一审原告\\))|(被上诉人\\(原审原告反诉被告\\))|" +
                "(原审原告)|(再审申请人)|(被告\\(原告\\))|(被申请人\\(原审原告\\))|(附带民事诉讼原告人)|(复议申请人\\(原申请执行人\\))|(再审申请人\\(一审原告二审上诉人\\))|(再审申请人\\(原审原告\\))|(申请再审人\\(一审原告二审上诉人\\))|" +
                "(二审上诉人)|(原告人)|(附带民事诉讼原告)|(上诉人\\(原审原告原审被告\\))|(起诉人)|(申请人\\(仲裁申请人\\))|(赔偿请求人)");
        return p2.matcher(name).find();
    }

    static boolean isBg(String name){
        Pattern p1 = Pattern.compile("(被执行人)|(被告)|(被申请人)|(被申请执行人)|(原审被告)|(被上诉人\\(原审被告\\))|(上诉人\\(原审被告\\))|(被告\\(反诉原告\\))|(被告人)|(上诉人\\(一审被告\\))|" +
                "(被上诉人\\(一审被告\\))|(被上诉人)|(上诉人\\(原审被告反诉原告\\))|(被告二)|(被告一)|(原告\\(被告\\))|(被申请人\\(一审被告二审被上诉人\\))|(被申请人\\(原审被告\\))|(再审申请人\\(一审被告二审上诉人\\))|" +
                "(再审申请人\\(原审被告\\))|(被申请人\\(仲裁被申请人\\))|(被申请人\\(原被执行人\\))|(再审被申请人)|(上诉人\\(原审被告原审原告\\))");
        return p1.matcher(name).find();

    }


    public static void main(String[] args) throws InvocationTargetException, IllegalAccessException {
//        String json = "[{\"AmtInfo\":{\"dde9aa59389b584d66438912d130f3f1\":{\"Amt\":\"88708279.68\",\"IsValid\":\"1\",\"Type\":\"案件金额\"},\"p3483c261f318de061fb3ded0508bf90\":{\"Amt\":\"89876936\",\"IsValid\":\"1\",\"Type\":\"执行标的\"}},\"AnNoList\":\"（2019）浙02民初873号,（2020）浙民辖终29号,（2021）浙02执22号\",\"AnnoCnt\":3,\"CaseCnt\":2,\"CaseName\":\"贾跃亭与江苏红土创业投资管理有限公司合同纠纷的案件\",\"CaseReason\":\"合同纠纷\",\"CaseRole\":\"[{\\\"D\\\":\\\"管辖上诉上诉人,一审被告,首次执行被执行人\\\",\\\"N\\\":\\\"p3483c261f318de061fb3ded0508bf90\\\",\\\"O\\\":2,\\\"P\\\":\\\"贾跃亭\\\",\\\"R\\\":\\\"上诉人（原审被告）\\\"},{\\\"D\\\":\\\"管辖上诉被上诉人,一审原告\\\",\\\"N\\\":\\\"dde9aa59389b584d66438912d130f3f1\\\",\\\"O\\\":0,\\\"P\\\":\\\"江苏红土创业投资管理有限公司\\\",\\\"R\\\":\\\"被上诉人（原审原告）\\\"}]\",\"CaseType\":\"执行案件,民事案件\",\"CfdfCnt\":0,\"CfgsCnt\":0,\"CfxyCnt\":0,\"CompanyKeywords\":\"dde9aa59389b584d66438912d130f3f1,p3483c261f318de061fb3ded0508bf90,江苏红土创业投资管理有限公司,贾跃亭\",\"CourtList\":\"浙江省宁波市中级人民法院,浙江省高级人民法院\",\"EarliestDate\":1564070400,\"EarliestDateType\":\"民事一审|立案日期\",\"FyggCnt\":0,\"GqdjCnt\":0,\"GroupId\":\"dde543ca08385aeadaf3e60bc2e6b363\",\"HbcfCnt\":0,\"Id\":\"bb746bfe76170b0d4bd4bd2d5ac073f5\",\"InfoList\":[{\"AnNo\":\"（2019）浙02民初873号\",\"CaseList\":[{\"Amt\":\"88708279.68\",\"CaseType\":\"民事判决书\",\"DocType\":\"判决日期\",\"Id\":\"6c01e3eb12c47ec6df7b35ca16eb225b0\",\"IsValid\":1,\"JudgeDate\":1601308800,\"Result\":\"被告贾跃亭应于本判决生效之日起十日内向原告江苏红土创业投资管理有限公司支付补偿款88708279.68元。\",\"ResultType\":\"判决结果\"}],\"CaseReason\":\"合同纠纷\",\"CaseType\":\"民事案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"浙江省宁波市中级人民法院\",\"Defendant\":[{\"KeyNo\":\"p3483c261f318de061fb3ded0508bf90x\",\"Name\":\"贾跃亭x\",\"Org\":2,\"Role\":\"被告\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[{\"ExecuteUnite\":\"第十四审判庭\",\"Id\":\"d4b62f2f5419c01d9cf817140122f20c5\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"dde9aa59389b584d66438912d130f3f1\",\"Name\":\"江苏红土创业投资管理有限公司\",\"Org\":0},{\"KeyNo\":\"p3483c261f318de061fb3ded0508bf90\",\"Name\":\"贾跃亭\",\"Org\":2}],\"OpenDate\":1578447000},{\"ExecuteUnite\":\"第十审判庭\",\"Id\":\"ed1c39de778dabf308775dd6b33be7b05\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"dde9aa59389b584d66438912d130f3f1\",\"Name\":\"江苏红土创业投资管理有限公司\",\"Org\":0},{\"KeyNo\":\"p3483c261f318de061fb3ded0508bf90\",\"Name\":\"贾跃亭\",\"Org\":2}],\"OpenDate\":1588054500}],\"LatestTimestamp\":1601308800,\"LianList\":[{\"Id\":\"6c01e3eb12c47ec6df7b35ca16eb225b\",\"IsValid\":1,\"LianDate\":1564070400,\"NameAndKeyNo\":[{\"KeyNo\":\"dde9aa59389b584d66438912d130f3f1x\",\"Name\":\"江苏红土创业投资管理有限公司x\",\"Org\":0},{\"KeyNo\":\"p3483c261f318de061fb3ded0508bf90x\",\"Name\":\"贾跃亭x\",\"Org\":2}]}],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"dde9aa59389b584d66438912d130f3f1x\",\"Name\":\"江苏红土创业投资管理有限公司x\",\"Org\":0,\"Role\":\"原告\"}],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"民事一审\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]},{\"AnNo\":\"（2020）浙民辖终29号\",\"CaseList\":[{\"Amt\":\"\",\"CaseType\":\"民事裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"434fdf731e0aad2426dd8b47953754160\",\"IsValid\":1,\"JudgeDate\":1583769600,\"Result\":\"驳回上诉，维持原裁定。\",\"ResultType\":\"裁定结果\"}],\"CaseReason\":\"合同纠纷\",\"CaseType\":\"民事案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"浙江省高级人民法院\",\"Defendant\":[{\"KeyNo\":\"dde9aa59389b584d66438912d130f3f1\",\"Name\":\"江苏红土创业投资管理有限公司\",\"Org\":0,\"Role\":\"被上诉人（原审原告）\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1583769600,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"p3483c261f318de061fb3ded0508bf90\",\"Name\":\"贾跃亭\",\"Org\":2,\"Role\":\"上诉人（原审被告）\"}],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"民事管辖上诉\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]},{\"AnNo\":\"（2021）浙02执22号\",\"CaseList\":[],\"CaseReason\":\"\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"浙江省宁波市中级人民法院\",\"Defendant\":[{\"KeyNo\":\"p3483c261f318de061fb3ded0508bf90\",\"Name\":\"贾跃亭\",\"Org\":2,\"Role\":\"被执行人\"}],\"ExecuteNo\":\"（2019）浙02民初873号\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1615132800,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[],\"SdggList\":[],\"SxList\":[{\"ActionType\":\"违反财产报告制度\",\"ExecuteStatus\":\"全部未履行\",\"Id\":\"f5141fd12f855ad02175f89848bffce72\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"p3483c261f318de061fb3ded0508bf90\",\"Name\":\"贾跃亭\",\"Org\":2}],\"PublishDate\":1615132800}],\"TrialRound\":\"首次执行\",\"XgList\":[{\"CompanyInfo\":[{\"KeyNo\":\"p3483c261f318de061fb3ded0508bf90\",\"Name\":\"贾跃亭\",\"Org\":2}],\"GlNameAndKeyNo\":[],\"Id\":\"ab2e9a402c9a52518b94659c6b22a8a8\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"p3483c261f318de061fb3ded0508bf90\",\"Name\":\"贾跃亭\",\"Org\":2}],\"PublishDate\":1610380800,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"dde9aa59389b584d66438912d130f3f1\",\"Name\":\"江苏红土创业投资管理有限公司\",\"Org\":0}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"p3483c261f318de061fb3ded0508bf90\",\"Name\":\"贾跃亭\",\"Org\":2}]}],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[{\"Biaodi\":\"89876936\",\"Id\":\"f5141fd12f855ad02175f89848bffce71\",\"IsValid\":1,\"LianDate\":1609776000,\"NameAndKeyNo\":[{\"KeyNo\":\"p3483c261f318de061fb3ded0508bf90\",\"Name\":\"贾跃亭\",\"Org\":2}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"dde9aa59389b584d66438912d130f3f1\",\"Name\":\"江苏红土创业投资管理有限公司\",\"Org\":0}]}]}],\"KtggCnt\":2,\"LastestDate\":1615132800,\"LastestDateType\":\"首次执行|失信人发布日期\",\"LatestTrialRound\":\"首次执行\",\"LianCnt\":1,\"PcczCnt\":0,\"ProcuratorateList\":\"\",\"Province\":\"ZJ\",\"SdggCnt\":0,\"Source\":\"OT\",\"SxCnt\":1,\"Tags\":\"1,2,3,4,11,12\",\"Type\":1,\"XgCnt\":1,\"XjpgCnt\":0,\"XzcfCnt\":0,\"ZbCnt\":0,\"ZxCnt\":1}] ";

//        String json = "[{\"AmtInfo\":{\"31634b60adaab342a23ef8ad682f5cd4\":{\"Amt\":\"*********\",\"IsValid\":\"1\",\"Type\":\"未履行金额\"},\"9da51287f433eb974e5e4e764765582b\":{\"Amt\":\"*********\",\"IsValid\":\"1\",\"Type\":\"未履行金额\"},\"e2b65a6f28f79defc2d61e75f025a947\":{\"Amt\":\"*********\",\"IsValid\":\"1\",\"Type\":\"未履行金额\"},\"p3483c261f318de061fb3ded0508bf90\":{\"Amt\":\"*********\",\"IsValid\":\"1\",\"Type\":\"未履行金额\"}},\"AnNoList\":\"（2018）京04民初316号,（2018）京民辖终239号,（2019）京民终646号,（2020）京04执98号,（2020）京04执异28号\",\"AnnoCnt\":5,\"CaseCnt\":4,\"CaseName\":\"乐视控股（北京）有限公司与乐视体育文化产业发展（北京）有限公司,厦门章鱼互动网络科技有限公司,浙商银行股份有限公司北京分行等金融借款合同纠纷的案件\",\"CaseReason\":\"金融借款合同纠纷\",\"CaseRole\":\"[{\\\"D\\\":\\\"管辖上诉上诉人,一审被告,二审原审被告,执行异议被执行人,首次执行被执行人\\\",\\\"N\\\":\\\"9da51287f433eb974e5e4e764765582b\\\",\\\"O\\\":0,\\\"P\\\":\\\"乐视控股（北京）有限公司\\\",\\\"R\\\":\\\"上诉人（原审被告）\\\"},{\\\"D\\\":\\\"管辖上诉原审被告,一审被告,二审原审被告,执行异议被执行人,首次执行被执行人\\\",\\\"N\\\":\\\"31634b60adaab342a23ef8ad682f5cd4\\\",\\\"O\\\":0,\\\"P\\\":\\\"乐视体育文化产业发展（北京）有限公司\\\",\\\"R\\\":\\\"原审被告\\\"},{\\\"D\\\":\\\"管辖上诉原审被告,一审被告,二审原审被告,执行异议被执行人,首次执行被执行人\\\",\\\"N\\\":\\\"e2b65a6f28f79defc2d61e75f025a947\\\",\\\"O\\\":0,\\\"P\\\":\\\"厦门章鱼互动网络科技有限公司\\\",\\\"R\\\":\\\"原审被告\\\"},{\\\"D\\\":\\\"管辖上诉被上诉人,一审原告,二审被上诉人,执行异议申请执行人\\\",\\\"N\\\":\\\"31dcbcb451456b9818ba88fbbce5b6c0\\\",\\\"O\\\":0,\\\"P\\\":\\\"浙商银行股份有限公司北京分行\\\",\\\"R\\\":\\\"被上诉人（原审原告）\\\"},{\\\"D\\\":\\\"管辖上诉原审被告,一审被告,二审上诉人,执行异议被执行人,首次执行被执行人\\\",\\\"N\\\":\\\"p3483c261f318de061fb3ded0508bf90\\\",\\\"O\\\":2,\\\"P\\\":\\\"贾跃亭\\\",\\\"R\\\":\\\"原审被告\\\"}]\",\"CaseType\":\"执行案件,民事案件\",\"CfdfCnt\":0,\"CfgsCnt\":0,\"CfxyCnt\":0,\"CompanyKeywords\":\"31634b60adaab342a23ef8ad682f5cd4,31dcbcb451456b9818ba88fbbce5b6c0,9da51287f433eb974e5e4e764765582b,e2b65a6f28f79defc2d61e75f025a947,p0e2a342fde6d58f5d1fb6654cafde26,p3483c261f318de061fb3ded0508bf90,pb2b08aeccb7d9c9b553d47e5954b22c,pr25632031b3364cafbcc9c9d439c18a,乐视体育文化产业发展(北京)有限公司,乐视体育文化产业发展（北京）有限公司,乐视控股(北京)有限公司,乐视控股（北京）有限公司,侯有志,厦门章鱼互动网络科技有限公司,合伙企业(有限合伙),吴孟,浙商银行股份有限公司北京分行,贾跃亭,雷振剑\",\"CourtList\":\"北京市第四中级人民法院,北京市高级人民法院\",\"EarliestDate\":1531324800,\"EarliestDateType\":\"民事一审|立案日期\",\"FyggCnt\":0,\"GqdjCnt\":0,\"GroupId\":\"6d1a2170fa4a0bb40fcb330061b801ba\",\"HbcfCnt\":0,\"Id\":\"dfd8d22cbab359e5470d6127f0aa761c\",\"InfoList\":[{\"AnNo\":\"（2018）京04民初316号\",\"CaseList\":[{\"Amt\":\"\",\"CaseType\":\"民事判决书\",\"DocType\":\"判决日期\",\"Id\":\"6974d1adfdb1ca25578ac86dd09cab150\",\"IsValid\":1,\"JudgeDate\":1556553600,\"Result\":\"一、被告乐视体育文化产业发展（北京）有限公司于本判决生效后十日内给付原告浙商银行股份有限公司北京分行借款本金195 259 283.03元及期内利息10 556 000元；  二、被告乐视体育文化产业发展（北京）有限公司于本判决生效后十日内给付原告浙商银行股份有限公司北京分行罚息、复利（截至2018年6月25日产生的罚息为212 344.47元，并自2018年6月26日起计算至实际付清之日止，以本金195 259 283.03元为基数，按年利率7.83%计付；截至2018年6月25日产生的复利为11 479.65元，并自2018年6月26日起计算至实际付清之日止，以利息10 556 000元为基数，按年利率7.83%计付）；  三、被告乐视控股（北京）有限公司、贾跃亭、厦门章鱼互动网络科技有限公司对本判决第一、二项确定的债务承担连带保证责任；  四、被告乐视控股（北京）有限公司、贾跃亭、厦门章鱼互动网络科技有限公司承担保证责任后，有权向被告乐视体育文化产业发展（北京）有限公司追偿。\",\"ResultType\":\"判决结果\"}],\"CaseReason\":\"金融借款合同纠纷\",\"CaseType\":\"民事案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"北京市第四中级人民法院\",\"Defendant\":[{\"KeyNo\":\"31634b60adaab342a23ef8ad682f5cd4\",\"Name\":\"乐视体育文化产业发展（北京）有限公司\",\"Org\":0,\"Role\":\"被告\"},{\"KeyNo\":\"9da51287f433eb974e5e4e764765582b\",\"Name\":\"乐视控股（北京）有限公司\",\"Org\":0,\"Role\":\"被告\"},{\"KeyNo\":\"e2b65a6f28f79defc2d61e75f025a947\",\"Name\":\"厦门章鱼互动网络科技有限公司\",\"Org\":0,\"Role\":\"被告\"},{\"KeyNo\":\"p3483c261f318de061fb3ded0508bf90\",\"Name\":\"贾跃亭\",\"Org\":2,\"Role\":\"被告\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1556553600,\"LianList\":[{\"Id\":\"6974d1adfdb1ca25578ac86dd09cab15\",\"IsValid\":1,\"LianDate\":1531324800,\"NameAndKeyNo\":[{\"KeyNo\":\"31dcbcb451456b9818ba88fbbce5b6c0\",\"Name\":\"浙商银行股份有限公司北京分行\",\"Org\":0},{\"KeyNo\":\"\",\"Name\":\"贾跃亭\",\"Org\":-2},{\"KeyNo\":\"9da51287f433eb974e5e4e764765582b\",\"Name\":\"乐视控股（北京）有限公司\",\"Org\":0},{\"KeyNo\":\"e2b65a6f28f79defc2d61e75f025a947\",\"Name\":\"厦门章鱼互动网络科技有限公司\",\"Org\":0},{\"KeyNo\":\"31634b60adaab342a23ef8ad682f5cd4\",\"Name\":\"乐视体育文化产业发展（北京）有限公司\",\"Org\":0}]}],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"31dcbcb451456b9818ba88fbbce5b6c0\",\"Name\":\"浙商银行股份有限公司北京分行\",\"Org\":0,\"Role\":\"原告\"}],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"民事一审\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]},{\"AnNo\":\"（2018）京民辖终239号\",\"CaseList\":[{\"Amt\":\"\",\"CaseType\":\"民事裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"bdbe367bed3bd1b64058a492aca464370\",\"IsValid\":1,\"JudgeDate\":1542902400,\"Result\":\"驳回上诉，维持原裁定。\",\"ResultType\":\"裁定结果\"}],\"CaseReason\":\"金融借款合同纠纷\",\"CaseType\":\"民事案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"北京市高级人民法院\",\"Defendant\":[{\"KeyNo\":\"31634b60adaab342a23ef8ad682f5cd4\",\"Name\":\"乐视体育文化产业发展（北京）有限公司\",\"Org\":0,\"Role\":\"原审被告\"},{\"KeyNo\":\"e2b65a6f28f79defc2d61e75f025a947\",\"Name\":\"厦门章鱼互动网络科技有限公司\",\"Org\":0,\"Role\":\"原审被告\"},{\"KeyNo\":\"31dcbcb451456b9818ba88fbbce5b6c0\",\"Name\":\"浙商银行股份有限公司北京分行\",\"Org\":0,\"Role\":\"被上诉人（原审原告）\"},{\"KeyNo\":\"p3483c261f318de061fb3ded0508bf90\",\"Name\":\"贾跃亭\",\"Org\":2,\"Role\":\"原审被告\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1542902400,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"9da51287f433eb974e5e4e764765582b\",\"Name\":\"乐视控股（北京）有限公司\",\"Org\":0,\"Role\":\"上诉人（原审被告）\"}],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"民事管辖上诉\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]},{\"AnNo\":\"（2019）京民终646号\",\"CaseList\":[{\"Amt\":\"\",\"CaseType\":\"民事裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"6651bec578f6b86de3663b6895cfceef0\",\"IsValid\":1,\"JudgeDate\":1567094400,\"Result\":\"本案按上诉人贾跃亭自动撤回上诉处理。\",\"ResultType\":\"裁定结果\"}],\"CaseReason\":\"金融借款合同纠纷\",\"CaseType\":\"民事案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"北京市高级人民法院\",\"Defendant\":[{\"KeyNo\":\"31634b60adaab342a23ef8ad682f5cd4\",\"Name\":\"乐视体育文化产业发展（北京）有限公司\",\"Org\":0,\"Role\":\"原审被告\"},{\"KeyNo\":\"9da51287f433eb974e5e4e764765582b\",\"Name\":\"乐视控股（北京）有限公司\",\"Org\":0,\"Role\":\"原审被告\"},{\"KeyNo\":\"e2b65a6f28f79defc2d61e75f025a947\",\"Name\":\"厦门章鱼互动网络科技有限公司\",\"Org\":0,\"Role\":\"原审被告\"},{\"KeyNo\":\"31dcbcb451456b9818ba88fbbce5b6c0\",\"Name\":\"浙商银行股份有限公司北京分行\",\"Org\":0,\"Role\":\"被上诉人（原审原告）\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1567094400,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"p3483c261f318de061fb3ded0508bf90\",\"Name\":\"贾跃亭\",\"Org\":2,\"Role\":\"上诉人（原审被告）\"}],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"民事二审\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]},{\"AnNo\":\"（2020）京04执98号\",\"CaseList\":[],\"CaseReason\":\"\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"北京市第四中级人民法院\",\"Defendant\":[{\"KeyNo\":\"31634b60adaab342a23ef8ad682f5cd4\",\"Name\":\"乐视体育文化产业发展（北京）有限公司\",\"Org\":0,\"Role\":\"被执行人\"},{\"KeyNo\":\"9da51287f433eb974e5e4e764765582b\",\"Name\":\"乐视控股（北京）有限公司\",\"Org\":0,\"Role\":\"被执行人\"},{\"KeyNo\":\"e2b65a6f28f79defc2d61e75f025a947\",\"Name\":\"厦门章鱼互动网络科技有限公司\",\"Org\":0,\"Role\":\"被执行人\"},{\"KeyNo\":\"p3483c261f318de061fb3ded0508bf90\",\"Name\":\"贾跃亭\",\"Org\":2,\"Role\":\"被执行人\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1601308800,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"首次执行\",\"XgList\":[{\"CompanyInfo\":[{\"KeyNo\":\"9da51287f433eb974e5e4e764765582b\",\"Name\":\"乐视控股（北京）有限公司\",\"Org\":0}],\"GlNameAndKeyNo\":[{\"KeyNo\":\"pb2b08aeccb7d9c9b553d47e5954b22c\",\"Name\":\"吴孟\",\"Org\":2}],\"Id\":\"03839700c7600de6f43b91f91d88508d\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"9da51287f433eb974e5e4e764765582b\",\"Name\":\"乐视控股（北京）有限公司\",\"Org\":0}],\"PublishDate\":1595779200,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"31dcbcb451456b9818ba88fbbce5b6c0\",\"Name\":\"浙商银行股份有限公司北京分行\",\"Org\":0}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"9da51287f433eb974e5e4e764765582b\",\"Name\":\"乐视控股（北京）有限公司\",\"Org\":0}]},{\"CompanyInfo\":[{\"KeyNo\":\"p3483c261f318de061fb3ded0508bf90\",\"Name\":\"贾跃亭\",\"Org\":2}],\"GlNameAndKeyNo\":[],\"Id\":\"5d055c788c81cafeb0c5072c51342dfa\",\"IsValid\":0,\"NameAndKeyNo\":[{\"KeyNo\":\"p3483c261f318de061fb3ded0508bf90\",\"Name\":\"贾跃亭\",\"Org\":2}],\"PublishDate\":1595520000,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"31dcbcb451456b9818ba88fbbce5b6c0\",\"Name\":\"浙商银行股份有限公司北京分行\",\"Org\":0}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"p3483c261f318de061fb3ded0508bf90\",\"Name\":\"贾跃亭\",\"Org\":2}]},{\"CompanyInfo\":[{\"KeyNo\":\"31634b60adaab342a23ef8ad682f5cd4\",\"Name\":\"乐视体育文化产业发展（北京）有限公司\",\"Org\":0}],\"GlNameAndKeyNo\":[{\"KeyNo\":\"p0e2a342fde6d58f5d1fb6654cafde26\",\"Name\":\"雷振剑\",\"Org\":2}],\"Id\":\"6006f23897230cf62f339d70ed7e3c24\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"31634b60adaab342a23ef8ad682f5cd4\",\"Name\":\"乐视体育文化产业发展（北京）有限公司\",\"Org\":0}],\"PublishDate\":1595779200,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"31dcbcb451456b9818ba88fbbce5b6c0\",\"Name\":\"浙商银行股份有限公司北京分行\",\"Org\":0}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"31634b60adaab342a23ef8ad682f5cd4\",\"Name\":\"乐视体育文化产业发展（北京）有限公司\",\"Org\":0}]},{\"CompanyInfo\":[{\"KeyNo\":\"e2b65a6f28f79defc2d61e75f025a947\",\"Name\":\"厦门章鱼互动网络科技有限公司\",\"Org\":0}],\"GlNameAndKeyNo\":[{\"KeyNo\":\"pr25632031b3364cafbcc9c9d439c18a\",\"Name\":\"侯有志\",\"Org\":2}],\"Id\":\"c27bde37488c434e3d65eac956660437\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"e2b65a6f28f79defc2d61e75f025a947\",\"Name\":\"厦门章鱼互动网络科技有限公司\",\"Org\":0}],\"PublishDate\":1599753600,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"31dcbcb451456b9818ba88fbbce5b6c0\",\"Name\":\"浙商银行股份有限公司北京分行\",\"Org\":0}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"e2b65a6f28f79defc2d61e75f025a947\",\"Name\":\"厦门章鱼互动网络科技有限公司\",\"Org\":0}]}],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[{\"ExecuteObject\":\"*********\",\"FailureAct\":\"*********\",\"Id\":\"0678fa6a0138d3abcaffedfc6ea7e195\",\"IsValid\":1,\"JudgeDate\":1601308800,\"NameAndKeyNo\":[{\"KeyNo\":\"9da51287f433eb974e5e4e764765582b\",\"Name\":\"乐视控股（北京）有限公司\",\"Org\":0}]},{\"ExecuteObject\":\"*********\",\"FailureAct\":\"*********\",\"Id\":\"780ea1cfeb0de4bc29216cc687f4349e\",\"IsValid\":1,\"JudgeDate\":1601308800,\"NameAndKeyNo\":[{\"KeyNo\":\"p3483c261f318de061fb3ded0508bf90\",\"Name\":\"贾跃亭\",\"Org\":2}]},{\"ExecuteObject\":\"*********\",\"FailureAct\":\"*********\",\"Id\":\"ad3736a9173ccd05a38e9d42a9598051\",\"IsValid\":1,\"JudgeDate\":1601308800,\"NameAndKeyNo\":[{\"KeyNo\":\"31634b60adaab342a23ef8ad682f5cd4\",\"Name\":\"乐视体育文化产业发展（北京）有限公司\",\"Org\":0}]},{\"ExecuteObject\":\"*********\",\"FailureAct\":\"*********\",\"Id\":\"e75a23ba1f3fda7b6140a302bd292bae\",\"IsValid\":1,\"JudgeDate\":1601308800,\"NameAndKeyNo\":[{\"KeyNo\":\"e2b65a6f28f79defc2d61e75f025a947\",\"Name\":\"厦门章鱼互动网络科技有限公司\",\"Org\":0}]}],\"ZxList\":[{\"Biaodi\":\"230046800\",\"Id\":\"2baa72c826e52b7851e6b3e1625dc2421\",\"IsValid\":0,\"LianDate\":1585670400,\"NameAndKeyNo\":[{\"KeyNo\":\"9da51287f433eb974e5e4e764765582b\",\"Name\":\"乐视控股（北京）有限公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"31dcbcb451456b9818ba88fbbce5b6c0\",\"Name\":\"浙商银行股份有限公司北京分行\",\"Org\":0}]},{\"Biaodi\":\"230046800\",\"Id\":\"74793daa4acd90ebfa55489c73fd137f1\",\"IsValid\":0,\"LianDate\":1585670400,\"NameAndKeyNo\":[{\"KeyNo\":\"31634b60adaab342a23ef8ad682f5cd4\",\"Name\":\"乐视体育文化产业发展（北京）有限公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"31dcbcb451456b9818ba88fbbce5b6c0\",\"Name\":\"浙商银行股份有限公司北京分行\",\"Org\":0}]},{\"Biaodi\":\"230046800\",\"Id\":\"96d087e517941d0f68e153eeb8e76c581\",\"IsValid\":0,\"LianDate\":1585670400,\"NameAndKeyNo\":[{\"KeyNo\":\"e2b65a6f28f79defc2d61e75f025a947\",\"Name\":\"厦门章鱼互动网络科技有限公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"31dcbcb451456b9818ba88fbbce5b6c0\",\"Name\":\"浙商银行股份有限公司北京分行\",\"Org\":0}]},{\"Biaodi\":\"230046800\",\"Id\":\"f05ea9da689c43ebdc7bc674b7e75fd61\",\"IsValid\":0,\"LianDate\":1585670400,\"NameAndKeyNo\":[{\"KeyNo\":\"p3483c261f318de061fb3ded0508bf90x\",\"Name\":\"贾跃亭x\",\"Org\":2}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"31dcbcb451456b9818ba88fbbce5b6c0\",\"Name\":\"浙商银行股份有限公司北京分行\",\"Org\":0}]}]},{\"AnNo\":\"（2020）京04执异28号\",\"CaseList\":[{\"Amt\":\"\",\"CaseType\":\"执行裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"2a27c334dba5c0dff2048b6ca0cf19380\",\"IsValid\":1,\"JudgeDate\":1590595200,\"Result\":\"本院（2020）京04执98号执行案件的申请执行人由浙商银行股份有限公司北京分行变更为东方前海腾钧投资管理（杭州）合伙企业（有限合伙）。\",\"ResultType\":\"裁定结果\"}],\"CaseReason\":\"民事案件执行\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"北京市第四中级人民法院\",\"Defendant\":[{\"KeyNo\":\"31634b60adaab342a23ef8ad682f5cd4\",\"Name\":\"乐视体育文化产业发展（北京）有限公司\",\"Org\":0,\"Role\":\"被执行人\"},{\"KeyNo\":\"9da51287f433eb974e5e4e764765582b\",\"Name\":\"乐视控股（北京）有限公司\",\"Org\":0,\"Role\":\"被执行人\"},{\"KeyNo\":\"e2b65a6f28f79defc2d61e75f025a947\",\"Name\":\"厦门章鱼互动网络科技有限公司\",\"Org\":0,\"Role\":\"被执行人\"},{\"KeyNo\":\"p3483c261f318de061fb3ded0508bf90\",\"Name\":\"贾跃亭\",\"Org\":2,\"Role\":\"被执行人\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1590595200,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"31dcbcb451456b9818ba88fbbce5b6c0\",\"Name\":\"浙商银行股份有限公司北京分行\",\"Org\":0,\"Role\":\"申请执行人\"}],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"执行异议\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]}],\"KtggCnt\":0,\"LastestDate\":1601308800,\"LastestDateType\":\"首次执行|案件终本日期\",\"LatestTrialRound\":\"首次执行\",\"LianCnt\":1,\"PcczCnt\":0,\"ProcuratorateList\":\"\",\"Province\":\"BJ\",\"SdggCnt\":0,\"Source\":\"OT\",\"SxCnt\":0,\"Tags\":\"2,3,4,6,12\",\"Type\":1,\"XgCnt\":4,\"XjpgCnt\":0,\"XzcfCnt\":0,\"ZbCnt\":4,\"ZxCnt\":4}]";

//        String json = "[{\"AmtInfo\":{\"84c17a005a759a5e0d875c1ebb6c9846\":{\"Amt\":\"23000\",\"IsValid\":\"1\",\"Type\":\"执行标的\"},\"dfb3dc5aa3dbe24dd24e944720576c71\":{\"Amt\":\"3000.00\",\"IsValid\":\"1\",\"Type\":\"案件金额\"}},\"AnNoList\":\"（2020）京0491执1500号,（2020）京0491民初2058号\",\"AnnoCnt\":2,\"CaseCnt\":1,\"CaseName\":\"北京中影网络传媒技术有限公司与乐视网信息技术（北京）股份有限公司侵害作品信息网络传播权纠纷的案件\",\"CaseReason\":\"侵害作品信息网络传播权纠纷\",\"CaseRole\":\"[{\\\"D\\\":\\\"一审原告\\\",\\\"N\\\":\\\"dfb3dc5aa3dbe24dd24e944720576c71\\\",\\\"O\\\":0,\\\"P\\\":\\\"北京中影网络传媒技术有限公司\\\",\\\"R\\\":\\\"原告\\\"},{\\\"D\\\":\\\"一审被告,首次执行被执行人\\\",\\\"N\\\":\\\"84c17a005a759a5e0d875c1ebb6c9846\\\",\\\"O\\\":0,\\\"P\\\":\\\"乐视网信息技术（北京）股份有限公司\\\",\\\"R\\\":\\\"被告\\\"}]\",\"CaseType\":\"执行案件,民事案件\",\"CfdfCnt\":0,\"CfgsCnt\":0,\"CfxyCnt\":0,\"CompanyKeywords\":\"84c17a005a759a5e0d875c1ebb6c9846,dfb3dc5aa3dbe24dd24e944720576c71,乐视网信息技术(北京)股份有限公司,乐视网信息技术（北京）股份有限公司,北京中影网络传媒技术有限公司\",\"CourtList\":\"北京互联网法院\",\"EarliestDate\":1591579920,\"EarliestDateType\":\"民事一审|开庭时间\",\"FyggCnt\":0,\"GqdjCnt\":0,\"GroupId\":\"72fb0c5d5728970406f7e5910639ef50\",\"HbcfCnt\":0,\"Id\":\"435e1bbe356c0dde8ed94645836722d7\",\"InfoList\":[{\"AnNo\":\"（2020）京0491执1500号\",\"CaseList\":[],\"CaseReason\":\"\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"北京互联网法院\",\"Defendant\":[{\"KeyNo\":\"84c17a005a759a5e0d875c1ebb6c9846\",\"Name\":\"乐视网信息技术（北京）股份有限公司\",\"Org\":0,\"Role\":\"被执行人\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1607443200,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"首次执行\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[{\"Biaodi\":\"23000\",\"Id\":\"937b2bb61ac0e29c46961991fda0fa3d1\",\"IsValid\":1,\"LianDate\":1607443200,\"NameAndKeyNo\":[{\"KeyNo\":\"84c17a005a759a5e0d875c1ebb6c9846\",\"Name\":\"乐视网信息技术（北京）股份有限公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]}]},{\"AnNo\":\"（2020）京0491民初2058号\",\"CaseList\":[{\"Amt\":\"3000.00\",\"CaseType\":\"民事判决书\",\"DocType\":\"判决日期\",\"Id\":\"84e2f0fd5ee65dd7ced29e5ff5efc9ad0\",\"IsValid\":1,\"JudgeDate\":1595520000,\"Result\":\"一、乐视网信息技术(北京)股份有限公司于本判决生效后十日内赔偿原告北京中影网络传媒技术有限公司经济损失20 000元、制止侵权合理费用3000元；  二、驳回原告北京中影网络传媒技术有限公司的其他诉讼请求。\",\"ResultType\":\"判决结果\"}],\"CaseReason\":\"侵害作品信息网络传播权纠纷\",\"CaseType\":\"民事案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"北京互联网法院\",\"Defendant\":[{\"KeyNo\":\"84c17a005a759a5e0d875c1ebb6c9846\",\"Name\":\"乐视网信息技术（北京）股份有限公司\",\"Org\":0,\"Role\":\"被告\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[{\"ExecuteUnite\":\"第十法庭\",\"Id\":\"5546271d78be644bb9dba4fc76de5dc25\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"dfb3dc5aa3dbe24dd24e944720576c71\",\"Name\":\"北京中影网络传媒技术有限公司\",\"Org\":0},{\"KeyNo\":\"84c17a005a759a5e0d875c1ebb6c9846\",\"Name\":\"乐视网信息技术（北京）股份有限公司\",\"Org\":0}],\"OpenDate\":1591579920}],\"LatestTimestamp\":1595520000,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"dfb3dc5aa3dbe24dd24e944720576c71\",\"Name\":\"北京中影网络传媒技术有限公司\",\"Org\":0,\"Role\":\"原告\"}],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"民事一审\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]}],\"KtggCnt\":1,\"LastestDate\":1607443200,\"LastestDateType\":\"首次执行|被执行人立案日期\",\"LatestTrialRound\":\"首次执行\",\"LianCnt\":0,\"PcczCnt\":0,\"ProcuratorateList\":\"\",\"Province\":\"BJ\",\"SdggCnt\":0,\"Source\":\"OT\",\"SxCnt\":0,\"Tags\":\"2,4,11\",\"Type\":1,\"XgCnt\":0,\"XjpgCnt\":0,\"XzcfCnt\":0,\"ZbCnt\":0,\"ZxCnt\":1}]";

//        String json = "[{\"AmtInfo\":{\"531bb2653d363917dea25a3fae337fda\":{\"Amt\":\"53426338.00\",\"IsValid\":\"1\",\"Type\":\"案件金额\"},\"5bc9e6a3da338107ce77c360a024710e\":{\"Amt\":\"*********\",\"IsValid\":\"1\",\"Type\":\"未履行金额\"},\"72010df77a394297ffd63029a915ea07\":{\"Amt\":\"*********\",\"IsValid\":\"1\",\"Type\":\"未履行金额\"},\"84c17a005a759a5e0d875c1ebb6c9846\":{\"Amt\":\"*********\",\"IsValid\":\"1\",\"Type\":\"未履行金额\"},\"p9507cfb847c93e2164dbe679eed072f\":{\"Amt\":\"53426338.00\",\"IsValid\":\"1\",\"Type\":\"案件金额\"},\"pr0767af6e3dbede2d1ff8134b168bb6\":{\"Amt\":\"53426338.00\",\"IsValid\":\"1\",\"Type\":\"案件金额\"},\"pr1b0263756a68c7cd303c575c7fe796\":{\"Amt\":\"53426338.00\",\"IsValid\":\"1\",\"Type\":\"案件金额\"}},\"AnNoList\":\"（2020）京03执862号\",\"AnnoCnt\":1,\"CaseCnt\":3,\"CaseName\":\"上海元祯投资中心（有限合伙）与乐乐互动体育文化发展（北京）有限公司,乐视网信息技术（北京）股份有限公司,北京鹏翼资产管理中心（有限合伙）合伙企业纠纷案件执行的案件\",\"CaseReason\":\"合伙企业纠纷案件执行\",\"CaseRole\":\"[{\\\"D\\\":\\\"首次执行申请执行人\\\",\\\"N\\\":\\\"531bb2653d363917dea25a3fae337fda\\\",\\\"O\\\":0,\\\"P\\\":\\\"上海元祯投资中心（有限合伙）\\\",\\\"R\\\":\\\"申请执行人\\\"},{\\\"D\\\":\\\"首次执行被执行人\\\",\\\"N\\\":\\\"72010df77a394297ffd63029a915ea07\\\",\\\"O\\\":0,\\\"P\\\":\\\"乐乐互动体育文化发展（北京）有限公司\\\",\\\"R\\\":\\\"被执行人\\\"},{\\\"D\\\":\\\"首次执行被执行人\\\",\\\"N\\\":\\\"84c17a005a759a5e0d875c1ebb6c9846\\\",\\\"O\\\":0,\\\"P\\\":\\\"乐视网信息技术（北京）股份有限公司\\\",\\\"R\\\":\\\"被执行人\\\"},{\\\"D\\\":\\\"首次执行被执行人\\\",\\\"N\\\":\\\"5bc9e6a3da338107ce77c360a024710e\\\",\\\"O\\\":0,\\\"P\\\":\\\"北京鹏翼资产管理中心（有限合伙）\\\",\\\"R\\\":\\\"被执行人\\\"}]\",\"CaseType\":\"执行案件\",\"CfdfCnt\":0,\"CfgsCnt\":0,\"CfxyCnt\":0,\"CompanyKeywords\":\"531bb2653d363917dea25a3fae337fda,5bc9e6a3da338107ce77c360a024710e,72010df77a394297ffd63029a915ea07,84c17a005a759a5e0d875c1ebb6c9846,p9507cfb847c93e2164dbe679eed072f,pr0767af6e3dbede2d1ff8134b168bb6,pr1b0263756a68c7cd303c575c7fe796,上海元祯投资中心(有限合伙),乐乐互动体育文化发展(北京)有限公司,乐乐互动体育文化发展（北京）有限公司,乐视网信息技术(北京)股份有限公司,乐视网信息技术（北京）股份有限公司,刘延峰,刘文选,北京鹏翼资产管理中心(有限合伙),北京鹏翼资产管理中心（有限合伙）,高飞\",\"CourtList\":\"北京市第三中级人民法院\",\"EarliestDate\":1589817600,\"EarliestDateType\":\"首次执行|被执行人立案日期\",\"FyggCnt\":0,\"GqdjCnt\":0,\"GroupId\":\"11d6c2c8670b1e9e32355d3e344c219f\",\"HbcfCnt\":0,\"Id\":\"c834c8743ab16e15f2199bc52881f442\",\"InfoList\":[{\"AnNo\":\"（2020）京03执862号\",\"CaseList\":[{\"Amt\":\"\",\"CaseType\":\"执行裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"33b14f5a46bed135df53e8fb3a86f9f00\",\"IsValid\":1,\"JudgeDate\":1594828800,\"Result\":\"终结（2019）京仲裁字第3215号裁决的本次执行程序。\",\"ResultType\":\"裁定结果\"},{\"Amt\":\"\",\"CaseType\":\"执行裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"573bbc9541706dcb1030f75e096c72ac0\",\"IsValid\":1,\"JudgeDate\":1594828800,\"Result\":\"终结（2019）京仲裁字第3215号裁决的本次执行程序。\",\"ResultType\":\"裁定结果\"},{\"Amt\":\"53426338.00\",\"CaseType\":\"执行裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"b37e29c16fb3369a5904842dfa8762e10\",\"IsValid\":1,\"JudgeDate\":1594656000,\"Result\":\"一、冻结、划拨被执行人乐视网信息技术（北京）股份有限公司、乐乐互动体育文化发展（北京）有限公司、北京鹏翼资产管理中心（有限合伙）的银行存款一亿五千三百四十二万六千三百三十八元九角五分。\",\"ResultType\":\"裁定结果\"}],\"CaseReason\":\"合伙企业纠纷案件执行\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"北京市第三中级人民法院\",\"Defendant\":[{\"KeyNo\":\"72010df77a394297ffd63029a915ea07\",\"Name\":\"乐乐互动体育文化发展（北京）有限公司\",\"Org\":0,\"Role\":\"被执行人\"},{\"KeyNo\":\"84c17a005a759a5e0d875c1ebb6c9846\",\"Name\":\"乐视网信息技术（北京）股份有限公司\",\"Org\":0,\"Role\":\"被执行人\"},{\"KeyNo\":\"5bc9e6a3da338107ce77c360a024710e\",\"Name\":\"北京鹏翼资产管理中心（有限合伙）\",\"Org\":0,\"Role\":\"被执行人\"}],\"ExecuteNo\":\"（2019）京仲裁字第3215号\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1594828800,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"531bb2653d363917dea25a3fae337fda\",\"Name\":\"上海元祯投资中心（有限合伙）\",\"Org\":0,\"Role\":\"申请执行人\"}],\"SdggList\":[],\"SxList\":[{\"ActionType\":\"违反财产报告制度\",\"ExecuteStatus\":\"全部未履行\",\"Id\":\"145ecbc9eea4026cb25616e147d10d9f2\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"5bc9e6a3da338107ce77c360a024710e\",\"Name\":\"北京鹏翼资产管理中心（有限合伙）\",\"Org\":0}],\"PublishDate\":1594051200},{\"ActionType\":\"违反财产报告制度\",\"ExecuteStatus\":\"全部未履行\",\"Id\":\"289dc576300470c7558c4297478d09b72\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"84c17a005a759a5e0d875c1ebb6c9846\",\"Name\":\"乐视网信息技术（北京）股份有限公司\",\"Org\":0}],\"PublishDate\":1594051200},{\"ActionType\":\"违反财产报告制度\",\"ExecuteStatus\":\"全部未履行\",\"Id\":\"fbe7caf42f32901eeba51b69bab505762\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"72010df77a394297ffd63029a915ea07\",\"Name\":\"乐乐互动体育文化发展（北京）有限公司\",\"Org\":0}],\"PublishDate\":1594051200}],\"TrialRound\":\"首次执行\",\"XgList\":[{\"CompanyInfo\":[{\"KeyNo\":\"84c17a005a759a5e0d875c1ebb6c9846\",\"Name\":\"乐视网信息技术（北京）股份有限公司\",\"Org\":0}],\"GlNameAndKeyNo\":[{\"KeyNo\":\"pr1b0263756a68c7cd303c575c7fe796\",\"Name\":\"刘延峰\",\"Org\":2}],\"Id\":\"61683f1708d529b76c082ceae6dc7b29\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"84c17a005a759a5e0d875c1ebb6c9846\",\"Name\":\"乐视网信息技术（北京）股份有限公司\",\"Org\":0}],\"PublishDate\":1593705600,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"531bb2653d363917dea25a3fae337fda\",\"Name\":\"上海元祯投资中心（有限合伙）\",\"Org\":0}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"84c17a005a759a5e0d875c1ebb6c9846\",\"Name\":\"乐视网信息技术（北京）股份有限公司\",\"Org\":0}]},{\"CompanyInfo\":[{\"KeyNo\":\"72010df77a394297ffd63029a915ea07\",\"Name\":\"乐乐互动体育文化发展（北京）有限公司\",\"Org\":0}],\"GlNameAndKeyNo\":[{\"KeyNo\":\"p9507cfb847c93e2164dbe679eed072f\",\"Name\":\"高飞\",\"Org\":2}],\"Id\":\"6d4684b78f301ef8f9d140bfad3d7bcc\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"72010df77a394297ffd63029a915ea07\",\"Name\":\"乐乐互动体育文化发展（北京）有限公司\",\"Org\":0}],\"PublishDate\":1593705600,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"531bb2653d363917dea25a3fae337fda\",\"Name\":\"上海元祯投资中心（有限合伙）\",\"Org\":0}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"72010df77a394297ffd63029a915ea07\",\"Name\":\"乐乐互动体育文化发展（北京）有限公司\",\"Org\":0}]},{\"CompanyInfo\":[{\"KeyNo\":\"5bc9e6a3da338107ce77c360a024710e\",\"Name\":\"北京鹏翼资产管理中心（有限合伙）\",\"Org\":0}],\"GlNameAndKeyNo\":[{\"KeyNo\":\"pr0767af6e3dbede2d1ff8134b168bb6\",\"Name\":\"刘文选\",\"Org\":2}],\"Id\":\"f4a1d261ec99d07194f55f5c1e69771d\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"5bc9e6a3da338107ce77c360a024710e\",\"Name\":\"北京鹏翼资产管理中心（有限合伙）\",\"Org\":0}],\"PublishDate\":1593705600,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"531bb2653d363917dea25a3fae337fda\",\"Name\":\"上海元祯投资中心（有限合伙）\",\"Org\":0}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"5bc9e6a3da338107ce77c360a024710e\",\"Name\":\"北京鹏翼资产管理中心（有限合伙）\",\"Org\":0}]}],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[{\"ExecuteObject\":\"*********\",\"FailureAct\":\"*********\",\"Id\":\"145ecbc9eea4026cb25616e147d10d9f\",\"IsValid\":1,\"JudgeDate\":1594828800,\"NameAndKeyNo\":[{\"KeyNo\":\"5bc9e6a3da338107ce77c360a024710e\",\"Name\":\"北京鹏翼资产管理中心（有限合伙）\",\"Org\":0}]},{\"ExecuteObject\":\"*********\",\"FailureAct\":\"*********\",\"Id\":\"289dc576300470c7558c4297478d09b7\",\"IsValid\":1,\"JudgeDate\":1594828800,\"NameAndKeyNo\":[{\"KeyNo\":\"84c17a005a759a5e0d875c1ebb6c9846\",\"Name\":\"乐视网信息技术（北京）股份有限公司\",\"Org\":0}]},{\"ExecuteObject\":\"*********\",\"FailureAct\":\"*********\",\"Id\":\"fbe7caf42f32901eeba51b69bab50576\",\"IsValid\":1,\"JudgeDate\":1594828800,\"NameAndKeyNo\":[{\"KeyNo\":\"72010df77a394297ffd63029a915ea07\",\"Name\":\"乐乐互动体育文化发展（北京）有限公司\",\"Org\":0}]}],\"ZxList\":[{\"Biaodi\":\"*********\",\"Id\":\"145ecbc9eea4026cb25616e147d10d9f1\",\"IsValid\":0,\"LianDate\":1589817600,\"NameAndKeyNo\":[{\"KeyNo\":\"5bc9e6a3da338107ce77c360a024710e\",\"Name\":\"北京鹏翼资产管理中心（有限合伙）\",\"Org\":0}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"531bb2653d363917dea25a3fae337fda\",\"Name\":\"上海元祯投资中心（有限合伙）\",\"Org\":0}]},{\"Biaodi\":\"*********\",\"Id\":\"289dc576300470c7558c4297478d09b71\",\"IsValid\":0,\"LianDate\":1589817600,\"NameAndKeyNo\":[{\"KeyNo\":\"84c17a005a759a5e0d875c1ebb6c9846\",\"Name\":\"乐视网信息技术（北京）股份有限公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"531bb2653d363917dea25a3fae337fda\",\"Name\":\"上海元祯投资中心（有限合伙）\",\"Org\":0}]},{\"Biaodi\":\"*********\",\"Id\":\"fbe7caf42f32901eeba51b69bab505761\",\"IsValid\":0,\"LianDate\":1589817600,\"NameAndKeyNo\":[{\"KeyNo\":\"72010df77a394297ffd63029a915ea07\",\"Name\":\"乐乐互动体育文化发展（北京）有限公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"531bb2653d363917dea25a3fae337fda\",\"Name\":\"上海元祯投资中心（有限合伙）\",\"Org\":0}]}]}],\"KtggCnt\":0,\"LastestDate\":1594828800,\"LastestDateType\":\"首次执行|案件终本日期\",\"LatestTrialRound\":\"首次执行\",\"LianCnt\":0,\"PcczCnt\":0,\"ProcuratorateList\":\"\",\"Province\":\"BJ\",\"SdggCnt\":0,\"Source\":\"OT\",\"SxCnt\":3,\"Tags\":\"1,2,3,4,6\",\"Type\":1,\"XgCnt\":3,\"XjpgCnt\":0,\"XzcfCnt\":0,\"ZbCnt\":3,\"ZxCnt\":3}]";

//        String json = "[{\"AmtInfo\":{\"be39f3636129d5531fe1278bf0a5b3a9\":{\"Amt\":\"500000.00\",\"IsValid\":\"1\",\"Type\":\"案件金额\"},\"p9d667eb459f62537a1992fdbb68134f\":{\"Amt\":\"153948\",\"IsValid\":\"0\",\"Type\":\"未履行金额\"}},\"AnNoList\":\"（2016）赣0425民初507号,（2016）赣0425民初854号,（2017）永证执字第1号,（2017）赣0425执106号,（2017）赣0425执112号,（2017）赣0425执175号,（2020）赣0425执恢85号\",\"AnnoCnt\":7,\"CaseCnt\":12,\"CaseName\":\"江西永修农村商业银行股份有限公司与蔡雨欣借款合同纠纷案件执行的案件\",\"CaseReason\":\"借款合同纠纷案件执行\",\"CaseRole\":\"[{\\\"D\\\":\\\"首次执行申请执行人,恢复执行申请执行人\\\",\\\"N\\\":\\\"be39f3636129d5531fe1278bf0a5b3a9\\\",\\\"O\\\":0,\\\"P\\\":\\\"江西永修农村商业银行股份有限公司\\\",\\\"R\\\":\\\"申请执行人\\\"},{\\\"D\\\":\\\"首次执行被执行人,恢复执行被执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2,\\\"P\\\":\\\"蔡雨欣\\\",\\\"R\\\":\\\"被执行人\\\"}]\",\"CaseType\":\"执行案件\",\"CfdfCnt\":0,\"CfgsCnt\":0,\"CfxyCnt\":0,\"CompanyKeywords\":\"be39f3636129d5531fe1278bf0a5b3a9,p9d667eb459f62537a1992fdbb68134f,万修禄,中国工商银行股份有限公司永修县支行,何冬梅,叶正春,柯喜保,江西永修农村商业银行股份有限公司,蔡雨欣\",\"CourtList\":\"永修县人民法院,江西省九江市永修县人民法院\",\"EarliestDate\":1488124800,\"EarliestDateType\":\"首次执行|被执行人立案日期\",\"FyggCnt\":0,\"GqdjCnt\":0,\"GroupId\":\"afb6bd01fb6334b9d2d2aebdaf907bdf\",\"HbcfCnt\":0,\"Id\":\"667833a567780fd5f65ae5d0dd8a3795\",\"InfoList\":[{\"AnNo\":\"（2016）赣0425民初507号\",\"CaseList\":[],\"CaseReason\":\"\",\"CaseType\":\"民事案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"永修县人民法院\",\"Defendant\":[],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1451577600,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"民事一审\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]},{\"AnNo\":\"（2016）赣0425民初854号\",\"CaseList\":[],\"CaseReason\":\"\",\"CaseType\":\"民事案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"永修县人民法院\",\"Defendant\":[],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1451577600,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"民事一审\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]},{\"AnNo\":\"（2017）永证执字第1号\",\"CaseList\":[],\"CaseReason\":\"借款合同纠纷案件执行\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"\",\"Defendant\":[],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1483200000,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"首次执行\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]},{\"AnNo\":\"（2017）赣0425执106号\",\"CaseList\":[{\"Amt\":\"\",\"CaseType\":\"执行裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"5b0e158baac13aeb4ec30333ad23ef3c0\",\"IsValid\":1,\"JudgeDate\":1504051200,\"Result\":\"查封被执行人蔡雨欣名下的坐落于永修县新城珠岭路德基亲水山庄25栋603室产权证为永房权证私字第××号房屋，查封期限为三年。\",\"ResultType\":\"裁定结果\"},{\"Amt\":\"\",\"CaseType\":\"执行裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"72d774f790c355bcaacede951a3b461d0\",\"IsValid\":1,\"JudgeDate\":1506384000,\"Result\":\"终结本次执行程序。\",\"ResultType\":\"裁定结果\"}],\"CaseReason\":\"借款合同纠纷案件执行\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"江西省九江市永修县人民法院\",\"Defendant\":[{\"KeyNo\":\"\",\"Name\":\"蔡雨欣\",\"Org\":-2,\"Role\":\"被执行人\"}],\"ExecuteNo\":\"（2016）赣0425民初854号\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1514822400,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"be39f3636129d5531fe1278bf0a5b3a9\",\"Name\":\"江西永修农村商业银行股份有限公司\",\"Org\":0,\"Role\":\"申请执行人\"}],\"SdggList\":[],\"SxList\":[{\"ActionType\":\"违反财产报告制度\",\"ExecuteStatus\":\"全部未履行\",\"Id\":\"06207309bfba82ca99e2202d8c621a452\",\"IsValid\":0,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"蔡雨欣\",\"Org\":-2}],\"PublishDate\":1506441600}],\"TrialRound\":\"首次执行\",\"XgList\":[{\"CompanyInfo\":[{\"KeyNo\":\"\",\"Name\":\"蔡雨欣\",\"Org\":-2}],\"GlNameAndKeyNo\":[],\"Id\":\"c44986c8d72b999997cc2babd55e65e6\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"蔡雨欣\",\"Org\":-2}],\"PublishDate\":1514822400,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"be39f3636129d5531fe1278bf0a5b3a9\",\"Name\":\"江西永修农村商业银行股份有限公司\",\"Org\":0}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"蔡雨欣\",\"Org\":-2}]}],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[{\"ExecuteObject\":\"397939\",\"FailureAct\":\"397939\",\"Id\":\"06207309bfba82ca99e2202d8c621a45\",\"IsValid\":1,\"JudgeDate\":1506441600,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"蔡雨欣\",\"Org\":-2}]}],\"ZxList\":[{\"Biaodi\":\"397939\",\"Id\":\"06207309bfba82ca99e2202d8c621a451\",\"IsValid\":0,\"LianDate\":1488124800,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"蔡雨欣\",\"Org\":-2}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"be39f3636129d5531fe1278bf0a5b3a9\",\"Name\":\"江西永修农村商业银行股份有限公司\",\"Org\":0}]}]},{\"AnNo\":\"（2017）赣0425执112号\",\"CaseList\":[{\"Amt\":\"\",\"CaseType\":\"执行裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"a42f7bda4cc74467e2303dd2bbd7d2070\",\"IsValid\":1,\"JudgeDate\":1504051200,\"Result\":\"查封被执行人叶正春名下的坐落于永修县新城建昌东路华丰商住楼2A303室产权证为永房权证私字第××号房屋，查封期限为三年。\",\"ResultType\":\"裁定结果\"},{\"Amt\":\"\",\"CaseType\":\"执行裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"ff20df747f7b82fcda46e2897bd82ecd0\",\"IsValid\":1,\"JudgeDate\":1506384000,\"Result\":\"终结本次执行程序。\",\"ResultType\":\"裁定结果\"}],\"CaseReason\":\"借款合同纠纷案件执行\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"江西省九江市永修县人民法院\",\"Defendant\":[{\"KeyNo\":\"\",\"Name\":\"叶正春\",\"Org\":-2,\"Role\":\"被执行人\"}],\"ExecuteNo\":\"（2016）赣0425民初507号\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1514822400,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"be39f3636129d5531fe1278bf0a5b3a9\",\"Name\":\"江西永修农村商业银行股份有限公司\",\"Org\":0,\"Role\":\"申请执行人\"}],\"SdggList\":[],\"SxList\":[{\"ActionType\":\"违反财产报告制度\",\"ExecuteStatus\":\"全部未履行\",\"Id\":\"e93118e1a3e5ea40464893de845766f12\",\"IsValid\":0,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"叶正春\",\"Org\":-2}],\"PublishDate\":1506441600}],\"TrialRound\":\"首次执行\",\"XgList\":[{\"CompanyInfo\":[{\"KeyNo\":\"\",\"Name\":\"叶正春\",\"Org\":-2}],\"GlNameAndKeyNo\":[],\"Id\":\"60782077df37a09318964227ca74efd9\",\"IsValid\":0,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"叶正春\",\"Org\":-2}],\"PublishDate\":1514822400,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"be39f3636129d5531fe1278bf0a5b3a9\",\"Name\":\"江西永修农村商业银行股份有限公司\",\"Org\":0}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"叶正春\",\"Org\":-2}]}],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[{\"ExecuteObject\":\"252746\",\"FailureAct\":\"252746\",\"Id\":\"e93118e1a3e5ea40464893de845766f1\",\"IsValid\":0,\"JudgeDate\":1506441600,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"叶正春\",\"Org\":-2}]}],\"ZxList\":[{\"Biaodi\":\"252746\",\"Id\":\"e93118e1a3e5ea40464893de845766f11\",\"IsValid\":0,\"LianDate\":1488124800,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"叶正春\",\"Org\":-2}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"be39f3636129d5531fe1278bf0a5b3a9\",\"Name\":\"江西永修农村商业银行股份有限公司\",\"Org\":0}]}]},{\"AnNo\":\"（2017）赣0425执175号\",\"CaseList\":[{\"Amt\":\"\",\"CaseType\":\"执行裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"199df3a3959628af68eb97822cba14ca0\",\"IsValid\":1,\"JudgeDate\":1505145600,\"Result\":\"拍卖被执行人柯喜保、何冬梅所有的坐落在永修县柘林镇杨垅村永房权证私字第××号三层的房屋一栋。\",\"ResultType\":\"裁定结果\"},{\"Amt\":\"\",\"CaseType\":\"执行裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"1eb289d7f30aa063a4ae347639ec5a920\",\"IsValid\":1,\"JudgeDate\":1505174400,\"Result\":\"拍卖被执行人蔡雨欣所有的坐落在永修县新城珠岭路德基亲水山庄25栋603室产权证为永房权证私字第××号的房屋一套。\",\"ResultType\":\"裁定结果\"},{\"Amt\":\"\",\"CaseType\":\"执行裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"32aaef86b5e2a6eb331d7692b42d586d0\",\"IsValid\":1,\"JudgeDate\":1540224000,\"Result\":\"终结本次执行程序。\",\"ResultType\":\"裁定结果\"},{\"Amt\":\"\",\"CaseType\":\"执行裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"69808fc1f63d60ca402262ff1a02a4ae0\",\"IsValid\":1,\"JudgeDate\":1505174400,\"Result\":\"拍卖被执行人叶正春所有的坐落在永修县新城建昌东路华丰商住楼2A303室产权证为永房权证私字第××号的房屋一套。\",\"ResultType\":\"裁定结果\"},{\"Amt\":\"\",\"CaseType\":\"执行裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"99ca86d8dfe5c02f70135e71a6ad49210\",\"IsValid\":1,\"JudgeDate\":1503590400,\"Result\":\"查封被执行人柯喜保、何冬梅名下的坐落于永修县柘林镇杨垅村永房权证私字第××号三层房屋一栋，查封期限为三年。\",\"ResultType\":\"裁定结果\"},{\"Amt\":\"160000.00\",\"CaseType\":\"执行裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"de57c53b47ae602d31447b8a9ffdac8b0\",\"IsValid\":1,\"JudgeDate\":1540137600,\"Result\":\"冻结、扣划被执行人柯喜保、何冬梅、万修禄的存款160000元或查封、扣押其相应价值的财产。\",\"ResultType\":\"裁定结果\"}],\"CaseReason\":\"不当得利纠纷案件执行\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"江西省九江市永修县人民法院\",\"Defendant\":[{\"KeyNo\":\"\",\"Name\":\"万修禄\",\"Org\":-2,\"Role\":\"被执行人\"},{\"KeyNo\":\"\",\"Name\":\"何冬梅\",\"Org\":-2,\"Role\":\"被执行人\"},{\"KeyNo\":\"p9d667eb459f62537a1992fdbb68134f\",\"Name\":\"柯喜保\",\"Org\":2,\"Role\":\"被执行人\"}],\"ExecuteNo\":\"执行证书\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1540396800,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"\",\"Name\":\"中国工商银行股份有限公司永修县支行\",\"Org\":-1,\"Role\":\"申请执行人\"}],\"SdggList\":[],\"SxList\":[{\"ActionType\":\"有履行能力而拒不履行生效法律文书确定义务\",\"ExecuteStatus\":\"全部未履行\",\"Id\":\"2b43e3229f1fe65bfffbf232f4f36e712\",\"IsValid\":0,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"何冬梅\",\"Org\":-2}],\"PublishDate\":1540310400},{\"ActionType\":\"有履行能力而拒不履行生效法律文书确定义务\",\"ExecuteStatus\":\"全部未履行\",\"Id\":\"db995a100b77f07d1c661cf80bfe4f612\",\"IsValid\":0,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"万修禄\",\"Org\":-2}],\"PublishDate\":1540310400}],\"TrialRound\":\"首次执行\",\"XgList\":[{\"CompanyInfo\":[{\"KeyNo\":\"\",\"Name\":\"万修禄\",\"Org\":-2}],\"GlNameAndKeyNo\":[],\"Id\":\"7b56ab31dac640bc50af46d7f579dffd\",\"IsValid\":0,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"万修禄\",\"Org\":-2}],\"PublishDate\":1540224000,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"04398f291fa832bf1bd6ff55b0eba5ba\",\"Name\":\"中国工商银行股份有限公司永修支行\",\"Org\":0}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"万修禄\",\"Org\":-2}]},{\"CompanyInfo\":[{\"KeyNo\":\"\",\"Name\":\"何冬梅\",\"Org\":-2}],\"GlNameAndKeyNo\":[],\"Id\":\"8f35e7f7a5b2c1b307c2b4cff43fc131\",\"IsValid\":0,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"何冬梅\",\"Org\":-2}],\"PublishDate\":1540224000,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"04398f291fa832bf1bd6ff55b0eba5ba\",\"Name\":\"中国工商银行股份有限公司永修支行\",\"Org\":0}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"何冬梅\",\"Org\":-2}]}],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[{\"ExecuteObject\":\"153948\",\"FailureAct\":\"153948\",\"Id\":\"2b43e3229f1fe65bfffbf232f4f36e71\",\"IsValid\":0,\"JudgeDate\":1540396800,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"何冬梅\",\"Org\":-2}]},{\"ExecuteObject\":\"153948\",\"FailureAct\":\"153948\",\"Id\":\"385d03e67e8777136776d48949f10ff4\",\"IsValid\":0,\"JudgeDate\":1540396800,\"NameAndKeyNo\":[{\"KeyNo\":\"p9d667eb459f62537a1992fdbb68134f\",\"Name\":\"柯喜保\",\"Org\":2}]}],\"ZxList\":[{\"Biaodi\":\"153949\",\"Id\":\"385d03e67e8777136776d48949f10ff41\",\"IsValid\":0,\"LianDate\":1494259200,\"NameAndKeyNo\":[{\"KeyNo\":\"p9d667eb459f62537a1992fdbb68134f\",\"Name\":\"柯喜保\",\"Org\":2}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"04398f291fa832bf1bd6ff55b0eba5ba\",\"Name\":\"中国工商银行股份有限公司永修支行\",\"Org\":0}]},{\"Biaodi\":\"153949\",\"Id\":\"db995a100b77f07d1c661cf80bfe4f611\",\"IsValid\":0,\"LianDate\":1494259200,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"万修禄\",\"Org\":-2}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"04398f291fa832bf1bd6ff55b0eba5ba\",\"Name\":\"中国工商银行股份有限公司永修支行\",\"Org\":0}]}]},{\"AnNo\":\"（2020）赣0425执恢85号\",\"CaseList\":[{\"Amt\":\"500000.00\",\"CaseType\":\"执行裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"48e297fb51e46b512e7ff043558187f00\",\"IsValid\":1,\"JudgeDate\":1594828800,\"Result\":\"查封、冻结、扣划被执行人蔡雨欣的存款500000元或查封、扣押、冻结其相应价值的财产。\",\"ResultType\":\"裁定结果\"},{\"Amt\":\"\",\"CaseType\":\"执行裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"b5d2161e8f9dd3d4d61b06c2a9f387d70\",\"IsValid\":1,\"JudgeDate\":1605456000,\"Result\":\"终结本次执行程序。\",\"ResultType\":\"裁定结果\"}],\"CaseReason\":\"借款合同纠纷案件执行\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"江西省九江市永修县人民法院\",\"Defendant\":[{\"KeyNo\":\"\",\"Name\":\"蔡雨欣\",\"Org\":-2,\"Role\":\"被执行人\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1605456000,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"be39f3636129d5531fe1278bf0a5b3a9\",\"Name\":\"江西永修农村商业银行股份有限公司\",\"Org\":0,\"Role\":\"申请执行人\"}],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"恢复执行\",\"XgList\":[{\"CompanyInfo\":[{\"KeyNo\":\"\",\"Name\":\"蔡雨欣\",\"Org\":-2}],\"GlNameAndKeyNo\":[],\"Id\":\"e44e240765445cdbb3da0ec629301401\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"蔡雨欣\",\"Org\":-2}],\"PublishDate\":1595520000,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"be39f3636129d5531fe1278bf0a5b3a9\",\"Name\":\"江西永修农村商业银行股份有限公司\",\"Org\":0}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"蔡雨欣\",\"Org\":-2}]}],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[{\"Biaodi\":\"397939\",\"Id\":\"88cfd8b135ed8a7436d4765d51addea71\",\"IsValid\":0,\"LianDate\":1594656000,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"蔡雨欣\",\"Org\":-2}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"be39f3636129d5531fe1278bf0a5b3a9\",\"Name\":\"江西永修农村商业银行股份有限公司\",\"Org\":0}]}]}],\"KtggCnt\":0,\"LastestDate\":1605456000,\"LastestDateType\":\"恢复执行|裁定日期\",\"LatestTrialRound\":\"恢复执行\",\"LianCnt\":0,\"PcczCnt\":0,\"ProcuratorateList\":\"\",\"Province\":\"JX\",\"SdggCnt\":0,\"Source\":\"OT\",\"SxCnt\":4,\"Tags\":\"1,2,3,4,6\",\"Type\":1,\"XgCnt\":5,\"XjpgCnt\":0,\"XzcfCnt\":0,\"ZbCnt\":4,\"ZxCnt\":5}]";

//        String json = "[{\"AmtInfo\":{\"5f9ba704cdcfed58a30a1d4596a05d54\":{\"Amt\":\"5175839\",\"IsValid\":\"1\",\"Type\":\"执行标的\"},\"p18c771199e469b3292d9f0a9281af96\":{\"Amt\":\"5175839\",\"IsValid\":\"1\",\"Type\":\"执行标的\"},\"p707a3c1284f980d4b346f0d4c95e562\":{\"Amt\":\"5175839\",\"IsValid\":\"1\",\"Type\":\"执行标的\"},\"pcb87316846e5d9e05279d7bce113f9f\":{\"Amt\":\"5175839\",\"IsValid\":\"1\",\"Type\":\"执行标的\"}},\"AnNoList\":\"（2020）渝0106执4055号,（2020）渝0106民初578号,（2021）渝0106执恢99号\",\"AnnoCnt\":3,\"CaseCnt\":1,\"CaseName\":\"上海浦东发展银行股份有限公司重庆分行与张盛乔,石郁婷,谭诗蕾等金融借款合同纠纷的案件\",\"CaseReason\":\"金融借款合同纠纷\",\"CaseRole\":\"[{\\\"D\\\":\\\"一审原告\\\",\\\"N\\\":\\\"c73f8a8d657a84ffa9085aeffcc1a244\\\",\\\"O\\\":0,\\\"P\\\":\\\"上海浦东发展银行股份有限公司重庆分行\\\",\\\"R\\\":\\\"原告\\\"},{\\\"D\\\":\\\"一审被告,首次执行被执行人,恢复执行被执行人\\\",\\\"N\\\":\\\"p18c771199e469b3292d9f0a9281af96\\\",\\\"O\\\":2,\\\"P\\\":\\\"张盛乔\\\",\\\"R\\\":\\\"被告\\\"},{\\\"D\\\":\\\"一审被告,首次执行被执行人,恢复执行被执行人\\\",\\\"N\\\":\\\"p707a3c1284f980d4b346f0d4c95e562\\\",\\\"O\\\":2,\\\"P\\\":\\\"石郁婷\\\",\\\"R\\\":\\\"被告\\\"},{\\\"D\\\":\\\"一审被告,恢复执行被执行人\\\",\\\"N\\\":\\\"pcb87316846e5d9e05279d7bce113f9f\\\",\\\"O\\\":2,\\\"P\\\":\\\"谭诗蕾\\\",\\\"R\\\":\\\"被告\\\"},{\\\"D\\\":\\\"一审被告,首次执行被执行人,恢复执行被执行人\\\",\\\"N\\\":\\\"5f9ba704cdcfed58a30a1d4596a05d54\\\",\\\"O\\\":0,\\\"P\\\":\\\"重庆冠虹环保科技有限公司\\\",\\\"R\\\":\\\"被告\\\"},{\\\"D\\\":\\\"一审被告\\\",\\\"N\\\":\\\"44c57d1b0e038a2038a6fb111c916cb0\\\",\\\"O\\\":0,\\\"P\\\":\\\"重庆新亿融资担保有限公司\\\",\\\"R\\\":\\\"被告\\\"}]\",\"CaseType\":\"执行案件,民事案件\",\"CfdfCnt\":0,\"CfgsCnt\":0,\"CfxyCnt\":0,\"CompanyKeywords\":\"44c57d1b0e038a2038a6fb111c916cb0,5f9ba704cdcfed58a30a1d4596a05d54,c73f8a8d657a84ffa9085aeffcc1a244,p18c771199e469b3292d9f0a9281af96,p707a3c1284f980d4b346f0d4c95e562,pcb87316846e5d9e05279d7bce113f9f,上海浦东发展银行股份有限公司重庆分行,张盛乔,石郁婷,谭诗蕾,重庆冠虹商业管理有限公司,重庆冠虹环保科技有限公司,重庆冠虹环保能源有限公司,重庆冠虹环保能源股份有限公司,重庆新亿融资担保有限公司\",\"CourtList\":\"重庆市沙坪坝区人民法院\",\"EarliestDate\":1587346200,\"EarliestDateType\":\"民事一审|开庭时间\",\"FyggCnt\":0,\"GqdjCnt\":0,\"GroupId\":\"6b14667985ce2af32bc9eb5ff3162626\",\"HbcfCnt\":0,\"Id\":\"00030922038e6ba3abb997a83cf9d5a2\",\"InfoList\":[{\"AnNo\":\"（2020）渝0106执4055号\",\"CaseList\":[],\"CaseReason\":\"\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"重庆市沙坪坝区人民法院\",\"Defendant\":[{\"KeyNo\":\"p18c771199e469b3292d9f0a9281af96\",\"Name\":\"张盛乔\",\"Org\":2,\"Role\":\"被执行人\"},{\"KeyNo\":\"p707a3c1284f980d4b346f0d4c95e562\",\"Name\":\"石郁婷\",\"Org\":2,\"Role\":\"被执行人\"},{\"KeyNo\":\"5f9ba704cdcfed58a30a1d4596a05d54\",\"Name\":\"重庆冠虹环保科技有限公司\",\"Org\":0,\"Role\":\"被执行人\"}],\"ExecuteNo\":\"（2020）渝0106民初578号\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1605196800,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[],\"SdggList\":[],\"SxList\":[{\"ActionType\":\"有履行能力而拒不履行生效法律文书确定义务\",\"ExecuteStatus\":\"全部未履行\",\"Id\":\"1d3ce089268ab69ac6f45e82042197172\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"p707a3c1284f980d4b346f0d4c95e562\",\"Name\":\"石郁婷\",\"Org\":2}],\"PublishDate\":1605196800},{\"ActionType\":\"有履行能力而拒不履行生效法律文书确定义务\",\"ExecuteStatus\":\"全部未履行\",\"Id\":\"25f43a321996829c7202aed6b4ab39fa2\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"p18c771199e469b3292d9f0a9281af96\",\"Name\":\"张盛乔\",\"Org\":2}],\"PublishDate\":1605196800},{\"ActionType\":\"有履行能力而拒不履行生效法律文书确定义务\",\"ExecuteStatus\":\"全部未履行\",\"Id\":\"7bdd8665461d9e9fb9d21840b35a866f2\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"5f9ba704cdcfed58a30a1d4596a05d54\",\"Name\":\"重庆冠虹环保科技有限公司\",\"Org\":0}],\"PublishDate\":1605196800}],\"TrialRound\":\"首次执行\",\"XgList\":[{\"CompanyInfo\":[{\"KeyNo\":\"p707a3c1284f980d4b346f0d4c95e562\",\"Name\":\"石郁婷\",\"Org\":2}],\"GlNameAndKeyNo\":[],\"Id\":\"d831aa0195c6f875999c4d99677087be\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"p707a3c1284f980d4b346f0d4c95e562\",\"Name\":\"石郁婷\",\"Org\":2}],\"PublishDate\":1605196800,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"c73f8a8d657a84ffa9085aeffcc1a244\",\"Name\":\"上海浦东发展银行股份有限公司重庆分行\",\"Org\":0}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"p707a3c1284f980d4b346f0d4c95e562\",\"Name\":\"石郁婷\",\"Org\":2}]},{\"CompanyInfo\":[{\"KeyNo\":\"5f9ba704cdcfed58a30a1d4596a05d54\",\"Name\":\"重庆冠虹环保科技有限公司\",\"Org\":0}],\"GlNameAndKeyNo\":[{\"KeyNo\":\"p18c771199e469b3292d9f0a9281af96\",\"Name\":\"张盛乔\",\"Org\":2}],\"Id\":\"ea9ea6464e62c9823eab122f1ca877d2\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"5f9ba704cdcfed58a30a1d4596a05d54\",\"Name\":\"重庆冠虹环保科技有限公司\",\"Org\":0}],\"PublishDate\":1605196800,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"c73f8a8d657a84ffa9085aeffcc1a244\",\"Name\":\"上海浦东发展银行股份有限公司重庆分行\",\"Org\":0}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"5f9ba704cdcfed58a30a1d4596a05d54\",\"Name\":\"重庆冠虹环保科技有限公司\",\"Org\":0}]},{\"CompanyInfo\":[{\"KeyNo\":\"p18c771199e469b3292d9f0a9281af96\",\"Name\":\"张盛乔\",\"Org\":2}],\"GlNameAndKeyNo\":[],\"Id\":\"f6751eb1f0f75590ac21592c4783e0c7\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"p18c771199e469b3292d9f0a9281af96\",\"Name\":\"张盛乔\",\"Org\":2}],\"PublishDate\":1605196800,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"c73f8a8d657a84ffa9085aeffcc1a244\",\"Name\":\"上海浦东发展银行股份有限公司重庆分行\",\"Org\":0}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"p18c771199e469b3292d9f0a9281af96\",\"Name\":\"张盛乔\",\"Org\":2}]}],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[{\"Biaodi\":\"5175840\",\"Id\":\"140d5d3729147d923ac8140888b5c6ac1\",\"IsValid\":0,\"LianDate\":1595865600,\"NameAndKeyNo\":[{\"KeyNo\":\"pcb87316846e5d9e05279d7bce113f9f\",\"Name\":\"谭诗蕾\",\"Org\":2}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"c73f8a8d657a84ffa9085aeffcc1a244\",\"Name\":\"上海浦东发展银行股份有限公司重庆分行\",\"Org\":0}]},{\"Biaodi\":\"5175840\",\"Id\":\"1d3ce089268ab69ac6f45e82042197171\",\"IsValid\":0,\"LianDate\":1595865600,\"NameAndKeyNo\":[{\"KeyNo\":\"p707a3c1284f980d4b346f0d4c95e562\",\"Name\":\"石郁婷\",\"Org\":2}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"c73f8a8d657a84ffa9085aeffcc1a244\",\"Name\":\"上海浦东发展银行股份有限公司重庆分行\",\"Org\":0}]},{\"Biaodi\":\"5175840\",\"Id\":\"25f43a321996829c7202aed6b4ab39fa1\",\"IsValid\":0,\"LianDate\":1595865600,\"NameAndKeyNo\":[{\"KeyNo\":\"p18c771199e469b3292d9f0a9281af96\",\"Name\":\"张盛乔\",\"Org\":2}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"c73f8a8d657a84ffa9085aeffcc1a244\",\"Name\":\"上海浦东发展银行股份有限公司重庆分行\",\"Org\":0}]},{\"Biaodi\":\"5175840\",\"Id\":\"7bdd8665461d9e9fb9d21840b35a866f1\",\"IsValid\":0,\"LianDate\":1595865600,\"NameAndKeyNo\":[{\"KeyNo\":\"5f9ba704cdcfed58a30a1d4596a05d54\",\"Name\":\"重庆冠虹环保科技有限公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"c73f8a8d657a84ffa9085aeffcc1a244\",\"Name\":\"上海浦东发展银行股份有限公司重庆分行\",\"Org\":0}]}]},{\"AnNo\":\"（2020）渝0106民初578号\",\"CaseList\":[{\"Amt\":\"\",\"CaseType\":\"民事调解书\",\"DocType\":\"调解日期\",\"Id\":\"1d37449882a5419da90de5b6984512db0\",\"IsValid\":1,\"JudgeDate\":1588003200,\"Result\":\"\",\"ResultType\":\"调解结果\"}],\"CaseReason\":\"金融借款合同纠纷\",\"CaseType\":\"民事案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"重庆市沙坪坝区人民法院\",\"Defendant\":[{\"KeyNo\":\"p18c771199e469b3292d9f0a9281af96\",\"Name\":\"张盛乔\",\"Org\":2,\"Role\":\"被告\"},{\"KeyNo\":\"p707a3c1284f980d4b346f0d4c95e562\",\"Name\":\"石郁婷\",\"Org\":2,\"Role\":\"被告\"},{\"KeyNo\":\"pcb87316846e5d9e05279d7bce113f9f\",\"Name\":\"谭诗蕾\",\"Org\":2,\"Role\":\"被告\"},{\"KeyNo\":\"5f9ba704cdcfed58a30a1d4596a05d54\",\"Name\":\"重庆冠虹环保科技有限公司\",\"Org\":0,\"Role\":\"被告\"},{\"KeyNo\":\"44c57d1b0e038a2038a6fb111c916cb0\",\"Name\":\"重庆新亿融资担保有限公司\",\"Org\":0,\"Role\":\"被告\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[{\"ExecuteUnite\":\"第二十审判庭\",\"Id\":\"210cfbffc24b884a95e59338408fc9075\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"c73f8a8d657a84ffa9085aeffcc1a244\",\"Name\":\"上海浦东发展银行股份有限公司重庆分行\",\"Org\":0},{\"KeyNo\":\"5f9ba704cdcfed58a30a1d4596a05d54\",\"Name\":\"重庆冠虹环保科技有限公司\",\"Org\":0},{\"KeyNo\":\"p18c771199e469b3292d9f0a9281af96\",\"Name\":\"张盛乔\",\"Org\":2},{\"KeyNo\":\"pcb87316846e5d9e05279d7bce113f9f\",\"Name\":\"谭诗蕾\",\"Org\":2},{\"KeyNo\":\"44c57d1b0e038a2038a6fb111c916cb0\",\"Name\":\"重庆新亿融资担保有限公司\",\"Org\":0},{\"KeyNo\":\"p707a3c1284f980d4b346f0d4c95e562\",\"Name\":\"石郁婷\",\"Org\":2}],\"OpenDate\":1587346200}],\"LatestTimestamp\":1588003200,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"c73f8a8d657a84ffa9085aeffcc1a244\",\"Name\":\"上海浦东发展银行股份有限公司重庆分行\",\"Org\":0,\"Role\":\"原告\"}],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"民事一审\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]},{\"AnNo\":\"（2021）渝0106执恢99号\",\"CaseList\":[],\"CaseReason\":\"\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"重庆市沙坪坝区人民法院\",\"Defendant\":[{\"KeyNo\":\"p18c771199e469b3292d9f0a9281af96\",\"Name\":\"张盛乔\",\"Org\":2,\"Role\":\"被执行人\"},{\"KeyNo\":\"p707a3c1284f980d4b346f0d4c95e562\",\"Name\":\"石郁婷\",\"Org\":2,\"Role\":\"被执行人\"},{\"KeyNo\":\"pcb87316846e5d9e05279d7bce113f9f\",\"Name\":\"谭诗蕾\",\"Org\":2,\"Role\":\"被执行人\"},{\"KeyNo\":\"5f9ba704cdcfed58a30a1d4596a05d54\",\"Name\":\"重庆冠虹环保科技有限公司\",\"Org\":0,\"Role\":\"被执行人\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1610640000,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"恢复执行\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[{\"Biaodi\":\"5175839\",\"Id\":\"5e5acac94302e40d6745c80676f3c0041\",\"IsValid\":1,\"LianDate\":1610640000,\"NameAndKeyNo\":[{\"KeyNo\":\"p707a3c1284f980d4b346f0d4c95e562\",\"Name\":\"石郁婷\",\"Org\":2}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"5175839\",\"Id\":\"77673dc59aa508e0155caa90ef2ad76d1\",\"IsValid\":1,\"LianDate\":1610640000,\"NameAndKeyNo\":[{\"KeyNo\":\"pcb87316846e5d9e05279d7bce113f9f\",\"Name\":\"谭诗蕾\",\"Org\":2}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"5175839\",\"Id\":\"83caa511c3524388e1e42fb92119c3c51\",\"IsValid\":1,\"LianDate\":1610640000,\"NameAndKeyNo\":[{\"KeyNo\":\"p18c771199e469b3292d9f0a9281af96\",\"Name\":\"张盛乔\",\"Org\":2}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"5175839\",\"Id\":\"e1d644b8ab2252cf4bd24925372028b11\",\"IsValid\":1,\"LianDate\":1610640000,\"NameAndKeyNo\":[{\"KeyNo\":\"5f9ba704cdcfed58a30a1d4596a05d54\",\"Name\":\"重庆冠虹环保科技有限公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]}]}],\"KtggCnt\":1,\"LastestDate\":1610640000,\"LastestDateType\":\"恢复执行|被执行人立案日期\",\"LatestTrialRound\":\"恢复执行\",\"LianCnt\":0,\"PcczCnt\":0,\"ProcuratorateList\":\"\",\"Province\":\"CQ\",\"SdggCnt\":0,\"Source\":\"OT\",\"SxCnt\":3,\"Tags\":\"1,2,3,4,11\",\"Type\":1,\"XgCnt\":3,\"XjpgCnt\":0,\"XzcfCnt\":0,\"ZbCnt\":0,\"ZxCnt\":8}]";

        String json = "[{\"AmtInfo\":{\"a9bbfb4328530d36e3a2379ef1c498f2\":{\"Amt\":\"2279564\",\"IsValid\":\"1\",\"Type\":\"执行标的\"}},\"AnNoList\":\"（2006）成执字第973号\",\"AnnoCnt\":1,\"CaseCnt\":0,\"CaseName\":\"成都倍特发展集团股份有限公司与成都南星热电股份有限公司执行案件\",\"CaseReason\":\"\",\"CaseRole\":\"[{\\\"D\\\":\\\"首次执行原告\\\",\\\"N\\\":\\\"g4b4d7e9b4ed8d0b3206714b44ce87d9\\\",\\\"O\\\":4,\\\"P\\\":\\\"成都倍特发展集团股份有限公司\\\",\\\"R\\\":\\\"原告\\\"},{\\\"D\\\":\\\"首次执行被告\\\",\\\"N\\\":\\\"a9bbfb4328530d36e3a2379ef1c498f2\\\",\\\"O\\\":0,\\\"P\\\":\\\"成都南星热电股份有限公司\\\",\\\"R\\\":\\\"被告\\\"}]\",\"CaseType\":\"执行案件\",\"CfdfCnt\":0,\"CfgsCnt\":0,\"CfxyCnt\":0,\"CompanyKeywords\":\"a9bbfb4328530d36e3a2379ef1c498f2,g4b4d7e9b4ed8d0b3206714b44ce87d9,成都倍特发展集团股份有限公司,成都南星热电股份有限公司\",\"CourtList\":\"四川省成都市中级人民法院\",\"EarliestDate\":1152720000,\"EarliestDateType\":\"首次执行|立案日期\",\"FyggCnt\":0,\"GqdjCnt\":0,\"GroupId\":\"06c85920c0149f485fd76c75054ce447\",\"HbcfCnt\":0,\"Id\":\"059de2d7e0cb8eb2c2b67a89cebe0bf3\",\"InfoList\":[{\"AnNo\":\"（2006）成执字第973号\",\"CaseList\":[],\"CaseReason\":\"\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"四川省成都市中级人民法院\",\"Defendant\":[{\"KeyNo\":\"a9bbfb4328530d36e3a2379ef1c498f2\",\"Name\":\"成都南星热电股份有限公司\",\"Org\":0,\"Role\":\"被告\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1157385600,\"LianList\":[{\"Id\":\"d38054ef0b099fd639bd31e8a0a09557\",\"IsValid\":1,\"LianDate\":1152720000,\"NameAndKeyNo\":[{\"KeyNo\":\"g4b4d7e9b4ed8d0b3206714b44ce87d9\",\"Name\":\"成都倍特发展集团股份有限公司\",\"Org\":4},{\"KeyNo\":\"a9bbfb4328530d36e3a2379ef1c498f2\",\"Name\":\"成都南星热电股份有限公司\",\"Org\":0}]}],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"g4b4d7e9b4ed8d0b3206714b44ce87d9\",\"Name\":\"成都倍特发展集团股份有限公司\",\"Org\":4,\"Role\":\"原告\"},{\"KeyNo\":\"g4b4d7e9b4ed8d0b3206714b44ce87dX\",\"Name\":\"X成都倍特发展集团股份有限公司\",\"Org\":4,\"Role\":\"原告\"}],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"首次执行\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[{\"ExecuteObject\":\"2279564\",\"FailureAct\":\"\",\"Id\":\"e33a2a1eb18d9c8ee385b3d05737e873\",\"IsValid\":1,\"JudgeDate\":1157385600,\"NameAndKeyNo\":[{\"KeyNo\":\"a9bbfb4328530d36e3a2379ef1c498f2\",\"Name\":\"成都南星热电股份有限公司\",\"Org\":0}]}],\"ZxList\":[]}],\"KtggCnt\":0,\"LastestDate\":1157385600,\"LastestDateType\":\"首次执行|案件终本日期\",\"LatestTrialRound\":\"首次执行\",\"LianCnt\":1,\"PcczCnt\":0,\"ProcuratorateList\":\"\",\"Province\":\"SC\",\"SdggCnt\":0,\"Source\":\"OT\",\"SxCnt\":0,\"Tags\":\"6,12\",\"Type\":1,\"XgCnt\":0,\"XjpgCnt\":0,\"XzcfCnt\":0,\"ZbCnt\":1,\"ZxCnt\":0}] ";


        List<LawSuitV3OutputEntity> jsonList =  JSONArray.parseArray(json,LawSuitV3OutputEntity.class);
        for (LawSuitV3OutputEntity outputEntity : jsonList) {
            System.out.println(evaluate(JSON.toJSONString(outputEntity)));
        }

    }

}

@Data
@AllArgsConstructor
@NoArgsConstructor
class AmountType{
    private BigDecimal amount;
    private long timeStamp;
}

@Data
@AllArgsConstructor
@NoArgsConstructor
class CaseRoleInfo{
    private List<NameAndKeyNoEntity> prosecutor;
    private List<NameAndKeyNoEntity> defendant;
}

@Data
class ZwrDebtInfo{
    private String zwrKeyNo;
    private String zwrName;
    private String amountType;
    private BigDecimal amount;
}
