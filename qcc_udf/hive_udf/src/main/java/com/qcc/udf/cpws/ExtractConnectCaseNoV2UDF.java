package com.qcc.udf.cpws;

import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 裁判文书清洗UDF：提取本案件关联的其它裁判文书案号(v3)
 * ---------------------------------------------------------------------------------------------------------
 * create temporary function extractConnectCaseNoV2 as 'com.qcc.udf.cpws.ExtractConnectCaseNoV2UDF' using jar 'hdfs:///data/hive/udf/qcc_udf.jar';
 * ---------------------------------------------------------------------------------------------------------
 * select extractConnectCaseNo (contentClear, caseNo, beforeCaseNo);
 * 结果: 'caseNo1,caseNo2,CaseNo3...'
 */
public class ExtractConnectCaseNoV2UDF extends UDF {
    private final static Set<String> CaseNoKeywordSet = Sets.newHashSet(
            "民", "刑", "行", "辖", "保",
            "赔", "执", "商", "调", "破",
            "知", "司", "催", "申", "法",
            "劳", "交", "立", "督", "减", "仲", "经", "办", "罚");

    /**
     * 从文书正文中提取与该案件关联的其它案号信息（多个案号用,分隔，没有则返回空）
     * @param caseContent   裁判文书正文
     * @param caseNo        当前案件的案号
     * @return
     */
    public String evaluate(String caseContent, String caseNo, String beforeCaseNo) {
        try {
            if (StringUtils.isNotBlank(caseContent) && StringUtils.isNotBlank(caseNo)) {
                String content = HtmlToTextUtil.convert2(caseContent);
                /**
                 * 提取逻辑描述：
                 * 1 转为全角括号
                 * 2 正则提取段落中的案号  (（[0-9]{4}）[\u4e00-\u9fa5]+.+?号(之[一二三四五六七八九十])*)
                 * 3 剔除案号列表提取字数较少的案号以及与自身重复的案号
                 * 4 剔除包含在beforeCaseNo中的案号
                 * 5 返回去重后的结果
                 */
                String excludeCaseNoInfo = beforeCaseNo.replace("(", "（").replace(")", "）");

                Pattern extractPattern = Pattern.compile("(（[0-9]{4}）[\\u4e00-\\u9fa5]+.+?号(之[一二三四五六七八九十])*)");
                Matcher matcher = extractPattern.matcher(
                        content.replace("(", "（").replace(")", "）"));
                List<String> linkedCaseNoList = new ArrayList<>();
                while (matcher.find()) {
                    linkedCaseNoList.add(matcher.group(0));
                }

                final String compareCaseNo = caseNo.replace("(", "（").replace(")", "）");
                Set<String> beforeCaseNoSet = linkedCaseNoList.stream()
                        .filter(e -> !e.equals(compareCaseNo))
                        .filter(e -> !e.contains("第xx号"))
                        .filter(e -> !e.contains("["))
                        .filter(e -> e.length() > 8 && e.length() < 38)
                        .filter(e -> containKeyword(e))
                        .filter(e -> !excludeCaseNoInfo.contains(e))
                        .collect(Collectors.toSet());
                return StringUtils.join(beforeCaseNoSet, ",");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return "";
    }

    private static Boolean containKeyword(String caseNo) {
        Boolean isMatch = false;
        if (StringUtils.isNotBlank(caseNo)) {
            for (String keyword : CaseNoKeywordSet) {
                if (caseNo.contains(keyword)) {
                    isMatch = true;
                    break;
                }
            }
        }
        return isMatch;
    }

    public static void main(String[] args) {
        String caseContent = "<html xmlns:v=\"urn:schemas-microsoft-com:vml\"xmlns:o=\"urn:schemas-microsoft-com:office:office\"xmlns:w=\"urn:schemas-microsoft-com:office:word\"xmlns:m=\"http://schemas.microsoft.com/office/2004/12/omml\"xmlns=\"http://www.w3.org/TR/REC-html40\"><head><meta http-equiv=Content-Type content=\"text/html; \"><meta name=ProgId content=Word.Document><meta name=Generator content=\"Microsoft Word 14\"><meta name=Originator content=\"Microsoft Word 14\"><link rel=File-List href=\"2ee8d4c2200e4895a2ba475be476b915.files/filelist.xml\"><!--[if gte mso 9]><xml> <o:DocumentProperties>  <o:Author>admin</o:Author>  <o:Template>Normal</o:Template>  <o:LastAuthor>Windows 用户</o:LastAuthor>  <o:Revision>2</o:Revision>  <o:TotalTime>380</o:TotalTime>  <o:Created>2019-05-31T03:29:00Z</o:Created>  <o:LastSaved>2019-05-31T03:29:00Z</o:LastSaved>  <o:Pages>2</o:Pages>  <o:Words>118</o:Words>  <o:Characters>674</o:Characters>  <o:Lines>5</o:Lines>  <o:Paragraphs>1</o:Paragraphs>  <o:CharactersWithSpaces>791</o:CharactersWithSpaces>  <o:Version>14.00</o:Version> </o:DocumentProperties></xml><![endif]--><link rel=themeData href=\"2ee8d4c2200e4895a2ba475be476b915.files/themedata.thmx\"><link rel=colorSchemeMappinghref=\"2ee8d4c2200e4895a2ba475be476b915.files/colorschememapping.xml\"><!--[if gte mso 9]><xml> <w:WordDocument>  <w:TrackMoves/>  <w:TrackFormatting/>  <w:PunctuationKerning/>  <w:DrawingGridVerticalSpacing>7.8 磅</w:DrawingGridVerticalSpacing>  <w:DisplayHorizontalDrawingGridEvery>0</w:DisplayHorizontalDrawingGridEvery>  <w:DisplayVerticalDrawingGridEvery>2</w:DisplayVerticalDrawingGridEvery>  <w:ValidateAgainstSchemas/>  <w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid>  <w:IgnoreMixedContent>false</w:IgnoreMixedContent>  <w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText>  <w:DoNotPromoteQF/>  <w:LidThemeOther>EN-US</w:LidThemeOther>  <w:LidThemeAsian>ZH-CN</w:LidThemeAsian>  <w:LidThemeComplexScript>X-NONE</w:LidThemeComplexScript>  <w:Compatibility>   <w:SpaceForUL/>   <w:BalanceSingleByteDoubleByteWidth/>   <w:DoNotLeaveBackslashAlone/>   <w:ULTrailSpace/>   <w:DoNotExpandShiftReturn/>   <w:AdjustLineHeightInTable/>   <w:BreakWrappedTables/>   <w:SnapToGridInCell/>   <w:WrapTextWithPunct/>   <w:UseAsianBreakRules/>   <w:DontGrowAutofit/>   <w:SplitPgBreakAndParaMark/>   <w:UseFELayout/>  </w:Compatibility>  <m:mathPr>   <m:mathFont m:val=\"Cambria Math\"/>   <m:brkBin m:val=\"before\"/>   <m:brkBinSub m:val=\"&#45;-\"/>   <m:smallFrac m:val=\"off\"/>   <m:dispDef/>   <m:lMargin m:val=\"0\"/>   <m:rMargin m:val=\"0\"/>   <m:defJc m:val=\"centerGroup\"/>   <m:wrapIndent m:val=\"1440\"/>   <m:intLim m:val=\"subSup\"/>   <m:naryLim m:val=\"undOvr\"/>  </m:mathPr></w:WordDocument></xml><![endif]--><!--[if gte mso 9]><xml> <w:LatentStyles DefLockedState=\"false\" DefUnhideWhenUsed=\"true\"  DefSemiHidden=\"true\" DefQFormat=\"false\" DefPriority=\"99\"  LatentStyleCount=\"267\">  <w:LsdException Locked=\"false\" Priority=\"0\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" QFormat=\"true\" Name=\"Normal\"/>  <w:LsdException Locked=\"false\" Priority=\"9\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" QFormat=\"true\" Name=\"heading 1\"/>  <w:LsdException Locked=\"false\" Priority=\"9\" QFormat=\"true\" Name=\"heading 2\"/>  <w:LsdException Locked=\"false\" Priority=\"9\" QFormat=\"true\" Name=\"heading 3\"/>  <w:LsdException Locked=\"false\" Priority=\"9\" QFormat=\"true\" Name=\"heading 4\"/>  <w:LsdException Locked=\"false\" Priority=\"9\" QFormat=\"true\" Name=\"heading 5\"/>  <w:LsdException Locked=\"false\" Priority=\"9\" QFormat=\"true\" Name=\"heading 6\"/>  <w:LsdException Locked=\"false\" Priority=\"9\" QFormat=\"true\" Name=\"heading 7\"/>  <w:LsdException Locked=\"false\" Priority=\"9\" QFormat=\"true\" Name=\"heading 8\"/>  <w:LsdException Locked=\"false\" Priority=\"9\" QFormat=\"true\" Name=\"heading 9\"/>  <w:LsdException Locked=\"false\" Priority=\"39\" Name=\"toc 1\"/>  <w:LsdException Locked=\"false\" Priority=\"39\" Name=\"toc 2\"/>  <w:LsdException Locked=\"false\" Priority=\"39\" Name=\"toc 3\"/>  <w:LsdException Locked=\"false\" Priority=\"39\" Name=\"toc 4\"/>  <w:LsdException Locked=\"false\" Priority=\"39\" Name=\"toc 5\"/>  <w:LsdException Locked=\"false\" Priority=\"39\" Name=\"toc 6\"/>  <w:LsdException Locked=\"false\" Priority=\"39\" Name=\"toc 7\"/>  <w:LsdException Locked=\"false\" Priority=\"39\" Name=\"toc 8\"/>  <w:LsdException Locked=\"false\" Priority=\"39\" Name=\"toc 9\"/>  <w:LsdException Locked=\"false\" Priority=\"35\" QFormat=\"true\" Name=\"caption\"/>  <w:LsdException Locked=\"false\" Priority=\"10\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" QFormat=\"true\" Name=\"Title\"/>  <w:LsdException Locked=\"false\" Priority=\"1\" Name=\"Default Paragraph Font\"/>  <w:LsdException Locked=\"false\" Priority=\"0\" Name=\"Body Text Indent\"/>  <w:LsdException Locked=\"false\" Priority=\"11\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" QFormat=\"true\" Name=\"Subtitle\"/>  <w:LsdException Locked=\"false\" Priority=\"22\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" QFormat=\"true\" Name=\"Strong\"/>  <w:LsdException Locked=\"false\" Priority=\"20\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" QFormat=\"true\" Name=\"Emphasis\"/>  <w:LsdException Locked=\"false\" Priority=\"59\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Table Grid\"/>  <w:LsdException Locked=\"false\" UnhideWhenUsed=\"false\" Name=\"Placeholder Text\"/>  <w:LsdException Locked=\"false\" Priority=\"1\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" QFormat=\"true\" Name=\"No Spacing\"/>  <w:LsdException Locked=\"false\" Priority=\"60\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Light Shading\"/>  <w:LsdException Locked=\"false\" Priority=\"61\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Light List\"/>  <w:LsdException Locked=\"false\" Priority=\"62\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Light Grid\"/>  <w:LsdException Locked=\"false\" Priority=\"63\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Shading 1\"/>  <w:LsdException Locked=\"false\" Priority=\"64\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Shading 2\"/>  <w:LsdException Locked=\"false\" Priority=\"65\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium List 1\"/>  <w:LsdException Locked=\"false\" Priority=\"66\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium List 2\"/>  <w:LsdException Locked=\"false\" Priority=\"67\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Grid 1\"/>  <w:LsdException Locked=\"false\" Priority=\"68\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Grid 2\"/>  <w:LsdException Locked=\"false\" Priority=\"69\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Grid 3\"/>  <w:LsdException Locked=\"false\" Priority=\"70\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Dark List\"/>  <w:LsdException Locked=\"false\" Priority=\"71\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Colorful Shading\"/>  <w:LsdException Locked=\"false\" Priority=\"72\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Colorful List\"/>  <w:LsdException Locked=\"false\" Priority=\"73\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Colorful Grid\"/>  <w:LsdException Locked=\"false\" Priority=\"60\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Light Shading Accent 1\"/>  <w:LsdException Locked=\"false\" Priority=\"61\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Light List Accent 1\"/>  <w:LsdException Locked=\"false\" Priority=\"62\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Light Grid Accent 1\"/>  <w:LsdException Locked=\"false\" Priority=\"63\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Shading 1 Accent 1\"/>  <w:LsdException Locked=\"false\" Priority=\"64\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Shading 2 Accent 1\"/>  <w:LsdException Locked=\"false\" Priority=\"65\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium List 1 Accent 1\"/>  <w:LsdException Locked=\"false\" UnhideWhenUsed=\"false\" Name=\"Revision\"/>  <w:LsdException Locked=\"false\" Priority=\"34\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" QFormat=\"true\" Name=\"List Paragraph\"/>  <w:LsdException Locked=\"false\" Priority=\"29\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" QFormat=\"true\" Name=\"Quote\"/>  <w:LsdException Locked=\"false\" Priority=\"30\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" QFormat=\"true\" Name=\"Intense Quote\"/>  <w:LsdException Locked=\"false\" Priority=\"66\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium List 2 Accent 1\"/>  <w:LsdException Locked=\"false\" Priority=\"67\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Grid 1 Accent 1\"/>  <w:LsdException Locked=\"false\" Priority=\"68\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Grid 2 Accent 1\"/>  <w:LsdException Locked=\"false\" Priority=\"69\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Grid 3 Accent 1\"/>  <w:LsdException Locked=\"false\" Priority=\"70\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Dark List Accent 1\"/>  <w:LsdException Locked=\"false\" Priority=\"71\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Colorful Shading Accent 1\"/>  <w:LsdException Locked=\"false\" Priority=\"72\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Colorful List Accent 1\"/>  <w:LsdException Locked=\"false\" Priority=\"73\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Colorful Grid Accent 1\"/>  <w:LsdException Locked=\"false\" Priority=\"60\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Light Shading Accent 2\"/>  <w:LsdException Locked=\"false\" Priority=\"61\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Light List Accent 2\"/>  <w:LsdException Locked=\"false\" Priority=\"62\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Light Grid Accent 2\"/>  <w:LsdException Locked=\"false\" Priority=\"63\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Shading 1 Accent 2\"/>  <w:LsdException Locked=\"false\" Priority=\"64\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Shading 2 Accent 2\"/>  <w:LsdException Locked=\"false\" Priority=\"65\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium List 1 Accent 2\"/>  <w:LsdException Locked=\"false\" Priority=\"66\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium List 2 Accent 2\"/>  <w:LsdException Locked=\"false\" Priority=\"67\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Grid 1 Accent 2\"/>  <w:LsdException Locked=\"false\" Priority=\"68\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Grid 2 Accent 2\"/>  <w:LsdException Locked=\"false\" Priority=\"69\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Grid 3 Accent 2\"/>  <w:LsdException Locked=\"false\" Priority=\"70\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Dark List Accent 2\"/>  <w:LsdException Locked=\"false\" Priority=\"71\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Colorful Shading Accent 2\"/>  <w:LsdException Locked=\"false\" Priority=\"72\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Colorful List Accent 2\"/>  <w:LsdException Locked=\"false\" Priority=\"73\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Colorful Grid Accent 2\"/>  <w:LsdException Locked=\"false\" Priority=\"60\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Light Shading Accent 3\"/>  <w:LsdException Locked=\"false\" Priority=\"61\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Light List Accent 3\"/>  <w:LsdException Locked=\"false\" Priority=\"62\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Light Grid Accent 3\"/>  <w:LsdException Locked=\"false\" Priority=\"63\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Shading 1 Accent 3\"/>  <w:LsdException Locked=\"false\" Priority=\"64\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Shading 2 Accent 3\"/>  <w:LsdException Locked=\"false\" Priority=\"65\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium List 1 Accent 3\"/>  <w:LsdException Locked=\"false\" Priority=\"66\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium List 2 Accent 3\"/>  <w:LsdException Locked=\"false\" Priority=\"67\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Grid 1 Accent 3\"/>  <w:LsdException Locked=\"false\" Priority=\"68\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Grid 2 Accent 3\"/>  <w:LsdException Locked=\"false\" Priority=\"69\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Grid 3 Accent 3\"/>  <w:LsdException Locked=\"false\" Priority=\"70\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Dark List Accent 3\"/>  <w:LsdException Locked=\"false\" Priority=\"71\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Colorful Shading Accent 3\"/>  <w:LsdException Locked=\"false\" Priority=\"72\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Colorful List Accent 3\"/>  <w:LsdException Locked=\"false\" Priority=\"73\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Colorful Grid Accent 3\"/>  <w:LsdException Locked=\"false\" Priority=\"60\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Light Shading Accent 4\"/>  <w:LsdException Locked=\"false\" Priority=\"61\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Light List Accent 4\"/>  <w:LsdException Locked=\"false\" Priority=\"62\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Light Grid Accent 4\"/>  <w:LsdException Locked=\"false\" Priority=\"63\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Shading 1 Accent 4\"/>  <w:LsdException Locked=\"false\" Priority=\"64\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Shading 2 Accent 4\"/>  <w:LsdException Locked=\"false\" Priority=\"65\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium List 1 Accent 4\"/>  <w:LsdException Locked=\"false\" Priority=\"66\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium List 2 Accent 4\"/>  <w:LsdException Locked=\"false\" Priority=\"67\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Grid 1 Accent 4\"/>  <w:LsdException Locked=\"false\" Priority=\"68\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Grid 2 Accent 4\"/>  <w:LsdException Locked=\"false\" Priority=\"69\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Grid 3 Accent 4\"/>  <w:LsdException Locked=\"false\" Priority=\"70\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Dark List Accent 4\"/>  <w:LsdException Locked=\"false\" Priority=\"71\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Colorful Shading Accent 4\"/>  <w:LsdException Locked=\"false\" Priority=\"72\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Colorful List Accent 4\"/>  <w:LsdException Locked=\"false\" Priority=\"73\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Colorful Grid Accent 4\"/>  <w:LsdException Locked=\"false\" Priority=\"60\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Light Shading Accent 5\"/>  <w:LsdException Locked=\"false\" Priority=\"61\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Light List Accent 5\"/>  <w:LsdException Locked=\"false\" Priority=\"62\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Light Grid Accent 5\"/>  <w:LsdException Locked=\"false\" Priority=\"63\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Shading 1 Accent 5\"/>  <w:LsdException Locked=\"false\" Priority=\"64\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Shading 2 Accent 5\"/>  <w:LsdException Locked=\"false\" Priority=\"65\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium List 1 Accent 5\"/>  <w:LsdException Locked=\"false\" Priority=\"66\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium List 2 Accent 5\"/>  <w:LsdException Locked=\"false\" Priority=\"67\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Grid 1 Accent 5\"/>  <w:LsdException Locked=\"false\" Priority=\"68\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Grid 2 Accent 5\"/>  <w:LsdException Locked=\"false\" Priority=\"69\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Grid 3 Accent 5\"/>  <w:LsdException Locked=\"false\" Priority=\"70\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Dark List Accent 5\"/>  <w:LsdException Locked=\"false\" Priority=\"71\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Colorful Shading Accent 5\"/>  <w:LsdException Locked=\"false\" Priority=\"72\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Colorful List Accent 5\"/>  <w:LsdException Locked=\"false\" Priority=\"73\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Colorful Grid Accent 5\"/>  <w:LsdException Locked=\"false\" Priority=\"60\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Light Shading Accent 6\"/>  <w:LsdException Locked=\"false\" Priority=\"61\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Light List Accent 6\"/>  <w:LsdException Locked=\"false\" Priority=\"62\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Light Grid Accent 6\"/>  <w:LsdException Locked=\"false\" Priority=\"63\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Shading 1 Accent 6\"/>  <w:LsdException Locked=\"false\" Priority=\"64\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Shading 2 Accent 6\"/>  <w:LsdException Locked=\"false\" Priority=\"65\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium List 1 Accent 6\"/>  <w:LsdException Locked=\"false\" Priority=\"66\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium List 2 Accent 6\"/>  <w:LsdException Locked=\"false\" Priority=\"67\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Grid 1 Accent 6\"/>  <w:LsdException Locked=\"false\" Priority=\"68\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Grid 2 Accent 6\"/>  <w:LsdException Locked=\"false\" Priority=\"69\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Medium Grid 3 Accent 6\"/>  <w:LsdException Locked=\"false\" Priority=\"70\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Dark List Accent 6\"/>  <w:LsdException Locked=\"false\" Priority=\"71\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Colorful Shading Accent 6\"/>  <w:LsdException Locked=\"false\" Priority=\"72\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Colorful List Accent 6\"/>  <w:LsdException Locked=\"false\" Priority=\"73\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" Name=\"Colorful Grid Accent 6\"/>  <w:LsdException Locked=\"false\" Priority=\"19\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" QFormat=\"true\" Name=\"Subtle Emphasis\"/>  <w:LsdException Locked=\"false\" Priority=\"21\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" QFormat=\"true\" Name=\"Intense Emphasis\"/>  <w:LsdException Locked=\"false\" Priority=\"31\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" QFormat=\"true\" Name=\"Subtle Reference\"/>  <w:LsdException Locked=\"false\" Priority=\"32\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" QFormat=\"true\" Name=\"Intense Reference\"/>  <w:LsdException Locked=\"false\" Priority=\"33\" SemiHidden=\"false\"   UnhideWhenUsed=\"false\" QFormat=\"true\" Name=\"Book Title\"/>  <w:LsdException Locked=\"false\" Priority=\"37\" Name=\"Bibliography\"/>  <w:LsdException Locked=\"false\" Priority=\"39\" QFormat=\"true\" Name=\"TOC Heading\"/> </w:LatentStyles></xml><![endif]--><style><!-- /* Font Definitions */ @font-face{font-family:宋体;panose-1:2 1 6 0 3 1 1 1 1 1;mso-font-alt:SimSun;mso-font-charset:134;mso-generic-font-family:auto;mso-font-pitch:variable;mso-font-signature:3 680460288 22 0 262145 0;}@font-face{font-family:宋体;panose-1:2 1 6 0 3 1 1 1 1 1;mso-font-alt:SimSun;mso-font-charset:134;mso-generic-font-family:auto;mso-font-pitch:variable;mso-font-signature:3 680460288 22 0 262145 0;}@font-face{font-family:Calibri;panose-1:2 15 5 2 2 2 4 3 2 4;mso-font-charset:0;mso-generic-font-family:swiss;mso-font-pitch:variable;mso-font-signature:-520092929 1073786111 9 0 415 0;}@font-face{font-family:仿宋_GB2312;mso-font-alt:仿宋;mso-font-charset:134;mso-generic-font-family:modern;mso-font-pitch:fixed;mso-font-signature:1 135135232 16 0 262144 0;}@font-face{font-family:\"\\@宋体\";panose-1:2 1 6 0 3 1 1 1 1 1;mso-font-charset:134;mso-generic-font-family:auto;mso-font-pitch:variable;mso-font-signature:3 680460288 22 0 262145 0;}@font-face{font-family:\"\\@仿宋_GB2312\";mso-font-charset:134;mso-generic-font-family:modern;mso-font-pitch:fixed;mso-font-signature:1 135135232 16 0 262144 0;} /* Style Definitions */ p.MsoNormal, li.MsoNormal, div.MsoNormal{mso-style-unhide:no;mso-style-qformat:yes;mso-style-parent:\"\";margin:0cm;margin-bottom:.0001pt;text-align:justify;text-justify:inter-ideograph;mso-pagination:none;font-size:10.5pt;mso-bidi-font-size:12.0pt;font-family:\"Times New Roman\",\"serif\";mso-fareast-font-family:宋体;mso-font-kerning:1.0pt;}p.MsoHeader, li.MsoHeader, div.MsoHeader{mso-style-priority:99;mso-style-link:\"页眉 Char\";margin:0cm;margin-bottom:.0001pt;text-align:center;mso-pagination:none;tab-stops:center 207.65pt right 415.3pt;layout-grid-mode:char;border:none;mso-border-bottom-alt:solid windowtext .75pt;padding:0cm;mso-padding-alt:0cm 0cm 1.0pt 0cm;font-size:9.0pt;font-family:\"Calibri\",\"sans-serif\";mso-ascii-font-family:Calibri;mso-ascii-theme-font:minor-latin;mso-fareast-font-family:宋体;mso-fareast-theme-font:minor-fareast;mso-hansi-font-family:Calibri;mso-hansi-theme-font:minor-latin;mso-bidi-font-family:\"Times New Roman\";mso-bidi-theme-font:minor-bidi;mso-font-kerning:1.0pt;}p.MsoFooter, li.MsoFooter, div.MsoFooter{mso-style-priority:99;mso-style-link:\"页脚 Char\";margin:0cm;margin-bottom:.0001pt;mso-pagination:none;tab-stops:center 207.65pt right 415.3pt;layout-grid-mode:char;font-size:9.0pt;font-family:\"Calibri\",\"sans-serif\";mso-ascii-font-family:Calibri;mso-ascii-theme-font:minor-latin;mso-fareast-font-family:宋体;mso-fareast-theme-font:minor-fareast;mso-hansi-font-family:Calibri;mso-hansi-theme-font:minor-latin;mso-bidi-font-family:\"Times New Roman\";mso-bidi-theme-font:minor-bidi;mso-font-kerning:1.0pt;}p.MsoBodyTextIndent, li.MsoBodyTextIndent, div.MsoBodyTextIndent{mso-style-unhide:no;mso-style-link:\"正文文本缩进 Char\";margin:0cm;margin-bottom:.0001pt;text-indent:32.25pt;line-height:20.0pt;mso-pagination:widow-orphan;font-size:16.0pt;mso-bidi-font-size:10.0pt;font-family:仿宋_GB2312;mso-hansi-font-family:\"Times New Roman\";mso-bidi-font-family:\"Times New Roman\";}span.Char{mso-style-name:\"页眉 Char\";mso-style-priority:99;mso-style-unhide:no;mso-style-locked:yes;mso-style-link:页眉;mso-ansi-font-size:9.0pt;mso-bidi-font-size:9.0pt;}span.Char0{mso-style-name:\"页脚 Char\";mso-style-priority:99;mso-style-unhide:no;mso-style-locked:yes;mso-style-link:页脚;mso-ansi-font-size:9.0pt;mso-bidi-font-size:9.0pt;}span.Char1{mso-style-name:\"正文文本缩进 Char\";mso-style-unhide:no;mso-style-locked:yes;mso-style-link:正文文本缩进;mso-ansi-font-size:16.0pt;mso-bidi-font-size:10.0pt;font-family:仿宋_GB2312;mso-ascii-font-family:仿宋_GB2312;mso-fareast-font-family:仿宋_GB2312;mso-hansi-font-family:\"Times New Roman\";mso-bidi-font-family:\"Times New Roman\";mso-font-kerning:0pt;}.MsoChpDefault{mso-style-type:export-only;mso-default-props:yes;font-family:\"Calibri\",\"sans-serif\";mso-bidi-font-family:\"Times New Roman\";mso-bidi-theme-font:minor-bidi;} /* Page Definitions */ @page{mso-page-border-surround-header:no;mso-page-border-surround-footer:no;mso-footnote-separator:url(\"2ee8d4c2200e4895a2ba475be476b915.files/header.html\") fs;mso-footnote-continuation-separator:url(\"2ee8d4c2200e4895a2ba475be476b915.files/header.html\") fcs;mso-endnote-separator:url(\"2ee8d4c2200e4895a2ba475be476b915.files/header.html\") es;mso-endnote-continuation-separator:url(\"2ee8d4c2200e4895a2ba475be476b915.files/header.html\") ecs;}@page WordSection1{size:595.3pt 841.9pt;margin:72.0pt 85.0pt 92.15pt 90.0pt;mso-header-margin:42.55pt;mso-footer-margin:49.6pt;mso-footer:url(\"2ee8d4c2200e4895a2ba475be476b915.files/header.html\") f1;mso-paper-source:0;layout-grid:15.6pt;}div.WordSection1{page:WordSection1;}--></style><!--[if gte mso 10]><style> /* Style Definitions */ table.MsoNormalTable{mso-style-name:普通表格;mso-tstyle-rowband-size:0;mso-tstyle-colband-size:0;mso-style-noshow:yes;mso-style-priority:99;mso-style-parent:\"\";mso-padding-alt:0cm 5.4pt 0cm 5.4pt;mso-para-margin:0cm;mso-para-margin-bottom:.0001pt;mso-pagination:widow-orphan;font-size:10.5pt;mso-bidi-font-size:11.0pt;font-family:\"Calibri\",\"sans-serif\";mso-ascii-font-family:Calibri;mso-ascii-theme-font:minor-latin;mso-hansi-font-family:Calibri;mso-hansi-theme-font:minor-latin;mso-bidi-font-family:\"Times New Roman\";mso-bidi-theme-font:minor-bidi;mso-font-kerning:1.0pt;}</style><![endif]--><!--[if gte mso 9]><xml> <o:shapedefaults v:ext=\"edit\" spidmax=\"2050\"/></xml><![endif]--><!--[if gte mso 9]><xml> <o:shapelayout v:ext=\"edit\">  <o:idmap v:ext=\"edit\" data=\"2\"/> </o:shapelayout></xml><![endif]--></head><body lang=ZH-CN style='tab-interval:21.0pt;text-justify-trim:punctuation'><div class=WordSection1 style='layout-grid:15.6pt'><p class=MsoNormal align=center style='text-align:center;line-height:34.0pt;mso-line-height-rule:exactly;mso-pagination:widow-orphan'><spanstyle='font-size:22.0pt;mso-bidi-font-size:12.0pt;font-family:宋体;mso-ascii-font-family:\"Times New Roman\";mso-hansi-font-family:\"Times New Roman\"'>北京市平谷区人民法院</span><spanlang=EN-US style='font-size:22.0pt;mso-bidi-font-size:10.0pt;mso-font-kerning:0pt'><o:p></o:p></span></p><p class=MsoNormal align=center style='text-align:center;line-height:34.0pt;mso-line-height-rule:exactly'><span style='font-size:26.0pt;mso-bidi-font-size:12.0pt;font-family:宋体;mso-ascii-font-family:\"Times New Roman\";mso-hansi-font-family:\"Times New Roman\"'>刑事判决书</span><span lang=EN-US style='font-size:26.0pt;mso-bidi-font-size:10.0pt'><o:p></o:p></span></p><p class=MsoNormal align=right style='text-align:right;line-height:26.0pt;mso-line-height-rule:exactly'><span lang=EN-US style='font-size:16.0pt;mso-bidi-font-size:10.0pt;mso-fareast-font-family:仿宋_GB2312'><o:p>&nbsp;</o:p></span></p><p class=MsoNormal align=right style='text-align:right;line-height:26.0pt;mso-line-height-rule:exactly'><span style='font-size:16.0pt;mso-bidi-font-size:12.0pt;font-family:仿宋_GB2312'>（<span lang=EN-US>2019</span>）京<span lang=EN-US>0117</span>刑初<spanlang=EN-US>153</span>号<span lang=EN-US><o:p></o:p></span></span></p><p class=MsoNormal align=right style='text-align:right;line-height:26.0pt;mso-line-height-rule:exactly'><span lang=EN-US style='font-size:16.0pt;mso-bidi-font-size:12.0pt;font-family:仿宋_GB2312'><o:p>&nbsp;</o:p></span></p><p class=MsoNormal style='text-indent:32.0pt;mso-char-indent-count:2.0;line-height:26.0pt;mso-line-height-rule:exactly'><span style='font-size:16.0pt;font-family:仿宋_GB2312'>公诉机关<a href=\"https://www.qcc.com/firm_gb697043da5cd3571b98d6cc7619d152.html\" target=\"_blank\">北京市平谷区人民检察院</a>。<span lang=EN-US><o:p></o:p></span></span></p><p class=MsoNormal style='text-indent:32.0pt;mso-char-indent-count:2.0;line-height:26.0pt;mso-line-height-rule:exactly'><spanstyle='font-size:16.0pt;font-family:仿宋_GB2312'>被告人王攀，男，<span lang=EN-US>1988</span>年<spanlang=EN-US>11</span>月<span lang=EN-US>20</span>日出生于北京市，汉族，大专文化，中共党员，北京市平谷区某村委会会计，住北京市平谷区；因涉嫌犯危险驾驶罪，于<spanlang=EN-US>2019</span>年<span lang=EN-US>5</span>月<span lang=EN-US>13</span>日被刑事拘留。现羁押在北京市平谷区看守所。<spanlang=EN-US style='color:black'><o:p></o:p></span></span></p><p class=MsoNormal style='text-indent:32.0pt;mso-char-indent-count:2.0;line-height:26.0pt;mso-line-height-rule:exactly'><span style='font-size:16.0pt;font-family:仿宋_GB2312;color:black'>公诉机关以京平检一部刑诉<span lang=EN-US>[2019]37</span>号起诉书指控被告人王攀犯危险驾驶罪。本院适用刑事案件速裁程序，实行独任审判，公开开庭审理了本案。<spanlang=EN-US><o:p></o:p></span></span></p><p class=MsoNormal style='text-indent:32.0pt;mso-char-indent-count:2.0;line-height:26.0pt;mso-line-height-rule:exactly'><span style='font-size:16.0pt;font-family:仿宋_GB2312;color:black'>公诉机关指控，<span lang=EN-US>2019</span>年<spanlang=EN-US>5</span>月<span lang=EN-US>12</span>日<span lang=EN-US>23</span>时许，被告人王攀酒后驾驶无号牌的白色本田牌<spanlang=EN-US>50</span>型轻便两轮摩托车，沿平谷区某街由西向东行驶至某饭店对面，与杜某发生纠纷后被查获。经鉴定，被告人王攀血液中酒精含量为<spanlang=EN-US>148.8mg/100ml</span>。<span lang=EN-US>2019</span>年<span lang=EN-US>5</span>月<spanlang=EN-US>13</span>日，被告人王攀被公安机关查获。公诉机关认为被告人王攀具有如实供述犯罪事实的从轻处罚情节，建议判处王攀拘役<spanlang=EN-US>2</span>个月至<span lang=EN-US>3</span>个月，并处罚金。<span lang=EN-US><o:p></o:p></span></span></p><p class=MsoNormal style='text-indent:32.0pt;mso-char-indent-count:2.0;line-height:26.0pt;mso-line-height-rule:exactly'><span style='font-size:16.0pt;font-family:仿宋_GB2312'>被告人王攀对指控的事实、罪名及量刑建议均没有异议且签字具结，在开庭审理过程中亦无异议。<spanlang=EN-US><o:p></o:p></span></span></p><p class=MsoNormal style='text-indent:32.0pt;mso-char-indent-count:2.0;line-height:26.0pt;mso-line-height-rule:exactly'><span style='font-size:16.0pt;font-family:仿宋_GB2312'>本院认为，公诉机<span style='color:black'>关指控被告人王攀犯危险驾驶罪的事实清楚，提供的证据确实、充分，指控罪名成立，量刑建议适当，应予采纳。依照《中华人民共和国刑法》第一百三十三条之一第一款第（二）项、第四十二条、第四十四条、第五十二条、第五十三条、第六十七条第三款、第六十一条之规定，判决如下：<spanlang=EN-US><o:p></o:p></span></span></span></p><p class=MsoNormal style='text-indent:32.0pt;mso-char-indent-count:2.0;line-height:26.0pt;mso-line-height-rule:exactly'><span style='font-size:16.0pt;font-family:仿宋_GB2312;color:black'>被告人王攀犯危险驾驶罪，判处拘役二个月，罚金人民币四千元（刑期自<spanlang=EN-US>2019</span>年<span lang=EN-US>5</span>月<span lang=EN-US>13</span>日起至<spanlang=EN-US>2019</span>年<span lang=EN-US>7</span>月<span lang=EN-US>12</span>日止；罚金限判决生效后<spanlang=EN-US>10</span>日内缴纳）。<span lang=EN-US><o:p></o:p></span></span></p><p class=MsoNormal style='text-indent:32.0pt;mso-char-indent-count:2.0;line-height:26.0pt;mso-line-height-rule:exactly'><span style='font-size:16.0pt;font-family:仿宋_GB2312;color:black'>如不服本判决，可在接到判决书的第二日起十日内，通过本院或者直接向北京市第三中级人民法院提出上诉。书面上诉的，应当提交上诉状正本一份，副本一份。<spanlang=EN-US><o:p></o:p></span></span></p><p class=MsoNormal style='text-indent:32.0pt;mso-char-indent-count:2.0;line-height:26.0pt;mso-line-height-rule:exactly'><span lang=EN-USstyle='font-size:16.0pt;font-family:仿宋_GB2312;color:black'><o:p>&nbsp;</o:p></span></p><p class=MsoBodyTextIndent align=right style='text-align:right;text-indent:0cm;line-height:26.0pt;mso-line-height-rule:exactly'><span lang=EN-USstyle='mso-bidi-font-size:16.0pt'><o:p>&nbsp;</o:p></span></p><p class=MsoBodyTextIndent align=right style='text-align:right;text-indent:0cm;line-height:26.0pt;mso-line-height-rule:exactly'><span lang=EN-USstyle='mso-bidi-font-size:16.0pt'><o:p>&nbsp;</o:p></span></p><p class=MsoBodyTextIndent align=right style='text-align:right;text-indent:0cm;line-height:26.0pt;mso-line-height-rule:exactly'><span lang=EN-USstyle='mso-bidi-font-size:16.0pt'><o:p>&nbsp;</o:p></span></p><p class=MsoBodyTextIndent align=right style='text-align:right;text-indent:0cm;line-height:26.0pt;mso-line-height-rule:exactly'><span lang=EN-USstyle='mso-bidi-font-size:16.0pt'><o:p>&nbsp;</o:p></span></p><p class=MsoBodyTextIndent align=right style='text-align:right;text-indent:0cm;line-height:26.0pt;mso-line-height-rule:exactly'><span style='mso-bidi-font-size:16.0pt'>审<span lang=EN-US><span style='mso-spacerun:yes'>&nbsp; </span></span>判<spanlang=EN-US><span style='mso-spacerun:yes'>&nbsp; </span></span>员<spanlang=EN-US><span style='mso-spacerun:yes'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span>唐丽珺<span lang=EN-US><o:p></o:p></span></span></p><p class=MsoBodyTextIndent align=right style='margin-right:32.0pt;text-align:right;text-indent:0cm;line-height:26.0pt;mso-line-height-rule:exactly'><spanlang=EN-US style='mso-bidi-font-size:16.0pt'><o:p>&nbsp;</o:p></span></p><p class=MsoNormal align=right style='text-align:right;line-height:26.0pt;mso-line-height-rule:exactly'><span style='font-size:16.0pt;font-family:仿宋_GB2312'>二〇一九年五月三十一日<spanlang=EN-US><o:p></o:p></span></span></p><p class=MsoNormal align=right style='text-align:right;line-height:26.0pt;mso-line-height-rule:exactly'><span lang=EN-US style='font-size:16.0pt;font-family:仿宋_GB2312'><o:p>&nbsp;</o:p></span></p><p class=MsoNormal align=right style='margin-right:2.1pt;text-align:right;line-height:26.0pt;mso-line-height-rule:exactly;word-break:break-all'><spanstyle='font-size:16.0pt;font-family:仿宋_GB2312'>书<span lang=EN-US><spanstyle='mso-spacerun:yes'>&nbsp; </span></span>记<span lang=EN-US><spanstyle='mso-spacerun:yes'>&nbsp; </span></span>员<span lang=EN-US><spanstyle='mso-spacerun:yes'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span>宋春雁<spanlang=EN-US><o:p></o:p></span></span></p></div></body></html>";
        String caseNo = "111";
        String beforeCaseNo = "";

//        String caseContent = "<div style=\"TEXT-ALIGN: center; LINE-HEIGHT: 25pt; MARGIN: 0.5pt 0cm; FONT-FAMILY: 宋体; FONT-SIZE: 22pt;\">  河北省唐山市中级人民法院 </div> <div style=\"TEXT-ALIGN: center; LINE-HEIGHT: 30pt; MARGIN: 0.5pt 0cm; FONT-FAMILY: 仿宋; FONT-SIZE: 26pt;\">  民 事 判 决 书 </div> <div style=\"TEXT-ALIGN: right; LINE-HEIGHT: 30pt; MARGIN: 0.5pt 0cm;  FONT-FAMILY: 仿宋;FONT-SIZE: 16pt; \">  （2017）冀02民终3017号 </div>  <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  上诉人（原审被告）：吴秀春，农民。 </div> <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  委托代理人：胥胜超，<a href=\"https://www.qichacha.com/firm_wb296fb6aad2171b490561d577c6935c.html\" target=\"_blank\">河北华夏中天律师事务所</a>律师。 </div> <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  委托代理人：宋旭光，<a href=\"https://www.qichacha.com/firm_wb296fb6aad2171b490561d577c6935c.html\" target=\"_blank\">河北华夏中天律师事务所</a>律师。 </div> <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  被上诉人（原审原告）：陆文强，农民。 </div> <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  委托代理人：徐立增，<a href=\"https://www.qichacha.com/firm_w82c69bb60812e578d95bd7ee696a1d4.html\" target=\"_blank\">河北力公律师事务所</a>律师。 </div> <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  委托代理人：倪晓佳，<a href=\"https://www.qichacha.com/firm_w82c69bb60812e578d95bd7ee696a1d4.html\" target=\"_blank\">河北力公律师事务所</a>律师。 </div>  <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  上诉人吴秀春因买卖合同纠纷一案，不服河北省遵化市人民法院（2016）冀0281民初3367号民事判决，向本院提起上诉，本院依法组成合议庭公开开庭审理了本案。本案现已审理终结。 </div>  <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  一审法院经审理查明：2013年4月27日，原、被告双方签订了协议书一份，内容为：“协议书甲方：吴秀春身份证号码：乙方：陆文强身份证号：甲方有玫瑰园住宅三套，原为玫瑰园售楼处顶工程款给赤峰建工集团，后赤峰建工集团顶钢材款给甲方。经中证方居间介绍，甲乙双方友好协商，甲方自愿将玫瑰园一区（建明街路北法院东侧）16栋2单元1303室出售给乙方，建筑面积为：92.53平米，售价：3050元／平米，地下室30.7平米，售价为1500元／平米，房屋总价：叁拾贰万捌仟元整，￥328000。今乙方向甲方一次性支付全部房款，甲方向乙方提供与赤峰建工集团的顶账证明复印件和此房购房协议及收据。甲方承诺，在乙方领取此房钥匙和办理购房手续时负责办理更名手续。领钥匙及办理房产证的各种初装费及各种税费由乙方承担。甲方：吴秀春乙方：陆文强中证方；王福强2013年4月27日”，该协议后附收条，内容为：“（叁拾贰万捌仟元正）房款以收到。328000元正吴秀春4.28”。2013年7月10日，原、被告双方又签订协议书一份,内容为:“协议书甲方：吴秀春乙方:陆文强甲方有玫瑰园住宅一套，为玫瑰园售楼处顶工程款给赤峰建工集团，后赤峰建工集团顶钢材款给甲方。经甲乙方双方协商，甲方将玫瑰园一区15栋2单元403室出售给乙方，建筑面积为92.75平方米，售价：每平米3000元，房屋总价：贰拾柒万捌仟元整，（278000元）。乙方向甲方一次性支付全部房款，甲方向乙方提供与赤峰建工集团的顶账证明复印件和此房购房协议及收据。此协议双方签字生效。甲方:吴秀春乙方:陆文强2013年7月10日”，协议书下方附收条内容：“收到陆文强房款278000元正，计贰拾柒万捌仟元正吴秀春”。原、被告双方签订协议时，被告提供给原告16-2-1303、15-2-403两套房产的空白的商品房买卖合同及两套房产房款的收据。两份商品房买卖合同尾部出卖人处加盖着<a href=\"https://www.qichacha.com/firm_5d9ac25002c2012ed84bc60c81b29d0d.html\" target=\"_blank\">唐山凤辉房地产开发有限公司</a>公章及法定代表人梅子杰印章。两份收据加盖着<a href=\"https://www.qichacha.com/firm_5d9ac25002c2012ed84bc60c81b29d0d.html\" target=\"_blank\">唐山凤辉房地产开发有限公司</a>售楼处的印章，收据上的时间为2010年8月15日。被告还提供给原告一份证明复印件，证明内容为：“我公司欠吴秀春钢材款，用遵化玫瑰园住宅楼抵顶。16#-1-2001、16#-2-1303、15#-1-403。特此证明赤建集团2013年4月11日[加盖<a href=\"https://www.qichacha.com/firm_3ee2ab6087cf77221052941bed058c1a.html\" target=\"_blank\">赤峰建设建筑（集团）有限责任公司</a>西二里项目部公章]”。2013年，<a href=\"https://www.qichacha.com/firm_3ee2ab6087cf77221052941bed058c1a.html\" target=\"_blank\">赤峰建设建筑（集团）有限责任公司</a>诉<a href=\"https://www.qichacha.com/firm_5d9ac25002c2012ed84bc60c81b29d0d.html\" target=\"_blank\">唐山凤辉房地产开发有限公司</a>建设工程合同纠纷一案中，<a href=\"https://www.qichacha.com/firm_3ee2ab6087cf77221052941bed058c1a.html\" target=\"_blank\">赤峰建设建筑（集团）有限责任公司</a>提出财产保全申请，2013年7月30日，河北省高级人民法院作出（2013）冀民一初字第9号民事裁定书，将<a href=\"https://www.qichacha.com/firm_5d9ac25002c2012ed84bc60c81b29d0d.html\" target=\"_blank\">唐山凤辉房地产开发有限公司</a>计76614173.5元人民币的房产、财产及银行存款预以查封，并附查封财产清单，查封了<a href=\"https://www.qichacha.com/firm_5d9ac25002c2012ed84bc60c81b29d0d.html\" target=\"_blank\">唐山凤辉房地产开发有限公司</a>名下的133套住宅及52套商业楼。2015年5月19日，河北省高级人民法院对该133套住宅及52套商业楼继续查封，查封至2018年7月30日止。被告称，其将本案所涉两套房产卖给被告时，该两套房产并未被法院查封，在2014年9月25日河北省高级人民法院依据（2013）冀民一初字第9-1号民事裁定书又查封了<a href=\"https://www.qichacha.com/firm_5d9ac25002c2012ed84bc60c81b29d0d.html\" target=\"_blank\">唐山凤辉房地产开发有限公司</a>名下位于唐山××玫瑰园小区××套住宅，在此次查封中才有本案所涉两套房产。 </div> <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  一审法院认为，本案争议焦点为原、被告双方于2013年4月27日、2013年7月10日签订的房屋买卖协议应否予以解除，被告应否返还购房款606000元。关于本案所涉两套房产原、被告双方签订了买卖协议，被告已收原告支付的购房款共计606000元，该两套房产尚没有取得房屋产权证书，仍在<a href=\"https://www.qichacha.com/firm_5d9ac25002c2012ed84bc60c81b29d0d.html\" target=\"_blank\">唐山凤辉房地产开发有限公司</a>名下，赤建在没有完全取得房屋产权的情况下将该两套房产顶账给被告，被告又将其出卖给原告，原告至今尚未取得该两套房产的钥匙及办理入住手续，原、被告双方对上述事实均无异议，本院予以确认。原告主张，双方签订协议后至今被告无法交付房屋已达三年之久，且该两套房屋已被河北省高级人民法院查封，致使原告的购房目的至今无法实现，要求解除房屋买卖协议并要求被告返还购房款。而被告主张，其出卖给原告房产时，该两套房产并未被查封，故不同意解除房屋买卖协议及返还购房款。因唐山凤辉房产地开发有限公司欠赤建工程款，虽然被告及证人称唐山凤辉房产地开发有限公司将包括本案所涉两套房产在内的344套房产顶账给赤建，但赤建并未取得所顶账房产的合法的所有权，在<a href=\"https://www.qichacha.com/firm_3ee2ab6087cf77221052941bed058c1a.html\" target=\"_blank\">赤峰建设建筑（集团）有限责任公司</a>诉<a href=\"https://www.qichacha.com/firm_5d9ac25002c2012ed84bc60c81b29d0d.html\" target=\"_blank\">唐山凤辉房地产开发有限公司</a>建设工程合同纠纷一案中河北省高级人民法院查封了所顶账房产，在赤建没有取得房产所有权的情况下，赤建与被告达成协议，将该两套房产顶账给被告，至此被告亦没有取得合法的房屋所有权，而被告在没有取得房产所有权的情况下又将该两套房产出卖给原告，致使房产所有权不能发生转移，原告至今无法取得房产所有权，故原告要求解除双方签订的房屋买卖协议，依法有据，本院予以支持，协议解除后被告理应返还原告两套房产的购房款606000元。原、被告双方签订的房屋买卖协议并未约定房产交付时间，故原告要求被告自2013年7月10日起按照中国人民银行贷款利率支付利息，依法无据，本院不予支持，但可自起诉之日起，参照最高人民法院《关于审理民间借贷案件适用法律若干问题的规定》第二十九条第二款第（一）项规定的年利率6%计算逾期利息。为维护当事人的合法权益，遂判决：一、解除原、被告双方于2013年4月27日、2013年7月10日签订的关于买卖位于遵化市玫瑰园小区××单元××室、××单元××室房产的两份协议书。二、由被告吴秀春于本判决生效后10日内返还原告陆文强购房款共计606000元，并自2016年7月11日起按年利率6%计算逾期利息。如果未按本判决指定的期间履行金钱给付义务，应当依照《中华人民共和国民事诉讼法》第二百五十三条的规定，加倍支付迟延履行期间的债务利息。案件受理费9860元，减半收取4930元，由被告吴秀春负担。 </div> <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  判后，吴秀春不服上述判决，向本院提出上诉，其主要上诉理由：双方签订协议时间为2013年4月27日和2013年7月10日，而河北省高级人民法院依据赤建申请作出《河北省高级人民法院（2013）冀民一初字第9－1号民事裁定书》公告查封时间为2014年9月25日，此时至双方签订协议已经一年多时间，被上诉人长时间未向赤建或者玟瑰园办理入住手续，应由被上诉人承担后果。请求二审法院改判驳回被上诉人的诉讼请求或发回重审。 </div> <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  被上诉人陆文强答辩称：双方签订房屋买卖合同后，上诉人并未将该两套房产实际交付给被上诉人占有使用，在实际履行过程中上述房产被查封，其中一套房产已进入执行拍卖程序被上诉人无法实现权属转移。一审判决认定事实清楚，适用法律正确，请求二审法院驳回上诉，维持原审判决。 </div> <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  本院二审查明的事实与一审法院查明的事实相一致。 </div>  <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  本院认为，2013年4月27日、2013年7月10日，上诉人吴秀春与被上诉人陆文强签订的房屋买卖协议，协议约定上诉人将诉争房产出售给被上诉人，协议签订后，被上诉人支付了相应购房款，但诉争房产未发生所有权变更，其所有权仍登记在<a href=\"https://www.qichacha.com/firm_5d9ac25002c2012ed84bc60c81b29d0d.html\" target=\"_blank\">唐山凤辉房地产开发有限公司</a>，2014年9月25日，河北省高级人民法院依据（2013）冀民一初字第9-1号民事裁定书查封了诉争房产，被上诉人无权实现诉争房产所有权，故一审法院解除双方签订的买卖协议，判令上诉人返还购房款并无不妥。上诉人主张双方签订买卖协议时间先于法院查封时间，其法律后果应由被上诉人承担的上诉理据不足，本院不予支持。一审法院认定事实清楚，适用法律正确，依照《中华人民共和国民事诉讼法》第一百七十条第一款第（一）项：“原判决、裁定认定事实清楚，适用法律正确的，以判决、裁定方式驳回上诉，维持原判决、裁定”之规定，判决如下： </div>  <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  驳回上诉，维持原判。 </div> <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  二审案件受理费9860元，由上诉人吴秀春负担。 </div> <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  本判决为终审判决。 </div>  <div style=\"TEXT-ALIGN: right; LINE-HEIGHT: 25pt; MARGIN: 0.5pt 72pt 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  审　判　长　　李建波 </div> <div style=\"TEXT-ALIGN: right; LINE-HEIGHT: 25pt; MARGIN: 0.5pt 72pt 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  审　判　员　　董媛媛 </div> <div style=\"TEXT-ALIGN: right; LINE-HEIGHT: 25pt; MARGIN: 0.5pt 72pt 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  代理审判员　　许永委 </div> <br> <div style=\"TEXT-ALIGN: right; LINE-HEIGHT: 25pt; MARGIN: 0.5pt 72pt 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  二〇一七年四月二十四日 </div> <div style=\"TEXT-ALIGN: right; LINE-HEIGHT: 25pt; MARGIN: 0.5pt 72pt 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  书　记　员　　赵亚征 </div>";
//        String caseNo = "（2017）冀02民终3017号";
//        String beforeCaseNo = "（2016）冀0281民初3367号";  // 	（2016）冀0281民初3367号

        String result = new ExtractConnectCaseNoV2UDF().evaluate(caseContent, caseNo, beforeCaseNo);
        System.out.println(result);
    }
}
