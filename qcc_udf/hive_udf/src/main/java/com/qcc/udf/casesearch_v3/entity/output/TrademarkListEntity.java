package com.qcc.udf.casesearch_v3.entity.output;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Data
public class TrademarkListEntity extends BaseCaseOutEntity{
    @JSONField(name = "Id")
    private String id;
    @JSONField(name = "RegNo")
    private String regNo;
    @JSONField(name = "IntCls")
    private Integer intCls;
    @JSONField(name = "Name")
    private String name;
    @JSONField(name = "AppDate")
    private Long appDate;
    @JSONField(name = "ApplicantCn")
    private String applicantCn;
    @JSONField(name = "Agent")
    private String agent;
    @JSONField(name = "Type")
    private Integer type = 3;
    @JSONField(name = "CompanyKeywords")
    private String companyKeywords;
    @JSONField(name = "IsValid")
    private Integer isValid;
    @JSONField(name = "NameAndKeyNo")
    private List<NameAndKeyNoEntity> nameAndKeyNo;
    @JSONField(name = "tmType")
    private String tmType;
    @JSONField(name = "statusCode")
    private String statusCode = "019";
    /**
     * 图片ID
     */
    @JSONField(name = "picId")
    private String picId = "";
    /**
     * 根据流程洗出的状态
     */
    @JSONField(name = "flowStatus")
    private Integer flowStatus = 1;

    @Override
    public boolean equals(Object o) {
        if (o == null ) return false;
        if (this == o) return true;
        TrademarkListEntity bean = (TrademarkListEntity) o;
        return Objects.equals(id,bean.id);
    }

    @Override
    public int hashCode() {
        int result = 1;
        result = 31 * result + id.hashCode();
        result = 31 * result + regNo.hashCode();
        result = 31 * result + picId.hashCode();
        return result;
    }
}
