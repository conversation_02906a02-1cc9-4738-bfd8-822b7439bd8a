package com.qcc.udf.overseas;

import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.overseas.constant.CompanyMessageBean;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * 业务UDF（海外企业）根据地址中的邮编信息匹配得到所在城市
 * ---------------------------------------------------------------------------------------------------------
 * add jar hdfs://ldh/data/hive/udf/qcc_udf.jar;
 * create temporary function CombineCompanyInfo as 'com.qcc.udf.overseas.CombineCompanyInfoUDF';
 * ---------------------------------------------------------------------------------------------------------
 * select CombineCompanyInfo ('oper': 0, 'countryCode': '001', 'id', 'id1', 'keyno', 'keyno1', 'employee', 'employee1');
 * 结果: "{\"oper\": 0, \"countryCode\": \"001\", \"id\": \"id1\", \"keyno\": \"keyno1\", \"employee\":\"employee1\"}"
 */
public class CombineCompanyInfoUDF extends UDF {

    public String evaluate(String... params) {
        try {
            if (params.length % 2 == 0) {
                CompanyMessageBean companyMessageBean = new CompanyMessageBean();
                JSONObject data = new JSONObject();

                for (int i = 0; i < params.length; i += 2) {
                    String key = params[i];
                    String value = StringUtils.isNotBlank(params[i+1]) ? params[i+1] : "";
                    if (StringUtils.equals(key, "oper")) {
                        companyMessageBean.setOper(Integer.parseInt(value));
                    } else if (StringUtils.equals(key, "countryCode")) {
                        companyMessageBean.setCountryCode(value);
                    } else {
                        data.put(key, value);
                    }
                }
                companyMessageBean.setData(JSONObject.toJSONString(data));
                return JSONObject.toJSONString(companyMessageBean);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static void main(String[] args) {
        String[] params = new String[]{
                "id", "z002ad990fe83f79318e3feae98b4a52",
                "name", "金森林环保科技(天津)有限公司",
                "keyno", "80a4fe545be2f270a0de763457004db2",
                "org", "0",
                "oper", "{\"KeyNo\":\"pr26fe1077d8fa0397d92b7145f03a31\",\"Org\":2,\"OperType\":1,\"HasImage\":false,\"ProvinceInfo\":\"[{\\\"P\\\":\\\"TJ\\\",\\\"C\\\":1,\\\"N\\\":\\\"金森林环保科技(天津)有限公司\\\"},{\\\"P\\\":\\\"JX\\\",\\\"C\\\":1,\\\"N\\\":\\\"江西主流竹资源科技开发有限公司\\\"}]\",\"CompanyCount\":2,\"Name\":\"Ong Chun Hou\"}",
                "stockpercent_total", "1",
                "registcapi", "500万元人民币",
                "imageurl", "https://qccdata.qichacha.com/AutoImage/80a4fe545be2f270a0de763457004db2.jpg",
                "shouldcapi", "500"
        };
        System.out.println(new CombineCompanyInfoUDF().evaluate(params));
    }
}
