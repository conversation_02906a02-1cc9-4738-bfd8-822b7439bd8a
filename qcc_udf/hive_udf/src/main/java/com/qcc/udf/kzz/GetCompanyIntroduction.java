package com.qcc.udf.kzz;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class GetCompanyIntroduction extends UDF {
//    public static void main(String[] args) {
//        String a = "公司还兼营各种无源器件、连接器、继电器、传感器等诸多偏冷门、停产、军工类集成电路。";
//        String result = evaluate(a);
//        System.out.printf(result);
//    }

    /**
     * 获取当前企业标识
     *
     * @param introduction(企业简介)
     */
    public static String evaluate(String introduction) {
        String newIntro = "";
        try {
            if (StringUtils.isBlank(introduction)) {
                return newIntro;
            }
            newIntro = introduction;
            //标点-军工内容-标点
            String FILTER_1_REGEXP = "[,，;；、]{1}((国防)?军工(行业|领域|品质|产品|工程|机械)?)[,，;；、]{1}";
            List<String> filter_1_List = RegexHelper.getGlobalRegex(FILTER_1_REGEXP, newIntro);
            if (CollectionUtils.isNotEmpty(filter_1_List)) {
                for (String item : filter_1_List) {
                    String replaceTxt = item.substring(0, item.length() - 1);
                    newIntro = newIntro.replace(replaceTxt, "");
                }
            }

            String FILTER_2_REGEXP = "(、|，|以及|和|及)((其他|地方|国防)?军工(行业|领域|品质|产品|工程)?)(等+)";
            List<String> filter_2_List = RegexHelper.getGlobalRegex(FILTER_2_REGEXP, newIntro);
            if (CollectionUtils.isNotEmpty(filter_2_List)) {
                for (String item : filter_2_List) {
                    String replaceTxt = item.replace("等", "");
                    newIntro = newIntro.replace(replaceTxt, "");
                }
            }

            String FILTER_3_REGEXP = "(包含|覆盖|涉及|参与)(其他|地方|国防)?军工(行业|领域|产品|工程)?[、,，;；]{1}";
            List<String> filter_3_List = RegexHelper.getGlobalRegex(FILTER_3_REGEXP, newIntro);
            if (CollectionUtils.isNotEmpty(filter_3_List)) {
                for (String item : filter_3_List) {
                    String replaceTxt = item.replaceAll("^(包含|覆盖|涉及|参与)", "");
                    newIntro = newIntro.replace(replaceTxt, "");
                }
            }
        } catch (Exception e) {

        }
        return newIntro;
    }
}