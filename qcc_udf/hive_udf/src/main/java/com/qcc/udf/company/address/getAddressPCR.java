package com.qcc.udf.company.address;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import org.apache.commons.collections.CollectionUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class getAddressPCR extends UDF {
    private List<addressAreaModel> addressAreaModels = getjson("area.json");
    private static String Error ="";

    public String evaluate(String address) {
        try {
            if (CollectionUtils.isEmpty(addressAreaModels)) {
                return "jsondata" + Error;
            }
            if (address != null && address.length() > 0) {
                boolean flag = false;
                String provice = null;
                String city = null;
                String county = null;
                String finalAddress = address;
                List<addressAreaModel> tempAreaModels = addressAreaModels.stream().filter(i -> finalAddress.startsWith(i.getContent().split(",")[0])).collect(Collectors.toList());

                //省
                if (!CollectionUtils.isEmpty(tempAreaModels)) {
                    provice = tempAreaModels.get(0).getContent().split(",")[0];
                    address = address.substring(provice.length());
                } else {
                    tempAreaModels = addressAreaModels;
                }

                //市
                String finalAddress2 = address;
                List<addressAreaModel> tempAreaModelsCity = tempAreaModels.stream().filter(i ->
                        finalAddress2.startsWith(i.getContent().split(",")[1])
                                || finalAddress2.startsWith(i.getCity())
                                || i.getTitle().stream().anyMatch(j -> finalAddress2.startsWith(j))).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(tempAreaModelsCity)) {
                    provice = tempAreaModelsCity.get(0).getContent().split(",")[0];
                    city = tempAreaModelsCity.get(0).getContent().split(",")[1];
                } else {
                    tempAreaModelsCity = tempAreaModels;
                }
                //区
                for (addressAreaModel addressAreaModel : tempAreaModelsCity) {
                    List<com.qcc.udf.company.address.addressAreaModel.Countys> countysList = addressAreaModel.getCountys();
                    for (com.qcc.udf.company.address.addressAreaModel.Countys countys : countysList) {
                        List<String> titlelist = countys.getTitle();
                        for (String title : titlelist) {
                            if (address.contains(title)) {
                                return addressAreaModel.getContent() + "," + titlelist.get(0);
                            }
                        }
                    }
                }

                if ("北京市".equals(provice) || "天津市".equals(provice) || "重庆市".equals(provice) || "上海市".equals(provice)) {
                    city = provice;
                }
                return provice + "," + city + "," + county;
            } else {
                return null;
            }
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    private static List<addressAreaModel> getjson(String s){
        List<addressAreaModel> result=new ArrayList<>();
        BufferedReader infile;
        try {
//               infile = new BufferedReader(new InputStreamReader(new FileInputStream("F://"+s)));

            String url = "hdfs:///user/commondata/baseclean/udf_getaddress/" + s;
            Configuration conf = new Configuration();
            FileSystem fs = FileSystem.get(new URI(url), conf);
            infile = new BufferedReader(new InputStreamReader(fs.open(new Path(url))));

            String line = null;
            StringBuilder build = new StringBuilder();
            while((line=infile.readLine())!=null){
                build.append(line.replaceAll("\t|\r|\n| ", ""));
            }
            Gson gson = new GsonBuilder().create();
            JsonArray jsonArray  = gson.fromJson(build.toString(), JsonArray.class);
            for (JsonElement city :jsonArray){
                result.add(gson.fromJson(city, addressAreaModel.class));
            }
            infile.close();
        } catch (Exception e) {
            Error = e.getMessage();
        }
        return result;
    }

//    public static void main(String[] args) {
//        getAddressPCR c = new getAddressPCR();
//        String a = c.evaluate("阜新市辽宁省阜新市阜蒙县伊吗图镇庄家店村68号");
//        //System.out.println( c.GetFormatAddress("西二旗中路33号院6号楼6层006号"));
//        System.out.println(a);
//    }
}

