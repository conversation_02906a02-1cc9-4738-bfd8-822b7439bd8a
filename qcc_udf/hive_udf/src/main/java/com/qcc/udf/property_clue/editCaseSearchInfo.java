package com.qcc.udf.property_clue;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Iterator;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Auther: liulh
 * @Date: 2020/9/3 20:33
 * @Description:
 */
public class editCaseSearchInfo extends UDF {
    public static String  evaluate(String id, String info) {
        JSONArray result = new JSONArray();

        if (StringUtils.isEmpty(id) || StringUtils.isEmpty(info)){
            return "";
        }

        JSONObject infoJson = JSONObject.parseObject(info);
        JSONArray caseRoleJsonArray = infoJson.getJSONArray("CaseRole");
        if (caseRoleJsonArray != null && caseRoleJsonArray.size() > 0){
            Iterator<Object> it = caseRoleJsonArray.iterator();
            while(it.hasNext()){
                JSONObject jsonObject = (JSONObject)it.next();
                if (StringUtils.isNotEmpty(jsonObject.getString("N"))){
                    JSONObject json = new JSONObject();
                    json.put("Id", id);
                    json.put("KeyNo", jsonObject.getString("N"));
                    json.put("CompanyName", jsonObject.getString("P"));
                    json.put("Role", getCaseRoleCode(jsonObject.getString("R")));
                    json.put("LatestTrialRound", infoJson.getString("LatestTrialRound") == null ? "" : infoJson.getString("LatestTrialRound"));

                    result.add(json);
                }
            }
        }else{
            return "";
        }

        return result.toString();
    }

    public static String getCaseRoleCode(String caseRole){
        String result = "";
        if (StringUtils.isEmpty(caseRole)){
            return "";
        }

        Pattern p1 = Pattern.compile("(被执行人)|(被告)|(被申请人)|(被申请执行人)|(原审被告)|(被上诉人\\(原审被告\\))|(上诉人\\(原审被告\\))|(被告\\(反诉原告\\))|(被告人)|(上诉人\\(一审被告\\))|" +
                "(被上诉人\\(一审被告\\))|(被上诉人)|(上诉人\\(原审被告反诉原告\\))|(被告二)|(被告一)|(原告\\(被告\\))|(被申请人\\(一审被告二审被上诉人\\))|(被申请人\\(原审被告\\))|(再审申请人\\(一审被告二审上诉人\\))|" +
                "(再审申请人\\(原审被告\\))|(被申请人\\(仲裁被申请人\\))|(被申请人\\(原被执行人\\))|(再审被申请人)|(上诉人\\(原审被告原审原告\\))");
        Matcher m1 = p1.matcher(caseRole);
        if (m1.matches()) {
            result = "D";
        }

        Pattern p2 = Pattern.compile("(申请执行人)|(原告)|(申请人)|(被上诉人\\(原审原告\\))|(复议申请人)|(上诉人\\(原审原告\\))|(原告\\(反诉被告\\))|(上诉人)|(被上诉人\\(一审原告\\))|(上诉人\\(一审原告\\))|(被上诉人\\(原审原告反诉被告\\))|" +
                "(原审原告)|(再审申请人)|(被告\\(原告\\))|(被申请人\\(原审原告\\))|(附带民事诉讼原告人)|(复议申请人\\(原申请执行人\\))|(再审申请人\\(一审原告二审上诉人\\))|(再审申请人\\(原审原告\\))|(申请再审人\\(一审原告二审上诉人\\))|" +
                "(二审上诉人)|(原告人)|(附带民事诉讼原告)|(上诉人\\(原审原告原审被告\\))|(起诉人)|(申请人\\(仲裁申请人\\))|(赔偿请求人)");
        Matcher m2 = p2.matcher(caseRole);
        if (m2.matches()) {
            result = "P";
        }

        return result;
    }

    public static void main(String[] args) {
        System.out.println(evaluate("1","{\"ZxCnt\":0,\"XjpgCnt\":0,\"KtggCnt\":0,\"LastestDateType\":\"\",\"CfgsCnt\":0,\"LastestDate\":-1,\"EarliestDate\":1459440000,\"AnnoCnt\":1,\"EarliestDateType\":\"恢复执行|裁定日期\",\"CompanyKeywords\":\"42b0d019bb1df6bf2e637e376a3c0fc3,刘树森,刘沛林,平安银行股份有限公司济南分行,张爱英,深圳发展银行股份有限公司（深圳发展银行）济南分行,深圳发展银行股份有限公司济南分行,王文婷\",\"AnNoList\":\"（2015）历执恢字第154号\",\"GqdjCnt\":0,\"GroupCourt\":\"济南市历下区人民法院\",\"XgCnt\":0,\"FyggCnt\":0,\"ZbCnt\":0,\"LatestTrialRound\":\"恢复执行\",\"CfdfCnt\":0,\"CfxyCnt\":0,\"CaseName\":\"平安银行股份有限公司济南分行与张爱英、刘树森、刘沛林等借款合同纠纷\",\"SxCnt\":0,\"Province\":\"SD\",\"LianCnt\":0,\"CaseCnt\":1,\"PcczCnt\":0,\"HbcfCnt\":0,\"CaseType\":\"执行案件\",\"ProcuratorateList\":\"\",\"CaseRole\":\"[{\\\"P\\\":\\\"平安银行股份有限公司济南分行\\\",\\\"R\\\":\\\"申请执行人\\\",\\\"N\\\":\\\"42b0d019bb1df6bf2e637e376a3c0fc3\\\",\\\"O\\\":0},{\\\"P\\\":\\\"张爱英\\\",\\\"R\\\":\\\"被执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"刘树森\\\",\\\"R\\\":\\\"被执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"刘沛林\\\",\\\"R\\\":\\\"被执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"王文婷\\\",\\\"R\\\":\\\"被执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2}]\",\"CaseReason\":\"借款合同纠纷\",\"CourtList\":\"济南市历下区人民法院\",\"InfoList\":[{\"Defendant\":[{\"Role\":\"被执行人\",\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"张爱英\"},{\"Role\":\"被执行人\",\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"刘树森\"},{\"Role\":\"被执行人\",\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"刘沛林\"},{\"Role\":\"被执行人\",\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"王文婷\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"济南市历下区人民法院\",\"ZxList\":[],\"LatestTimestamp\":1459440000,\"HbcfList\":[],\"CfdfList\":[],\"CaseList\":[{\"JudgeDate\":1459440000,\"Id\":\"d376a669c128584d8249541f3984b80e0\",\"DocType\":\"执行裁定日期\",\"IsValid\":1}],\"TrialRound\":\"恢复执行\",\"Prosecutor\":[{\"Role\":\"申请执行人\",\"KeyNo\":\"42b0d019bb1df6bf2e637e376a3c0fc3\",\"Org\":0,\"Name\":\"平安银行股份有限公司济南分行\"}],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2015）历执恢字第154号\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"借款合同纠纷\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]}],\"SdggCnt\":0} "));
    }
}
