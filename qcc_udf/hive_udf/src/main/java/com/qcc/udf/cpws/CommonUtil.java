package com.qcc.udf.cpws;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class CommonUtil {

    /**
     * 拆分所有的涉诉维度记录，将没有通过验证的某条记录拆分到另一组
     * @param inputLawSuitEntity
     * @param type
     * @return
     */
    public static List<LawSuitEntity> getLawSuitEntityList(LawSuitEntity inputLawSuitEntity, String type) {
        List<LawSuitEntity> lawSuitEntityList = new ArrayList<>();
        try {
            if (inputLawSuitEntity != null && StringUtils.isNotBlank(type)) {
                if (type.equals("zx")) {
                    /**
                     * 执行案件的分组逻辑：
                     * 1 根据立案日期（精确到日）分组sx/zx/xg维度的数据，根据立案时间戳"LianTs"分组创建一个map<LianTs, LawSuitEneity>
                     * 2 将第1步中每组的汇总当事人列表，创建一个Map<当事人keyword, LawSuitEntity>
                     * 3 遍历裁判文书当事人列表，如果任一个当事人出现在keyword中，则认为该文书有关联；没有则该条文书单独存档
                     */
                    Map<Long, LawSuitEntity> lianTsWithLawSuitEntityMap = new HashMap<>();

                    for (String sxInfo : inputLawSuitEntity.getSxList()) {
                        try {
                            Long lianTs = CommonUtil.parseDateToTimeStamp(JSONObject.parseObject(sxInfo).getString("liandate"));
                            if (lianTsWithLawSuitEntityMap.get(lianTs) != null) {
                                LawSuitEntity lawSuitEntity = lianTsWithLawSuitEntityMap.get(lianTs);
                                List<String> sourceSxList = (lawSuitEntity.getSxList() != null ? lawSuitEntity.getSxList() : new ArrayList<>());
                                sourceSxList.add(sxInfo);
                                lawSuitEntity.setSxList(sourceSxList);
                                lianTsWithLawSuitEntityMap.put(lianTs, lawSuitEntity);
                            } else {
                                LawSuitEntity lawSuitEntity = new LawSuitEntity("", new ArrayList<>(Arrays.asList(sxInfo)), new ArrayList<>(), new ArrayList<>());
                                lianTsWithLawSuitEntityMap.put(lianTs, lawSuitEntity);
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }
                    for (String zxInfo : inputLawSuitEntity.getZxList()) {
                        try {
                            Long lianTs = CommonUtil.parseDateToTimeStamp(JSONObject.parseObject(zxInfo).getString("liandate"));
                            if (lianTsWithLawSuitEntityMap.get(lianTs) != null) {
                                LawSuitEntity lawSuitEntity = lianTsWithLawSuitEntityMap.get(lianTs);
                                List<String> sourceZxList = (lawSuitEntity.getZxList() != null ? lawSuitEntity.getZxList() : new ArrayList<>());
                                sourceZxList.add(zxInfo);
                                lawSuitEntity.setZxList(sourceZxList);
                                lianTsWithLawSuitEntityMap.put(lianTs, lawSuitEntity);
                            } else {
                                LawSuitEntity lawSuitEntity = new LawSuitEntity("", new ArrayList<>(), new ArrayList<>(Arrays.asList(zxInfo)), new ArrayList<>());
                                lianTsWithLawSuitEntityMap.put(lianTs, lawSuitEntity);
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }
                    for (String xgInfo : inputLawSuitEntity.getXgList()) {
                        try {
                            Long judgeDate = JSONObject.parseObject(xgInfo).getLong("judgedate");
                            Long lianTs = (judgeDate != null ? judgeDate : -1L);
                            if (lianTsWithLawSuitEntityMap.get(lianTs) != null) {
                                LawSuitEntity lawSuitEntity = lianTsWithLawSuitEntityMap.get(lianTs);
                                List<String> sourceXgList = (lawSuitEntity.getXgList() != null ? lawSuitEntity.getXgList() : new ArrayList<>());
                                sourceXgList.add(xgInfo);
                                lawSuitEntity.setXgList(sourceXgList);
                                lianTsWithLawSuitEntityMap.put(lianTs, lawSuitEntity);
                            } else {
                                LawSuitEntity lawSuitEntity = new LawSuitEntity("", new ArrayList<>(), new ArrayList<>(), new ArrayList<>(Arrays.asList(xgInfo)));
                                lianTsWithLawSuitEntityMap.put(lianTs, lawSuitEntity);
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }

                    // 将有关联的案号对应的LawSuitEntity拼接到一起
                    /**
                     * lianTsWithLawSuitEntityMap各项提取出当事人信息列表a,b,c
                     * 以a做依据，当b中至少有一个当事人在a的列表中，则将b合并到a，否则将b单独存放
                     */
                    Map<String, LawSuitEntity> keyWordWithLawSuitEntityMap = new HashMap<>();
                    if (lianTsWithLawSuitEntityMap != null && lianTsWithLawSuitEntityMap.size() > 0) {
                        for (LawSuitEntity lawSuitEntity : lianTsWithLawSuitEntityMap.values()) {
                            Set<String> mainKeySet = new HashSet<>();
                            getNameSetFromNameAndKeyNoField(lawSuitEntity.getSxList()).stream()
                                    .filter(StringUtils::isNotBlank).forEach(e -> {
                                mainKeySet.add(e);
                            });
                            getNameSetFromNameAndKeyNoField(lawSuitEntity.getZxList()).stream()
                                    .filter(StringUtils::isNotBlank).forEach(e -> {
                                mainKeySet.add(e);
                            });
                            getXgNameSetFromPersonNameAndCompanyName(lawSuitEntity.getXgList()).stream()
                                    .filter(StringUtils::isNotBlank).forEach(e -> {
                                mainKeySet.add(e);
                            });

                            String key = "";
                            for (String lawSuitKey : keyWordWithLawSuitEntityMap.keySet()) {
                                for (String name : mainKeySet) {
                                    if (lawSuitKey.contains(name)) {
                                        key = lawSuitKey;
                                    }
                                }
                            }

                            if (StringUtils.isNotBlank(key)) {
                                // 将两个lawsuitEntity对象合并
                                LawSuitEntity mainLawSuitEntity = keyWordWithLawSuitEntityMap.get(key);

                                List<String> mainSxList = mainLawSuitEntity.getSxList() != null ? mainLawSuitEntity.getSxList() : new ArrayList<>();
                                lawSuitEntity.getSxList().stream()
                                        .filter(StringUtils::isNotBlank).forEach(e -> {
                                    mainSxList.add(e);
                                });
                                List<String> mainZxList = mainLawSuitEntity.getZxList() != null ? mainLawSuitEntity.getZxList() : new ArrayList<>();
                                lawSuitEntity.getZxList().stream()
                                        .filter(StringUtils::isNotBlank).forEach(e -> {
                                    mainZxList.add(e);
                                });
                                List<String> mainXgList = mainLawSuitEntity.getXgList() != null ? mainLawSuitEntity.getXgList() : new ArrayList<>();
                                lawSuitEntity.getXgList().stream()
                                        .filter(StringUtils::isNotBlank).forEach(e -> {
                                    mainXgList.add(e);
                                });

                                mainLawSuitEntity.setSxList(mainSxList);
                                mainLawSuitEntity.setZxList(mainZxList);
                                mainLawSuitEntity.setXgList(mainXgList);
                                keyWordWithLawSuitEntityMap.put(key, mainLawSuitEntity);
                            } else {
                                String nameKeyWord = StringUtils.join(mainKeySet, ",");
                                keyWordWithLawSuitEntityMap.put(nameKeyWord, lawSuitEntity);
                            }
                        }
                    }

                    List<String> caseList = inputLawSuitEntity.getCaseList();
                    for (String caseItem : caseList) {
                        try {
                            Set<String> caseNameSet = getNameSetFromNameAndKeyNoField(new ArrayList<>(Arrays.asList(caseItem)));
                            String key = "";
                            for (String lawSuitKey : keyWordWithLawSuitEntityMap.keySet()) {
                                for (String caseName : caseNameSet) {
                                    if (lawSuitKey.contains(caseName)) {
                                        key = lawSuitKey;
                                    }
                                }
                            }

                            if (StringUtils.isNotBlank(key)) {
                                LawSuitEntity lawSuitEntity = keyWordWithLawSuitEntityMap.get(key);
                                List<String> sourceCaseList = (lawSuitEntity.getCaseList() != null ? lawSuitEntity.getCaseList() : new ArrayList<>());
                                sourceCaseList.add(caseItem);
                                lawSuitEntity.setCaseList(sourceCaseList);
                                keyWordWithLawSuitEntityMap.put(key, lawSuitEntity);
                            } else {
                                String newKeyWord = StringUtils.join(caseNameSet, ",");
                                LawSuitEntity lawSuitEntity = new LawSuitEntity(newKeyWord, new ArrayList<>(Arrays.asList(caseItem)));
                                keyWordWithLawSuitEntityMap.put(newKeyWord, lawSuitEntity);
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }

                    // 遍历map结果，增加法院名称字段信息，汇总至list中
                    for (LawSuitEntity lawSuitEntity : keyWordWithLawSuitEntityMap.values()) {
                        String court = getFirstCourtNameFromLawSuitEntity(lawSuitEntity);
                        lawSuitEntity.setCourt(court);
                        lawSuitEntityList.add(lawSuitEntity);
                    }
                } else if (type.equals("ms")) {
                    /**
                     * 民事案件的分组逻辑：
                     * 1 假定ktgg/fygg关联是正确的，提取四个维度下的所有当事人组成列表list
                     * 2 遍历裁判文书当事人列表，如果任一个当事人出现在keyword中，则认为该文书有关联；没有则该条文书单独存放
                     */
                    List<String> caseList = inputLawSuitEntity.getCaseList();

                    List<String> lianList = inputLawSuitEntity.getLianList();
                    List<String> ktggList = inputLawSuitEntity.getKtggList();
                    List<String> sdggList = inputLawSuitEntity.getSdggList();
                    List<String> fyggList = inputLawSuitEntity.getFyggList();

                    Set<String> mainKeySet = new HashSet<>();
                    getNameSetFromNameAndKeyNoField(ktggList).stream()
                            .filter(StringUtils::isNotBlank).forEach(e -> {
                        mainKeySet.add(e);
                    });
                    getNameSetFromNameAndKeyNoField(fyggList).stream()
                            .filter(StringUtils::isNotBlank).forEach(e -> {
                        mainKeySet.add(e);
                    });

                    // 主关联对象
                    String keyword = StringUtils.join(mainKeySet, ",");
                    LawSuitEntity mainLawSuitEntity = new LawSuitEntity(keyword, lianList, ktggList, sdggList, fyggList);

                    Map<String, LawSuitEntity> lawSuitEntityMap = new HashMap<>();
                    lawSuitEntityMap.put(keyword, mainLawSuitEntity);
                    for (String caseItem : caseList) {
                        try {
                            Set<String> caseNameSet = getNameSetFromNameAndKeyNoField(new ArrayList<>(Arrays.asList(caseItem)));
                            String key = "";
                            for (String lawSuitKey : lawSuitEntityMap.keySet()) {
                                for (String caseName : caseNameSet) {
                                    if (lawSuitKey.contains(caseName)) {
                                        key = lawSuitKey;
                                    }
                                }
                            }

                            if (StringUtils.isNotBlank(key)) {
                                LawSuitEntity lawSuitEntity = lawSuitEntityMap.get(key);
                                List<String> sourceCaseList = (lawSuitEntity.getCaseList() != null ? lawSuitEntity.getCaseList() : new ArrayList<>());
                                sourceCaseList.add(caseItem);
                                lawSuitEntity.setCaseList(sourceCaseList);
                                lawSuitEntityMap.put(key, lawSuitEntity);
                            } else {
                                String newKeyWord = StringUtils.join(caseNameSet, ",");
                                LawSuitEntity lawSuitEntity = new LawSuitEntity(newKeyWord, new ArrayList<>(Arrays.asList(caseItem)));
                                lawSuitEntityMap.put(newKeyWord, lawSuitEntity);
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }

                    // 遍历map结果，增加法院名称字段信息，汇总至list中
                    for (LawSuitEntity lawSuitEntity : lawSuitEntityMap.values()) {
                        String court = getFirstCourtNameFromLawSuitEntity(lawSuitEntity);
                        lawSuitEntity.setCourt(court);
                        lawSuitEntityList.add(lawSuitEntity);
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return lawSuitEntityList;
    }

    /**
     * 从原被告字段中提取并返回涉案主体信息（x1、x2、x3，顿号分隔，最多取三个，超过三个则加上"等"尾缀）
     * @param jsonArray
     * @return
     */
    private static String getParticipants(JSONArray jsonArray) {
        Set<String> top3Names = jsonArray.stream()
                .map(e -> {
                    try {
                        return (JSONObject) e;
                    } catch (Exception ex) {
                        return null;
                    }
                })
                .filter(e -> e != null && e.containsKey("Name"))
                .map(e -> e.getString("Name"))
                .filter(StringUtils::isNotBlank)
                .limit(3)
                .collect(Collectors.toSet());
        if (top3Names != null && top3Names.size() > 0) {
            if (top3Names.size() == 3) {
                return StringUtils.join(top3Names, "、") + "等";
            } else {
                return StringUtils.join(top3Names, "、");
            }
        } else {
            return "";
        }
    }

    /**
     * 从infoList中获取LastestTimeStamp最前一项的案由
     * @param infoList
     * @return
     */
    public static String getCaseReasonFromInfoList(JSONArray infoList) {
        String caseReason = "";
        try {
            List<JSONObject> jsonObjectList = infoList.stream()
                    .map(e -> {
                        try {
                            return (JSONObject) e;
                        } catch (Exception ex) {
                            return null;
                        }
                    })
                    .filter(e -> e != null && e.containsKey("LatestTimestamp"))
                    .sorted(Comparator.comparingLong(e -> e.getLong("LatestTimestamp")))
                    .collect(Collectors.toList());
            if (jsonObjectList != null && jsonObjectList.size() > 0) {
                JSONObject jsonObject = jsonObjectList.get(0);
                caseReason = jsonObject.getString("CaseReason");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return caseReason;
    }

    /**
     * 从infoList中获取LastestTimeStamp最前一项的原被告信息及案由，拼接为CaseName
     * @param infoList
     * @param caseType
     * @return
     */
    public static String getCaseNameFromInfoList(JSONArray infoList, String caseType) {
        try {
            List<JSONObject> jsonObjectList = infoList.stream()
                    .map(e -> {
                        try {
                            return (JSONObject) e;
                        } catch (Exception ex) {
                            return null;
                        }
                    })
                    .filter(e -> e != null && e.containsKey("LatestTimestamp"))
                    .sorted(Comparator.comparingLong(e -> e.getLong("LatestTimestamp")))
                    .collect(Collectors.toList());
            if (jsonObjectList != null && jsonObjectList.size() > 0) {
                JSONObject jsonObject = jsonObjectList.get(0);  // Prosecutor Defendant  CaseReason

                JSONArray prosecutorJsonArray =
                        jsonObject.getJSONArray("Prosecutor") != null ? jsonObject.getJSONArray("Prosecutor") : new JSONArray();
                JSONArray defendantJsonArray =
                        jsonObject.getJSONArray("Defendant") != null ? jsonObject.getJSONArray("Defendant") : new JSONArray();
                String caseReason =
                        jsonObject.getString("CaseReason") != null ? jsonObject.getString("CaseReason") : "";
                /**
                 * CaseName拼接规则
                 * 民事案件
                 *     原被告+案由（或"民事案件"）
                 *
                 * 执行案件
                 *     1 原被告均存在时
                 *       -> 原被告+案由（或"执行案件"）
                 *     2 原被告一方不存在时
                 *       2.1 仅有申请执行人（原告信息） -> （申请执行人姓名）+“申请执行案件”
                 *       2.2 仅有被执行人（被告信息）-> （被申请执行人姓名）+“被申请执行案件”
                 */
                if (caseType.equals("ms") ||
                        (caseType.equals("zx") && prosecutorJsonArray.size() > 0 && defendantJsonArray.size() > 0)) {
                    String prosecutor = CommonUtil.getParticipants(prosecutorJsonArray);
                    String defendant = CommonUtil.getParticipants(defendantJsonArray);

                    String prefix = "";
                    if (StringUtils.isNotBlank(prosecutor) && StringUtils.isNotBlank(defendant)) {
                        prefix = prosecutor + "与" + defendant;
                    } else if (StringUtils.isBlank(prosecutor) && StringUtils.isBlank(defendant)) {
                        prefix = "";
                    } else {
                        prefix = prosecutor + defendant;
                    }
                    String caseTypeValue = caseType.equals("ms") ? "民事案件" : "执行案件";
                    String suffix = (StringUtils.isNotBlank(caseReason) ? caseReason : caseTypeValue);
                    return prefix + suffix;
                } else {
                    if (prosecutorJsonArray.size() > 0 && defendantJsonArray.size() == 0) {
                        String prosecutor = CommonUtil.getParticipants(prosecutorJsonArray);
                        String suffix = (StringUtils.isNotBlank(caseReason) ? caseReason : "申请执行案件");
                        return prosecutor + suffix;
                    } else if (defendantJsonArray.size() > 0 && prosecutorJsonArray.size() == 0) {
                        String defendant = CommonUtil.getParticipants(defendantJsonArray);
                        String suffix = (StringUtils.isNotBlank(caseReason) ? caseReason : "被申请执行案件");
                        return defendant + suffix;
                    } else {
                        return "";
                    }
                }
            } else {
                return "";
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return "";
    }

    /**
     * 提取infoList每一项最晚一个审判程序节点的时间戳（不存在时默认为-1）
     * @param jsonObject
     * @return
     */
    public static Long getLatestTimestampFromInfoListItem(JSONObject jsonObject) {
        Long latestTimestamp = -1L;

        List<String> fieldNameList = Arrays.asList(
                "LianList", "FyggList", "KtggList", "SdggList", "CaseList", "SxList", "ZxList", "XgList"
        );
        try {
            for (String fieldName : fieldNameList) {
                JSONArray jsonArray = jsonObject.getJSONArray(fieldName);
                Iterator iterator = jsonArray.iterator();
                while (iterator.hasNext()) {
                    JSONObject itemJson = (JSONObject) iterator.next();
                    latestTimestamp = compareTimestamp(itemJson.getLong("LianDate"), latestTimestamp);
                    latestTimestamp = compareTimestamp(itemJson.getLong("OpenDate"), latestTimestamp);
                    latestTimestamp = compareTimestamp(itemJson.getLong("PublishDate"), latestTimestamp);
                    latestTimestamp = compareTimestamp(itemJson.getLong("JudgeDate"), latestTimestamp);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return latestTimestamp;
    }

    /**
     * 按照LatestTimestamp的递增顺序排列并提取合并指定字段的值（做去重处理）
     * @param infoList
     * @param fieldName
     * @return
     */
    public static String getKeywordsFromInfoList(JSONArray infoList, String fieldName) {
        List<String> keyWordList = new ArrayList<>();
        try {
            List<JSONObject> jsonObjectList = infoList.stream()
                    .map(e -> {
                        try {
                            return (JSONObject) e;
                        } catch (Exception ex) {
                            return null;
                        }
                    })
                    .filter(e -> e != null && e.containsKey("LatestTimestamp"))
                    .sorted(Comparator.comparingLong(e -> e.getLong("LatestTimestamp")))
                    .collect(Collectors.toList());
            if (jsonObjectList != null && jsonObjectList.size() > 0 && StringUtils.isNotBlank(fieldName)) {
                for (JSONObject jsonObj : jsonObjectList) {
                    if (jsonObj != null && jsonObj.containsKey(fieldName)) {
                        String value = jsonObj.getString(fieldName);
                        if (StringUtils.isNotBlank(value) && !keyWordList.contains(value)) {
                            keyWordList.add(value);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return StringUtils.join(keyWordList, ",");
    }

//    /**
//     * 提取infoList每一项的Court值，汇总为法院名称列表信息
//     * @param infoList
//     * @return
//     */
//    public static String getCourtsFromInfoList(JSONArray infoList) {
//        List<String> courtList = new ArrayList<>();
//        try {
//            List<JSONObject> jsonObjectList = infoList.stream()
//                    .map(e -> {
//                        try {
//                            return (JSONObject) e;
//                        } catch (Exception ex) {
//                            return null;
//                        }
//                    })
//                    .filter(e -> e != null && e.containsKey("LatestTimestamp"))
//                    .sorted(Comparator.comparingLong(e -> e.getLong("LatestTimestamp")))
//                    .collect(Collectors.toList());
//            if (jsonObjectList != null && jsonObjectList.size() > 0) {
//                for (JSONObject jsonObj : jsonObjectList) {
//                    String court = jsonObj.getString("Court");
//                    if (StringUtils.isNotBlank(court)) {
//                        courtList.add(court);
//                    }
//                }
//            }
//        } catch (Exception ex) {
//            ex.printStackTrace();
//        }
//        return StringUtils.join(courtList, ",");
//    }

//    /**
//     * 提取infoList每一项的Procuratorate值，汇总为检察院名称列表信息
//     * @param infoList
//     * @return
//     */
//    public static String getProcuratoratesFromInfoList(JSONArray infoList) {
//        List<String>procuratorateList = new ArrayList<>();
//        try {
//            List<JSONObject> jsonObjectList = infoList.stream()
//                    .map(e -> {
//                        try {
//                            return (JSONObject) e;
//                        } catch (Exception ex) {
//                            return null;
//                        }
//                    })
//                    .filter(e -> e != null && e.containsKey("LatestTimestamp"))
//                    .sorted(Comparator.comparingLong(e -> e.getLong("LatestTimestamp")))
//                    .collect(Collectors.toList());
//            if (jsonObjectList != null && jsonObjectList.size() > 0) {
//                for (JSONObject jsonObj : jsonObjectList) {
//                    String procuratorate = jsonObj.getString("Procuratorate");
//                    if (StringUtils.isNotBlank(procuratorate)) {
//                        procuratorateList.add(procuratorate);
//                    }
//                }
//            }
//        } catch (Exception ex) {
//            ex.printStackTrace();
//        }
//        return StringUtils.join(procuratorateList, ",");
//    }

    /**
     * 从searchWordSet中汇总所有的搜索关键词信息
     * @param searchWordSet
     * @return
     */
    public static String getCompanyKeywordsFromSearchWordSet(Set<String> searchWordSet) {
        List<String> companyKeyWords = searchWordSet.stream()
                .filter(StringUtils::isNotBlank)
                .filter(e -> e.length() > 4 || (e.length() <= 4 && !e.contains("某")))
                .sorted().collect(Collectors.toList());
        if (companyKeyWords != null && companyKeyWords.size() > 0) {
            return StringUtils.join(companyKeyWords, ",");
        } else {
            return "";
        }
    }

    /**
     * 汇总所有省份编码信息
     * @param itemLists
     * @return
     */
    public static Set<String> collectProvinceCode(List<String>... itemLists) {
        Set<String> provinceCodeSet = new HashSet<>();
        for (List<String> itemList : itemLists) {
            for (String item : itemList) {
                try {
                    JSONObject jsonObject = JSONObject.parseObject(item);
                    if (jsonObject.containsKey("province")) {
                        provinceCodeSet.add(jsonObject.getString("province"));
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        }
        return provinceCodeSet;
    }

    public static Boolean isKeyword(String str) {
        String regex = "^[a-zA-Z0-9]+$";
        Pattern pattern = Pattern.compile(regex);
        Matcher match = pattern.matcher(str);
        return match.matches();
    }

    public static JSONObject getJsonObject(Object obj){
        /*String content = obj.toString().replace("\\\"","\"").replace("\\\\\"","\"").replace("\"[","[").replace("]\"","]");
        System.out.println(content);*/
        return JSONObject.parseObject(obj.toString());
    }


    /**
     * 将日期字符串转换为时间戳（精度到秒）
     * @param inputDate
     * @return
     */
    public static Long parseDateToTimeStamp(String inputDate) {
        Long timestamp = -1L;
        try {
            if (StringUtils.isNotBlank(inputDate)) {

                try {
                    if (inputDate.contains("T")) {
                        DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
                        timestamp = df.parse(inputDate).getTime() / 1000;
                    }
                } catch (Exception ex) {
                }

                if (timestamp == -1L) {
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    timestamp = format.parse(inputDate).getTime() / 1000;
                }
            }
        } catch (Exception e) {
        }
        return timestamp;
    }

    public static String full2Half(Object input) {
        if(input == null || "".equals(input)){
            return "";
        }
        char c[] = input.toString().toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == '\u3000') {
                c[i] = ' ';
            } else if (c[i] > '\uFF00' && c[i] < '\uFF5F') {
                c[i] = (char) (c[i] - 65248);

            }
        }
        String returnString = new String(c);

        return returnString;
    }

    /**
     * 返回按照审判日期排序的裁判文书列表
     * @param caseList
     * @return
     */
    public static List<JSONObject> getSortedCaseList(List<String> caseList, String provinceCode) {
        List<JSONObject> jsonObjectList = new ArrayList<>();
        if (caseList != null && caseList.size() > 0) {
            jsonObjectList =  caseList.stream()
                    .map(e -> {
                        try {
                            return JSONObject.parseObject(e);
                        } catch (Exception ex) {
                            return null;
                        }
                    })
                    .filter(e -> e != null)
                    .filter(e -> e.getString("province").equals(provinceCode))
                    .sorted(Comparator.comparing(e -> {
                        String judgeDate = e.getString("judgedate");
                        return StringUtils.isBlank(judgeDate) ? "" : judgeDate;
                    }))
                    .collect(Collectors.toList());
        }
        return jsonObjectList;
    }

    /**
     * 返回列表中按照审判日期排列的最早的一个文书的案件身份，若为空或"[]"则取下一个
     * @param caseList
     * @param provinceCode
     * @return
     */
    public static String getCaseRoleFromCaseList(List<String> caseList, String provinceCode) {
        String caseRole = new JSONArray().toJSONString();
        try {
            List<JSONObject> sortedCaseList = getSortedCaseList(caseList, provinceCode);
            if (sortedCaseList.size() > 0) {
                for (JSONObject caseJsonObj : sortedCaseList) {
                    String role = caseJsonObj.getString("caserole");
                    if (StringUtils.isNotBlank(role) && !role.equals("[]")) {
                        caseRole = role;
                        break;
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return caseRole;
    }


    /**
     * 从立案或开庭公告列表中提取并拼装案件身份，若为空或"[]"则取下一个
     * @param lianOrKtggList
     * @param type  1 -> 开庭公告 2-> 开庭公告
     * @return
     */
    public static String getCaseRoleFromLianOrKtggList(List<String> lianOrKtggList, int type) {
        String caseRole = new JSONArray().toJSONString();
        try {
            if (lianOrKtggList != null && lianOrKtggList.size() > 0) {
                for (String lianOrKtggItem : lianOrKtggList) {
                    JSONObject lianOrKtggJsonObj = JSONObject.parseObject(lianOrKtggItem);
                    JSONArray prosecutorArray = lianOrKtggJsonObj.getJSONArray("prosecutorlistos");
                    JSONArray defendantArray = lianOrKtggJsonObj.getJSONArray("defendantlistos");
                    if (prosecutorArray != null && defendantArray != null && prosecutorArray.size() > 0 && defendantArray.size()>0) {
                        JSONArray caseRoleArray = new JSONArray();

                        /**
                         * [{\\\"KeyNo\\\":\\\"5f0587b86d2f67ec6b9f3fc0e6c43673\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"深圳市城市歌声娱乐有限公司\\\"}]
                         */
                        for (int i = 0; i < prosecutorArray.size(); i++) {
                            JSONObject jsonObject = prosecutorArray.getJSONObject(i);
                            if (jsonObject != null) {
                                JSONObject caseRoleJson = new JSONObject();
                                if (type == 1) {
                                    caseRoleJson.put("P", jsonObject.getString("Name"));
                                    caseRoleJson.put("N", jsonObject.getString("KeyNo"));
                                    caseRoleJson.put("R", "原告");
                                    caseRoleJson.put("O", jsonObject.getInteger("Org"));
                                } else if (type == 2){
                                    caseRoleJson.put("P", jsonObject.getString("name"));
                                    caseRoleJson.put("N", jsonObject.getString("keyno"));
                                    caseRoleJson.put("R", "原告");
                                    caseRoleJson.put("O", jsonObject.getInteger("Org"));
                                }
                                caseRoleArray.add(caseRoleJson);
                            }
                        }

                        for (int i = 0; i < defendantArray.size(); i++) {
                            JSONObject jsonObject = defendantArray.getJSONObject(i);
                            if (jsonObject != null) {
                                JSONObject caseRoleJson = new JSONObject();
                                if (type == 1) {
                                    caseRoleJson.put("P", jsonObject.getString("Name"));
                                    caseRoleJson.put("N", jsonObject.getString("KeyNo"));
                                    caseRoleJson.put("R", "被告");
                                    caseRoleJson.put("O", jsonObject.getInteger("Org"));
                                } else if (type == 2) {
                                    caseRoleJson.put("P", jsonObject.getString("name"));
                                    caseRoleJson.put("N", jsonObject.getString("keyno"));
                                    caseRoleJson.put("R", "被告");
                                    caseRoleJson.put("O", jsonObject.getInteger("Org"));
                                }
                                caseRoleArray.add(caseRoleJson);
                            }
                        }
                        caseRole = caseRoleArray.toJSONString();
                        break;
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return caseRole;
    }

    /**
     * 从infoList中获取并返回LatestTimestamp最后的一个案号对应的审理程序
     * @param infoList
     * @return
     */
    public static String getLatestTrialRoundFromInfoList(JSONArray infoList) {
        String caseNo = "";
        Long latestTimeStamp = -1L;

        Iterator iterator = infoList.iterator();
        while (iterator.hasNext()) {
            try {
                JSONObject jsonObject = (JSONObject) iterator.next();
                Long currentTimeStamp = jsonObject.getLong("LatestTimestamp");
                if (currentTimeStamp >= latestTimeStamp) {
                    latestTimeStamp = currentTimeStamp;
                    caseNo = jsonObject.getString("AnNo");
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return new ExtractCaseTrialRoundUDF().evaluate(caseNo);
    }

    /**
     * 提取诉讼双方当事人信息
     * @param input
     * @param caseRole
     * @return
     */
    public static JSONArray getLitigantJSONArray(String input, String caseRole) {
        JSONArray litigantJsonArray = new JSONArray();
        try {
            Map<String, JSONObject> nameAndCaseRoleMap = new HashMap<>();
            JSONArray jsonArray = JSONArray.parseArray(caseRole);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject caseRoleJsonObj = jsonArray.getJSONObject(i);
                /**
                 * [{"P":"宁波东方电缆股份有限公司","R":"原告","N":"002f193a1039940a373750a3e1d3f994","O":0},
                 * {"P":"浙江凡心律师事务所","R":"代理律师事务所","N":"w244606064394cf429568b0d80139e80","O":4}]
                 */
                String name = CommonUtil.full2Half(caseRoleJsonObj.getString("P"));

                JSONObject jsonObject = new JSONObject();
                jsonObject.put("Name", name);
                jsonObject.put("Role", CommonUtil.full2Half(caseRoleJsonObj.getString("R")));
                jsonObject.put("KeyNo", caseRoleJsonObj.getString("N"));
                jsonObject.put("Org", caseRoleJsonObj.getInteger("O"));
                nameAndCaseRoleMap.put(name, jsonObject);
            }

            if (StringUtils.isNotBlank(input)) {
                for (String item : input.split(",")) {
                    if (!CommonUtil.isKeyword(item)) {
                        JSONObject jsonObj = nameAndCaseRoleMap.get(CommonUtil.full2Half(item));
                        if (jsonObj != null) {
                            litigantJsonArray.add(jsonObj);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return litigantJsonArray;
    }

    /**
     * 根据KeyNo获取Org
     * 当keyNo为空时，根据name长度做二次判断：
     * 1 长度 > 4时，返回-1
     * 2 长度 <= 4时，返回-2
     * @param keyNo
     * @param name
     * @return
     */
    public static int getOrgByKeyNo(String keyNo, String name) {
        if (StringUtils.isBlank(keyNo)) {
            return (name.length() > 4 ? -1 : -2);
        }

        int org = 0;
        if (keyNo.startsWith("s")) {
            org = 1;
        } else if (keyNo.startsWith("h")) {
            org = 3;
        } else if (keyNo.startsWith("t")) {
            org = 5;
        } else if (keyNo.startsWith("g") || keyNo.startsWith("x") || keyNo.startsWith("w") || keyNo.startsWith("j")) {
            org = 4;
        } else if (keyNo.startsWith("y")) {
            org = 7;
        } else if (keyNo.startsWith("o")) {
            org = 8;
        } else if (keyNo.startsWith("z")) {
            org = 9;
        } else if (keyNo.startsWith("p")) {
            org = 2;
        }
        return org;
    }

    /**
     * 时间节点文案信息的首字符为"|"时，剔除该字符
     * @param dataType
     * @return
     */
    public static String getDataTypeWithoutTrialRound(String dataType) {
        if (StringUtils.isNotBlank(dataType) && dataType.startsWith("|")) {
            return dataType.substring(1);
        } else {
            return dataType;
        }
    }

    /**
     * 补全infoList中缺失的涉诉维度对象信息
     * @param jsonObj
     * @return
     */
    public static JSONObject addExternalFieldToJsonStruct(JSONObject jsonObj) {
        List<String> fieldNameList = Arrays.asList(
            "LianList", "FyggList", "KtggList", "SdggList", "CaseList", "SxList", "ZxList", "XgList"
        );
        for (String fieldName : fieldNameList) {
            if (!jsonObj.containsKey(fieldName)) {
                jsonObj.put(fieldName, new JSONArray());
            }
        }
        return jsonObj;
    }

    private static Long compareTimestamp(Long compareTimestamp, Long initTimestamp) {
        if (compareTimestamp == null || compareTimestamp == -1L) {
            return initTimestamp;
        } else {
            if (compareTimestamp > initTimestamp) {
                return compareTimestamp;
            } else {
                return initTimestamp;
            }
        }
    }

    /**
     * 从nameandkeyno字段中提取name信息并返回集合
     * @param itemList
     * @return
     */
    private static Set<String> getNameSetFromNameAndKeyNoField(List<String> itemList) {
        Set<String> nameSet = new HashSet<>();
        try {
            if (itemList != null && itemList.size() > 0) {
                for (String item : itemList) {
                    JSONObject jsonObj = JSONObject.parseObject(item);
                    if (jsonObj != null && jsonObj.containsKey("nameandkeyno")) {
                        JSONArray jsonArray = jsonObj.getJSONArray("nameandkeyno");
                        if (jsonArray != null) {
                            Iterator iterator = jsonArray.iterator();
                            while (iterator.hasNext()) {
                                JSONObject nameAndKeyNoJson = (JSONObject) iterator.next();
                                String name = nameAndKeyNoJson.getString("Name");
                                if (StringUtils.isNotBlank(name)) {
                                    nameSet.add(name);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return nameSet;
    }

    /**
     * 从xg维度中的companyname和personname字段汇总name信息并返回集合
     * @param xgList
     * @return
     */
    private static Set<String> getXgNameSetFromPersonNameAndCompanyName(List<String> xgList) {
        Set<String> nameSet = new HashSet<>();
        try {
            if (xgList != null && xgList.size() > 0) {
                for (String xgItem : xgList) {
                    JSONObject xgJson = JSONObject.parseObject(xgItem);
                    if (xgJson != null) {
                        String personName = xgJson.getString("personname");
                        if (StringUtils.isNotBlank(personName)) {
                            nameSet.add(personName);
                        }

                        String companyName = xgJson.getString("companyname");
                        if (StringUtils.isNotBlank(companyName)) {
                            nameSet.add(companyName);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return nameSet;
    }

    /**
     * 从lawSuitEntity对象中汇总所有的法院信息，递增排序后返回第一个法院名。（没有则返回空）
     * @param lawSuitEntity
     * @return
     */
    private static String getFirstCourtNameFromLawSuitEntity(LawSuitEntity lawSuitEntity) {
        String courtName = "";
        try {
            Set<String> courtNameSet = new HashSet<>();
            // 开庭公告法院提取
            List<String> ktggList = lawSuitEntity.getKtggList() != null ? lawSuitEntity.getKtggList() : new ArrayList<>();
            ktggList.stream().map(e -> {
                try {
                    return JSONObject.parseObject(e).getString("executegov");
                } catch (Exception ex) {
                    return "";
                }
            }).filter(StringUtils::isNotBlank).forEach(e-> {
                courtNameSet.add(e);
            });
            // 送达公告法院提取
            List<String> sdggList = lawSuitEntity.getSdggList() != null ? lawSuitEntity.getSdggList() : new ArrayList<>();
            sdggList.stream().map(e -> {
                try {
                    return JSONObject.parseObject(e).getString("courtname");
                } catch (Exception ex) {
                    return "";
                }
            }).filter(StringUtils::isNotBlank).forEach(e -> {
                courtNameSet.add(e);
            });
            // 法院公告法院提取
            List<String> fyggList = lawSuitEntity.getFyggList() != null ? lawSuitEntity.getFyggList() : new ArrayList<>();
            fyggList.stream().map(e -> {
                try {
                    return JSONObject.parseObject(e).getString("court");
                } catch (Exception ex) {
                    return "";
                }
            }).filter(StringUtils::isNotBlank).forEach(e -> {
                courtNameSet.add(e);
            });
            // 裁判文书法院提取
            List<String> caseList = lawSuitEntity.getCaseList() != null ? lawSuitEntity.getCaseList() : new ArrayList<>();
            caseList.stream().map(e -> {
                try {
                    return JSONObject.parseObject(e).getString("court");
                } catch (Exception ex) {
                    return "";
                }
            }).filter(StringUtils::isNotBlank).forEach(e -> {
                courtNameSet.add(e);
            });
            // 失信法院提取
            List<String> sxList = lawSuitEntity.getSxList() != null ? lawSuitEntity.getSxList() : new ArrayList<>();
            sxList.stream().map(e -> {
                try {
                    return JSONObject.parseObject(e).getString("executegov");
                } catch (Exception ex) {
                    return "";
                }
            }).filter(StringUtils::isNotBlank).forEach(e -> {
                courtNameSet.add(e);
            });
            // 执行法院提取
            List<String> zxList = lawSuitEntity.getZxList() != null ? lawSuitEntity.getZxList() : new ArrayList<>();
            zxList.stream().map(e -> {
                try {
                    return JSONObject.parseObject(e).getString("executegov");
                } catch (Exception ex) {
                    return "";
                }
            }).filter(StringUtils::isNotBlank).forEach(e -> {
                courtNameSet.add(e);
            });

            if (courtNameSet != null && courtNameSet.size() > 0) {
                List<String> resCourtList = courtNameSet.stream().sorted().limit(1).collect(Collectors.toList());
                courtName = resCourtList.get(0);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return courtName;
    }

    public static void main(String[] args) {
        String inputDate = "2018-07-25 00:00:00.0";
        Long resTimeStamp = parseDateToTimeStamp(inputDate);
        System.out.println(resTimeStamp);

//        Set<String> courtSet = new HashSet<>();
//        courtSet.add("东阳市人民法院");
//        courtSet.add("广州市从化区人民法院");
//        courtSet.add("从化区人民法院");
//        String courtNames = getCourtsFromCourtSet(courtSet);
//        System.out.println(courtNames);
    }
}