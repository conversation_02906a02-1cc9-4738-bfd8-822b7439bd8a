package com.qcc.udf.casesearch_v3;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import org.apache.hadoop.hive.ql.exec.UDF;
import com.qcc.udf.casesearch_v3.entity.output.*;

import java.util.List;

public class CaseSearchV3HandleDel extends UDF {
    public static String evaluate(String groupId,String source) {
        List<LawSuitV3OutputEntity> outList = Lists.newArrayList();
        LawSuitV3OutputEntity item = new LawSuitV3OutputEntity();
        item.setGroupId(groupId);//组id
        item.setType(2);//操作类型 0-更新 1-新增  2-删除
        item.setSource(source);//来源 RT-实时计算 OT-离线任务
        outList.add(item);
        return JSON.toJSONString(outList, SerializerFeature.DisableCircularReferenceDetect);
    }


    public static void main(String[] args) {
        System.out.println(evaluate("123","OT"));
    }

}
