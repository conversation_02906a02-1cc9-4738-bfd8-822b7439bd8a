package com.qcc.udf.cpws.wjp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.court_notice.anUtils.Util;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.stream.Collectors;

public class PartyRoleUdf extends UDF {

    public static void main(String[] args) {
        System.out.println(evaluate(
                "陈孔卫,河南元恒建设集团有限公司遂平县土地整治七标段项目部,段春利,84cc638f383cd5a9444bf07efbfb06a3,河南元恒建设集团有限公司",
                "[{\"P\":\"段春利\",\"R\":\"被上诉人（原审原告）\",\"ShowName\":\"段**\",\"N\":\"\",\"O\":-2},{\"P\":\"陈孔卫\",\"R\":\"被上诉人（原审原告）\",\"ShowName\":\"陈**\",\"N\":\"\",\"O\":-2},{\"P\":\"河南元恒建设集团有限公司遂平县土地整治七标段项目部\",\"R\":\"原审被告\",\"ShowName\":\"河南元恒建设集团有限公司遂平县土地整治七标段项目部\",\"N\":\"\",\"O\":-1}]",
                "84cc638f383cd5a9444bf07efbfb06a3_23", "开庭"
        ));
    }

    public static String evaluate(String keynos, String caseRole, String partrole, String wdName) {
        String newParty = "";
        if ("裁判文书".equals(wdName)) {
            newParty = getPartyRoleDetail(keynos, caseRole);
        } else if ("开庭".equals(wdName)) {
            newParty = getPartyRole(caseRole);
        }
        if (partrole == null) {
            partrole = "";
        }

        String[] p1 = newParty.split(",");
        String[] p2 = partrole.split(",");
        Map<String, String> pr = new HashedMap();
        for (String s : p1) {
            String[] ss = s.split("_");
            if (ss.length > 1) {
                pr.put(ss[0], ss[1]);
            }
        }
        Set<String> rep = new HashSet<>();
        Map<String, String> pr2 = new HashedMap();
        for (String s : p2) {
            String[] ss = s.split("_");
            if (ss.length > 1) {
                if (pr2.containsKey(ss[0])) {
                    rep.add(ss[0]);
                }
                pr2.put(ss[0], ss[1]);
            }
        }
        Set<String> keysOnlyInMap1 = new HashSet<>(pr.keySet());
        keysOnlyInMap1.removeAll(pr2.keySet()); // map1 中有，但 map2 中没有的 key

        Set<String> keysOnlyInMap2 = new HashSet<>(pr2.keySet());
        keysOnlyInMap2.removeAll(pr.keySet()); // map2 中有，但 map1 中没有的 key

        // 找出两个 Map 中相同 key 但不同 value 的 key
        List<JSONObject> list = new ArrayList<>();
        for (String key : pr.keySet()) {
            if (pr2.containsKey(key) && !pr.get(key).equals(pr2.get(key))) {
                JSONObject object = new JSONObject();
                object.put("keyno", key);
                object.put("p1", pr.get(key));
                object.put("p2", pr2.get(key));
                list.add(object);
            }
        }
        keysOnlyInMap1.addAll(keysOnlyInMap2);
        JSONObject object = new JSONObject();
        object.put("miss", String.join(",", keysOnlyInMap1));
        object.put("diff", list);
        object.put("repl", String.join(",", rep));

        if (keysOnlyInMap1.isEmpty() && list.isEmpty() && rep.isEmpty()) {
            return null;
        }

        return object.toJSONString();
    }

    public static String getPartyRoleDetail(String keynos, String caseRole) {
        Set<String> result = new HashSet<>();
        Map<String, String> roleMap = new LinkedHashMap<>();
        if (StringUtils.isNotEmpty(caseRole)) {
            JSONArray array = JSONArray.parseArray(caseRole);
            for (Object o : array) {
                JSONObject json = (JSONObject) o;
                if (StringUtils.isNotEmpty(json.getString("N"))) {
                    roleMap.put(json.getString("N"), json.getString("R") == null ? "" : json.getString("R"));
                }
                if (StringUtils.isNotEmpty(json.getString("SupNameAndKeyNo")) && StringUtils.isNotEmpty(json.getJSONObject("SupNameAndKeyNo").getString("KeyNo"))) {
                    roleMap.put(json.getJSONObject("SupNameAndKeyNo").getString("KeyNo"), json.getString("R") == null ? "" : json.getString("R"));
                }
            }
        }

        if (StringUtils.isNotEmpty(keynos)) {
            String[] arr = keynos.split(",");
            for (String str : arr) {
                if (Util.isKeyword(str)) {
                    int code;
                    if (roleMap.containsKey(str)) {
                        String role = roleMap.get(str);
                        code = PartyRoleCodeEnum.findCode(role);

                    } else {
                        code = 99;
                    }
                    result.add(str.concat("_") + code);
                }
            }
        }


        return String.join(",", result);
    }

    private static String getPartyRole(String nameAndKeyNo) {
        List<NameKeyNoEntity> nameKeyNoEntitys = getNameKeyNoEntitys(nameAndKeyNo);
        String partyRole = nameKeyNoEntitys.stream().filter(e -> e != null).filter(e -> StringUtils.isNotBlank(e.getKeyNo()))
                .map(k -> {
                    StringBuilder sb = new StringBuilder();
                    //name与keyno拼接
                    return sb.append(k.getKeyNo()).append("_" + k.getRoleType());
                }).collect(Collectors.joining(","));

        for (NameKeyNoEntity item : nameKeyNoEntitys) {
            if (item.getSupNameAndKeyNo() != null && StringUtils.isNotEmpty(item.getSupNameAndKeyNo().getKeyNo())) {
                partyRole = partyRole.concat(",").concat(item.getSupNameAndKeyNo().getKeyNo().concat("_" + item.getRoleType()));
            }
        }

        return partyRole;
    }

    public static final String NULL_ARRAY_STRING = "[]";

    public static List<NameKeyNoEntity> getNameKeyNoEntitys(String nameAndKeyNo) {
        List<NameKeyNoEntity> nameAndKeyNoList = new ArrayList<>();
        if (StringUtils.isNotEmpty(nameAndKeyNo) && !NULL_ARRAY_STRING.equals(nameAndKeyNo)) {
            nameAndKeyNoList = JSON.parseArray(nameAndKeyNo, NameKeyNoEntity.class);
        }
        return nameAndKeyNoList;
    }
}
