package com.qcc.udf.company;

import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.HashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2020/12/31 13:43
 * @description ：从公司名和keyNo集合中获取公司名
 */
public class GetCompanyName extends UDF {

    private static Pattern pattern = Pattern.compile("^[A-Za-z0-9]{30,40}$");

    public String evaluate(String input) {
        String result = "";
        if (StringUtils.isNotEmpty(input)) {
            Set<String> names = new HashSet<>();
            String[] nameStr = input.split(",");
            for(String item : nameStr) {
                Matcher matcher = pattern.matcher(item);
                if (matcher.find()) {
                    continue;
                }
                names.add(item);
            }
            if (names.size() > 0) {
                result = names.stream().collect(Collectors.joining(","));
            }
         }
        return result;
    }

    public static void main(String[] args) {
        String test = "张元,a42b51585ab8574009fb67eb01d5116c,四川国灿博室物业管理有限公司,四川国灿博室物业服务有限公司";
        System.out.println(new GetCompanyName().evaluate(test));
    }
}
