package com.qcc.udf.casesearch_v3.entity.input;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.casesearch_v3.enums.CaseCategoryEnum;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import com.google.common.base.Strings;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: zhangqiang
 * @Date: 2020/11/11 17:54
 * @Description:送达公告
 */
@Data
public class SDGGEntity  extends BaseCaseEntity {

    private String id;
    private String anno;
    private String companynames;
    private String isvalid;
    private String courtname;
    private String provincecode;

    private long publishdate;
    private String casereason;
    private String nameandkeyno;
    private String prosecutorlist;
    private String defendantlist;
    private String recipientlist;


    private List<NameAndKeyNoEntity> prosecutorlistoEntityList;
    private List<NameAndKeyNoEntity> defendantlistoEntityList;
    private List<NameAndKeyNoEntity> nameandkeynoEntityList;
    private List<NameAndKeyNoEntity> recipientEntityList;


    public static List<SDGGEntity> convert(List<String> jsonList) {
        List<SDGGEntity> list = new ArrayList<>();
        SDGGEntity entity = null;
        if(CollectionUtils.isEmpty(jsonList)){
            return list;
        }
        for (String json : jsonList) {
            if(Strings.isNullOrEmpty(json)){
                continue;
            }
            entity = JSON.parseObject(json, SDGGEntity.class);
            if(entity == null  || Strings.isNullOrEmpty(entity.getId())){
                continue;
            }


            String str = entity.getNameandkeyno();
            Map<String,String> nameRoleMap = new HashMap<>();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setNameandkeynoEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getNameandkeynoEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                    nameRoleMap.put(namekey.getName(),namekey.getRole());
                }
            }

            str = entity.getProsecutorlist();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setProsecutorlistoEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getProsecutorlistoEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                    namekey.setRole(nameRoleMap.getOrDefault(namekey.getName(),""));
                }
            }

            str = entity.getDefendantlist();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setDefendantlistoEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getDefendantlistoEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                    namekey.setRole(nameRoleMap.getOrDefault(namekey.getName(),""));
                }
            }
            str = entity.getRecipientlist();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setRecipientEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getRecipientEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                }
            }

            //公共字段赋值
            entity.setBaseCaseNo(entity.getAnno());
            entity.setBaseCaseCategoryEnum(CaseCategoryEnum.SDGG);
            if(!Strings.isNullOrEmpty(entity.getCompanynames())){
                entity.setBaseSearchWordSet(Arrays.stream(entity.getCompanynames().split(","))
                        .collect(Collectors.toSet()));
            }

            entity.setBaseCaseReason(entity.getCasereason());
            entity.setBaseCourt(entity.getCourtname());
            entity.setBaseProvinceCode(entity.getProvincecode());
            entity.setBaseNameKeyNoList(entity.getNameandkeynoEntityList());
            entity.setBaseId(entity.getBaseCaseCategoryEnum().getType()+"_"+entity.getId());
            String caseType= CommonV3Util.getCaseType(CommonV3Util.getCaseNo(entity.getBaseCaseNo()));
            //案件类型为空的数据直接过滤
            if(Strings.isNullOrEmpty(caseType)){
                continue;
            }
            list.add(entity);
        }
        return list;
    }

    public static void main(String[] args) {
        String json = "{\"beforecaseno\":\"\",\"anno\":\"（2021）皖0223民初1498号\",\"provincecode\":\"AH\",\"courtname\":\"安徽省芜湖市南陵县人民法院\",\"isvalid\":1,\"pushdate\":1637829980,\"id\":\"d7c8f286a15313e1e621d4df8f558de1\",\"type\":9,\"prosecutorlist\":\"[]\",\"casereason\":\"其他民事\",\"publishdate\":1619712000,\"companynames\":\"\",\"nameandkeyno\":\"null\",\"defendantlist\":\"[]\"}";
        convert(Lists.newArrayList(json));
    }
}
