package com.qcc.udf.casesearch_v3.tmp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022年06月01日 10:01
 */
public class DebtRelateTmpUDF extends UDF {

    public static void main(String[] args) {
        String json="[{\"CompanyInfo\":{\"Name\":\"易航科技股份有限公司\",\"KeyNo\":\"690d2100669890715ba5194a885a0089\",\"HasImage\":true},\"RelatedType\":{\"RelatedType\":2,\"Detail\":\"5.43%\"},\"AmtInfo\":{\"DebtInfo\":{\"TotalAmount\":*********.52,\"SfajAmount\":*********.52,\"TotalCount\":13},\"HisDebtInfo\":{\"TotalAmount\":32665624,\"SfajAmount\":32665624,\"TotalCount\":67},\"RightsInfo\":{\"TotalAmount\":2430043,\"SfajAmount\":2430043,\"TotalCount\":1},\"HisRightsInfo\":{\"TotalAmount\":6549815,\"SfajAmount\":6549815,\"TotalCount\":1}}},{\"CompanyInfo\":{\"Name\":\"海南物管集团股份有限公司\",\"KeyNo\":\"f9f4174818c58d0355b5c5a2578e7e2e\",\"HasImage\":true},\"RelatedType\":{\"RelatedType\":2,\"Detail\":\"9.82%\"},\"AmtInfo\":{\"DebtInfo\":{\"TotalAmount\":3740,\"SfajAmount\":3740,\"TotalCount\":1},\"HisDebtInfo\":{\"TotalAmount\":1065022,\"SfajAmount\":1065022,\"TotalCount\":4},\"RightsInfo\":{\"TotalAmount\":2128.38,\"SfajAmount\":2128.38,\"TotalCount\":1},\"HisRightsInfo\":{\"TotalAmount\":74460,\"SfajAmount\":74460,\"TotalCount\":4}}},{\"CompanyInfo\":{\"Name\":\"国银金融租赁股份有限公司\",\"KeyNo\":\"e5af7b232d2f49bd19eb937d3f4e0318\",\"HasImage\":true},\"RelatedType\":{\"RelatedType\":2,\"Detail\":\"6.29%\"},\"AmtInfo\":{\"DebtInfo\":{\"TotalAmount\":40493652.45,\"SfajAmount\":35993652.45,\"RzAmount\":4500000,\"TotalCount\":52},\"HisDebtInfo\":{\"TotalAmount\":20906786,\"SfajAmount\":20906786,\"TotalCount\":11},\"RightsInfo\":{\"TotalAmount\":5374022348.55,\"SfajAmount\":*********.55,\"OtherAmount\":4825135400,\"TotalCount\":282},\"HisRightsInfo\":{\"TotalAmount\":6025219839.04,\"SfajAmount\":3224935455.97,\"OtherAmount\":2800284383.07,\"TotalCount\":667}}},{\"CompanyInfo\":{\"Name\":\"海南新国宾馆有限公司\",\"KeyNo\":\"15a7b10012b42f757f7da3f4b9e14ba9\",\"HasImage\":true},\"RelatedType\":{\"RelatedType\":2,\"Detail\":\"6.00%\"},\"AmtInfo\":{\"DebtInfo\":{},\"HisDebtInfo\":{\"TotalAmount\":*********,\"RzAmount\":*********,\"TotalCount\":3},\"RightsInfo\":{},\"HisRightsInfo\":{\"TotalAmount\":211170,\"SfajAmount\":211170,\"TotalCount\":1}}},{\"CompanyInfo\":{\"Name\":\"海南航空控股股份有限公司\",\"KeyNo\":\"918551a38b7bb58df883e8df0f156ed4\",\"HasImage\":true},\"RelatedType\":{\"RelatedType\":2,\"Detail\":\"1.79%\"},\"AmtInfo\":{\"DebtInfo\":{\"TotalAmount\":7946318995.26,\"SfajAmount\":48778325.5,\"RzAmount\":7895229500,\"OtherAmount\":2311169.76,\"TotalCount\":38},\"HisDebtInfo\":{\"TotalAmount\":24285361276.72,\"SfajAmount\":*********.92,\"RzAmount\":23523825021.8,\"TotalCount\":78},\"RightsInfo\":{\"TotalAmount\":643263.85,\"SfajAmount\":643263.85,\"TotalCount\":4},\"HisRightsInfo\":{\"TotalAmount\":6608425,\"SfajAmount\":6608425,\"TotalCount\":10}}},{\"CompanyInfo\":{\"Name\":\"海航实业集团有限公司\",\"KeyNo\":\"1cb995eb33b04317b86f68f6838d7add\",\"HasImage\":true},\"RelatedType\":{\"RelatedType\":2,\"Detail\":\"96.06%\"},\"AmtInfo\":{\"DebtInfo\":{\"TotalAmount\":1*********.43,\"SfajAmount\":*********.43,\"RzAmount\":1000000000,\"TotalCount\":56},\"HisDebtInfo\":{\"TotalAmount\":85197638128,\"SfajAmount\":2622529968,\"RzAmount\":82575108160,\"TotalCount\":87},\"RightsInfo\":{\"TotalAmount\":4131800,\"SfajAmount\":4131800,\"TotalCount\":1},\"HisRightsInfo\":{}}},{\"CompanyInfo\":{\"Name\":\"大新华物流控股（集团）有限公司\",\"KeyNo\":\"2041b406e1025a7ed652b8d3bb3f7449\",\"HasImage\":true},\"RelatedType\":{\"RelatedType\":2,\"Detail\":\"10.1233%\"},\"AmtInfo\":{\"DebtInfo\":{\"TotalAmount\":10193533.64,\"SfajAmount\":10193533.64,\"TotalCount\":19},\"HisDebtInfo\":{\"TotalAmount\":*********.29,\"SfajAmount\":*********.29,\"RzAmount\":*********,\"TotalCount\":27},\"RightsInfo\":{\"TotalAmount\":2389698.5,\"SfajAmount\":2389698.5,\"TotalCount\":1},\"HisRightsInfo\":{\"TotalAmount\":69000000,\"SfajAmount\":69000000,\"TotalCount\":1}}},{\"CompanyInfo\":{\"Name\":\"宁波梅山保税港区鑫鲲达投资合伙企业（有限合伙）\",\"KeyNo\":\"3e8c2a9d18864731628f1ef534f1aee9\",\"HasImage\":false},\"RelatedType\":{\"RelatedType\":2,\"Detail\":\"39.4322%\"},\"AmtInfo\":{\"DebtInfo\":{},\"HisDebtInfo\":{},\"RightsInfo\":{},\"HisRightsInfo\":{\"TotalAmount\":10000000000,\"OtherAmount\":10000000000,\"TotalCount\":6}}}]";
        System.out.println(evaluate(json));
    }

    public static int evaluate(String json) {
        JSONArray jsonArray = JSON.parseArray(json);
        int matchCount = 0;
        for (Object obj : jsonArray) {
            JSONObject jsonObject = (JSONObject) obj;
            JSONObject relateType = jsonObject.getJSONObject("RelatedType");
            String type = relateType.getString("RelatedType");
            String Detail = relateType.getString("Detail");
            if ("1".equals(type) || "2".equals(type)) {
                boolean match = matchPresent(Detail, new BigDecimal("5"));
                if (!match) {
                    matchCount++;
                }
            }
        }
        int diff = jsonArray.size()-matchCount;

        return diff;
    }

    /**
     * 判断占比是否符合指定值
     *
     * @param present
     * @return
     */
    public static boolean matchPresent(String present, BigDecimal score) {
        try {
            BigDecimal decimal = new BigDecimal(present.replace("%", ""));
            if (decimal.compareTo(score) >= 0) {
                return true;
            }

        } catch (Exception e) {
            //占比可能转换数值失败,一般不可能吧

            ;
        }
        return false;
    }

}
