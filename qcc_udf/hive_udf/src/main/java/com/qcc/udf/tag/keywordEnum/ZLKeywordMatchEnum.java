package com.qcc.udf.tag.keywordEnum;

import com.qcc.udf.tag.tagEnum.ProductQualityEnum;

/**
 * 产品质量问题-关键词
 */
public enum ZLKeywordMatchEnum {
    //ZL01
    WZSC(".*无证.*生产.*", ProductQualityEnum.ZL01),
    WZ_ZHI_ZAO(".*无证.*制造.*", ProductQualityEnum.ZL01),
    WZ_ZHI_ZUO(".*无证.*制作.*", ProductQualityEnum.ZL01),

    //ZL02
    TJWJP(".*[^(无|未)]添加.*", ProductQualityEnum.ZL02),

    //ZL03
    ZLAQSG(".*(食品|产品).*质量.*事故.*", ProductQualityEnum.ZL03),
    ZDZL_AQSG(".*\"value\":\"危害质量安全/生产安全\".*", ProductQualityEnum.ZL03),

    //ZL04
    CPZH(".*召回.*", ProductQualityEnum.ZL04),

    //ZL05
    JYBHG(".*(检验|抽检).*不合格.*", ProductQualityEnum.ZL05),
    JGBHG("结果不合格", ProductQualityEnum.ZL05),
    CPJYBHG(".*产品.*(检验|抽检).*不合格.*", ProductQualityEnum.ZL05),
    BHG(".*不合格.*", ProductQualityEnum.ZL05),
    CJJGBHG("抽检结果不合格", ProductQualityEnum.ZL05),
    YPCJ_JYBHG("抽检结果不合格|不符合规定", ProductQualityEnum.ZL05),

    //ZL06
    JMWLZL(".*[^虚](冒|伪|劣|质量).*", ProductQualityEnum.ZL06),
    JIA(".*[^虚]假.*", ProductQualityEnum.ZL06),
    JMWL(".*[^虚](假|冒|伪|劣).*", ProductQualityEnum.ZL06),

    //ZL07
    XJXC(".*虚假.*(宣传|广告).*", ProductQualityEnum.ZL07),

    //ZL99
    DEFAULT("其他", ProductQualityEnum.ZL99),
    ;


    private String keyword;
    private ProductQualityEnum productQualityEnum;

    ZLKeywordMatchEnum(String keyword, ProductQualityEnum productQualityEnum) {
        this.keyword = keyword;
        this.productQualityEnum = productQualityEnum;
    }

    public String getKeyword() {
        return keyword;
    }

    public ProductQualityEnum getProductQualityEnum() {
        return productQualityEnum;
    }

    public ProductQualityEnum getTagEnum() {
        return getProductQualityEnum();
    }
}
