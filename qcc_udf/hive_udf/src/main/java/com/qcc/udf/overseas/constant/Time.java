package com.qcc.udf.overseas.constant;

import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.util.Locale;


/**
 * 时间处理格式
 */
public interface Time {
    /**
     * 10/24/1995 类型日期
     */
    DateTimeFormatter Formatter_T1 = DateTimeFormat.forPattern("MM/dd/yyyy");
    /**
     * 2009-08-03 类型日期
     */
    DateTimeFormatter Formatter_T2 = DateTimeFormat.forPattern("yyyy-MM-dd");
    /**
     * 1-19-1990 类型日期
     */
    DateTimeFormatter Formatter_T3 = DateTimeFormat.forPattern("MM-dd-yyyy");
    /**
     * 19 Sep 1985 类型日期
     */
    DateTimeFormatter Formatter_T4 = DateTimeFormat.forPattern("dd MMM yyyy").withLocale(Locale.US);
    /**
     * Mar 15, 1995 类型日期
     */
    DateTimeFormatter Formatter_T5 = DateTimeFormat.forPattern("MMM dd, yyyy").withLocale(Locale.US);
    /**
     * 2018年6月1日 类型日期
     */
    DateTimeFormatter Formatter_T6 = DateTimeFormat.forPattern("yyyy年MM月dd日");
}
