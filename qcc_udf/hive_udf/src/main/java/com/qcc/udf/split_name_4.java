package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentLengthException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDTF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;

import java.util.ArrayList;

public class split_name_4 extends GenericUDTF {
    @Override
    public void close() throws HiveException {}

    @Override
    public StructObjectInspector initialize(ObjectInspector[] args)
            throws UDFArgumentException {
        if (args.length != 4) {
            throw new UDFArgumentLengthException("ExplodeMap takes only 4 argument");
        }

        ArrayList<String> fieldNames = new ArrayList<String>();
        ArrayList<ObjectInspector> fieldOIs = new ArrayList<ObjectInspector>();
        for(int i=0;i<4;i++){
            fieldNames.add("col"+i);
            fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        }

        return ObjectInspectorFactory.getStandardStructObjectInspector(fieldNames,fieldOIs);
    }

    @Override
    public void process(Object[] args) throws HiveException {
        String name = args[0]==null?null:args[0].toString() ;
        String args1 = args[1]==null?null:args[1].toString() ;
        String args2 = args[2]==null?null:args[2].toString() ;
        String args3 = args[3]==null?null:args[3].toString() ;
        if(name != null && name.length()>3){
            name = name.replace("、", ",").replace("。", "").replace(";", ",").replace(" ", "").replace("\t", "").replaceAll("，", ",");
            if(name.contains(",")){
                for(String tmp:name.split(",")){
                    if(tmp.length()>3){
                        String[] tmplist = {tmp,args1,args2,args3};
                        forward(tmplist);
                    }
                }
            }else{
                String[] tmplist = {name,args1,args2,args3};
                forward(tmplist);
            }
        }
    }
}
