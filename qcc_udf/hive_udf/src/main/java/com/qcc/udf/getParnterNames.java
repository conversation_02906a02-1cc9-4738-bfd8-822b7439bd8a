package com.qcc.udf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Collections;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;


public class getParnterNames extends UDF {

    public static String evaluate(String jsonStr) throws Exception {
        if (StringUtils.isBlank(jsonStr)) {
            return "";
        }
        try {
            List<String> nameList = new LinkedList<>();
            JSONArray array = JSONArray.parseArray(jsonStr);
            Iterator<Object> it = array.iterator();
            while (it.hasNext()){
                JSONObject jsonObject = (JSONObject)it.next();
                nameList.add(jsonObject.getString("StockName") == null ? "" : jsonObject.getString("StockName"));
            }
            Collections.sort(nameList);

            return String.join(",", nameList);
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    public static void main(String[] args) {
        try {
            System.out.println(evaluate("[{\"Org\":2,\"StockName\":\"房立亮\",\"HasImage\":false,\"StockPercent\":\"50.00%\",\"CompanyCount\":2,\"TotalShouldAmount\":\"50\",\"ShouldCapi\":\"50\",\"InvestType\":\"货币\",\"KeyNo\":\"p7cf0ae664652ff6e804915072e25f32\",\"ShoudDate\":\"2045-12-31\",\"IdentifyType\":\"非公示项\",\"CapiDate\":null,\"StockType\":\"自然人股东\",\"IdentifyNo\":null,\"RealCapi\":null,\"PartnerShouldDetailList\":[{\"ShoudDate\":\"2045-12-31\",\"ShouldCapi\":\"50\",\"InvestType\":\"货币\"}],\"Job\":\"监事\",\"InvestName\":null},{\"Org\":2,\"StockName\":\"张彦丽\",\"HasImage\":false,\"StockPercent\":\"50.00%\",\"CompanyCount\":3,\"TotalShouldAmount\":\"50\",\"ShouldCapi\":\"50\",\"InvestType\":\"货币\",\"KeyNo\":\"p6656e5bc5ee0f3e969d1c1c15aa6188\",\"ShoudDate\":\"2045-12-31\",\"IdentifyType\":\"非公示项\",\"CapiDate\":null,\"StockType\":\"自然人股东\",\"IdentifyNo\":null,\"RealCapi\":null,\"PartnerShouldDetailList\":[{\"ShoudDate\":\"2045-12-31\",\"ShouldCapi\":\"50\",\"InvestType\":\"货币\"}],\"Job\":\"执行董事\",\"InvestName\":null}] "));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}