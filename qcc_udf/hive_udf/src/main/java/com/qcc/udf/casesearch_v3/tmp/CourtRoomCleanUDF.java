package com.qcc.udf.casesearch_v3.tmp;

import com.qcc.udf.tax.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Auther: zhanqgiang
 * @Date: 2020/11/19 10:00
 * @Description: 案号简单清洗
 */
public class CourtRoomCleanUDF extends UDF {
    public static final String data_SPLIT_REGEX = ",|，|.|。";
    public static final String date_SPLIT_REGEX = "定于|本院.?于|本案.?于|开庭时间";
    public static final String court_room_REGEX = "开庭地点";
//    public static final String room_SPLIT_REGEX = "开庭进行审理|公开开庭审理|开庭审理|公开审理|进行证据交换";
    public static final String room_SPLIT_REGEX = "(公开)?(开庭)?(进行)?审理|进行证据交换";
    private final static String REGEXP_BIRTH_COURT_ROOM = court_room_REGEX+ "为(.*)";
    public static final Pattern REGEXP_BIRTH_COURT_ROOM_PATTERN = Pattern.compile(REGEXP_BIRTH_COURT_ROOM);
    private final static String REGEXP_BIRTH_ALL_ROOM = ".*[在](.+)(" + room_SPLIT_REGEX + ")?";
    public static final Pattern REGEXP_BIRTH_ALL_ROOM_PATTERN = Pattern.compile(REGEXP_BIRTH_ALL_ROOM);


    public static String evaluate(String content) {
        return extractDate(content);
    }


    /**
     * 从文本中提取出时间，并转换为LocalDateTime格式  yyyy年mm月dd日格式
     *
     * @param content
     * @return
     */
    public static String extractDate(String content) {
        if (StringUtils.isBlank(content)) {
            return "";
        }
        String extractContent = content;
        String[] split = extractContent.split(date_SPLIT_REGEX);
        if (split.length > 1) {
            extractContent = content.replace(split[0],"")
                    .replace("开庭地点","")
            ;


            String[] split1 = extractContent.split(room_SPLIT_REGEX);
            if (split.length > 1) {
                extractContent = split1[0];
            }
        }
        String courtRoom = getCourtRoom(extractContent,REGEXP_BIRTH_ALL_ROOM_PATTERN);
        if ((StringUtils.isBlank(courtRoom) || !courtRoom.contains("庭")) && content.contains(court_room_REGEX)){
            extractContent =content.substring(content.indexOf(court_room_REGEX));
            courtRoom = getCourtRoom(extractContent,REGEXP_BIRTH_COURT_ROOM_PATTERN);
        }

        return courtRoom;
    }

    private static String getCourtRoom(String extractContent,Pattern pattern) {
        String courtRoom = "";
        Matcher m = pattern.matcher(extractContent);
        if (m.find()) {
            courtRoom = m.group(1);
            courtRoom = courtRoom.replace("审理", "")
                    ;
        }
        if (StringUtils.isNotBlank(courtRoom)){
            courtRoom = StringUtils.splitPreserveAllTokens(courtRoom, data_SPLIT_REGEX)[0];
        }
        return courtRoom;
    }


    public static void main(String[] args) {
        String content = "东莞市金美克能源有限公司、深圳市超境界新能源有限公司：本院受理原告深圳市华美兴泰科技股份有限公司诉被告深圳市伏特云商有限公司，你们（2019）粤0309民初1291号产品责任纠纷一案，原告诉讼请求为：1、请求法院判令三被告赔偿原告损失共计1336900.19元；2、请求法院判令三被告对上述损失承担连带赔偿责任；3、本案诉讼费用由三被告承担。本案标的额为1336900.19元。因被告你们下落不明，现依法向你们公告送达起诉状副本、证据副本、应诉通知书、开庭传票、合议庭组成人员通知书等。自公告之日起经过60日，即视为送达。提出答辩状和举证的期限为公告期满后的15日内，并定于2019年6月24日下午14时30分在观澜法庭第二审判庭开庭审理，逾期将依法缺席裁判。";
//        String content = "李蒙建，男，1971年1月13日出生，汉族、住浙江省绍兴市柯桥区稽东镇高阳村谢家湾161号。本院在审理原告刘乾红诉被告李蒙建买卖合同纠纷一案，因你下落不明，不能直接送达有关诉讼法律文书，现依照《中华人民共和国民事诉讼法》的有关规定向你公告送达起诉状副本、应诉通知书、举证通知书、合议庭组成人员通知书、开庭传票、民事诉讼须知、权利义务告知书、风险提示书、上网告知书、廉政监督卡、民事裁定书。本院于2019年9月23日10时00分在本院第十四审判庭公开开庭进行审理。自公告发出之日起经过60日，即视为送达。提出答辩状的期限为公告送达期满后的15日。举证期限为公告期满后的30日内。逾期本院则将依法判决。";
        content = "(2017)粤0307民初2606号 吴波:本院受理原告深圳市赢时通汽车服务有限公司龙岗分公司诉你方车辆租赁合同纠纷一案，因你方下落不明，依照《中华人民共和国民事诉讼法》第九十二条之规定，向你公告送达起诉状副本、举证通知书、应诉通知书、开庭传票、民事裁定书、合议庭组成人员通知书、证据材料等各一份。自本公告发出之日起，经过60天，即视为送达。提交答辩状的期限为公告期满后的15日内，举证期限为公告送达期满后的30日内。开庭时间定于2017年8月14日9时30分，开庭地点为本院坪地法庭第一审判庭。逾期将依法判决。";
//        content = "肖云萍、蒋实、钜鼎（扬州）光电显示科技有限公司、深圳市钜鼎实业发现有限公司：本院在审理原告李秋月、卢志鸿诉你们民间借贷纠纷二案，现依法向你们公告送达起诉状副本、应诉通知书、举证通知书及开庭传票等诉讼文书。举证期限为公告期满后15日。本院定于2015年12月10日上午9时30分依法开庭审理此案，开庭地点在本院第1332审判庭，逾期本院将依法缺席审理和判决。 "
        content = "2021)粤0309民初12150号 开庭公告广东省深圳市龙华区人民法院公告 (2021)粤0309民初12150号深圳市梦航传媒科技有限公司:本院受理原告张阳诉被告深圳市梦航传媒科技有限公司 服务合同纠纷一案,因你下落不明,依照《中华人民共和国民事诉讼法》第九十二条的规定,现依法向你方公告送达(2021)粤0309民初12150号起诉状副本、应诉通知书、开庭传票、举证通知书、转换普通程序民事裁定书、证据副本等,自公告之日起经过六十日即视为送达。你方提出答辩状和举证的期限为公告期满后的15日内。本院定于2022年01月12日14时30分在深圳市龙华区龙观东路383号龙华人民法庭四楼第八审判庭开庭,请于开庭前十分钟在开庭地点进行证据交换。请依时出庭,逾期将依法裁判。特此公告! 二〇二一年十月二十八日 本公告于2021年10月28日刊登于深圳市中级人民法院官方网站公告单位:深圳市龙华区人民法院 法官助理:曾家馨 联系电话:0755-21051658本庭地址:深圳市龙华区龙观东路383号龙华人民法庭";
        System.out.println(extractDate(content));
    }

}
