package com.qcc.udf;


import org.apache.hadoop.hive.ql.exec.UDF;
import org.json.JSONException;

import java.io.IOException;
import java.net.URISyntaxException;


public class CheckSymmetricCharacter extends UDF {

    public static Boolean evaluate(String str) throws org.json.JSONException {
        if (str.length() == 0) {
            return false;
        }

        String param = "()、【】、《》、＜＞、﹝﹞、<>、()、[]、«»、‹›、〔〕、〈〉、{}、［］、「」、｛｝、〖〗、『』、“”、‘’、〝〞、 \" \"、''、＂＂、＇＇、＇＇、´´";

        String allParam[] = param.split("、");

        for (int i = 0; i < allParam.length; i++) {
            String paramOne=allParam[i].replace(" ","");
            String leftParam=paramOne.substring(0,1);
            String rightParam=paramOne.substring(1,2);
            if (!check(str, leftParam,rightParam)) {
                return false;
            }

        }

        return true;
    }

    public static Boolean check(String str, String leftParam, String rightParam) {
        int count = 0;

        for (int i = 0; i < str.length(); i++) {

            if (count < 0) {
                break;
            }

            String temp = str.substring(i, i + 1);

            if (temp.equals(leftParam)) {
                count++;
            }

            if (temp.equals(rightParam)) {
                count--;
            }
        }

        return count == 0;
    }

    public static void main(String[] args) throws IOException, URISyntaxException, JSONException {
        System.out.println(evaluate("鲁滨交（04）罚【2015】0512C1罚[2015] 0512C11509060001"));
    }


}