package com.qcc.udf.cpws.casesearch_v2;

import cn.hutool.core.util.ReUtil;
import com.qcc.udf.cpws.casesearch_v2.util.SbcToDbc;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 案号标准化
 * 1.全角转半角；
 * 2.删除中英文空格,(),[],【】
 * 3.剔除：纯数字,纯数字加标点，纯汉字,公证，证执,仲，征决，劳裁
 * 4.多案号切割，分隔符：、|,|;
 *  4.1. 案号多余内容删除：年|民事|调解书|判决书|生效|支付令|裁定书
 *  4.2. 案号头部信息处理：删除年份前的脏数据，补全年份，补全（）
 * 5.输出：多个案号，以逗号拼接
 * @Auther: wuql
 * @Date: 2020/11/17 10:00
 * @Description:
 */
public class AnnoStandard extends UDF {
    public String evaluate(String anno) {
        String annoRes = "";
        if (StringUtils.isBlank(anno) || anno == null) {
            return annoRes;
        }
        //1.全角转半角
        annoRes = SbcToDbc.convertSBCToDBC(anno);
        //2.删除空格（）()[等各种符号
        String rule1 = "[\\s]+|[\\u3000]+|[\\(]+|[\\)]+|[\\（]+|[\\）]+|[\\[]+|[\\]]+|[\\【]+|[\\】]+|[\\〔]+|[\\〕]+|[a-zA-Z]+|[:]+";
        annoRes = ReUtil.replaceAll(annoRes, rule1, "");

        //3.剔除：纯数字，纯汉字,公证，证执,仲，征决，劳裁
        String rule2 = "\\d{1,}|[\\u4e00-\\u9fa5]+|.*公证.*|.*证执.*|.*仲.*|.*征决.*|.*劳裁.*|[\\d\\p{Punct}]+";
        boolean matched1 = ReUtil.isMatch(rule2, annoRes);
        if (matched1) {
            return "";
        }

        //4.多余内容删除：年|民事|刑事|行政|执行|刑附民|刑事附带民事|判决书|判决|民判|裁定书|裁定|调解书|调解|决定书|决定|通知书|通知|批复|答复|含|支付令|生效
        String rule4 = "年|行政|执行|刑附民|刑事附带民事|刑事|民事|判决书|判决|民判|裁定书|裁定|调解书|调解|决定书|决定|通知书|通知|批复|答复|含|支付令|生效";
        annoRes = ReUtil.replaceAll(annoRes, rule4, "");

        //针对：(2019)闽0982执89号(2019)闽0982执1295号 ---> (2019)闽0982执89号,(2019)闽0982执1295号
        String rule5 = "号";
        annoRes = ReUtil.replaceAll(annoRes, rule5, "号,");
        if (annoRes.endsWith(",")) {
            annoRes = annoRes.substring(0, annoRes.lastIndexOf(","));
        }

        //4.多案号切割
        String rule6 = "、|,|;";
        String[] annoArr = annoRes.split(rule6);
        if (isSplit(annoArr)) {
            List<String> annoList = Arrays.stream(annoArr)
                    .map(str -> headHandle(str))
//                    .map(annos->cleanCharacter(annos))
                    .filter(str -> StringUtils.isNotBlank(str))
                    .collect(Collectors.toList());

            annoRes = String.join(",", annoList);
        } else {
            annoRes = headHandle(annoRes);
        }
        return annoRes;
    }

    private boolean isSplit(String[] annoArr) {
        return Arrays.stream(annoArr).noneMatch(anno -> {
            String rule = "\\d+[\\p{Punct}]*|[\\p{Punct}]+第{0,}\\d+号";
            return ReUtil.isMatch(rule, anno);
        });
    }

    private static String headHandle(String annoRes) {
        String rule2 = "\\d{1,}|[\\u4e00-\\u9fa5]+|.*公证.*|.*证执.*|.*仲.*|.*征决.*|.*劳裁.*|[\\d\\p{Punct}]+|[\\p{Punct}]+第{0,}\\d+号";
        boolean matched1 = ReUtil.isMatch(rule2, annoRes);
        if (matched1) {
            return "";
        }
        //4.2.头部信息处理
        //正则查找匹配的第一个字符串
        String rule5 = "\\d{1,4}";
        String resNumber = ReUtil.get(rule5, annoRes, 0);
        if (resNumber == null) {
            return "";
        }
        if (StringUtils.isNumeric(resNumber) && resNumber.length() == 2) {
            int year = Integer.parseInt(resNumber);
            if (year < 80) {
                resNumber = "20" + resNumber;
            } else {
                resNumber = "19" + resNumber;
            }
        }
        //删除第一个匹配到的内容以及之前的文本
        String annoSub = ReUtil.delPre(rule5, annoRes);
        annoRes = "(" + resNumber + ")" + annoSub;
        //针对编号处理：（2018）定民二初字第0040370号 ---> （2018）定民二初字第40370号
        annoRes = numberHandle(annoRes);

        boolean matched2 = ReUtil.isMatch(rule2, annoRes);
        if (matched2) {
            return "";
        }

        return annoRes.endsWith("号") ? annoRes : annoRes + "号";
    }

    //删除所有中文符号
    private static String cleanCharacter(String annoRes) {
        return annoRes.replaceAll("[\\u000B\\u00AD|\\u3002|\\uff1f|\\uff01|\\uff0c|\\u3001|\\uff1b|\\uff1a|\\u201c|\\u201d|\\u2018|\\u2019|\\uff08|\\uff09|\\u300a|\\u300b|\\u3008|\\u3009|\\u3010|\\u3011|\\u300e|\\u300f|\\u300c|\\u300d|\\ufe43|\\ufe44|\\u3014|\\u3015|\\u2026|\\uff5e|\\ufe4f|\\uffe5]", "");
    }

    private static String numberHandle(String annoRes) {
        if (annoRes.contains("之")){
            annoRes = annoRes.substring(0, annoRes.lastIndexOf("之"));
        }
        String rule6 = ".*\\d+号?$";
        boolean match = ReUtil.isMatch(rule6, annoRes);
        if (!match) {
            return annoRes;
        }
        annoRes = new StringBuffer(annoRes).reverse().toString();
        String numberRes = ReUtil.get("\\d+", annoRes, 0);
        //去掉末尾的0
        numberRes =numberRes.replaceAll("0+$","");
        annoRes = numberRes + ReUtil.delPre("\\d+", annoRes);
        annoRes = new StringBuffer(annoRes).reverse().toString();
        return annoRes;
    }


    public static void main(String[] args) {
        String anno = "(2007)昌0107执字第0001001号";
        String output = new AnnoStandard().evaluate(anno);
        System.out.println(output);

    }

}
