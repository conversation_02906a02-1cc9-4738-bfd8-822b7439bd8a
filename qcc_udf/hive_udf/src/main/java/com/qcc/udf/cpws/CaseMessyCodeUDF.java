package com.qcc.udf.cpws;

import jodd.util.StringUtil;
import org.apache.commons.io.IOUtils;
import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;

public class CaseMessyCodeUDF extends GenericUDF {

    private static final int commonCounterLine = 2000;
    private static final Map<String, Long> charStatisticMap;
    private static Long commonCounter;
    private transient ObjectInspector[] argumentOIs;

    static {
        InputStream inputStream = null;
        InputStreamReader inputStreamReader = null;
        BufferedReader reader = null;
        try {
            inputStream = CaseMessyCodeUDF.class.getResourceAsStream("/case_char_statistics.txt");
            inputStreamReader = new InputStreamReader(inputStream, "UTF-8");
            reader = new BufferedReader(inputStreamReader);
            Map<String, Long> tempMap = new HashMap<>();
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                String[] items = line.split("\t");
                if (items.length >= 2) {
                    tempMap.put(items[0], Long.parseLong(items[1]));
                    if (commonCounter == null && tempMap.size() >= commonCounterLine) {
                        commonCounter = Long.parseLong(items[1]);
                    }
                }
            }
            charStatisticMap = tempMap;

        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            IOUtils.closeQuietly(reader);
            IOUtils.closeQuietly(inputStreamReader);
            IOUtils.closeQuietly(inputStream);
        }
    }


    @Override
    public ObjectInspector initialize(ObjectInspector[] objectInspectors) throws UDFArgumentException {

        /**
         * add by daixuan
         * 20221129
         */
        this.argumentOIs = objectInspectors;
        if (objectInspectors == null || objectInspectors.length == 0) {
            throw new UDFArgumentException("The number of parameters is null");
        }

        return PrimitiveObjectInspectorFactory.javaStringObjectInspector;
    }

    @Override
    public Object evaluate(DeferredObject[] deferredObjects) throws HiveException {

        /**
         * add by daixuan
         * 20221129
         */
        if (deferredObjects == null || deferredObjects.length == 0) {
            return null;
        }

        String inputStr = String.valueOf(deferredObjects[0].get());
        inputStr = inputStr.replaceAll("[ \r\n\t]", "");
        int totalCount = inputStr.length();
        int generalCount = 0;
        String tempChar = null;
        for (int i = 0; i < inputStr.length(); i++) {
            tempChar = inputStr.substring(i, i + 1);
            Long tenderStatisticsCount = charStatisticMap.get(tempChar);
            if (tenderStatisticsCount != null && tenderStatisticsCount >= commonCounter) {
                // 常规字
                generalCount++;
            }
        }
        double generalPercent = ((double) generalCount / (double) totalCount);
        return String.valueOf(generalPercent);
    }

    @Override
    public String getDisplayString(String[] strings) {
        return "";
    }


    public static void main(String[] args) throws Exception {
        DeferredObject[] deferredObjects = new DeferredObject[]{
                new DeferredJavaObject("人")
        };

        Object a = new CaseMessyCodeUDF().evaluate(deferredObjects);
        System.out.println(a);
    }

}
