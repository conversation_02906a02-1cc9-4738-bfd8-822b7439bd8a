package com.qcc.udf.casesearch_v3.entity.output;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class XZCFListEntity   extends BasePenaltyOutEntity{
    //源数据id
    @JSONField(name = "SourceId")
    private String sourceId;
    //来源
    @JSONField(name = "Source")
    private int source;
    //来源名称
    @JSONField(name = "SourceName")
    private String sourceName;
    //工商的决定文书链接
    @JSONField(name = "PenaltyLink")
    private String penalty_link;
    //分组id
    @JSONField(name = "GroupId")
    private String groupId;
    //分组count
    @JSONField(name = "GroupCount")
    private Integer groupCount;
}
