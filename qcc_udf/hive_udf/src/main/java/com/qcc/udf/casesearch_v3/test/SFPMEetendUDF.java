package com.qcc.udf.casesearch_v3.test;


import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.output.LawSuitV3OutputEntity;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.casesearch_v3.entity.output.SFPMListEntity;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class SFPMEetendUDF extends UDF {

    public static String evaluate(String json)  {
        LawSuitV3OutputEntity jsonEntity = JSON.parseObject(json,LawSuitV3OutputEntity.class);
        Map<String, Set<String>> map = new HashMap<>();
        int okaCount = 0;
        int sfpmCnt = 0;
        Set<String> set = new HashSet<>();
        if(jsonEntity != null){
            if(jsonEntity.getInfoList() != null){
                for (InfoListEntity info : jsonEntity.getInfoList()) {
                    if(info.getSfpmList() != null){
                        for (SFPMListEntity sfpm : info.getSfpmList()) {
                            if(sfpm.getOwnerKeyNoArray() != null){
                                for (NameAndKeyNoEntity entity : sfpm.getOwnerKeyNoArray()) {
                                    if(!Strings.isNullOrEmpty(entity.getKeyNo())){
                                        set.add(entity.getKeyNo());
                                    }
                                    okaCount++;
                                }
                            }
                            sfpmCnt++;
                        }
                    }
                    map.put( info.getAnno(),set);
                }
            }
        }
        Map<String, Integer> result = new HashMap<>();
        result.put("okaCount",okaCount);
        result.put("keyNoCount",set.size());
        result.put("sfpmCnt",sfpmCnt);


        return JSON.toJSONString(result);
    }

    public static void main(String[] args) {
        String json  = "{\"AmtInfo\":{\"p11f3175053f049c93e5cac408ff3412\":{\"Amt\":\"350000\",\"IsValid\":\"0\",\"Type\":\"执行标的\"},\"p7bb83a116e158c54521ffbb4df25088\":{\"Amt\":\"350000\",\"IsValid\":\"0\",\"Type\":\"执行标的\"}},\"AnNoList\":\"（2019）川0503民初2552号,（2020）川0503执698号\",\"AnnoCnt\":2,\"CaseCnt\":2,\"CaseName\":\"泸州农村商业银行股份有限公司纳溪支公司与先义梅,徐强金融借款合同纠纷的案件\",\"CaseReason\":\"金融借款合同纠纷\",\"CaseRole\":\"[{\\\"D\\\":\\\"一审原告,首次执行申请执行人\\\",\\\"N\\\":\\\"4ebf18eefaf6532f06df4898927ba3ce\\\",\\\"O\\\":0,\\\"P\\\":\\\"泸州农村商业银行股份有限公司纳溪支公司\\\",\\\"R\\\":\\\"原告\\\"},{\\\"D\\\":\\\"一审被告,首次执行被执行人\\\",\\\"N\\\":\\\"p7bb83a116e158c54521ffbb4df25088\\\",\\\"O\\\":2,\\\"P\\\":\\\"先义梅\\\",\\\"R\\\":\\\"被告\\\"},{\\\"D\\\":\\\"一审被告,首次执行被执行人\\\",\\\"N\\\":\\\"p11f3175053f049c93e5cac408ff3412\\\",\\\"O\\\":2,\\\"P\\\":\\\"徐强\\\",\\\"R\\\":\\\"被告\\\"}]\",\"CaseType\":\"执行案件,民事案件\",\"CfdfCnt\":0,\"CfgsCnt\":0,\"CfxyCnt\":0,\"CompanyKeywords\":\"4ebf18eefaf6532f06df4898927ba3ce,p11f3175053f049c93e5cac408ff3412,p7bb83a116e158c54521ffbb4df25088,先义梅,徐强,泸州农村商业银行股份有限公司纳溪支公司,泸州农村商业银行股份有限公司纳溪支行\",\"CourtList\":\"四川省泸州市纳溪区人民法院\",\"EarliestDate\":1573056000,\"EarliestDateType\":\"民事一审|调解日期\",\"FyggCnt\":0,\"GqdjCnt\":0,\"GroupId\":\"00491c36dc1604c62aed4ce9a8c1dd24\",\"HbcfCnt\":0,\"Id\":\"c1468b9be18ed20fd031b8b1bce55ddf\",\"InfoList\":[{\"AnNo\":\"（2019）川0503民初2552号\",\"CaseList\":[{\"Amt\":\"\",\"CaseType\":\"民事调解书\",\"DocType\":\"调解日期\",\"Id\":\"023299e39bc7fd9f82b3782a818716d90\",\"IsValid\":1,\"JudgeDate\":1573056000,\"Result\":\"\",\"ResultType\":\"调解结果\"}],\"CaseReason\":\"金融借款合同纠纷\",\"CaseType\":\"民事案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"四川省泸州市纳溪区人民法院\",\"Defendant\":[{\"KeyNo\":\"p7bb83a116e158c54521ffbb4df25088\",\"Name\":\"先义梅\",\"Org\":2,\"Role\":\"被告\"},{\"KeyNo\":\"p11f3175053f049c93e5cac408ff3412\",\"Name\":\"徐强\",\"Org\":2,\"Role\":\"被告\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1573056000,\"LianList\":[{\"Id\":\"f5d2d99be86dba056d3c518f3ab7d45e\",\"IsValid\":1,\"LianDate\":1573056000,\"NameAndKeyNo\":[{\"KeyNo\":\"4ebf18eefaf6532f06df4898927ba3ce\",\"Name\":\"泸州农村商业银行股份有限公司纳溪支公司\",\"Org\":0},{\"KeyNo\":\"p11f3175053f049c93e5cac408ff3412\",\"Name\":\"徐强\",\"Org\":2},{\"KeyNo\":\"p7bb83a116e158c54521ffbb4df25088\",\"Name\":\"先义梅\",\"Org\":2}]}],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"4ebf18eefaf6532f06df4898927ba3ce\",\"Name\":\"泸州农村商业银行股份有限公司纳溪支公司\",\"Org\":0,\"Role\":\"原告\"}],\"SdggList\":[],\"SfpmList\":[],\"SqtjList\":[],\"SxList\":[],\"TrialRound\":\"民事一审\",\"XdpgjgList\":[],\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"XzcjList\":[],\"ZbList\":[],\"ZxList\":[]},{\"AnNo\":\"（2020）川0503执698号\",\"CaseList\":[{\"Amt\":\"\",\"CaseType\":\"执行通知书\",\"DocType\":\"通知日期\",\"Id\":\"168b2337c79ca244ff587c950a21c52f0\",\"IsValid\":1,\"JudgeDate\":1609171200,\"Result\":\"\",\"ResultType\":\"通知结果\"}],\"CaseReason\":\"借款合同纠纷案件执行\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"四川省泸州市纳溪区人民法院\",\"Defendant\":[{\"KeyNo\":\"p7bb83a116e158c54521ffbb4df25088\",\"Name\":\"先义梅\",\"Org\":2,\"Role\":\"被执行人\"},{\"KeyNo\":\"p11f3175053f049c93e5cac408ff3412\",\"Name\":\"徐强\",\"Org\":2,\"Role\":\"被执行人\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1609171200,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"4ebf18eefaf6532f06df4898927ba3ce\",\"Name\":\"泸州农村商业银行股份有限公司纳溪支行\",\"Org\":0,\"Role\":\"申请执行人\"}],\"SdggList\":[],\"SfpmList\":[{\"AmountUnit\":\"元\",\"BiaoDi\":\"泸州市江阳区滨江路二段2号楼2单元30号房屋\",\"EvaluationPrice\":\"534045\",\"Id\":\"8fce1a1b8745e8d5e8702fac63e86a65\",\"IsValid\":1,\"LianDate\":1606924800,\"Name\":\"一拍泸州市江阳区滨江路二段2号楼2单元30号房屋\",\"OwnerKeyNoArray\":[],\"YiWu\":\"451800\"},{\"AmountUnit\":\"元\",\"BiaoDi\":\"泸州市江阳区滨江路二段2号楼2单元30号房屋\",\"EvaluationPrice\":\"534045\",\"Id\":\"a29093f04f9449c5ecca6e3e78bdeb20\",\"IsValid\":1,\"LianDate\":1605369600,\"Name\":\"一拍泸州市江阳区滨江路二段2号楼2单元30号房屋\",\"OwnerKeyNoArray\":[],\"YiWu\":\"534045\"}],\"SqtjList\":[],\"SxList\":[],\"TrialRound\":\"首次执行\",\"XdpgjgList\":[],\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"XzcjList\":[],\"ZbList\":[],\"ZxList\":[{\"Biaodi\":\"350000\",\"Id\":\"368282b28687b775cceb7552f760d7931\",\"IsValid\":0,\"LianDate\":1598198400,\"NameAndKeyNo\":[{\"KeyNo\":\"p7bb83a116e158c54521ffbb4df25088\",\"Name\":\"先义梅\",\"Org\":2}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"350000\",\"Id\":\"454aac53eee83f4a284ba61ce1e1ccc11\",\"IsValid\":0,\"LianDate\":1598198400,\"NameAndKeyNo\":[{\"KeyNo\":\"p11f3175053f049c93e5cac408ff3412\",\"Name\":\"徐强\",\"Org\":2}],\"SqrNameAndKeyNo\":[]}]}],\"KtggCnt\":0,\"LastestDate\":1609171200,\"LastestDateType\":\"首次执行|通知日期\",\"LatestTrialRound\":\"首次执行\",\"LianCnt\":1,\"PcczCnt\":0,\"ProcuratorateList\":\"\",\"Province\":\"SC\",\"SdggCnt\":0,\"SfpmCnt\":2,\"Source\":\"OT\",\"SqtjCnt\":0,\"SxCnt\":0,\"Tags\":\"2,4,12,15\",\"Type\":1,\"XdpgjgCnt\":0,\"XgCnt\":0,\"XjpgCnt\":0,\"XzcfCnt\":0,\"XzcjCnt\":0,\"ZbCnt\":0,\"ZxCnt\":2}";

        System.out.println(evaluate(json));
    }
}
