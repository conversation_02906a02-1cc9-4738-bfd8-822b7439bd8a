package com.qcc.udf.risk_analysis;

import com.alibaba.fastjson.JSONObject;
import org.apache.hadoop.hive.ql.exec.UDF;

public class getNameAndKeyNo extends UDF {

    public String evaluate(String keyNo, String name) {
        JSONObject result = new JSONObject();
        result.put("Name", name == null ? "" : name);
        result.put("KeyNo", keyNo == null ? "" : keyNo);
        result.put("Org", RiskAnalysisUtil.getOrgByKeyNo(keyNo));

        return result.toString();
    }

    public static void main(String[] args) {
        getNameAndKeyNo aa = new getNameAndKeyNo();
        System.out.println(aa.evaluate("张三","000104c8f7e900b5beda00e2617a7a1e"));

    }
}
