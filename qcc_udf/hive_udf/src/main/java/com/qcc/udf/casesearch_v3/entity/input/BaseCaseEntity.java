package com.qcc.udf.casesearch_v3.entity.input;

import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.casesearch_v3.enums.CaseCategoryEnum;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * @Auther: zhangqiang
 * @Date: 2020/11/11 17:54
 * @Description:案件查询多维度入参基类
 */
@Data
public class BaseCaseEntity {

    /**
     * 司法案件维度来源
     */
    private CaseCategoryEnum baseCaseCategoryEnum;

    /**
     * 裁判文书:beforecaseno;失信:executeno，yiwu;执行:BeforeCaseNo;终本: cysah（关联案号）
     */
    private Set<String> baseBeforeNoSet;

    /**
     * 基础案号
     */
    private String baseCaseNo;

    /**
     * 基础案由
     */
    private String baseCaseReason;


    /**
     * 基础法院
     */
    private String baseCourt;

    /**
     * 省份code
     */
    private String baseProvinceCode;

    /**
     * 搜索字段（公司名称+人名+keyNo+personId集合）
     */
    private Set<String> baseSearchWordSet;


    /**
     * 当事人集合
     */
    private List<NameAndKeyNoEntity> baseNameKeyNoList;


    private String baseId;


    //当事人提取优化用
    /**
     * 原告+被告数量
     */
    private int rolePartyCount;

    /**
     * 当事人数量
     */
    private int partyCount;

    /**
     * 案件时间戳
     */
    private long caseTimeStamp;

}
