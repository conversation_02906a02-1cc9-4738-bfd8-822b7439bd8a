package com.qcc.udf.casesearch_v3.entity.output;

import lombok.Data;
import com.alibaba.fastjson.annotation.JSONField;

import java.util.LinkedList;
import java.util.List;

@Data
public class KtggListEntity  extends BaseCaseOutEntity{
     @JSONField(name = "Id")
    private String id = "";
     @JSONField(name = "OpenDate")
    private Long openDate = 0L;
     @JSONField(name = "IsValid")
    private Integer isValid = 0;
    @JSONField(name = "NameAndKeyNo")
    private List<NameAndKeyNoEntity> nameAndKeyNo = new LinkedList<>();
    @JSONField(name = "ExecuteUnite")
    private String executeUnite="";
}
