package com.qcc.udf.sivs;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.sivs.entity.CompanyDetailEntity;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 投资机构清洗UDF：下属企业表逻辑1的新增关联公司的判断逻辑
 * ---------------------------------------------------------------------------------------------------------
 * create temporary function affiliatedIncrementLogic1 as 'com.qcc.udf.sivs.AffiliatedIncrementLogic1UDF' using jar 'hdfs:///data/hive/udf/qcc_udf.jar';
 */
public class AffiliatedIncrementLogic1UDF extends UDF {
    private static List<String> InValidCompanyStatusList = Arrays.asList(
            "注销","吊销","筹建","清算","停业","撤销",   // 大陆企业的无效公司状态
            "已告解散", "休止活动", "已终止营业地点", "不再是独立的实体" // 香港企业的无效公司状态
    );

    /**
     * 根据link所给出的控制链路向下关联投资机构，当其中的某一环公司不满足继续往下的条件时终止关联情况
     * 不满足的条件：
     * 1 有基金标签
     * 2 存在与基准公司keyNo不一致的机构标签（keyNo关联的时IDG资本，当链路中的某个公司关联的为非IDG资本的机构时即为不满足的条件）
     * 3 有产品标签
     * 4 shortstatus给出的是无效的公司状态（'注销','吊销','筹建','清算','停业','撤销'）
     * 5 是否为过期公司（isexpire='true'）
     * @param keyNo 逻辑1中使用控制企业逻辑向下穿透得到更多关联公司的基准公司（该公司为当前下属企业库中的某个公司）
     * @param link 向下穿透过程中的公司链路（每一层都是通过控制企业的概念得到的唯一一个公司 a->b->c->d） 控制企业的概念：公司制为大股东，有限合伙为执行事务合伙人
     * @param infoArray 该链路所涉及到的所有公司的基本信息（包括状态，标签，公司名，注册时间等）
     * @return
     */
    public String evaluate(String keyNo, String link, String infoArray, String institutionName, String institutionCode) {
        JSONArray jsonArray = new JSONArray();
        try {
            if (StringUtils.isNotBlank(keyNo) && StringUtils.isNotBlank(link)
                    && StringUtils.isNotBlank(infoArray) && StringUtils.isNotBlank(institutionName) && StringUtils.isNotBlank(institutionCode)) {
                // 控制的公司链路
                List<String> linkCompanyKeyNoList = Arrays.stream(link.split(",")).collect(Collectors.toList());
                // 获取所有的公司详情信息映射集合
                Map<String, CompanyDetailEntity> companyDetailMap = getCompanyDetailMap(infoArray);
                if (CollectionUtils.isNotEmpty(linkCompanyKeyNoList) && CollectionUtils.isNotEmpty(companyDetailMap.keySet())) {
                    for (String linkCompanyKeyNo : linkCompanyKeyNoList) {
                        CompanyDetailEntity companyDetailEntity = companyDetailMap.get(linkCompanyKeyNo);
                        if (companyDetailEntity == null) {
                            break;
                        }
                        Boolean condition1 = companyDetailEntity.getIsExpire().equals("false") ? Boolean.TRUE : Boolean.FALSE;
                        Boolean condition2 = StringUtils.isBlank(companyDetailEntity.getProduct()) ? Boolean.TRUE : Boolean.FALSE;
                        Boolean condition3 = !InValidCompanyStatusList.contains(companyDetailEntity.getShortStatus()) ? Boolean.TRUE : Boolean.FALSE;
                        Boolean condition4 = (StringUtils.isBlank(companyDetailEntity.getInvestList())
                                || companyDetailEntity.getInvestList().equals("[]")
                                || companyDetailEntity.getInvestList().contains(institutionCode)
                                || companyDetailEntity.getInvestList().contains(institutionName)) ? Boolean.TRUE : Boolean.FALSE;
                        Boolean condition5 = (StringUtils.isBlank(companyDetailEntity.getTags())
                                || companyDetailEntity.getTags().equals("[]")
                                || !companyDetailEntity.getTags().equals("私募基金")) ? Boolean.TRUE : Boolean.FALSE;
                        if (condition1 && condition2 && condition3 && condition4 && condition5) {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("KeyNo", linkCompanyKeyNo);
                            jsonObject.put("StartDate", companyDetailEntity.getStartDate());
                            // 增加一个判断：判断该公司是关联了同一机构还是没有关联任何投资机构
                            if (StringUtils.isBlank(companyDetailEntity.getInvestList()) || companyDetailEntity.getInvestList().equals("[]")) {
                                jsonObject.put("HasInvest", 0);
                            } else {
                                jsonObject.put("HasInvest", 1);
                            }
                            jsonArray.add(jsonObject);
                        } else {
                            break;
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return jsonArray.toJSONString();
    }

    private static Map<String, CompanyDetailEntity> getCompanyDetailMap(String infoArray) {
        Map<String, CompanyDetailEntity> companyDetailMap = new HashMap<>();
        try {
            if (StringUtils.isNotBlank(infoArray)) {
                JSONArray jsonArray = JSONArray.parseArray(infoArray);
                if (CollectionUtils.isNotEmpty(jsonArray)) {
                    for (int i=0; i<jsonArray.size(); i++) {
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        String name = jsonObject.getString("name");
                        String keyNo = jsonObject.getString("keyno");
                        String isExpire = jsonObject.getString("isexpired");
                        String investList = jsonObject.getString("investlist");
                        String product = jsonObject.getString("product");
                        String shortStatus = jsonObject.getString("shortstatus");
                        String tags = jsonObject.getString("tags");

                        Long startDate = 0L;
                        try {
                            if (keyNo.startsWith("h")) {
                                Matcher hkStartDateMatcher = Pattern.compile("(\\d{4})年(\\d{1,2})月(\\d{1,2})日").matcher(jsonObject.getString("startdate"));
                                if (hkStartDateMatcher.find()) {
                                    String yearInfo = hkStartDateMatcher.group(1);
                                    String monthInfo = hkStartDateMatcher.group(2);
                                    String monthInfoFinal = monthInfo.length() == 1 ? "0"+monthInfo : monthInfo;
                                    String dayInfo = hkStartDateMatcher.group(3);

                                    DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                                    try {
                                        LocalDateTime localDateTime =
                                                LocalDateTime.parse((StringUtils.join(Arrays.asList(yearInfo,monthInfoFinal,dayInfo), "-") + " 00:00:00"), df);
                                        if (localDateTime.getYear() >= 1990 && localDateTime.getYear() <= LocalDateTime.now().getYear()) {
                                            startDate = localDateTime.toEpochSecond(ZoneOffset.of("+8"));
                                        }
                                    } catch (Exception ignored) {
                                    }
                                } else if (keyNo.startsWith("t")) {

                                } else {
                                    Matcher startDateMatcher = Pattern.compile("\\d{10}").matcher(jsonObject.getString("startdate"));
                                    if (startDateMatcher.find()) {
                                        startDate = Long.parseLong(startDateMatcher.group(0));
                                    }
                                }
                            }
                        } catch (Exception ex) {
                        }

                        CompanyDetailEntity companyDetailEntity = CompanyDetailEntity.builder()
                                .name(name).keyNo(keyNo).isExpire(isExpire).investList(investList).product(product).shortStatus(shortStatus)
                                .tags(tags).startDate(startDate).build();
                        companyDetailMap.put(keyNo, companyDetailEntity);
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return companyDetailMap;
    }

    public static void main(String[] args) {
        String keyNo = "e938830fef075d80730ca0182ea90ad3";
        String link = "23904b0c69b453eba1f740a3152ffd81,164bf0f28c67fb899f074fe0a62bf6d0";
        String infoArray = "[{\"name\":\"北京京能清洁能源电力股份有限公司\",\"startdate\":\"{\\\"$numberLong\\\":\\\"*********\\\"}\",\"product\":\"{\\\"FinancingDate\\\":{\\\"$numberLong\\\":\\\"1322668800\\\"},\\\"CompatCount\\\":20,\\\"RoundDesc\\\":\\\"E轮及以后\\\",\\\"Amount\\\":\\\"-\\\",\\\"Round\\\":\\\"E轮及以后\\\",\\\"MemberCount\\\":3,\\\"_id\\\":\\\"27e40243-a8e0-4ca8-ba17-8bc9e54f1564\\\",\\\"FinancingCount\\\":1,\\\"Name\\\":\\\"北京京能\\\",\\\"Logo\\\":\\\"https://image.qcc.com/product/27e40243-a8e0-4ca8-ba17-8bc9e54f1564.jpg\\\"}\",\"shortstatus\":\"存续\",\"investlist\":\"\",\"tags\":\"[{\\\"DataExtend\\\":\\\"27e40243-a8e0-4ca8-ba17-8bc9e54f1564\\\",\\\"Type\\\":3,\\\"TradingPlaceCode\\\":null,\\\"ShortName\\\":\\\"北京京能\\\",\\\"TradingPlaceName\\\":null,\\\"Name\\\":\\\"E轮及以后\\\"},{\\\"DataExtend\\\":\\\"\\\",\\\"Type\\\":505,\\\"TradingPlaceCode\\\":\\\"\\\",\\\"ShortName\\\":\\\"\\\",\\\"TradingPlaceName\\\":\\\"\\\",\\\"Name\\\":\\\"小微企业\\\"},{\\\"DataExtend\\\":\\\"27e40243-a8e0-4ca8-ba17-8bc9e54f1564\\\",\\\"Type\\\":4,\\\"TradingPlaceCode\\\":\\\"8df91d65f5f53acbdc6d35018df2dfcb\\\",\\\"ShortName\\\":null,\\\"TradingPlaceName\\\":null,\\\"Name\\\":\\\"高科技\\\"},{\\\"DataExtend\\\":\\\"27e40243-a8e0-4ca8-ba17-8bc9e54f1564\\\",\\\"Type\\\":4,\\\"TradingPlaceCode\\\":\\\"bf62757f26ab036edd2134d8113fd1d5\\\",\\\"ShortName\\\":null,\\\"TradingPlaceName\\\":null,\\\"Name\\\":\\\"发电\\\"},{\\\"DataExtend\\\":\\\"27e40243-a8e0-4ca8-ba17-8bc9e54f1564\\\",\\\"Type\\\":4,\\\"TradingPlaceCode\\\":\\\"c4cf232b5045c2641aa036d542f70ed9\\\",\\\"ShortName\\\":null,\\\"TradingPlaceName\\\":null,\\\"Name\\\":\\\"燃气\\\"},{\\\"DataExtend\\\":\\\"27e40243-a8e0-4ca8-ba17-8bc9e54f1564\\\",\\\"Type\\\":4,\\\"TradingPlaceCode\\\":\\\"785d0b5a2b8dabc8a848411e094c0e66\\\",\\\"ShortName\\\":null,\\\"TradingPlaceName\\\":null,\\\"Name\\\":\\\"风电\\\"},{\\\"DataExtend\\\":\\\"00579.HK\\\",\\\"Type\\\":6,\\\"TradingPlaceCode\\\":null,\\\"ShortName\\\":\\\"京能清洁能源\\\",\\\"TradingPlaceName\\\":\\\"\\\",\\\"Name\\\":\\\"港股\\\"},{\\\"DataExtend\\\":null,\\\"Type\\\":622,\\\"TradingPlaceCode\\\":null,\\\"ShortName\\\":null,\\\"TradingPlaceName\\\":null,\\\"Name\\\":\\\"国有企业\\\"}]\",\"isexpired\":\"false\",\"keyno\":\"23904b0c69b453eba1f740a3152ffd81\"},{\"name\":\"黑水县三联水电开发有限责任公司\",\"startdate\":\"{\\\"$numberLong\\\":\\\"1097510400\\\"}\",\"product\":\"\",\"shortstatus\":\"存续\",\"investlist\":\"\",\"tags\":\"[{\\\"DataExtend\\\":\\\"\\\",\\\"Type\\\":505,\\\"TradingPlaceCode\\\":\\\"\\\",\\\"ShortName\\\":\\\"\\\",\\\"TradingPlaceName\\\":\\\"\\\",\\\"Name\\\":\\\"小微企业\\\"},{\\\"DataExtend\\\":null,\\\"Type\\\":622,\\\"TradingPlaceCode\\\":null,\\\"ShortName\\\":null,\\\"TradingPlaceName\\\":null,\\\"Name\\\":\\\"国有企业\\\"}]\",\"isexpired\":\"false\",\"keyno\":\"164bf0f28c67fb899f074fe0a62bf6d0\"}] ";
        String institutionName = "北京能源集团";
        String instituionCode = "2b8ed17fbe7858c641d55ca03eb8ace3";
        String result = new AffiliatedIncrementLogic1UDF().evaluate(keyNo, link, infoArray, institutionName, instituionCode);
        System.out.println(result);
    }
}
