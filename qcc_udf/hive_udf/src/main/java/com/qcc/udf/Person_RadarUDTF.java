package com.qcc.udf;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.TypeAdapter;
import com.google.gson.internal.LinkedTreeMap;
import com.google.gson.reflect.TypeToken;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;
import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentLengthException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDTF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;

import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.regex.Pattern;


public class Person_RadarUDTF extends GenericUDTF{
    static String inschema="keyno1,name1,oper1,employees1,partners1,keyno2,name2,oper2,employees2,partners2,source_type";
    static String outschema="c0,c1,c2,c3,c4,c5,c6,c7";

    Map<String, Integer> inschemap = new HashMap<>();
    Map<String, Integer> outschemap = new HashMap<>();
    @Override
    public void close() throws HiveException {
        // TODO Auto-generated method stub

    }

    @Override
    public void process(Object[] args) throws HiveException {
        try {
            //初始化
            inschemap.clear();
            outschemap.clear();
            String[] schema_array = inschema.split(",");
            for (int i = 0; i < schema_array.length; i++) {
                inschemap.put(schema_array[i], i);
            }
            schema_array = outschema.split(",");
            for (int i = 0; i < schema_array.length; i++) {
                outschemap.put(schema_array[i], i);
            }
            Object[] infos = new Object[args.length];
            for (int i = 0; i < args.length; i++) {
                infos[i] = args[i];
            }

            GsonBuilder builder = new GsonBuilder();
            builder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapTypeAdapter());
            Gson gson = builder.create();

            //old company
/*        if(infos[inschemap.get("keyno1")]!=null){

        }*/
            //new company
            if (infos[inschemap.get("keyno2")] == null) {
                return;
            }
            Object[] results;
            String cmpid = infos[inschemap.get("keyno2")] == null ? "" : infos[inschemap.get("keyno2")].toString();
            String cmpname = infos[inschemap.get("name2")] == null ? "" : infos[inschemap.get("name2")].toString();
            String md5key = "516A4D39-1B56-4FCC-93A6-3A3249AAACE7";
            if (infos[inschemap.get("source_type")] != null) {
                //opername
                if ("opername".equals(infos[inschemap.get("source_type")].toString())) {
                    String operidold = "null";
                    String opernameold = "";
                    String operidnew = "null";
                    String opernamenew = "";
                    Map<String, Object> jsonoper = new HashMap<String, Object>();
                    if (infos[inschemap.get("oper1")] != null) {
                        jsonoper = (Map<String, Object>) gson.fromJson(infos[inschemap.get("oper1")].toString(), new TypeToken<Map<String, Object>>() {
                        }.getType());
                    }
                    if (jsonoper.containsKey("Name")) {
                        if (jsonoper.get("Name") != null) {
                            opernameold = Drop.DropCC(jsonoper.get("Name").toString());
                        }
                    }
                    if (jsonoper.containsKey("KeyNo")) {
                        if (jsonoper.get("KeyNo") != null && jsonoper.get("KeyNo") != "" && jsonoper.get("KeyNo") != "null") {
                            operidold = jsonoper.get("KeyNo").toString().replace(" ", "");
                            if (!operidold.startsWith("p") || operidold.length() != 32) {
                                operidold = Person_MD5Key.create(cmpid + md5key + opernameold);
                            }
                        } else {
                            operidold = Person_MD5Key.create(cmpid + md5key + opernameold);
                        }
                    }
                    if (infos[inschemap.get("oper2")] != null) {
                        jsonoper = (Map<String, Object>) gson.fromJson(infos[inschemap.get("oper2")].toString(), new TypeToken<Map<String, Object>>() {
                        }.getType());
                    }
                    if (jsonoper.containsKey("Name")) {
                        if (jsonoper.get("Name") != null) {
                            opernamenew = Drop.DropCC(jsonoper.get("Name").toString());
                        }
                    }
                    if (jsonoper.containsKey("KeyNo")) {
                        if (jsonoper.get("KeyNo") != null && jsonoper.get("KeyNo") != "" && jsonoper.get("KeyNo") != "null") {
                            operidnew = jsonoper.get("KeyNo").toString().replace(" ", "");
                            if (!operidnew.startsWith("p") || operidnew.length() != 32) {
                                operidnew = Person_MD5Key.create(cmpid + md5key + opernamenew);
                            }
                        } else {
                            operidnew = Person_MD5Key.create(cmpid + md5key + opernamenew);
                        }
                    }
                    //catagory:（1法人2任职3投资4股权5失信6被执行7人员key）',
                    //radartype: '0更新1新增2删除',
                   // System.out.println("人员key变动forward输出");
                    if (!opernameold.equals(opernamenew)) {
                        if (!"".equals(opernameold)) {
                            results = new Object[]{operidold, opernameold, cmpid, cmpname, "1", "2", opernameold, opernamenew};
                           // System.out.println(results);
                            forward(results);
                        }
                       // System.out.println(new Object[]{operidnew, opernamenew, cmpid, cmpname, "1", "1", opernameold, opernamenew});
                        forward(new Object[]{operidnew, opernamenew, cmpid, cmpname, "1", "1", opernameold, opernamenew});
                    } else if (!operidold.equals(operidnew)) {
                       // System.out.println(new Object[]{operidold, opernameold, cmpid, cmpname, "7", "0", operidold, operidnew});
                        forward(new Object[]{operidold, opernameold, cmpid, cmpname, "7", "0", operidold, operidnew});
                    }
                }
                //employeename
                else if ("employees".equals(infos[inschemap.get("source_type")].toString())) {
                    Map<String, String> listkey = new HashMap<String, String>();
                    Map<String, String> listoldkey = new HashMap<String, String>();
                    Map<String, String> listnewkey = new HashMap<String, String>();
                    Map<String, String> jsonempold = new HashMap<String, String>();
                    Map<String, String> jsonempnew = new HashMap<String, String>();
                    ArrayList<Map<String, Object>> jsonemp = new ArrayList<Map<String, Object>>();
                    if (infos[inschemap.get("employees1")] != null) {
                        jsonemp = (ArrayList<Map<String, Object>>) gson.fromJson(infos[inschemap.get("employees1")].toString(), new TypeToken<ArrayList<Map<String, Object>>>() {
                        }.getType());
                    }
                    for (int i = 0; i < jsonemp.size(); i++) {
                        Map<String, Object> jsontmp = (Map<String, Object>) jsonemp.get(i);
                        if (jsontmp.containsKey("KeyNo")) {
                            String id, name, job;
                            name = jsontmp.get("Name") == null ? "" : Drop.DropCC(jsontmp.get("Name").toString());
                            job = jsontmp.get("Job") == null ? "" : jsontmp.get("Job").toString();
                            job = Pattern.compile("[^\\u4e00-\\u9fa5]*").matcher(job).replaceAll("").trim().toString();
                            //--------new----------//
                            if (!"".equals(name)) {
                                if (jsontmp.get("KeyNo") != null && jsontmp.get("KeyNo") != "" && jsontmp.get("KeyNo") != "null") {
                                    id = jsontmp.get("KeyNo").toString().replace(" ", "");
                                    if (!id.startsWith("p") || id.length() != 32) {
                                        id = Person_MD5Key.create(cmpid + md5key + name);
                                    }
                                } else {
                                    id = Person_MD5Key.create(cmpid + md5key + name);
                                }
                                listkey.put(name, id);
                                listoldkey.put(name, id);
                                if (jsonempold.containsKey(name)) {
                                    if (!("," + jsonempold.get(name) + ",").contains("," + job + ",")) {
                                        jsonempold.put(name, jsonempold.get(name) + "," + job);
                                    }
                                } else {
                                    jsonempold.put(name, job);
                                }
                            }
                        }
                    }
                    if (infos[inschemap.get("employees2")] != null) {
                        jsonemp = (ArrayList<Map<String, Object>>) gson.fromJson(infos[inschemap.get("employees2")].toString(), new TypeToken<ArrayList<Map<String, Object>>>() {
                        }.getType());
                    }
                    for (int i = 0; i < jsonemp.size(); i++) {
                        Map<String, Object> jsontmp = (Map<String, Object>) jsonemp.get(i);
                        if (jsontmp.containsKey("KeyNo")) {
                            String id, name, job;
                            name = jsontmp.get("Name") == null ? "" : Drop.DropCC(jsontmp.get("Name").toString());
                            job = jsontmp.get("Job") == null ? "" : jsontmp.get("Job").toString();
                            job = Pattern.compile("[^\\u4e00-\\u9fa5]*").matcher(job).replaceAll("").trim().toString();

                            //--------new----------//
                            if (!"".equals(name)) {
                                if (jsontmp.get("KeyNo") != null && jsontmp.get("KeyNo") != "" && jsontmp.get("KeyNo") != "null") {
                                    id = jsontmp.get("KeyNo").toString().replace(" ", "");
                                    if (!id.startsWith("p") || id.length() != 32) {
                                        id = Person_MD5Key.create(cmpid + md5key + name);
                                    }
                                } else {
                                    id = Person_MD5Key.create(cmpid + md5key + name);
                                }
                                listkey.put(name, id);
                                listnewkey.put(name, id);
                                if (jsonempnew.containsKey(name)) {
                                    if (!("," + jsonempnew.get(name) + ",").contains("," + job + ",")) {
                                        jsonempnew.put(name, jsonempnew.get(name) + "," + job);
                                    }
                                } else {
                                    jsonempnew.put(name, job);
                                }
                            }
                        }
                    }

                    Iterator<Map.Entry<String, String>> iter = listkey.entrySet().iterator();
                    while (iter.hasNext()) {
                        Map.Entry<String, String> entry = (Map.Entry<String, String>) iter.next();
                        String key = entry.getKey();
                        if (jsonempold.containsKey(key) && !jsonempnew.containsKey(key)) {
                           // System.out.println(new Object[]{entry.getValue(), key, cmpid, cmpname, "2", "2", jsonempold.get(key), ""});
                            forward(new Object[]{entry.getValue(), key, cmpid, cmpname, "2", "2", jsonempold.get(key), ""});

                        } else if (!jsonempold.containsKey(key) && jsonempnew.containsKey(key)) {
                           // System.out.println(new Object[]{entry.getValue(), key, cmpid, cmpname, "2", "1", "", jsonempnew.get(key)});
                            forward(new Object[]{entry.getValue(), key, cmpid, cmpname, "2", "1", "", jsonempnew.get(key)});

                        } else if (jsonempold.containsKey(key) && jsonempnew.containsKey(key)) {
                            if (!listoldkey.get(key).equals(listnewkey.get(key))) {
                              //  System.out.println(new Object[]{listoldkey.get(key), key, cmpid, cmpname, "7", "0", listoldkey.get(key), listnewkey.get(key)});
                                forward(new Object[]{listoldkey.get(key), key, cmpid, cmpname, "7", "0", listoldkey.get(key), listnewkey.get(key)});
                            }
                            String vold = jsonempold.get(key);
                            String vnew = jsonempnew.get(key);
                            if (vold.contains(",")) {
                                try {
                                    String[] js2 = vold.split(",");
                                    Arrays.sort(js2);
                                    vold = "";
                                    for (String tmp : js2) {
                                        vold += tmp;
                                    }
                                } catch (Exception e) {
                                }
                            }
                            if (vnew.contains(",")) {
                                try {
                                    String[] js2 = vnew.split(",");
                                    Arrays.sort(js2);
                                    vnew = "";
                                    for (String tmp : js2) {
                                        vnew += tmp;
                                    }
                                } catch (Exception e) {
                                }
                            }
                            if (!vold.equals(vnew)) {
                                if (vold != vnew) {
                                   // System.out.println(new Object[]{entry.getValue(), key, cmpid, cmpname, "2", "0", jsonempold.get(key), jsonempnew.get(key)});
                                    forward(new Object[]{entry.getValue(), key, cmpid, cmpname, "2", "0", jsonempold.get(key), jsonempnew.get(key)});
                                }
                            }
                        }
                    }
                }
                //partnername
                else if ("partner".equals(infos[inschemap.get("source_type")].toString())) {
                    Map<String, String> listkey = new HashMap<String, String>();
                    Map<String, String> listoldkey = new HashMap<String, String>();
                    Map<String, String> listnewkey = new HashMap<String, String>();
                    Map<String, String> jsonpntold = new HashMap<String, String>();
                    Map<String, String> jsonpntnew = new HashMap<String, String>();
                    HashSet<String> oldname = new HashSet<String>();
                    HashSet<String> newname = new HashSet<String>();
                    ArrayList<Map<String, Object>> jsonpnt = new ArrayList<Map<String, Object>>();

                    if(infos[inschemap.get("partners1")]!=null) {
                        jsonpnt = (ArrayList<Map<String, Object>>) gson.fromJson(infos[inschemap.get("partners1")].toString(), new TypeToken<ArrayList<Map<String, Object>>>() {
                        }.getType());
                    }
                    for (int i = 0; i < jsonpnt.size(); i++) {
                        Map<String, Object> jsontmp = (Map<String, Object>) jsonpnt.get(i);
                        if (jsontmp.containsKey("KeyNo")) {
                            String id, name, stp;
                            name=jsontmp.get("StockName") == null?"": Drop.DropCC(jsontmp.get("StockName").toString());
                            stp=jsontmp.get("StockPercent") == null?"":jsontmp.get("StockPercent").toString().replace(" ", "");
                            //--------new----------//
                            Long org = (Long) jsontmp.get("Org");
                            oldname.add(name);
                            if(!"".equals(name)&& (org==-2 || org==2)) {
                                if (jsontmp.get("KeyNo") != null && jsontmp.get("KeyNo") != "" && jsontmp.get("KeyNo") != "null") {
                                    id = jsontmp.get("KeyNo").toString().replace(" ", "");
                                    if(!id.startsWith("p")||id.length()!=32){
                                        id= Person_MD5Key.create(cmpid + md5key + name);
                                    }
                                } else {
                                    id = Person_MD5Key.create(cmpid + md5key + name);
                                }
                                listkey.put(name, id);
                                listoldkey.put(name, id);
                                if (jsonpntold.containsKey(name)) {
                                    if(!(","+jsonpntold.get(name)+",").contains(","+stp+",")) {
                                        jsonpntold.put(name, jsonpntold.get(name) + "," + stp);
                                    }
                                } else {
                                    jsonpntold.put(name, stp);
                                }
                            }
                        }
                    }
                    if(infos[inschemap.get("partners2")]!=null) {
                        jsonpnt = (ArrayList<Map<String, Object>>) gson.fromJson(infos[inschemap.get("partners2")].toString(), new TypeToken<ArrayList<Map<String, Object>>>() {
                        }.getType());
                    }
                    for (int i = 0; i < jsonpnt.size(); i++) {
                        Map<String, Object> jsontmp = (Map<String, Object>) jsonpnt.get(i);
                        if (jsontmp.containsKey("KeyNo")) {
                            String id, name, stp;
                            name=jsontmp.get("StockName") == null?"": Drop.DropCC(jsontmp.get("StockName").toString());
                            stp=jsontmp.get("StockPercent") == null?"":jsontmp.get("StockPercent").toString().replace(" ", "");
                            //--------new----------//
                            Long org = (Long) jsontmp.get("Org");
                            newname.add(name);
                            if(!"".equals(name)&&(org==-2 || org==2)) {
                                if (jsontmp.get("KeyNo") != null && jsontmp.get("KeyNo") != "" && jsontmp.get("KeyNo") != "null") {
                                    id = jsontmp.get("KeyNo").toString().replace(" ", "");
                                    if(!id.startsWith("p")||id.length()!=32){
                                        id= Person_MD5Key.create(cmpid + md5key + name);
                                    }
                                } else {
                                    id = Person_MD5Key.create(cmpid + md5key + name);
                                }
                                listkey.put(name, id);
                                listnewkey.put(name, id);
                                if (jsonpntnew.containsKey(name)) {
                                    if(!(","+jsonpntnew.get(name)+",").contains(","+stp+",")) {
                                        jsonpntnew.put(name, jsonpntnew.get(name) + "," + stp);
                                    }
                                } else {
                                    jsonpntnew.put(name, stp);
                                }
                            }
                        }
                    }
                    Iterator<Map.Entry<String, String>> iter = listkey.entrySet().iterator();
                    while (iter.hasNext()) {
                        Map.Entry<String, String> entry = (Map.Entry<String, String>) iter.next();
                        String key = entry.getKey();
                        if (jsonpntold.containsKey(key) && !newname.contains(key)) {
                            forward(new Object[]{entry.getValue(), key, cmpid, cmpname, "3", "2", jsonpntold.get(key), ""});

                        } else if (!oldname.contains(key) && jsonpntnew.containsKey(key)) {
                            forward(new Object[]{entry.getValue(), key, cmpid, cmpname, "3", "1", "", jsonpntnew.get(key)});

                        } else if (jsonpntold.containsKey(key) && jsonpntnew.containsKey(key)) {
                            if(!listoldkey.get(key).equals(listnewkey.get(key))){
                                forward(new Object[]{listoldkey.get(key),key,cmpid,cmpname,"7","0",listoldkey.get(key),listnewkey.get(key)});
                            }
                            String vold = jsonpntold.get(key);
                            String vnew = jsonpntnew.get(key);
                            float stp_old=0f;
                            float stp_new=0f;
                            if (vold.contains(",")) {
                                try {
                                    String[] js2 = vold.split(",");
                                    for (String tmp : js2) {
                                        stp_old += Float.parseFloat(tmp.replace("%","").trim());
                                    }
                                } catch (Exception e) {
                                }
                            }else{
                                try {
                                    stp_old = Float.parseFloat(vold.replace("%", ""));
                                }catch (Exception e){

                                }
                            }
                            if (vnew.contains(",")) {
                                try {
                                    String[] js2 = vnew.split(",");
                                    for (String tmp : js2) {
                                        stp_new += Float.parseFloat(tmp.replace("%","").trim());
                                    }
                                } catch (Exception e) {
                                }
                            }else {
                                try {
                                    stp_new = Float.parseFloat(vnew.replace("%", ""));
                                }catch (Exception e){

                                }
                            }
                            if (stp_old!=stp_new && stp_old > 0 && stp_new >0) {
                                forward(new Object[]{entry.getValue(), key, cmpid, cmpname, "4", "0", jsonpntold.get(key), jsonpntnew.get(key)});
                            }
                        }
                    }
                }
            } else {
                return;
            }
        }catch(Exception e){
            e.printStackTrace();
        }





    }
    public static class MapTypeAdapter extends TypeAdapter<Object> {

        @Override
        public Object read(JsonReader in) throws IOException {
            JsonToken token = in.peek();
            switch (token) {
                case BEGIN_ARRAY:
                    List<Object> list = new ArrayList<Object>();
                    in.beginArray();
                    while (in.hasNext()) {
                        list.add(read(in));
                    }
                    in.endArray();
                    return list;

                case BEGIN_OBJECT:
                    Map<String, Object> map = new LinkedTreeMap<String, Object>();
                    in.beginObject();
                    while (in.hasNext()) {
                        map.put(in.nextName(), read(in));
                    }
                    in.endObject();
                    return map;

                case STRING:
                    return in.nextString();

                case NUMBER:

                    double dbNum = in.nextDouble();

                    if (dbNum > Long.MAX_VALUE) {
                        return dbNum;
                    }

                    long lngNum = (long) dbNum;
                    if (dbNum == lngNum) {
                        return lngNum;
                    } else {
                        return dbNum;
                    }

                case BOOLEAN:
                    return in.nextBoolean();

                case NULL:
                    in.nextNull();
                    return null;

                default:
                    throw new IllegalStateException();
            }
        }
        @Override
        public void write(JsonWriter out, Object value) throws IOException {
        }
    }

    public static class Person_MD5Key {
        public static String encrypByMd5(String context) {
            /*
             * 1.一个运用基本类的实例
             * MessageDigest 对象开始被初始化。该对象通过使用 update 方法处理数据。
             * 任何时候都可以调用 reset 方法重置摘要。
             * 一旦所有需要更新的数据都已经被更新了，应该调用 digest 方法之一完成哈希计算。
             * 对于给定数量的更新数据，digest 方法只能被调用一次。
             * 在调用 digest 之后，MessageDigest 对象被重新设置成其初始状态。
             */
            try {
                MessageDigest md = MessageDigest.getInstance("MD5");
                md.update(context.getBytes());//update处理
                byte [] encryContext = md.digest();//调用该方法完成计算

                int i;
                StringBuffer buf = new StringBuffer("");
                for (int offset = 0; offset < encryContext.length; offset++) {//做相应的转化（十六进制）
                    i = encryContext[offset];
                    if (i < 0) i += 256;
                    if (i < 16) buf.append("0");
                    buf.append(Integer.toHexString(i));
                }
                return buf.toString();
            } catch (NoSuchAlgorithmException e) {
                e.printStackTrace();
                return null;
            }
        }
        public static String create(String str) {
            return "pr" + encrypByMd5(str).substring(2,32);
        }
    }



    public static class Drop{
        public static String DropCC(String a){
            return a.replace("（", "(").replace("）", ")")
                    .replace("。", "").
                            replace(";", ",").
                            replace("！", "!").
                            replace("？", "?").
                            replace("，", ",").
                            replace(" ", "").
                            replace("　", "");
        }
    }

    @Override
    public StructObjectInspector initialize(ObjectInspector[] args)
            throws UDFArgumentException {
        if (args.length != 11) {
            throw new UDFArgumentLengthException(
                    "Person_RadarUDTF takes only 11 argument");
        }
        ArrayList<String> fieldNames = new ArrayList<String>();
        ArrayList<ObjectInspector> fieldOIs = new ArrayList<ObjectInspector>();
        String[] schema_array = outschema.split(",");
        for(int i=0;i<schema_array.length;i++){
            fieldNames.add(schema_array[i].toString());
            fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        }
        return ObjectInspectorFactory.getStandardStructObjectInspector(
                fieldNames, fieldOIs);
    }

    public static void main(String[] args) throws HiveException {
        String str="006f1aaf77963044d9cdcd66e980e855\n" +
                "北京首航波纹管制造有限公司\n" +
                "{\"KeyNo\":\"p7b2910ff68fb050643afd9041b5ac5d\",\"Org\":2,\"OperType\":1,\"HasImage\":false,\"CompanyCount\":4,\"Name\":\"黄文革\"}\n" +
                "[{\"KeyNo\":\"p7b2910ff68fb050643afd9041b5ac5d\",\"No\":0,\"HasImage\":false,\"CompanyCount\":4,\"ScertName\":null,\"Job\":\"执行董事长\",\"CerNo\":null,\"Name\":\"黄文革\"},{\"KeyNo\":\"p437fb7470b3ce7c8c5583950063d7d3\",\"No\":0,\"HasImage\":false,\"CompanyCount\":2,\"ScertName\":null,\"Job\":\"经理\",\"CerNo\":null,\"Name\":\"鲍兴连\"},{\"KeyNo\":\"p21d17b695ad3c02bed16e44879afc2c\",\"No\":0,\"HasImage\":false,\"CompanyCount\":5,\"ScertName\":null,\"Job\":\"监事\",\"CerNo\":null,\"Name\":\"黄卿河\"}]\n" +
                "[{\"Org\":2,\"StockName\":\"黄文革\",\"HasImage\":false,\"StockPercent\":\"33.62%\",\"CompanyCount\":4,\"ShouldCapi\":\"1800.23256\",\"InvestType\":null,\"KeyNo\":\"p7b2910ff68fb050643afd9041b5ac5d\",\"ShoudDate\":null,\"IdentifyType\":\"非公示项\",\"CapiDate\":null,\"StockType\":\"自然人股东\",\"IdentifyNo\":null,\"RealCapi\":null,\"InvestName\":null},{\"Org\":2,\"StockName\":\"黄文佳\",\"HasImage\":false,\"StockPercent\":\"27.36%\",\"CompanyCount\":1,\"ShouldCapi\":\"1509.94368\",\"InvestType\":null,\"KeyNo\":\"pa87c18b55c6dac48c2db0814d9f263a\",\"ShoudDate\":null,\"IdentifyType\":\"非公示项\",\"CapiDate\":null,\"StockType\":\"自然人股东\",\"IdentifyNo\":null,\"RealCapi\":null,\"InvestName\":null},{\"Org\":2,\"StockName\":\"黄卿乐\",\"HasImage\":false,\"StockPercent\":\"18.28%\",\"CompanyCount\":15,\"ShouldCapi\":\"1008.83664\",\"InvestType\":null,\"KeyNo\":\"p6d4900287ee26772d6dc65e53f177fb\",\"ShoudDate\":null,\"IdentifyType\":\"非公示项\",\"CapiDate\":null,\"StockType\":\"自然人股东\",\"IdentifyNo\":null,\"RealCapi\":null,\"InvestName\":null},{\"Org\":2,\"StockName\":\"黄卿仕\",\"HasImage\":false,\"StockPercent\":\"10.87%\",\"CompanyCount\":2,\"ShouldCapi\":\"599.89356\",\"InvestType\":null,\"KeyNo\":\"pab1821f8bc755e73919b1f2f11c2f9d\",\"ShoudDate\":null,\"IdentifyType\":\"非公示项\",\"CapiDate\":null,\"StockType\":\"自然人股东\",\"IdentifyNo\":null,\"RealCapi\":null,\"InvestName\":null},{\"Org\":2,\"StockName\":\"黄文博\",\"HasImage\":true,\"StockPercent\":\"10.87%\",\"CompanyCount\":12,\"ShouldCapi\":\"599.89356\",\"InvestType\":null,\"KeyNo\":\"pc571c3aefb987345e062961b4369e98\",\"ShoudDate\":null,\"IdentifyType\":\"非公示项\",\"CapiDate\":null,\"StockType\":\"自然人股东\",\"IdentifyNo\":null,\"RealCapi\":null,\"InvestName\":null}]\n" +
                "006f1aaf77963044d9cdcd66e980e855\n" +
                "北京首航波纹管制造有限公司\n" +
                "{\"KeyNo\":\"p7b2910ff68fb050643afd9041b5ac56\",\"Org\":2,\"OperType\":1,\"HasImage\":false,\"CompanyCount\":4,\"Name\":\"黄文革\"}\n" +
                "[{\"KeyNo\":\"p7b2910ff68fb050643afd9041b5ac5d\",\"No\":0,\"HasImage\":false,\"CompanyCount\":4,\"ScertName\":null,\"Job\":\"执行董事\",\"CerNo\":null,\"Name\":\"黄文革\"},{\"KeyNo\":\"p437fb7470b3ce7c8c5583950063d7d3\",\"No\":0,\"HasImage\":false,\"CompanyCount\":2,\"ScertName\":null,\"Job\":\"经理\",\"CerNo\":null,\"Name\":\"鲍兴连\"},{\"KeyNo\":\"p21d17b695ad3c02bed16e44879afc2c\",\"No\":0,\"HasImage\":false,\"CompanyCount\":5,\"ScertName\":null,\"Job\":\"监事\",\"CerNo\":null,\"Name\":\"黄卿河\"}]\n" +
                "[{\"Org\":2,\"StockName\":\"黄文革\",\"HasImage\":false,\"StockPercent\":\"32.62%\",\"CompanyCount\":4,\"ShouldCapi\":\"1800.23256\",\"InvestType\":null,\"KeyNo\":\"p7b2910ff68fb050643afd9041b5ac5d\",\"ShoudDate\":null,\"IdentifyType\":\"非公示项\",\"CapiDate\":null,\"StockType\":\"自然人股东\",\"IdentifyNo\":null,\"RealCapi\":null,\"InvestName\":null},{\"Org\":2,\"StockName\":\"黄文佳\",\"HasImage\":false,\"StockPercent\":\"27.36%\",\"CompanyCount\":1,\"ShouldCapi\":\"1509.94368\",\"InvestType\":null,\"KeyNo\":\"pa87c18b55c6dac48c2db0814d9f263a\",\"ShoudDate\":null,\"IdentifyType\":\"非公示项\",\"CapiDate\":null,\"StockType\":\"自然人股东\",\"IdentifyNo\":null,\"RealCapi\":null,\"InvestName\":null},{\"Org\":2,\"StockName\":\"黄卿乐\",\"HasImage\":false,\"StockPercent\":\"18.28%\",\"CompanyCount\":15,\"ShouldCapi\":\"1008.83664\",\"InvestType\":null,\"KeyNo\":\"p6d4900287ee26772d6dc65e53f177fb\",\"ShoudDate\":null,\"IdentifyType\":\"非公示项\",\"CapiDate\":null,\"StockType\":\"自然人股东\",\"IdentifyNo\":null,\"RealCapi\":null,\"InvestName\":null},{\"Org\":2,\"StockName\":\"黄卿仕\",\"HasImage\":false,\"StockPercent\":\"10.87%\",\"CompanyCount\":2,\"ShouldCapi\":\"599.89356\",\"InvestType\":null,\"KeyNo\":\"pab1821f8bc755e73919b1f2f11c2f9d\",\"ShoudDate\":null,\"IdentifyType\":\"非公示项\",\"CapiDate\":null,\"StockType\":\"自然人股东\",\"IdentifyNo\":null,\"RealCapi\":null,\"InvestName\":null},{\"Org\":2,\"StockName\":\"黄文博\",\"HasImage\":true,\"StockPercent\":\"10.87%\",\"CompanyCount\":12,\"ShouldCapi\":\"599.89356\",\"InvestType\":null,\"KeyNo\":\"pc571c3aefb987345e062961b4369e98\",\"ShoudDate\":null,\"IdentifyType\":\"非公示项\",\"CapiDate\":null,\"StockType\":\"自然人股东\",\"IdentifyNo\":null,\"RealCapi\":null,\"InvestName\":null}]\n" +
                "partner";
        Object[] obj = new Object[str.split("\\n").length];
        for(int i=0;i<str.split("\\n").length;i++){
            obj[i]=str.split("\\n")[i];
        }
        new Person_RadarUDTF().process(obj);


    }
}