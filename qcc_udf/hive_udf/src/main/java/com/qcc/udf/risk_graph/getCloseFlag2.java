package com.qcc.udf.risk_graph;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * @Auther: liulh
 * @Date: 2020/6/2 20:33
 * @Description:
 */
public class getCloseFlag2 extends UDF {
    public static String  evaluate(String lawPaperType, String lawSuitResultNew, String lawJudgeResult, String fmtContent) {
        if (StringUtils.isNotBlank(lawPaperType) && lawPaperType.contains("结案")) {
            return lawPaperType;
        }
        // 逻辑1 / 逻辑2
        if (StringUtils.isNotBlank(lawJudgeResult)) {
            if (lawJudgeResult.contains("结案") || lawJudgeResult.contains("执行完毕") || lawJudgeResult.startsWith("不予执行") || lawJudgeResult.startsWith("终结")) {
                return (StringUtils.isNotBlank(lawPaperType) ? lawPaperType + "（结案）" : "结案通知书");
            }
        }
        // 逻辑3
        if (StringUtils.isNotBlank(lawSuitResultNew)) {
            if (lawSuitResultNew.contains("\"5\"") || lawSuitResultNew.contains("\"6\"")
                    || lawSuitResultNew.contains("\"10\"") || lawSuitResultNew.contains("\"14\"")) {
                return (StringUtils.isNotBlank(lawPaperType) ? lawPaperType + "（结案）" : "结案通知书");
            }
        }

        if(StringUtils.isNotEmpty(fmtContent) && fmtContent.length() <= 57 && fmtContent.contains("结案")){
            return (StringUtils.isNotBlank(lawPaperType) ? lawPaperType + "（结案）" : "结案通知书");
        }

        if (StringUtils.isNotEmpty(lawJudgeResult)){
            if(lawJudgeResult.contains("终结") && StringUtils.isNotEmpty(lawPaperType) && lawPaperType.contains("执行裁定书")){
                return (StringUtils.isNotBlank(lawPaperType) ? lawPaperType + "（结案）" : "结案通知书");
            }
        }

        return lawPaperType == null ? "" : lawPaperType;
    }

    public static void main(String[] args) {
        System.out.println(evaluate("", "","[]",
                "<div><center><h1>不公开理由：以调解方式结案的</h1></center></div>"));
    }
}
