package com.qcc.udf.company;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;

public class GetCompanyNameFromArrayV2 extends UDF {
    public String evaluate(String input, String keyWord) {
        List<String> result = new ArrayList<>();
        if (StringUtils.isNotEmpty(input)) {
            try {
                JSONArray jsonArray = JSON.parseArray(input);
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    String value = jsonObject.getOrDefault(keyWord, "").toString();
                    if (StringUtils.isNotEmpty(value)) {
                        result.add(value);
                    }
                }
            } catch (Exception e) {

            }
        }
        if (CollectionUtils.isEmpty(result)) {
            return "";
        } else {
            return String.join(",", result);
        }
    }
}
