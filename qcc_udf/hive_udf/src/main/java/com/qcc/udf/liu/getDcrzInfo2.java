package com.qcc.udf.liu;

import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.cpws.CommonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

public class getDcrzInfo2 extends UDF {

    public static String evaluate(List<String> infoList)  throws Exception{
        JSONObject result = new JSONObject();
        String priority = "3";
        String dataStatus = "1";

        if(CollectionUtils.isNotEmpty(infoList)){
            long maxDate = 0L;
            long maxRegDate = 0L;
            Set<Long> dateSet = new LinkedHashSet<>();
            JSONObject maxInfo = null;
            JSONObject maxRegInfo = null;
            for (String str : infoList){
                JSONObject json = JSONObject.parseObject(str);
                String createDate = json.getString("createdate");
                if (CommonUtil.parseDateToTimeStamp(createDate) > maxDate){
                    maxDate = CommonUtil.parseDateToTimeStamp(createDate);
                    maxInfo = json;
                }
                String regDate = json.getString("regdate");
                if (CommonUtil.parseDateToTimeStamp(regDate) > maxRegDate){
                    maxRegDate = CommonUtil.parseDateToTimeStamp(regDate);
                    maxRegInfo = json;
                }
                dateSet.add(CommonUtil.parseDateToTimeStamp(json.getString("regdate")));
            }

            if (StringUtils.isEmpty(maxInfo.getString("businesstype")) && StringUtils.isEmpty(maxInfo.getString("regdate")) && "1".equals(maxInfo.getString("datastatus"))){
                priority = "3";
                dataStatus = maxInfo.getString("datastatus");
            }else{
                long today = System.currentTimeMillis()/1000;
                // 最近一周
                Set<Long> dateSet7 = new LinkedHashSet<>();
                Set<Long> dateSet7_30 = new LinkedHashSet<>();
                Set<Long> dateSet30_180 = new LinkedHashSet<>();
                Set<Long> dateSet180 = new LinkedHashSet<>();
                for (Long date : dateSet){
                    if ((today - date) <= (7*86400)){
                        dateSet7.add(date);
                    }
                    if ((today - date) > (7*86400) && (today - date) <= (30*86400)){
                        dateSet7_30.add(date);
                    }
                    if ((today - date) > (30*86400) && (today - date) <= (180*86400)){
                        dateSet30_180.add(date);
                    }
                    if ((today - date) > (180*86400)){
                        dateSet180.add(date);
                    }
                }

                // t-7=<regdate（最新一次请求），且近1个月有多笔，优先级为1，t-7=<regdate（进一个月只有本周有融资记录），或近1个月有多笔（进7天无融资记录）， 优先级为2 ，否则进行下一步
                if (dateSet7.size() > 0 && dateSet7_30.size() > 0){
                    priority = "1";
                }
                if (dateSet7.size() > 0 && dateSet7_30.size() == 0){
                    priority = "2";
                }
                /*if (dateSet7.size() == 0 && dateSet7_30.size() > 0){
                    priority = "2";
                }*/
                if (priority.equals("3")){
                    if (dateSet7_30.size() > 0 && dateSet30_180.size() > 0){
                        priority = "1";
                    }
                    if (dateSet7_30.size() > 0 && dateSet30_180.size() == 0){
                        priority = "2";
                    }
                    if (dateSet7_30.size() == 0 && dateSet30_180.size() > 0){
                        priority = "2";
                    }
                    if (priority.equals("3")){
                        if (dateSet30_180.size() > 0){
                            priority = "2";
                        }
                    }
                }

                dataStatus = maxInfo.getString("datastatus");
            }
        }

        result.put("Priority", priority);
        result.put("DataStatus", dataStatus);


        return result.toString();
    }


    public static void main(String[] args) {
        try {
            List<String> infoList = new LinkedList<>();
            infoList.add("{\"id\":\"ad0c421e5f0c4acb896907ec4106a325\",\"createdate\":\"2022-11-09 02:28:35.0\",\"regdate\":\"2022-11-03 20:55:44\",\"businesstype\":\"应收账款转让（保理）\",\"datastatus\":\"1\",\"regno\":\"20037079002480615124\"}");
            infoList.add("{\"id\":\"a2c0125e70404610a851d1293bf13af4\",\"createdate\":\"2022-11-09 02:28:36.0\",\"regdate\":\"2022-10-14 12:14:35\",\"businesstype\":\"应收账款转让（保理）\",\"datastatus\":\"1\",\"regno\":\"19709829002424553961\"}");
            infoList.add("{\"id\":\"8c39999e07934224918162614bff73aa\",\"createdate\":\"2022-11-09 02:28:36.0\",\"regdate\":\"2022-10-12 09:54:30\",\"businesstype\":\"应收账款转让（保理）\",\"datastatus\":\"1\",\"regno\":\"19669117002418028461\"}");

            System.out.println(evaluate(infoList));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
