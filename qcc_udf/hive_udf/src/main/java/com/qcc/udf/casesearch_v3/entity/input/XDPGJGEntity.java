package com.qcc.udf.casesearch_v3.entity.input;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.casesearch_v3.enums.CaseCategoryEnum;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 选定评估机构UDF入参
 */
@Data
public class XDPGJGEntity extends BaseCaseEntity {
    /**
     * {
     *     "id":"007bc8ac126856f1999c425fb17528bb",
     *     "companynames":"ed809e9b530bfc93122dff537273b621,新郑市圣大路易服饰有限公司",
     *     "anno":"（2020）豫0102执恢163号",
     *     "provincecode":"HEN",
     *     "courtname":"河南省郑州市中原区人民法院",
     *     "lotterydate":"1599753600",
     *     "isvalid":"1",
     *     "keynoaarrayowner":"[{"KeyNo":"ed809e9b530bfc93122dff537273b621","Org":0,"Name":"新郑市圣大路易服饰有限公司"}]",
     *     "subjectclass":"土地使用权",
     *     "subjectname":"新村镇裴李岗路北段",
     *     "subjectsubclass":"建设用地使用权",
     *     "keynoarrayagency":"[{"KeyNo":"8c4dab758fd41645625c1d1c621378a3","Org":0,"Name":"河南光明土地评估咨询有限责任公司"},{"KeyNo":"75a09aeab37327b95d0ff4d90fe335b1","Org":0,"Name":"河南凯业房地产估价咨询有限公司"},{"KeyNo":"c0f03ebfa7f18f691868037fa196f61c","Org":0,"Name":"河南龙源土地评估咨询有限公司"}]",
     *     "companynamesowner":"ed809e9b530bfc93122dff537273b621,新郑市圣大路易服饰有限公司"
     * }
     */

    private String id;

    private String companynames;

    private String anno;

    private String provincecode;

    private String courtname;

    /**
     * 摇号日期
     */
    private long lotterydate;

    private int isvalid;
    /**
     *当事人array
     */
    private String keynoaarrayowner;
    /**
     *标的物类型
     */
    private String subjectclass;
    /**
     *标的物名称
     */
    private String subjectname;
    /**
     *标的物二级类型
     */
    private String subjectsubclass;
    /**
     *评估机构array
     */
    private String keynoarrayagency;
    /**
     *当事人搜索词
     */
    private String companynamesowner;


    private List<NameAndKeyNoEntity> keynoaarrayownerEntityList;

    /**
     *当事人array
     */
    private List<NameAndKeyNoEntity> keynoarrayagencyEntityList;



    public static List<XDPGJGEntity> convert(List<String> jsonList) {
        List<XDPGJGEntity> list = new ArrayList<>();
        XDPGJGEntity entity = null;
        if(CollectionUtils.isEmpty(jsonList)){
            return list;
        }
        if(CollectionUtils.isEmpty(jsonList)){
            return list;
        }
        for (String json : jsonList) {
            if(Strings.isNullOrEmpty(json)){
                continue;
            }
            entity = JSON.parseObject(json, XDPGJGEntity.class);
            if(entity == null  || Strings.isNullOrEmpty(entity.getId())){
                continue;
            }


            String str = entity.getKeynoaarrayowner();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setKeynoaarrayownerEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getKeynoaarrayownerEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                }
            }

            str = entity.getKeynoarrayagency();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setKeynoarrayagencyEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getKeynoarrayagencyEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                }
            }

            //公共字段赋值
            entity.setBaseCaseNo(entity.getAnno());
            entity.setBaseCaseCategoryEnum(CaseCategoryEnum.XDPGJG);
            entity.setBaseCourt(entity.getCourtname());
            if(!Strings.isNullOrEmpty(entity.getCompanynames())){
                entity.setBaseSearchWordSet(Arrays.stream(entity.getCompanynames().split(","))
                        .collect(Collectors.toSet()));
            }

            entity.setBaseProvinceCode(entity.getProvincecode());
            entity.setBaseNameKeyNoList(entity.getKeynoaarrayownerEntityList());
            entity.setBaseId(entity.getBaseCaseCategoryEnum().getType()+"_"+entity.getId());

            String caseType= CommonV3Util.getCaseType(CommonV3Util.getCaseNo(entity.getBaseCaseNo()));
            //案件类型为空的数据直接过滤
            if(Strings.isNullOrEmpty(caseType)){
                continue;
            }
            list.add(entity);
        }
        return list;
    }

}
