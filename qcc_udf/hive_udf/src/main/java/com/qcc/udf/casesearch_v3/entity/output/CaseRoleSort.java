package com.qcc.udf.casesearch_v3.entity.output;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class CaseRoleSort {
    @JSONField(name = "R")
    private String role;
    @JSONField(name = "T")
    private String trialRound;
    /**
     * 判决结果
     */
    @JSONField(name = "LR")
    private String lawsuitResult;

    /**
     * 判决结果v2
     */
    @JSONField(name = "JR")
    private String lawsuitResultV2;

    @JSONField(serialize = false)
    private long timeStamp;
}
