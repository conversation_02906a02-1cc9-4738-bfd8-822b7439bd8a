package com.qcc.udf.company;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2022-10-14 10:56
 */
public class getCompanyLevelDivide extends UDF {
    /**
     * 获取企业划分
     *
     * @param companyType      根据财务数据计算的企业规模
     * @param registCapi       注册资本
     * @param isSm             是否为小微企业
     * @param isFin            是否为金融企业
     * @param hasFinancialData 是否为有公司财务数据
     * @param econKind         企业类型
     * @param startDate        成立日期
     * @param isZjtx           是否是专精特新中小企业、专精特新小巨人企业、科技型中小企业
     * @return
     */
    public String evaluate(String companyType, double registCapi, int isSm, int isFin, int hasFinancialData, String econKind, String startDate, int isZjtx) {
        //根据企业主页下的财务数据计算的结果直接使用
        if (hasFinancialData == 1 && StringUtils.isNotEmpty(companyType)) {
            return companyType;
        }

        //金融行业
        if (isFin == 1) {
            if (StringUtils.isNotEmpty(companyType)) {
                return companyType;
            } else {
                if (isSm == 1) {
                    if (registCapi > 100000 || isZjtx == 1) {
                        return "小型企业";
                    } else {
                        return "微型企业";
                    }
                } else {
                    if (registCapi > 10000000) {
                        return "大型企业";
                    } else if (registCapi > 1000000) {
                        return "中型企业";
                    } else if (registCapi > 100000 || isZjtx == 1) {
                        return "小型企业";
                    } else {
                        return "微型企业";
                    }
                }
            }
        }

        String result = "";
        //小微企业
        if (isSm == 1) {
            if ("大型企业".equals(companyType) || "中型企业".equals(companyType) || isZjtx == 1) {
                result = "小型企业";
            } else if ("小型企业".equals(companyType) || "微型企业".equals(companyType)) {
                result = companyType;
            } else if (registCapi > 100) {
                result = "小型企业";
            } else if (registCapi <= 100) {
                result = "微型企业";
            }
        } else {
            if ("大型企业".equals(companyType)) {
                if (StringUtils.isNotEmpty(econKind) && Pattern.matches(".*(个人独资|自然人独资|合作社|专业合作|全民所有|集体所有|集体企业|集体经济).*", econKind)) {
                    result = "";
                } else if (registCapi <= 10000) {
                    result = "中型企业";
                }
            } else if (isZjtx == 1 && "微型企业".equals(companyType)) {
                result = "小型企业";
            } else if (StringUtils.isNotEmpty(companyType)) {
                result = companyType;
            }
        }

        if (StringUtils.isEmpty(result)) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            if (registCapi > 10000 && StringUtils.isNotEmpty(startDate) && startDate.compareTo(df.format(LocalDateTime.now().minusYears(1))) < 0) {
                result = "大型企业";
            } else if (registCapi > 500) {
                result = "中型企业";
            } else if (registCapi > 100 || isZjtx == 1) {
                result = "小型企业";
            } else {
                result = "微型企业";
            }
        }

        return result;
    }

    public static void main(String[] args) {
        getCompanyLevelDivide model = new getCompanyLevelDivide();
        String result = model.evaluate("", 68000, 0, 0, 0, "有限责任公司（法人独资）", "2011-03-11", 1);
    }
}
