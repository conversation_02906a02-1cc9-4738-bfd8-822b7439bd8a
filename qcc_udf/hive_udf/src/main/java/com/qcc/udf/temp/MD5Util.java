package com.qcc.udf.temp;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @desc
 * @date 2021/2/9
 */
public class MD5Util {
    private static final char[] hexDigits = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    private MD5Util() {
    }

    public static String encode(String content) {
        String result = "";
        if (content != null) {
            try {
                MessageDigest md = MessageDigest.getInstance("MD5");
                byte[] source = content.getBytes("utf-8");
                md.update(source);
                byte[] tmp = md.digest();
                char[] str = new char[32];
                int i = 0;

                for(int var7 = 0; i < 16; ++i) {
                    byte b = tmp[i];
                    str[var7++] = hexDigits[b >>> 4 & 15];
                    str[var7++] = hexDigits[b & 15];
                }

                result = new String(str);
            } catch (UnsupportedEncodingException | NoSuchAlgorithmException var9) {
            }
        }

        return result;
    }

    public static void main(String[] args) {

    }
}
