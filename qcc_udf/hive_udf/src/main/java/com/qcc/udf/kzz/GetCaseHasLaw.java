package com.qcc.udf.kzz;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 提取当事人的律所
 * <AUTHOR>
 * @date 2022/4/21
 */
public class GetCaseHasLaw extends UDF {
    public static String evaluate(String lawInfo, String keyNo) {
        String result = "";
        try {
            if (StringUtils.isNotBlank(lawInfo) && StringUtils.isNotBlank(keyNo)) {
                JSONArray array = JSONArray.parseArray(lawInfo);
                List<String> list = new ArrayList<>();
                if (!array.isEmpty()) {
                    for (Object obj : array) {
                        JSONObject jsonObject = (JSONObject) obj;
                        String targetKeyNo = jsonObject.getString("KeyNo");
                        JSONArray lawArray = jsonObject.getJSONArray("LawFirmList");
                        if (keyNo.equals(targetKeyNo) && !lawArray.isEmpty()) {
                            for (Object lawObj : lawArray) {
                                JSONObject law = (JSONObject) lawObj;
                                String lawKeyNo = law.getString("LawFirm_KeyNo");
                                if(StringUtils.isNotBlank(lawKeyNo)){
                                    list.add(lawKeyNo);
                                }
                            }
                        }
                    }
                }
                if(CollectionUtils.isNotEmpty(list)){
                    result = list.stream().collect(Collectors.joining(","));
                }
            }

        } catch (Exception e) {

        }
        return result;
    }

//    public static void main(String[] args) {
//        String content = "[\n" +
//                "  {\n" +
//                "    \"Role\": \"原告\",\n" +
//                "    \"KeyNo\": \"3e0326925ce2c0ed7a3a36f8dd62bada\",\n" +
//                "    \"LawFirmList\": [\n" +
//                "      {\n" +
//                "        \"LawFirm_KeyNo\": \"w85716745f9f0f14971fa1ca728bd72c\",\n" +
//                "        \"LawFirm_Name\": \"辽宁宫丹律师事务所\",\n" +
//                "        \"LawyerList\": [\n" +
//                "          {\n" +
//                "            \"Lawyer_Name\": \"包容华\",\n" +
//                "            \"Lawyer_Role\": \"委托诉讼代理人\",\n" +
//                "            \"Lawyer_KeyNo\": \"6cc4e11d40f8e2791c8c7ea444b73baa\"\n" +
//                "          },\n" +
//                "          {\n" +
//                "            \"Lawyer_Name\": \"王继生\",\n" +
//                "            \"Lawyer_Role\": \"委托诉讼代理人\",\n" +
//                "            \"Lawyer_KeyNo\": \"3b0553f9a8c8be488e4414e0cb67e8a0\"\n" +
//                "          }\n" +
//                "        ]\n" +
//                "      }\n" +
//                "    ],\n" +
//                "    \"RoleTag\": 0,\n" +
//                "    \"Name\": \"营口佳壹润法律咨询有限公司\"\n" +
//                "  },\n" +
//                "  {\n" +
//                "    \"Role\": \"被告\",\n" +
//                "    \"KeyNo\": \"\",\n" +
//                "    \"LawFirmList\": [],\n" +
//                "    \"RoleTag\": 1,\n" +
//                "    \"Name\": \"高金官\"\n" +
//                "  }\n" +
//                "]";
//        String region = evaluate(content,"3e0326925ce2c0ed7a3a36f8dd62bada");
//        System.out.println(region);
//    }
}
