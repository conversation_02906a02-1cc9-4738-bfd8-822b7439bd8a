package com.qcc.udf.cpws.casereason;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.io.Resources;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.IOException;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 匹配Excel中的code，读取标签
 *
 * <AUTHOR>
 */
public class MatchExcelCode2 extends UDF {

    public static void main(String[] args) throws Exception {

        String code1 = "B,B05,B0501";
        String code2 = "B,B09,B0910";
        String excelList = evaluate(code1);

        System.out.println(excelList);


    }

    /**
     * 读取excel，放入map中并缓存，程序结束缓存失效
     */
    public static String evaluate(String code) throws IOException {
        System.out.println("-----------------------------------------------1");
        JSONArray array = new JSONArray();
        JSONObject json1 = new JSONObject();
        json1.put("json1","1");
        array.add(json1);
        Map<String, List<ExcelObj>> collect = new LinkedHashMap<>();
        List<ExcelObj> listRisk = null;

        // String reloadPath = "C:\\Users\\<USER>\\Desktop\\案由分类.xlsx";
        URL resource = Resources.getResource("case_reason_sort.xlsx");
        String path = resource.getPath();
        System.out.println("-----------------------------------------------path:" + path);
        json1 = new JSONObject();
        json1.put("json2",path);
        array.add(json1);

        //1.获取工作簿
        XSSFWorkbook workbook = new XSSFWorkbook(path);
        List<ExcelObj> list = new ArrayList<>();
        System.out.println("-----------------------------------------------workbook:" + workbook.getNumberOfSheets());
        json1 = new JSONObject();
        json1.put("json3",workbook.getNumberOfSheets());
        array.add(json1);
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            //2.获取工作表
            XSSFSheet sheet = workbook.getSheetAt(i);
            if (sheet == null) {
                continue;
            }
            String sheetName = sheet.getSheetName();
            //获取行
            XSSFRow headRow = sheet.getRow(0);
            //因为excel存在删除的历史数据，导致sheet不止14个，所以需要把不符合的sheet过滤掉
            if (headRow == null || !Objects.equals(headRow.getCell(0).getStringCellValue(), "标签")) {
                continue;
            }
            //去掉第一行，所以从1开始
            String historyFirst = "", historySecond = "";
            for (int row = 1; row < sheet.getLastRowNum() + 1; row++) {
                XSSFRow sheetRow = sheet.getRow(row);
                if (sheetRow == null) {
                    continue;
                }
                //如果没有id，直接下一个
                XSSFCell idCell = sheetRow.getCell(3);
                if (idCell == null || StringUtils.isEmpty(idCell.getStringCellValue())) {
                    continue;
                }
                XSSFCell firstCell = sheetRow.getCell(0);
                String first = firstCell == null ? historyFirst : firstCell.getStringCellValue();
                if (StringUtils.isEmpty(first)) {
                    first = historyFirst;
                } else {
                    historyFirst = first;
                }
                XSSFCell secondCell = sheetRow.getCell(1);
                String second = "";
                if (secondCell == null) {
                    second = "-";
                } else {
                    second = secondCell.getStringCellValue();
                }
                if ("-".equals(second)) {
                    second = "";
                    historySecond = "";
                } else if (StringUtils.isEmpty(second)) {
                    second = historySecond;
                } else {
                    historySecond = second;
                }
                ExcelObj obj = new ExcelObj();
                obj.setFirstLabel(first);
                obj.setSecondLabel(second);
                obj.setId(idCell.getStringCellValue());
                obj.setSheetName(sheetName);
                list.add(obj);
            }
        }
        //根据id分组
        collect = list.stream().collect(Collectors.groupingBy(it -> it.id));

        listRisk = collect.get(code);


        System.out.println("-----------------------------------------------listRisk:" + listRisk);
        json1 = new JSONObject();
        json1.put("json4",listRisk.toString());
        array.add(json1);

        if (listRisk != null && !listRisk.isEmpty()){
            for (ExcelObj item : listRisk){
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(item));
                array.add(jsonObject);
            }
        }


        System.out.println("-----------------------------------------------array:" + array);
        json1 = new JSONObject();
        json1.put("json5",array.toString());
        array.add(json1);
        return array.toString();
    }

    @Data
    public static class ExcelObj {
        // 第一级标签名
        private String firstLabel;
        // 第二级标签名
        private String secondLabel;
        // 案由code
        private String id;
        // 页脚名称
        private String sheetName;
    }
}
