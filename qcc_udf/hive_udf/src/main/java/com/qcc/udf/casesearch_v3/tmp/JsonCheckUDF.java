package com.qcc.udf.casesearch_v3.tmp;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.qcc.udf.casesearch_v3.entity.input.KTGGEntity;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2023/02/15 14:08
 * @description ：
 */
public class JsonCheckUDF extends UDF {
    public static String evaluate(String id, String message) {
        try {
            KTGGEntity entity = JSON.parseObject(message, KTGGEntity.class);
            String str = entity.getNameandkeyno();
            Map<String,String> nameRoleMap = new HashMap<>();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setNameandkeynoEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getNameandkeynoEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                    namekey.setLawFirmList(null);
                    nameRoleMap.put(namekey.getName(),namekey.getRole());
                }
            }

            str = entity.getProsecutorlist();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setProsecutorlistoEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getProsecutorlistoEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                    namekey.setRole(nameRoleMap.getOrDefault(namekey.getName(),""));
                    namekey.setLawFirmList(null);
                }
            }

            str = entity.getDefendantlist();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setDefendantlistoEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getDefendantlistoEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                    namekey.setRole(nameRoleMap.getOrDefault(namekey.getName(),""));
                    namekey.setLawFirmList(null);
                }
            }
        } catch (Exception e) {
            return "0";
        }

        return "1";
    }

    public static void main(String[] args) {
        String id = "79d3d0653c70784b80dc362e0b954beb";
        String message = "{\"id\":\"79d3d0653c70784b80dc362e0b954beb\",\"anno\":\"（2019）新3022刑初362号\",\"provincecode\":\"XJ\",\"companynames\":\"温振亮\",\"isvalid\":\"0\",\"liandate\":1576141200,\"casereason\":\"危险驾驶罪\",\"courtname\":\"新疆维吾尔自治区克孜勒苏柯尔克孜自治州阿克陶县人民法院\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Name\\\":\\\"温振亮\\\",\\\"Org\\\":-1,\\\"Role\\\":\\\"被告\\\",\\\"RoleTag\\\":1,\\\"RoleType\\\":21,\\\"ShowName\\\":\\\"温**\\\",\\\"Source\\\":0}]\",\"prosecutorlist\":\"[]\",\"defendantlist\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Name\\\":\\\"温振亮\\\",\\\"Org\\\":-1,\\\"ShowName\\\":\\\"温**\\\"}]\",\"executeunite\":\"第一审判法庭\"}";

        String output = new JsonCheckUDF().evaluate(id, message);
        System.out.println(output);
    }
}
