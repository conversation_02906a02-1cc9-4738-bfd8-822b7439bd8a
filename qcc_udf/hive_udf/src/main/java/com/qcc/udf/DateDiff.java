package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.text.SimpleDateFormat;
import java.util.Date;

/*

此UDF函数 是为了修正hive中的datediff函数而编写的.hive版本中目前存在的datediff函数 存在bug 数据相减 格式不正确
date1 是起始日期
date2 为终止日期
函数 实现 date1-date2
 */
public class DateDiff extends UDF {

    public Integer evaluate(String date1, String date2) {

        try {
            Date startDate = new SimpleDateFormat("yyyy-MM-dd").parse(date2);
            Date endDate = new SimpleDateFormat("yyyy-MM-dd").parse(date1);
            long diffTime = endDate.getTime() - startDate.getTime();
            Integer diffDays = Math.toIntExact(diffTime / (1000 * 60 * 60 * 24));
            return diffDays;
        } catch (Exception e) {
            //e.printStackTrace();
            return null;
        }

    }


}
