package com.qcc.udf.casesearch_v3.role;

import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 检查当事人身份
 */
public class CheckPartyRoleUtil {
    private final static Map<String, String> partyRoleMap;
    static {
        partyRoleMap = new LinkedHashMap<>();
        try {
            Map<String, String> map = new HashMap<>();
            try (InputStream is = CheckPartyRoleUtil.class.getResourceAsStream("/PartyRole.csv")) {
                BufferedReader br = new BufferedReader(new InputStreamReader(is));
                String line;
                while ((line = br.readLine()) != null) {
                    String[] splits = line.split("\t");
                    if (splits != null) {
                        String role = splits[0].trim();
                        partyRoleMap.put(role, role);
                    }
                }
                br.close();
            }
        } catch (Exception ex) {
        }
    }

    public static String checkRole(String role){
        if (StringUtils.isBlank(role)){
            return "";
        }
        String roleName = partyRoleMap.get(role);
        if (StringUtils.isBlank(roleName)){
            return "";
        }
        return roleName;
    }


    public static void main(String[] args) {
        String role = "被告";
        System.out.println(checkRole(role));
    }

}
