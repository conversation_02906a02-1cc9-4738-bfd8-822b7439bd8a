package com.qcc.udf.risk_analysis;

import com.qcc.udf.risk_analysis.entity.RoundAndIndexDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date ：Created in 2021/03/03 13:34
 * @description ：汇总去重后id的个数
 */
public class SortShortTrialRoundUDF extends UDF {

    public static String evaluate(String param, String splitWord) {
        String result = "";
        if (StringUtils.isNotEmpty(param)) {
            List<RoundAndIndexDto> list = new ArrayList<>();
            String[] strings = param.split(splitWord);
            List<String> hasUsed = new ArrayList<>();
            for(String item :strings) {
                if (StringUtils.isNotEmpty(item) && !hasUsed.contains(item)) {
                    list.add(getSortIndex(item));
                    hasUsed.add(item);
                }
            }
            if (CollectionUtils.isNotEmpty(list)) {
                result = list.stream()
                        .sorted(Comparator.comparing(RoundAndIndexDto::getSortIndex)).map(e -> e.getRoundName()).collect(Collectors.joining(","));
            }
        }
        return result;
    }

    private static RoundAndIndexDto getSortIndex(String round) {
        RoundAndIndexDto result = new RoundAndIndexDto();
        int index = 12;
        if (round.contains("申请再审审查")){
            index = 6;
        } else if(round.contains("依职权再审审查")){
            index = 7;
        } else if(round.contains("审判监督")){
            index = 8;
        } else if(round.contains("特别程序监督")){
            index = 9;
        } else if(round.contains("抗诉再审审查")){
            index = 10;
        } else if(round.contains("管辖上诉")){
            index = 2;
        } else if(round.contains("管辖")){
            index = 1;
        }  else if(round.contains("一审")){
            index = 3;
        } else if(round.contains("二审")){
            index = 4;
        } else if(round.contains("再审")){
            index = 5;
        } else if(round.contains("复核")){
            index = 11;
        }
        result.setRoundName(round);
        result.setSortIndex(index);
        return result;
    }

    public static void main(String[] args) {
        String roles = "一审上诉人,一审上诉人";
        System.out.println(evaluate(roles, ","));

    }
}
