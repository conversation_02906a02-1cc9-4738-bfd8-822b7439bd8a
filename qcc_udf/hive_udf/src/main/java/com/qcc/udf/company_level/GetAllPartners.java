package com.qcc.udf.company_level;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * 获取普通股东和是大股东
 */
@Description(name = "GetAllPartners", value = "_FUNC_(String partners, String topten, String keyNo); - Return  普通股东和十大股东合并去重，以十大股东优先。传keyno表示股东不能是自己")
public class GetAllPartners extends UDF {

    public static void main(String[] args) throws Exception {
        String aa = "[{\"Org\":0,\"StockName\":\"成都展业投资有限公司\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"52.26%\",\"CompanyCount\":6,\"ShouldCapi\":\"37180428\",\"Source\":3,\"InvestType\":null,\"KeyNo\":\"ed19ff6645634dc6e3bc6d6a6a6773fe\",\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":2,\"StockName\":\"邓富学\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"8.43%\",\"CompanyCount\":14,\"ShouldCapi\":\"5999340\",\"Source\":3,\"InvestType\":null,\"KeyNo\":\"p42895683f19024547ce2951036b181d\",\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":2,\"StockName\":\"梁华\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"5.66%\",\"CompanyCount\":2,\"ShouldCapi\":\"4030000\",\"Source\":3,\"InvestType\":null,\"KeyNo\":\"pdd4cada4b5d7225dbea957db6912cb5\",\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":2,\"StockName\":\"石琳\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"4.86%\",\"CompanyCount\":3,\"ShouldCapi\":\"3458365\",\"Source\":3,\"InvestType\":null,\"KeyNo\":\"p3a41c6fc838ab277f9e2592d8fb5716\",\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":2,\"StockName\":\"张煜\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"4.11%\",\"CompanyCount\":9,\"ShouldCapi\":\"2923775\",\"Source\":3,\"InvestType\":null,\"KeyNo\":\"p3ed5143f907159c81645f221a710a6f\",\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":2,\"StockName\":\"陈玉民\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"3.65%\",\"CompanyCount\":1,\"ShouldCapi\":\"2600374\",\"Source\":3,\"InvestType\":null,\"KeyNo\":\"pda7e406f2eda47556950e5bfc7d5d8b\",\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":2,\"StockName\":\"罗苇\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"3.53%\",\"CompanyCount\":6,\"ShouldCapi\":\"2514575\",\"Source\":3,\"InvestType\":null,\"KeyNo\":\"p963be238d99de12c55a61b71a905e15\",\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":2,\"StockName\":\"劳靖华\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"2.88%\",\"CompanyCount\":13,\"ShouldCapi\":\"2045980\",\"Source\":3,\"InvestType\":null,\"KeyNo\":\"pa93107fb211e0b5bacf61436ed52540\",\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":-2,\"StockName\":\"马红菊\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"2.56%\",\"CompanyCount\":0,\"ShouldCapi\":\"1822000\",\"Source\":3,\"InvestType\":null,\"KeyNo\":null,\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":-2,\"StockName\":\"郭佳\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"2.04%\",\"CompanyCount\":0,\"ShouldCapi\":\"1454500\",\"Source\":3,\"InvestType\":null,\"KeyNo\":null,\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":2,\"StockName\":\"程伟\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"1.58%\",\"CompanyCount\":1,\"ShouldCapi\":\"1121989\",\"Source\":3,\"InvestType\":null,\"KeyNo\":\"p939f4e503452cb82e40c4549a7268ef\",\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":-2,\"StockName\":\"林喆\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"1.56%\",\"CompanyCount\":0,\"ShouldCapi\":\"1112700\",\"Source\":3,\"InvestType\":null,\"KeyNo\":null,\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":-2,\"StockName\":\"曹冬海\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"1.52%\",\"CompanyCount\":0,\"ShouldCapi\":\"1080000\",\"Source\":3,\"InvestType\":null,\"KeyNo\":null,\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":2,\"StockName\":\"袁松\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"1.12%\",\"CompanyCount\":1,\"ShouldCapi\":\"798592\",\"Source\":3,\"InvestType\":null,\"KeyNo\":\"p24da35418566e3a8a60c3df00b5f156\",\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":0,\"StockName\":\"中山证券有限责任公司\",\"IsReportMerged\":\"\",\"HasImage\":true,\"StockPercent\":\"0.7%\",\"CompanyCount\":38,\"ShouldCapi\":\"501000\",\"Source\":3,\"InvestType\":null,\"KeyNo\":\"ca7981e737b2bc879aac57902d1207ba\",\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":2,\"StockName\":\"李菁\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"0.65%\",\"CompanyCount\":0,\"ShouldCapi\":\"462000\",\"Source\":3,\"InvestType\":null,\"KeyNo\":\"pr3acbf920d3c15644902eaa5840a3a5\",\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":-2,\"StockName\":\"陶玉柏\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"0.52%\",\"CompanyCount\":0,\"ShouldCapi\":\"370900\",\"Source\":3,\"InvestType\":null,\"KeyNo\":null,\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":2,\"StockName\":\"刘建国\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"0.51%\",\"CompanyCount\":2,\"ShouldCapi\":\"364797\",\"Source\":3,\"InvestType\":null,\"KeyNo\":\"p9f57b169e786cbd6cc3a0d3c6940232\",\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":2,\"StockName\":\"路明\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"0.22%\",\"CompanyCount\":3,\"ShouldCapi\":\"158398\",\"Source\":3,\"InvestType\":null,\"KeyNo\":\"p5f2bd69848776e62a2788e1750b409c\",\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":-2,\"StockName\":\"朱勤友\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"0.17%\",\"CompanyCount\":0,\"ShouldCapi\":\"120900\",\"Source\":3,\"InvestType\":null,\"KeyNo\":null,\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":-2,\"StockName\":\"孙伟\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"0.17%\",\"CompanyCount\":0,\"ShouldCapi\":\"120900\",\"Source\":3,\"InvestType\":null,\"KeyNo\":null,\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":-2,\"StockName\":\"祝悦\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"0.16%\",\"CompanyCount\":0,\"ShouldCapi\":\"116387\",\"Source\":3,\"InvestType\":null,\"KeyNo\":null,\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":-2,\"StockName\":\"胡义江\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"0.14%\",\"CompanyCount\":0,\"ShouldCapi\":\"100700\",\"Source\":3,\"InvestType\":null,\"KeyNo\":null,\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":-2,\"StockName\":\"张魁\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"0.14%\",\"CompanyCount\":0,\"ShouldCapi\":\"100700\",\"Source\":3,\"InvestType\":null,\"KeyNo\":null,\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":-2,\"StockName\":\"欧阳甫生\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"0.14%\",\"CompanyCount\":0,\"ShouldCapi\":\"100700\",\"Source\":3,\"InvestType\":null,\"KeyNo\":null,\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":2,\"StockName\":\"魏家平\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"0.11%\",\"CompanyCount\":1,\"ShouldCapi\":\"80600\",\"Source\":3,\"InvestType\":null,\"KeyNo\":\"pr2ae51b59e7cdaf126c20bad2839a7f\",\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":-2,\"StockName\":\"丁炜\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"0.1%\",\"CompanyCount\":0,\"ShouldCapi\":\"70500\",\"Source\":3,\"InvestType\":null,\"KeyNo\":null,\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":-2,\"StockName\":\"吕旭钢\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"0.08%\",\"CompanyCount\":0,\"ShouldCapi\":\"60400\",\"Source\":3,\"InvestType\":null,\"KeyNo\":null,\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":-2,\"StockName\":\"许继瑞\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"0.08%\",\"CompanyCount\":0,\"ShouldCapi\":\"60400\",\"Source\":3,\"InvestType\":null,\"KeyNo\":null,\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":-2,\"StockName\":\"黄献锋\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"0.08%\",\"CompanyCount\":0,\"ShouldCapi\":\"60400\",\"Source\":3,\"InvestType\":null,\"KeyNo\":null,\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":-2,\"StockName\":\"陈林辉\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"0.05%\",\"CompanyCount\":0,\"ShouldCapi\":\"37000\",\"Source\":3,\"InvestType\":null,\"KeyNo\":null,\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":-2,\"StockName\":\"陈少林\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"0.05%\",\"CompanyCount\":0,\"ShouldCapi\":\"34200\",\"Source\":3,\"InvestType\":null,\"KeyNo\":null,\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":-2,\"StockName\":\"刘强兵\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"0.05%\",\"CompanyCount\":0,\"ShouldCapi\":\"34200\",\"Source\":3,\"InvestType\":null,\"KeyNo\":null,\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":-2,\"StockName\":\"程沛\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"0.05%\",\"CompanyCount\":0,\"ShouldCapi\":\"34200\",\"Source\":3,\"InvestType\":null,\"KeyNo\":null,\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":-2,\"StockName\":\"陈军伟\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"0.01%\",\"CompanyCount\":0,\"ShouldCapi\":\"10000\",\"Source\":3,\"InvestType\":null,\"KeyNo\":null,\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":-2,\"StockName\":\"丁欢\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"0.0056%\",\"CompanyCount\":0,\"ShouldCapi\":\"4000\",\"Source\":3,\"InvestType\":null,\"KeyNo\":null,\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null},{\"Org\":-2,\"StockName\":\"史剑荣\",\"IsReportMerged\":\"\",\"HasImage\":false,\"StockPercent\":\"0.0014%\",\"CompanyCount\":0,\"ShouldCapi\":\"1000\",\"Source\":3,\"InvestType\":null,\"KeyNo\":null,\"ShoudDate\":null,\"IdentifyType\":null,\"CapiDate\":null,\"UnitType\":\"1\",\"StockType\":null,\"IdentifyNo\":null,\"RealCapi\":null,\"PublicDate\":{\"$numberLong\":\"1561651200\"},\"InvestName\":null}]";
        System.out.println(evaluate(aa, "", "122"));
    }

    public static String evaluate(String partners, String topten, String keyNo) throws Exception {
        String result = "";
        try {
            JSONArray resultData = new JSONArray();
            List<String> keyNos = new ArrayList<>();
            keyNos.add(keyNo);
            if (StringUtils.isNotBlank(topten)) {
                JSONArray topTenArray = JSONArray.parseArray(getShareHolderKeyNosByPartners(topten, keyNo));
                for (Object t : topTenArray) {
                    JSONObject tObj = JSONObject.parseObject(t.toString());
                    String tKeyNo = tObj.getString("KeyNo");
                    if (StringUtils.isNotBlank(tKeyNo) && keyNos.contains(tKeyNo)) continue;
                    keyNos.add(tKeyNo);
                    resultData.add(tObj);
                }
            }
            if (StringUtils.isNotBlank(partners)) {
                JSONArray partnersArray = JSONArray.parseArray(getShareHolderKeyNosByPartners(partners, keyNo));
                for (Object p : partnersArray) {
                    JSONObject pObj = JSONObject.parseObject(p.toString());
                    String pKeyNo = pObj.getString("KeyNo");
                    if (StringUtils.isNotBlank(pKeyNo) && keyNos.contains(pKeyNo)) continue;
                    keyNos.add(pKeyNo);
                    resultData.add(pObj);
                }
            }
            resultData = SortData(resultData);
            result = JSON.toJSONString(resultData, SerializerFeature.DisableCircularReferenceDetect);
        } catch (Exception e) {

        }
        return result;
    }


    private static JSONArray SortData(JSONArray resultData) {
        resultData.sort((a, b) -> {
            Double i;
            JSONObject aObj = JSONObject.parseObject(a.toString());
            JSONObject bObj = JSONObject.parseObject(b.toString());
            i = stringToDouble(aObj.getString("StockRightNum")) - stringToDouble(bObj.getString("StockRightNum"));
            if (i == 0) {
                i = stringToDouble(aObj.getString("StockPercent")) - stringToDouble(bObj.getString("StockPercent"));
            }
            if (i == 0) {
                i = stringToDouble(aObj.getString("ShouldCapi")) - stringToDouble(bObj.getString("ShouldCapi"));
            }
            if (i == 0) {
                i = stringToDouble(aObj.getString("KeyNo")) - (stringToDouble(bObj.getString("KeyNo")));
            }
            int r = 0;
            if (i > 0) {
                r = -1;
            } else if (i < 0) {
                r = 1;
            }
            return r;
        });
        return resultData;
    }


    public static double stringToDouble(String str) {
        double result = 0.0;
        try {
            if (StringUtils.isBlank(str)) return result;
            return Double.parseDouble(str.replace("%", "").replace(",", ""));
        } catch (Exception e) {

        }
        return result;
    }

    /**
     * 解析kafka中的partners的数据的股东
     *
     * @param partners
     * @return
     */
    public static String getShareHolderKeyNosByPartners(String partners, String keyNo) {
        if (StringUtils.isBlank(partners)) return partners;
        JSONArray shareHolderKeyNos = new JSONArray();
        JSONArray jsonArray = JSONArray.parseArray(partners);
        for (Object object : jsonArray) {
            JSONObject jsonObj = JSONObject.parseObject(object.toString());
            //pKeyNo--->patners中的keyNo
            String pKeyNo = jsonObj.getString("KeyNo") == null ? "" : jsonObj.getString("KeyNo");
            String pName = jsonObj.getString("StockName") == null ? "" : jsonObj.getString("StockName");
            if (!pKeyNo.equals(keyNo)) {
                JSONObject partnerInfo = GetPartnerObj(shareHolderKeyNos, pKeyNo, pName);
                if (partnerInfo == null) shareHolderKeyNos.add(jsonObj);
                else {
                    shareHolderKeyNos.remove(partnerInfo);
                    DecimalFormat df = new DecimalFormat("#,###");
                    Double percent = stringToDouble(jsonObj.getString("StockPercent"));
                    Double oldPercent = stringToDouble(partnerInfo.getString("StockPercent"));
                    partnerInfo.put("StockPercent", (percent + oldPercent) + "%");
                    long shouldCapi = string2Long(jsonObj.getString("ShouldCapi"));
                    long stockRightNum = string2Long(jsonObj.getString("StockRightNum"));
                    long oldShouldCapi = string2Long(partnerInfo.getString("ShouldCapi"));
                    long oldStockRightNum = string2Long(partnerInfo.getString("StockRightNum"));
                    long allShouldCapi = oldShouldCapi + shouldCapi;
                    long allStockRightNum = oldStockRightNum + stockRightNum;
                    partnerInfo.put("ShouldCapi", allShouldCapi == 0 ? "" : df.format(allShouldCapi));
                    partnerInfo.put("StockRightNum", allStockRightNum == 0 ? "" : df.format(allStockRightNum));
                    shareHolderKeyNos.add(partnerInfo);
                }
            }
        }
        return JSON.toJSONString(shareHolderKeyNos, SerializerFeature.DisableCircularReferenceDetect);
    }

    private static JSONObject GetPartnerObj(JSONArray jsonArray, String keyNo, String name) {
        for (Object o : jsonArray) {
            JSONObject jsonObj = JSONObject.parseObject(o.toString());
            String pKeyNo = jsonObj.getString("KeyNo");
            String pCompanyName = jsonObj.getString("StockName");
            if ((StringUtils.isNotBlank(keyNo) && keyNo.equals(pKeyNo)) || (StringUtils.isBlank(keyNo) && name.equals(pCompanyName))) {
                return jsonObj;
            }
        }
        return null;
    }

    public static Long string2Long(String number) {
        Long intNumber = 0L;
        try {
            if (StringUtils.isNotBlank(number) && !"".equals(number)) {
                intNumber = Long.parseLong(number.replace(",", ""));
            }
        } catch (Exception e) {

        }
        return intNumber;
    }
}
