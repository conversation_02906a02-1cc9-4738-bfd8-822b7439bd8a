package com.qcc.udf.casesearch_v3;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Collections;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class getValidWdid extends UDF {
    public static String evaluate(String param) {
        JSONArray result = new JSONArray();
        if (StringUtils.isNotEmpty(param)){
            JSONArray array = JSONObject.parseObject(param).getJSONArray("InfoList");
            if (array != null && !array.isEmpty()){
                Iterator<Object> it = array.iterator();
                while (it.hasNext()){
                    JSONObject json = (JSONObject) it.next();
                    getInfoFromArray(json.getJSONArray("SxList"), result, "sx");
                    getInfoFromArray(json.getJSONArray("ZxList"), result, "zx");
                    getInfoFromArray(json.getJSONArray("XgList"), result, "xg");
                    getInfoFromArray(json.getJSONArray("ZbList"), result, "zb");
                    getInfoFromArray(json.getJSONArray("CaseList"), result, "cpws");
                    getInfoFromArray(json.getJSONArray("LianList"), result, "lian");
                    getInfoFromArray(json.getJSONArray("SqtjList"), result, "sqtj");
                    getInfoFromArray(json.getJSONArray("FyggList"), result, "fygg");
                    getInfoFromArray(json.getJSONArray("KtggList"), result, "ktgg");
                    getInfoFromArray(json.getJSONArray("SdggList"), result, "sdgg");
                }
            }
        }

        return result.toString();
    }

    public static void getInfoFromArray(JSONArray array, JSONArray result, String type){
        if (array != null && !array.isEmpty() && array.size() > 0){
            Iterator<Object> it = array.iterator();
            while (it.hasNext()){
                JSONObject json = (JSONObject) it.next();

                //if (json.getInteger("IsValid") == 1){
                     JSONObject jsonObject = new JSONObject();
                     jsonObject.put("Id", json.getString("Id"));
                     jsonObject.put("Type", type);

                     result.add(jsonObject);
                //}
            }
        }
    }
}
