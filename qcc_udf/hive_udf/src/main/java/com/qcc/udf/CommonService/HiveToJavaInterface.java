package com.qcc.udf.CommonService;

import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URISyntaxException;
import java.net.URL;

@Description(name = "HiveToJavaInterface", value = "_FUNC_(String keyNo); - Return remain count" +
        "插入数据进Java程序接口")
public class HiveToJavaInterface extends UDF {

    public static String evaluate(String requestUrl, String param) throws IOException {
        URL url = new URL(requestUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setDoOutput(true);
        connection.setDoInput(true);
        connection.setRequestMethod("POST");
        connection.setUseCaches(false);
        connection.setInstanceFollowRedirects(true);
        connection.setRequestProperty("accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
        connection.setRequestProperty("connection", "Keep-Alive");
        connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1)");

        connection.connect();

        DataOutputStream out = new DataOutputStream(connection.getOutputStream());
        out.writeBytes(param);
        out.flush();
        out.close();
        try {
            BufferedReader bf = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            StringBuilder sb = new StringBuilder();
            while ((line = bf.readLine()) != null) {
                sb.append(line);
            }
            bf.close();
            connection.disconnect();
            return sb.toString();
        } catch (IOException e) {
            return e.toString();
        }
    }

    public static void main(String[] args) throws IOException, URISyntaxException {
        System.out.println(evaluate("http://qcc-user-app-user-api.sit.office.qichacha.com/cert/writeDataForDirectCancel",
                "batchParameter=[{\"companyKeyno\": \"f625a5b661058ba5082ca508f99ffe1b\",\"reasonType\": \"1\"},{\"companyKeyno\": \"88d4c24a865aca511539e9c7ce9cb2df\",\"reasonType\": \"2\"}]"));
    }


}