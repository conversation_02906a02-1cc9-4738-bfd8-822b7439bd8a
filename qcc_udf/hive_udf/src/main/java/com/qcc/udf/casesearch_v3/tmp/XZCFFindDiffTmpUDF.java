package com.qcc.udf.casesearch_v3.tmp;

import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.input.XZCFEntity;
import com.qcc.udf.casesearch_v3.entity.output.LianListEntity;
import com.qcc.udf.temp.CommonUtil;
import joptsimple.internal.Strings;
import org.apache.commons.collections.CollectionUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022年01月06日 11:06
 */
public class XZCFFindDiffTmpUDF extends UDF {

    public static void main(String[] args) {
        System.out.println(evaluate("****黑水路批发市场二期2-30精品屋**", 1));
    }

    public static String evaluate(String content, int type) {
        if (type == 1) {
            if (Strings.isNullOrEmpty(content)) {
                return content;
            }
            content = CommonUtil.full2Half(content);
            content = content.replaceAll("\\r|\\n|\\r\\n|\\t|、|\\~|\\!|@|\\#|\\$|\\%|\\^|\\&|\\“|\r|\n|\t|\r\n|&nbsp;|\\\\r|\\\\n|\\\\t"
                    , "").trim();
            String specialStr = ".|,| | |-|\"|'|、|·";
            String[] specialArray = specialStr.split("\\|");
            //最多N次可以去除所有首尾特殊符号
            for(int i = 0;i<specialArray.length;i++){
                content = trimFirstAndLastChar(content,specialArray);
            }

            //最多N次可以去除所有头部特殊符号
            String[] specialFirstArray = "*".split("\\|");
            for(int i = 0;i<specialFirstArray.length;i++){
                content = trimFirstChar(content,specialFirstArray);
            }

        }

        if (type == 2) {
            Set<String> st = getRegNoFromContent(content);
            return st.stream().collect(Collectors.joining(","));
        }

        return content;
    }

    static String trimFirstAndLastChar(String str, String[] specialArray) {
        boolean beginIdxFlag = true;
        boolean endIdxFlag = true;
        do {
            for (String spChar : specialArray) {
                int delLength = spChar.length();
                beginIdxFlag = str.startsWith(spChar);
                if(beginIdxFlag){
                    str = str.substring(delLength);
                }
                endIdxFlag = str.endsWith(spChar);
                if(endIdxFlag){
                    str = str.substring(0,str.length()-delLength);
                }
            }
        } while (beginIdxFlag || endIdxFlag);
        return str;
    }

    static String trimFirstChar(String str, String[] specialArray) {
        boolean beginIdxFlag = true;
        do {
            for (String spChar : specialArray) {
                int delLength = spChar.length();
                beginIdxFlag = str.startsWith(spChar);
                if(beginIdxFlag){
                    str = str.substring(delLength);
                }
            }
        } while (beginIdxFlag);
        return str;
    }

    /**
     * 获取文本中统一社会信用代码
     *
     * @param content
     * @return
     */
    public static Set<String> getRegNoFromContent(String content) {
        String reg1 = "[1-9ANYG][\\d]{7}[\\dA-Z]{9,10}";
        String reg2 = "([1-9ANYG]\\d)[\\dA-Z]{13}";
        Set<String> allRegSet = new HashSet<>();

        Pattern compile1 = Pattern.compile(reg1);
        Matcher matcher1 = compile1.matcher(content);
        while (matcher1.find()) {
            String group = matcher1.group(0);
            if (group.length() == 18) {
                allRegSet.add(group);
            }
        }
        if (CollectionUtils.isEmpty(allRegSet)) {
            compile1 = Pattern.compile(reg2);
            matcher1 = compile1.matcher(content);
            while (matcher1.find()) {
                String group = matcher1.group(0);
                if (group.length() == 15) {
                    allRegSet.add(group);
                }
            }
        }
        return allRegSet;
    }
}
