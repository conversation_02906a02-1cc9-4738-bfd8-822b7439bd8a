package com.qcc.udf.risk_analysis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

public class GetHistorySxListV2 extends UDF {

    public String evaluate(String keyNo, List<String> infoList) {
        JSONArray result = new JSONArray();
        JSONArray array = new JSONArray();
        if (infoList != null && infoList.size() > 0){
            Map<String, JSONObject> infoMap = new LinkedHashMap<>();
            for (String str : infoList){
                JSONObject json = JSONObject.parseObject(str);
                json.put("subkeyno", json.getString("subkeyno") == null ? "" : json.getString("subkeyno"));
                json.put("subname", json.getString("subname") == null ? "" : json.getString("subname"));

                String key = "";
                if (StringUtils.isNotEmpty(json.getString("subkeyno"))){
                    key = json.getString("subkeyno");
                }else if (StringUtils.isNotEmpty(json.getString("subname"))){
                    key = RiskAnalysisUtil.full2Half(json.getString("subname"));
                }

                if (StringUtils.isNotEmpty(key)){
                    if (infoMap.containsKey(key)){
                        JSONObject item = infoMap.get(key);
                        item.put("KeyNo", keyNo);
                        JSONObject sqrInfo = new JSONObject();
                        sqrInfo.put("KeyNo", json.getString("subkeyno"));
                        sqrInfo.put("Name", json.getString("subname"));
                        sqrInfo.put("Org", RiskAnalysisUtil.getOrgByKeyNo(sqrInfo.getString("KeyNo")));
                        item.put("NameKeyNo", sqrInfo);
                        item.put("Cnt", item.getInteger("Cnt") + 1);
                        item.put("Anno", item.getString("Anno").concat(",").concat(json.getInteger("publicdate").toString().concat("_").concat(json.getString("anno"))));
                        item.put("PublishDate", item.getString("PublishDate").concat(",").concat(json.getInteger("publicdate").toString()));
                        item.put("SortDate", json.getInteger("publicdate") > item.getInteger("SortDate") ? json.getInteger("publicdate") : item.getInteger("SortDate"));
                        item.put("OriginId", item.getString("OriginId").concat(",").concat(json.getString("id")));

                        infoMap.put(key, item);
                    }else{
                        JSONObject item = new JSONObject();
                        item.put("KeyNo", keyNo);
                        JSONObject sqrInfo = new JSONObject();
                        sqrInfo.put("KeyNo", json.getString("subkeyno"));
                        sqrInfo.put("Name", json.getString("subname"));
                        sqrInfo.put("Org", RiskAnalysisUtil.getOrgByKeyNo(sqrInfo.getString("KeyNo")));
                        item.put("NameKeyNo", sqrInfo);
                        item.put("Cnt", 1);
                        item.put("Anno", json.getInteger("publicdate").toString().concat("_").concat(json.getString("anno")));
                        item.put("PublishDate", json.getInteger("publicdate").toString());
                        item.put("SortDate", json.getInteger("publicdate"));
                        item.put("OriginId", json.getString("id"));

                        infoMap.put(key, item);
                    }
                }
            }

            Set<String> sqrSet = infoMap.keySet();
            for (String str : sqrSet){
                JSONObject jsonObject = infoMap.get(str);
                jsonObject.put("Id", RiskAnalysisUtil.ecodeByMD5(keyNo.concat(str).concat("_his")));
                array.add(jsonObject);
            }

            // 编辑时间和金额
            Iterator<Object> iterator = array.iterator();
            while (iterator.hasNext()){
                JSONArray annoArr = new JSONArray();
                JSONObject jsonObject = (JSONObject)iterator.next();
                // 日期：内部排序；外部排序取最新时间
                String[] annos = jsonObject.getString("Anno").split(",");
                List<String> annoList = new LinkedList<>();
                for (String str : annos){
                    annoList.add(str);
                }
                Collections.sort(annoList,((o1, o2) ->{
                    return o2.compareTo(o1);
                }));
                String anno = "";
                for (String str : annoList){
                    anno = anno.concat(",").concat(str.split("_")[1]);
                    JSONObject tmpJson = new JSONObject();
                    tmpJson.put("A", str.split("_")[1]);
                    tmpJson.put("B", Long.parseLong(str.split("_")[0]));
                    annoArr.add(tmpJson);
                }
                anno = anno.length() > 0 ? anno.substring(1) : anno;

                jsonObject.put("Anno", anno);

                // 日期：内部排序；外部排序取最新时间
                String[] dates = jsonObject.getString("PublishDate").split(",");
                List<String> dateList = new LinkedList<>();
                for (String str : dates){
                    dateList.add(str);
                }
                Collections.sort(dateList,((o1, o2) ->{
                    return o2.compareTo(o1);
                }));
                String date = "";
                for (String str : dateList){
                    date = date.concat(",").concat(str);
                }
                date = date.length() > 0 ? date.substring(1) : date;

                jsonObject.put("PublishDate", date);

                jsonObject.put("AnnoArr", annoArr);

                result.add(jsonObject);
            }
        }


        return array.toString();
    }

    public static void main(String[] args) {
        GetHistorySxListV2 aa = new GetHistorySxListV2();
        List<String> infoList = JSON.parseArray("[\"{\\\"keyno\\\":\\\"004b831ab1e5178f9638e62ff25998fd\\\",\\\"id\\\":\\\"5f4a732b1b88853de062aa52a4a80b6f2\\\",\\\"anno\\\":\\\"（2016）豫0311执1542号\\\",\\\"publicdate\\\":\\\"1480521600\\\",\\\"subkeyno\\\":\\\"\\\",\\\"subname\\\":\\\"陈之慷\\\",\\\"executestatus\\\":\\\"1\\\"}\",\"{\\\"keyno\\\":\\\"004b831ab1e5178f9638e62ff25998fd\\\",\\\"id\\\":\\\"a8191141eeccd6ae5ef2ea599238d8e22\\\",\\\"anno\\\":\\\"（2016）豫0311执1542号\\\",\\\"publicdate\\\":\\\"1484236800\\\",\\\"subkeyno\\\":\\\"93ec42a5ca2ef8a2a3b10eb98cd68d21\\\",\\\"subname\\\":\\\"洛阳海创置业有限公司\\\",\\\"executestatus\\\":\\\"1\\\"}\",\"{\\\"keyno\\\":\\\"004b831ab1e5178f9638e62ff25998fd\\\",\\\"id\\\":\\\"d208d953a0fc70b585740e8a430c249d2\\\",\\\"anno\\\":\\\"（2019）豫0311执恢177号\\\",\\\"publicdate\\\":\\\"1570550400\\\",\\\"subkeyno\\\":\\\"\\\",\\\"subname\\\":\\\"成莎莎\\\",\\\"executestatus\\\":\\\"1\\\"}\",\"{\\\"keyno\\\":\\\"004b831ab1e5178f9638e62ff25998fd\\\",\\\"id\\\":\\\"411b99a5e8942d262222b3e8561f3d222\\\",\\\"anno\\\":\\\"（2016）豫0311执1542号\\\",\\\"publicdate\\\":\\\"1484236800\\\",\\\"subkeyno\\\":\\\"924ce48040134e349213451e603b4b41\\\",\\\"subname\\\":\\\"洛阳海拓假日置业有限公司\\\",\\\"executestatus\\\":\\\"1\\\"}\",\"{\\\"keyno\\\":\\\"004b831ab1e5178f9638e62ff25998fd\\\",\\\"id\\\":\\\"1dc5380531b061f4274c06acc1c74e782\\\",\\\"anno\\\":\\\"（2016）豫0311执1542号\\\",\\\"publicdate\\\":\\\"1480521600\\\",\\\"subkeyno\\\":\\\"\\\",\\\"subname\\\":\\\"刘丽红\\\",\\\"executestatus\\\":\\\"1\\\"}\",\"{\\\"keyno\\\":\\\"004b831ab1e5178f9638e62ff25998fd\\\",\\\"id\\\":\\\"f87b6f9bea0f3fe74e83a6b54850d8242\\\",\\\"anno\\\":\\\"（2019）豫0311执恢177号\\\",\\\"publicdate\\\":\\\"1570550400\\\",\\\"subkeyno\\\":\\\"93ec42a5ca2ef8a2a3b10eb98cd68d21\\\",\\\"subname\\\":\\\"洛阳海创置业有限公司\\\",\\\"executestatus\\\":\\\"1\\\"}\",\"{\\\"keyno\\\":\\\"004b831ab1e5178f9638e62ff25998fd\\\",\\\"id\\\":\\\"507b910df6c4ce79048fdaf1958d33042\\\",\\\"anno\\\":\\\"（2019）豫0311执恢177号\\\",\\\"publicdate\\\":\\\"1570550400\\\",\\\"subkeyno\\\":\\\"\\\",\\\"subname\\\":\\\"刘丽红\\\",\\\"executestatus\\\":\\\"1\\\"}\",\"{\\\"keyno\\\":\\\"004b831ab1e5178f9638e62ff25998fd\\\",\\\"id\\\":\\\"9a83d6b75819398960f3e69e8426f5da2\\\",\\\"anno\\\":\\\"（2019）豫0311执恢177号\\\",\\\"publicdate\\\":\\\"1570550400\\\",\\\"subkeyno\\\":\\\"\\\",\\\"subname\\\":\\\"牛涛\\\",\\\"executestatus\\\":\\\"1\\\"}\",\"{\\\"keyno\\\":\\\"004b831ab1e5178f9638e62ff25998fd\\\",\\\"id\\\":\\\"b0d1193e6cd9f0c389c4b1d9eb978ed72\\\",\\\"anno\\\":\\\"（2019）京0105执44465号\\\",\\\"publicdate\\\":\\\"1575475200\\\",\\\"subkeyno\\\":\\\"\\\",\\\"subname\\\":\\\"陈之慷\\\",\\\"executestatus\\\":\\\"1\\\"}\",\"{\\\"keyno\\\":\\\"004b831ab1e5178f9638e62ff25998fd\\\",\\\"id\\\":\\\"20ae5d556a03ad58bfd9790d2c68bf3d2\\\",\\\"anno\\\":\\\"（2016）豫0311执1542号\\\",\\\"publicdate\\\":\\\"1480521600\\\",\\\"subkeyno\\\":\\\"\\\",\\\"subname\\\":\\\"成莎莎\\\",\\\"executestatus\\\":\\\"1\\\"}\",\"{\\\"keyno\\\":\\\"004b831ab1e5178f9638e62ff25998fd\\\",\\\"id\\\":\\\"2a1642e097fc235e168c2482cc179c842\\\",\\\"anno\\\":\\\"（2016）豫0311执1542号\\\",\\\"publicdate\\\":\\\"1480521600\\\",\\\"subkeyno\\\":\\\"\\\",\\\"subname\\\":\\\"牛涛\\\",\\\"executestatus\\\":\\\"1\\\"}\",\"{\\\"keyno\\\":\\\"004b831ab1e5178f9638e62ff25998fd\\\",\\\"id\\\":\\\"3808e0b92be4ceb5543992f31a113ea42\\\",\\\"anno\\\":\\\"（2019）豫0311执恢177号\\\",\\\"publicdate\\\":\\\"1570550400\\\",\\\"subkeyno\\\":\\\"\\\",\\\"subname\\\":\\\"陈之慷\\\",\\\"executestatus\\\":\\\"1\\\"}\"] ", String.class);
        System.out.println(aa.evaluate("004b831ab1e5178f9638e62ff25998fd", infoList));
    }
}
