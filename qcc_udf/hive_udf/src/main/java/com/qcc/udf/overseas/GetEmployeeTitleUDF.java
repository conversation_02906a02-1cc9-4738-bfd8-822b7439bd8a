package com.qcc.udf.overseas;

import com.qcc.udf.overseas.constant.EmployeeTitle;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业务UDF（海外企业）匹配员工职位信息及对应优先级
 * ---------------------------------------------------------------------------------------------------------
 * add jar hdfs://ldh/data/hive/udf/qcc_udf.jar;
 * create temporary function GetEmployeeTitle as 'com.qcc.udf.overseas.GetEmployeeTitleUDF';
 * ---------------------------------------------------------------------------------------------------------
 * select GetEmployeeTitle ('INCORPORATOR');
 * 结果: ['INCORPORATOR', '9']
 * select GetEmployeeTitle ('OTHER OVER LIST');
 * 结果: ['OTHER', '2']
 * select GetEmployeeTitle (' ');
 * 结果: ['', '1']
 */
public class GetEmployeeTitleUDF extends UDF {
    private final static Map<String, EmployeeTitle> employeeTitleMap;

    static {
        employeeTitleMap = new HashMap<>();
        try {
            InputStream is = GetEmployeeTitleUDF.class.getResourceAsStream("/us_employee_title_mapping.txt");
            BufferedReader br = new BufferedReader(new InputStreamReader(is));
            String line;
            while ((line = br.readLine()) != null) {
                if (!line.startsWith("#")) {
                    List<String> itemList = Arrays.asList(line.split("\\|"));
                    if (itemList.size() == 3) {
                        EmployeeTitle employeeTitle = new EmployeeTitle(itemList.get(0), itemList.get(1), itemList.get(2));
                        employeeTitleMap.put(itemList.get(0), employeeTitle);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List<String> evaluate(String input) {
        String title = input.trim();
        if (StringUtils.isNotBlank(title)) {
            if (employeeTitleMap.containsKey(title)) {
                EmployeeTitle employeeTitle = employeeTitleMap.get(title);
                return Arrays.asList(employeeTitle.getTitle(), employeeTitle.getPriority());
            } else {
                return Arrays.asList("OTHER", "2");
            }
        } else {
            return Arrays.asList("", "1");
        }
    }
}
