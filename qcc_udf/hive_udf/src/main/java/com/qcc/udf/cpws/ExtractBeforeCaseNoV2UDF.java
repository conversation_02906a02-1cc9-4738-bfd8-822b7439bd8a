package com.qcc.udf.cpws;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 裁判文书清洗UDF：提取本案件关联的其它裁判文书案号第二版
 * ---------------------------------------------------------------------------------------------------------
 * create temporary function extractBeforeCaseNoV2 as 'com.ld.qianjg.cpws.ExtractBeforeCaseNoV2UDF' using jar 'hdfs:///data/hive/udf/qcc_udf.jar';
 * ---------------------------------------------------------------------------------------------------------
 * select extractCaseTrialRound (fmtContent, caseNo, caseType, trialRound);
 * 结果: 'caseNo1,caseNo2,CaseNo3...'
 */
public class ExtractBeforeCaseNoV2UDF extends UDF {

    /**
     *
     * @param fmtContent    标准格式裁判文书正文
     * @param caseNo        当前案件的案号
     * @param caseType      案件类型
     * @param trialRound    审理程序
     * @return
     */
    public String evaluate(String fmtContent, String caseNo, String caseType, String trialRound) {
        try {
            if (StringUtils.isNotBlank(fmtContent) && StringUtils.isNotBlank(caseNo)
                    && StringUtils.isNotBlank(caseType) && StringUtils.isNotBlank(trialRound)) {
                /**
                 * 提取逻辑描述：
                 * 1 转为全角括号
                 * 2 正则提取审判经过和判决结果段落中的案号  (（[0-9]{4}）[\u4e00-\u9fa5]+.+?号(之[一二三四五六七八九十])*)
                 * 3 提取到的案号列表剔除字数较少的案号以及与自身重复的案号
                 *
                 * 注：目前的需求中提取
                 * - 执行案件 / 行政案件 / 民事案件, 民事一审程序不需要提取beforeCaseNo
                 */
                if (caseType.equals("zx")
                        || caseType.equals("xz")
                        || (caseType.equals("ms") && !trialRound.equals("民事一审"))) {
                    String trialProcess = getPureContentByClassName(fmtContent, "qcc_law_judge_trial");
                    String judgeResult = getPureContentByClassName(fmtContent, "qcc_law_judge_result");

                    String searchContent = String.join("\n", Arrays.asList(trialProcess, judgeResult));
                    Pattern extractPattern = Pattern.compile("(（[0-9]{4}）[\\u4e00-\\u9fa5]+.+?号(之[一二三四五六七八九十])*)");
                    Matcher matcher = extractPattern.matcher(
                            searchContent.replace("(", "（").replace(")", "）"));
                    List<String> linkedCaseNoList = new ArrayList<>();
                    while (matcher.find()) {
                        linkedCaseNoList.add(matcher.group(0));
                    }

                    final String compareCaseNo = caseNo.replace("(", "（").replace(")", "）");
                    Set<String> beforeCaseNoSet = linkedCaseNoList.stream()
                            .filter(e -> !e.equals(compareCaseNo))
                            .filter(e -> !e.contains("第xx号"))
                            .filter(e -> !e.contains("["))
                            .collect(Collectors.toSet());
                    return StringUtils.join(beforeCaseNoSet, ",");
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return "";
    }

    /**
     * 从裁判文书中获取指定样式对应部分的纯文本信息（去掉html标签）
     * @param fmtContent
     * @param className
     * @return
     */
    private static String getPureContentByClassName(String fmtContent, String className) {
        String resContent = "";
        try {
            if (StringUtils.isNotBlank(fmtContent) && StringUtils.isNotBlank(className)) {
                Document document = Jsoup.parse(fmtContent);
                if (document != null) {
                    Elements elements = document.getElementsByClass(className);
                    if (elements != null && elements.size() > 0) {
                        resContent = HtmlToTextUtil.convert(elements.get(0).toString())
                                .replace("\n", "").trim();
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return resContent;
    }

    public static void main(String[] args) {
        String trialProcess = "<div class=\"qcc_law_doc\">    <div class=\"qcc_law_court\">          <span>浙江省高级人民法院</span>    </div>    <div class=\"qcc_law_paper_type\">         <span>行政判决书</span>    </div>    <div class=\"qcc_law_case_no\">          <span>（2009）浙行再字第4号</span>    </div>    <div class=\"qcc_law_judge_party_label\"><span>当事人信息</span></div>    <div class=\"qcc_law_judge_party\">                  <span>申请再审人（一审原告、二审上诉人）沈甲。</span>                <span>申请再审人（一审原告、二审上诉人）陈</span>                <span>××</span>                <span>。</span>                <span>被申请人（一审被告、二审被上诉人）绍兴市</span>                <span>××</span>                <span>局（原<a href=\"https://www.qichacha.com/firm_g36732b17229bdc81245d934aa39633d.html\" target=\"_blank\">绍兴市城乡建设委员会</a>），住所地绍兴市人民</span>                <span>××</span>                <span>路</span>                <span>××</span>                <span>号。</span>                <span>法定代表人吉</span>                <span>××</span>                <span>。</span>                <span>委托代理人汤</span>                <span>××</span>                <span>。</span>                <span>沈甲、陈</span>                <span>××</span>                <span>诉绍兴市</span>                <span>××</span>            </div>        <div class=\"qcc_law_judge_trial_label\"><span>审理经过</span></div>    <div class=\"qcc_law_judge_trial\">                <span>局（原<a href=\"https://www.qichacha.com/firm_g36732b17229bdc81245d934aa39633d.html\" target=\"_blank\">绍兴市城乡建设委员会</a>）房屋拆迁裁决行政争议一案，<a href=\"https://www.qichacha.com/firm_gfd67ef88b8474aad41c5a140823def5.html\" target=\"_blank\">绍兴市越城区人民法院</a>于2000年10月30日作出（2000）越行初字第22号行政判决，沈甲、陈</span>            </div>                                            <div class=\"qcc_law_current_review_label\"><span>本院查明</span></div>    <div class=\"qcc_law_current_review\">                <span>本案再审争议的焦点为被申请人所作（2000）第43号裁决将涉案被拆迁房屋按照住宅进行安置补偿是否合法，主要涉及两个方面：一、涉案被拆迁房屋是否为住宅；二、涉案被拆迁房屋是否符合营业房的安置条件。</span>            </div>            <div class=\"qcc_law_current_identification_label\"><span>本院认为</span></div>    <div class=\"qcc_law_current_identification\">                <span>本院再审认为：首先，关于涉案被拆迁房屋的性质问题，因涉案房屋系由沈甲之父沈乙翻建而来，1984年6月1日，沈乙申请翻建房屋的理由为“房屋小、人多、住房十分困难”，虽然1990年绍兴<a href=\"https://www.qichacha.com/firm_gcfb26117a16560192307f224f4ec0bd.html\" target=\"_blank\">市房地产管理局</a>颁发的房产证上并未明确该房屋的住宅性质，但是1991年绍兴市土地管理局颁发的国有土地使用权证上明确登记该土地用途为住宅，故申请再审人认为涉案房屋系非住宅没有依据，该房屋性质依法应认定为住宅；其次，关于涉案被拆迁房屋是否符合营业房的安置条件问题，<a href=\"https://www.qichacha.com/firm_g38948136fd8c4a28217eec6ee535810.html\" target=\"_blank\">绍兴市人民政府</a>1999年4月12日修订发布的《绍兴市区房屋拆迁安置补偿实施办法》第九条第四款规定：“拆除由原私有住宅房屋改变的营业用房，原则上按原房屋性质安置。对原房屋所有人自己依法营业并以此为主要生活来源，要求安置营业用房的，在确保住宅安置标准的前提下，经房管、工商管理部门确认在一九九一年六月一日前已改变房屋性质的，应补缴城市基础设施配套费，可作营业用房安置；一九九一年六月一日后改变房屋性质的，作住宅安置；……”一则房管部门从未确认该房屋性质的变化，二则再审申请人自己依法营业的最早时间为1993年8月3日，工商管理部门亦未确认该房屋于一九九一年六月一日前发生了性质变化，故涉案被拆迁房屋的性质未经有关部门确认已于一九九一年六月一日前改为营业用房，不符合上述文件规定的“可作营业用房安置”条件。综上所述，涉案被拆迁房屋性质属于住宅，且不符合营业房的安置条件，<a href=\"https://www.qichacha.com/firm_g36732b17229bdc81245d934aa39633d.html\" target=\"_blank\">绍兴市城乡建设委员会</a>所作（2000）第43号裁决认定事实清楚、适用法规、规章正确，一审法院判决维持该裁决并无不当。再审申请人的申请理由不能成立，本院不予支持。依照《中华人民共和国行政诉讼法》第六十一条第（一）项之规定，判决如下：</span>            </div>                <div class=\"qcc_law_judge_result_label\"><span>判决结果</span></div>    <div class=\"qcc_law_judge_result\">                <span>维持<a href=\"https://www.qichacha.com/firm_gfd67ef88b8474aad41c5a140823def5.html\" target=\"_blank\">绍兴市越城区人民法院</a>（2000）越行初字第22号行政判决。</span>                <span>本判决为终审判决。</span>            </div>        <div class=\"qcc_law_judge_collegiate_bench_label\"><span>合议庭</span></div>    <div class=\"qcc_law_judge_collegiate_bench\">                <span>审判长王君</span>                <span>审判员周萍</span>                <span>代理审判员易斌</span>            </div>    <div class=\"qcc_law_judge_date_label\"><span>判决日期</span></div>    <div class=\"qcc_law_judge_date\">        <span>二零零九年六月十八日</span>    </div>        <div class=\"qcc_law_judge_recorder_label\"><span>书记员</span></div>    <div class=\"qcc_law_judge_recorder\">                <span>书记员黄佳伟</span>            </div>    </div> \n";
        String caseNo = "（2009）浙行再字第00004号";
        String caseType = "zx";
        String trialRound = "行政终审";

        String result = new ExtractBeforeCaseNoV2UDF().evaluate(trialProcess, caseNo, caseType, trialRound);
        System.out.println(result);
    }
}
