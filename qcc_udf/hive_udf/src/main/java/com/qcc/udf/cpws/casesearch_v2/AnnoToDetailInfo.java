package com.qcc.udf.cpws.casesearch_v2;

import cn.hutool.core.util.ReUtil;
import com.alibaba.fastjson.JSON;
import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.output.LawSuitV3OutputEntity;
import com.qcc.udf.cpws.ExtractCaseTrialRoundUDF;
import com.qcc.udf.cpws.casesearch_v2.util.CaseTypeUtil;
import com.qcc.udf.cpws.casesearch_v2.util.CourtNameFromCaseNoUtil;
import com.qcc.udf.cpws.casesearch_v2.util.SbcToDbc;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;

/**
 *  根据案号，清洗出detailnfo信息
 * @Auther: wuql
 * @Date: 2020/11/17 10:00
 * @Description:
 */
public class AnnoToDetailInfo extends UDF {
    public String evaluate(String anno)  {
        anno = SbcToDbc.convertSBCToDBC(anno);
        //案件类型
        String caseType = new CaseTypeUtil().evaluate(anno);

        String rule ="[\\u4e00-\\u9fa5]\\d{0,4}";
        String caseNoChar = ReUtil.get(rule, anno, 0);
        //法院
        String courtName = new CourtNameFromCaseNoUtil().evaluate(caseNoChar);

        String caseNo = anno.replaceAll("\\(", "（").replaceAll("\\)", "）");

        //最新审理程序
        String trialRound = new ExtractCaseTrialRoundUDF().evaluate(caseNo);

        long time =-1L;
        /*String rule2 ="\\d{4}";
        String year = ReUtil.get(rule2, anno, 0);
        if (StringUtils.isNotBlank(year)&& StringUtils.isNumeric(year)){
            int yearNum = Integer.parseInt(year);
            if (yearNum>1980){
                DateTime dateTime = DateUtil.parse(year, "yyyy");
                time = dateTime.getTime()/1000;
            }
        }*/


        LawSuitV3OutputEntity outputEntity = new LawSuitV3OutputEntity();
        //案件类型
        outputEntity.setCaseType(caseType);
        //法院
        outputEntity.setCourtList(courtName);
        //审理程序
        outputEntity.setLatestTrialRound(trialRound);
        //审理程序
        outputEntity.setLastestDateType(trialRound);
        outputEntity.setAnNoList(caseNo);
        outputEntity.setAnnoCnt(1);
        outputEntity.setLastestDate(time);
        outputEntity.setEarliestDate(time);

        //明细信息
        List<InfoListEntity> infoList = new ArrayList<>();
        InfoListEntity info = new InfoListEntity();
        info.setAnno(caseNo);
        info.setCaseType(caseType);
        info.setCourt(courtName);
        info.setTrialRound(trialRound);
        info.setLatestTimestamp(time);
        infoList.add(info);
        outputEntity.setInfoList(infoList);

        return JSON.toJSONString(outputEntity);
    }

    public static void main(String[] args) {
        String anNo ="（2015）鹿民再字第6号";
        AnnoToDetailInfo annoToDetailInfo = new AnnoToDetailInfo();
        String evaluate = annoToDetailInfo.evaluate(anNo);
        System.out.println(evaluate);
    }
}
