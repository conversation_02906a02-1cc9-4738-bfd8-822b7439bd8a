package com.qcc.udf.trade_mark;


import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * 根据注册号 国际分类 生成商标id
 */
public class TmIdGenerateUDF extends UDF {

    public final static String SALT = "TMEncodeMD5Parameter";

    public String evaluate(String reg_no, String int_cls) {
        return encodeMd5Double(reg_no + "_" + int_cls, SALT);
    }

    public String encodeMd5Double(String input, String salt) {
        if (StringUtils.isNotEmpty(input)) {
            input = encodeMd5(String.format("%s_%s", input, salt));
            input = encodeMd5(String.format("%s|%s", salt, input));
        }
        return input;
    }

    public String encodeMd5(String input) {
        return DigestUtils.md5Hex(input.getBytes());
    }
}
