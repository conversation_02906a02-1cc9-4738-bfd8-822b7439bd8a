package com.qcc.udf.casesearch_v3;

import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import org.apache.hadoop.hive.ql.exec.UDF;
import parquet.Strings;


public class CaseNoPreCleanUDF extends UDF {
    public static String evaluate(String caseNo) {
        if (Strings.isNullOrEmpty(caseNo)) {
            return "";
        }
        caseNo = CommonV3Util.full2Half(caseNo);
        caseNo = caseNo.replaceAll(" | |　", "");
        caseNo = caseNo.replaceAll("\r\n|\n", "");
        return caseNo;
    }


    public static void main(String[] args) {
        System.out.println(evaluate("(2007)昌 0107执 字12号之一"));
    }
}