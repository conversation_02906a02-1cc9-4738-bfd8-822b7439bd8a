package com.qcc.udf.kzz;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * 根据地区编码获取省份code
 *
 * <AUTHOR>
 * @date 2022/1/26
 */
public class GetProvinceCodeByAreaCode extends UDF {
    public static String evaluate(String content) {
        String provinceCode = "";
        try {
            if (StringUtils.length(content) >= 2) {
                String pronvinceCode = content.substring(0, 2);
                switch (pronvinceCode) {
                    case "11":          // 11   北京
                        return "BJ";
                    case "12":          // 12   天津
                        return "TJ";
                    case "13":          // 13   河北
                        return "HB";
                    case "14":          // 14   山西
                        return "SX";
                    case "15":          // 15   内蒙古
                        return "NMG";
                    case "21":          // 21   辽宁
                        return "LN";
                    case "22":          // 22   吉林
                        return "JL";
                    case "23":          // 23   黑龙江
                        return "HLJ";
                    case "31":          // 31   上海
                        return "SH";
                    case "32":          // 32   江苏
                        return "JS";
                    case "33":          // 33   浙江
                        return "ZJ";
                    case "34":          // 34   安徽
                        return "AH";
                    case "35":          // 35   福建
                        return "FJ";
                    case "36":          // 36   江西
                        return "JX";
                    case "37":          // 37   山东
                        return "SD";
                    case "41":          // 41   河南
                        return "HEN";
                    case "42":          // 42   湖北
                        return "HUB";
                    case "43":          // 43   湖南
                        return "HUN";
                    case "44":          // 44   广东
                        return "GD";
                    case "45":          // 45   广西
                        return "GX";
                    case "46":          // 46   海南
                        return "HAIN";
                    case "50":          // 50   重庆
                        return "CQ";
                    case "51":          // 51   四川
                        return "SC";
                    case "52":          // 52   贵州
                        return "GZ";
                    case "53":          // 53   云南
                        return "YN";
                    case "54":          // 54   西藏
                        return "XZ";
                    case "61":          // 61   陕西
                        return "SAX";
                    case "62":          // 62   甘肃
                        return "GS";
                    case "63":          // 63   青海
                        return "QH";
                    case "64":          // 64   宁夏
                        return "NX";
                    case "65":          // 65   新疆
                        return "XJ";
                    default:
                        return "";
                }
            }

        } catch (Exception e) {

        }
        return provinceCode;
    }
}
