package com.qcc.udf.casesearch_v3.util;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 个人隐私屏蔽
 * <AUTHOR>
 * @date 2021年12月13日 14:09
 */
public class PersonNameHideUtil {
    //空白字符
    public static final String BLANK_REGEX = "\\s*|\t|\r|\n";
    public static final Pattern BLANK_CHAR_PATTERN = Pattern.compile(BLANK_REGEX);

    /* 已知的复姓 */
    private static final String[] SURNAME = {
            "上官", "欧阳", "夏侯", "诸葛", "闻人", "东方", "赫连", "皇甫", "羊舌", "尉迟", "公羊", "澹台", "公冶", "宗正", "濮阳", "淳于",
            "单于", "太叔", "申屠", "公孙", "仲孙", "轩辕", "令狐", "钟离", "宇文", "长孙", "慕容", "鲜于", "闾丘", "司徒", "司空", "兀官",
            "司寇", "南门", "呼延", "子车", "颛孙", "端木", "巫马", "公西", "漆雕", "车正", "壤驷", "公良", "拓跋", "夹谷", "宰父", "谷梁",
            "段干", "百里", "东郭", "微生", "梁丘", "左丘", "东门", "西门", "南宫", "第五", "公仪", "公乘", "太史", "仲长", "叔孙", "屈突",
            "尔朱", "东乡", "相里", "胡母", "司城", "张廖", "雍门", "毋丘", "贺兰", "綦毋", "屋庐", "独孤", "南郭", "北宫", "王孙", "万俟",
            "司马"
    };


    /**
     * 脱敏人名
     *
     * @param  name  姓名入参
     * @return String  返回脱敏后的字符串
     */
    public static String nameEncrypt(String name,String keyNo) {
        if (StringUtils.isBlank(name)) {
            return name;
        }
        String showName = name;
        if (name.length() <= 4) {
            if (StringUtils.isBlank(keyNo) || keyNo.length()<10) {
                showName = nameEncry(name);
            }
        }
        return showName;
    }

    /**
     * 脱敏人名
     *
     * @param name 人名
     * @return String  已脱敏的人名
     */
    private static String nameEncry(String name) {
        if (StringUtils.isBlank(name)) {
            return name;
        }
        //剔除空白字符
        name = replaceBlank(name);
        if (name.length() > 4) {
            return name;
        }
        String newName = name.length() >= 2 ? name.substring(0, 2) : name;
        String surName = Arrays.asList(SURNAME).stream().filter(str -> newName.contains(str)).findFirst().orElse(name.substring(0, 1));
        StringBuffer encryName = new StringBuffer(surName);
        //计算*的数量
        int asteriskCount = name.length() - surName.length();
        for (int i = 0; i < asteriskCount; i++) {
            encryName.append("*");
        }
        return encryName.toString();
    }

    public static String replaceBlank(String str) {
        String dest = null;
        if (str == null) {
            return dest;
        } else {
            Matcher m = BLANK_CHAR_PATTERN.matcher(str);
            dest = m.replaceAll("");
            return dest;
        }
    }


    public static void main(String[] args) {
        System.out.println(nameEncrypt("欧阳母","11111111111"));
    }

}
