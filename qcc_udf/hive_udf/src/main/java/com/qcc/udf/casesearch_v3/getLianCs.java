package com.qcc.udf.casesearch_v3;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.input.SQTJEntity;
import com.qcc.udf.casesearch_v3.entity.output.LianListEntity;
import com.qcc.udf.casesearch_v3.entity.output.SQTJListEntity;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

/**
 * <AUTHOR>
 */
public class getLianCs extends UDF {
    public static String evaluate(String param) {
        Set<String> idSet = new LinkedHashSet<>();

        if (StringUtils.isNotEmpty(param)){
            List<InfoListEntity> infoList = JSONArray.parseArray(JSONObject.parseObject(param).getJSONArray("InfoList").toString(), InfoListEntity.class);

            Collections.sort(infoList, ((o1, o2 ) ->{
                return o1.getLatestTimestamp().compareTo(o2.getLatestTimestamp());
            }));

            InfoListEntity item = infoList.get(infoList.size() - 1);

            List<LianListEntity> lianList = item.getLianList();
            if (lianList.size() > 0){
                for (LianListEntity sub : lianList){
                    idSet.add(sub.getId());
                }
            }
            List<SQTJListEntity> sqtjList = item.getSqtjList();
            if (sqtjList.size() > 0){
                for (SQTJListEntity sub : sqtjList){
                    idSet.add(sub.getId());
                }
            }
        }

        return String.join(",", idSet);
    }

    public static void main(String[] args) {
        System.out.println(evaluate("{\"KtggCnt\":0,\"LastestDateType\":\"民事二审|立案日期\",\"XjpgCnt\":0,\"ZxCnt\":0,\"CfgsCnt\":0,\"LastestDate\":1597334400,\"AmtInfo\":{},\"EarliestDate\":1597334400,\"Source\":\"OT\",\"AnnoCnt\":1,\"EarliestDateType\":\"民事二审|立案日期\",\"XzcfCnt\":0,\"CompanyKeywords\":\"9a62d1e19ce44f7aafab86b4d2f64e05,深圳市万事达物流有限公司\",\"AnNoList\":\"（2020）粤03民终20871号\",\"GqdjCnt\":0,\"XgCnt\":0,\"Tags\":\"12\",\"FyggCnt\":0,\"ZbCnt\":0,\"LatestTrialRound\":\"民事二审\",\"CfdfCnt\":0,\"CaseName\":\"王达德与易贤锋,深圳市万事达物流有限公司民事案件\",\"CfxyCnt\":0,\"SxCnt\":0,\"Province\":\"GD\",\"GroupId\":\"e995bbb1741ec35c0bf39c066c117aad\",\"LianCnt\":1,\"CaseCnt\":0,\"HbcfCnt\":0,\"PcczCnt\":0,\"Type\":1,\"CaseType\":\"民事案件\",\"ProcuratorateList\":\"\",\"CaseRole\":\"[{\\\"D\\\":\\\"二审原告\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2,\\\"P\\\":\\\"王达德\\\",\\\"R\\\":\\\"原告\\\",\\\"RL\\\":[{\\\"R\\\":\\\"原告\\\",\\\"T\\\":\\\"二审\\\"}]},{\\\"D\\\":\\\"二审被告\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2,\\\"P\\\":\\\"易贤锋\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"二审\\\"}]},{\\\"D\\\":\\\"二审被告\\\",\\\"N\\\":\\\"9a62d1e19ce44f7aafab86b4d2f64e05\\\",\\\"O\\\":0,\\\"P\\\":\\\"深圳市万事达物流有限公司\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"二审\\\"}]}]\",\"CaseReason\":\"\",\"CourtList\":\"广东省深圳市中级人民法院\",\"Id\":\"85f4a73aaddc94e9ee6ee0e8c34040f1\",\"InfoList\":[{\"Defendant\":[{\"KeyNo\":\"\",\"Role\":\"被告\",\"Org\":-2,\"Name\":\"易贤锋\"},{\"KeyNo\":\"9a62d1e19ce44f7aafab86b4d2f64e05\",\"Role\":\"被告\",\"Org\":0,\"Name\":\"深圳市万事达物流有限公司\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"广东省深圳市中级人民法院\",\"LatestTimestamp\":1597334400,\"ZxList\":[],\"XzcffList\":[],\"CfdfList\":[],\"HbcfList\":[],\"CaseList\":[],\"TrialRound\":\"民事二审\",\"Prosecutor\":[{\"KeyNo\":\"\",\"Role\":\"原告\",\"Org\":-2,\"Name\":\"王达德\"}],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2020）粤03民终20871号\",\"CaseType\":\"民事案件\",\"LianList\":[{\"LianDate\":1597334400,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"王达德\"},{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"易贤锋\"},{\"KeyNo\":\"9a62d1e19ce44f7aafab86b4d2f64e05\",\"Org\":0,\"Name\":\"深圳市万事达物流有限公司\"}],\"Id\":\"738278b7f8349b5d1a3716fcd1ce42af\",\"IsValid\":1}],\"XgList\":[],\"CaseReason\":\"\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]}],\"SdggCnt\":0} "));
    }
}
