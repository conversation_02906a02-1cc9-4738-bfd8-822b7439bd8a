package com.qcc.udf.trade_mark.util;

import org.apache.commons.codec.digest.DigestUtils;

public class EncryptCoder {
	private static int EncryptKey = 54367;

	public static String EncryptId(long id) {
		return Encode(String.valueOf(id * EncryptKey));
	}

	public static String Encode(String str) {
		String htext = "";

		for (int i = 0; i < str.length(); i++) {
			htext = htext + (char) (str.charAt(i) + 30 - 1 * 2);
		}
		return htext;
	}

	public static String EncodeMd5Double(String input) {
		if (input != null && !"".equals(input)) {
			input = EncodeMd5(input + "_TMEncodeMD5Parameter");
			input = EncodeMd5("TMEncodeMD5Parameter|" + input);
		}

		return input;
	}

	public static String EncodeMd5(String input) {
		return DigestUtils.md5Hex(input);
	}

	public static void main(String[] args) {
		System.out.println(EncryptId(23693216));
	}
}