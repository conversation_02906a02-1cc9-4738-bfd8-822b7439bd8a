package com.qcc.udf.risk_analysis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.qcc.udf.court_notice.anUtils.MD5Util;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class getRiskAnalysisSumInfo extends UDF {

	//限制详情count数
	private static final Integer LIMIT_COUNT = 100;
	//分隔符
	private static final String SPLIT_KEY = ",";

	public String evaluate(String keyNo, List<String> pcczinfo, List<String> xginfo, List<String> sxinfo, List<String> zxinfo,
	                       List<String> zbinfo, List<String> cpwsinfo, List<String> lianinfo, List<String> ktgginfo, List<String> fygginfo,
	                       List<String> sdgginfo, List<String> xjpginfo, List<String> sfpminfo, List<String> gqdjinfo, List<String> zxbainfo,
	                       List<String> jyzxinfo, List<String> jyycinfo, List<String> gqczinfo, List<String> dcdyinfo, List<String> tddyinfo,
	                       List<String> dwdbinfo, List<String> gscginfo, List<String> yzwfinfo, List<String> xzcfinfo, List<String> hbcfinfo,
	                       List<String> sswfinfo, List<String> qsgginfo, List<String> wgclinfo, List<String> ccjcinfo, List<String> wzrjinfo,
						   List<String> cpzhinfo, List<String> xzcjinfo, List<String> sqtjinfo, List<String> hmdinfo, List<String> spaqinfo,
						   List<String> ssjinfo,List<String> zcccinfo) {
		// 全量数据
		JSONArray array = new JSONArray();

		// 破产重整
		editPcczInfo(keyNo, pcczinfo, array);
		// 限高
		editXgInfo(keyNo, xginfo, array);
		// 失信
		editSxInfo(keyNo, sxinfo, array);
		// 被执行
		editZxInfo(keyNo, zxinfo, array);
		// 获取终本信息
		editZbInfo(keyNo, zbinfo, array);
		// 裁判文书
		editCpwsInfo(keyNo, cpwsinfo, array);
		// 立案
		editLianInfo(keyNo, lianinfo, array);
		// 开庭公告
		editKtggInfo(keyNo, ktgginfo, array);
		// 法院公告
		editFyggInfo(keyNo, fygginfo, array);
		// 送达公告
		editSdggInfo(keyNo, sdgginfo, array);
		// 询价评估
		editXjpgInfo(keyNo, xjpginfo, array);
		// 司法拍卖
		editSfpmInfo(keyNo, sfpminfo, array);
		// 股权冻结
		editGqdjInfo(keyNo, gqdjinfo, array);
		// 注销备案
		editZxbaInfo(keyNo, zxbainfo, array);
		// 简易注销
		editJyzxInfo(keyNo, jyzxinfo, array);
		// 经营异常
		editJyycInfo(keyNo, jyycinfo, array);
		// 股权出质
		editGqczInfo(keyNo, gqczinfo, array);
		// 动产抵押
		editDcdyInfo(keyNo, dcdyinfo, array);
		// 土地抵押
		editTddyInfo(keyNo, tddyinfo, array);
		// 对外担保
		editDwdbInfo(keyNo, dwdbinfo, array);
		// 公示催告
		editGscgInfo(keyNo, gscginfo, array);
		// 严重违法
		editYzwfInfo(keyNo, yzwfinfo, array);
		// 行政处罚
		editXzcfInfo(keyNo, xzcfinfo, array);
		// 环保处罚
		editHbcfInfo(keyNo, hbcfinfo, array);
		// 税收违法
		editSswfInfo(keyNo, sswfinfo, array);
		// 欠税公告
		editQsggInfo(keyNo, qsgginfo, array);
		// 违规处理
		editWgclInfo(keyNo, wgclinfo, array);
		// 抽查检查
		editCcjcInfo(keyNo, ccjcinfo, array);
		// 未准入境
		editWzrjInfo(keyNo, wzrjinfo, array);
		//产品召回
		editCpzhInfo(keyNo, cpzhinfo, array);
		//限制出境
		editXzcjInfo(keyNo, xzcjinfo, array);
		//诉前调解
		editSqtjInfo(keyNo, sqtjinfo, array);
		//黑名单
		editHmdInfo(keyNo, hmdinfo, array);
		//食品安全
		editSpaqInfo(keyNo, spaqinfo, array);
		// 双随机
		editSSJInfo(keyNo,ssjinfo,array);
		//知产出质
		editZccZInfo(keyNo, zcccinfo, array);

		// 全维度统计
		editAllInfo(keyNo, array);
		// array.toString()
//		SerializeConfig config = SerializeConfig.getGlobalInstance();
//		config.put(Double.class, new DoubleSerializer("#.##"));
		return JSON.toJSONString(array, SerializerFeature.DisableCircularReferenceDetect);
	}

	/**
	 * 编辑全维度cnt信息
	 *
	 * @param keyNo 公司keyno
	 * @param array 基础数据信息
	 */
	public void editAllInfo(String keyNo, JSONArray array) {
		JSONObject type0Json = new JSONObject();
		type0Json.put("KeyNo", keyNo);
		type0Json.put("Type", WeiduEnum.ALL.getCode());
		type0Json.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.ALL.getCode().toString())));

		Set<Integer> wdSet = new LinkedHashSet<>();
		for (int i = 1; i <= 28; i++) {
			wdSet.add(i);
		}

		JSONArray detailJson = new JSONArray();
		for (Object o : array) {
			JSONObject jsonObject = (JSONObject) o;
			JSONObject item = new JSONObject();

			// 破产重整
			if (WeiduEnum.PCCZ.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.PCCZ.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 限高
			if (WeiduEnum.XG.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.XG.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 失信
			if (WeiduEnum.SX.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.SX.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
				item.put("B", 0);
				JSONArray array1 = jsonObject.getJSONObject("DetailInfo").getJSONArray("B");
				if (array1 != null && !array1.isEmpty()) {
					Iterator<Object> it = array1.iterator();
					while (it.hasNext()) {
						JSONObject jsonObject1 = (JSONObject) it.next();
						if ("1".equals(jsonObject1.getString("A"))) {
							item.put("B", jsonObject1.getInteger("B"));
						}
					}
				}
//                item.put("B", jsonObject.getJSONObject("DetailInfo").getJSONArray("B"));
			}

			// 被执行人
			if (WeiduEnum.ZX.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.ZX.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
				item.put("B", jsonObject.getJSONObject("DetailInfo").getBigDecimal("B"));
			}

			// 终本
			if (WeiduEnum.ZB.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.ZB.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
				item.put("B", jsonObject.getJSONObject("DetailInfo").getBigDecimal("C"));
			}

			// 裁判文书
			if (WeiduEnum.CPWS.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.CPWS.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
				item.put("B", jsonObject.getJSONObject("DetailInfo").getBigDecimal("B"));
			}

			// 立案
			if (WeiduEnum.LIAN.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.LIAN.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 开庭公告
			if (WeiduEnum.KTGG.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.KTGG.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 法院公告
			if (WeiduEnum.FYGG.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.FYGG.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 送达公告
			if (WeiduEnum.SDGG.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.SDGG.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 询价评估
			if (WeiduEnum.XJPG.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.XJPG.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
				item.put("B", jsonObject.getJSONObject("DetailInfo").getInteger("B"));
				item.put("C", jsonObject.getJSONObject("DetailInfo").getBigDecimal("C"));
				item.put("D", jsonObject.getJSONObject("DetailInfo").getBigDecimal("D"));
			}

			// 司法拍卖
			if (WeiduEnum.SFPM.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.SFPM.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 股权冻结
			if (WeiduEnum.GQDJ.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.GQDJ.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 注销备案
			if (WeiduEnum.ZXBA.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.ZXBA.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 简易注销
			if (WeiduEnum.JYZX.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.JYZX.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 经营异常
			if (WeiduEnum.JYYC.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.JYYC.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 股权出质
			if (WeiduEnum.GQCZ.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.GQCZ.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 动产抵押
			if (WeiduEnum.DCDY.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.DCDY.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 土地抵押
			if (WeiduEnum.TDDY.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.TDDY.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 对外担保
			if (WeiduEnum.DWDB.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.DWDB.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 公示催告
			if (WeiduEnum.GSCG.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.GSCG.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 严重违法
			if (WeiduEnum.YZWF.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.YZWF.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 行政处罚
			if (WeiduEnum.XZCF.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.XZCF.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
				item.put("B", jsonObject.getJSONObject("DetailInfo").getString("B"));
			}

			// 环保处罚
			if (WeiduEnum.HBCF.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.HBCF.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
				item.put("B", jsonObject.getJSONObject("DetailInfo").getString("D"));
			}

			// 税收违法
			if (WeiduEnum.SSWF.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.SSWF.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
				item.put("B", jsonObject.getJSONObject("DetailInfo").getBigDecimal("B"));
				item.put("C", jsonObject.getJSONObject("DetailInfo").getBigDecimal("C"));
			}

			// 欠税公告
			if (WeiduEnum.QSGG.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.QSGG.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
				item.put("B", jsonObject.getJSONObject("DetailInfo").getBigDecimal("B"));
				item.put("C", jsonObject.getJSONObject("DetailInfo").getBigDecimal("C"));
			}

			// 违规处理
			if (WeiduEnum.WGCL.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.WGCL.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
				item.put("B", jsonObject.getJSONObject("DetailInfo").getBigDecimal("B"));
			}

			// 抽查检查
			if (WeiduEnum.CCJC.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.CCJC.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 未准入境
			if (WeiduEnum.WZRJ.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.WZRJ.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}
			// 产品召回
			if (WeiduEnum.CPZH.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.CPZH.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			//限制出境
			if (WeiduEnum.XZCJ.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.XZCJ.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
				item.put("B", jsonObject.getJSONObject("DetailInfo").getInteger("B"));
			}
			//诉前调解
			if (WeiduEnum.SQTJ.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.SQTJ.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}
			//黑名单
			if (WeiduEnum.HMD.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.HMD.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}
			//食品安全
			if (WeiduEnum.SPAQ.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.SPAQ.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}
			// 双随机
			if (WeiduEnum.SSJCC.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.SSJCC.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 知产出质
			if (WeiduEnum.ZSCQCZ.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.ZSCQCZ.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
				item.put("B", jsonObject.getJSONObject("DetailInfo").getInteger("B"));
			}

			detailJson.add(item);
			wdSet.remove(jsonObject.getInteger("Type"));
		}

		for (Integer i : wdSet) {
			JSONObject item = new JSONObject();
			item.put("Type", i);
			item.put("A", 0);
			detailJson.add(item);
		}

		type0Json.put("DetailInfo", detailJson);

		array.add(type0Json);
	}

	/**
	 * 编辑破产重整信息
	 *
	 * @param keyNo    公司keynoSX
	 * @param pcczinfo 破产重整信息
	 * @param array    列表数据
	 */
	public void editPcczInfo(String keyNo, List<String> pcczinfo, JSONArray array) {
		if (pcczinfo != null && pcczinfo.size() > 0) {
			for (String str : pcczinfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", jsonObject.getInteger("def_sum"));
					cntInfo.put("C", jsonObject.getInteger("self_sum"));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.PCCZ.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.PCCZ.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
	}

	/**
	 * 编辑限高信息
	 *
	 * @param keyNo  公司keyno
	 * @param xginfo 限高
	 * @param array  列表数据
	 */
	public void editXgInfo(String keyNo, List<String> xginfo, JSONArray array) {
		if (xginfo != null && xginfo.size() > 0) {
			for (String str : xginfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.XG.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.XG.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
	}

	/**
	 * 编辑失信信息
	 *
	 * @param keyNo  公司keyno
	 * @param sxinfo 失信
	 * @param array  列表数据
	 */
	public void editSxInfo(String keyNo, List<String> sxinfo, JSONArray array) {
		if (sxinfo != null && sxinfo.size() > 0) {
			for (String str : sxinfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					String courtLevel = jsonObject.getString("exestatus");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(courtLevel)) {
							List<String> list = JSON.parseArray(courtLevel, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", sub.split("#FGF#")[0]);
								jsonObject1.put("B", Integer.valueOf(sub.split("#FGF#")[1]));

								array1.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("B", array1);

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.SX.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.SX.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    总金额
		// C    法院层级
		// D    案由
	}

	/**
	 * 编辑被执行人信息
	 *
	 * @param keyNo  公司keyno
	 * @param zxinfo 被执行人信息
	 * @param array  列表数据
	 */
	public void editZxInfo(String keyNo, List<String> zxinfo, JSONArray array) {
		if (zxinfo != null && zxinfo.size() > 0) {
			for (String str : zxinfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", new BigDecimal(jsonObject.getString("biaodi")));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.ZX.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.ZX.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    执行标的
	}

	/**
	 * 编辑终本信息
	 *
	 * @param keyNo  公司keyno
	 * @param zbinfo 终本信息
	 * @param array  列表数据
	 */
	public void editZbInfo(String keyNo, List<String> zbinfo, JSONArray array) {
		if (zbinfo != null && zbinfo.size() > 0) {
			for (String str : zbinfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", new BigDecimal(jsonObject.getString("biaodi")));
					cntInfo.put("C", new BigDecimal(jsonObject.getString("failureact")));
					cntInfo.put("D", jsonObject.getString("failurepercent"));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.ZB.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.ZB.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    执行标的
		// C    未履行金额
		// D    未履行比例
	}

	/**
	 * 编辑裁判文书信息
	 *
	 * @param keyNo    公司keyno
	 * @param cpwsinfo 裁判文书信息
	 * @param array    列表数据
	 */
	public void editCpwsInfo(String keyNo, List<String> cpwsinfo, JSONArray array) {
		if (cpwsinfo != null && cpwsinfo.size() > 0) {
			for (String str : cpwsinfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", jsonObject.getBigDecimal("money"));

					String courtLevel = jsonObject.getString("courtlevel");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(courtLevel)) {
							List<String> list = JSON.parseArray(courtLevel, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								String[] subStr = sub.split("#FGF#");
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								if (subStr.length > 2) {
									jsonObject1.put("C", limitDetailsCount(subStr[2], SPLIT_KEY));
								} else {
									jsonObject1.put("C", "");
								}
								array1.add(jsonObject1);
							}
						}
					} catch (Exception ex) {
						ex.printStackTrace();
					}
					cntInfo.put("C", array1);
					String courtReason = jsonObject.getString("casereason");
					JSONArray array2 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(courtReason)) {
							List<String> list2 = JSON.parseArray(courtReason, String.class);
							for (String sub : list2) {
								JSONObject jsonObject1 = new JSONObject();
								String[] subStr = sub.split("#FGF#");
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								if (subStr.length > 2) {
									jsonObject1.put("C", limitDetailsCount(subStr[2], SPLIT_KEY));
								} else {
									jsonObject1.put("C", "");
								}
								array2.add(jsonObject1);
							}
						}
					} catch (Exception ex) {
						ex.printStackTrace();
					}
					cntInfo.put("D", array2);


					cntInfo.put("E", 0);
//                    cntInfo.put("F", new BigDecimal(0.00));
					cntInfo.put("F", "0");
					String pro_sum = jsonObject.getString("pro_sum");
					try {
						if (StringUtils.isNotEmpty(pro_sum)) {
							String[] subStr = pro_sum.split("#FGF#");
							cntInfo.put("E", new Integer(subStr[0]));
                            cntInfo.put("F", new BigDecimal(subStr[1]));
							if (subStr.length > 2) {
								cntInfo.put("I", limitDetailsCount(subStr[2], SPLIT_KEY));
							} else {
								cntInfo.put("I", "");
							}
						}
					} catch (Exception ex) {
						ex.printStackTrace();
					}
					cntInfo.put("G", 0);
//                    cntInfo.put("H",new BigDecimal(0.00));
					cntInfo.put("H", "0");
					String def_sum = jsonObject.getString("def_sum");
					try {
						if (StringUtils.isNotEmpty(def_sum)) {
							String[] subStr = def_sum.split("#FGF#");
							cntInfo.put("G", new Integer(subStr[0]));
                            cntInfo.put("H", new BigDecimal(subStr[1]));
							if (subStr.length > 2) {
								cntInfo.put("J", limitDetailsCount(subStr[2], SPLIT_KEY));
							} else {
								cntInfo.put("J", "");
							}
						}
					} catch (Exception ex) {
						ex.printStackTrace();
					}

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.CPWS.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.CPWS.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    总金额
		// C    法院层级
		// D    案由
	}

	/**
	 * 编辑立案信息
	 *
	 * @param keyNo    公司keyno
	 * @param lianInfo 立案信息
	 * @param array    列表数据
	 */
	public void editLianInfo(String keyNo, List<String> lianInfo, JSONArray array) {
		if (lianInfo != null && lianInfo.size() > 0) {
			for (String str : lianInfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					String courtLevel = jsonObject.getString("court");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(courtLevel)) {
							List<String> list = JSON.parseArray(courtLevel, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								String[] subStr =  sub.split("#FGF#");
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								if (subStr.length > 2) {
									jsonObject1.put("C", limitDetailsCount(subStr[2], SPLIT_KEY));
								} else {
									jsonObject1.put("C", "");
								}
								array1.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("B", array1);
					cntInfo.put("C",jsonObject.getInteger("ygcnt"));
					cntInfo.put("D", jsonObject.getInteger("bgcnt"));
					cntInfo.put("E", limitDetailsCount(jsonObject.getString("ygids"), SPLIT_KEY));
					cntInfo.put("F", limitDetailsCount(jsonObject.getString("bgids"), SPLIT_KEY));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.LIAN.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.LIAN.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    法院层级
	}

	/**
	 * 编辑开庭公告
	 *
	 * @param keyNo    公司keyno
	 * @param ktggInfo 开庭公告
	 * @param array    列表数据
	 */
	public void editKtggInfo(String keyNo, List<String> ktggInfo, JSONArray array) {
		if (ktggInfo != null && ktggInfo.size() > 0) {
			for (String str : ktggInfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					String courtLevel = jsonObject.getString("casereason");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(courtLevel)) {
							List<String> list = JSON.parseArray(courtLevel, String.class);
							for (String sub : list) {
								String[] subStr = sub.split("#FGF#");
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								if (subStr.length > 2) {
									jsonObject1.put("C", limitDetailsCount(subStr[2], SPLIT_KEY));
								} else {
									jsonObject1.put("C", "");
								}
								array1.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("B", array1);
					cntInfo.put("C", jsonObject.getInteger("pro_sum"));
					cntInfo.put("D", jsonObject.getInteger("def_sum"));
					cntInfo.put("E", limitDetailsCount(jsonObject.getString("ygids"), SPLIT_KEY));
					cntInfo.put("F", limitDetailsCount(jsonObject.getString("bgids"), SPLIT_KEY));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.KTGG.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.KTGG.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    案由
	}

	/**
	 * 编辑法院公告信息
	 *
	 * @param keyNo    公司keyno
	 * @param fyggInfo 法院公告
	 * @param array    列表数据
	 */
	public void editFyggInfo(String keyNo, List<String> fyggInfo, JSONArray array) {
		if (fyggInfo != null && fyggInfo.size() > 0) {
			for (String str : fyggInfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					String category = jsonObject.getString("category");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(category)) {
							List<String> list = JSON.parseArray(category, String.class);
							for (String sub : list) {
								String[] subStr = sub.split("#FGF#");
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								if (subStr.length > 2) {
									jsonObject1.put("C", limitDetailsCount(subStr[2], SPLIT_KEY));
								} else {
									jsonObject1.put("C", "");
								}

								array1.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("C", array1);

					String casereason = jsonObject.getString("casereason");
					JSONArray array2 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(casereason)) {
							List<String> list2 = JSON.parseArray(casereason, String.class);
							for (String sub : list2) {
								String[] subStr = sub.split("#FGF#");
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								if (subStr.length > 2) {
									jsonObject1.put("C", limitDetailsCount(subStr[2], SPLIT_KEY));
								} else {
									jsonObject1.put("C", "");
								}
								array2.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("B", array2);

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.FYGG.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.FYGG.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    案由
		// C    公告类型
	}

	/**
	 * 编辑送达公告信息
	 *
	 * @param keyNo    公司keyno
	 * @param sdggInfo 送达公告
	 * @param array    列表数据
	 */
	public void editSdggInfo(String keyNo, List<String> sdggInfo, JSONArray array) {
		if (sdggInfo != null && sdggInfo.size() > 0) {
			for (String str : sdggInfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					String category = jsonObject.getString("category");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(category)) {
							List<String> list = JSON.parseArray(category, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								String[] subStr = sub.split("#FGF#");
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								if (subStr.length > 2) {
									jsonObject1.put("C", limitDetailsCount(subStr[2], SPLIT_KEY));
								} else {
									jsonObject1.put("C", "");
								}
								array1.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("C", array1);

					String casereason = jsonObject.getString("casereason");
					JSONArray array2 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(casereason)) {
							List<String> list2 = JSON.parseArray(casereason, String.class);
							for (String sub : list2) {
								JSONObject jsonObject1 = new JSONObject();
								String[] subStr = sub.split("#FGF#");
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								if (subStr.length > 2) {
									jsonObject1.put("C", limitDetailsCount(subStr[2], SPLIT_KEY));
								} else {
									jsonObject1.put("C", "");
								}
								array2.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("B", array2);

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.SDGG.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.SDGG.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    案由
		// C    公告类型
	}

	/**
	 * 编辑询价评估信息
	 *
	 * @param keyNo    公司keyno
	 * @param infoList 询价评估
	 * @param array    列表数据
	 */
	public void editXjpgInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (infoList != null && infoList.size() > 0) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);
					//18561427-24403907
					String evaluation_price = jsonObject.getString("evaluation_price");
					String[] ep = evaluation_price.split("-");
					BigDecimal price1 = new BigDecimal(ep[0]);
					BigDecimal price2 = null;
					if (ep.length > 1) {
						price2 = new BigDecimal(ep[1]);
					}
					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", jsonObject.getInteger("target_count"));
					cntInfo.put("C", price1);
					cntInfo.put("D", price2);

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.XJPG.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.XJPG.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    标的物个数
		// C    金额
	}

	/**
	 * 编辑司法拍卖信息
	 *
	 * @param keyNo    公司keyno
	 * @param infoList 司法拍卖
	 * @param array    列表数据
	 */
	public void editSfpmInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (infoList != null && infoList.size() > 0) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.SFPM.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.SFPM.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
	}

	/**
	 * 编辑股权冻结信息
	 *
	 * @param keyNo    公司keyno
	 * @param infoList 股权冻结
	 * @param array    列表数据
	 */
	public void editGqdjInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (infoList != null && infoList.size() > 0) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", jsonObject.getBigDecimal("amount"));
					cntInfo.put("C", jsonObject.getInteger("zxcnt"));
					cntInfo.put("D", jsonObject.getBigDecimal("zxamount"));
					cntInfo.put("E", jsonObject.getInteger("zxcasecnt"));
					cntInfo.put("F", jsonObject.getInteger("bdcnt"));
					cntInfo.put("G", jsonObject.getBigDecimal("bdamount"));
					cntInfo.put("H", jsonObject.getInteger("bdcasecnt"));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.GQDJ.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.GQDJ.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
	}

	/**
	 * 编辑注销备案信息
	 *
	 * @param keyNo    公司keyno
	 * @param infoList 注销备案
	 * @param array    列表数据
	 */
	public void editZxbaInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (infoList != null && infoList.size() > 0) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.ZXBA.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.ZXBA.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
	}

	/**
	 * 编辑简易注销信息
	 *
	 * @param keyNo    公司keyno
	 * @param infoList 简易注销
	 * @param array    列表数据
	 */
	public void editJyzxInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (infoList != null && infoList.size() > 0) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.JYZX.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.JYZX.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
	}

	/**
	 * 编辑经营异常信息
	 *
	 * @param keyNo    公司keyno
	 * @param infoList 经营异常
	 * @param array    列表数据
	 */
	public void editJyycInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (infoList != null && infoList.size() > 0) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", jsonObject.getInteger("removecnt"));
					cntInfo.put("C", jsonObject.getInteger("addcnt"));

					String category = jsonObject.getString("court");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(category)) {
							String[] categoryStr = category.split("#GFG#");
							for (String sub : categoryStr) {
								JSONObject jsonObject1 = new JSONObject();
								String[] subStr = sub.split("#FGF#");
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								//json处理
								JSONArray jsonArrayResult = new JSONArray();
								if (subStr.length > 2) {
									JSONArray jsonArray = JSON.parseArray(subStr[2]);
									int sum = 0;
									for(int i = 0; i < jsonArray.size(); i ++) {
										if (sum >= LIMIT_COUNT) {
											break;
										}
										JSONObject details = jsonArray.getJSONObject(i);
										parseDateFromJson(details, "PublishDate");
										parseDateFromJson(details, "AddDate");
										parseDateFromJson(details, "RemoveDate");
										details.put("AnnualReportYear", details.getOrDefault("AnnualReportYear", "") == null? "" : details.getOrDefault("AnnualReportYear", "").toString());
										jsonArrayResult.add(details);
										sum ++;
									}
								}
								jsonObject1.put("C", jsonArrayResult.toJSONString());
								array1.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("D", array1);

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.JYYC.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.JYYC.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    移除数
		// C    移入数
		// D    原因
	}

	/**
	 * 编辑股权出质信息
	 *
	 * @param keyNo    公司keyno
	 * @param infoList 股权出质
	 * @param array    列表数据
	 */
	public void editGqczInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (infoList != null && infoList.size() > 0) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", jsonObject.getBigDecimal("amount"));
					cntInfo.put("C", jsonObject.getInteger("czrcnt"));
					cntInfo.put("D", jsonObject.getBigDecimal("czramount"));
					cntInfo.put("E", jsonObject.getInteger("zqrcnt"));
					cntInfo.put("F", jsonObject.getBigDecimal("zqramount"));
					cntInfo.put("G", jsonObject.getInteger("bdcnt"));
					cntInfo.put("H", jsonObject.getBigDecimal("bdamount"));

					cntInfo.put("I", limitDetailsCount(jsonObject.getString("ids"), ","));
					cntInfo.put("J", limitDetailsCount(jsonObject.getString("czrids"), ","));
					cntInfo.put("K", limitDetailsCount(jsonObject.getString("zqrids"), ","));
					cntInfo.put("L", limitDetailsCount(jsonObject.getString("bdids"), ","));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.GQCZ.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.GQCZ.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
	}

	/**
	 * 编辑动产抵押信息
	 *
	 * @param keyNo    公司keyno
	 * @param infoList 动产抵押
	 * @param array    列表数据
	 */
	public void editDcdyInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (infoList != null && infoList.size() > 0) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", jsonObject.getBigDecimal("amount"));
					cntInfo.put("C", jsonObject.getInteger("zwrcnt"));
					cntInfo.put("D", jsonObject.getBigDecimal("zwramount"));
					cntInfo.put("E", jsonObject.getInteger("dyqrcnt"));
					cntInfo.put("F", jsonObject.getBigDecimal("dyqramount"));
					cntInfo.put("G", jsonObject.getString("ids"));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.DCDY.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.DCDY.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
	}

	/**
	 * 编辑土地抵押信息
	 *
	 * @param keyNo    公司keyno
	 * @param infoList 土地抵押
	 * @param array    列表数据
	 */
	public void editTddyInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (infoList != null && infoList.size() > 0) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", jsonObject.getInteger("mortgagor_count"));
					cntInfo.put("C", jsonObject.getString("mortgagor_count_percent"));
					cntInfo.put("D", jsonObject.getBigDecimal("mortgagor_area"));
					cntInfo.put("E", jsonObject.getBigDecimal("mortgagor_price"));
					cntInfo.put("F", jsonObject.getInteger("mortgage_count"));
					cntInfo.put("G", jsonObject.getString("mortgage_count_percent"));
					cntInfo.put("H", jsonObject.getBigDecimal("mortgage_area"));
					cntInfo.put("I", jsonObject.getBigDecimal("mortgage_price"));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.TDDY.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.TDDY.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
	}

	/**
	 * 编辑对外担保信息
	 *
	 * @param keyNo    公司keyno
	 * @param infoList 对外担保
	 * @param array    列表数据
	 */
	public void editDwdbInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (infoList != null && infoList.size() > 0) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", jsonObject.getBigDecimal("amount"));
					cntInfo.put("C", jsonObject.getInteger("dbcnt"));
					cntInfo.put("D", jsonObject.getBigDecimal("dbamount"));
					cntInfo.put("E", jsonObject.getInteger("bdbcnt"));
					cntInfo.put("F", jsonObject.getBigDecimal("bdbamount"));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.DWDB.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.DWDB.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
	}

	/**
	 * 编辑公示催告信息
	 *
	 * @param keyNo    公司keyno
	 * @param infoList 公示催告
	 * @param array    列表数据
	 */
	public void editGscgInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (infoList != null && infoList.size() > 0) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					cntInfo.put("B", 0);
					cntInfo.put("C", new BigDecimal(0));
					String apply_sum = jsonObject.getString("apply_sum");
					try {
						if (StringUtils.isNotEmpty(apply_sum)) {
							cntInfo.put("B", new Integer(apply_sum.split("#FGF#")[0]));
							cntInfo.put("C", new BigDecimal(apply_sum.split("#FGF#")[1]));
						}
					} catch (Exception ex) {
						ex.printStackTrace();
					}
					cntInfo.put("D", 0);
					cntInfo.put("E", new BigDecimal(0));
					String owner_sum = jsonObject.getString("owner_sum");
					try {
						if (StringUtils.isNotEmpty(owner_sum)) {
							cntInfo.put("D", new Integer(owner_sum.split("#FGF#")[0]));
							cntInfo.put("E", new BigDecimal(owner_sum.split("#FGF#")[1]));
						}
					} catch (Exception ex) {
						ex.printStackTrace();
					}

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.GSCG.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.GSCG.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
	}

	/**
	 * 编辑严重违法信息
	 *
	 * @param keyNo    公司keyno
	 * @param infoList 严重违法
	 * @param array    列表数据
	 */
	public void editYzwfInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (infoList != null && infoList.size() > 0) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", jsonObject.getInteger("removecnt"));
					cntInfo.put("C", jsonObject.getInteger("addcnt"));

					String category = jsonObject.getString("court");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(category)) {
							String[] categoryStr = category.split("#GFG#");
							for (String sub : categoryStr) {
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", sub.split("#FGF#")[0]);
								jsonObject1.put("B", new Integer(sub.split("#FGF#")[1]));
								//json处理
								JSONArray jsonArrayResult = new JSONArray();
								if (sub.split("#FGF#").length > 2) {
									JSONArray jsonArray = JSON.parseArray(sub.split("#FGF#")[2]);
									int sum = 0;
									for(int i = 0; i < jsonArray.size(); i ++) {
										if (sum >= LIMIT_COUNT) {
											break;
										}
										JSONObject details = jsonArray.getJSONObject(i);
										parseDateFromJson(details, "AddDate");
										parseDateFromJson(details, "RemoveDate");
										jsonArrayResult.add(details);
										sum ++;
									}
								}
								jsonObject1.put("C", jsonArrayResult.toJSONString());
								array1.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("D", array1);

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.YZWF.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.YZWF.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    移除数
		// C    移入数
		// D    原因
	}

	/**
	 * 编辑行政处罚信息
	 *
	 * @param keyNo    公司keyno
	 * @param infoList 行政处罚
	 * @param array    列表数据
	 */
	public void editXzcfInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (infoList != null && infoList.size() > 0) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", jsonObject.getBigDecimal("money"));

					String category = jsonObject.getString("punish_reason");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(category)) {
							List<String> list = JSON.parseArray(category, String.class);
							for (String sub : list) {
								String[] subStr = sub.split("#FGF#");
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								if (subStr.length > 2) {
									jsonObject1.put("C", limitDetailsCount(subStr[2], SPLIT_KEY));
								} else {
									jsonObject1.put("C", "");
								}

								array1.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("C", array1);

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.XZCF.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.XZCF.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
	}

	/**
	 * 编辑环保处罚信息
	 *
	 * @param keyNo    公司keyno
	 * @param hbcfInfo 环保处罚
	 * @param array    列表数据
	 */
	public void editHbcfInfo(String keyNo, List<String> hbcfInfo, JSONArray array) {
		if (hbcfInfo != null && hbcfInfo.size() > 0) {
			for (String str : hbcfInfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					String category = jsonObject.getString("illegaltype");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(category)) {
							List<String> list = JSON.parseArray(category, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								String[] subStr = sub.split("#FGF#");
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								if (subStr.length > 2) {
									jsonObject1.put("C", limitDetailsCount(subStr[2], SPLIT_KEY));
								} else {
									jsonObject1.put("C", "");
								}

								array1.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("C", array1);

					String casereason = jsonObject.getString("implementaion");
					JSONArray array2 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(casereason)) {
							List<String> list2 = JSON.parseArray(casereason, String.class);
							for (String sub : list2) {
								JSONObject jsonObject1 = new JSONObject();
								String[] subStr = sub.split("#FGF#");
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								if (subStr.length > 2) {
									jsonObject1.put("C", limitDetailsCount(subStr[2], SPLIT_KEY));
								} else {
									jsonObject1.put("C", "");
								}

								array2.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("B", array2);
					// 金额
					cntInfo.put("D", jsonObject.getBigDecimal("amt"));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.HBCF.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.HBCF.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    执行情况
		// C    违法类型
	}

	/**
	 * 编辑税收违法信息
	 *
	 * @param keyNo    公司keyno
	 * @param infoList 税收违法
	 * @param array    列表数据
	 */
	public void editSswfInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (infoList != null && infoList.size() > 0) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", jsonObject.getBigDecimal("punishamount"));
					cntInfo.put("C", jsonObject.getBigDecimal("taxamount"));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.SSWF.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.SSWF.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    处罚金额
		// C    税款
	}

	/**
	 * 编辑欠税公告信息
	 *
	 * @param keyNo    公司keyno
	 * @param infoList 欠税公告
	 * @param array    列表数据
	 */
	public void editQsggInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (infoList != null && infoList.size() > 0) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", jsonObject.getBigDecimal("amt"));
					cntInfo.put("C", jsonObject.getBigDecimal("new_amt"));

					String category = jsonObject.getString("category");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(category)) {
							List<String> list = JSON.parseArray(category, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								String[] subStr = sub.split("#FGF#");
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								if (subStr.length > 2) {
									jsonObject1.put("C", limitDetailsCount(subStr[2], SPLIT_KEY));
								} else {
									jsonObject1.put("C", "");
								}

								array1.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("D", array1);

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.QSGG.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.QSGG.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    金额
		// C    新增金额
		// D    欠税税种
	}

	/**
	 * 编辑违规处理信息
	 *
	 * @param keyNo    公司keyno
	 * @param infoList 违规处理
	 * @param array    列表数据
	 */
	public void editWgclInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (infoList != null && infoList.size() > 0) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", jsonObject.getBigDecimal("amount"));
					cntInfo.put("C", jsonObject.getInteger("companycnt"));
					cntInfo.put("D", jsonObject.getInteger("personcnt"));
					cntInfo.put("F", jsonObject.getJSONArray("companydetails"));
					cntInfo.put("G", jsonObject.getJSONArray("persondetails"));

					String category = jsonObject.getString("court");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(category)) {
							String[] categoryStr = category.split("#GFG#");
							for (String sub : categoryStr) {
								JSONObject jsonObject1 = new JSONObject();
								String[] subStr = sub.split("#FGF#");
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								JSONArray jsonArrayResult = new JSONArray();
								if (subStr.length > 2) {
									JSONArray jsonArray = JSON.parseArray(subStr[2]);
									int sum = 0;
									for(int i = 0; i < jsonArray.size(); i ++) {
										if (sum >= LIMIT_COUNT) {
											break;
										}
										JSONObject details = jsonArray.getJSONObject(i);
										jsonArrayResult.add(details);
										sum ++;
									}
								}
								jsonObject1.put("C", jsonArrayResult.toJSONString());

								array1.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("E", array1);

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.WGCL.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.WGCL.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
	}

	/**
	 * 编辑抽查检查信息
	 *
	 * @param keyNo    公司keyno
	 * @param infoList 抽查检查
	 * @param array    列表数据
	 */
	public void editCcjcInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (infoList != null && infoList.size() > 0) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					String category = jsonObject.getString("court");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(category)) {
							String[] categoryStr = category.split("#GFG#");
							for (String sub : categoryStr) {
								JSONObject jsonObject1 = new JSONObject();
								String[] subStr = sub.split("#FGF#");
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								JSONArray jsonArrayResult = new JSONArray();
								if (subStr.length > 2) {
									JSONArray jsonArray = JSON.parseArray(subStr[2]);
									int sum = 0;
									for(int i = 0; i < jsonArray.size(); i ++) {
										if (sum >= LIMIT_COUNT) {
											break;
										}
										JSONObject details = jsonArray.getJSONObject(i);
										if (details.containsKey("Date")) {
											JSONObject jsonDate = details.getJSONObject("Date");
											String item = jsonDate.getOrDefault("$numberLong", "0").toString();
											details.put("Date", item);
										} else {
											details.put("Date", "0");
										}
										details.put("Remark", details.getOrDefault("Remark", "") == null? "" : details.getOrDefault("Remark", "").toString());
										details.put("No", details.getOrDefault("No", "") == null ? "" : details.getOrDefault("No", "").toString());
										jsonArrayResult.add(details);
										sum ++;
									}
								}
								jsonObject1.put("C", jsonArrayResult.toJSONString());

								array1.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("B", array1);

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.CCJC.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.CCJC.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
	}

	/**
	 * 编辑未准入境信息
	 *
	 * @param keyNo  公司keyno
	 * @param myinfo 未准入境
	 * @param array  列表数据
	 */
	public void editWzrjInfo(String keyNo, List<String> myinfo, JSONArray array) {
		if (myinfo != null && myinfo.size() > 0) {
			for (String str : myinfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					String reason = jsonObject.getString("reason");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(reason)) {
							List<String> list = JSON.parseArray(reason, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", sub.split("#FGF#")[0]);
								jsonObject1.put("B", Integer.valueOf(sub.split("#FGF#")[1]));

								array1.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("B", array1);

					String origin = jsonObject.getString("origin");
					JSONArray array2 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(origin)) {
							List<String> list = JSON.parseArray(origin, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", sub.split("#FGF#")[0]);
								jsonObject1.put("B", Integer.valueOf(sub.split("#FGF#")[1]));

								array2.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("C", array2);

					String subyear = jsonObject.getString("subyear");
					JSONArray array3 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(subyear)) {
							List<String> list = JSON.parseArray(subyear, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", sub.split("#FGF#")[0]);
								jsonObject1.put("B", Integer.valueOf(sub.split("#FGF#")[1]));

								array3.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("D", array3);

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.WZRJ.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.WZRJ.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    总金额
		// C    法院层级
		// D    案由
	}



	/**
	 * 双随机
	 * @param keyNo
	 * @param ssjinfo
	 * @param array
	 */
	public void editSSJInfo(String keyNo, List<String> ssjinfo, JSONArray array){
		if (ssjinfo != null && ssjinfo.size() > 0) {
			for (String str : ssjinfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject c = new JSONObject();
					c.put("A", jsonObject.getInteger("cnt"));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.SSJCC.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.SSJCC.getCode());
					result.put("DetailInfo", c);
					array.add(result);
				}
			}
		}
	}

	/**
	 * 知产出质   出质次数 / 出质类型 / 出质年份
	 * @param keyNo
	 * @param zcczinfo
	 * @param array
	 */
	public void editZccZInfo(String keyNo, List<String> zcczinfo, JSONArray array){
		if (zcczinfo != null && zcczinfo.size() > 0) {
			for (String str : zcczinfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject c = new JSONObject();
					c.put("A", jsonObject.getInteger("cznums"));
					c.put("B", jsonObject.getInteger("cztimes"));

					String ctype = jsonObject.getString("type");
					try {
						if (StringUtils.isNotEmpty(ctype)) {
							List<String> list = JSON.parseArray(ctype, String.class);
							for (String sub : list) {
								String type = sub.split("#FGF#")[0];
								if (type.equals("1")) {
									c.put("C", Integer.valueOf(sub.split("#FGF#")[1]));
									c.put("D", sub.split("#FGF#")[2]);
								} else if (type.equals("2")) {
									c.put("E", Integer.valueOf(sub.split("#FGF#")[1]));
									c.put("F", sub.split("#FGF#")[2]);
								}
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}

					String pyear = jsonObject.getString("pyear");
					JSONArray pyeararray = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(pyear)) {
							List<String> list = JSON.parseArray(pyear, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", sub.split("#FGF#")[0]);
								jsonObject1.put("B", Integer.valueOf(sub.split("#FGF#")[1]));
								pyeararray.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					c.put("G", pyeararray);


					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.ZSCQCZ.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.ZSCQCZ.getCode());
					result.put("DetailInfo", c);
					array.add(result);
				}
			}
		}
	}


	/**
	 * Desc:产品召回 A:总数,B:各年份总数
	 *
	 * */
	public void editCpzhInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (CollectionUtils.isNotEmpty(infoList)) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					String subyear = jsonObject.getString("yearinfos");
					JSONArray jsonArray = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(subyear)) {
							List<String> list = JSON.parseArray(subyear, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", sub.split("#FGF#")[0]);
								jsonObject1.put("B", Integer.valueOf(sub.split("#FGF#")[1]));

								jsonArray.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("B", jsonArray);

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.CPZH.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.CPZH.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
	}

	/**
	 * Desc:限制出境 A:总数,B:各年份总数
	 *
	 * */
	public void editXzcjInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (CollectionUtils.isNotEmpty(infoList)) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", jsonObject.getBigDecimal("amount"));

					String subyear = jsonObject.getString("courtinfos");
					JSONArray jsonArray = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(subyear)) {
							List<String> list = JSON.parseArray(subyear, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", sub.split("#FGF#")[0]);
								jsonObject1.put("B", Integer.valueOf(sub.split("#FGF#")[1]));
								jsonObject1.put("C", sub.split("#FGF#")[2]);

								jsonArray.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("C", jsonArray);

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.XZCJ.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.XZCJ.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
	}

	/**
	 * Desc:诉前调解 A:总数,B:各年份总数
	 *
	 * */
	public void editSqtjInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (CollectionUtils.isNotEmpty(infoList)) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);
					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					String courtinfos = jsonObject.getString("courtinfos");
					JSONArray jsonArray = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(courtinfos)) {
							List<String> list = JSON.parseArray(courtinfos, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", sub.split("#FGF#")[0]);
								jsonObject1.put("B", Integer.valueOf(sub.split("#FGF#")[1]));
								jsonObject1.put("C", sub.split("#FGF#")[2]);

								jsonArray.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("B", jsonArray);

					String roleinfos = jsonObject.getString("roleinfos");
					try {

						if (StringUtils.isNotEmpty(roleinfos)) {
							List<String> list = JSON.parseArray(roleinfos, String.class);
							for (String sub : list) {
								String type = sub.split("#FGF#")[0];
								if (type.equals("1")) {
									//原告
									cntInfo.put("C", Integer.valueOf(sub.split("#FGF#")[1]));
									cntInfo.put("D", sub.split("#FGF#")[2]);
								} else if (type.equals("2")) {
									//原告
									cntInfo.put("E", Integer.valueOf(sub.split("#FGF#")[1]));
									cntInfo.put("F", sub.split("#FGF#")[2]);
								}
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.SQTJ.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.SQTJ.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
	}

	/**
	 * Desc:黑名单 A:总数,B:各年份总数
	 *
	 * */
	public void editHmdInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (CollectionUtils.isNotEmpty(infoList)) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					String typeinfos = jsonObject.getString("typeinfos");
					JSONArray jsonArray = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(typeinfos)) {
							List<String> list = JSON.parseArray(typeinfos, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", sub.split("#FGF#")[0]);
								jsonObject1.put("B", Integer.valueOf(sub.split("#FGF#")[1]));
								jsonObject1.put("C", sub.split("#FGF#")[2]);

								jsonArray.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("B", jsonArray);

					String officeinfos = jsonObject.getString("officeinfos");
					JSONArray jsonArray2 = new JSONArray();
					try {

						if (StringUtils.isNotEmpty(officeinfos)) {
							List<String> list = JSON.parseArray(officeinfos, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", sub.split("#FGF#")[0]);
								jsonObject1.put("B", Integer.valueOf(sub.split("#FGF#")[1]));
								jsonObject1.put("C", sub.split("#FGF#")[2]);

								jsonArray2.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("C", jsonArray2);

					String yearinfos = jsonObject.getString("yearinfos");
					JSONArray jsonArray3 = new JSONArray();
					try {

						if (StringUtils.isNotEmpty(yearinfos)) {
							List<String> list = JSON.parseArray(yearinfos, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", sub.split("#FGF#")[0]);
								jsonObject1.put("B", Integer.valueOf(sub.split("#FGF#")[1]));

								jsonArray3.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("D", jsonArray3);

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.HMD.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.HMD.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
	}

	/**
	 * Desc:食品安全 A:总数,B:各年份总数
	 *
	 * */
	public void editSpaqInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (CollectionUtils.isNotEmpty(infoList)) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					String yearinfos = jsonObject.getString("yearinfos");
					JSONArray jsonArray = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(yearinfos)) {
							List<String> list = JSON.parseArray(yearinfos, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", sub.split("#FGF#")[0]);
								jsonObject1.put("B", Integer.valueOf(sub.split("#FGF#")[1]));

								jsonArray.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("B", jsonArray);

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.SPAQ.getCode().toString())));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.SPAQ.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
	}

	/**
	 * Desc: 处理json中的date
	 *
	 * */
	private void parseDateFromJson(JSONObject jsonObject, String keyWords) {
		if (jsonObject.containsKey(keyWords)) {
			JSONObject jsonDate = jsonObject.getJSONObject(keyWords);
			String item = jsonDate.getOrDefault("$numberLong", "0").toString();
			jsonObject.put(keyWords, item);
		} else {
			jsonObject.put(keyWords, "0");
		}
	}

	/**
	 * Desc:处理id限制数
	 *
	 * */
	private String limitDetailsCount(String details, String splitKey) {
		if (StringUtils.isEmpty(details)) {
			return "";
		}
		String[] str = details.split(splitKey);
		List<String> list = new ArrayList<>();
		int sum = 0;
		for(String item : str) {
			if (StringUtils.isEmpty(item)) {
				continue;
			}
			if (sum >= LIMIT_COUNT) {
				break;
			}
			list.add(item);
			sum ++;
		}
		String result = list.stream().collect(Collectors.joining(splitKey));
		return result;
 	}

	public static void main(String[] args) {
		getRiskAnalysisSumInfo job = new getRiskAnalysisSumInfo();
		List<String> zbInfoList = new LinkedList<>();
		zbInfoList.add("{\"keyno\":\"808f846747a07eddf3f21b5f922b6967\",\"cnt\":1,\"typeinfos\":\"[\\\"欠薪#FGF#1#FGF#1106526a6b39228534f3ed3df8edc55b\\\"]\",\"officeinfos\":\"[\\\"人社部#FGF#1#FGF#1106526a6b39228534f3ed3df8edc55b\\\"]\",\"yearinfos\":\"[\\\"2021#FGF#1#FGF#1106526a6b39228534f3ed3df8edc55b\\\"]\"}");
		System.out.println(job.evaluate("808f846747a07eddf3f21b5f922b6967", null, null, null, null, null, null, null, null,
				null, null, null, null, null, null, null, null, null, null, null, null,
				null, null, null, null, null, null, null, null, null,null,null,null,zbInfoList,null,null, null));
	}
}
