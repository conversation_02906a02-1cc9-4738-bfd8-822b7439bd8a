package com.qcc.udf.overseas;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 业务UDF（海外企业）根据地址中的邮编信息匹配得到所在城市
 * ---------------------------------------------------------------------------------------------------------
 * add jar hdfs://ldh/data/hive/udf/qcc_udf.jar;
 * create temporary function GetCityInfoByZipCode as 'com.qcc.udf.overseas.GetCityInfoByZipCodeUDF';
 * ---------------------------------------------------------------------------------------------------------
 * select GetCityInfoByZipCode ('722 SW 97 PLACE CIRCLE      MIAMI, FL 33174');
 * 结果: MIAMI
 */
public class GetCityInfoByZipCodeUDF extends UDF {
    private final static Map<String, ZipCode> zipCodeMap;
    static {
        zipCodeMap = new HashMap<>();
        try {
            InputStream is = GetCityInfoByZipCodeUDF.class.getResourceAsStream("/us_zipcode_mapping.txt");
            BufferedReader br = new BufferedReader(new InputStreamReader(is));
            String line;
            while ((line = br.readLine()) != null) {
                List<String> itemList = Arrays.asList(line.split("\t"));
                if (itemList.size() > 1) {
                    ZipCode zipCode = new ZipCode();
                    zipCode.setZipCode(itemList.get(1));
                    zipCode.setType(itemList.get(2));
                    zipCode.setCity(itemList.get(3));
                    zipCode.setState(itemList.get(4));
                    zipCode.setCounty(itemList.size() > 5 ? itemList.get(5) : "");
                    zipCodeMap.put(itemList.get(1), zipCode);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String evaluate(String input) {
        String output = "";
        try {
            Set<String> codeSet = new HashSet<>();
            Matcher matcher = Pattern.compile("\\d{5}").matcher(input);
            while (matcher.find()) {
                codeSet.add(matcher.group());
            }

            Set<String> cities = new HashSet<>();
            for (String code : codeSet) {
                ZipCode zipCode = zipCodeMap.get(code);
                if (zipCode != null) {
                    String city = zipCode.getCity();
                    if (input.contains(city)) {
                        cities.add(city);
                    }
                }
            }
            output = StringUtils.join(cities, " | ");
        } catch (Exception e) {

        }
        return output;
    }

    static class ZipCode {
        private String zipCode;
        private String type;
        private String city;
        private String state;
        private String county;

        public String getZipCode() {
            return zipCode;
        }

        public void setZipCode(String zipCode) {
            this.zipCode = zipCode;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getState() {
            return state;
        }

        public void setState(String state) {
            this.state = state;
        }

        public String getCounty() {
            return county;
        }

        public void setCounty(String county) {
            this.county = county;
        }

        @Override
        public String toString() {
            return "ZipCode{" +
                    "zipCode='" + zipCode + '\'' +
                    ", type='" + type + '\'' +
                    ", city='" + city + '\'' +
                    ", state='" + state + '\'' +
                    ", county='" + county + '\'' +
                    '}';
        }
    }
}
