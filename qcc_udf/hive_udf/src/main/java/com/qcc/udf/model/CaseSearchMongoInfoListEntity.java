package com.qcc.udf.model;

import lombok.Data;

import java.util.LinkedList;
import java.util.List;

@Data
public class CaseSearchMongoInfoListEntity {

    private String trialRound = "";
    private String anno = "";
    private String caseReason = "";
    private List<CaseSearchMongoRoleEntity> prosecutor = new LinkedList<>();
    private List<CaseSearchMongoRoleEntity> defendant = new LinkedList<>();
    private String procuratorate = "";
    private String court = "";
    private String executeNo = "";
    private List<CaseSearchMongoSXListEntity> sxList = new LinkedList<>();
    private List<CaseSearchMongoZXListEntity> zxList = new LinkedList<>();
    private List<CaseSearchMongoXGListEntity> xgList = new LinkedList<>();
    private List<CaseSearchMongoCaseListEntity> caseList = new LinkedList<>();
    private List<CaseSearchMongoLianListEntity> lianList = new LinkedList<>();
    private List<CaseSearchMongoFyggListEntity> fyggList = new LinkedList<>();
    private List<CaseSearchMongoKtggListEntity> ktggList = new LinkedList<>();
    private List<CaseSearchMongoSdggListEntity> sdggList = new LinkedList<>();
    private List<CaseSearchMongoPcczListEntity> pcczList = new LinkedList<>();
    private List<CaseSearchMongoGqdjListEntity> gqdjList = new LinkedList<>();
    private List<CaseSearchMongoXjpgListEntity> xjpgList = new LinkedList<>();
    private List<CaseSearchMongoZbListEntity> zbList = new LinkedList<>();
    private List<CaseSearchMongoHbcfListEntity> hbcfList = new LinkedList<>();
    private List<CaseSearchMongoCfgsListEntity> cfgsList = new LinkedList<>();
    private List<CaseSearchMongoCfxyListEntity> cfxyList = new LinkedList<>();
    private List<CaseSearchMongoCfdfListEntity> cfdfList = new LinkedList<>();
    private Long latestTimestamp = 0L;
}