package com.qcc.udf.casesearch_v3.role;

import com.qcc.udf.casesearch_v3.enums.CaseReasonEnum;
import org.apache.hadoop.hive.ql.exec.UDF;
import parquet.Strings;

/**
 * @Auther: wuql
 * @Date: 2022/08/19 10:00
 * @Description: 当事人身份细化
 */
public class CleanPartiesRoleUDF extends UDF {
    public static String evaluate(String caseNo, String pltfKeynoArray, String defdKeynoArray, String tdptKeynoArray, String keynoArray) {
        PartiesRoleEntity partiesRoleEntity = new PartiesRoleEntity("", caseNo, pltfKeynoArray, defdKeynoArray, tdptKeynoArray, keynoArray);
        String partiesRole = PartiesRoleCleanUtil.cleanPartiesRoleAndRoleTag(partiesRoleEntity);

        return partiesRole;
    }


    public static void main(String[] args) {
        String caseNo = "（2015）朝执他字第4号";
        String pltfKeynoArray = "[{\"KeyNo\":\"gd4a8a2afc894054875785f0f67f8cfc\",\"Name\":\"北京市朝阳区人力资源和社会保障局\",\"Org\":4}]";
        String defdKeynoArray = "[{\"KeyNo\":\"9a618240351981b455316d957a9de31c\",\"Name\":\"北京华清中科科技有限公司\",\"Org\":0}]";
        String tdptKeynoArray = "[]";
        String keynoArray = "[{\"KeyNo\":\"gd4a8a2afc894054875785f0f67f8cfc\",\"Name\":\"北京市朝阳区人力资源和社会保障局\",\"Org\":4,\"Role\":\"原告/上诉人\",\"RoleTag\":0,\"RoleType\":11,\"Source\":1},{\"KeyNo\":\"9a618240351981b455316d957a9de31c\",\"Name\":\"北京华清中科科技有限公司\",\"Org\":0,\"Role\":\"被告/被上诉人\",\"RoleTag\":1,\"RoleType\":21,\"Source\":1}]";
        System.out.println(evaluate(caseNo, pltfKeynoArray, defdKeynoArray, tdptKeynoArray, keynoArray));
    }

}
