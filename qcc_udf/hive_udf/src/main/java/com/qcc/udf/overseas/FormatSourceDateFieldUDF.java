package com.qcc.udf.overseas;

import com.qcc.udf.overseas.constant.Time;
import com.qcc.udf.overseas.constant.UsStateEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 业务UDF（海外企业）格式化处理各州的日期字段值信息，统一为时间戳格式返回
 * ---------------------------------------------------------------------------------------------------------
 * add jar hdfs://ldh/data/hive/udf/qcc_udf.jar;
 * create temporary function FormatSourceDateField as 'com.qcc.udf.overseas.FormatSourceDateFieldUDF' ;
 * ---------------------------------------------------------------------------------------------------------
 * select FormatSourceDateField ('CA', '1/19/1970');
 * 结果: 1526400000
 */
public class FormatSourceDateFieldUDF extends UDF {

    /**
     * 格式化处理日期字段值
     * @param source 州缩写
     * @param dateInput 日期字段值
     * @return
     */
    public String evaluate(String source, String dateInput) {
        String output = dateInput;
        try {
            if (StringUtils.isNotBlank(source) && StringUtils.isNotBlank(dateInput)) {
                UsStateEnum usStateEnum = UsStateEnum.getStateEnumByName(source);
                switch (usStateEnum) {
                    case CALIFORNIA:
                    case FLORIDA:
                    case MICHIGAN:
                    case UTAH:
                    case INDIANA:
                    case ARIZONA:
                    case MISSISSIPPI:
                    case ARKANSAS:
                    case WESTVIRGINIA:
                    case GEORGIA:
                    case MISSOURI:
                    case VERMONT:
                    case VIRGINIA:
                    case RHODE_ISLAND:
                    case PENNSYLVANIA:
                    case MAINE:
                    case WYOMING:
                    case DELAWARE:
                    case MINNESOTA:
                    case NORTH_CAROLINA:
                    case NORTH_DAKOTA:
                    case NEVADA:
                    case LOUISIANA:
                    case SOUTH_DAKOTA:
                    case KANSAS:
                    case MONTANA:
                    case WISCONSIN:
                    case IOWA:
                    case ILLINOIS:
                        output = CommonUtil.getTimestampFromDateField(dateInput, Time.Formatter_T1);
                        break;
                    case WASHINGTON:
                        output = CommonUtil.getTimestampFromDateField(dateInput, Time.Formatter_T2);
                        break;
                    case KENTUCKY:  // 有特殊情况，需要前置处理
                        String[] splits = dateInput.split(" ");
                        output = ((splits != null && splits.length>0)?CommonUtil.getTimestampFromDateField(splits[0], Time.Formatter_T1):"");
                        break;
                    case ALABAMA:
                    case MASSACHUSETTS:
                    case OREGON:
                        output = CommonUtil.getTimestampFromDateField(dateInput, Time.Formatter_T3);
                        break;
                    case OKLAHOMA:
                        output = CommonUtil.getTimestampFromDateField(dateInput, Time.Formatter_T4);
                        break;
                    case HAWAII:
                    case CONNECTICUT:
                    case NEW_YORK:  // 需要前置处理（month day之间空格不规范）
                        List<String> itemList = Arrays.asList(dateInput.split(" "))
                                .stream()
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.toList());
                        output = CommonUtil.getTimestampFromDateField(StringUtils.join(itemList, " "), Time.Formatter_T5);
                        break;
                    case IDAHO:  // 该州记录中日期字段值均为空

                        break;
                    case MARYLAND:  // 该州记录中日期字段值均为空

                        break;
                    case ALASKA:    // 没有数据

                        break;
                    case COLORADO:  // 没有数据

                        break;
                    case NEBRASKA:  // 没有数据

                        break;
                    case NEW_HAMPSHIRE:  // 没有数据

                        break;
                    case NEW_JERSEY:  // 没有数据

                        break;
                    case NEW_MEXICO:  // 没有数据

                        break;
                    case OHIO:  // 没有数据

                        break;
                    case SOUTH_CAROLINA:  // 没有数据

                        break;
                    case TENNESSEE:  // 没有数据

                        break;
                    case TEXAS:  // 没有数据

                        break;
                    default:

                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return output;
    }



    public static void main(String[] args) {
        String state = "OR";
        String dateInput = "08-12-1982";

        System.out.println(new FormatSourceDateFieldUDF().evaluate(state, dateInput));
    }
}

