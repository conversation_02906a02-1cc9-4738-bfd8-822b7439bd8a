package com.qcc.udf.group;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 */
public class GetControllerMaxGPHolder extends UDF {
//    public static void main(String[] args) {
//        String msg = "[{\"KeyNo\":\"prb3d4d02b2f19ca9c447ca1c98eeab6\",\"Name\":\"林宁\",\"Org\":2,\"HasImage\":false,\"StockPercent\":0.0066,\"IsGP\":0,\"DataType\":0},{\"KeyNo\":\"58eb417000f1d36ba62a0ae842c5c3f1\",\"Name\":\"青岛城投城金控股集团有限公司\",\"Org\":0,\"HasImage\":true,\"StockPercent\":0.0061,\"IsGP\":0,\"DataType\":0},{\"KeyNo\":\"pr06bfa9a0039b8ba3a405e611897d8c\",\"Name\":\"王琛玮\",\"Org\":2,\"HasImage\":false,\"StockPercent\":0.0051,\"IsGP\":0,\"DataType\":0},{\"KeyNo\":\"\",\"Name\":\"中国工商银行股份有限公司-广发中证传媒交易型开放式指数证券投资基金\",\"Org\":-1,\"HasImage\":false,\"StockPercent\":0.0061,\"IsGP\":0,\"DataType\":0},{\"KeyNo\":\"b3bc13dfe29c1caaad29c9cd04126bff\",\"Name\":\"莘县融智兴业管理咨询中心（有限合伙）\",\"Org\":0,\"HasImage\":false,\"StockPercent\":0.0207,\"IsGP\":0,\"DataType\":0},{\"KeyNo\":\"dcf5109629a637be741bf8033c3408c0\",\"Name\":\"北京万达文化产业集团有限公司\",\"Org\":0,\"HasImage\":true,\"StockPercent\":0.0203,\"IsGP\":0,\"DataType\":0},{\"KeyNo\":\"34bf6260d6174fdc0c1665a15d071f30\",\"Name\":\"互爱（北京）科技股份有限公司\",\"Org\":0,\"HasImage\":true,\"StockPercent\":0.0075,\"IsGP\":0,\"DataType\":0},{\"KeyNo\":\"714b5082a7fd773bf8fc92e956127d32\",\"Name\":\"杭州臻希投资管理有限公司\",\"Org\":0,\"HasImage\":false,\"StockPercent\":0.0619,\"IsGP\":0,\"DataType\":0},{\"KeyNo\":\"c2dbad658a8febcfb63de4d876ed79bd\",\"Name\":\"北京万达投资有限公司\",\"Org\":0,\"HasImage\":false,\"StockPercent\":0.3899,\"IsGP\":0,\"DataType\":0},{\"KeyNo\":\"prfb67d8485b6797d95181643eb3aca0\",\"Name\":\"逄宇峰\",\"Org\":2,\"HasImage\":false,\"StockPercent\":0.0078,\"IsGP\":0,\"DataType\":0},{\"KeyNo\":\"pa21c9d253848f65999adb9011ed7d11\",\"Name\":\"孙喜双\",\"Org\":2,\"HasImage\":true,\"StockPercent\":0.0102,\"IsGP\":0,\"DataType\":0},{\"KeyNo\":\"pfe22dd3c43a466752807e52f4da7f04\",\"Name\":\"尹香今\",\"Org\":2,\"HasImage\":true,\"StockPercent\":0.0068,\"IsGP\":0,\"DataType\":0}]";
//        String result = evaluate(msg);
//        System.out.printf(result);
//    }

    public static String evaluate(String partnerJson) {
        String result = "0";
        try {
            if (StringUtils.isNotBlank(partnerJson)) {
                List<GroupPartnerOutItem> partnerList = JSONObject.parseArray(partnerJson, GroupPartnerOutItem.class);
                if (CollectionUtils.isNotEmpty(partnerList)) {
                    if (partnerList.stream().anyMatch(i -> i.getIsGP().equals(1) && StringUtils.isNotBlank(i.getKeyNo()))) {
                        result = "1";
                    } else {
                        partnerList.sort((o1, o2) -> o2.getStockPercent().compareTo(o1.getStockPercent()));
                        GroupPartnerOutItem firstItem = partnerList.get(0);
                        if (firstItem.getStockPercent() > 0.5 && StringUtils.isNotBlank(firstItem.getKeyNo())) {
                            result = "1";
                        } else if (partnerList.size() >= 2) {
                            GroupPartnerOutItem secondItem = partnerList.get(1);
                            Double diffPercent = firstItem.getStockPercent() - secondItem.getStockPercent();
                            if (diffPercent > 0 && secondItem.getStockPercent() > 0) {
                                result = (diffPercent * 100 / secondItem.getStockPercent()) >= 20 ? "1" : "0";
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            result = "0";
        }
        return result;
    }
}