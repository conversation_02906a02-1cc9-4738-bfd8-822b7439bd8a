package com.qcc.udf.tag;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * @Description: 标签实体对象
 * @author: wuql
 * @create: 2022-09-06 11:50
 */
@Data
public class TagEntity {
    /**
     * code，标签code
     */
    @JSONField(name = "C")
    private String C;

    /**
     * name,标签名称
     */
    @JSONField(name = "N")
    private String N;

    public TagEntity() {
    }

    public TagEntity(String c, String n) {
        C = c;
        N = n;
    }
}
