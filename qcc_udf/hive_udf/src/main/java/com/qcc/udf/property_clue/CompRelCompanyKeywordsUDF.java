package com.qcc.udf.property_clue;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther: wuql
 * @Date: 2023/1/8 17:00
 * @Description:
 */
public class CompRelCompanyKeywordsUDF extends UDF {
    public static String evaluate(String comp_rel_keywords) {
        if (StringUtils.isEmpty(comp_rel_keywords)) {
            return "";
        }
        return getCompRelCompanyKeywords(comp_rel_keywords);
    }

    /**
     * Desc:获取人员调整后keyNo
     */
    public static String getCompRelCompanyKeywords(String compRelCompKeywords) {
        String result = "";
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(compRelCompKeywords)) {
            List<String> personKeyNos = new ArrayList<>();
            String[] strings = compRelCompKeywords.split(",");
            for (String item : strings) {
                if (item.length() == 32 && item.startsWith("p")) {
                    personKeyNos.add(item);
                }
            }
            if (CollectionUtils.isNotEmpty(personKeyNos)) {
                result = personKeyNos.stream().collect(Collectors.joining(","));
            }
        }
        return result;
    }


}
