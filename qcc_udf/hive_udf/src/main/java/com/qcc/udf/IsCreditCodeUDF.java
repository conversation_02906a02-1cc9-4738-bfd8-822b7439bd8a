package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class IsCreditCodeUDF extends UDF {

    public Boolean evaluate(String str)  {
        if(str == null || str.isEmpty())
        {
            return false;
        }

        String pattern = "^(1[1239]|2[19]|3[123459]|4[19]|5[1239]|6[129]|7[129]|8[19]|9[123]|A[19]|N[1239]|Y1)[\\d]{6}[\\dA-Z]{9,10}$";

        Pattern r = Pattern.compile(pattern);

        // Now create matcher object.
        Matcher m = r.matcher(str);

        if(m.find()){
            return true;
        }

        return false;

    }

  
    // public static void main(String[] args) {

    //     String str = "92321302MA1XX7Q03";
    //     String pattern = "^(1[1239]|2[19]|3[123459]|4[19]|5[1239]|6[129]|7[129]|8[19]|9[123]|A[19]|N[1239]|Y1)[\\d]{6}[\\dA-Z]{9,10}$";
    
    //     Pattern r = Pattern.compile(pattern);
    
    //     // Now create matcher object.
    //     Matcher m = r.matcher(str);
    
    //     if(m.find())
    //     {
    //         System.out.println("true");
    //     }
    //     else{
    //         System.out.println("false");
    //     }
       
    // }
    
}