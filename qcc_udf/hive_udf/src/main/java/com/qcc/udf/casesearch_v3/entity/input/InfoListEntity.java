package com.qcc.udf.casesearch_v3.entity.input;

import com.alibaba.fastjson.annotation.JSONField;
import com.qcc.udf.casesearch_v3.entity.output.*;
import lombok.Data;

import java.util.LinkedList;
import java.util.List;

/**
 * @Auther: z<PERSON>qiang
 * @Date: 2020/11/11 17:54
 * @Description:明细数据
 */
@Data
public class InfoListEntity {
    /**
     * 阶段描述	-阶段
     */
    @JSONField(name = "TrialRound")
    private String trialRound = "";
    /**
     * 阶段描述	-案号
     */
    @JSONField(name = "AnNo")
    private String anno = "";
    /**
     * 阶段描述	-案由
     */
    @JSONField(name = "CaseReason")
    private String caseReason = "";
    /**
     * 阶段描述	-当事人
     * eg:[
     *                                        {
     * 						"Role": "申请执行人",
     * 						"KeyNo": "",
     * 						"Org": -2,
     * 						"Name": "陈桂香"
     *                    }
     * 				]
     */
    @JSONField(name = "Prosecutor")
    private List<NameAndKeyNoEntity> prosecutor = new LinkedList<>();
    /**
     * 阶段描述	-当事人
     * eg:[
     *                                        {
     * 						"Role": "被执行人",
     * 						"KeyNo": "12d6dbdb87107811a62173d591d63824",
     * 						"Org": 0,
     * 						"Name": "吉林省宏利房地产开发有限公司乾安第一分公司"
     *                    }
     * 				]
     */
    @JSONField(name = "Defendant")
    private List<NameAndKeyNoEntity> defendant = new LinkedList<>();
    /**
     * 阶段描述	-抗诉/监督机关
     */
    @JSONField(name = "Procuratorate")
    private String procuratorate = "";
    /**
     * 阶段描述	-法院
     */
    @JSONField(name = "Court")
    private String court = "";
    /**
     * 执行依据文书号（TODO 哪里使用？）
     */
    @JSONField(name = "ExecuteNo")
    private String executeNo = "";


    @JSONField(name = "SxList")
    private List<SXListEntity> sxList = new LinkedList<>();
    @JSONField(name = "ZxList")
    private List<ZXListEntity> zxList = new LinkedList<>();
    @JSONField(name = "XgList")
    private List<XGListEntity> xgList = new LinkedList<>();
    @JSONField(name = "CaseList")
    private List<CaseListEntity> caseList = new LinkedList<>();
    @JSONField(name = "LianList")
    private List<LianListEntity> lianList = new LinkedList<>();
    @JSONField(name = "FyggList")
    private List<FyggListEntity> fyggList = new LinkedList<>();
    @JSONField(name = "KtggList")
    private List<KtggListEntity> ktggList = new LinkedList<>();
    @JSONField(name = "SdggList")
    private List<SdggListEntity> sdggList = new LinkedList<>();
    @JSONField(name = "PcczList")
    private List<PcczListEntity> pcczList = new LinkedList<>();
    @JSONField(name = "GqdjList")
    private List<GqdjListEntity> gqdjList = new LinkedList<>();
    @JSONField(name = "XjpgList")
    private List<XjpgListEntity> xjpgList = new LinkedList<>();
    @JSONField(name = "ZbList")
    private List<ZbListEntity> zbList = new LinkedList<>();
    @Deprecated
    @JSONField(name = "HbcfList")
    private List<HbcfListEntity> hbcfList = new LinkedList<>();
    @Deprecated
    @JSONField(name = "CfgsList")
    private List<CfgsListEntity> cfgsList = new LinkedList<>();
    @Deprecated
    @JSONField(name = "CfxyList")
    private List<CfxyListEntity> cfxyList = new LinkedList<>();
    @Deprecated
    @JSONField(name = "CfdfList")
    private List<CfdfListEntity> cfdfList = new LinkedList<>();
    @Deprecated
    @JSONField(name = "XzcffList")
    private List<XZCFListEntity> xzcfList = new LinkedList<>();
    @JSONField(name = "SfpmList")
    private List<SFPMListEntity> sfpmList = new LinkedList<>();
    @JSONField(name = "SqtjList")
    private List<SQTJListEntity> sqtjList = new LinkedList<>();
    @JSONField(name = "XzcjList")
    private List<XZCJListEntity> xzcjList = new LinkedList<>();
    @JSONField(name = "XdpgjgList")
    private List<XDPGJGListEntity> xdpgjgList = new LinkedList<>();
    @JSONField(name = "XsggList")
    private List<XSGGListEntity> xsggList = new LinkedList<>();

    /**
     * 排序用
     */
    @JSONField(name = "LatestTimestamp")
    private Long latestTimestamp = 0L;

    /**
     * 纯案号数据日期标识
     */
    @JSONField(serialize = false)
    private Long simpleAnnoYear;

    /**
     * 案件类型
     */
    @JSONField(name = "CaseType")
    private String caseType;
}
