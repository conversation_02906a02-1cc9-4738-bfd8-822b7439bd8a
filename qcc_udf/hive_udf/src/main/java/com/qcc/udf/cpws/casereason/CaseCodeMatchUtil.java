package com.qcc.udf.cpws.casereason;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.casesearch_v3.role.CheckPartyRoleUtil;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 检查当事人身份
 */
public class CaseCodeMatchUtil {
    private static Map<String, List<ExcelObj>> LOCAL_VARS = new HashMap<>();

    static {
        init();
    }

    private static void init() {
        InputStream isr = null;
        BufferedReader br = null;
        try {
            isr = CheckPartyRoleUtil.class.getResourceAsStream("/case_reason_sort.csv");
            br = new BufferedReader(new InputStreamReader(isr));
        } catch (Exception e) {
            e.printStackTrace();
        }
        String line = "";
        List<ExcelObj> list = new ArrayList<>();
        String historyFirst = "", historySecond = "";
        try {
            while ((line = br.readLine()) != null) {
                if (line.indexOf('"') < 0) {
                    continue;
                }
                String[] firstAndSecond = line.substring(0, line.indexOf('"')).split(",");
                String first = firstAndSecond.length > 0 ? firstAndSecond[0] : "";
                String second = firstAndSecond.length > 1 ? firstAndSecond[1] : "";
                if (StringUtils.isEmpty(first)) {
                    first = historyFirst;
                } else {
                    historyFirst = first;
                }
                if ("-".equals(second)) {
                    historySecond = "";
                    second = "";
                } else if (StringUtils.isEmpty(second)) {
                    second = historySecond;
                } else {
                    historySecond = second;
                }
                ExcelObj obj = new ExcelObj();
                obj.setFirstLabel(first);
                obj.setSecondLabel(second);
                String substring = line.substring(line.indexOf('"')).replaceAll("\"", "");
                obj.setId(substring);
                list.add(obj);
            }
            LOCAL_VARS.putAll(list.stream().collect(Collectors.groupingBy(it -> it.getId())));
        } catch (Exception ex) {
        } finally {
            try {
                br.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                isr.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static String checkRole(String role) {
        JSONArray array = new JSONArray();
        List<ExcelObj> listRisk = Collections.EMPTY_LIST;
        try {
            List<ExcelObj> excelObjs = LOCAL_VARS.get(role);
            if (excelObjs != null) {
                listRisk = excelObjs;
            }
        } catch (Exception e) {
        }

        listRisk.stream().filter(e -> e != null).forEach(e -> {
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(e));
            array.add(jsonObject);
        });

        return array.toString();
    }


    public static void main(String[] args) {
        String code = "A,A03,A0303,A030306";
        System.out.println(CaseCodeMatchUtil.checkRole(code));
    }

}
