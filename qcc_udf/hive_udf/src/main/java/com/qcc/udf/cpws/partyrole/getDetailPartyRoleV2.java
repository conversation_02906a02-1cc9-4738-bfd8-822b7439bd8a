package com.qcc.udf.cpws.partyrole;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.cpws.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

/**
 * <AUTHOR>
 */
public class getDetailPartyRoleV2 extends UDF {

    public String evaluate(String keyNo, String name, String caseRole) {

        Map<String, String> roleMap = new LinkedHashMap<>();
        if (StringUtils.isNotEmpty(caseRole)){
            try {
                JSONArray array = JSONArray.parseArray(caseRole);
                Iterator<Object> it = array.iterator();
                while (it.hasNext()){
                    JSONObject json = (JSONObject)it.next();
                    if (StringUtils.isNotEmpty(json.getString("N"))){
                        roleMap.put(json.getString("N"), json.getString("R") == null ? "" : json.getString("R"));
                    }
                    if (StringUtils.isNotEmpty(json.getString("P"))){
                        roleMap.put(json.getString("P"), json.getString("R") == null ? "" : json.getString("R"));
                    }
                }

            }catch (Exception ex){
                ex.printStackTrace();
            }
        }

        Set<String> roleSet = new LinkedHashSet<>();
        if (StringUtils.isNotEmpty(keyNo)){
            if (roleMap.keySet().contains(keyNo)){
                String role = roleMap.get(keyNo);
                int code = PartyRoleCodeEnum.findCode(role);

                roleSet.add(String.valueOf(code));
            }
        }
        if (StringUtils.isNotEmpty(name)){
            if (roleMap.keySet().contains(name)){
                String role = roleMap.get(name);
                int code = PartyRoleCodeEnum.findCode(role);

                roleSet.add(String.valueOf(code));
            }
        }

        Set<String> roleSetNew = new LinkedHashSet<>();
        for (String str : roleSet){
            if (str.equals("11")){
                roleSetNew.add("原告");
            }
            if (str.equals("12")){
                roleSetNew.add("申请执行人");
            }
            if (str.equals("13")){
                roleSetNew.add("上诉人");
            }
            if (str.equals("14")){
                roleSetNew.add("申请人");
            }
            if (str.equals("21")){
                roleSetNew.add("被告");
            }
            if (str.equals("22")){
                roleSetNew.add("被执行人");
            }
            if (str.equals("23")){
                roleSetNew.add("被上诉人");
            }
            if (str.equals("24")){
                roleSetNew.add("被申请人");
            }
        }

        return String.join(",", roleSetNew);
    }

    public static void main(String[] args) {
        System.out.println(new getDetailPartyRoleV2().evaluate("91846b92fc6cc7bee803839f73418cb8","杭州越华物业管理有限公司123","[{\"P\":\"杭州越华物业管理有限公司\",\"R\":\"原告\",\"ShowName\":\"杭州越华物业管理有限公司\",\"N\":\"91846b92fc6cc7bee803839f73418cb8\",\"O\":0},{\"P\":\"陈农祖\",\"R\":\"被告\",\"ShowName\":\"陈**\",\"N\":\"\",\"O\":-2}]"));
    }
}
