package com.qcc.udf.cpws.casesearch_v2;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.cpws.ExtractCaseTrialRoundUDF;
import com.qcc.udf.cpws.ExtractCaseTypeUDF;
import com.qcc.udf.cpws.ExtractCourtNameFromCaseNoUDF;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.stream.Collectors;

public class GetDetailInfoMSV2UDF extends UDF {

    public String evaluate(List<String> sourceLianList, List<String> sourceKtggList,
                           List<String> sourceSdggList, List<String> sourceFyggList,
                           List<String> sourceCaseList, List<String> sourcePcczList) {

        Collections.sort(sourceLianList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceKtggList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceSdggList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceFyggList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceCaseList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourcePcczList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });

        LawSuitV2Entity inputLawSuitEntity = new LawSuitV2Entity();
        inputLawSuitEntity.setLianList(sourceLianList);
        inputLawSuitEntity.setKtggList(sourceKtggList);
        inputLawSuitEntity.setSdggList(sourceSdggList);
        inputLawSuitEntity.setFyggList(sourceFyggList);
        inputLawSuitEntity.setCaseList(sourceCaseList);
        inputLawSuitEntity.setPcczList(sourcePcczList);

        List<LawSuitV2Entity> lawSuitEntityList = CommonV2Util.getLawSuitEntityList(inputLawSuitEntity, "ms");

        JSONArray detailInfoArray = new JSONArray();
        for (LawSuitV2Entity lawSuitEntity : lawSuitEntityList) {
            List<String> lianList = lawSuitEntity.getLianList();
            List<String> ktggList = lawSuitEntity.getKtggList();
            List<String> sdggList = lawSuitEntity.getSdggList();
            List<String> fyggList = lawSuitEntity.getFyggList();
            List<String> caseList = lawSuitEntity.getCaseList();
            List<String> pcczList = lawSuitEntity.getPcczList();

            Set<String> provinceCodeSet = CommonV2Util.collectProvinceCode(lianList, ktggList, sdggList, fyggList, caseList, pcczList);
            for (String provinceCode : provinceCodeSet) {
                JSONObject result = new JSONObject();
                try {
                    /**
                     * 各审理程序列表对应字段
                     */
                    // 审理程序总数
                    Set<String> caseNoSet = new LinkedHashSet<>();
                    // 搜索关键字集合
                    Set<String> searchWordSet = new TreeSet<>();
                    // 所有时间节点的map集合（key-> 时间戳; value->表示当前的节点状态，审判程序 + (立案/法院公告/开庭公告/送达公告)发布日期 或 判决日期 或 裁定日期）
                    Map<Long, String> dateNodeMap = new HashMap<>();

                    // 案号分组
                    Map<String, JSONObject> anNoMap = new LinkedHashMap<>();
                    Set<String> lianIdSet = new LinkedHashSet<>();
                    Set<String> ktggIdSet = new LinkedHashSet<>();
                    Set<String> sdggIdSet = new LinkedHashSet<>();
                    Set<String> fyggIdSet = new LinkedHashSet<>();
                    Set<String> caseIdSet = new LinkedHashSet<>();
                    Set<String> pcczIdSet = new LinkedHashSet<>();

                    dataClean(provinceCode, lianList, 1, anNoMap, caseNoSet, lianIdSet, searchWordSet, dateNodeMap);
                    dataClean(provinceCode, ktggList, 2, anNoMap, caseNoSet, ktggIdSet, searchWordSet, dateNodeMap);
                    dataClean(provinceCode, sdggList, 3, anNoMap, caseNoSet, sdggIdSet, searchWordSet, dateNodeMap);
                    dataClean(provinceCode, fyggList, 4, anNoMap, caseNoSet, fyggIdSet, searchWordSet, dateNodeMap);
                    dataClean(provinceCode, caseList, 5, anNoMap, caseNoSet, caseIdSet, searchWordSet, dateNodeMap);
                    dataClean(provinceCode, pcczList, 6, anNoMap, caseNoSet, pcczIdSet, searchWordSet, dateNodeMap);

                    // 按照案号显示数据
                    JSONArray array = new JSONArray();
                    for (String str : caseNoSet) {
                        /**
                         * 兼容执行案件中的infoList项
                         */
                        JSONObject jsonObj = anNoMap.get(str);
                        if (jsonObj != null) {
                            jsonObj = CommonV2Util.addExternalFieldToJsonStruct(jsonObj);
                            Long latestTimestamp = CommonV2Util.getLatestTimestampFromInfoListItem(jsonObj);
                            jsonObj.put("LatestTimestamp", latestTimestamp);
                            array.add(jsonObj);
                        }
                    }
                    result.put("InfoList", array);
                    result.put("AnNoList", StringUtils.join(caseNoSet, ","));

                    /**
                     * 案件统计字段
                     */
                    // 最新审理程序
                    result.put("LatestTrialRound", CommonV2Util.getLatestTrialRoundFromInfoList(array));
                    // 审理程序总数
                    result.put("AnnoCnt", caseNoSet.size());
                    // 关联裁判文书数
                    result.put("CaseCnt", caseIdSet.size());
                    // 关联立案信息数
                    result.put("LianCnt", lianIdSet.size());
                    // 关联开庭公告数
                    result.put("KtggCnt", ktggIdSet.size());
                    // 关联送达公告数
                    result.put("SdggCnt", sdggIdSet.size());
                    // 关联法院公告数
                    result.put("FyggCnt", fyggIdSet.size());
                    // 关联破产公告数
                    result.put("PcczCnt", pcczIdSet.size());

                    // 兼容字段处理
                    // 关联被执行数
                    result.put("ZxCnt", 0);
                    // 关联失信数
                    result.put("SxCnt", 0);
                    // 关联限高数
                    result.put("XgCnt", 0);
                    // 关联终本案件数
                    result.put("ZbCnt", 0);
                    // 关联询价评估数
                    result.put("XjpgCnt", 0);
                    // 关联股权冻结数
                    result.put("GqdjCnt", 0);
                    // 关联环保处罚数
                    result.put("HbcfCnt", 0);
                    // 关联行政处罚（工商）
                    result.put("CfgsCnt", 0);
                    // 关联行政处罚（信用中国）
                    result.put("CfxyCnt", 0);
                    // 关联行政处罚（地方）
                    result.put("CfdfCnt", 0);

                    /**
                     * 案件基础字段
                     */
                    // 分组法院信息
                    result.put("GroupCourt", lawSuitEntity.getCourt());
                    // 所在省份编码
                    result.put("Province", provinceCode);
                    // 案件名称
                    result.put("CaseName", CommonV2Util.getCaseNameFromInfoList(array, "ms"));
                    // 案件类型
                    result.put("CaseType", "民事案件");
                    // 关联的公司或个人信息
                    result.put("CompanyKeywords", CommonV2Util.getCompanyKeywordsFromSearchWordSet(searchWordSet));
                    // 相关案号
                    result.put("AnNoList", CommonV2Util.getKeywordsFromInfoList(array, "AnNo"));
                    // 列表中的案由
                    result.put("CaseReason", CommonV2Util.getCaseReasonFromInfoList(array));
                    // 列表中的案件身份
                    /*String caseRoleInfo = getCaseRoleInfo(caseList, lianList, ktggList, fyggList, sdggList, provinceCode);
                    if (caseRoleInfo.equals("[]")) {
                        caseRoleInfo = CommonV2Util.getCaseRoleFromInfoList(array);
                    }*/
                    String caseRoleInfo = CommonV2Util.getCaseRoleFromInfoList(array);
                    result.put("CaseRole", caseRoleInfo);
                    // 相关法院
                    result.put("CourtList", CommonV2Util.getKeywordsFromInfoList(array, "Court"));
                    // 相关检察院
                    result.put("ProcuratorateList", CommonV2Util.getKeywordsFromInfoList(array, "Procuratorate"));

                    Map<Long, String> sortedDateNodeMap = new LinkedHashMap<>();
                    dateNodeMap.entrySet().stream()
                            .sorted(Map.Entry.comparingByKey())
                            .forEachOrdered(e -> sortedDateNodeMap.put(e.getKey(), e.getValue()));

                    result.put("EarliestDate", -1L);
                    result.put("EarliestDateType", "");
                    result.put("LastestDate", -1);
                    result.put("LastestDateType", "");

                    Long earliestDate = -1L;
                    String earliestDateType = "";
                    Long lastestDate = -1L;
                    String lastestDateType = "";
                    boolean flag = true;
                    for (Map.Entry<Long, String> sortedDateNodeEntry : sortedDateNodeMap.entrySet()) {
                        if (flag) {
                            earliestDate = sortedDateNodeEntry.getKey();
                            earliestDateType = sortedDateNodeEntry.getValue();
                            flag = false;
                        }
                        lastestDate = sortedDateNodeEntry.getKey();
                        lastestDateType = sortedDateNodeEntry.getValue();
                    }
                    result.put("EarliestDate", earliestDate);
                    result.put("EarliestDateType", CommonV2Util.getDataTypeWithoutTrialRound(earliestDateType));
                    if (sortedDateNodeMap.size() > 1) {
                        result.put("LastestDate", lastestDate);
                        result.put("LastestDateType", CommonV2Util.getDataTypeWithoutTrialRound(lastestDateType));
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                detailInfoArray.add(result);
            }
        }

        JSONArray resDetailInfoJSONArray = new JSONArray();
        Iterator iterator = detailInfoArray.iterator();
        while (iterator.hasNext()) {
            try {
                JSONObject jsonObject = (JSONObject) iterator.next();
                if (jsonObject.getLong("AnnoCnt") > 0) {
                    resDetailInfoJSONArray.add(jsonObject);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return resDetailInfoJSONArray.toJSONString();
    }

    /**
     *
     * @param provinceCode
     * @param infoList
     * @param type 1->立案信息 2->开庭公告 3->送达公告 4->法院公告 5->裁判文书 6->破产公告
     * @param anNoMap
     * @param caseNoSet
     * @param idSet
     * @param searchWordSet
     * @param dateNodeMap
     */
    public static void dataClean(String provinceCode, List<String> infoList, int type, Map<String, JSONObject> anNoMap,
                                 Set<String> caseNoSet, Set<String> idSet, Set<String> searchWordSet, Map<Long, String> dateNodeMap){
        // 编辑数据
        if (infoList != null && infoList.size() > 0) {
            Iterator it = infoList.iterator();
            while(it.hasNext()){
                JSONObject json = JSONObject.parseObject(it.next().toString());
                if (json == null || json.isEmpty()) {
                    continue;
                }
                // 过滤涉诉历史信息记录
                /*if (json.getInteger("isvalid") != 1) {
                    continue;
                }*/
                if (!json.getString("province").equals(provinceCode)) {
                    continue;
                }

                // 获取案号
                String anNo = CommonV2Util.full2Half(json.getString("anno"));
                if (type == 5 || type == 6){
                    anNo = CommonV2Util.full2Half(json.getString("caseno"));
                }
                // 过滤掉案号没有对应到执行案件类型的记录
                if (!new ExtractCaseTypeUDF().evaluate(anNo).equals("民事案件")) {
                    continue;
                }
                anNo = anNo.split("之")[0];
                Set<String> anNoSet = Arrays.stream(anNo.split(",")).collect(Collectors.toSet());
                caseNoSet.add(String.join(",",anNoSet));

                // 部分字段的汇总逻辑
                try {
                    List<String> companyNameList = new ArrayList<>();
                    if (type == 1) {
                        companyNameList = Arrays.stream(json.getString("companykeywords").split(","))
                                .collect(Collectors.toList());
                    } else if (type == 2 || type == 3 || type == 5) {
                        companyNameList = Arrays.stream(json.getString("companynames").split(","))
                                .collect(Collectors.toList());
                    } else if (type == 4) {
                        JSONArray jsonArray = JSONArray.parseArray(json.getString("nameandkeyno"));
                        for (int i = 0; i < jsonArray.size(); i++) {
                            JSONObject jsonObject = jsonArray.getJSONObject(i);
                            companyNameList.add(jsonObject.getString("Name"));
                            companyNameList.add(jsonObject.getString("KeyNo"));
                        }
                    } else if (type == 6) {
                        JSONArray respondentJsonArray = JSONArray.parseArray(json.getString("respondentnameandkeyno"));
                        for (int i = 0; i < respondentJsonArray.size(); i++) {
                            JSONObject jsonObject = respondentJsonArray.getJSONObject(i);
                            companyNameList.add(jsonObject.getString("Name"));
                            companyNameList.add(jsonObject.getString("KeyNo"));
                        }
                        JSONArray applicantJsonArray = JSONArray.parseArray(json.getString("applicantnameandkeyno"));
                        for (int i = 0; i < applicantJsonArray.size(); i++) {
                            JSONObject jsonObject = respondentJsonArray.getJSONObject(i);
                            companyNameList.add(jsonObject.getString("Name"));
                            companyNameList.add(jsonObject.getString("KeyNo"));
                        }
                    }

                    // 汇总关联公司或个人信息
                    for (String companyName : companyNameList) {
                        searchWordSet.add(companyName);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

                // 判断该案号是否已经存在
                JSONObject jsonObject = new JSONObject();
                JSONArray itemArray = new JSONArray();
                JSONObject itemJson = new JSONObject();

                // 不存在，则创建新的对象
                if (!anNoMap.keySet().contains(anNo)){
                    // 列表数据
                    itemJson = editItemJson(json, type, dateNodeMap, anNo);
                }else{
                    // 存在则获取原有列表，进行数据补充
                    jsonObject = anNoMap.get(anNo);
                    // 列表数据
                    if (type == 1){
                        itemArray = jsonObject.getJSONArray("LianList");
                    }else if (type == 2){
                        itemArray = jsonObject.getJSONArray("KtggList");
                    }else if (type == 3){
                        itemArray = jsonObject.getJSONArray("SdggList");
                    }else if (type == 4){
                        itemArray = jsonObject.getJSONArray("FyggList");
                    }else if (type == 5){
                        itemArray = jsonObject.getJSONArray("CaseList");
                    }else if (type == 6) {
                        itemArray = jsonObject.getJSONArray("PcczList");
                    }
                    if (!idSet.contains(json.getString("id"))){
                        itemJson = editItemJson(json, type, dateNodeMap, anNo);
                    }
                }
                idSet.add(json.getString("id"));
                itemArray = itemArray == null ? new JSONArray() : itemArray;
                itemArray.add(itemJson);
                if (type == 1){
                    jsonObject.put("LianList", itemArray);
                }else if (type == 2){
                    jsonObject.put("KtggList", itemArray);
                }else if (type == 3){
                    jsonObject.put("SdggList", itemArray);
                }else if (type == 4){
                    jsonObject.put("FyggList", itemArray);
                }else if (type == 5){
                    jsonObject.put("CaseList", itemArray);
                }else if (type == 6) {
                    jsonObject.put("PcczList", itemArray);
                }
                jsonObject.put("AnNo", anNo);
                jsonObject.put("TrialRound", new ExtractCaseTrialRoundUDF().evaluate(anNo));
                jsonObject.put("CaseReason", "");
                jsonObject.put("Court", new ExtractCourtNameFromCaseNoUDF().evaluate(anNo));
                jsonObject.put("Procuratorate", "");
                jsonObject.put("ExecuteNo", "");        // 执行依据文书号

                if (type == 1 || type == 2) {
                    String caseRole = CommonV2Util.getCaseRoleFromLianOrKtggList(Arrays.asList(json.toJSONString()), type);
                    JSONArray caseRoleJsonArray = JSONArray.parseArray(caseRole);
                    if (caseRoleJsonArray != null && caseRoleJsonArray.size() > 0) {
                        JSONArray prosecutorArray = new JSONArray();
                        JSONArray defendantArray = new JSONArray();
                        for (int i = 0; i < caseRoleJsonArray.size(); i++) {
                            JSONObject caseRoleJson = caseRoleJsonArray.getJSONObject(i);

                            JSONObject jsonObj = new JSONObject();
                            jsonObj.put("Name", caseRoleJson.getString("P"));
                            jsonObj.put("KeyNo", caseRoleJson.getString("N"));
                            jsonObj.put("Role", caseRoleJson.getString("R"));
                            jsonObj.put("Org", caseRoleJson.getInteger("O"));

                            if (caseRoleJson.getString("R").equals("原告")) {
                                prosecutorArray.add(jsonObj);
                            } else if (caseRoleJson.getString("R").equals("被告")) {
                                defendantArray.add(jsonObj);
                            }
                        }
                        jsonObject.put("Prosecutor", prosecutorArray);
                        jsonObject.put("Defendant", defendantArray);
                    } else {
                        jsonObject.put("Prosecutor", new JSONArray());
                        jsonObject.put("Defendant", new JSONArray());
                    }

                    // 提取法院公告中的案由信息
                    if (type == 2) {
                        String caseReason = json.getString("casereason");
                        if (StringUtils.isNotBlank(caseReason)) {
                            jsonObject.put("CaseReason", caseReason);
                        }
                    }

                    // 法院提取
                    String court = json.getString("executegov");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }
                } else if (type == 3 || type == 4) {
                    if (jsonObject.getJSONArray("Defendant") == null) {
                        jsonObject.put("Defendant", new JSONArray());
                    }

                    if (jsonObject.getJSONArray("Prosecutor") == null) {
                        jsonObject.put("Prosecutor", new JSONArray());
                    }

                    String caseReason = json.getString("casereason");
                    if (StringUtils.isNotBlank(caseReason)) {
                        jsonObject.put("CaseReason", caseReason);
                    }

                    String caseRole = CommonV2Util.getCaseRoleFromLianOrKtggList(Arrays.asList(json.toJSONString()), 1);
                    JSONArray caseRoleJsonArray = JSONArray.parseArray(caseRole);
                    if (caseRoleJsonArray != null && caseRoleJsonArray.size() > 0) {
                        JSONArray prosecutorArray = new JSONArray();
                        JSONArray defendantArray = new JSONArray();
                        for (int i = 0; i < caseRoleJsonArray.size(); i++) {
                            JSONObject caseRoleJson = caseRoleJsonArray.getJSONObject(i);

                            JSONObject jsonObj = new JSONObject();
                            jsonObj.put("Name", caseRoleJson.getString("P"));
                            jsonObj.put("KeyNo", caseRoleJson.getString("N"));
                            jsonObj.put("Role", caseRoleJson.getString("R"));
                            jsonObj.put("Org", caseRoleJson.getInteger("O"));

                            if (caseRoleJson.getString("R").equals("原告")) {
                                prosecutorArray.add(jsonObj);
                            } else if (caseRoleJson.getString("R").equals("被告")) {
                                defendantArray.add(jsonObj);
                            }
                        }
                        jsonObject.put("Prosecutor", prosecutorArray);
                        jsonObject.put("Defendant", defendantArray);
                    } else {
                        jsonObject.put("Prosecutor", new JSONArray());
                        jsonObject.put("Defendant", new JSONArray());
                    }

                    // 法院提取
                    String court = json.getString("courtname");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }
                    String anotherCourt = json.getString("court");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", anotherCourt);
                    }
                } else if (type == 5) { // 从裁判文书信息中提取案由 / 当事人（双方）/ 执行法院
                    String trialRound = json.getString("trialround");
                    if (StringUtils.isNotBlank(trialRound)) {
                        jsonObject.put("TrialRound", trialRound);
                    }

                    String caseReason = json.getString("casereason");
                    if (StringUtils.isNotBlank(caseReason)) {
                        jsonObject.put("CaseReason", caseReason);
                    }

                    JSONArray prosecutorArray = CommonV2Util.getLitigantJSONArray(json.getString("prosecutor"), json.getString("caserole"));
                    if (prosecutorArray != null && prosecutorArray.size() > 0) {
                        jsonObject.put("Prosecutor", prosecutorArray);
                    }

                    JSONArray defendantArray = CommonV2Util.getLitigantJSONArray(json.getString("defendant"), json.getString("caserole"));
                    if (defendantArray != null && defendantArray.size() > 0) {
                        jsonObject.put("Defendant", defendantArray);
                    }

                    String court = json.getString("court");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }

                    JSONObject protestorganJsonObj = JSONObject.parseObject(json.getString("protestorgan"));
                    if (protestorganJsonObj != null && protestorganJsonObj.containsKey("name")) {
                        jsonObject.put("Procuratorate", protestorganJsonObj.getString("name"));
                    }
                } else if (type == 6) {
                    jsonObject.put("TrialRound", "破产");

                    String court = json.getString("courtname");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }

                    JSONArray applicantJsonArray = CommonV2Util.getLitigantJSONArrayFromPccz(
                            json.getString("applicantnameandkeyno"), "申请人");
                    jsonObject.put("Prosecutor", applicantJsonArray);

                    JSONArray respondentJsonArray = CommonV2Util.getLitigantJSONArrayFromPccz(
                            json.getString("respondentnameandkeyno"), "被申请人");
                    jsonObject.put("Defendant", respondentJsonArray);
                }
                anNoMap.put(anNo, jsonObject);
            }
        }
    }

    public static JSONObject editItemJson(JSONObject jsonObject, int type, Map<Long, String> dateNodeMap, String anNo){
        JSONObject result = new JSONObject();

        String trialRound = new ExtractCaseTrialRoundUDF().evaluate(anNo);
        // 编辑字段
        result.put("Id", jsonObject.getString("id"));
        result.put("IsValid", jsonObject.getInteger("isvalid"));

        if (type == 1) {        // 立案信息（lianList）
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = CommonV2Util.parseDateToTimeStamp(jsonObject.getString("punishdate"));
            result.put("LianDate", publishDate);
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "立案日期");
            }
        } else if (type == 2) { // 开庭公告（ktggList）
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long openDate = CommonV2Util.parseDateToTimeStamp(jsonObject.getString("liandate"));
            result.put("OpenDate", openDate);
            if (openDate != -1) {
                dateNodeMap.put(openDate, trialRound + "|" + "开庭时间");
            }
        } else if (type == 3) { // 送达公告（sdggList）
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));
            Long publishDate = -1L;
            if (jsonObject.containsKey("publishdate")){
                String publishdateStr = jsonObject.getString("publishdate");
                if (StringUtils.isNotBlank(publishdateStr) && StringUtils.isNumeric(publishdateStr.trim())){
                     publishDate = Long.parseLong(publishdateStr);
                }
            }

            result.put("PublishDate", publishDate);
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "送达公告发布日期");
            }
        } else if (type == 4) { // 法院公告（fyggList）
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("Category", jsonObject.getString("category"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = CommonV2Util.parseDateToTimeStamp(jsonObject.getString("publishdate"));
            result.put("PublishDate", publishDate);
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "法院公告刊登日期");
            }
        } else if (type == 5) { // 裁判文书（caseList）
            result.put("Id", jsonObject.getString("id"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));
            Long judgeDate = CommonV2Util.parseDateToTimeStamp(jsonObject.getString("judgedate"));
            result.put("JudgeDate", judgeDate);

            String docType = jsonObject.getString("doctype");
            if (StringUtils.isNotBlank(docType)) {
                if (docType.equals("ver")) {
                    result.put("DocType", "民事判决日期");
                    dateNodeMap.put(judgeDate, trialRound + "|" + "判决日期");
                    result.put("ResultType", "判决结果");
                } else {
                    result.put("DocType", "民事裁定日期");
                    dateNodeMap.put(judgeDate, trialRound + "|" + "裁定日期");
                    result.put("ResultType", "裁定结果");
                }
                result.put("Result", jsonObject.getString("judgeresult") == null ? "" : jsonObject.getString("judgeresult"));
            }
            result.put("Amt", jsonObject.getString("amountinvolved") == null ? "" : jsonObject.getString("amountinvolved"));
        } else if (type == 6) { // 破产重整(pcczList)
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("applicantnameandkeyno"));

            String category = "破产";
            String caseType = jsonObject.getString("casetype").trim();
            if (StringUtils.isNotBlank(caseType) && !caseType.equals("案件")) {
                category = caseType.replace("案件", "");
            }
            result.put("Category", category);
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = jsonObject.getLong("riskdate");
            result.put("PublishDate", publishDate != null ? publishDate : -1L);
            if (publishDate != null) {
                dateNodeMap.put(publishDate, "破产" + "|" + category + "公开日期");
            }
        }

        return result;
    }

    /**
     * 提取案件角色信息
     */
    private static String getCaseRoleInfo(List<String> caseList, List<String> lianList, List<String> ktggList, List<String> fyggList, List<String> sdggList, String provinceCode) {
        /**
         * 提取规则
         * 1，如果有裁判文书，取裁判文书中的当事人信息 - caserole
         * 2，如果裁判文书缺失，取开庭公告中的当事人信息 - prosecutorlistos / defendantlistos
         * 3，如果1、2都缺失，取立案信息中的当事人信息 - prosecutorlistos / defendantlistos
         * 4，如果1、2、3都缺失，取破产公告中的申请人和被申请人信息 - respondentnameandkeyno / applicantnameandkeyno
         */
        String caseRole = CommonV2Util.getCaseRoleFromCaseList(caseList, provinceCode);
        if (caseRole.equals(new JSONArray().toJSONString())) {
            caseRole = CommonV2Util.getCaseRoleFromLianOrKtggList(ktggList, 2);
        }
        if (caseRole.equals(new JSONArray().toJSONString())) {
            caseRole = CommonV2Util.getCaseRoleFromLianOrKtggList(lianList, 1);
        }
        if (caseRole.equals(new JSONArray().toJSONString())) {
            caseRole = CommonV2Util.getCaseRoleFromLianOrKtggList(sdggList, 1);
        }
        if (caseRole.equals(new JSONArray().toJSONString())) {
            caseRole = CommonV2Util.getCaseRoleFromLianOrKtggList(fyggList, 1);
        }
        return caseRole;
    }

    public static void main(String[] args) {

        List<String> lianList = new ArrayList<>();
        String lianStr1 ="{\"id\":\"f1c5dc11cad649108535c3be3beaf4f7\",\"companykeywords\":\"dc5e4d3d6bb53dd0000914755e1518c5,中信银行股份有限公司成都分行,中信银行成都分行,中信实业银行成都分行,,四川省乐至县天池藕粉有限责任公司,资阳市农业融资担保有限责任公司,杨后超,p45684877cfb22f3f76fb554659145f0,周勇,p05bcbde56d70971e485aff27605302b,简阳市西部果蔬交易中心有限公司,四川省简阳市大地生态发展有限公司,9cb9b35693dba82d274277fe779c54b3,f4b882646e2a6716c70c329ffe0f0664,资阳市农业产业化信用担保有限责任公司,资阳市千万工程信用担保有限责任公司,7f72621e2779ad61ffa57027469a9101,ad2d9ff63486064610aaeac8ae414553\",\"anno\":\"（2016）川0191民初9268号\",\"punishdate\":\"2016-08-02T00:00:00+08:00\",\"isvalid\":1,\"prosecutorlistos\":\"[{\\\"KeyNo\\\":\\\"dc5e4d3d6bb53dd0000914755e1518c5\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"中信银行股份有限公司成都分行\\\"}]\",\"defendantlistos\":\"[{\\\"KeyNo\\\":\\\"9cb9b35693dba82d274277fe779c54b3\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"四川省乐至县天池藕粉有限责任公司\\\"},{\\\"KeyNo\\\":\\\"f4b882646e2a6716c70c329ffe0f0664\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"资阳市农业融资担保有限责任公司\\\"},{\\\"KeyNo\\\":\\\"p45684877cfb22f3f76fb554659145f0\\\",\\\"Org\\\":2,\\\"Name\\\":\\\"杨后超\\\"},{\\\"KeyNo\\\":\\\"p05bcbde56d70971e485aff27605302b\\\",\\\"Org\\\":2,\\\"Name\\\":\\\"周勇\\\"},{\\\"KeyNo\\\":\\\"7f72621e2779ad61ffa57027469a9101\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"简阳市西部果蔬交易中心有限公司\\\"},{\\\"KeyNo\\\":\\\"ad2d9ff63486064610aaeac8ae414553\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"四川省简阳市大地生态发展有限公司\\\"}]\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"dc5e4d3d6bb53dd0000914755e1518c5\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"中信银行股份有限公司成都分行\\\"},{\\\"KeyNo\\\":\\\"9cb9b35693dba82d274277fe779c54b3\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"四川省乐至县天池藕粉有限责任公司\\\"},{\\\"KeyNo\\\":\\\"f4b882646e2a6716c70c329ffe0f0664\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"资阳市农业融资担保有限责任公司\\\"},{\\\"KeyNo\\\":\\\"p45684877cfb22f3f76fb554659145f0\\\",\\\"Org\\\":2,\\\"Name\\\":\\\"杨后超\\\"},{\\\"KeyNo\\\":\\\"p05bcbde56d70971e485aff27605302b\\\",\\\"Org\\\":2,\\\"Name\\\":\\\"周勇\\\"},{\\\"KeyNo\\\":\\\"7f72621e2779ad61ffa57027469a9101\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"简阳市西部果蔬交易中心有限公司\\\"},{\\\"KeyNo\\\":\\\"ad2d9ff63486064610aaeac8ae414553\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"四川省简阳市大地生态发展有限公司\\\"}]\",\"province\":\"SC\"}";
        lianList.add(lianStr1);
        List<String> ktggList = new ArrayList<>();
        String ktggStr1 ="{\"id\":\"79963471b81bcdae964ac828ead72b535\",\"liandate\":\"2017-02-06T09:30:00+08:00\",\"casereason\":\"\",\"executegov\":\"成都高新技术产业开发区人民法院\",\"anno\":\"（2016）川0191民初9268号\",\"province\":\"SC\",\"companynames\":\"dc5e4d3d6bb53dd0000914755e1518c5,中信银行股份有限公司成都分行,9cb9b35693dba82d274277fe779c54b3,四川省乐至县天池藕粉有限责任公司,f4b882646e2a6716c70c329ffe0f0664,资阳市农业融资担保有限责任公司,p45684877cfb22f3f76fb554659145f0,杨后超,p05bcbde56d70971e485aff27605302b,周勇,7f72621e2779ad61ffa57027469a9101,简阳市西部果蔬交易中心有限公司,ad2d9ff63486064610aaeac8ae414553,四川省简阳市大地生态发展有限公司\",\"isvalid\":\"1\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"dc5e4d3d6bb53dd0000914755e1518c5\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"中信银行股份有限公司成都分行\\\"},{\\\"KeyNo\\\":\\\"9cb9b35693dba82d274277fe779c54b3\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"四川省乐至县天池藕粉有限责任公司\\\"},{\\\"KeyNo\\\":\\\"f4b882646e2a6716c70c329ffe0f0664\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"资阳市农业融资担保有限责任公司\\\"},{\\\"KeyNo\\\":\\\"p45684877cfb22f3f76fb554659145f0\\\",\\\"Org\\\":2,\\\"Name\\\":\\\"杨后超\\\"},{\\\"KeyNo\\\":\\\"p05bcbde56d70971e485aff27605302b\\\",\\\"Org\\\":2,\\\"Name\\\":\\\"周勇\\\"},{\\\"KeyNo\\\":\\\"7f72621e2779ad61ffa57027469a9101\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"简阳市西部果蔬交易中心有限公司\\\"},{\\\"KeyNo\\\":\\\"ad2d9ff63486064610aaeac8ae414553\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"四川省简阳市大地生态发展有限公司\\\"}]\",\"prosecutorlistos\":\"[{\\\"keyno\\\":\\\"dc5e4d3d6bb53dd0000914755e1518c5\\\",\\\"Org\\\":0,\\\"name\\\":\\\"中信银行股份有限公司成都分行\\\"}]\",\"defendantlistos\":\"[{\\\"keyno\\\":\\\"9cb9b35693dba82d274277fe779c54b3\\\",\\\"Org\\\":0,\\\"name\\\":\\\"四川省乐至县天池藕粉有限责任公司\\\"},{\\\"keyno\\\":\\\"f4b882646e2a6716c70c329ffe0f0664\\\",\\\"Org\\\":0,\\\"name\\\":\\\"资阳市农业融资担保有限责任公司\\\"},{\\\"keyno\\\":\\\"p45684877cfb22f3f76fb554659145f0\\\",\\\"Org\\\":2,\\\"name\\\":\\\"杨后超\\\"},{\\\"keyno\\\":\\\"p05bcbde56d70971e485aff27605302b\\\",\\\"Org\\\":2,\\\"name\\\":\\\"周勇\\\"},{\\\"keyno\\\":\\\"7f72621e2779ad61ffa57027469a9101\\\",\\\"Org\\\":0,\\\"name\\\":\\\"简阳市西部果蔬交易中心有限公司\\\"},{\\\"keyno\\\":\\\"ad2d9ff63486064610aaeac8ae414553\\\",\\\"Org\\\":0,\\\"name\\\":\\\"四川省简阳市大地生态发展有限公司\\\"}]\"}";
        ktggList.add(ktggStr1);
        List<String> sdggList= new ArrayList<>();
        List<String> fyggList = new ArrayList<>();
        String fyggStr1 ="{\"id\":\"2131632\",\"anno\":\"(2016)川0191民初9268号\",\"category\":\"诉状副本及开庭传票\",\"court\":\"成都高新技术产业开发区人民法院\",\"publishdate\":\"2016-11-03T00:00:00+08:00\",\"isvalid\":1,\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"ad2d9ff63486064610aaeac8ae414553\\\",\\\"Name\\\":\\\"四川省简阳市大地生态发展有限公司\\\",\\\"Org\\\":0},{\\\"KeyNo\\\":\\\"p45684877cfb22f3f76fb554659145f0\\\",\\\"Name\\\":\\\"杨后超\\\",\\\"Org\\\":2},{\\\"KeyNo\\\":\\\"7f72621e2779ad61ffa57027469a9101\\\",\\\"Name\\\":\\\"简阳市西部果蔬交易中心有限公司\\\",\\\"Org\\\":0},{\\\"KeyNo\\\":\\\"9cb9b35693dba82d274277fe779c54b3\\\",\\\"Name\\\":\\\"四川省乐至县天池藕粉有限责任公司\\\",\\\"Org\\\":0},{\\\"KeyNo\\\":\\\"dc5e4d3d6bb53dd0000914755e1518c5\\\",\\\"Name\\\":\\\"中信银行股份有限公司成都分行\\\",\\\"Org\\\":0},{\\\"KeyNo\\\":\\\"f4b882646e2a6716c70c329ffe0f0664\\\",\\\"Name\\\":\\\"资阳市农业融资担保有限责任公司\\\",\\\"Org\\\":0}]\",\"province\":\"SC\",\"casereason\":\"借款合同纠纷\",\"prosecutorlistos\":\"[{\\\"KeyNo\\\":\\\"dc5e4d3d6bb53dd0000914755e1518c5\\\",\\\"Name\\\":\\\"中信银行股份有限公司成都分行\\\",\\\"Org\\\":0}]\",\"defendantlistos\":\"[{\\\"KeyNo\\\":\\\"p45684877cfb22f3f76fb554659145f0\\\",\\\"Name\\\":\\\"杨后超\\\",\\\"Org\\\":2},{\\\"KeyNo\\\":\\\"9cb9b35693dba82d274277fe779c54b3\\\",\\\"Name\\\":\\\"四川省乐至县天池藕粉有限责任公司\\\",\\\"Org\\\":0},{\\\"KeyNo\\\":\\\"f4b882646e2a6716c70c329ffe0f0664\\\",\\\"Name\\\":\\\"资阳市农业融资担保有限责任公司\\\",\\\"Org\\\":0},{\\\"KeyNo\\\":\\\"\\\",\\\"Name\\\":\\\"周勇\\\",\\\"Org\\\":-1},{\\\"KeyNo\\\":\\\"7f72621e2779ad61ffa57027469a9101\\\",\\\"Name\\\":\\\"简阳市西部果蔬交易中心有限公司\\\",\\\"Org\\\":0},{\\\"KeyNo\\\":\\\"ad2d9ff63486064610aaeac8ae414553\\\",\\\"Name\\\":\\\"四川省简阳市大地生态发展有限公司\\\",\\\"Org\\\":0}]\"}";
        fyggList.add(fyggStr1);
        String fyggStr2 ="{\"id\":\"2417735\",\"anno\":\"(2016)川0191民初9268号,(2016)川0191民初9268号之一\",\"category\":\"裁判文书\",\"court\":\"成都高新技术产业开发区人民法院\",\"publishdate\":\"2017-03-14T00:00:00+08:00\",\"isvalid\":1,\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"ad2d9ff63486064610aaeac8ae414553\\\",\\\"Name\\\":\\\"四川省简阳市大地生态发展有限公司\\\",\\\"Org\\\":0},{\\\"KeyNo\\\":\\\"p45684877cfb22f3f76fb554659145f0\\\",\\\"Name\\\":\\\"杨后超\\\",\\\"Org\\\":2},{\\\"KeyNo\\\":\\\"7f72621e2779ad61ffa57027469a9101\\\",\\\"Name\\\":\\\"简阳市西部果蔬交易中心有限公司\\\",\\\"Org\\\":0},{\\\"KeyNo\\\":\\\"9cb9b35693dba82d274277fe779c54b3\\\",\\\"Name\\\":\\\"四川省乐至县天池藕粉有限责任公司\\\",\\\"Org\\\":0},{\\\"KeyNo\\\":\\\"dc5e4d3d6bb53dd0000914755e1518c5\\\",\\\"Name\\\":\\\"中信银行股份有限公司成都分行\\\",\\\"Org\\\":0},{\\\"KeyNo\\\":\\\"f4b882646e2a6716c70c329ffe0f0664\\\",\\\"Name\\\":\\\"资阳市农业融资担保有限责任公司\\\",\\\"Org\\\":0}]\",\"province\":\"SC\",\"casereason\":\"借款合同纠纷\",\"prosecutorlistos\":\"[{\\\"KeyNo\\\":\\\"dc5e4d3d6bb53dd0000914755e1518c5\\\",\\\"Name\\\":\\\"中信银行股份有限公司成都分行\\\",\\\"Org\\\":0}]\",\"defendantlistos\":\"[{\\\"KeyNo\\\":\\\"p45684877cfb22f3f76fb554659145f0\\\",\\\"Name\\\":\\\"杨后超\\\",\\\"Org\\\":2},{\\\"KeyNo\\\":\\\"9cb9b35693dba82d274277fe779c54b3\\\",\\\"Name\\\":\\\"四川省乐至县天池藕粉有限责任公司\\\",\\\"Org\\\":0},{\\\"KeyNo\\\":\\\"f4b882646e2a6716c70c329ffe0f0664\\\",\\\"Name\\\":\\\"资阳市农业融资担保有限责任公司\\\",\\\"Org\\\":0},{\\\"KeyNo\\\":\\\"\\\",\\\"Name\\\":\\\"周勇\\\",\\\"Org\\\":-1},{\\\"KeyNo\\\":\\\"7f72621e2779ad61ffa57027469a9101\\\",\\\"Name\\\":\\\"简阳市西部果蔬交易中心有限公司\\\",\\\"Org\\\":0},{\\\"KeyNo\\\":\\\"ad2d9ff63486064610aaeac8ae414553\\\",\\\"Name\\\":\\\"四川省简阳市大地生态发展有限公司\\\",\\\"Org\\\":0}]\"}";
        fyggList.add(fyggStr2);
        String fyggStr3 ="{\"id\":\"2417735\",\"anno\":\"(2016)川0191民初9268号,(2016)川0191民初9268号之一\",\"category\":\"裁判文书\",\"court\":\"成都高新技术产业开发区人民法院\",\"publishdate\":\"2017-03-14T00:00:00+08:00\",\"isvalid\":1,\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"ad2d9ff63486064610aaeac8ae414553\\\",\\\"Name\\\":\\\"四川省简阳市大地生态发展有限公司\\\",\\\"Org\\\":0},{\\\"KeyNo\\\":\\\"p45684877cfb22f3f76fb554659145f0\\\",\\\"Name\\\":\\\"杨后超\\\",\\\"Org\\\":2},{\\\"KeyNo\\\":\\\"7f72621e2779ad61ffa57027469a9101\\\",\\\"Name\\\":\\\"简阳市西部果蔬交易中心有限公司\\\",\\\"Org\\\":0},{\\\"KeyNo\\\":\\\"9cb9b35693dba82d274277fe779c54b3\\\",\\\"Name\\\":\\\"四川省乐至县天池藕粉有限责任公司\\\",\\\"Org\\\":0},{\\\"KeyNo\\\":\\\"dc5e4d3d6bb53dd0000914755e1518c5\\\",\\\"Name\\\":\\\"中信银行股份有限公司成都分行\\\",\\\"Org\\\":0},{\\\"KeyNo\\\":\\\"f4b882646e2a6716c70c329ffe0f0664\\\",\\\"Name\\\":\\\"资阳市农业融资担保有限责任公司\\\",\\\"Org\\\":0}]\",\"province\":\"SC\",\"casereason\":\"借款合同纠纷\",\"prosecutorlistos\":\"[{\\\"KeyNo\\\":\\\"dc5e4d3d6bb53dd0000914755e1518c5\\\",\\\"Name\\\":\\\"中信银行股份有限公司成都分行\\\",\\\"Org\\\":0}]\",\"defendantlistos\":\"[{\\\"KeyNo\\\":\\\"p45684877cfb22f3f76fb554659145f0\\\",\\\"Name\\\":\\\"杨后超\\\",\\\"Org\\\":2},{\\\"KeyNo\\\":\\\"9cb9b35693dba82d274277fe779c54b3\\\",\\\"Name\\\":\\\"四川省乐至县天池藕粉有限责任公司\\\",\\\"Org\\\":0},{\\\"KeyNo\\\":\\\"f4b882646e2a6716c70c329ffe0f0664\\\",\\\"Name\\\":\\\"资阳市农业融资担保有限责任公司\\\",\\\"Org\\\":0},{\\\"KeyNo\\\":\\\"\\\",\\\"Name\\\":\\\"周勇\\\",\\\"Org\\\":-1},{\\\"KeyNo\\\":\\\"7f72621e2779ad61ffa57027469a9101\\\",\\\"Name\\\":\\\"简阳市西部果蔬交易中心有限公司\\\",\\\"Org\\\":0},{\\\"KeyNo\\\":\\\"ad2d9ff63486064610aaeac8ae414553\\\",\\\"Name\\\":\\\"四川省简阳市大地生态发展有限公司\\\",\\\"Org\\\":0}]\"}";
        fyggList.add(fyggStr3);
        List<String> caseList = new ArrayList<>();
        String caseStr1 ="{\"id\":\"eaaa772c7a99b269bbf403d55cf2b9910\",\"defendant\":\"四川省简阳市大地生态发展有限公司,杨后超,简阳市西部果蔬交易中心有限公司,9cb9b35693dba82d274277fe779c54b3,周勇,f4b882646e2a6716c70c329ffe0f0664,p45684877cfb22f3f76fb554659145f0,p05bcbde56d70971e485aff27605302b,7f72621e2779ad61ffa57027469a9101,四川省乐至县天池藕粉有限责任公司,ad2d9ff63486064610aaeac8ae414553,资阳市农业融资担保有限责任公司\",\"prosecutor\":\"dc5e4d3d6bb53dd0000914755e1518c5,中信银行股份有限公司成都分行\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"dc5e4d3d6bb53dd0000914755e1518c5\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"中信银行股份有限公司成都分行\\\"},{\\\"KeyNo\\\":\\\"9cb9b35693dba82d274277fe779c54b3\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"四川省乐至县天池藕粉有限责任公司\\\"},{\\\"KeyNo\\\":\\\"f4b882646e2a6716c70c329ffe0f0664\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"资阳市农业融资担保有限责任公司\\\"},{\\\"KeyNo\\\":\\\"p45684877cfb22f3f76fb554659145f0\\\",\\\"Org\\\":2,\\\"Name\\\":\\\"杨后超\\\"},{\\\"KeyNo\\\":\\\"p05bcbde56d70971e485aff27605302b\\\",\\\"Org\\\":2,\\\"Name\\\":\\\"周勇\\\"},{\\\"KeyNo\\\":\\\"7f72621e2779ad61ffa57027469a9101\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"简阳市西部果蔬交易中心有限公司\\\"},{\\\"KeyNo\\\":\\\"ad2d9ff63486064610aaeac8ae414553\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"四川省简阳市大地生态发展有限公司\\\"}]\",\"caseno\":\"（2016）川0191民初9268号\",\"submitdate\":\"2017-07-21T08:00:00+08:00\",\"judgedate\":\"2017-02-06T08:00:00+08:00\",\"courtdate\":\"2017-07-21T08:00:00+08:00\",\"casereason\":\"金融借款合同纠纷\",\"isvalid\":\"1\",\"trialround\":\"民事一审\",\"court\":\"成都高新技术产业开发区人民法院\",\"companynames\":\"四川省简阳市大地生态发展有限公司,9cb9b35693dba82d274277fe779c54b3,f4b882646e2a6716c70c329ffe0f0664,中信实业银行成都分行,p05bcbde56d70971e485aff27605302b,7f72621e2779ad61ffa57027469a9101,四川省乐至县天池藕粉有限责任公司,中信银行成都分行,资阳市农业融资担保有限责任公司,资阳市千万工程信用担保有限责任公司,杨后超,简阳市西部果蔬交易中心有限公司,周勇,p45684877cfb22f3f76fb554659145f0,dc5e4d3d6bb53dd0000914755e1518c5,资阳市农业产业化信用担保有限责任公司,ad2d9ff63486064610aaeac8ae414553,中信银行股份有限公司成都分行\",\"caserole\":\"[{\\\"P\\\":\\\"中信银行股份有限公司成都分行\\\",\\\"R\\\":\\\"原告\\\",\\\"N\\\":\\\"dc5e4d3d6bb53dd0000914755e1518c5\\\",\\\"O\\\":0},{\\\"P\\\":\\\"四川奥伦济律师事务所\\\",\\\"R\\\":\\\"代理律师事务所\\\",\\\"N\\\":\\\"w89592d3585aef04bc08a4082a130f99\\\",\\\"O\\\":4},{\\\"P\\\":\\\"四川省乐至县天池藕粉有限责任公司\\\",\\\"R\\\":\\\"被告\\\",\\\"N\\\":\\\"9cb9b35693dba82d274277fe779c54b3\\\",\\\"O\\\":0},{\\\"P\\\":\\\"资阳市农业融资担保有限责任公司\\\",\\\"R\\\":\\\"被告\\\",\\\"N\\\":\\\"f4b882646e2a6716c70c329ffe0f0664\\\",\\\"O\\\":0},{\\\"P\\\":\\\"杨后超\\\",\\\"R\\\":\\\"被告\\\",\\\"N\\\":\\\"p45684877cfb22f3f76fb554659145f0\\\",\\\"O\\\":2},{\\\"P\\\":\\\"周勇\\\",\\\"R\\\":\\\"被告\\\",\\\"N\\\":\\\"p05bcbde56d70971e485aff27605302b\\\",\\\"O\\\":2},{\\\"P\\\":\\\"简阳市西部果蔬交易中心有限公司\\\",\\\"R\\\":\\\"被告\\\",\\\"N\\\":\\\"7f72621e2779ad61ffa57027469a9101\\\",\\\"O\\\":0},{\\\"P\\\":\\\"四川省简阳市大地生态发展有限公司\\\",\\\"R\\\":\\\"被告\\\",\\\"N\\\":\\\"ad2d9ff63486064610aaeac8ae414553\\\",\\\"O\\\":0}]\",\"doctype\":\"ver\",\"protestorgan\":\"\",\"province\":\"SC\",\"amountinvolved\":\"14400000.00\",\"judgeresult\":\"一、被告四川省乐至县天池藕粉有限责任公司应于本判决生效之日起十日内向原告中信银行股份有限公司成都分行归还借款本金14400000元以及此款按照《人民币流动资金贷款合同》约定标准计算至本息付清之日止的利息、罚息、复利；  二、被告资阳市农业融资担保有限责任公司、杨后超、周勇、简阳市西部果蔬交易中心有限公司、四川省简阳市大地生态发展有限公司对被告四川省乐至县天池藕粉有限责任公司的上述债务承担连带清偿责任，其承担保证责任后，可向被告四川省乐至县天池藕粉有限责任公司追偿。\"}";
        caseList.add(caseStr1);
        List<String> pcczList = new ArrayList<>();      // 破产重整信息集合

        String output = new GetDetailInfoMSV2UDF().evaluate(lianList, ktggList, sdggList, fyggList, caseList, pcczList);
        System.out.println(output);

    }
}
