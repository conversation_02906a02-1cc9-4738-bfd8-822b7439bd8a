package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by xpc on 2018/10/10.
 */
public class IsInRegion extends UDF {

    public static String evaluate(double lng, double lat, String pointstr) throws Exception {
        List<String[]> pathList = new ArrayList<>();
        if(pointstr==null || lng==0 || lat==0)
        {
            return "0";
        }
        String[] path  =   pointstr.split(";");
        for (int i = 0; i < path.length; i++) {
            String[] coor = path[i].split(",");
            if (coor.length == 2){
                pathList.add(coor);
            }
            else
            {
                pathList = new ArrayList<>();
                break;
            }
        }
        //比较园区经纬度
        if (pathList.size() < 3)//点小于3无法构成多边形
        {
            return "0";
        }
        int iSum = 0;
        int pathCount = pathList.size();
        double longStart = 0, latiStart = 0, longEnd = 0, latiEnd = 0;
        double dLong = 0;
        for (int i = 0; i < pathCount; i++)
        {
            int nextIndex = i + 1;
            if (i == pathCount - 1)
            {
                nextIndex = 0;
            }
            longStart = Double.parseDouble(pathList.get(i)[0]) ;
            latiStart = Double.parseDouble(pathList.get(i)[1]);
            longEnd = Double.parseDouble(pathList.get(nextIndex)[0]);
            latiEnd = Double.parseDouble(pathList.get(nextIndex)[1]);

            //判断纬度即Y坐标是否在2点的Y坐标内，只有在其内水平线才会相交
            if ((lat >= latiStart && lat < latiEnd) ||
                    (lat >= latiEnd && lat < latiStart))
            {
                if (Math.abs(latiStart - latiEnd) > 0)
                {
                    dLong = longStart - ((longStart - longEnd) * (latiStart - lat)) / (latiStart - latiEnd);
                    if (dLong < lng)
                    {
                        iSum++;
                    }
                }
            }
        }

        if ((iSum % 2) != 0)
        {
            return "1";
        }
        return "0";
    }

}
