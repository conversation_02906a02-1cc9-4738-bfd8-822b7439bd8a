package com.qcc.udf;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;
import org.json.JSONException;
import org.json.JSONObject;
import ua_parser.Client;
import ua_parser.Parser;

/**
 * <AUTHOR>
 */
public class UserAgentUDF extends UDF {

    public static String evaluate(String ua) throws JSONException {
        if (StringUtils.isEmpty(ua)){
            return null;
        }
        Parser uaParser = new Parser();
        Client c = uaParser.parse(ua);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("os",c.os.family);
        jsonObject.put("ov",c.os.major);
        jsonObject.put("bs",c.userAgent.family);
        jsonObject.put("bv",c.userAgent.major);
        return jsonObject.toString();
    }

    public static void main(String[] args) throws JSONException {
        System.out.println(evaluate(""));

    }
}
