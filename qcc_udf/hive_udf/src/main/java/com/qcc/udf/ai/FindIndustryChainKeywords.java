package com.qcc.udf.ai;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class FindIndustryChainKeywords extends UDF {

    private static Set<String> keywords;

    static {
        Set<String> tempKeywords = new HashSet<>();
        InputStream inputStream = null;
        InputStreamReader inputStreamReader = null;
        BufferedReader reader = null;
        try {
            inputStream = FindIndustryChainKeywords.class.getResourceAsStream("/industry_chain_keywords.txt");
            inputStreamReader = new InputStreamReader(inputStream, "UTF-8");
            reader = new BufferedReader(inputStreamReader);
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                tempKeywords.add(line);
            }
            keywords = tempKeywords;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    public String evaluate(String text1) {
        List<String> matched = new ArrayList<>();
        for (String k: keywords) {
            if (text1.indexOf(k) > -1) {
                matched.add(k);
            }
        }
        return StringUtils.join(matched, "^");
    }

    public static void main(String[] args) {
        System.out.println(new FindIndustryChainKeywords().evaluate("地诺孕素地中海水牛"));
    }

}
