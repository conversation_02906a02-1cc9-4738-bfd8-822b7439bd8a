package com.qcc.udf.casesearch_v3.entity.output;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class SFPMListEntity  extends BaseCaseOutEntity{
    @JSONField(name = "Id")
    private String id = "";
    @JSONField(name = "IsValid")
    private Integer isValid = 0;
    /**
     * 拍卖物所有人
     */
    @JSONField(name = "OwnerKeyNoArray")
    private List<NameAndKeyNoEntity> ownerKeyNoArray;
    @JSONField(name = "LianDate")
    private Long lianDate = 0L;

    /**
     * 起拍价
     */
    @JSONField(name = "YiWu")
    private String yiwu;

    /**
     * 起拍价单位
     */
    @JSONField(name = "AmountUnit")
    private String amountunit;

    /**
     * 评估价
     */
    @JSONField(name = "EvaluationPrice")
    private String evaluationprice;

    /**
     * 法拍卖名称
     */
    @JSONField(name = "Name")
    private String name;

    /**
     * 标的
     */
    @JSONField(name = "BiaoDi")
    private String biaodi;
}
