package com.qcc.udf.liu;

import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.cpws.CommonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

public class getDcrzInfo extends UDF {

    public static String evaluate(List<String> infoList)  throws Exception{
        JSONObject result = new JSONObject();
        String priority = "3";
        String dataStatus = "1";

        if(CollectionUtils.isNotEmpty(infoList)){
            long maxDate = 0L;
            long maxRegDate = 0L;
            Set<Long> dateSet = new LinkedHashSet<>();
            JSONObject maxInfo = null;
            JSONObject maxRegInfo = null;
            for (String str : infoList){
                JSONObject json = JSONObject.parseObject(str);
                String createDate = json.getString("createdate");
                if (CommonUtil.parseDateToTimeStamp(createDate) > maxDate){
                    maxDate = CommonUtil.parseDateToTimeStamp(createDate);
                    maxInfo = json;
                }
                String regDate = json.getString("regdate");
                if (CommonUtil.parseDateToTimeStamp(regDate) > maxRegDate){
                    maxRegDate = CommonUtil.parseDateToTimeStamp(regDate);
                    maxRegInfo = json;
                }
                dateSet.add(CommonUtil.parseDateToTimeStamp(json.getString("regdate")));
            }

            if (StringUtils.isEmpty(maxInfo.getString("businesstype")) && StringUtils.isEmpty(maxInfo.getString("regdate")) && "1".equals(maxInfo.getString("datastatus"))){
                priority = "3";
                dataStatus = maxInfo.getString("datastatus");
            }else{
                long today = System.currentTimeMillis()/1000;
                // 最近一周
                Set<Long> dateSet7 = new LinkedHashSet<>();
                Set<Long> dateSet7_30 = new LinkedHashSet<>();
                Set<Long> dateSet30_180 = new LinkedHashSet<>();
                Set<Long> dateSet180 = new LinkedHashSet<>();
                for (Long date : dateSet){
                    if ((today - maxRegDate) <= (7*86400)){
                        dateSet7.add(date);
                    }
                    if ((today - maxRegDate) > (7*86400) && (today - maxRegDate) <= (30*86400)){
                        dateSet7_30.add(date);
                    }
                    if ((today - maxRegDate) > (30*86400) && (today - maxRegDate) <= (180*86400)){
                        dateSet30_180.add(date);
                    }
                    if ((today - maxRegDate) > (180*86400)){
                        dateSet180.add(date);
                    }
                }

                // t-7=<regdate（最新一次请求），且近1个月有多笔，优先级为1，t-7=<regdate（进一个月只有本周有融资记录），或近1个月有多笔（进7天无融资记录）， 优先级为2 ，否则进行下一步
                if (dateSet7.size() > 0 && dateSet7_30.size() > 0){
                    priority = "1";
                }
                if (dateSet7.size() > 0 && dateSet7_30.size() == 0){
                    priority = "2";
                }
                if (dateSet7.size() == 0 && dateSet7_30.size() > 0){
                    priority = "2";
                }

                if (dateSet7_30.size() > 0 && dateSet30_180.size() > 0){
                    priority = "1";
                }
                if (dateSet7_30.size() > 0 && dateSet30_180.size() == 0){
                    priority = "2";
                }
                if (dateSet7_30.size() == 0 && dateSet30_180.size() > 0){
                    priority = "2";
                }

                if (dateSet30_180.size() > 0){
                    priority = "2";
                }


                dataStatus = maxInfo.getString("datastatus");
            }


        }

        result.put("Priority", priority);
        result.put("DataStatus", dataStatus);


        return result.toString();
    }


    public static void main(String[] args) {
        try {
            List<String> infoList = new LinkedList<>();
            infoList.add("{\"id\":\"f6e3dc25ea644ea4becf7b8e2cbfa85e\",\"createdate\":\"2022-10-28 15:22:01.0\",\"regdate\":\"2018-01-10 15:43:40\",\"businesstype\":\"融资租赁\",\"datastatus\":\"1\",\"regno\":\"04191414000502814848\"}");
            infoList.add("{\"id\":\"2001c3e3cd3b4acc9391b80119381ea8\",\"createdate\":\"2022-10-28 15:30:11.0\",\"regdate\":\"2021-03-30 14:28:01\",\"businesstype\":\"生产设备、原材料、半成品、产品抵押\",\"datastatus\":\"0\",\"regno\":\"10765081001285310436\"}");

            System.out.println(evaluate(infoList));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
