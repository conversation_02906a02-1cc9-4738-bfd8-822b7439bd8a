package com.qcc.udf.risk_analysis;

import com.qcc.udf.casesearch_v3.role.PartyRoleCodeEnum;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * <AUTHOR>
 * @date ：Created in 2023/01/05 13:54
 * @description ：获取裁判文书身份信息
 */
public class GetCPWSRoleType extends UDF {

    /**
     * @param param caseRole身份描述
     * @return 0-原告 1-被告 -1未知 2-其他
     * */
    public static int evaluate(String param) {
        int roleCode = PartyRoleCodeEnum.findCode(param);
        return roleCode;
    }

    public static void main(String[] args) {
        String role ="被申请人（原审原告）";
        System.out.println(evaluate(role));
    }
}
