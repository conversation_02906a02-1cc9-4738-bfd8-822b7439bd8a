package com.qcc.udf.CommonService;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Description(name = "CheckDataContinuity", value = "_FUNC_(String  str) arguments: 1,2,3 ; - Return true/false")
public class CheckDataContinuity extends UDF {

    public static boolean evaluate(String str) {

        if (StringUtils.isBlank(str)) return false;
        String[] strArray = str.split(",");
        if (strArray.length == 1) return true;
        List<Integer> strList = Arrays.stream(strArray)
                .map(a -> Integer.parseInt(a))
                .sorted().collect(Collectors.toList());

        Boolean result = true;
        for (int i = 1; i < strList.size(); i++) {
            Integer preStr = strList.get(i - 1);
            Integer currentStr = strList.get(i);
            if (currentStr - preStr != 1) {
                result = false;
                break;
            }
        }
        return result;
    }
}
