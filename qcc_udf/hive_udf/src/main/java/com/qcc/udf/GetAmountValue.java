package com.qcc.udf;

import com.alibaba.fastjson.JSONObject;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.List;

/**
 * 获取金额数值，负数保留。如-1000万人民币，返回 -1000.0
 * Created by wangb on 2020/9/8.
 */
public class GetAmountValue extends UDF {

    public double evaluate(String registCapi) {
        double result = 0;
        try {
            if (registCapi != null && registCapi != "") {
                double amount = Double.parseDouble(registCapi.replaceAll("[^0-9-.]", ""));
                if ((registCapi.contains("元人民币") || registCapi.contains("人民币元")) && !registCapi.contains("万")) {
                    amount = amount / 10000;
                }
                String str = "[{\"Name\":\"日元\",\"Code\":\"JPY\",\"Rate\":0.0529,\"KeyWords\":[\"日元\",\"日本\"]},{\"Name\":\"印度卢比\",\"Code\":\"INR\",\"Rate\":0.0977,\"KeyWords\":[\"卢比\",\"印度\"]},{\"Name\":\"人民币\",\"Code\":\"CNY\",\"Rate\":1,\"KeyWords\":[\"人民币\"]},{\"Name\":\"新加坡元\",\"Code\":\"SGD\",\"Rate\":4.5697,\"KeyWords\":[\"新币\",\"新加坡\"]},{\"Name\":\"瑞士法郎\",\"Code\":\"CHF\",\"Rate\":6.6,\"KeyWords\":[\"瑞士\"]},{\"Name\":\"澳大利亚元\",\"Code\":\"AUD\",\"Rate\":4.6,\"KeyWords\":[\"澳元\",\"澳大利亚\"]},{\"Name\":\"英镑\",\"Code\":\"GBP\",\"Rate\":9.8,\"KeyWords\":[\"英镑\"]},{\"Name\":\"菲律宾比索\",\"Code\":\"PHP\",\"Rate\":0.1369,\"KeyWords\":[\"比索\",\"菲律宾\"]},{\"Name\":\"缅甸元\",\"Code\":\"BUK\",\"Rate\":0.005,\"KeyWords\":[\"缅甸\"]},{\"Name\":\"挪威克朗\",\"Code\":\"NOK\",\"Rate\":0.78,\"KeyWords\":[\"挪威\"]},{\"Name\":\"瑞典克朗\",\"Code\":\"SEK\",\"Rate\":0.76,\"KeyWords\":[\"瑞典\"]},{\"Name\":\"新西兰元\",\"Code\":\"NZD\",\"Rate\":4.2803,\"KeyWords\":[\"新西兰\"]},{\"Name\":\"美元\",\"Code\":\"US\",\"Rate\":6.8,\"KeyWords\":[\"美元\"]},{\"Name\":\"新台币\",\"Code\":\"TWD\",\"Rate\":0.1963,\"KeyWords\":[\"台币\",\"台湾\"]},{\"Name\":\"泰国铢\",\"Code\":\"THB\",\"Rate\":0.1795,\"KeyWords\":[\"泰\"]},{\"Name\":\"韩国圆\",\"Code\":\"KRW\",\"Rate\":0.0056,\"KeyWords\":[\"韩\"]},{\"Name\":\"欧元\",\"Code\":\"EUR\",\"Rate\":7.2,\"KeyWords\":[\"欧元\"]},{\"Name\":\"加拿大元\",\"Code\":\"CAD\",\"Rate\":4.8,\"KeyWords\":[\"加元\",\"加拿大\"]},{\"Name\":\"港元\",\"Code\":\"HKD\",\"Rate\":0.8,\"KeyWords\":[\"港\"]},{\"Name\":\"丹麦克朗\",\"Code\":\"DKK\",\"Rate\":0.96,\"KeyWords\":[\"丹麦\"]}]";
                List<MoneyExchange> moneyExchangeList = JSONObject.parseArray(str, MoneyExchange.class);
                if (moneyExchangeList != null && moneyExchangeList.size() > 0) {
                    boolean flag = false;
                    for (MoneyExchange moneyExchange : moneyExchangeList) {
                        for (String keyWord : moneyExchange.KeyWords) {
                            if (registCapi.contains(keyWord)) {
                                result = amount * moneyExchange.Rate;
                                flag = true;
                                break;
                            }
                        }
                        if (flag) {
                            break;
                        }
                    }
                }

                if (result == 0) {
                    result = amount;
                }
            }
        } catch (Exception e) {
        }
        return result;
    }
//
//    public static void main(String[] args) {
//        GetAmountValue gav = new GetAmountValue();
//        System.out.println(gav.evaluate("-1000日元"));
//    }
}
