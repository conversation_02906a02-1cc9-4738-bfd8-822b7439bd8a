package com.qcc.udf.risk_analysis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

public class getWzrjCnt extends UDF {

    public String evaluate(List<String> infoList) {
        JSONArray array= new JSONArray();

        Map<String, Integer> map = new LinkedHashMap<>();
        Map<String, Set<String>> mapId = new LinkedHashMap<>();
        for (String str : infoList){
            String[] strArr = str.split("#FGF#");
            if (!map.containsKey(strArr[0])){
                map.put(strArr[0], 1);

                Set<String> idSet = new LinkedHashSet<>();
                idSet.add(strArr[1]);
                mapId.put(strArr[0], idSet);
            }else{
                map.put(strArr[0], map.get(strArr[0]) + 1);

                Set<String> idSet = mapId.get(strArr[0]);
                idSet.add(strArr[1]);
                mapId.put(strArr[0], idSet);
            }
        }

        Set<String> idSet = map.keySet();
        for (String str : idSet){
            Set<String> mySet = mapId.get(str);
            String ids = String.join(",", mySet);
            array.add(str.concat("#FGF#").concat(map.get(str).toString()).concat("#FGF#").concat(ids));
        }

        return array.toString();
    }

    public static void main(String[] args) {
        getWzrjCnt aa = new getWzrjCnt();
        List<String> infoList = JSON.parseArray("[\"3\",\"1\",\"3\"]", String.class);
        System.out.println(aa.evaluate(infoList));
    }
}
