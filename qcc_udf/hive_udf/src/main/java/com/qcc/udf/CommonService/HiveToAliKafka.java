package com.qcc.udf.CommonService;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDF;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;

import java.util.Properties;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

@Description(name = "HiveToAliKafka", value = "_FUNC_(String  topics,String msg);arguments max.request.size = 236870912; - Return offset")
public class HiveToAliKafka extends UDF {

    private static Properties props = new Properties() {{
        put("bootstrap.servers", "**********:9092,**********:9092,**********:9092");
        put("acks", "all");
//        put("min.insync.replicas", 2);
        put("retries", 3);
//        put("batch.size", 16384);
        put("linger.ms", 50);
        put("buffer.memory", 236870912);
        put("max.request.size", 20000000);
        put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
    }};
    private static KafkaProducer<String, String> producer = new KafkaProducer<>(props);

//    public static void main(String[] args) throws Exception {
//        evaluate("Report_Test", "1");
//    }

    /**
     * @param topics
     * @param msg
     * @return
     * @throws Exception
     */
    public static String evaluate(String topics, String msg) throws ExecutionException, InterruptedException {
        try {
            if (StringUtils.isBlank(topics)) throw new InterruptedException("topic 不能为空");
            if (StringUtils.isBlank(msg)) return "0";
            Future<RecordMetadata> result = producer.send(new ProducerRecord<>(topics, msg));
            return String.valueOf(result.get().offset());
        } catch (Exception ex) {
            throw ex;
        }
    }
}
