package com.qcc.udf.casesearch_v3;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.risk_analysis.entity.NameKeyDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 判断同一个案号下的同一个人员,是否存在不同KeyNo,存在则返回KeyNos.否则返回""
 * @date 2022/10/19 17:33
 */
public class GetAbnormalKeyNoInfoUDF extends UDF {
    private static final String PERSON_FLAG = "p";


    public String evaluate(String nameAndKeyNos) {
        if(StringUtils.isBlank(nameAndKeyNos)){
            return StringUtils.EMPTY;
        }
        Set<String> abnormalKeyNos = new HashSet<>();
        try {
            List<NameKeyDto> nameKeyDtos = JSONObject.parseArray(nameAndKeyNos, NameKeyDto.class);
            Map<String, List<NameKeyDto>> nameAndKeyNoMap = nameKeyDtos.stream().
                    filter(item -> item != null && StringUtils.isNotBlank(item.getKeyNo()) && StringUtils.isNotBlank(item.getName()) && item.getKeyNo().startsWith(PERSON_FLAG)).
                    collect(Collectors.groupingBy(NameKeyDto::getName));
            for (Map.Entry<String, List<NameKeyDto>> stringListEntry : nameAndKeyNoMap.entrySet()) {
                Set<String> collect = stringListEntry.getValue().stream().map(NameKeyDto::getKeyNo).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(collect) && collect.stream().count() > 1) {
                    abnormalKeyNos.addAll(collect);
                }
            }
        }catch(Exception e){
            e.printStackTrace();
        }
        return String.join(StrUtil.COMMA,abnormalKeyNos);
    }

    public static void main(String[] args) {
        String value = "[{\"KeyNo\":\"43db761d4355081c54205c39a1fd2226\",\"Name\":\"中集融资租赁有限公司\",\"Org\":0,\"Role\":\"申请执行人\",\"RoleTag\":0,\"RoleType\":12,\"ShowName\":\"中集融资租赁有限公司\",\"Source\":1},{\"KeyNo\":\"p3e21ff48e6a52e1f13f41739e09ea43\",\"Name\":\"吉志芳\",\"Org\":2,\"Role\":\"被执行人\",\"RoleTag\":1,\"RoleType\":22,\"ShowName\":\"吉志芳\",\"Source\":1},{\"KeyNo\":\"88e456dcc9b98c6212450e901fc6a676\",\"Name\":\"广州佳利物流有限公司\",\"Org\":0,\"Role\":\"被执行人\",\"RoleTag\":1,\"RoleType\":22,\"ShowName\":\"广州佳利物流有限公司\",\"Source\":1,\"T\":\"21\"},{\"KeyNo\":\"39e5244317ec111b74180acb6ad5cd9a\",\"Name\":\"广州市晋旗贸易有限公司\",\"Org\":0,\"Role\":\"被执行人\",\"RoleTag\":1,\"RoleType\":22,\"ShowName\":\"广州市晋旗贸易有限公司\",\"Source\":1,\"T\":\"21\"},{\"KeyNo\":\"p6577ec67733a8e801d0b876ac8d16e4\",\"Name\":\"胡晋旗\",\"Org\":2,\"Role\":\"被执行人\",\"RoleTag\":1,\"RoleType\":22,\"ShowName\":\"胡晋旗\",\"Source\":1,\"T\":\"21\"},{},{\"KeyNo\":\"39e5244317ec111b74180acb6ad5cd9a\",\"Org\":0,\"ShowName\":\"广州市晋旗贸易有限公司\",\"Name\":\"广州市晋旗贸易有限公司\"},{\"KeyNo\":\"pcca66a13260bd61f19f4a952e22236c\",\"Org\":2,\"ShowName\":\"吉志芳\",\"Name\":\"吉志芳\"},{\"KeyNo\":\"43db761d4355081c54205c39a1fd2226\",\"Org\":0,\"ShowName\":\"中集融资租赁有限公司\",\"Name\":\"中集融资租赁有限公司\"},{\"KeyNo\":\"88e456dcc9b98c6212450e901fc6a676\",\"Org\":0,\"ShowName\":\"广州佳利物流有限公司\",\"Name\":\"广州佳利物流有限公司\"},{\"KeyNo\":\"p6577ec67733a8e801d0b876ac8d16e4\",\"Org\":2,\"ShowName\":\"胡晋旗\",\"Name\":\"胡晋旗\"},{},{},{\"Name\":\"吉志芳\",\"KeyNo\":\"p3e21ff48e6a52e1f13f41739e09ea43\",\"Org\":2},{\"Name\":\"广州市晋旗贸易有限公司\",\"KeyNo\":\"39e5244317ec111b74180acb6ad5cd9a\",\"Org\":0},{\"Name\":\"胡晋旗\",\"KeyNo\":\"p6577ec67733a8e801d0b876ac8d16e4\",\"Org\":2},{\"Name\":\"广州佳利物流有限公司\",\"KeyNo\":\"88e456dcc9b98c6212450e901fc6a676\",\"Org\":0},{},{},{},{\"Name\":\"吉志芳\",\"KeyNo\":\"p3e21ff48e6a52e1f13f41739e09ea43\",\"Org\":2},{\"Name\":\"胡晋旗\",\"KeyNo\":\"p6577ec67733a8e801d0b876ac8d16e4\",\"Org\":2},{\"Name\":\"广州佳利物流有限公司\",\"KeyNo\":\"88e456dcc9b98c6212450e901fc6a676\",\"Org\":0},{\"Name\":\"广州市晋旗贸易有限公司\",\"KeyNo\":\"39e5244317ec111b74180acb6ad5cd9a\",\"Org\":0},{}]";
        System.out.println(new GetAbnormalKeyNoInfoUDF().evaluate(value));
    }

}
