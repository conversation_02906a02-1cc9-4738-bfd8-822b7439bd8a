package com.qcc.udf.company_level;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;

public class GetInvests extends UDF {
    //
//    public static void main(String[] args) throws Exception {
//        String aa = "[{\"keyno\":\"d5f2dff42614e61ea8806cd5f2126514\",\"companycode\":\"\",\"name\":\"上海霁柏电子商务有限公司\",\"shortstatus\":\"存续\",\"shouldcapi\":\"30\",\"stockrightnum\":\"\",\"percent\":\"30.00%\"},{\"keyno\":\"9b276d2807f56064a978064a6ab3e3d7\",\"companycode\":\"\",\"name\":\"上海侨蔚网络科技有限公司\",\"shortstatus\":\"存续\",\"shouldcapi\":\"30\",\"stockrightnum\":\"\",\"percent\":\"30.00%\"},{\"keyno\":\"4b86ad9f85529ae2092daf2b80737bca\",\"companycode\":\"\",\"name\":\"上海粤御网络科技有限公司\",\"shortstatus\":\"存续\",\"shouldcapi\":\"30\",\"stockrightnum\":\"\",\"percent\":\"30.00%\"},{\"keyno\":\"b5cb8415d405b5a43f480255859d214d\",\"companycode\":\"\",\"name\":\"成都京工作信息科技有限公司\",\"shortstatus\":\"存续\",\"shouldcapi\":\"98\",\"stockrightnum\":\"\",\"percent\":\"49.00%\"},{\"keyno\":\"cf62a3548c3a80c0784e1f43d2e231c9\",\"companycode\":\"\",\"name\":\"上海皋隆文化传播有限公司\",\"shortstatus\":\"存续\",\"shouldcapi\":\"30\",\"stockrightnum\":\"\",\"percent\":\"30.00%\"},{\"keyno\":\"d02f2a15efb1283372f165207668ec54\",\"companycode\":\"\",\"name\":\"上海素相网络科技有限公司\",\"shortstatus\":\"存续\",\"shouldcapi\":\"30\",\"stockrightnum\":\"\",\"percent\":\"30.00%\"},{\"keyno\":\"e7ee9ecde10ac8ea155fda8696cdea92\",\"companycode\":\"\",\"name\":\"上海具芒信息科技有限公司\",\"shortstatus\":\"存续\",\"shouldcapi\":\"30\",\"stockrightnum\":\"\",\"percent\":\"30.00%\"},{\"keyno\":\"b182e2423b7358e9608bc79d566c6e68\",\"companycode\":\"\",\"name\":\"上海然炫信息科技有限公司\",\"shortstatus\":\"存续\",\"shouldcapi\":\"30\",\"stockrightnum\":\"\",\"percent\":\"30.00%\"},{\"keyno\":\"1072eb6e27dd142f0d632b73f103daf4\",\"companycode\":\"\",\"name\":\"上海铄伦电子商务有限公司\",\"shortstatus\":\"存续\",\"shouldcapi\":\"20\",\"stockrightnum\":\"\",\"percent\":\"20.00%\"}]";
//        System.out.println(evaluate(aa, ""));
//    }
    public static String evaluate(String invests, String keyno) throws Exception {
        String result = "";
        try {
            if (StringUtils.isBlank(invests)) return invests;
            JSONArray resultData = new JSONArray();
            JSONArray investsArray = JSONArray.parseArray(invests);
            List<String> keyNos = new ArrayList<>();
            keyNos.add(keyno);
            for (Object t : investsArray) {
                JSONObject tObj = JSONObject.parseObject(t.toString());
                String tKeyNo = tObj.getString("keyno");
                if (StringUtils.isBlank(tKeyNo) || keyNos.contains(tKeyNo)) continue;
                keyNos.add(tKeyNo);
                resultData.add(tObj);
            }
            resultData = SortData(resultData);
            result = JSON.toJSONString(resultData, SerializerFeature.DisableCircularReferenceDetect);
        } catch (Exception e) {

        }
        return result;
    }


    private static JSONArray SortData(JSONArray resultData) {
        resultData.sort((a, b) -> {
            Double i;
            JSONObject aObj = JSONObject.parseObject(a.toString());
            JSONObject bObj = JSONObject.parseObject(b.toString());

            i = stringToDouble(aObj.getString("stockrightnum")) - stringToDouble(bObj.getString("stockrightnum"));
            if (i == 0) {
                i = stringToDouble(aObj.getString("percent")) - stringToDouble(bObj.getString("percent"));
            }
            if (i == 0) {
                i = stringToDouble(aObj.getString("shouldcapi")) - stringToDouble(bObj.getString("shouldcapi"));
            }
            if (i == 0) {
                i = stringToDouble(aObj.getString("keyno")) - (stringToDouble(bObj.getString("keyno")));
            }
            int r = 0;
            if (i > 0) {
                r = -1;
            } else if (i < 0) {
                r = 1;
            }
            return r;
        });
        return resultData;
    }


    private static double stringToDouble(String str) {
        double result = 0.0;
        try {
            if (StringUtils.isBlank(str)) return result;
            return Double.parseDouble(str.replace("%", "").replace(",", ""));
        } catch (Exception e) {

        }
        return result;
    }
}
