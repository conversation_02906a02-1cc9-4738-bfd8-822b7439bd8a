package com.qcc.udf.tax;

import com.google.common.base.Strings;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CommonUtils {
    public static Map<String, String> CHINESE_NUM_MAP = new LinkedHashMap<>();
    public static List<Pattern> PATTERN_LIST = new ArrayList<>();

    static {

        CHINESE_NUM_MAP.put("二?一", "201");
        CHINESE_NUM_MAP.put("二十一", "21");
        CHINESE_NUM_MAP.put("二十二", "22");
        CHINESE_NUM_MAP.put("二十三", "23");
        CHINESE_NUM_MAP.put("二十四", "24");
        CHINESE_NUM_MAP.put("二十五", "25");
        CHINESE_NUM_MAP.put("二十六", "26");
        CHINESE_NUM_MAP.put("二十七", "27");
        CHINESE_NUM_MAP.put("二十八", "28");
        CHINESE_NUM_MAP.put("二十九", "29");
        CHINESE_NUM_MAP.put("三十一", "31");
        CHINESE_NUM_MAP.put("三十二", "32");
        CHINESE_NUM_MAP.put("三十三", "33");
        CHINESE_NUM_MAP.put("三十四", "34");
        CHINESE_NUM_MAP.put("三十五", "35");
        CHINESE_NUM_MAP.put("三十六", "36");
        CHINESE_NUM_MAP.put("三十七", "37");
        CHINESE_NUM_MAP.put("三十八", "38");
        CHINESE_NUM_MAP.put("三十九", "39");
        CHINESE_NUM_MAP.put("四十一", "41");
        CHINESE_NUM_MAP.put("四十二", "42");
        CHINESE_NUM_MAP.put("四十三", "43");
        CHINESE_NUM_MAP.put("四十四", "44");
        CHINESE_NUM_MAP.put("四十五", "45");
        CHINESE_NUM_MAP.put("四十六", "46");
        CHINESE_NUM_MAP.put("四十七", "47");
        CHINESE_NUM_MAP.put("四十八", "48");
        CHINESE_NUM_MAP.put("四十九", "49");
        CHINESE_NUM_MAP.put("五十一", "51");
        CHINESE_NUM_MAP.put("五十二", "52");
        CHINESE_NUM_MAP.put("五十三", "53");
        CHINESE_NUM_MAP.put("五十四", "54");
        CHINESE_NUM_MAP.put("五十五", "55");
        CHINESE_NUM_MAP.put("五十六", "56");
        CHINESE_NUM_MAP.put("五十七", "57");
        CHINESE_NUM_MAP.put("五十八", "58");
        CHINESE_NUM_MAP.put("五十九", "59");
        CHINESE_NUM_MAP.put("十一", "11");
        CHINESE_NUM_MAP.put("十二", "12");
        CHINESE_NUM_MAP.put("十三", "13");
        CHINESE_NUM_MAP.put("十四", "14");
        CHINESE_NUM_MAP.put("十五", "15");
        CHINESE_NUM_MAP.put("十六", "16");
        CHINESE_NUM_MAP.put("十七", "17");
        CHINESE_NUM_MAP.put("十八", "18");
        CHINESE_NUM_MAP.put("十九", "19");
        CHINESE_NUM_MAP.put("二十", "20");
        CHINESE_NUM_MAP.put("三十", "30");
        CHINESE_NUM_MAP.put("四十", "40");
        CHINESE_NUM_MAP.put("五十", "50");
        CHINESE_NUM_MAP.put("十", "10");
        CHINESE_NUM_MAP.put("一", "1");
        CHINESE_NUM_MAP.put("壹", "1");
        CHINESE_NUM_MAP.put("二", "2");
        CHINESE_NUM_MAP.put("三", "3");
        CHINESE_NUM_MAP.put("四", "4");
        CHINESE_NUM_MAP.put("五", "5");
        CHINESE_NUM_MAP.put("六", "6");
        CHINESE_NUM_MAP.put("七", "7");
        CHINESE_NUM_MAP.put("八", "8");
        CHINESE_NUM_MAP.put("九", "9");
        CHINESE_NUM_MAP.put("零", "0");
        CHINESE_NUM_MAP.put("〇", "0");
        CHINESE_NUM_MAP.put("O", "0");
        CHINESE_NUM_MAP.put("○", "0");
        CHINESE_NUM_MAP.put("Ο", "0");


        PATTERN_LIST.add(Pattern.compile("\\d{4}-\\d{1,2}-\\d{1,2} \\d{1,2}:\\d{1,2}:\\d{1,2}"));
        PATTERN_LIST.add(Pattern.compile("\\d{4}-\\d{1,2}-\\d{1,2} \\d{1,2}:\\d{1,2}"));
        PATTERN_LIST.add(Pattern.compile("\\d{4}-\\d{1,2}-\\d{1,2} \\d{1,2}"));
        PATTERN_LIST.add(Pattern.compile("\\d{4}-\\d{1,2}-\\d{1,2}"));
    }

    /**
     * 去除html标签
     * @param content
     * @return
     */
    public static String cleanHtmlTag(String content) {
        if (StringUtils.isNotBlank(content)) {

            //html转义符转化为标签对
            content = replaceHtml(content);

            return content
                    .replaceAll("<[a-zA-Z].*?>", "")
                    .replaceAll("</[a-zA-Z].*?>", "").trim();
        }
        return content;
    }

    /**
     * 替换掉html转义字符
     *
     * @param content 带有html转义字符的内容
     * @return 去除转义后的内容
     */
    public static String replaceHtml(String content) {
        content = content.replace("&#34;", "\"");
        content = content.replace("&quot;", "\"");
        content = content.replace("&#38;", "&");
        content = content.replace("&amp;", "&");
        content = content.replace("&#60;", "<");
        content = content.replace("&lt;", "<");
        content = content.replace("&#62;", ">");
        content = content.replace("&gt;", ">");
        content = content.replace("&#160;", "");
        content = content.replace("&nbsp;", "");
        return content;
    }


    public static String full2Half(String input) {
        char c[] = input.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == '\u3000') {
                c[i] = ' ';
            } else if (c[i] > '\uFF00' && c[i] < '\uFF5F') {
                c[i] = (char) (c[i] - 65248);

            }
        }
        String returnString = new String(c);

        return returnString;
    }

    static Integer tryParseInt(String data) {
        try {
            return Integer.valueOf(data);
        } catch (Exception e) {
            return null;
        }
    }

    static Date convertDate(String str) {

        String year = str.split("-")[0];
        Integer time = tryParseInt(year);
        Date date = null;
        if(time != null && time>12){
            date = DateUtil.parseStrToDate(str, DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS);
            if (date == null) {
                date = DateUtil.parseStrToDate(str, DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI);
            }
            if (date == null) {
                date = DateUtil.parseStrToDate(str, "yyyy-MM-dd HH:mm");
            }
            if (date == null) {
                date = DateUtil.parseStrToDate(str, DateUtil.DATE_FORMAT_YYYY_MM_DD);
            }
            if (date == null) {
                date = DateUtil.parseStrToDate(str, "yy-MM-dd");
            }
        }

        if (date == null) {
            date = DateUtil.parseStrToDate(str, "MM-dd-yyyy");
        }

        if (date == null) {
            date = DateUtil.parseStrToDate(str, DateUtil.DATE_TIME_FORMAT_YYYYMMDD_HH_MI);
        }

        if (date == null) {
            date = DateUtil.parseStrToDate(str, "dd-MM-yyyy HH:mm");
        }
        if (date == null && str.length()==8) {
            date = DateUtil.parseStrToDate(str, DateUtil.DATE_FORMAT_YYYYMMDD);
        }

        if(date != null){
            //时间大于当前时间+5年，认为是错误数据
            if(date.getYear()>(new Date()).getYear()+5){
                return null;
            }
            //时间小于1990年之前的，也认为是错误数据
            if(date.getYear()+1900<1980){
                return null;
            }
        }

        return date;
    }

    public static String fmtData(String date) {
        date = date.replaceAll("/|\\\\|\\.", "-");
        date = date.replaceAll(" | ", " ");
        if(date.contains("上午") || date.contains("下午")){
            return null;
        }
        date = date.replace("  ", " ");
        date = date.replace("  ", " ");
        date = findDateStr(date);
        Date fmtData = convertDate(date);
        if (fmtData != null) {
            return DateUtil.parseDateToStr(fmtData, DateUtil.DATE_FORMAT_YYYYMMDD);
        }
        return null;
    }

    public static String findDateStr(String str) {
        for (Pattern pattern : PATTERN_LIST) {
            Matcher matcher = pattern.matcher(str);
            if (matcher.find()) {
                return matcher.group();
            }
        }
        return str;
    }

    static String convertChineseDate(String date) {
        for (Map.Entry<String, String> entry : CHINESE_NUM_MAP.entrySet()) {
            date = date.replace(entry.getKey(), entry.getValue());
        }
        return date;
    }

    static String dealAfterNoonTime(String dataNew) {
        int afterNoon = dataNew.indexOf("下午");
        if (afterNoon > 0) {
            String afterStr = dataNew.substring(afterNoon);
            int mhIdx = afterStr.indexOf(":");
            if (mhIdx > 0) {
                String afterTime = afterStr.substring(2, mhIdx);
                Integer time = tryParseInt(afterTime);
                //小于12点 需要+12转为24小时制
                if (time != null && time < 12) {
                    dataNew = dataNew.replace(afterStr.substring(0, mhIdx), " "+String.valueOf(time + 12));
                }
            }
        }

        return dataNew;
    }

    public static String cleanDate(String data) {
        if (Strings.isNullOrEmpty(data)) {
            return "";
        }

        String dataNew = full2Half(data);
        String fmtData = fmtData(dataNew);
        if(fmtData != null){
            return fmtData;
        }
        dataNew = dataNew.replaceAll("\\(星期(一|二|三|四|五|六|天|日)\\)", "");
        dataNew = convertChineseDate(dataNew);
        dataNew = dataNew.replace("元月", "1月");
        dataNew = dataNew.replaceAll(" | ", "");
        dataNew = dataNew.replace("时间:", " ");
        dataNew = dataNew.replace("日", "日 ");
        dataNew = dataNew.replace("整", "00");
        dataNew = dataNew.replaceAll("年|月", "-");
        dataNew = dataNew.replaceAll("时|点|分", ":");
        dataNew = dataNew.replaceAll("/|\\\\|\\.", "-");
        dataNew = dataNew.replaceAll("日|分|上午|中午", " ");
        //处理带下午的时间
        dataNew = dealAfterNoonTime(dataNew);
        dataNew = dataNew.replace("下午", " ");
        dataNew = dataNew.replace("  ", " ");
        dataNew = dataNew.replace("  ", " ");
        dataNew = dataNew.replaceAll("至|到", ";");
        if (dataNew.length() < 4) {
            return "";
        }
        if (dataNew.endsWith(":")) {
            dataNew = dataNew + "00";
        }
        dataNew = dataNew.split(";")[0].trim();
        Date date = convertDate(dataNew);

        if (date == null) {
            dataNew = findDateStr(dataNew);
            date = convertDate(dataNew.trim());
        }

        if (date != null) {
            return DateUtil.parseDateToStr(date, DateUtil.DATE_FORMAT_YYYYMMDD);
        }
        return "";
    }

}
