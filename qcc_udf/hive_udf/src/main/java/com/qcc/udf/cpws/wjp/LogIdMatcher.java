package com.qcc.udf.cpws.wjp;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.HashSet;
import java.util.Set;

public class LogIdMatcher {

    public static void findUnmatchedIds(String filePath) throws IOException {
        Set<String> startIds = new HashSet<>();
        Set<String> unmatchedIds = new HashSet<>();

        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();

                // 处理开始ID
                if (line.startsWith("开始：")) {
                    String id = line.substring(3).trim();
                    startIds.add(id);
                    unmatchedIds.add(id);
                }
                // 处理结束ID
                else if (line.startsWith("结束：")) {
                    String id = line.substring(3).trim();
                    if (startIds.contains(id)) {
                        unmatchedIds.remove(id);
                    }
                }
            }
        }

        // 输出未匹配的ID
        if (unmatchedIds.isEmpty()) {
            System.out.println("所有开始ID都有对应的结束ID");
        } else {
            System.out.println("未匹配的开始ID:");
            for (String id : unmatchedIds) {
                System.out.println(id);
            }
        }
    }

    public static void main(String[] args) {
        try {
            // 替换为你的日志文件路径
            findUnmatchedIds("C:\\Users\\<USER>\\Desktop\\新文件 1.txt");
        } catch (IOException e) {
            System.err.println("读取日志文件时出错: " + e.getMessage());
        }
    }
}