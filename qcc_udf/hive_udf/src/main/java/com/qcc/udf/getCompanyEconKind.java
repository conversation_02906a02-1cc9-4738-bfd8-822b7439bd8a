package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * @Auther: wangbin
 * @Date: 2019/7/19 09:26
 * @Description:
 */
public class getCompanyEconKind extends UDF {
    public static String evaluate(String econKind)
    {

        StringBuffer sb = new StringBuffer();

        if (econKind == null || "".equals(econKind)){
            return "";
        }
        if ((econKind.contains("个体") || econKind.contains("个人") || econKind.contains("家庭"))
                && !econKind.contains("企业")){
            sb.append(",个体工商户");
            return sb.toString().substring(1);
        }
        if (econKind.contains("有限责任公司")) sb.append(",有限责任公司");

        if (econKind.contains("股份有限公司")) sb.append(",股份有限公司");

        if (econKind.contains("国有") && !econKind.contains("非国有")) sb.append(",国企");

        if (econKind.contains("中外合作") || econKind.contains("中外合资") || econKind.contains("外国") || econKind.contains("外商")) sb.append(",外商投资企业");

        if ((econKind.contains("独资") && !econKind.matches("\".*[非]+.*[独资]\"")) || econKind.contains("一人有限责任公司")) sb.append(",独资企业");

        if (econKind.contains("有限合伙") || econKind.contains("普通合伙") || econKind.contains("合伙企业")) sb.append(",合伙制企业");

        if (econKind.contains("有限合伙")) sb.append(",有限合伙");

        if (econKind.contains("普通合伙")) sb.append(",普通合伙");

        if (econKind.contains("联营") && !econKind.contains("非联营")) sb.append(",联营企业");

        if (econKind.contains("集体") && !econKind.contains("非集体") && !econKind.contains("国有与集体企业联营") && !econKind.contains("集体与股份联营") && !econKind.contains("集体与私营联营") && !econKind.contains("集体与中外合资联营") && !econKind.contains("全民与集体联营")) sb.append(",集体所有制");

        if(sb.toString().length() != 0){
            return sb.toString().substring(1);
        }else{
            return sb.toString();
        }
    }
}
