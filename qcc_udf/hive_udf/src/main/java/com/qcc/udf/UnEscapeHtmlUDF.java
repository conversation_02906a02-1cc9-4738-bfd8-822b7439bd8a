package com.qcc.udf;

import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * Html特殊符号转义然后转为大写格式
 * ---------------------------------------------------------------------------------------------------------
 * create temporary function unescapeHtml as 'com.qcc.udf.UnEscapeHtmlUDF' using jar 'hdfs:///data/hive/udf/qcc_udf.jar';
 * ---------------------------------------------------------------------------------------------------------
 * select unescapeHtml ('J&AMP;A CAMELBACK PROPERTY, LLC');
 * 结果: "J&A CAMELBACK PROPERTY, LLC"
 */
public class UnEscapeHtmlUDF extends UDF{

    public String evaluate(String input) {
        String result = "";
        try {
            if (StringUtils.isNotBlank(input)) {
                String output = StringEscapeUtils.unescapeHtml4(StringUtils.lowerCase(input));
                result = StringUtils.upperCase(output);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return result;
    }

    public static void main(String[] args) {
        String input = "J&AMP;A CAMELBACK PROPERTY, LLC";
        String result = new UnEscapeHtmlUDF().evaluate(input);
        System.out.println(result);
    }
}