package com.qcc.udf.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * YCJ
 * 关联公司信息
 *
 * <AUTHOR>
 */
@Data
public class KeyNoOrgInfo {

    @J<PERSON><PERSON>ield(name = "KeyNo")
    private String keyNo;

    @JSONField(name = "Org")
    private int org = 0;

    @JSONField(name = "Name")
    private String name;

    public KeyNoOrgInfo() {
    }

    public String getKeyNo() {
        return keyNo;
    }

    public void setKeyNo(String keyNo) {
        this.keyNo = keyNo;
    }

    public int getOrg() {
        return org;
    }

    public void setOrg(int org) {
        this.org = org;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public KeyNoOrgInfo(String keyNo, String name) {
        this.keyNo = keyNo;
        this.name = name;
    }

    public KeyNoOrgInfo(String keyNo, String name, Integer org) {
        this.keyNo = keyNo;
        this.name = name;
        this.org = org;
    }
}