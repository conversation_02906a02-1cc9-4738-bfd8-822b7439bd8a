package com.qcc.udf.risk_graph;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.LinkedHashSet;
import java.util.Set;

/**
 * @Auther: liulh
 * @Date: 2020/6/11 17:54
 * @Description:
 */
public class editFirstLevel extends UDF {
    public static String evaluate(String wdNames) throws Exception{
        Set<String> levelSet = new LinkedHashSet<>();
        if (StringUtils.isNotEmpty(wdNames)){
            String[] arr = wdNames.split(",");
            for (String str : arr){
                if ("PCCZ".equals(str)){
                    levelSet.add("SSGX");
                }
                if ("XG".equals(str)){
                    levelSet.add("SSGX");
                }
                if ("ZX".equals(str)){
                    levelSet.add("SSGX");
                }
                if ("SFAJ".equals(str)){
                    levelSet.add("SSGX");
                }
                if ("XJPG".equals(str)){
                    levelSet.add("SSGX");
                }
                if ("GQDJ".equals(str)){
                    levelSet.add("SSGX");
                }
                if ("YZWF".equals(str)){
                    levelSet.add("JGGX");
                }
                if ("JYYC".equals(str)){
                    levelSet.add("JGGX");
                }
                if ("XZCF".equals(str)){
                    levelSet.add("JGGX");
                }
                if ("HBCF".equals(str)){
                    levelSet.add("JGGX");
                }
                if ("SSWF".equals(str)){
                    levelSet.add("JGGX");
                }
                if ("QSGG".equals(str)){
                    levelSet.add("JGGX");
                }
                if ("WGCL".equals(str)){
                    levelSet.add("JGGX");
                }
                if ("CCJC".equals(str)){
                    levelSet.add("JGGX");
                }
                if ("GQCZ".equals(str)){
                    levelSet.add("DBGX");
                }
                if ("DCDY".equals(str)){
                    levelSet.add("DBGX");
                }
                if ("TDDY".equals(str)){
                    levelSet.add("DBGX");
                }
                if ("DWDB".equals(str)){
                    levelSet.add("DBGX");
                }
                if ("GSCG".equals(str)){
                    levelSet.add("OTHERGX");
                }
                if ("JYXX".equals(str)){
                    levelSet.add("JYGX");
                }
                if ("TZXX".equals(str)){
                    levelSet.add("TZGX");
                }
                if ("RZXX".equals(str)){
                    levelSet.add("RZGX");
                }
            }
        }

        return String.join(",", levelSet);
    }
}
