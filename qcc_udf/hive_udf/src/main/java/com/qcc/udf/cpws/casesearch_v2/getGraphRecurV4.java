package com.qcc.udf.cpws.casesearch_v2;

import groovy.lang.Tuple2;
import org.apache.commons.logging.LogFactory;
import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDTF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;
import org.apache.commons.logging.Log;

import java.util.*;
import java.util.stream.Collectors;

@Description(name = "getGraphRecur", value = "")
public class getGraphRecurV4 extends GenericUDTF {
    static final Log LOG = LogFactory.getLog(getGraphRecurV4.class);

    List<Tuple2<String,String>> arcs=new ArrayList<Tuple2<String, String>>();
    Map<String, Set<String>>  angleGroupAll=new HashMap();
    @Override
    public StructObjectInspector initialize(ObjectInspector[] args) throws UDFArgumentException{
        ArrayList<String> fieldNames = new ArrayList<String>();
        ArrayList<ObjectInspector> fieldOIs = new ArrayList<ObjectInspector>();
        fieldNames.add("col1");
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);

        return ObjectInspectorFactory.getStandardStructObjectInspector(fieldNames,fieldOIs);
    }

    @Override
    public void process(Object[] record) throws HiveException {
        String tmpId = record[0].toString();
        String start = (tmpId == null || tmpId.trim().equalsIgnoreCase("") ? null : tmpId.trim());

        String tmpParent = record[1].toString();
        String end = (tmpParent == null || tmpParent.trim().equalsIgnoreCase("") ? null : tmpParent.trim());

        arcs.add(new Tuple2<String, String>(start,end));
    }


    Integer count=0;
    private void dfs(String id,int count,Map<String,Boolean> marked,Map<Integer,List<String>> belongs) {
        marked.put(id,true);
        belongs.get(count).add(id);
        if (belongs.get(count).size() > 100){
            return;
        }
        //取出所有和id关联的边

        //此点没有下一条边，返回
        if(angleGroupAll.get(id).size()==0){
            return;
        }

        for (String angle : angleGroupAll.get(id)) {
            if (!marked.containsKey(angle)) {
                dfs(angle, count, marked, belongs);
            }
        }
    }

    @Override
    public void close() throws HiveException {
        Map<String,Boolean> marked= new HashMap();
        Map<Integer,List<String>> belongs= new HashMap();


        LOG.info("arcs="+arcs.size()+"条，"+arcs.get(0));

        angleGroupAll=arcs.stream().flatMap(s->Arrays.stream(new String[]{s.getFirst(),s.getSecond()})).distinct()
                .collect(Collectors.toMap(strings -> strings,strings -> new HashSet<String>()));

        for(Tuple2<String,String> arc:arcs){
            angleGroupAll.get(arc.getFirst()).add(arc.getSecond());
            angleGroupAll.get(arc.getSecond()).add(arc.getFirst());
        }

        //遍历每一个点
        belongs.put(count,new ArrayList<>());
        for(String start : angleGroupAll.keySet()){
            if (!marked.containsKey(start)) {
                //s的一次递归调用能访问所有与它连通的顶点
                dfs(start,count,marked,belongs);
                //到这里说明s的连通顶点已经访问完毕
                count++;
                belongs.put(count,new ArrayList<>());
            }
        }
        for( List<String> components :belongs.values()){
            forward(new String[]{String.join(",",components)});
        }

    }
}
