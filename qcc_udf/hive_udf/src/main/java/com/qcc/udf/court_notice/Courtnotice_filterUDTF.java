package com.qcc.udf.court_notice;

import com.qcc.udf.court_notice.anCleanMethods.CleanCourtUtil;
import com.qcc.udf.court_notice.anUtils.Constants;
import com.qcc.udf.court_notice.anUtils.MD5Util;
import com.qcc.udf.court_notice.anUtils.Util;
import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentLengthException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDTF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;

import java.text.SimpleDateFormat;
import java.util.*;


/**
* 清洗ods_new_spider.court_notice老的数据源
**/
public class Courtnotice_filterUDTF extends GenericUDTF{

    String provinceArr[] = {"安徽","北京","天津","海南","新疆","陕西","山西","福建","湖北","西藏","重庆","云南","河南","宁夏"};
    String sourceFrom;
    StringBuffer sb = new StringBuffer();
    Date maxDate;
    boolean delFlag;
    long courtyear;
    String province;
    String strNull = null;
    String prosecutor;
    String defendant;
    Object content;
    String execute_gov;
    Calendar cal;
    String[] inschema
     = new String[]{"execute_gov", "execute_unite", "open_time", "schedule_time", "undertake_department", "chief_judge", "case_no",
     "case_reason", "prosecutor", "defendant", "party", "datatype", "source_from", "isvalid", "delflag", "created_date", "updated_date", "content","is_struct","key_no"};
    String[] outschema
     =new String[]{"id","liandate","casereason","prosecutorlist_os","defendantlist_os","executegov","caseno","courtyear","type","province","name","companynames","isvalid",
     "party","datatype","prosecutorlist","defendantlist","execute_unite","schedule_time","undertake_department","chief_judge","created_date","updated_date","key_no","content","is_struct"};
    Map<String, Integer> inschemap = new HashMap<>();
    Map<String, Integer> outschemap = new HashMap<>();
    Set nameset=new TreeSet();
    @Override
    public void close() throws HiveException {
        // TODO Auto-generated method stub

    }

    @Override
    public void process(Object[] args) throws HiveException {
            try {
                inschemap.clear();
                outschemap.clear();
                prosecutor=null;
                defendant=null;
                content=null;
                for (int i = 0; i < inschema.length; i++) {
                    inschemap.put(inschema[i], i);
                }
                for (int i = 0; i < outschema.length; i++) {
                    outschemap.put(outschema[i], i);
                }
                //初始化
                Object[] values = new Object[args.length];
                content=args[inschemap.get("content")];
                if(Util.notEmpty(content)&&content.toString().contains("<br>")){
                    content=content.toString().replace("<td width=\"200\">","").replace("<br> </td>","").replace("<br>"," ");
                }
                for (int i = 0; i < args.length; i++) {
                    if (Util.notEmpty(args[i])) {
                        args[i]=Util.full2Half(args[i].toString());
                        if("null".equals(args[i])) {
                            args[i] = null;
                        }
                    }
                    values[i] = args[i];
                }
                Object[] output = new Object[outschema.length];
                sb.setLength(0);

                if (Util.notEmpty(values[inschemap.get("defendant")])) {
                    sb.append(values[inschemap.get("defendant")].toString()).append(",");
                }
                if (Util.notEmpty(values[inschemap.get("prosecutor")])) {
                    sb.append(values[inschemap.get("prosecutor")].toString());
                }
                if (sb.toString().endsWith(",")) {
                    values[inschemap.get("party")] = sb.toString().substring(0, sb.length() - 1);
                } else {
                    values[inschemap.get("party")] = sb.toString();
                }
                //清洗content
                sourceFrom = values[inschemap.get("source_from")].toString();
                for (String province : provinceArr) {
                    //判断是否需要清洗的省份
                    if (Util.notEmpty(sourceFrom) && sourceFrom.contains(province)) {
                        /**
                         * 初始化数据
                         */
                        //执行法庭
                        for (String pro : new String[]{"安徽", "北京", "海南", "新疆", "陕西", "山西", "福建", "湖北", "西藏", "重庆", "宁夏"}) {
                            if (pro.equals(province)) {
                                values[inschemap.get("execute_unite")] = "";
                            }
                        }
                        //执行法院
                        for (String pro : new String[]{"陕西"}) {
                            if (pro.equals(province)) {
                                values[inschemap.get("execute_gov")] = "";
                            }
                        }
                        //原告、被告、原因
                        for (String pro : provinceArr) {
                            if (pro.equals(province)) {
                                values[inschemap.get("prosecutor")] = "";
                                values[inschemap.get("defendant")] = "";
                                values[inschemap.get("case_reason")] = "";
                            }
                        }

                        //开始清洗
                        //执行法院
                        if (!Util.isEmpty(values[inschemap.get("content")])) {
                            if (Util.isEmpty(values[inschemap.get("execute_gov")])) {
                                values[inschemap.get("execute_gov")] = CleanCourtUtil.getExecuteGov(values[inschemap.get("content")].toString(), province);
                            }
                            //执行法庭
                            if (Util.isEmpty(values[inschemap.get("execute_unite")])) {
                                values[inschemap.get("execute_unite")] = CleanCourtUtil.getExecuteUnite(values[inschemap.get("content")].toString(), province);
                            }
                            //除了天津的,其他的都要提取
                            if (!"天津".equals(province)) {
                                values[inschemap.get("open_time")] = "";
                            }
                            //开庭时间
                            if (Util.isEmpty(values[inschemap.get("open_time")])) {
                                values[inschemap.get("open_time")] = CleanCourtUtil.getOpneTime(values[inschemap.get("content")].toString());
                            }
                            //被告
                            if (Util.isEmpty(values[inschemap.get("defendant")])) {
                                values[inschemap.get("defendant")] = CleanCourtUtil.getDefendant(values[inschemap.get("content")].toString());
                            }
                            //原告
                            if (Util.isEmpty(values[inschemap.get("prosecutor")])) {
                                values[inschemap.get("prosecutor")] = CleanCourtUtil.getProsecutor(values[inschemap.get("content")].toString());
                            }
                            //当事人
                            if (Util.isEmpty(values[inschemap.get("party")])) {
                                values[inschemap.get("party")] = CleanCourtUtil.getParty(values[inschemap.get("content")].toString());
                            }
                            //案由
                            if (Util.isEmpty(values[inschemap.get("case_reason")])) {
                                values[inschemap.get("case_reason")] = CleanCourtUtil.getCaseReason(values[inschemap.get("content")].toString());
                            }
                            //案号
                            if (Util.isEmpty(values[inschemap.get("case_no")])) {
                                values[inschemap.get("case_no")] = CleanCourtUtil.getCaseNo(values[inschemap.get("content")].toString());
                            }
                        }
                    }
                }
                maxDate = null;
                delFlag = false;
                if (Util.notEmpty(values[inschemap.get("delflag")]) && Integer.parseInt(values[inschemap.get("delflag")].toString()) == 1) {
                    delFlag = true;
                    return;
                }


                //增加过滤条件：没有案由也没有案号的 数据过滤掉
                if (Util.isEmpty(values[inschemap.get("case_reason")]) && Util.isEmpty(values[inschemap.get("case_no")]) && Util.isEmpty(values[inschemap.get("open_time")])) {
                    return;
                }

                //提取开庭时间
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
                try {
                    output[outschemap.get("liandate")] = Util.isEmpty(values[inschemap.get("open_time")]) ? null : sdf.format(Util.getDate(values[inschemap.get("open_time")].toString()));
                } catch (Exception e) {
                    output[outschemap.get("liandate")] = null;
                }
                //案由
                output[outschemap.get("casereason")] = values[inschemap.get("case_reason")];
                //原告/上诉人

                if (!Util.isEmpty(values[inschemap.get("prosecutor")])) {
                    prosecutor = values[inschemap.get("prosecutor")].toString().replace("null", "");
                }
                if (Util.isEmpty(prosecutor)) {
                    prosecutor = null;
                }
                output[outschemap.get("prosecutorlist")] = prosecutor;
                //开放搜索字段
                output[outschemap.get("prosecutorlist_os")] = prosecutor;
                //被告/被上诉人
                if (!Util.isEmpty(values[inschemap.get("defendant")])) {
                    defendant = values[inschemap.get("defendant")].toString().replace("null", "");
                }
                if (Util.isEmpty(defendant)) {
                    defendant = null;
                }
                output[outschemap.get("defendantlist")] = defendant;
                //开放搜索字段
                output[outschemap.get("defendantlist_os")] = defendant;
                //法院
                execute_gov = null;
                if (!Util.isEmpty(values[inschemap.get("execute_gov")])) {
                    execute_gov = values[inschemap.get("execute_gov")].toString().replace("*", "");
                }
                output[outschemap.get("executegov")] = execute_gov;
                //案号
                output[outschemap.get("caseno")] = values[inschemap.get("case_no")];
                //开庭日期的年
                cal = Calendar.getInstance();
                try {
                    //courtyear = cal.setTime(maxRecord.getDatetime("liandate"));
                    cal.setTime(sdf.parse(output[outschemap.get("liandate")].toString()));
                    courtyear = cal.get(Calendar.YEAR);
                    output[outschemap.get("courtyear")] = courtyear;
                } catch (Exception e) {
                    output[outschemap.get("courtyear")] = 0;
                }
                //类型（暂时定为5）
                output[outschemap.get("type")] = Long.valueOf(Constants.COURT_NOTICE_TYPE);
                //省份
                province = null;
                if (!Util.isEmpty(values[inschemap.get("source_from")])) {
                    province = values[inschemap.get("source_from")].toString();
                    if (province.contains("网")) {
                        province = null;
                    }
                }
                output[outschemap.get("province")] = province;

                //公司名称和id(调用以前的公司曾用名接口，查询公司的包含id，这个任务值为空，另有任务去关联公司名)
                output[outschemap.get("companynames")] = "";
                output[outschemap.get("isvalid")] = values[inschemap.get("isvalid")];

                //详情字段
                //法庭
                output[outschemap.get("execute_unite")] = values[inschemap.get("execute_unite")];
                //排期
                output[outschemap.get("schedule_time")] = Util.isEmpty(values[inschemap.get("schedule_time")]) ? null : sdf.format(Util.getDate(values[inschemap.get("schedule_time")].toString()));
                //承办部门
                output[outschemap.get("undertake_department")] = values[inschemap.get("undertake_department")];
                //审判长/主审人
                if (Util.isEmpty(values[inschemap.get("chief_judge")])) {
                    output[outschemap.get("chief_judge")] = values[inschemap.get("chief_judge")];
                } else {
                    output[outschemap.get("chief_judge")] = values[inschemap.get("chief_judge")].toString().replace("null", "");
                }
                //created_date
                output[outschemap.get("created_date")] = Util.isEmpty(values[inschemap.get("created_date")]) ? null : values[inschemap.get("created_date")].toString().substring(0, 19);
                //updated_date
                output[outschemap.get("updated_date")] = Util.isEmpty(values[inschemap.get("updated_date")]) ? null : values[inschemap.get("updated_date")].toString().substring(0, 19);
                output[outschemap.get("party")] = values[inschemap.get("party")];
                output[outschemap.get("datatype")] = values[inschemap.get("datatype")];
                output[outschemap.get("key_no")] = values[inschemap.get("key_no")];
                output[outschemap.get("content")] = content;
                if(content!=null && (content.toString().contains("{")||content.toString().contains("}"))){
                    output[outschemap.get("is_struct")]="false";
                }else {
                    output[outschemap.get("is_struct")] = values[inschemap.get("is_struct")];
                }
                if (Util.isEmpty(output[outschemap.get("executegov")]) == true
                        && Util.isEmpty(output[outschemap.get("execute_unite")]) == true
                        && Util.isEmpty(output[outschemap.get("liandate")]) == true
                        && Util.isEmpty(output[outschemap.get("prosecutorlist_os")]) == true
                        && Util.isEmpty(output[outschemap.get("defendantlist_os")]) == true) {
                    return;
                }
                output[outschemap.get("id")] = MD5Util.ecodeByMD5(
                        (Util.isEmpty(output[outschemap.get("caseno")]) == true ? "" : output[outschemap.get("caseno")].toString())
                                + (Util.isEmpty(output[outschemap.get("casereason")]) == true ? "" : output[outschemap.get("casereason")].toString())
                                + (Util.isEmpty(output[outschemap.get("executegov")]) == true ? "" : output[outschemap.get("executegov")].toString())
                                + (Util.isEmpty(output[outschemap.get("execute_unite")]) == true ? "" : output[outschemap.get("execute_unite")].toString())
                                + (Util.isEmpty(output[outschemap.get("liandate")]) == true ? "" : output[outschemap.get("liandate")].toString())
                                + (Util.isEmpty(output[outschemap.get("prosecutorlist_os")]) == true ? "" : output[outschemap.get("prosecutorlist_os")].toString())
                                + (Util.isEmpty(output[outschemap.get("defendantlist_os")]) == true ? "" : output[outschemap.get("defendantlist_os")].toString())
                ) + Constants.COURT_NOTICE_TYPE;

                /**
                 * 说明：公司名要包含原告、被告，多个公司名以逗号隔开，
                 * 又考虑到别的任务要关联公司名，
                 * 所以采用一个公司名称输出一条记录的做法，
                 * 在关联公司名的任务中进行整合
                 */
                nameset.clear();
                if (!Util.isEmpty(prosecutor)) {
                    String key = prosecutor;
                    String s;
                    for (String name : Util.split(key)) {
                        //处理尾部为等的名称:王燕等
                        if (!Util.isEmpty(name) && name.endsWith("等")) {
                            name = name.substring(0, name.length() - 1);
                        }

                        //处理类似：张涌立与李红杰
                        if (!Util.isEmpty(name) && name.contains("与")) {
                            if (name.contains(s = "与") || name.contains(s = ",") || name.contains(s = "、")) {
                                for (String str : name.split(s)) {
                                    nameset.add(str);
                                }
                            }
                        } else {
                            nameset.add(name);
                        }

                    }
                }
                if (!Util.isEmpty(defendant)) {
                    String key = defendant;
                    String s;
                    for (String name : Util.split(key)) {
                        //处理尾部为等的名称:王燕等
                        if (!Util.isEmpty(name) && name.endsWith("等")) {
                            name = name.substring(0, name.length() - 1);
                        }

                        //处理类似：张涌立与李红杰
                        if (!Util.isEmpty(name) && name.contains("与")) {
                            if (name.contains(s = "与") || name.contains(s = ",") || name.contains(s = "、")) {
                                for (String str : name.split(s)) {
                                    nameset.add(str);
                                }
                            }
                        } else {
                            nameset.add(name);
                        }
                    }
                }

                if (nameset.isEmpty()) {
                    output[outschemap.get("name")] = strNull;
                    forward(output);
                } else {
                    output[outschemap.get("name")] = nameset.toString().
                            replaceAll("\\[|\\]", "").
                            replaceAll(", ", ",");//去除空格
                    forward(output);
                }
            }catch (Exception e){

            }
    }


        @Override
        public StructObjectInspector initialize(ObjectInspector[] args)
            throws UDFArgumentException {
            if (args.length != 20) {
                throw new UDFArgumentLengthException(
                        "Courtnotice_filterUDTF takes only 19 argument");
            }
            ArrayList<String> fieldNames = new ArrayList<String>();
            ArrayList<ObjectInspector> fieldOIs = new ArrayList<ObjectInspector>();

            for(int i=0;i<outschema.length;i++){
                fieldNames.add(outschema[i].toString());
                fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
            }
            return ObjectInspectorFactory.getStandardStructObjectInspector(
                    fieldNames, fieldOIs);

    }

/*    public static void main(String[] args) throws HiveException {
        System.out.println("aabbaa".replaceFirst("a",""));
    }*/



}