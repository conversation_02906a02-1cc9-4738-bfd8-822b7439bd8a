package com.qcc.udf.es_suggest;


import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/6/12
 * @Description 字段名对应的code
 */
public enum Column {
    //
    WEIGHT(0, 0.80),
    ADDRESS(5, 0.25),
    ORGNO(10, 0.80),
    REGNO(15, 0.80),
    CREDITCODE(20, 0.80),
    EMAIL(25, 0.80),
    CONTACTNUMBER(30, 0.80),
    WECHAT(35, 0.28),
    FEATURELIST(40, 0.30),
    INVEST(45, 0.80),
    NAME(50, 0.85),
    PERSONSEARCH(55, 0.90),
    PRODUCT(60, 0.95),
    STOCKINFO(65, 1.00),
    ;

    private Integer code;
    private Double weight;

    public Integer getCode() {
        return code;
    }

    public Double getWeight() {
        return weight;
    }

    Column(Integer code, Double weight) {
        this.code = code;
        this.weight = weight;
    }

    public static Column get(Integer code) {
        for (Column value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return WEIGHT;
    }

    public static List<String> fields() {
        ArrayList<String> fields = new ArrayList<>();
        fields.add("text");
        fields.add("weight");
        fields.add("code");
        return fields;
    }

    public static List<ObjectInspector> inspectors() {
        ArrayList<ObjectInspector> inspectors = new ArrayList<>();
        inspectors.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        inspectors.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        inspectors.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        return inspectors;
    }
}
