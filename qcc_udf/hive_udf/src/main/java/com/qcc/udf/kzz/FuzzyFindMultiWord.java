package com.qcc.udf.kzz;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * 模糊搜索字符串
 * <AUTHOR>
 * @date 2022/5/9
 */
public class FuzzyFindMultiWord extends UDF {

    public static boolean evaluate(String content, String substr, String splitStr) {
        boolean result =false;
        try{
            if(StringUtils.isNotBlank(content)&&StringUtils.isNotBlank(substr)){
                if(StringUtils.isNotBlank(splitStr)){
                    //多项
                    String[] array = substr.split(splitStr);
                    if(null!=array){
                        for (String str : array) {
                            if(content.contains(str)){
                                result =true;
                                break;
                            }
                        }
                    }
                }else{
                    //一项
                    result = content.contains(substr);
                }
            }

        }catch (Exception e){

        }
        return result;
    }
//    public static void main(String[] args) {
//        String content = " 负责公司开发产品的销售及推工程师广";
//        boolean region = evaluate(content,"开发,工程师",",");
//        System.out.println(region);
//    }
}
