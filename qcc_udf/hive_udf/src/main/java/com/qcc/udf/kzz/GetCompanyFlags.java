package com.qcc.udf.kzz;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 提取公司flag
 * <AUTHOR>
 * @date 2022/4/29
 */
public class GetCompanyFlags extends UDF {
    public static String evaluate(String flags,String filterFlags) {
        String result = flags;
        try{
            if(StringUtils.isNotBlank(flags)&&StringUtils.isNotBlank(filterFlags)){
                List<String> flagList = Arrays.asList(flags.split(","));
                List<String> filterFlagList = Arrays.asList(filterFlags.split(","));

                List<String> flagResultList = flagList.stream().filter(item->StringUtils.isNotBlank(item))
                        .filter(item->!filterFlagList.contains(item)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(flagResultList)){
                    result = flagResultList.stream().collect(Collectors.joining(","));
                }

            }

        }catch (Exception e){

        }
        return result;
    }

//    public static void main(String[] args) throws IOException {
//        String flags="567f8dbdc99d53f0,8df758f6087fa05f,AC,BL,CR,E,ed0e5d8d77d9c6bf,ensh.18,EPE,FBT,GT,JU,m3c5e84951e4a4f1715212e5d76174e5,PT,SME,VMN,VT";
//        String filterFlags="AOP,ENP,RE,T,MN,TE,BL";
//        String percent = evaluate(flags,filterFlags);
//        System.out.println(percent);
//    }
}
