package com.qcc.udf.cpws;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2022/08/15 14:38
 * @description ：
 */
public class BaseProvinceUtil {

    private static final Map<String, String> PROVINCE_CODE_MAP;

    static {
        PROVINCE_CODE_MAP = new HashMap<String, String>() {
            private static final long serialVersionUID = 7123666413724132193L;

            {
                this.put("北京市", "BJ");
                this.put("天津市", "TJ");
                this.put("河北省", "HB");
                this.put("山西省", "SX");
                this.put("内蒙古自治区", "NMG");
                this.put("辽宁省", "LN");
                this.put("吉林省", "JL");
                this.put("黑龙江省", "HLJ");
                this.put("上海市", "SH");
                this.put("江苏省", "JS");
                this.put("浙江省", "ZJ");
                this.put("安徽省", "AH");
                this.put("福建省", "FJ");
                this.put("江西省", "JX");
                this.put("山东省", "SD");
                this.put("河南省", "HEN");
                this.put("湖北省", "HUB");
                this.put("湖南省", "HUN");
                this.put("广东省", "GD");
                this.put("广西壮族自治区", "GX");
                this.put("海南省", "HAIN");
                this.put("重庆市", "CQ");
                this.put("四川省", "SC");
                this.put("贵州省", "GZ");
                this.put("云南省", "YN");
                this.put("西藏自治区", "XZ");
                this.put("西藏", "XZ");
                this.put("陕西省", "SAX");
                this.put("甘肃省", "GS");
                this.put("青海省", "QH");
                this.put("宁夏回族自治区", "NX");
                this.put("新疆维吾尔自治区", "XJ");
                this.put("台湾省", "TW");
                this.put("香港特别行政区", "HK");
                this.put("澳门", "MO");

                this.put("北京", "BJ");
                this.put("天津", "TJ");
                this.put("河北", "HB");
                this.put("山西", "SX");
                this.put("内蒙古", "NMG");
                this.put("辽宁", "LN");
                this.put("吉林", "JL");
                this.put("黑龙江", "HLJ");
                this.put("上海", "SH");
                this.put("江苏", "JS");
                this.put("浙江", "ZJ");
                this.put("安徽", "AH");
                this.put("福建", "FJ");
                this.put("江西", "JX");
                this.put("山东", "SD");
                this.put("河南", "HEN");
                this.put("湖北", "HUB");
                this.put("湖南", "HUN");
                this.put("广东", "GD");
                this.put("广西", "GX");
                this.put("海南", "HAIN");
                this.put("重庆", "CQ");
                this.put("四川", "SC");
                this.put("贵州", "GZ");
                this.put("云南", "YN");
                this.put("西藏", "XZ");
                this.put("陕西", "SAX");
                this.put("甘肃", "GS");
                this.put("青海", "QH");
                this.put("宁夏", "NX");
                this.put("新疆", "XJ");
                this.put("台湾", "TW");
                this.put("香港", "HK");

            }
        };
    }

    /**
     * Desc:根据省份名称获取省份code
     *
     * */
    public static String getProvinceCode(String provinceName) {
        if (StringUtils.isNotEmpty(provinceName)) {
            return PROVINCE_CODE_MAP.getOrDefault(provinceName, "");
        }
        return "";
    }
}
