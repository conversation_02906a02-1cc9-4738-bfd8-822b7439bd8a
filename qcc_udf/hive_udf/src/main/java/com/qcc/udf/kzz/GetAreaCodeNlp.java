package com.qcc.udf.kzz;

import com.qcc.udf.temp.CommonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 通过nlp分词匹配地址
 * <AUTHOR>
 * @date 2022/1/28
 */
public class GetAreaCodeNlp  extends UDF {
    private final static Map<String, List<String>> provinceAreaMap;
    private final static Map<String, String> areaCodeMap;
    private final static Map<String, String> codeAreaMap;
    static {
        provinceAreaMap = new ConcurrentHashMap<>();
        areaCodeMap = new ConcurrentHashMap<>();
        codeAreaMap = new ConcurrentHashMap<>();

        try {
            InputStream is = GetAreaCodeNlp.class.getResourceAsStream("/kzz_area_nlp.txt");
            BufferedReader br = new BufferedReader(new InputStreamReader(is));
            String line;
            List<String> contents = new ArrayList<>();
            while ((line = br.readLine()) != null) {
                contents.add(line);
            }
            for (String content : contents) {
                String[] areaArr = content.split("\\|");
                String code = areaArr[0];
                String province = areaArr[1];
                String area = areaArr[2];
                areaCodeMap.put(area,code);
                codeAreaMap.put(code,area);
                province=province.replaceAll("省|市","");
                List<String> areaList = provinceAreaMap.get(province);
                if (null == areaList) {
                    areaList = new ArrayList<>();
                }
                areaList.add(area);
                provinceAreaMap.put(province, areaList);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public static String evaluate(String content) {
        String result ="";
        try{
            String cleanHtmlAddress = CommonUtil.cleanHtmlTag(content);
            cleanHtmlAddress = cleanHtmlAddress.replaceAll("商厦|中国|自由贸易试验区|管理区|开发区|办公区|布依族|\\(.*?\\)|（.*?）|州街|哈尼族|白族|傣族|回族|彝族|侗族|自治|苗族|布依族|[一|二|三|四|五|六]区|\\w区|社区|工业区|\\w|市场|\\*|生产经营地所在行政区划|住所|住所所在区域|小区|经营场所|住所所在行政区划|属地监管单位|,|:|、|备案日期|所在|门牌号|产权|所在镇|\\t|\\r|\\n|号|公园|门市部|管辖单位", "");

            if(cleanHtmlAddress.contains("县")){
                cleanHtmlAddress = cleanHtmlAddress.substring(0,cleanHtmlAddress.indexOf("县")+1);
            }

            //判断省份
            List<String> reverseProvinceAreas = getProvinceAreas(cleanHtmlAddress);
            String matchArea="";
            if(CollectionUtils.isNotEmpty(reverseProvinceAreas)){
                //能够确定省份
                matchArea = matchAddress(reverseProvinceAreas,cleanHtmlAddress);
            }else{
                //不能确定省份
                matchArea = matchAddress(areaCodeMap.keySet(),cleanHtmlAddress);
            }
            if(StringUtils.isNotBlank(matchArea)){
                result = areaCodeMap.get(matchArea);
            }

        }catch(Exception e){

        }
        if(null==result){
            result="";
        }
        return result;
    }


    private static String matchAddress(Collection<String> areas,String address){
        List<MatchArea> matchAreas =new ArrayList<>();

        for (String area : areas) {
            int matchCount =0;
            int indexSum =0;
            String[] areaArr = area.split(",");
            for (String word : areaArr) {
                int wordIndex = address.indexOf(word);
                if(wordIndex>-1){
                    if(areas.size()<500){
                        //areas能具体到省份
                        matchCount+=1;
                        indexSum+=wordIndex;
                    }else if(address.indexOf(word)<8&&!address.contains(word+"路")&&!address.contains(word+"东路")&&!address.contains(word+"西路")
                            &&!address.contains(word+"南路")&&!address.contains(word+"北路")&&!address.contains(word+"中路")
                            &&!address.contains(word+"街道")&&!address.contains(word+"街道")&&!address.contains(word+"街道")
                            &&!address.contains(word+"街道")&&!address.contains(word+"街道")
                            &&!address.contains(word+"街")&&!address.contains(word+"街")&&!address.contains(word+"街")
                            &&!address.contains(word+"街")&&!address.contains(word+"街")
                            &&!address.contains(word+"北街")&&!address.contains(word+"北街")&&!address.contains(word+"北街")
                            &&!address.contains(word+"北街")&&!address.contains(word+"北街")){

                        //areas是全部数据
                        matchCount+=1;
                        indexSum+=wordIndex;
                    }
                }
            }
            if(matchCount>0){
                MatchArea matchArea =new MatchArea(area,matchCount,indexSum);
                matchAreas.add(matchArea);
            }
        }
        if(CollectionUtils.isNotEmpty(matchAreas)){
            MatchArea matchArea = matchAreas.stream().max(Comparator.comparingInt(MatchArea::getMatchCount)).get();
            int maxMatch = matchArea.getMatchCount();
            List<MatchArea> matchList = matchAreas.stream().filter(item->maxMatch==item.getMatchCount()).collect(Collectors.toList());
            if(matchList.size()==1){
                return matchList.get(0).getValue();
            }else{
                MatchArea matchAreaIndex = matchList.stream().min(Comparator.comparingInt(MatchArea::getIndexSum)).get();
                int minIndex = matchAreaIndex.getIndexSum();
                List<MatchArea> matchList1 = matchList.stream().filter(item->minIndex==item.getIndexSum()).collect(Collectors.toList());
                if(matchList1.size()==1){
                    return matchList1.get(0).getValue();
                }else{
                    Optional<MatchArea> matchArea1 = matchList.stream().sorted(Comparator.comparing(item->StringUtils.length(item.getValue()))).findFirst();
                    return matchArea1.get().getValue();
                }

            }
        }
        return "";
    }


    private static List<String> getProvinceAreas(String address) {
        Map<Integer, String> provinceIndexMap = new HashMap<>();
        for (String shortProvince : provinceAreaMap.keySet()) {
            Integer index = address.indexOf(shortProvince);
            if (index > -1&&index<3) {
                provinceIndexMap.put(index, shortProvince);
            }
        }
        if (!provinceIndexMap.isEmpty()) {
            Integer minIndex = provinceIndexMap.keySet().stream().min(Comparator.comparingInt(Integer::intValue)).get();
            String shortProvince = provinceIndexMap.get(minIndex);
            return provinceAreaMap.get(shortProvince);
        }
        return null;
    }
    private static String removeProvince(String address){
        for (String province : provinceAreaMap.keySet()) {
            if(address.contains(province)&&address.indexOf(province)<3){
                return address.replaceAll(province,"");
            }
        }
        return address;
    }
    private static class MatchArea{
        private String value;
        private int matchCount;
        private int indexSum;

        public MatchArea(String value, int matchCount,int indexSum) {
            this.value = value;
            this.matchCount = matchCount;
            this.indexSum= indexSum;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public int getMatchCount() {
            return matchCount;
        }

        public void setMatchCount(int matchCount) {
            this.matchCount = matchCount;
        }

        public int getIndexSum() {
            return indexSum;
        }
    }
//    public static  void main (String[] args) {
//        File sourceFile = new File("C:\\Users\\<USER>\\Desktop\\地址名称.txt");
//        File targetFile = new File("C:\\Users\\<USER>\\Desktop\\result.txt");
//
//        List<String> sourceList = FileUtils.readLines(sourceFile);
//        List<String> targetList = new ArrayList<>();
//        int count =0;
//        for (String content : sourceList) {
//            String code = evaluate(content);
//            String resultContent = code+"|"+content;
//            if(StringUtils.isBlank(code)){
//                System.out.println(resultContent);
//                count++;
//            }else{
//                String targetArea=codeAreaMap.get(code);
//                resultContent = targetArea+"|"+resultContent;
//            }
//            targetList.add(resultContent);
//        }
//        System.out.println(count);
//        FileUtils.writeLines(targetFile,targetList);
//        String address="张店北西六路齐赛科技二期号";
//        String result = evaluate(address);
//        System.out.println(result);
//    }
}
