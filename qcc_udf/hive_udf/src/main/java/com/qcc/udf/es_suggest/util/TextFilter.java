package com.qcc.udf.es_suggest.util;

import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Provide various filter to process chinese text.
 *
 * <AUTHOR>
 * @version 0.0.1 2020-04-13 10:08:43
 */
public class TextFilter {
    /**
     * Suppresses default constructor, ensuring non-instantiability.
     */
    private TextFilter() {
    }

    private static final int DEFAULT_NO_PROCESS_TEXT_LENGTH = 3;

    /**
     * Specify filter characteras.
     */
    public static final Pattern PUNCTUATION_PATTERN = Pattern.compile("[`~!@#$^&*()=|{}':;,\\[\\].<>/?！%￥…（）—｛｝【】‘’；：”“。，、？]");

    public static String trimInvalidPunctuation(String text) {
        if (StringUtils.isEmpty(text) || text.length() < DEFAULT_NO_PROCESS_TEXT_LENGTH) {
            return text;
        }

        String left = text.substring(0, DEFAULT_NO_PROCESS_TEXT_LENGTH);
        String middle = StringUtils.EMPTY;
        String right;

        int restSize = text.length() - DEFAULT_NO_PROCESS_TEXT_LENGTH;


        if (restSize <= DEFAULT_NO_PROCESS_TEXT_LENGTH) {
            right = text.substring(DEFAULT_NO_PROCESS_TEXT_LENGTH);
        } else {
            middle = text.substring(DEFAULT_NO_PROCESS_TEXT_LENGTH, text.length() - DEFAULT_NO_PROCESS_TEXT_LENGTH);
            right = text.substring(text.length() - DEFAULT_NO_PROCESS_TEXT_LENGTH);
        }


        Matcher leftMatcher = PUNCTUATION_PATTERN.matcher(left);
        if (leftMatcher.find()) {
            left = leftMatcher.replaceAll(StringUtils.EMPTY);

        }
        Matcher rightMatcher = PUNCTUATION_PATTERN.matcher(right);
        if (rightMatcher.find()) {
            right = leftMatcher.replaceAll(StringUtils.EMPTY);

        }
        return left + middle + right;
    }

    public static List<String> filterAdministrativeDistricts(String text) {
        List<String> result = new ArrayList<>();
        if (StringUtils.isEmpty(text)) {
            return result;
        }
        // preserve original text.
        result.add(text);
        result.add(AdministrativeDistrictFilter.smartDeleteAdministrativeDistrict(text));
        return result;
    }
}
