package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

public class TranscodeUDF extends UDF {

    public static String evaluate(String recieve, String code) {
        try {
            recieve = recieve.replaceAll("%(?![0-9a-fA-F]{2})", "%25");
            String decode = URLDecoder.decode(recieve, code);
            return decode;
        }catch (UnsupportedEncodingException e){
            return null;
        }

    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        System.out.println(evaluate("%E4%BC%81%E6%9F%A5%E6%9F%A"
                , "UTF-8"));
    }
}
