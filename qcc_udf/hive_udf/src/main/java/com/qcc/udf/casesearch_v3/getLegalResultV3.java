package com.qcc.udf.casesearch_v3;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class getLegalResultV3 extends UDF {
    public static String evaluate(String param) {
        JSONArray result = new JSONArray();
        if (StringUtils.isNotEmpty(param)) {
            JSONArray array = JSONObject.parseObject(param).getJSONArray("InfoList");
            if (array != null && !array.isEmpty()) {
                Iterator<Object> it = array.iterator();
                while (it.hasNext()) {
                    JSONObject json = (JSONObject) it.next();

                    // 获取判决结果的结合
                    JSONArray lrArray = new JSONArray();
                    JSONArray jrArray = new JSONArray();
                    JSONArray prosecutorArray = json.getJSONArray("Prosecutor");
                    if (prosecutorArray != null) {
                        Iterator<Object> sub = prosecutorArray.iterator();
                        while (sub.hasNext()) {
                            JSONObject jsonObject = (JSONObject) sub.next();
                            String keyNo = jsonObject.getString("KeyNo");
                            String name = jsonObject.getString("Name");
                            String lr = jsonObject.getString("LR");
                            String jr = jsonObject.getString("JR");
                            if (StringUtils.isNotEmpty(lr)) {
                                if (StringUtils.isNotEmpty(keyNo) || StringUtils.isNotEmpty(name)) {
                                    lrArray.add(jsonObject);
                                }
                            }
                            if (StringUtils.isNotEmpty(jr)) {
                                if (StringUtils.isNotEmpty(keyNo) || StringUtils.isNotEmpty(name)) {
                                    jrArray.add(jsonObject);
                                }
                            }
                        }
                    }
                    JSONArray defendantArray = json.getJSONArray("Defendant");
                    if (defendantArray != null) {
                        Iterator<Object> sub = defendantArray.iterator();
                        while (sub.hasNext()) {
                            JSONObject jsonObject = (JSONObject) sub.next();
                            String keyNo = jsonObject.getString("KeyNo");
                            String name = jsonObject.getString("Name");
                            String lr = jsonObject.getString("LR");
                            String jr = jsonObject.getString("JR");
                            if (StringUtils.isNotEmpty(lr)) {
                                if (StringUtils.isNotEmpty(keyNo) || StringUtils.isNotEmpty(name)) {
                                    lrArray.add(jsonObject);
                                }
                            }
                            if (StringUtils.isNotEmpty(jr)) {
                                if (StringUtils.isNotEmpty(keyNo) || StringUtils.isNotEmpty(name)) {
                                    jrArray.add(jsonObject);
                                }
                            }
                        }
                    }

                    getInfoFromArray(json.getJSONArray("SxList"), lrArray, jrArray, result, "sx");
                    getInfoFromArray(json.getJSONArray("ZxList"), lrArray, jrArray, result, "zx");
                    getInfoFromArray(json.getJSONArray("XgList"), lrArray, jrArray, result, "xg");
                    getInfoFromArray(json.getJSONArray("ZbList"), lrArray, jrArray, result, "zb");
                    getInfoFromArray(json.getJSONArray("CaseList"), lrArray, jrArray, result, "cpws");
                    getInfoFromArray(json.getJSONArray("SqtjList"), lrArray, jrArray, result, "sqtj");
                    getInfoFromArray(json.getJSONArray("FyggList"), lrArray, jrArray, result, "fygg");
                    getInfoFromArray(json.getJSONArray("SdggList"), lrArray, jrArray, result, "sdgg");
                    getInfoFromArray(json.getJSONArray("XzcjList"), lrArray, jrArray, result, "xzcj");
                    getInfoFromArray(json.getJSONArray("PcczList"), lrArray, jrArray, result, "pccz");
                    getInfoFromArray(json.getJSONArray("XjpgList"), lrArray, jrArray, result, "xjpg");
                    getInfoFromArray(json.getJSONArray("SfpmList"), lrArray, jrArray, result, "sfpm");
                    getInfoFromArray(json.getJSONArray("GqdjList"), lrArray, jrArray, result, "gqdj");
                    getInfoFromArray(json.getJSONArray("XdpgjgList"), lrArray, jrArray, result, "xdpgjg");
                }
            }
        }

        return JSONObject.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect);
    }

    public static void getInfoFromArray(JSONArray array, JSONArray lrArray, JSONArray jrArray, JSONArray result, String type) {
        if (array != null && !array.isEmpty() && array.size() > 0 && lrArray != null && lrArray.size() > 0 && !lrArray.isEmpty()) {
            Iterator<Object> it = array.iterator();
            while (it.hasNext()) {
                JSONObject json = (JSONObject) it.next();

                String keynos = "";
                if ("gqdj".equals(type)) {
                    Set<String> keyNoSet = new LinkedHashSet<>();
                    JSONArray keyNoArray = json.getJSONArray("NameAndKeyNo");
                    if (keyNoArray != null && !keyNoArray.isEmpty() && keyNoArray.size() > 0) {
                        Iterator<Object> sub = keyNoArray.iterator();
                        while (sub.hasNext()) {
                            JSONObject myJson = (JSONObject) sub.next();
                            if (StringUtils.isNotEmpty(myJson.getString("KeyNo"))) {
                                keyNoSet.add(myJson.getString("KeyNo"));
                            }
                        }
                    }
                    keyNoArray = json.getJSONArray("ZxNameAndKeyNo");
                    if (keyNoArray != null && !keyNoArray.isEmpty() && keyNoArray.size() > 0) {
                        Iterator<Object> sub = keyNoArray.iterator();
                        while (sub.hasNext()) {
                            JSONObject myJson = (JSONObject) sub.next();
                            if (StringUtils.isNotEmpty(myJson.getString("KeyNo"))) {
                                keyNoSet.add(myJson.getString("KeyNo"));
                            }
                        }
                    }
                    keynos = String.join(",", keyNoSet);
                }

                JSONObject jsonObject = new JSONObject();
                jsonObject.put("Id", json.getString("Id"));
                jsonObject.put("Type", type);
                jsonObject.put("LR", lrArray);
                jsonObject.put("JR", jrArray);
                jsonObject.put("KeyNos", keynos);

                result.add(jsonObject);
            }
        }
    }

    public static void main(String[] args) {
        System.out.println(evaluate("{\"KtggCnt\":0,\"LastestDateType\":\"首次执行|案件终本日期\",\"XjpgCnt\":0,\"ZxCnt\":1,\"CfgsCnt\":0,\"LastestDate\":1529856000,\"AmtInfo\":{\"2c7806314ad12c54cabdf7dd45f0c878\":{\"Type\":\"案件金额\",\"Amt\":\"296887.00\",\"IsValid\":\"1\"}},\"EarliestDate\":1465142400,\"Source\":\"OT\",\"AnnoCnt\":3,\"EarliestDateType\":\"民事一审|立案日期\",\"XzcfCnt\":0,\"LawyerIds\":\"997b4d52aa373b5155ec75f03903d96f,cb49ffd9b91794e0ad896da0ab118695,d6389af9c90424a7df589ce822998671\",\"CompanyKeywords\":\"2c7806314ad12c54cabdf7dd45f0c878,常州市双环磁电元件厂,常州市双环磁电元件厂（普通合伙）,郑海东\",\"AnNoList\":\"（2016）苏0921民初2859号,（2017）苏0921执3779号,（2017）苏09民终2315号\",\"GqdjCnt\":0,\"XgCnt\":1,\"Tags\":\"1,2,3,4,6,12\",\"FyggCnt\":0,\"ZbCnt\":1,\"LatestTrialRound\":\"首次执行\",\"CfdfCnt\":0,\"CaseName\":\"常州市双环磁电元件厂与郑海东买卖合同纠纷的案件\",\"CfxyCnt\":0,\"SxCnt\":1,\"Province\":\"JS\",\"GroupId\":\"213f97825d17d0dba075db05b6f066f1\",\"LianCnt\":2,\"CaseCnt\":3,\"HbcfCnt\":0,\"PcczCnt\":0,\"Type\":1,\"CaseType\":\"执行案件,民事案件\",\"ProcuratorateList\":\"\",\"CaseRole\":\"[{\\\"D\\\":\\\"一审原告,二审被上诉人,首次执行申请执行人\\\",\\\"N\\\":\\\"2c7806314ad12c54cabdf7dd45f0c878\\\",\\\"O\\\":0,\\\"P\\\":\\\"常州市双环磁电元件厂\\\",\\\"R\\\":\\\"原告\\\",\\\"RL\\\":[{\\\"LR\\\":\\\"8\\\",\\\"R\\\":\\\"原告\\\",\\\"T\\\":\\\"一审\\\"},{\\\"R\\\":\\\"被上诉人\\\",\\\"T\\\":\\\"二审\\\"},{\\\"R\\\":\\\"申请执行人\\\",\\\"T\\\":\\\"首次执行\\\"}]},{\\\"D\\\":\\\"一审被告,二审上诉人,首次执行被执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2,\\\"P\\\":\\\"郑海东\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"LR\\\":\\\"9\\\",\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"},{\\\"LR\\\":\\\"7\\\",\\\"R\\\":\\\"上诉人\\\",\\\"T\\\":\\\"二审\\\"},{\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"首次执行\\\"}]}]\",\"CaseReason\":\"买卖合同纠纷\",\"CourtList\":\"江苏省盐城市中级人民法院,江苏省盐城市响水县人民法院\",\"Id\":\"7d265ea25e7cb8c98bb8bc84203bda05\",\"InfoList\":[{\"Defendant\":[{\"KeyNo\":\"\",\"Role\":\"被告\",\"Org\":-2,\"LR\":\"9\",\"LawFirmList\":[{\"P\":\"\",\"LY\":[{\"P\":\"王祥林\",\"R\":\"委托诉讼代理人\",\"N\":\"\"}],\"N\":\"\",\"O\":-2}],\"Name\":\"郑海东\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"江苏省盐城市响水县人民法院\",\"LatestTimestamp\":1480896000,\"ZxList\":[],\"XzcffList\":[],\"CfdfList\":[],\"HbcfList\":[],\"CaseList\":[{\"CaseType\":\"民事判决书\",\"JudgeDate\":1480896000,\"Amt\":\"296887.00\",\"Id\":\"e9853721d2e11ecf30c344a52c7cce900\",\"ResultType\":\"判决结果\",\"DocType\":\"判决日期\",\"IsValid\":1,\"Result\":\"一、被告郑海东应于本判决生效之日起十日内给付原告常州市双环磁电元件厂货款296887元，并承担利息损失（自2016年6月6日起至偿还完毕之日止，按照中国人民银行同期贷款利率上浮50%计算）。\",\"ShieldCaseFlag\":0}],\"TrialRound\":\"民事一审\",\"Prosecutor\":[{\"KeyNo\":\"2c7806314ad12c54cabdf7dd45f0c878\",\"Role\":\"原告\",\"Org\":0,\"LR\":\"8\",\"LawFirmList\":[{\"P\":\"江苏源博律师事务所\",\"LY\":[{\"P\":\"贡蕾\",\"R\":\"委托诉讼代理人\",\"N\":\"cb49ffd9b91794e0ad896da0ab118695\"}],\"N\":\"wd6f671c208927c3a2a12cf16dc09164\",\"O\":4}],\"Name\":\"常州市双环磁电元件厂\"}],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2016）苏0921民初2859号\",\"CaseType\":\"民事案件\",\"LianList\":[{\"LianDate\":1465142400,\"NameAndKeyNo\":[{\"KeyNo\":\"2c7806314ad12c54cabdf7dd45f0c878\",\"Org\":0,\"Name\":\"常州市双环磁电元件厂\"},{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"郑海东\"}],\"Id\":\"e9853721d2e11ecf30c344a52c7cce90\",\"IsValid\":1}],\"XgList\":[],\"CaseReason\":\"买卖合同纠纷\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]},{\"Defendant\":[{\"KeyNo\":\"\",\"Role\":\"被执行人\",\"Org\":-2,\"Name\":\"郑海东\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"江苏省盐城市响水县人民法院\",\"LatestTimestamp\":1529856000,\"ZxList\":[{\"LianDate\":1507824000,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"2c7806314ad12c54cabdf7dd45f0c878\",\"Org\":0,\"Name\":\"常州市双环磁电元件厂\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"郑海东\"}],\"Id\":\"68e1c7a27c6a9c9ff2c6663c46d3873f1\",\"Biaodi\":\"296887\",\"IsValid\":0}],\"XzcffList\":[],\"CfdfList\":[],\"HbcfList\":[],\"CaseList\":[{\"CaseType\":\"执行裁定书\",\"JudgeDate\":1529251200,\"Amt\":\"\",\"Id\":\"a9a3a19ddcaf44b84d5c84cac7bbedd90\",\"ResultType\":\"裁定结果\",\"DocType\":\"裁定日期\",\"IsValid\":1,\"Result\":\"终结本次执行程序。\",\"ShieldCaseFlag\":0}],\"TrialRound\":\"首次执行\",\"Prosecutor\":[{\"KeyNo\":\"2c7806314ad12c54cabdf7dd45f0c878\",\"Role\":\"申请执行人\",\"Org\":0,\"Name\":\"常州市双环磁电元件厂\"}],\"ZbList\":[{\"FailureAct\":\"0\",\"ExecuteObject\":\"296887\",\"JudgeDate\":1529856000,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"郑海东\"}],\"Id\":\"68e1c7a27c6a9c9ff2c6663c46d3873f\",\"IsValid\":1}],\"ExecuteNo\":\"（2016）苏0921民初2859号\",\"SxList\":[{\"PublishDate\":1529251200,\"ActionType\":\"有履行能力而拒不履行生效法律文书确定义务\",\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"郑海东\"}],\"ExecuteStatus\":\"全部未履行\",\"Id\":\"68e1c7a27c6a9c9ff2c6663c46d3873f2\",\"IsValid\":1}],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2017）苏0921执3779号\",\"CaseType\":\"执行案件\",\"LianList\":[],\"XgList\":[{\"PublishDate\":1529424000,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"2c7806314ad12c54cabdf7dd45f0c878\",\"Org\":0,\"Name\":\"常州市双环磁电元件厂\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"郑海东\"}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"郑海东\"}],\"Id\":\"67c6f4bdd3613b7997c6f4aa52f8ef83\",\"CompanyInfo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"郑海东\"}],\"GlNameAndKeyNo\":[],\"IsValid\":1}],\"CaseReason\":\"借款合同纠纷案件执行\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]},{\"Defendant\":[{\"KeyNo\":\"2c7806314ad12c54cabdf7dd45f0c878\",\"Role\":\"被上诉人（原审原告）\",\"Org\":0,\"LawFirmList\":[{\"P\":\"江苏源博律师事务所\",\"LY\":[{\"P\":\"贡蕾\",\"R\":\"委托诉讼代理人\",\"N\":\"cb49ffd9b91794e0ad896da0ab118695\"}],\"N\":\"wd6f671c208927c3a2a12cf16dc09164\",\"O\":4}],\"Name\":\"常州市双环磁电元件厂（普通合伙）\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"江苏省盐城市中级人民法院\",\"LatestTimestamp\":1505952000,\"ZxList\":[],\"XzcffList\":[],\"CfdfList\":[],\"HbcfList\":[],\"CaseList\":[{\"CaseType\":\"民事判决书\",\"JudgeDate\":1505952000,\"Amt\":\"\",\"Id\":\"0881f293281b47baf0dc9a678a2bb2380\",\"ResultType\":\"判决结果\",\"DocType\":\"判决日期\",\"IsValid\":1,\"Result\":\"驳回上诉，维持原判。\",\"ShieldCaseFlag\":0}],\"TrialRound\":\"民事二审\",\"Prosecutor\":[{\"KeyNo\":\"\",\"Role\":\"上诉人（原审被告）\",\"Org\":-2,\"LR\":\"7\",\"LawFirmList\":[{\"P\":\"江苏锐智律师事务所\",\"LY\":[{\"P\":\"邵军\",\"R\":\"委托诉讼代理人\",\"N\":\"997b4d52aa373b5155ec75f03903d96f\"}],\"N\":\"w5449d331333cccac7fa0a9de76e8d98\",\"O\":4}],\"Name\":\"郑海东\"}],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2017）苏09民终2315号\",\"CaseType\":\"民事案件\",\"LianList\":[{\"LianDate\":1493654400,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"LawFirmList\":[{\"LY\":[{}],\"O\":0}],\"Name\":\"郑海东\"},{\"KeyNo\":\"2c7806314ad12c54cabdf7dd45f0c878\",\"Org\":0,\"Name\":\"常州市双环磁电元件厂\"}],\"Id\":\"0881f293281b47baf0dc9a678a2bb238\",\"IsValid\":1}],\"XgList\":[],\"CaseReason\":\"买卖合同纠纷\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]}],\"SdggCnt\":0}"));
//        System.out.println(evaluate("{\"KtggCnt\":1,\"LastestDateType\":\"首次执行|被执行人立案日期\",\"XjpgCnt\":0,\"ZxCnt\":1,\"CfgsCnt\":0,\"LastestDate\":1625500800,\"AmtInfo\":{\"c71a5b92c0643e75df0a8b4625cdb255\":{\"Type\":\"案件金额\",\"Amt\":\"1211760.29\",\"IsValid\":\"1\"}},\"EarliestDate\":1579017600,\"Source\":\"OT\",\"AnnoCnt\":3,\"EarliestDateType\":\"民事一审|立案日期\",\"XzcfCnt\":0,\"LawyerIds\":\"004c033a0d5305322b804e2121179aba,780d0d557e617548c664fbc9652fa221,bb3a13a64044ecbc538fa1606e557cb7,d1ec4ce3d090f3d3c6a4f77161c1a79b\",\"CompanyKeywords\":\"c71a5b92c0643e75df0a8b4625cdb255,俞昊,安盛天平财产保险股份有限公司合肥中心支公司,朱振洋,朱政洋\",\"AnNoList\":\"（2020）皖0191民初447号,（2020）皖01民终8191号,（2021）皖0191执2250号\",\"GqdjCnt\":0,\"XgCnt\":0,\"Tags\":\"2,4,11,12\",\"FyggCnt\":0,\"ZbCnt\":0,\"LatestTrialRound\":\"首次执行\",\"CfdfCnt\":0,\"CaseName\":\"俞昊与安盛天平财产保险股份有限公司合肥中心支公司,朱振洋机动车交通事故责任纠纷的案件\",\"CfxyCnt\":0,\"SxCnt\":0,\"Province\":\"AH\",\"GroupId\":\"190a7c9cb13bc07ab059e3e3847e75e1\",\"LianCnt\":2,\"CaseCnt\":2,\"HbcfCnt\":0,\"PcczCnt\":0,\"Type\":1,\"CaseType\":\"执行案件,民事案件\",\"ProcuratorateList\":\"\",\"CaseRole\":\"[{\\\"D\\\":\\\"一审原告,二审上诉人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2,\\\"P\\\":\\\"俞昊\\\",\\\"R\\\":\\\"原告\\\",\\\"RL\\\":[{\\\"LR\\\":\\\"8\\\",\\\"R\\\":\\\"原告\\\",\\\"T\\\":\\\"一审\\\"},{\\\"LR\\\":\\\"8\\\",\\\"R\\\":\\\"上诉人\\\",\\\"T\\\":\\\"二审\\\"}]},{\\\"D\\\":\\\"一审被告\\\",\\\"N\\\":\\\"c71a5b92c0643e75df0a8b4625cdb255\\\",\\\"O\\\":0,\\\"P\\\":\\\"安盛天平财产保险股份有限公司合肥中心支公司\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"LR\\\":\\\"9\\\",\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"}]},{\\\"D\\\":\\\"一审被告\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2,\\\"P\\\":\\\"朱振洋\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"LR\\\":\\\"9\\\",\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"}]}]\",\"CaseReason\":\"机动车交通事故责任纠纷\",\"CourtList\":\"安徽省合肥市中级人民法院,安徽省合肥市合肥高新技术产业开发区人民法院\",\"Id\":\"2c5a093b833d2a9c976e9407e368b768\",\"InfoList\":[{\"Defendant\":[{\"KeyNo\":\"c71a5b92c0643e75df0a8b4625cdb255\",\"Role\":\"被告\",\"Org\":0,\"LR\":\"9\",\"LawFirmList\":[{\"P\":\"\",\"LY\":[{\"P\":\"华心发\",\"R\":\"委托诉讼代理人\",\"N\":\"\"}],\"N\":\"\",\"O\":-2}],\"Name\":\"安盛天平财产保险股份有限公司合肥中心支公司\"},{\"KeyNo\":\"\",\"Role\":\"被告\",\"Org\":-2,\"LR\":\"9\",\"LawFirmList\":[{\"P\":\"北京盈科（合肥）律师事务所\",\"LY\":[{\"P\":\"孙承龙\",\"R\":\"委托诉讼代理人\",\"N\":\"bb3a13a64044ecbc538fa1606e557cb7\"},{\"P\":\"郭莉\",\"R\":\"委托诉讼代理人\",\"N\":\"004c033a0d5305322b804e2121179aba\"}],\"N\":\"wdf28e6c82b862f0c5a7090cb7f6a131\",\"O\":4}],\"Name\":\"朱振洋\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"安徽省合肥市合肥高新技术产业开发区人民法院\",\"LatestTimestamp\":1597161600,\"ZxList\":[],\"XzcffList\":[],\"CfdfList\":[],\"HbcfList\":[],\"CaseList\":[{\"CaseType\":\"民事判决书\",\"JudgeDate\":1597161600,\"Amt\":\"1451760.29\",\"Id\":\"c8f9eebc3c515724028375b713ebec320\",\"ResultType\":\"判决结果\",\"DocType\":\"判决日期\",\"IsValid\":1,\"Result\":\"一、被告安盛天平财产保险股份有限公司合肥中心支公司于本判决生效之日起十日内在其承保的机动车交通事故责任强制保险责任限额内赔偿原告俞昊120000元；  二、被告安盛天平财产保险股份有限公司合肥中心支公司于本判决生效之日起十日内在其承保的机动车第三者责任保险限额内赔偿原告俞昊990000元；  三、被告朱振洋于本判决生效之日起十日内赔偿原告俞昊341760.29元；  四、驳回原告俞昊的其他诉讼请求。\",\"ShieldCaseFlag\":0}],\"TrialRound\":\"民事一审\",\"Prosecutor\":[{\"KeyNo\":\"\",\"Role\":\"原告\",\"Org\":-2,\"LR\":\"8\",\"LawFirmList\":[{\"P\":\"北京尚公（合肥）律师事务所\",\"LY\":[{\"P\":\"尹佃阳\",\"R\":\"委托诉讼代理人\",\"N\":\"d1ec4ce3d090f3d3c6a4f77161c1a79b\"}],\"N\":\"w87cd5b41b91857067f70487b8127600\",\"O\":4},{\"P\":\"\",\"LY\":[{\"P\":\"俞吾东\",\"R\":\"委托诉讼代理人\",\"N\":\"\"}],\"N\":\"\",\"O\":-2}],\"Name\":\"俞昊\"}],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2020）皖0191民初447号\",\"CaseType\":\"民事案件\",\"LianList\":[{\"LianDate\":1579017600,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"俞昊\"},{\"KeyNo\":\"c71a5b92c0643e75df0a8b4625cdb255\",\"Org\":0,\"Name\":\"安盛天平财产保险股份有限公司合肥中心支公司\"},{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"朱振洋\"}],\"Id\":\"c8f9eebc3c515724028375b713ebec32\",\"IsValid\":1}],\"XgList\":[],\"CaseReason\":\"机动车交通事故责任纠纷\",\"KtggList\":[{\"ExecuteUnite\":\"第十法庭\",\"OpenDate\":1595900040,\"NameAndKeyNo\":[{\"KeyNo\":\"c71a5b92c0643e75df0a8b4625cdb255\",\"Org\":0,\"Name\":\"安盛天平财产保险股份有限公司合肥中心支公司\"},{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"俞昊\"},{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"朱政洋\"}],\"Id\":\"f7c2b345b38a542f86891ca6bb6f8b9a\",\"IsValid\":1}],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]},{\"Defendant\":[],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"安徽省合肥市中级人民法院\",\"LatestTimestamp\":1603296000,\"ZxList\":[],\"XzcffList\":[],\"CfdfList\":[],\"HbcfList\":[],\"CaseList\":[{\"CaseType\":\"民事判决书\",\"JudgeDate\":1603296000,\"Amt\":\"1211760.29\",\"Id\":\"7e5e5ed29781346fc14fde72e955cef10\",\"ResultType\":\"判决结果\",\"DocType\":\"判决日期\",\"IsValid\":1,\"Result\":\"一、撤销合肥高新技术产业开发区人民法院（2020）皖0191民初447号民事判决；  二、安盛天平财产保险股份有限公司合肥中心支公司于本判决生效之日起十日内在其承保的机动车交通事故强制保险限额内赔偿俞昊120000元（含垫付的10000元）；  三、安盛天平财产保险股份有限公司合肥中心支公司于本判决生效之日起十日内在其承保的机动车第三者责任保险限额内赔偿俞昊1000000元；  四、朱政洋于本判决生效之日起十日内赔偿俞昊91760.29元；  五、驳回俞昊的其他诉讼请求。\",\"ShieldCaseFlag\":0}],\"TrialRound\":\"民事二审\",\"Prosecutor\":[{\"KeyNo\":\"\",\"Role\":\"上诉人（原审原告）\",\"Org\":-2,\"LR\":\"8\",\"LawFirmList\":[{\"P\":\"北京尚公（合肥）律师事务所\",\"LY\":[{\"P\":\"尹佃阳\",\"R\":\"委托诉讼代理人\",\"N\":\"d1ec4ce3d090f3d3c6a4f77161c1a79b\"}],\"N\":\"w87cd5b41b91857067f70487b8127600\",\"O\":4},{\"P\":\"\",\"LY\":[{\"P\":\"郭娟娟\",\"R\":\"委托诉讼代理人\",\"N\":\"\"}],\"N\":\"\",\"O\":-2}],\"Name\":\"俞昊\"},{\"KeyNo\":\"\",\"Role\":\"上诉人（原审被告）\",\"Org\":-2,\"LR\":\"8\",\"LawFirmList\":[{\"P\":\"北京盈科（合肥）律师事务所\",\"LY\":[{\"P\":\"孙承龙\",\"R\":\"委托诉讼代理人\",\"N\":\"bb3a13a64044ecbc538fa1606e557cb7\"},{\"P\":\"刘浩\",\"R\":\"委托诉讼代理人\",\"N\":\"780d0d557e617548c664fbc9652fa221\"}],\"N\":\"wdf28e6c82b862f0c5a7090cb7f6a131\",\"O\":4}],\"Name\":\"朱政洋\"}],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2020）皖01民终8191号\",\"CaseType\":\"民事案件\",\"LianList\":[{\"LianDate\":1600963200,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"俞昊\"},{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"朱政洋\"},{\"KeyNo\":\"c71a5b92c0643e75df0a8b4625cdb255\",\"Org\":0,\"Name\":\"安盛天平财产保险股份有限公司合肥中心支公司\"}],\"Id\":\"7e5e5ed29781346fc14fde72e955cef1\",\"IsValid\":1}],\"XgList\":[],\"CaseReason\":\"机动车交通事故责任纠纷\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]},{\"Defendant\":[{\"KeyNo\":\"\",\"Role\":\"被执行人\",\"Org\":-2,\"Name\":\"朱政洋\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"安徽省合肥市合肥高新技术产业开发区人民法院\",\"LatestTimestamp\":1625500800,\"ZxList\":[{\"LianDate\":1625500800,\"SqrNameAndKeyNo\":[],\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"朱政洋\"}],\"Id\":\"bf72afd00fd36ceac9f853231430082a1\",\"Biaodi\":\"104030\",\"IsValid\":1}],\"XzcffList\":[],\"CfdfList\":[],\"HbcfList\":[],\"CaseList\":[],\"TrialRound\":\"首次执行\",\"Prosecutor\":[],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2021）皖0191执2250号\",\"CaseType\":\"执行案件\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]}],\"SdggCnt\":0} "));
    }
}
