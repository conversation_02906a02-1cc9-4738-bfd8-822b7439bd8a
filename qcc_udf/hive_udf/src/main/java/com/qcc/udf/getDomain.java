package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by xpc on 2018/7/24.
 */
public class getDomain extends UDF {

    public  static String evaluate(String host)  throws Exception{
        if(host == null){
            return "";
        }
        //定义好获取的域名后缀。如果还有要定义的  请添加 |(\\.域名的后缀)
        String regStr = "[^\\.]+((\\.com)|(\\.cn)|(\\.net)|(\\.edu)|(\\.com.cn)|(\\.xyz)|(\\.xin)|(\\.club)|(\\.shop)|(\\.site)|(\\.wang)" +
                "|(\\.top)|(\\.win)|(\\.online)|(\\.tech)|(\\.store)|(\\.bid)|(\\.cc)|(\\.ren)|(\\.lol)|(\\.pro)|(\\.red)|(\\.kim)|(\\.space)|(\\.link)" +
                "|(\\.click)|(\\.news)|(\\.ltd)|(\\.website)|(\\.biz)|(\\.help)|(\\.mom)|(\\.work)|(\\.date)|(\\.loan)|(\\.mobi)|(\\.live)|(\\.studio)" +
                "|(\\.info)|(\\.pics)|(\\.photo)|(\\.trade)|(\\.vc)|(\\.party)|(\\.game)|(\\.rocks)|(\\.band)|(\\.gift)|(\\.wiki)|(\\.design)|(\\.software)" +
                "|(\\.social)|(\\.lawyer)|(\\.engineer)|(\\.org)|(\\.net.cn)|(\\.org.cn)|(\\.gov.cn)|(\\.name)|(\\.tv)|(\\.me)|(\\.asia)|(\\.co)|(\\.press)" +
                "|(\\.video)|(\\.market)|(\\.games)|(\\.science)|(\\.中国)|(\\.公司)|(\\.网络)|(\\.pub)|(\\.la)|(\\.auction)|(\\.email)|(\\.sex)|(\\.sexy)" +
                "|(\\.one)|(\\.host)|(\\.rent)|(\\.fans)|(\\.cn.com)|(\\.life)|(\\.cool)|(\\.run)|(\\.gold)|(\\.rip)|(\\.ceo)|(\\.sale)|(\\.hk)|(\\.io)" +
                "|(\\.gg)|(\\.tm)|(\\.com.hk)|(\\.gs)|(\\.us))$";

        Pattern p = Pattern.compile(regStr);
        Matcher m = p.matcher(host);
        String domain = "";
        //获取一级域名
        while (m.find()) {
            domain = m.group();
        }
        return domain;
    }

    public static void main(String[] args) throws Exception {
        System.out.println(getDomain.evaluate("www.cods.gov.com.cn"));
    }
}
