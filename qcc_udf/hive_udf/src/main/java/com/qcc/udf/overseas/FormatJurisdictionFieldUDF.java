package com.qcc.udf.overseas;

import com.qcc.udf.overseas.constant.UsStateEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * 业务UDF（海外企业）格式化管辖权所属机构字段的样式（UNITED STATES, WASHINGTON 格式）
 * ---------------------------------------------------------------------------------------------------------
 * add jar hdfs://ldh/data/hive/udf/qcc_udf.jar;
 * create temporary function FormatJurisdictionField as 'com.qcc.udf.overseas.FormatJurisdictionFieldUDF';
 * ---------------------------------------------------------------------------------------------------------
 * select FormatJurisdictionField ('CA');
 * 结果: UNITED STATES, CALIFORNIA
 * select FormatJurisdictionField ('CALIFORNIA');
 * 结果: UNITED STATES, CALIFORNIA
 */
public class FormatJurisdictionFieldUDF extends UDF {

    public String evaluate(String input) {
        String output = StringUtils.upperCase(input.trim());
        if (StringUtils.isNotBlank(input)) {
            if (input.length() <= 2) {  // 州的缩写情况
                UsStateEnum usStateEnum = UsStateEnum.getStateEnumByName(input);
                output = (usStateEnum != null) ? ("UNITED STATES, " + StringUtils.upperCase(usStateEnum.getName())) : "";
            } else {
                if (!input.contains("UNITED STATES, ")) {
                    output = StringUtils.upperCase("UNITED STATES, " + input);
                }
            }
        }
        return output;
    }
}
