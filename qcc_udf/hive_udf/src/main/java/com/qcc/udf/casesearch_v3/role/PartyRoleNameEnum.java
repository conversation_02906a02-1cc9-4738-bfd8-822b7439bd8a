package com.qcc.udf.casesearch_v3.role;


import java.util.EnumSet;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 固定身份匹配（优先）
 */
public enum PartyRoleNameEnum {
    YG("原告", PartyRoleCodeEnum.YG),
    YG_SSR("原告/上诉人", PartyRoleCodeEnum.YG),
    SQZXR("申请执行人", PartyRoleCodeEnum.SQZXR),
    SSR("上诉人", PartyRoleCodeEnum.SSR),
    SQR("申请人", PartyRoleCodeEnum.SQR),
    TBCX_SQR("特别程序申请人", PartyRoleCodeEnum.SQR),
    ZS_SQR("再审申请人", PartyRoleCodeEnum.SQR),
    BG("被告", PartyRoleCodeEnum.BG),
    BG_BSSR("被告/被上诉人", PartyRoleCodeEnum.BG),
    BZXR("被执行人", PartyRoleCodeEnum.BZXR),
    BSSR("被上诉人", PartyRoleCodeEnum.BSSR),
    TBCX_BSQR("特别程序被申请人", PartyRoleCodeEnum.BSQR),
    ZS_BSQR("再审被申请人", PartyRoleCodeEnum.BSQR),
    DSR_QT("当事人", PartyRoleCodeEnum.QT),
    PCQQR("赔偿请求人", PartyRoleCodeEnum.QT),
    PCYWJG("赔偿义务机关", PartyRoleCodeEnum.QT),
    GSJG("公诉机关", PartyRoleCodeEnum.QT),
    BGR("被告人", PartyRoleCodeEnum.QT),
    DSR("第三人", PartyRoleCodeEnum.DSR),
    ;


    private String name;
    private PartyRoleCodeEnum roleCodeEnum;

    PartyRoleNameEnum(String name, PartyRoleCodeEnum roleCodeEnum) {
        this.name = name;
        this.roleCodeEnum = roleCodeEnum;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public PartyRoleCodeEnum getRoleCodeEnum() {
        return roleCodeEnum;
    }

    public void setRoleCodeEnum(PartyRoleCodeEnum roleCodeEnum) {
        this.roleCodeEnum = roleCodeEnum;
    }


    private static final Map<String, PartyRoleCodeEnum> lookup = new LinkedHashMap<>();

    static {
        EnumSet.allOf(PartyRoleNameEnum.class).stream().forEach(e -> {
                    lookup.put(e.name, e.roleCodeEnum);
                }
        );
    }

    public static PartyRoleCodeEnum find(String role) {
        if (role == null) {
            return PartyRoleCodeEnum.DEFAULT;
        }
        for (Map.Entry<String, PartyRoleCodeEnum> entry : lookup.entrySet()) {

            if (Objects.equals(role,entry.getKey())) {
                return entry.getValue();
            }
        }
        return PartyRoleCodeEnum.DEFAULT;
    }
}
