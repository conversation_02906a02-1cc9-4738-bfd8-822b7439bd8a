package com.qcc.udf.cpws;

import java.util.ArrayList;
import java.util.List;

public class LawSuitEntity {
    // 该对象中提取到的当事人信息集合（民事案件->ktgg/lian/fygg/sdgg中汇总，执行案件->相同立案日期的sx/zx/xg中汇总）
    private String keyword;
    // 对象中所有获取到的法院名称按照递增排序后的第一个法院名
    private String court;
    // 立案信息列表
    private List<String> lianList;
    // 开庭公告列表
    private List<String> ktggList;
    // 送达公告列表
    private List<String> sdggList;
    // 法院公告列表
    private List<String> fyggList;
    // 失信列表
    private List<String> sxList;
    // 执行列表
    private List<String> zxList;
    // 限高列表
    private List<String> xgList;
    // 裁判文书列表
    private List<String> caseList;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getCourt() {
        return court;
    }

    public void setCourt(String court) {
        this.court = court;
    }

    public List<String> getLianList() {
        return lianList;
    }

    public void setLianList(List<String> lianList) {
        this.lianList = lianList;
    }

    public List<String> getKtggList() {
        return ktggList;
    }

    public void setKtggList(List<String> ktggList) {
        this.ktggList = ktggList;
    }

    public List<String> getSdggList() {
        return sdggList;
    }

    public void setSdggList(List<String> sdggList) {
        this.sdggList = sdggList;
    }

    public List<String> getFyggList() {
        return fyggList;
    }

    public void setFyggList(List<String> fyggList) {
        this.fyggList = fyggList;
    }

    public List<String> getSxList() {
        return sxList;
    }

    public void setSxList(List<String> sxList) {
        this.sxList = sxList;
    }

    public List<String> getZxList() {
        return zxList;
    }

    public void setZxList(List<String> zxList) {
        this.zxList = zxList;
    }

    public List<String> getXgList() {
        return xgList;
    }

    public void setXgList(List<String> xgList) {
        this.xgList = xgList;
    }

    public List<String> getCaseList() {
        return caseList;
    }

    public void setCaseList(List<String> caseList) {
        this.caseList = caseList;
    }

    /**
     * 创建一个空对象
     */
    public LawSuitEntity() {
        this.keyword = "";
        this.court = "";
        this.lianList = new ArrayList<>();
        this.ktggList = new ArrayList<>();
        this.fyggList = new ArrayList<>();
        this.sdggList = new ArrayList<>();
        this.sxList = new ArrayList<>();
        this.zxList = new ArrayList<>();
        this.xgList = new ArrayList<>();
        this.caseList = new ArrayList<>();
    }

    /**
     * 创建一个民事类型对象
     * @param keyword
     * @param lianList
     * @param ktggList
     * @param sdggList
     * @param fyggList
     */
    public LawSuitEntity(String keyword, List<String> lianList, List<String> ktggList, List<String> sdggList, List<String> fyggList) {
        this.keyword = keyword;
        this.court = "";
        this.lianList = lianList;
        this.ktggList = ktggList;
        this.sdggList = sdggList;
        this.fyggList = fyggList;
        this.caseList = new ArrayList<>();
        this.zxList = new ArrayList<>();
        this.sxList = new ArrayList<>();
        this.xgList = new ArrayList<>();
    }

    /**
     * 创建一个执行案件对象
     * @param sxList
     * @param zxList
     * @param xgList
     */
    public LawSuitEntity(String keyword, List<String> sxList, List<String> zxList, List<String> xgList) {
        this.keyword = keyword;
        this.court = "";
        this.sxList = sxList;
        this.zxList = zxList;
        this.xgList = xgList;
        this.caseList = new ArrayList<>();
        this.lianList = new ArrayList<>();
        this.ktggList = new ArrayList<>();
        this.fyggList = new ArrayList<>();
        this.sdggList = new ArrayList<>();
    }

    /**
     * 创建一个只有裁判文书的对象
     * @param keyword
     * @param caseList
     */
    public LawSuitEntity(String keyword, List<String> caseList) {
        this.keyword = keyword;
        this.court = "";
        this.caseList = caseList;
        this.lianList = new ArrayList<>();
        this.ktggList = new ArrayList<>();
        this.fyggList = new ArrayList<>();
        this.sdggList = new ArrayList<>();
        this.sxList = new ArrayList<>();
        this.zxList = new ArrayList<>();
        this.xgList = new ArrayList<>();
    }

//    /**
//     * 创建一个只有失信/执行/限高的对象
//     * @param sxOrZxOrXgList
//     * @param type sx/zx/xg
//     */
//    public LawSuitEntity(List<String> sxOrZxOrXgList, String type) {
//        this.keyword = "";
//        this.court = "";
//        this.caseList = new ArrayList<>();
//        this.lianList = new ArrayList<>();
//        this.ktggList = new ArrayList<>();
//        this.fyggList = new ArrayList<>();
//        this.sdggList = new ArrayList<>();
//        this.sxList = type.equals("sx") ? sxOrZxOrXgList : new ArrayList<>();
//        this.zxList = type.equals("zx") ? sxOrZxOrXgList : new ArrayList<>();
//        this.xgList = type.equals("xg") ? sxOrZxOrXgList : new ArrayList<>();
//    }
}
