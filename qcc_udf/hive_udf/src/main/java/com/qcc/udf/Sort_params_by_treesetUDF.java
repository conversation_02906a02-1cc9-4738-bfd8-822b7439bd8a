package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Set;
import java.util.TreeSet;

public class Sort_params_by_treesetUDF extends UDF{
    public String evaluate(String...names){
        Set<String> nameSet = new TreeSet<String>();
        if (names.length>=1) {

            for (String name : names) {
                if(!"".equals(name.trim())) {
                    nameSet.add(name.trim());
                }
            }
        }
        String result="";
        for (String name : nameSet) {
            result = result +name+",";
        }
        if(result.endsWith(",")) {
            result = result.substring(0, result.length() - 1);
        }
        if(result.trim().length() == 0){
            result=null;
        }
        return result;

    }

}

