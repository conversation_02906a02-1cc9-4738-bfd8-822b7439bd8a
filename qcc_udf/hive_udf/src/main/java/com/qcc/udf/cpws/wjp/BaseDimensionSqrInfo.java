package com.qcc.udf.cpws.wjp;

import lombok.Data;

import javax.jdo.annotations.Column;
import java.io.Serializable;
import java.util.Date;

/**
* @website https://www.qcc.com
* @description /
* <AUTHOR>
* @date 2023-10-13
**/
@Data
public class BaseDimensionSqrInfo  implements Serializable {

    private String id;

    private Integer dataStatus;

    private Date createDate;

    private Date updateDate;

    /** 维度id */
    @Column(name = "dimension_id")
    private String dimensionId;

    /** 3：裁判文书；5：限高 */
    @Column(name = "dimension_type")
    private Integer dimensionType;

    /** 案号 */
    @Column(name = "case_no")
    private String caseNo;

    /** 案号md5加密 */
    @Column(name = "case_no_md5")
    private String caseNoMd5;

    /** 申请人id */
    @Column(name = "sqr_key_no")
    private String sqrKeyNo;

    /** 申请人名称 */
    @Column(name = "sqr_name")
    private String sqrName;

    /** 申请人org */
    @Column(name = "sqr_org")
    private Integer sqrOrg;

    /** 被告集合 */
    @Column(name = "defendants")
    private String defendants;



}