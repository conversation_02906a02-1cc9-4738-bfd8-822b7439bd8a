package com.qcc.udf.casesearch_v3;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.output.*;
import com.qcc.udf.casesearch_v3.enums.CaseCategoryEnum;
import com.qcc.udf.casesearch_v3.enums.CaseDocTypeEnum;
import com.qcc.udf.casesearch_v3.enums.LawResultEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 获取案件状态UDF
 * <AUTHOR>
 * @date 2021年09月16日 14:24
 */
public class GetCaseStatusUDF extends UDF {
    public static String evaluate(List<LawSuitV3OutputEntity> detailList){
//        List<LawSuitV3OutputEntity> detailList = JSON.parseArray(detailJson,LawSuitV3OutputEntity.class);
        List<Map<String,String>> caseStatusList = new ArrayList<>();
        for (LawSuitV3OutputEntity outputEntity : detailList) {
            if(CollectionUtils.isEmpty(outputEntity.getInfoList())){
                continue;
            }
            String id = outputEntity.getId();
            //按照时间到排的审理程序集合
            List<InfoListEntity> sortedInfoList = outputEntity.getInfoList().stream()
                    .sorted(Comparator.comparing(InfoListEntity::getLatestTimestamp,Comparator.reverseOrder())
                            .thenComparing(InfoListEntity::getTrialRound))
                    .collect(Collectors.toList());

            //最新的审理流程
            InfoListEntity latestTrialRound = sortedInfoList.get(0);
            String trialRoundName = latestTrialRound.getTrialRound();
            String anNo = latestTrialRound.getAnno();

            Set<String> allJudgeResultTags = new HashSet<>();
            for (NameAndKeyNoEntity nameKey : latestTrialRound.getProsecutor()) {
                if(!Strings.isNullOrEmpty(nameKey.getLawsuitResult())){
                    allJudgeResultTags.addAll(Arrays.asList(nameKey.getLawsuitResult().split(",")));
                }
            }
            for (NameAndKeyNoEntity nameKey : latestTrialRound.getDefendant()) {
                if(!Strings.isNullOrEmpty(nameKey.getLawsuitResult())){
                    allJudgeResultTags.addAll(Arrays.asList(nameKey.getLawsuitResult().split(",")));
                }
            }

            List<BaseCaseOutEntity> allOutList = buildAllOutput(latestTrialRound);

            if(CollectionUtils.isEmpty(allOutList)){
                continue;
            }

            allOutList.sort(Comparator.comparing(BaseCaseOutEntity::getTimeStamp,Comparator.reverseOrder())
            .thenComparing(BaseCaseOutEntity::getTimeType));

            Map<String,String> outMap = new HashMap<>();
            outMap.put("Id",id);
            //最新的案件
            BaseCaseOutEntity latestCase = allOutList.get(0);
            CaseTypeMappingEntity caseInfo = convert(latestCase);
            CaseCategoryEnum category = caseInfo.getCaseType();
            if(category == CaseCategoryEnum.KTGG
                    || category == CaseCategoryEnum.LA
                    || category == CaseCategoryEnum.SQTJ){
                outMap.put("CaseStatus",trialRoundName+"[结案状态-1]");
                outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                caseStatusList.add(outMap);
                continue;
            }

            if(anNo.contains("财保")){
                if(category == CaseCategoryEnum.GQDJ){
                    outMap.put("CaseStatus","财产保全中");
                    outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                    caseStatusList.add(outMap);
                    continue;
                }
                if(category == CaseCategoryEnum.CPWS){
                    if(allJudgeResultTags.contains(LawResultEnum.CHA_FENG_ZAN_KOU_DONG_JIE.getCode())){
                        outMap.put("CaseStatus","财产保全中");
                        outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                        caseStatusList.add(outMap);
                        continue;
                    }
                }
            }

            if(category == CaseCategoryEnum.FYGG){
                FyggListEntity fygg = (FyggListEntity)caseInfo.getCaseInfo();
                if(fygg.getCategory() != null && fygg.getCategory().contains("开庭传票")){
                    outMap.put("CaseStatus",trialRoundName+"[结案状态-1]");
                    outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                    caseStatusList.add(outMap);
                    continue;
                }
                if(fygg.getCategory() != null && (fygg.getCategory().contains("破产") || fygg.getCategory().contains("清算"))){
                    outMap.put("CaseStatus",trialRoundName+"破产受理中");
                    outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                    caseStatusList.add(outMap);
                    continue;
                }
            }
            if(category == CaseCategoryEnum.CPWS){
                CaseListEntity cpws = (CaseListEntity)caseInfo.getCaseInfo();
                if(allJudgeResultTags.contains(LawResultEnum.CHE_SU.getCode())
                        || allJudgeResultTags.contains(LawResultEnum.SHI_WEI_CHE_SU.getCode())){
                    outMap.put("CaseStatus",trialRoundName+"撤诉,结案");
                    outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                    caseStatusList.add(outMap);
                    continue;
                }
                if(allJudgeResultTags.contains(LawResultEnum.JIE_CHU_CHA_FENG_ZAN_KOU_DONG_JIE.getCode())){
                    outMap.put("CaseStatus",trialRoundName+"解除查冻扣");
                    outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                    caseStatusList.add(outMap);
                    continue;
                }
                if(allJudgeResultTags.contains(LawResultEnum.BO_HUI.getCode())){
                    if(trialRoundName.contains("一审")){
                        outMap.put("CaseStatus",trialRoundName+"驳回起诉");
                        outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                        caseStatusList.add(outMap);
                        continue;
                    } else if(trialRoundName.contains("二审") || trialRoundName.contains("再审")){
                        outMap.put("CaseStatus",trialRoundName+"驳回上诉");
                        outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                        caseStatusList.add(outMap);
                        continue;
                    }else{
                        outMap.put("CaseStatus",trialRoundName+"驳回");
                        outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                        caseStatusList.add(outMap);
                        continue;
                    }
                }
                if(allJudgeResultTags.contains(LawResultEnum.DA_CHENG_TIAO_JIE.getCode())){
                    outMap.put("CaseStatus",trialRoundName+"达成和解,已结案");
                    outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                    caseStatusList.add(outMap);
                    continue;
                }
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime judgeDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(cpws.getJudgeDate()*1000L), ZoneId.systemDefault());
                if(allJudgeResultTags.contains(LawResultEnum.ZHI_CHI.getCode())
                        ||allJudgeResultTags.contains(LawResultEnum.DUI_FANG_BEI_ZHI_CHI.getCode())
                        ||allJudgeResultTags.contains(LawResultEnum.BU_ZHI_CHI.getCode())
                        ||allJudgeResultTags.contains(LawResultEnum.DUI_FANG_BU_BEI_ZHI_CHI.getCode())
                        ||allJudgeResultTags.contains(LawResultEnum.BU_FEN_ZHI_CHI.getCode())
                        ||allJudgeResultTags.contains(LawResultEnum.DUI_FANG_BEI_BU_FEN_ZHI_CHI.getCode())
                        ||allJudgeResultTags.contains(LawResultEnum.BU_CHENG_DAN_ZE_RENG.getCode())
                        ||allJudgeResultTags.contains(LawResultEnum.WU.getCode())){
                    if(trialRoundName.contains("一审")){
                        String secondLevelName = "";
                        long days = Duration.between(judgeDate,now).toDays();
                        //判决书
                        if(cpws.getCaseType().contains(CaseDocTypeEnum.VERDICT.getType())){
                            if(days<=15){
                                secondLevelName = "已判决,可上诉[结案状态-2]";
                            }else{
                                secondLevelName = "已判决";
                            }
                        }
                        //裁定书
                        if(cpws.getCaseType().contains(CaseDocTypeEnum.ADJUDICATE.getType())){
                            secondLevelName = "已判决";
                        }

                        if(!Strings.isNullOrEmpty(secondLevelName)){
                            outMap.put("CaseStatus",trialRoundName+secondLevelName);
                            outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                            caseStatusList.add(outMap);
                            continue;
                        }
                    }

                    if(trialRoundName.contains("二审")||trialRoundName.contains("再审")){
                        String secondLevelName = "";
                        //判决书
                        if(cpws.getCaseType().contains(CaseDocTypeEnum.VERDICT.getType())){
                            secondLevelName = "已判决";
                        }
                        //裁定书
                        if(cpws.getCaseType().contains(CaseDocTypeEnum.ADJUDICATE.getType())){
                            secondLevelName = "已裁定";
                        }
                        if(!Strings.isNullOrEmpty(secondLevelName)){
                            outMap.put("CaseStatus",trialRoundName+secondLevelName);
                            outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                            caseStatusList.add(outMap);
                            continue;
                        }
                    }
                }

            }

            if(trialRoundName.contains("执行")){
                boolean flag = false;
                if(category == CaseCategoryEnum.GQDJ
                        ||category == CaseCategoryEnum.XJPG
                        || category == CaseCategoryEnum.SFPM){
                    flag = true;
                }
                if(category == CaseCategoryEnum.CPWS
                        && allJudgeResultTags.contains(LawResultEnum.CHA_FENG_ZAN_KOU_DONG_JIE.getCode())){
                    flag = true;
                }
                if(category == CaseCategoryEnum.FYGG){
                    FyggListEntity fygg = (FyggListEntity)caseInfo.getCaseInfo();
                    if(fygg.getCategory().contains("拍卖")){
                        flag = true;
                    }
                }
                if(flag){
                    outMap.put("CaseStatus",trialRoundName+"资产强制处置中");
                    outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                    caseStatusList.add(outMap);
                    continue;
                }
            }

            if(trialRoundName.contains("执行")){
                List<SXListEntity> sxList = genNoNullList(latestTrialRound.getSxList());
                List<ZXListEntity> zxList = genNoNullList(latestTrialRound.getZxList());
                List<XGListEntity> xgList = genNoNullList(latestTrialRound.getXgList());
                List<XZCJListEntity> xzcjList = genNoNullList(latestTrialRound.getXzcjList());
                List<ZbListEntity> zbList = genNoNullList(latestTrialRound.getXzcjList());

                long hisCount = sxList.stream().filter(x->Objects.equals(x.getIsValid(),0)).count()
                        + zxList.stream().filter(x->Objects.equals(x.getIsValid(),0)).count()
                        + xgList.stream().filter(x->Objects.equals(x.getIsValid(),0)).count()
                        + xzcjList.stream().filter(x->Objects.equals(x.getIsValid(),0)).count();

                long validCount = sxList.stream().filter(x->Objects.equals(x.getIsValid(),1)).count()
                        + zxList.stream().filter(x->Objects.equals(x.getIsValid(),1)).count()
                        + xgList.stream().filter(x->Objects.equals(x.getIsValid(),1)).count()
                        + xzcjList.stream().filter(x->Objects.equals(x.getIsValid(),1)).count();
                String secondLevelName = "";
                if(validCount>0 && CollectionUtils.isEmpty(zbList)){
                    secondLevelName = "强制执行中";
                }
                if(category == CaseCategoryEnum.FYGG){
                    FyggListEntity fygg = (FyggListEntity)caseInfo.getCaseInfo();
                    if(fygg.getCategory().contains("执行文书")){
                        secondLevelName = "强制执行中";
                    }
                }

                if(!Strings.isNullOrEmpty(secondLevelName)){
                    outMap.put("CaseStatus",trialRoundName+secondLevelName);
                    outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                    caseStatusList.add(outMap);
                    continue;
                }
                if(validCount == 0){
                    secondLevelName = "执行完毕,结案";
                }
                if(category == CaseCategoryEnum.CPWS) {
                    CaseListEntity cpws = (CaseListEntity) caseInfo.getCaseInfo();
                    boolean allEndFlag = true;
                    lp:for (NameAndKeyNoEntity defInfo : latestTrialRound.getDefendant()) {
                        if(!LawResultEnum.ZHI_XING_WAN_B.getCode().equals(defInfo.getLawsuitResult())){
                            allEndFlag = false;
                            break lp;
                        }
                    }
                    if(allEndFlag){
                        secondLevelName = "执行完毕,结案";
                    }
                }

                if(!Strings.isNullOrEmpty(secondLevelName)){
                    outMap.put("CaseStatus",trialRoundName+secondLevelName);
                    outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                    caseStatusList.add(outMap);
                    continue;
                }

                //部分被告的失信/被执行人/限制高消费/限制出境数据变为历史，剩余部分出现终本案件
                if(hisCount>0 && validCount>0 && CollectionUtils.isNotEmpty(zbList)){
                    outMap.put("CaseStatus",trialRoundName+"终结执行,结案");
                    outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                    caseStatusList.add(outMap);
                    continue;
                }

                if(category == CaseCategoryEnum.CPWS) {
                    CaseListEntity cpws = (CaseListEntity) caseInfo.getCaseInfo();
                    if(cpws.getCaseType().contains(CaseDocTypeEnum.ADJUDICATE.getType())){
                        if(cpws.getResult().contains("不予执行")){
                            outMap.put("CaseStatus",trialRoundName+"不予执行,结案");
                            outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                            caseStatusList.add(outMap);
                            continue;
                        }
                    }
                    if(allJudgeResultTags.contains(LawResultEnum.SU_SONG_ZHONG_ZHI.getCode())){
                        outMap.put("CaseStatus",trialRoundName+"诉讼中止");
                        outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                        caseStatusList.add(outMap);
                        continue;
                    }
                }
            }

//            if(trialRoundName.contains("破产")){
//                outMap.put("CaseStatus",trialRoundName+"破产受理中");
//                outputEntity.setCaseStatus(outMap.get("CaseStatus"));
//                caseStatusList.add(outMap);
//                continue;
//            }

        }

        System.out.println(JSON.toJSONString(caseStatusList));

        return "";
    }

    static List<BaseCaseOutEntity> buildAllOutput( InfoListEntity latestTrialRound ){
        List<BaseCaseOutEntity> allOutList = new ArrayList<>();
        allOutList.addAll(genNoNullList(latestTrialRound.getSxList()));
        allOutList.addAll(genNoNullList(latestTrialRound.getZxList()));
        allOutList.addAll(genNoNullList(latestTrialRound.getXgList()));
        allOutList.addAll(genNoNullList(latestTrialRound.getCaseList()));
        allOutList.addAll(genNoNullList(latestTrialRound.getLianList()));
        allOutList.addAll(genNoNullList(latestTrialRound.getFyggList()));
        allOutList.addAll(genNoNullList(latestTrialRound.getKtggList()));
        allOutList.addAll(genNoNullList(latestTrialRound.getSdggList()));
        allOutList.addAll(genNoNullList(latestTrialRound.getPcczList()));
        allOutList.addAll(genNoNullList(latestTrialRound.getGqdjList()));
        allOutList.addAll(genNoNullList(latestTrialRound.getXjpgList()));
        allOutList.addAll(genNoNullList(latestTrialRound.getZbList()));
        allOutList.addAll(genNoNullList(latestTrialRound.getSfpmList()));
        allOutList.addAll(genNoNullList(latestTrialRound.getSqtjList()));
        allOutList.addAll(genNoNullList(latestTrialRound.getXzcjList()));
        allOutList.addAll(genNoNullList(latestTrialRound.getXdpgjgList()));

        return allOutList;
    }

    static List genNoNullList(List<? extends BaseCaseOutEntity> list){
        if(list == null){
            return new ArrayList();
        }
        return list;
    }

    static CaseTypeMappingEntity convert(BaseCaseOutEntity baseCase){

        if(baseCase instanceof SXListEntity){
            return new CaseTypeMappingEntity(CaseCategoryEnum.SX,baseCase);
        }
        if(baseCase instanceof ZXListEntity){
            return new CaseTypeMappingEntity(CaseCategoryEnum.ZX,baseCase);
        }
        if(baseCase instanceof XGListEntity){
            return new CaseTypeMappingEntity(CaseCategoryEnum.XG,baseCase);
        }
        if(baseCase instanceof CaseListEntity){
            return new CaseTypeMappingEntity(CaseCategoryEnum.CPWS,baseCase);
        }
        if(baseCase instanceof LianListEntity){
            return new CaseTypeMappingEntity(CaseCategoryEnum.LA,baseCase);
        }
        if(baseCase instanceof FyggListEntity){
            return new CaseTypeMappingEntity(CaseCategoryEnum.FYGG,baseCase);
        }
        if(baseCase instanceof KtggListEntity){
            return new CaseTypeMappingEntity(CaseCategoryEnum.KTGG,baseCase);
        }
        if(baseCase instanceof SdggListEntity){
            return new CaseTypeMappingEntity(CaseCategoryEnum.SDGG,baseCase);
        }
        if(baseCase instanceof PcczListEntity){
            return new CaseTypeMappingEntity(CaseCategoryEnum.PCCZ,baseCase);
        }
        if(baseCase instanceof GqdjListEntity){
            return new CaseTypeMappingEntity(CaseCategoryEnum.GQDJ,baseCase);
        }
        if(baseCase instanceof XjpgListEntity){
            return new CaseTypeMappingEntity(CaseCategoryEnum.XJPG,baseCase);
        }
        if(baseCase instanceof ZbListEntity){
            return new CaseTypeMappingEntity(CaseCategoryEnum.ZB,baseCase);
        }
        if(baseCase instanceof SFPMListEntity){
            return new CaseTypeMappingEntity(CaseCategoryEnum.SFPM,baseCase);
        }
        if(baseCase instanceof SQTJListEntity){
            return new CaseTypeMappingEntity(CaseCategoryEnum.SQTJ,baseCase);
        }
        if(baseCase instanceof XZCJListEntity){
            return new CaseTypeMappingEntity(CaseCategoryEnum.XZCJ,baseCase);
        }
        if(baseCase instanceof XDPGJGListEntity){
            return new CaseTypeMappingEntity(CaseCategoryEnum.XDPGJG,baseCase);
        }


        return null;
    }

}
@Data
@AllArgsConstructor
@NoArgsConstructor
class CaseTypeMappingEntity{
    private CaseCategoryEnum caseType;
    private BaseCaseOutEntity caseInfo;


}
