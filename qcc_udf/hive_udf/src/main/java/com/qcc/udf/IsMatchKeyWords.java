package com.qcc.udf;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;

public class IsMatchKeyWords extends UDF {
//    public static void main(String[] args) {
//        String Source = "企查查是一款企业信用查询工具，旨在为用户提供快速查询企业工商信息、法院判决信息、关联企业信息、法律诉讼、失信信息、被执行人信息、知识产权信息、公司新闻、企业年报等服务。 公司总部位于苏州2.5产业园，依赖超强研发实力，公司拥有大数据挖掘，数据建模，行业标准定义和可视化分析技术，同时公司在北京、上海建立了研发团队。公司核心团队成员有十多年互联网专业知识，通过在实践中不停创新，持续迭代，为顾客提供最优质的服务。我们的口号是: 缔造有远见的商业传奇! ";
//        boolean ifl = IsMatchKeyWords.evaluate(Source, "关联企业信息&", 0);
//    }

    public static boolean evaluate(String source, String keyWords, int isAllMatch) {
        boolean iFlag = false;
        if (StringUtils.isEmpty(source) || StringUtils.isEmpty(keyWords)) {
            return iFlag;
        }
        String[] groupArray = keyWords.split("#");
        if (groupArray == null || groupArray.length == 0) {
            return iFlag;
        }
        for (String group : groupArray) {
            String[] keyArray = group.split("&");
            if (keyArray == null || keyArray.length == 0) {
                continue;
            }
            int num = 0;
            boolean isMissed = false;
            for (String key : keyArray) {
                if (isAllMatch == 1) {
                    String[] sourceArr = source.split(",");
                    if (Arrays.asList(sourceArr).contains(key)) {
                        num++;
                    } else {
                        isMissed = true;
                        break;
                    }
                } else {
                    if (source.contains(key)) {
                        num++;
                    } else {
                        isMissed = true;
                        break;
                    }
                }
            }
            if (isMissed) {
                continue;
            }
            if (num == keyArray.length) {
                iFlag = true;
                break;
            }
        }
        return iFlag;
    }
}