package com.qcc.udf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentLengthException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDTF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;

import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用于解析企业套餐权益时间段
 */
public class BusinessOrderRightsReferenceUDTF extends GenericUDTF {


    static DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public StructObjectInspector initialize(StructObjectInspector argOIs) throws UDFArgumentException {
        if (argOIs.getAllStructFieldRefs().size() != 1) {
            throw new UDFArgumentLengthException("ExplodeRiskMap takes only one argument");
        }
        // 输出
        List<String> fieldNames = new ArrayList<String>(3);
        List<ObjectInspector> fieldOIs = new ArrayList<ObjectInspector>(3);
        fieldNames.add("order_code");
        fieldNames.add("rights_start_time");
        fieldNames.add("rights_end_time");
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        return ObjectInspectorFactory.getStandardStructObjectInspector(fieldNames, fieldOIs);
    }

    @Override
    public void process(Object[] objects) throws HiveException {
        JSONArray orders = JSONArray.parseArray(objects[0].toString());

        ArrayList<JSONObject> orderRight = new ArrayList<JSONObject>();

        //按照pay_time排序
        List<Object> collect = orders.stream().sorted(Comparator.comparing(temp -> ((JSONObject) temp).getTimestamp("pay_time"))).collect(Collectors.toList());



        // 企业套餐权益与用户权益不一致 上一笔订单会被下一笔订单中断
        if (collect.size() > 0) {
            LocalDateTime nextRightStartTime = null;
            for (int i = 0; i < collect.size(); i++) {
                JSONObject order = (JSONObject) collect.get(i);
                JSONObject right = new JSONObject();
                String orderCode = order.getString("order_code");
                String payTime = order.getString("pay_time");
                String endTime = order.getString("end_time");
                Integer validPeriodMonths = order.getInteger("valid_period_months");
                LocalDateTime payLocalDateTime = LocalDateTime.parse(payTime, dateTimeFormatter);
                LocalDateTime endLocalDateTime = LocalDateTime.parse(endTime, dateTimeFormatter);
                //获得下一笔订单的开始时间
                nextRightStartTime = i + 1 == collect.size() ? null : ((JSONObject) collect.get(i+1)).getTimestamp("pay_time").toLocalDateTime();

                if (nextRightStartTime == null){
                    right.put("order_code", orderCode);
                    right.put("rights_start_time", payTime);
                    right.put("rights_end_time", endTime);
                    orderRight.add(right);
                }else if(endLocalDateTime.isBefore(nextRightStartTime)){
                    right.put("order_code", orderCode);
                    right.put("rights_start_time", payTime);
                    right.put("rights_end_time", endTime);
                    orderRight.add(right);
                }else {
                    right.put("order_code", orderCode);
                    right.put("rights_start_time", payTime);
                    right.put("rights_end_time", dateTimeFormatter.format(nextRightStartTime));
                    orderRight.add(right);
                }
            }
        }
        System.out.println(orderRight);

        orderRight.forEach(temp -> {
            JSONObject json = (JSONObject) temp;
            String[] strs = new String[3];
            strs[0] = json.getString("order_code");
            strs[1] = json.getString("rights_start_time");
            strs[2] = json.getString("rights_end_time");
            try {
                if (strs[0] != null){
                    forward(strs);
                }

            } catch (HiveException e) {
                e.printStackTrace();
            }
        });
    }

    @Override
    public void close() throws HiveException {

    }

    /**
     * 静态方法，用于解析改版后的用户权益时间段
     */
    public static void main(String[] args) throws HiveException {
        Object[] item = new Object[1];
        item[0] = "[{\"order_code\":\"1663796435144101\",\"business_type\":\"VIP\",\"pay_time\":\"2022-09-22 05:44:22\",\"end_time\":\"2025-09-22 05:44:22\",\"valid_period_months\":36},{\"order_code\":\"1663797909944635\",\"business_type\":\"VIP\",\"pay_time\":\"2022-09-22 06:05:41\",\"end_time\":\"2025-09-22 06:05:41\",\"valid_period_months\":36},{\"order_code\":\"1663800533787236\",\"business_type\":\"VIP\",\"pay_time\":\"2022-09-22 06:49:30\",\"end_time\":\"2025-09-22 06:49:30\",\"valid_period_months\":36},{\"order_code\":\"1663901769781605\",\"business_type\":\"VIP\",\"pay_time\":\"2022-09-23 10:56:29\",\"end_time\":\"2025-09-23 10:56:29\",\"valid_period_months\":36},{\"order_code\":\"1663907553945137\",\"business_type\":\"VIP\",\"pay_time\":\"2022-09-23 12:32:53\",\"end_time\":\"2025-09-23 12:32:53\",\"valid_period_months\":36},{\"order_code\":\"1663909268211160\",\"business_type\":\"VIP\",\"pay_time\":\"2022-09-23 13:01:24\",\"end_time\":\"2025-09-23 13:01:24\",\"valid_period_months\":36},{\"order_code\":\"1663913568275368\",\"business_type\":\"VIP\",\"pay_time\":\"2022-09-23 14:13:26\",\"end_time\":\"2025-09-23 14:13:26\",\"valid_period_months\":36},{\"order_code\":\"1663913828627561\",\"business_type\":\"VIP\",\"pay_time\":\"2022-09-23 14:17:21\",\"end_time\":\"2025-09-23 14:17:21\",\"valid_period_months\":36},{\"order_code\":\"1663915944701752\",\"business_type\":\"VIP\",\"pay_time\":\"2022-09-23 15:07:55\",\"end_time\":\"2025-09-23 15:07:55\",\"valid_period_months\":36},{\"order_code\":\"1663927893380558\",\"business_type\":\"VIP\",\"pay_time\":\"2022-09-23 18:12:21\",\"end_time\":\"2025-09-23 18:12:21\",\"valid_period_months\":36},{\"order_code\":\"1663928045288996\",\"business_type\":\"VIP\",\"pay_time\":\"2022-09-23 18:14:16\",\"end_time\":\"2025-09-23 18:14:16\",\"valid_period_months\":36},{\"order_code\":\"1663928191455028\",\"business_type\":\"VIP\",\"pay_time\":\"2022-09-23 18:16:44\",\"end_time\":\"2025-09-23 18:16:44\",\"valid_period_months\":36},{\"order_code\":\"1663928301704655\",\"business_type\":\"VIP\",\"pay_time\":\"2022-09-23 18:18:34\",\"end_time\":\"2025-09-23 18:18:34\",\"valid_period_months\":36},{\"order_code\":\"1663975800538011\",\"business_type\":\"VIP\",\"pay_time\":\"2022-09-24 07:30:20\",\"end_time\":\"2025-09-24 07:30:20\",\"valid_period_months\":36},{\"order_code\":\"1664094897173259\",\"business_type\":\"VIP\",\"pay_time\":\"2022-09-25 16:35:22\",\"end_time\":\"2025-09-25 16:35:22\",\"valid_period_months\":36},{\"order_code\":\"1664165183284673\",\"business_type\":\"VIP\",\"pay_time\":\"2022-09-26 12:06:48\",\"end_time\":\"2025-09-26 12:06:48\",\"valid_period_months\":36}]";
        BusinessOrderRightsReferenceUDTF udf = new BusinessOrderRightsReferenceUDTF();
        udf.process(item);
    }
}
