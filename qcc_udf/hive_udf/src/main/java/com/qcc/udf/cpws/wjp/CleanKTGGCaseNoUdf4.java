package com.qcc.udf.cpws.wjp;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.ReUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class CleanKTGGCaseNoUdf4 extends UDF {
    public static String evaluate(String caserole, String spiderCaseno) {
        try {
            if (StringUtils.isEmpty(caserole)) {
                return "";
            }
            JSONObject caseNoList = JSON.parseObject(caserole);
            String caseno = "";
            if (caseNoList.containsKey("Result")) {
                caseno = caseNoList.getJSONObject("Result").getString("Caseno");
            }
            if (StringUtils.isNotEmpty(spiderCaseno)) {
                caseno = cleanStr(spiderCaseno);
            }
            return caseNoClean(cleanCaseNo(caseno),0,"1");

            // 示例数据对比
        } catch (Exception e) {
            return "";
        }


    }

    public static String cleanStr(String str) {
        return CommonV3Util.full2Half(str).replaceAll(" |　|\r|\n|\r|\n|null|NULL|NaN", "");
    }


    public static String cleanCaseNo(String caseNo) {
        //1.1
        caseNo = StringUtils.isBlank(caseNo) ? "" : caseNo;
        caseNo = caseNo.replaceAll(" |　|\n|\r|null|NULL|NaN|:|：|案号|“|”|‘|’", "");

        //1.2
        if (caseNo.contains("-") || caseNo.contains("之")) {
            Pattern regex1 = Pattern.compile("号(?=[\\-|之][^0-9零一二三四五六七八九十，\\,、])");
            Matcher matcher = regex1.matcher(caseNo);
            if (matcher.find()) {
                caseNo = caseNo.substring(0, matcher.start() + 1);
            }
        } else {
            Pattern regex1 = Pattern.compile("号(?=[^0-9零一二三四五六七八九十，\\,、])");
            Matcher matcher = regex1.matcher(caseNo);
            if (matcher.find()) {
                caseNo = caseNo.substring(0, matcher.start() + 1);
            }
        }

        caseNo = caseNo.replace("((", "(")
                .replace("))", ")")
                .replace("（（", "（")
                .replace("））", "）");
        //1.4
        caseNo = caseNo.replaceAll("(,|，)(?=民|刑|行)", "");


        //2
        if (!ReUtil.isMatch("^(（|\\()?(19|20)(.*?)$", caseNo)) {
            caseNo = "";
        }

        //3
        if (!ReUtil.isMatch("^(（|\\()?(\\d{4})(）|\\))?([\\u4e00-\\u9fa5\\d,-，、()]+)?(号|之|-)?([0-9|零一二三四五六七八九十]+)?$", caseNo)) {
            caseNo = "";
        }
        //4
        caseNo = caseNo.replaceAll("\\[|【|〔|﹝|<", "(");
        caseNo = caseNo.replaceAll("\\]|】|〕|﹞|>", ")");
        caseNo = caseNo.replace("(", "（").replace(")", "）");

        //无中文置空
        if (!Validator.hasChinese(caseNo)) {
            caseNo = "";
        }

        //5
        if (StringUtils.isNotBlank(caseNo) && !caseNo.contains("号")) {
            caseNo = caseNo + "号";
        }
        // 特例去除号和号中间的
        if (!caseNo.contains("之") && !caseNo.contains("号-")) {
            caseNo = caseNo.replaceAll("号[^0-9]+号$", "号");
        }

        //异常值剔除
        if (caseNo.contains("在")) {
            caseNo = caseNo.substring(0, caseNo.indexOf("在"));
            //无中文置空
            if (!Validator.hasChinese(caseNo)) {
                caseNo = "";
            }
        }
        //异常值剔除
        if (caseNo.endsWith("号")) {
            String tep = caseNo.substring(0, caseNo.lastIndexOf("号"));
            if (!Validator.hasChinese(tep)) {
                caseNo = "";
            }
        }
        //剔除典型异常日期 eg 2024-7-16,（2024）甘01民终4864号
        Matcher matcher = REGEXP_DATE.matcher(Optional.ofNullable(caseNo).orElse(StringUtils.EMPTY));
        if (matcher.find()) {
            String group = matcher.group();
            caseNo = caseNo.replace(group, StringUtils.EMPTY);
        }
        //020108
        caseNo = caseNo.replaceAll("(?<=字)[|'‘’\"](?=第)", "");
        //020109
        if (caseNo.matches("^[\u4e00-\u9fa5]+$")) {
            return "";
        }
        return caseNo;
    }
    public static String caseNoClean(String caseNo,Integer type,String spiderId){
        String result = caseNo;
        if (StringUtils.isNotEmpty(caseNo)){
            // 020101 案号中含有空格、换行、中英文冒号、关键词【案号】，该案号该内容需要去除
            if(result.contains(" ") || result.contains(":") || result.contains("：") || result.contains("案号") || result.contains("案 号") || result.contains("\n") || result.contains("\t") ) {
                result = result.replaceAll("( )", "");
                result = result.replaceAll("( )|(:)|(：)|(案号)|(\n)|(\t)|(案 号)", "");
             }
            // 020103 案号中有冗余括号，该案号去除冗余括号
            if(result.contains("((") || result.contains("))") || result.contains("（（") || result.contains("））")) {
                result = result.replace("((", "(").replace("))", ")").replace("（（", "（").replace("））", "）");
                result = result.replace("((", "(").replace("))", ")").replace("（（", "（").replace("））", "）");
                result = result.replace("((", "(").replace("))", ")").replace("（（", "（").replace("））", "）");
             }
            // 020102 单个案号，案号关键词【号】后面非紧跟中英文数字|横杠+中英文数字|之+中英文数字，则去掉【号】后面的内容
            if (result.contains("号") && !result.endsWith("号") && !result.contains("号,")&&!result.contains("号，")&& !result.contains("、")){
                int idx = result.indexOf("号");
                String sub = result.substring(idx+1);
                Pattern pattern = Pattern.compile("^[0-9-之]");
                Matcher matcher = pattern.matcher(sub);
                if (!matcher.find()){
                    result = result.substring(0, idx + 1);
                 }
            }
            if (result.endsWith("号号")){
                int idx = result.indexOf("号");
                String sub = result.substring(idx+1);
                Pattern pattern = Pattern.compile("^[0-9-之]");
                Matcher matcher = pattern.matcher(sub);
                if (!matcher.find()){
                    result = result.substring(0, idx + 1);
                 }
            }

            // 020104 案号中有多余符号，去除民|刑|行前一位的中英文逗号
            if ((result.contains(",") || result.contains("，"))
                    && (result.contains(",民") || result.contains(",刑") || result.contains(",行") || result.contains("，民") || result.contains("，刑") || result.contains("，行"))){
                result = result.replace(",民", "民").replace(",刑", "刑").replace(",行", "行")
                        .replace("，民", "民").replace("，刑", "刑").replace("，行", "行");
             }

            //020107	案号解析重复
            if(StringUtils.isNotBlank(result) && (result.contains(",") || result.contains("，"))){
                String[] split = result.replace("（", "(").replace("）", ")").split(",|，");
                if(split!=null && split.length>1){
                    List<String> distinctCollect = Stream.of(split).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
                    if(distinctCollect.size() != split.length){
                        result = distinctCollect.stream().collect(Collectors.joining(","));
                     }
                }
            }
        }
        return result;
    }

    private static final Pattern REGEXP_DATE = Pattern.compile("\\b\\d{4}-(?:[1-9]|1[0-2])-(?:[1-9]|[12]\\d|3[01]),");


    public static void main(String[] args) {
        System.out.println(evaluate("{\"Input\":\"广东省东莞市第一人民法院 公告   （2023）粤1971民初16596号    丛雨欣： 本院在受理原告杨大勇诉被告丛雨欣民间借贷纠纷一案,因用法定的其他方式无法向你送达诉讼文书,依照《中华人民共和国民事诉讼法》第九十五条的规定,向你公告送达民事起诉状及证据副本,权利义务告知书,应诉通知书,举证通知书,审判人员通知书,开庭传票,廉政与作风评价卡,提供类似案例的提示,地址确认书,民事裁定书（转普）等材料。原告提出诉讼请求,请求判令：一,被告立即偿还借款本金10000元；二,被告立即偿还原告利息（以借款本金10000元为基数,按年化15.4%,自2022年7月9日起计算至被告清偿之日止,暂计至2023年3月15日为1024元）；三,被告承担本案的诉讼费。 本院于2023年6月25日作出（2023） 粤1971民初16596号民事裁定书,裁定本案转为普通程序。 自本公告发出之日起,经过三十日即视为送达。提出答辩的期限和举证期限分别为公告送达期满后的十五日内和三十日内。本院定于二○二三年八月三十一日十四时三十分在东莞市第一人民法院中堂人民法庭开庭审理此案,本案由审判员周玉婷一人适用普通程序独任审理。逾期不到庭将依法判决。 特此公告。                         二〇二三年六月二十五日。\",\"Result\":{\"Caseno\":\"（2023）粤1971民初16596号,（2023）粤1971民初16596号\",\"Casereason\":\"民间借贷纠纷\",\"Court\":\"广东省东莞市第一人民法院,东莞市第一人民法院\",\"CourtRoom\":\"中堂人民法庭\",\"Defendant\":\"丛雨欣\",\"IfAi\":1,\"Plaintiff\":\"杨大勇\",\"Preside\":\"\",\"PubltishDate\":\"二〇二三年六月二十五日\",\"ScheduleDate\":\"\",\"SessionTime\":\"二○二三年八月三十一日十四时三十分\"}}"
                , ""));
    }


}
