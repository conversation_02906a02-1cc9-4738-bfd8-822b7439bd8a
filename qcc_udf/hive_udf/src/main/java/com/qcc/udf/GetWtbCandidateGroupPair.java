package com.qcc.udf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.qcc.udf.model.KeyNoOrgInfo;
import com.qcc.udf.model.WtbCandiateGroupPair;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

/**
 * @Auther: daixuan
 * @Date: 2022/08/10
 * @Description: 此UDF函数
 */
public class GetWtbCandidateGroupPair extends UDF {
    public String evaluate(String source) throws ParseException {
        if (StringUtils.isBlank(source)) {
            return null;
        }
        List<KeyNoOrgInfo> infos = JSONArray.parseArray(source, KeyNoOrgInfo.class);
        List<WtbCandiateGroupPair> wtbCandidateGroupPairList = new ArrayList<>();
        for (int i = 0; i < infos.size(); i++) {
            for (int j = i + 1; j < infos.size(); j++) {
                WtbCandiateGroupPair pair = new WtbCandiateGroupPair();
                pair.setKey1(infos.get(i).getKeyNo());
                pair.setName1(infos.get(i).getName());
                pair.setKey2(infos.get(j).getKeyNo());
                pair.setName2(infos.get(j).getName());
                wtbCandidateGroupPairList.add(pair);
            }
        }
        return CollectionUtils.isNotEmpty(wtbCandidateGroupPairList) ? JSON.toJSONString(wtbCandidateGroupPairList) : "";
    }


    public static void main(String[] args) throws ParseException {

        GetWtbCandidateGroupPair s = new GetWtbCandidateGroupPair();
        String evaluate = s.evaluate("[{\"KeyNo\":\"gce7ee4fc1040abe77deedb88489c227\",\"Name\":\"社区中心\",\"Org\":4},{\"KeyNo\":\"0a3fd30587d84dbf02c81268616185ee\",\"Name\":\"青岛恒基伟泰建筑有限公司\",\"Org\":0}]");
        System.out.println(evaluate);
    }
}