package com.qcc.udf.kzz;

import com.qcc.udf.temp.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * 去除html元素
 *
 * <AUTHOR>
 * @date 2021/12/31
 */
public class ClearHtmlContent extends UDF {
    public static String evaluate(String content) {
        String result = "";
        try {
            if (StringUtils.isNotBlank(content)) {
                result = CommonUtil.cleanHtmlTag(content);
                result = result.replaceAll(" |　","");
            }

        } catch (Exception e) {

        }
        return result;
    }

//    public static void main(String[] args) {
//        String content = "安徽省六安经济技术开发区文教路 76 号";
//        String region = evaluate(content);
//        System.out.println(region);
//    }
}
