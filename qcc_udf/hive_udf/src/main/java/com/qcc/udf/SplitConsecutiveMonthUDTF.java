package com.qcc.udf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentLengthException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDTF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * 将连续时间按月切分
 */
public class SplitConsecutiveMonthUDTF extends GenericUDTF {

    static DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public StructObjectInspector initialize(StructObjectInspector argOIs) throws UDFArgumentException {
        if (argOIs.getAllStructFieldRefs().size() != 2) {
            throw new UDFArgumentLengthException("ExplodeRiskMap takes only two argument");
        }
        // 输出
        List<String> fieldNames = new ArrayList<String>(2);
        List<ObjectInspector> fieldOIs = new ArrayList<ObjectInspector>(2);
        fieldNames.add("launch_start_date");
        fieldNames.add("launch_end_date");
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        return ObjectInspectorFactory.getStandardStructObjectInspector(fieldNames, fieldOIs);
    }

    @Override
    public void process(Object[] objects) throws HiveException {
        JSONArray array = new JSONArray();
        String startTime = objects[0].toString();
        String endTime = objects[1].toString();

        LocalDate start = LocalDate.parse(startTime, dateTimeFormatter);
        LocalDate end = LocalDate.parse(endTime, dateTimeFormatter);

        while (start.isBefore(end) || start.isEqual(end)){
            JSONArray temp = new JSONArray();
            LocalDate firstDay = start.with(TemporalAdjusters.firstDayOfMonth());
            LocalDate lastDay = start.with(TemporalAdjusters.lastDayOfMonth());
            if (lastDay.isAfter(end)){
                temp.add(start.toString());
                temp.add(end.toString());
                array.add(temp);
                break;
            }
            temp.add(start.toString());
            temp.add(lastDay.toString());
            array.add(temp);

            start = firstDay.plusMonths(1);

        }
        System.out.println(array);

        array.forEach( k ->{
            JSONArray temp = (JSONArray) k;
            Object[] objects1 = temp.toArray();
            try {
                forward(objects1);
            } catch (HiveException e) {
                e.printStackTrace();
            }
        });

    }

    @Override
    public void close() throws HiveException {

    }


    /**
     *
     * @param args  开始日期 结束日期
     * @throws HiveException
     */
    public static void main(String[] args) throws HiveException {
        Object[] item = new Object[2];
        item[0] = "2022-01-08";
        item[1] = "2022-01-31";
        SplitConsecutiveMonthUDTF splitConsecutiveMonthUDTF = new SplitConsecutiveMonthUDTF();
        splitConsecutiveMonthUDTF.process(item);
    }


}
