package com.qcc.udf.model;

import lombok.Data;

import java.util.LinkedList;
import java.util.List;

@Data
public class CaseSearchMongoXjpgListEntity {
    private String id = "";
    private List<CaseSearchMongoNameAndKeyNoEntity> nameAndKeyNo = new LinkedList<>();
    private String category = "";
    private String target = "";
    private Long publishDate = 0L;
    private Integer isValid = 0;
    private List<CaseSearchMongoNameAndKeyNoEntity> targetNameAndKeyNo = new LinkedList<>();
}
