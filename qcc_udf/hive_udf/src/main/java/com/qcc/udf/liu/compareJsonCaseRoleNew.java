package com.qcc.udf.liu;

import com.alibaba.fastjson.JSONArray;
import com.qcc.udf.cpws.CommonUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

public class compareJsonCaseRoleNew extends UDF {

    public static String evaluate(String oldInfo, String newInfo)  throws Exception{
        List<entityInfoCaseRoleNew> oldList = new LinkedList<>();
        if (StringUtils.isNotEmpty(oldInfo)){
            oldList = JSONArray.parseArray(oldInfo, entityInfoCaseRoleNew.class);
        }

        List<entityInfoCaseRoleNew> newList = new LinkedList<>();
        if (StringUtils.isNotEmpty(newInfo)){
            newList = JSONArray.parseArray(newInfo, entityInfoCaseRoleNew.class);
        }

        if (oldList.size() != newList.size()){
            return "1";
        }else{
            List<String> oldSet = new LinkedList<>();
            for (entityInfoCaseRoleNew item : oldList){
                String n = item.getN() == null ? "" : item.getN();
                String p = item.getP() == null ? "" : item.getP();
                String showName = item.getShowName() == null ? "" : item.getShowName();
                String r = item.getR() == null ? "" : item.getR();
                String o = item.getO() == null ? "" : item.getO();
                String l = item.getL() == null ? "" : item.getL();
                String lyInfo = "";
                List<LyInfo1> lyList = item.getLY();
                if (lyList != null){
                    List<String> idList = new LinkedList<>();
                    for (LyInfo1 ly : lyList){
                        String n1 = ly.getN() == null ? "" : ly.getN();
                        String p1 = ly.getP() == null ? "" : ly.getP();
                        String o1 = ly.getO() == null ? "" : ly.getO();

                        idList.add(CommonUtil.full2Half(n1.concat(p1).concat(o1)));

                    }
                    Collections.sort(idList);
                    lyInfo = StringUtils.join(idList, ",");
                }
                oldSet.add(CommonUtil.full2Half(n.concat(p).concat(showName).concat(r).concat(o).concat(l).concat(lyInfo)));
            }


            List<String> newSet = new LinkedList<>();
            for (entityInfoCaseRoleNew item : newList){
                String n = item.getN() == null ? "" : item.getN();
                String p = item.getP() == null ? "" : item.getP();
                String showName = item.getShowName() == null ? "" : item.getShowName();
                String r = item.getR() == null ? "" : item.getR();
                String o = item.getO() == null ? "" : item.getO();
                String l = item.getL() == null ? "" : item.getL();
                String lyInfo = "";
                List<LyInfo1> lyList = item.getLY();
                if (lyList != null){
                    List<String> idList = new LinkedList<>();
                    for (LyInfo1 ly : lyList){
                        String n1 = ly.getN() == null ? "" : ly.getN();
                        String p1 = ly.getP() == null ? "" : ly.getP();
                        String o1 = ly.getO() == null ? "" : ly.getO();

                        idList.add(CommonUtil.full2Half(n1.concat(p1).concat(o1)));

                    }
                    Collections.sort(idList);
                    lyInfo = StringUtils.join(idList, ",");
                }
                newSet.add(CommonUtil.full2Half(n.concat(p).concat(showName).concat(r).concat(o).concat(l).concat(lyInfo)));
            }

            boolean flag = false;
            for (String old : oldSet){
                if (!newSet.contains(old)){
                    flag = true;
                }
            }
            for (String old : newSet){
                if (!oldSet.contains(old)){
                    flag = true;
                }
            }

            if (flag){
                return "1";
            }else{
                return "";
            }
        }
    }

    public static void main(String[] args) {
        try {
            System.out.println(evaluate("[{\"P\":\"邱华海\",\"R\":\"原告\",\"L\":0,\"LY\":[],\"ShowName\":\"邱**\",\"N\":\"\",\"O\":-2},{\"P\":\"河南朝野律师事务所\",\"R\":\"代理律师\",\"L\":1,\"LY\":[{\"P\":\"刘凯\",\"N\":\"22580ea005727ccd2b0359fb3d760a37\",\"O\":-2}],\"ShowName\":\"河南朝野律师事务所\",\"N\":\"w7fdef7f2b45d48830cd966ce1ce140f\",\"O\":4},{\"P\":\"孙钻伟\",\"R\":\"被告\",\"L\":0,\"LY\":[],\"ShowName\":\"孙**\",\"N\":\"\",\"O\":-2},{\"P\":\"河南朝野律师事务所\",\"R\":\"代理律师\",\"L\":1,\"LY\":[{\"P\":\"史柯\",\"N\":\"943314e6a8774a6410a4813c0c3666f4\",\"O\":-2}],\"ShowName\":\"河南朝野律师事务所\",\"N\":\"w7fdef7f2b45d48830cd966ce1ce140f\",\"O\":4}]",
                    "[{\"P\":\"邱华海\",\"R\":\"原告\",\"L\":0,\"LY\":[],\"ShowName\":\"邱**\",\"N\":\"\",\"O\":-2},{\"P\":\"河南朝野律师事务所\",\"R\":\"代理律师\",\"L\":1,\"LY\":[{\"P\":\"刘凯1\",\"N\":\"22580ea005727ccd2b0359fb3d760a37\",\"O\":-2}],\"ShowName\":\"河南朝野律师事务所\",\"N\":\"w7fdef7f2b45d48830cd966ce1ce140f\",\"O\":4},{\"P\":\"孙钻伟\",\"R\":\"被告\",\"L\":0,\"LY\":[],\"ShowName\":\"孙**\",\"N\":\"\",\"O\":-2},{\"P\":\"河南朝野律师事务所\",\"R\":\"代理律师\",\"L\":1,\"LY\":[{\"P\":\"史柯\",\"N\":\"943314e6a8774a6410a4813c0c3666f4\",\"O\":-2}],\"ShowName\":\"河南朝野律师事务所\",\"N\":\"w7fdef7f2b45d48830cd966ce1ce140f\",\"O\":4}]"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

@Data
class entityInfoCaseRoleNew{
    private String P;
    private String R;
    private String ShowName;
    private String N;
    private String O;
    private String L;
    private List<LyInfo1> LY;
}

@Data
class LyInfo1{
    private String P;
    private String N;
    private String O;
}
