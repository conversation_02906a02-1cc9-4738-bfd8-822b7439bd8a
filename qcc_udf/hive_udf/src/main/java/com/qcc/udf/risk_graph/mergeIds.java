package com.qcc.udf.risk_graph;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

/**
 * @Auther: liulh
 * @Date: 2020/6/11 17:54
 * @Description:
 */
public class mergeIds extends UDF {
    public static String evaluate(String info) {
        Set<String> idSet = new LinkedHashSet<>();
        if (StringUtils.isNotEmpty(info)){
            String[] infos = info.split(",");
            for (String str : infos){
                if (StringUtils.isNotEmpty(str)){
                    idSet.add(str);
                }
            }
        }


        return String.join(",", idSet);
    }

    public static void main(String[] args) {
        System.out.println(evaluate("4401,440106,4401,440112"));
    }

}
