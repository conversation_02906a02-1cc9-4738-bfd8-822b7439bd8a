package com.qcc.udf.risk_graph;

import cn.hutool.core.util.ReUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.qcc.udf.namekeyno.NameUtil;
import com.qcc.udf.temp.MD5Util;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2021/3/4
 */
public class RemoveNumberLongUDF extends UDF {
    public String evaluate(String detailinfo) {
        if (StringUtils.isBlank(detailinfo)){
            return  detailinfo;
        }
        return ReUtil.replaceAll(detailinfo,"\\{\"\\$numberLong\"\\:\"(\\d+)\"\\}","$1");
    }

    public static void main(String[] args) {
        String content ="{\"No\":\"9cb99e5c83fbc66e6115307a311bea0c\",\"PublishDate\":{\"$numberLong\":\"0\"},\"AddDate\":{\"$numberLong\":\"1562120888\"},\"RemoveDate\":{\"$numberLong\":\"0\"},\"AddReason\":\"\",\"RemoveDecisionOffice\":\"\",\"AnnualReportYear\":null,\"RomoveReason\":\"\",\"DecisionOffice\":\"黟县市场监督管理局\"}";
        RemoveNumberLongUDF removeNumberLongUDF = new RemoveNumberLongUDF();
        String output = removeNumberLongUDF.evaluate(content);
        System.out.println(output);
    }

}