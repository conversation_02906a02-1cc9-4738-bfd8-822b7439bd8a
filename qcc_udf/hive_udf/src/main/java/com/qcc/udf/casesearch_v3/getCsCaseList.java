package com.qcc.udf.casesearch_v3;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.court_notice.anUtils.MD5Util;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

/**
 * <AUTHOR>
 */
public class getCsCaseList extends UDF {
    public static String evaluate(String id, String param) {
        JSONArray result = new JSONArray();

        if (StringUtils.isNotEmpty(param)){
            // 获取被执行/终本的所有数据
            JSONArray array = JSONObject.parseObject(param).getJSONArray("InfoList");
            if (array != null && !array.isEmpty()){
                Iterator<Object> it = array.iterator();
                while (it.hasNext()){
                    JSONObject json = (JSONObject) it.next();
                    JSONArray caseArray = json.getJSONArray("CaseList");

                    Iterator<Object> subIt = caseArray.iterator();
                    while (subIt.hasNext()){
                        JSONObject caseJson = (JSONObject) subIt.next();
                        String caseId = caseJson.getString("Id");
                        int validFlag = caseJson.getInteger("IsValid");
                        long sortDate = caseJson.getLong("JudgeDate");

                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("Id", MD5Util.ecodeByMD5(id.concat(caseId)));
                        jsonObject.put("CsId", id);
                        jsonObject.put("CaseId", caseId);
                        jsonObject.put("IsValid", validFlag);
                        jsonObject.put("SortDate", sortDate);

                        result.add(jsonObject);
                    }
                }
            }
        }

        return result.toString();
    }

    public static void main(String[] args) {
        System.out.println(evaluate("id123", "{\"KtggCnt\":1,\"LastestDateType\":\"民事一审|开庭时间\",\"XjpgCnt\":0,\"ZxCnt\":0,\"CfgsCnt\":0,\"LastestDate\":1610937060,\"AmtInfo\":{},\"EarliestDate\":1610899200,\"Source\":\"OT\",\"AnnoCnt\":1,\"EarliestDateType\":\"民事一审|裁定日期\",\"XzcfCnt\":0,\"CompanyKeywords\":\"周志君,潘光远\",\"AnNoList\":\"（2021）黔0121民初106号\",\"GqdjCnt\":0,\"XgCnt\":0,\"Tags\":\"4,11\",\"FyggCnt\":0,\"ZbCnt\":0,\"LatestTrialRound\":\"民事一审\",\"CfdfCnt\":0,\"CaseName\":\"周志君与潘光远运输合同纠纷的案件\",\"CfxyCnt\":0,\"SxCnt\":0,\"Province\":\"GZ\",\"GroupId\":\"498235d8a42203bc68c64aca92e5f3f9\",\"LianCnt\":0,\"CaseCnt\":1,\"HbcfCnt\":0,\"PcczCnt\":0,\"Type\":1,\"CaseType\":\"民事案件\",\"ProcuratorateList\":\"\",\"CaseRole\":\"[{\\\"D\\\":\\\"一审原告\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2,\\\"P\\\":\\\"周志君\\\",\\\"R\\\":\\\"原告\\\",\\\"RL\\\":[{\\\"LR\\\":\\\"5\\\",\\\"R\\\":\\\"原告\\\",\\\"T\\\":\\\"一审\\\"}]},{\\\"D\\\":\\\"一审被告\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2,\\\"P\\\":\\\"潘光远\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"LR\\\":\\\"6\\\",\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"}]}]\",\"CaseReason\":\"运输合同纠纷\",\"CourtList\":\"贵州省贵阳市开阳县人民法院\",\"Id\":\"9b345d5dd79b29e025ae32fb18cc1083\",\"InfoList\":[{\"Defendant\":[{\"KeyNo\":\"\",\"Role\":\"被告\",\"Org\":-2,\"LR\":\"6\",\"Name\":\"潘光远\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"贵州省贵阳市开阳县人民法院\",\"LatestTimestamp\":1610937060,\"ZxList\":[],\"XzcffList\":[],\"CfdfList\":[],\"HbcfList\":[],\"CaseList\":[{\"CaseType\":\"民事裁定书\",\"JudgeDate\":1610899200,\"Amt\":\"\",\"Id\":\"b99e882d82c0c568711441a7a0da0e210\",\"ResultType\":\"裁定结果\",\"DocType\":\"裁定日期\",\"IsValid\":1,\"Result\":\"准许原告周志君撤回起诉。\",\"ShieldCaseFlag\":0}],\"TrialRound\":\"民事一审\",\"Prosecutor\":[{\"KeyNo\":\"\",\"Role\":\"原告\",\"Org\":-2,\"LR\":\"5\",\"Name\":\"周志君\"}],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2021）黔0121民初106号\",\"CaseType\":\"民事案件\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"运输合同纠纷\",\"KtggList\":[{\"ExecuteUnite\":\"第三人民法庭（双流镇）\",\"OpenDate\":1610937060,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"周志君\"},{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"潘光远\"}],\"Id\":\"38c73c1f06618c3ac609cf12c52ca7f65\",\"IsValid\":1}],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]}],\"SdggCnt\":0} "));
    }
}
