package com.qcc.udf.cpws;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class ExtractCpwsPersonalInfoUDF extends UDF {
    // 起始字符包含在以下列表时保留该句
    private final static String[] filterHeadKeywords = new String[]{
            "再审申请人（一审原告二审上诉人）", "上诉人（原审原告原审被告）", "上诉人（原审原告、反诉被告）", "上诉人（原审被告、反诉原告）",
            "上诉人（原审被告原审原告）", "再审申请人（一审被告、二审上诉人）", "申请再审人（一审原告二审上诉人）", "复议申请人（利害关系人）",
            "上诉人（原审被告反诉原告）", "再审申请人（原审原告）", "再审申请人（原审被告）", "复议申请人（原申请执行人）", "附带民事诉讼原告",
            "再审申请人（一审被告二审上诉人）", "附带民事诉讼原告人", "原告（反诉被告）", "上诉人（一审被告）", "上诉人（原审被告）",
            "解除保全申请人", "上诉人（原审原告）", "申请人（仲裁申请人）", "上诉人（一审原告）", "上诉人（原审第三人）", "二审上诉人",
            "原告（被告）", "公诉机关", "申请执行人", "复议申请人", "赔偿请求人", "原审原告", "再审申请人", "上诉人",
            "申请人", "申诉人", "起诉人", "申报人", "原告人", "原告",

            "被申请人（一审被告二审被上诉人）", "被申请人（一审原告、二审被上诉人）", "被上诉人（一审被告）", "被告（反诉原告）",
            "被上诉人（一审原告）", "被申请人（原审被告）", "被上诉人（原审原告反诉被告）", "一审第三人", "被上诉人（原审原告）",
            "被申请人（原审原告）", "被申请人（原被执行人）", "被上诉人（原审第三人）", "被申请人（仲裁被申请人）", "被上诉人（原审被告）",
            "被上诉人", "原审第三人", "被告（原告）", "赔偿义务机关", "被申请执行人", "再审被申请人",
            "复议机关", "被告人", "被执行人", "被申诉人", "被申请人", "被起诉人", "被申报人", "原审被告",
            "被告一", "被告二", "第三人", "被告", "罪犯",

            "法定代表人", "负责人", "法人", "经营者",

            "委托诉讼代理人", "委托代理人", "原告委托代理人", "被告委托代理人", "二被告共同委托诉讼代理人"
    };

    //原告关键字
    private final static List<String> PlaintiffList = new ArrayList();
    //被告关键字
    private final static List<String> DefendantList = new ArrayList();
    // 法人关键字
    private final static List<String> DelegateList = new ArrayList();
    // 委托诉讼代理人关键字
    private final static List<String> AgentList = new ArrayList<>();

    static {
        String[] Plaintiffs = new String[]{
                "原告", "原告人", "申请人", "申诉人", "起诉人", "申报人", "上诉人", "原审原告", "原告（反诉被告）", "再审申请人",
                "上诉人（原审被告反诉原告）", "公诉机关", "再审申请人（一审被告二审上诉人）", "复议申请人（利害关系人）", "原告（被告）",
                "申请再审人（一审原告二审上诉人）", "上诉人（一审原告）", "附带民事诉讼原告人", "上诉人（原审第三人）", "二审上诉人",
                "再审申请人（原审被告）", "申请执行人", "复议申请人", "再审申请人（原审原告）", "赔偿请求人", "上诉人（原审原告原审被告）",
                "复议申请人（原申请执行人）", "上诉人（原审被告）", "解除保全申请人", "再审申请人（一审被告、二审上诉人）", "上诉人（一审被告）",
                "上诉人（原审原告）", "附带民事诉讼原告", "申请人（仲裁申请人）", "上诉人（原审被告原审原告）", "再审申请人（一审原告二审上诉人）",
                "上诉人（原审原告、反诉被告）", "上诉人（原审被告、反诉原告）"
        };
        for (String item : Plaintiffs) {
            PlaintiffList.add(item);
        }

        String[] Defendants = new String[]{
                "被告", "被告人", "被执行人", "被申诉人", "被申请人", "被起诉人", "被申报人", "原审被告", "被上诉人（一审原告）", "被申请人（原审被告）",
                "原审第三人", "被上诉人", "被上诉人（原审第三人）", "被告（原告）", "赔偿义务机关", "被申请人（原审原告）", "被申请人（原被执行人）",
                "复议机关", "被上诉人（原审原告反诉被告）", "一审第三人", "被上诉人（原审原告）", "被告（反诉原告）", "被上诉人（一审被告）", "罪犯",
                "被申请人（一审被告二审被上诉人）", "被告一", "被告二", "第三人", "被申请执行人", "再审被申请人", "被申请人（一审原告、二审被上诉人）",
                "被申请人（仲裁被申请人）", "被上诉人（原审被告）",
        };
        for (String item : Defendants) {
            DefendantList.add(item);
        }

        String[] Delegates = new String[]{
                "法定代表人", "负责人", "法人", "经营者","法定代表人（负责人）"
        };
        for (String item : Delegates) {
            DelegateList.add(item);
        }

        String[] Agents = new String[]{
                "委托诉讼代理人",
                "委托代理人",
                "原告委托代理人",
                "被告委托代理人",
                "二被告共同委托诉讼代理人","委托代理人（特别授权）"
        };
        for (String item : Agents) {
            AgentList.add(item);
        }
    }


    public   String evaluate(String content) {
        String result = "";
        try {
            if (StringUtils.isNotBlank(content)) {
                //先去HTML标签
                String convertContent = HtmlToTextUtil.convert(content);
                if (!content.contains("\r\n") && !content.contains("\n")) {
                    content = HtmlToTextUtil.convert2(content);
                } else {
                    content = convertContent;
                }
                List<String> itemList = Arrays.stream(content.split("[\n|\r\n]"))
                        .map(StringUtils::trim)
                        .filter(e -> e.length() <= 100)
                        .map(e -> filterSegmentByHeadKeyword(e))
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());

                List<CasePersonEntity> casePersonEntityList = new LinkedList<>();
                String litigantName = "";   // 存储身份为原告或被告角色的公司名（长度必须大于4，按顺序解析时下一个会覆盖上一个）
                String litigantRole = "";   // 存储身份为原告或被告角色的角色名
                for (String item : itemList) {
                    CasePersonResponseDto casePersonResponseDto = parseCasePersonEntityInfo(item, litigantName, litigantRole);
                    if (casePersonResponseDto.getCasePersonEntity() != null) {
                        casePersonEntityList.add(casePersonResponseDto.getCasePersonEntity());
                    }
                    if (StringUtils.isNotBlank(casePersonResponseDto.getLitigantName())) {
                        litigantName = casePersonResponseDto.getLitigantName();
                    }
                    if (StringUtils.isNotBlank(casePersonResponseDto.getLitigantRole())) {
                        litigantRole = casePersonResponseDto.getLitigantRole();
                    }
                }
                result = JSONObject.toJSONString(casePersonEntityList);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return result;
    }


    /**
     * 解析字符串信息中的涉案个体信息
     *
     * @param content
     * @param litigantName 当前的当事人信息
     * @param litigantRole 当前的当事人角色
     * @return
     */
    private static CasePersonResponseDto parseCasePersonEntityInfo(String content, String litigantName, String litigantRole) {
        CasePersonResponseDto casePersonResponseDto = new CasePersonResponseDto();
        try {
            if (StringUtils.isNotBlank(content)) {
                String[] splits = content.split("[，。]");
                if (splits != null && splits.length > 0) {
                    /**
                     * 思路：解析content，得到案件身份role与名称信息name
                     * 1 当role为原被告，且name大于4，则认定为一个公司
                     *      -> 将name赋值给litigantName, 将role赋值给litigantRole
                     * 2 当role为原被告，且name小于等于4，则认定为人名
                     *      -> 从content中提取年月日和地址信息
                     * 3 当role为代表人或者委托诉讼代理人，且name大于等于4，且content不包"含律师事务所"信息
                     *      -> 从content中提取年月日和地址信息，并关联上当前litigant变量中存储的信息
                     */
                    String headerInfo = splits[0];
                    RoleEntity roleEntity = getCaseRole(headerInfo);
                    if (roleEntity != null) {
                        String role = roleEntity.getRole();
                        String name = roleEntity.getName();
                        if (PlaintiffList.contains(role) || DefendantList.contains(role)) {
                            if (name.length() <= 4) {
                                // 提取个人信息
                                CasePersonEntity casePersonEntity = getCasePersonEntityFromContent(
                                        roleEntity, content, "", "");
                                casePersonResponseDto.setCasePersonEntity(casePersonEntity);
                            } else {
                                casePersonResponseDto.setLitigantName(name);
                                casePersonResponseDto.setLitigantRole(role);
                            }
                        } else if (DelegateList.contains(role)) {
                            CasePersonEntity casePersonEntity = getCasePersonEntityFromContent(
                                    roleEntity, content, litigantName, litigantRole);
                            casePersonResponseDto.setCasePersonEntity(casePersonEntity);
                        } else if (AgentList.contains(role) && !content.contains("律师") && !content.contains("律务") && !content.contains("律师事务所") && !content.contains("法律")) {
                            CasePersonEntity casePersonEntity = getCasePersonEntityFromContent(
                                    roleEntity, content, litigantName, litigantRole);
                            casePersonResponseDto.setCasePersonEntity(casePersonEntity);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return casePersonResponseDto;
    }

    private static CasePersonEntity getCasePersonEntityFromContent(
            RoleEntity roleEntity, String content, String litigantName, String litigantRole) {
        CasePersonEntity casePersonEntity = new CasePersonEntity();
        try {
            if (StringUtils.isNotBlank(content) && roleEntity != null) {
                casePersonEntity = new CasePersonEntity();
                // 提取个人名称
                casePersonEntity.setName(roleEntity.getName());

                // 提取涉案角色及关联的公司信息
                String roleName = "";
                if (StringUtils.isNotBlank(litigantRole) && StringUtils.isNotBlank(roleEntity.getRole())) {
                    roleName = (StringUtils.isNotBlank(litigantRole) ? (litigantRole + "-" + roleEntity.getRole()) : "");
                } else if (StringUtils.isBlank(litigantRole) && StringUtils.isNotBlank(roleEntity.getRole())) {
                    roleName = roleEntity.getRole();
                }
                casePersonEntity.setRole(roleName);
                casePersonEntity.setRelatedCompanyName(litigantName);

                // 提取个人出生年月日信息
                try {
                    Pattern birthDayPattern = Pattern.compile("(\\d{4}年\\d{1,2}月\\d{1,2})");
                    Matcher birthDayMatcher = birthDayPattern.matcher(content);
                    if (birthDayMatcher.find()) {
                        String birthDay = birthDayMatcher.group(1).replace("年", "-").replace("月", "-");

                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                        Date dateInfo = simpleDateFormat.parse(birthDay);
                        casePersonEntity.setBirthDay(simpleDateFormat.format(dateInfo));
                    } else {
                        casePersonEntity.setBirthDay("");
                    }
                } catch (Exception ex) {
                    casePersonEntity.setBirthDay("");
                }

                // 提取地址相关信息
                try {
                    JSONObject addressJsonObj = new JSONObject();
                    Pattern addressPattern = Pattern.compile("(身份证住址|住|住所地)(.*)");
                    Matcher addressMatcher = addressPattern.matcher(content);
                    if (addressMatcher.find() && addressMatcher.groupCount() == 2) {
                        String addressType = addressMatcher.group(1);
                        String address = addressMatcher.group(2)
                                .replace("：", "")
                                .replace("，", "")
                                .replace("。", "");
                        addressJsonObj.put("type", addressType);
                        addressJsonObj.put("address", address);
                        casePersonEntity.setAddress(addressJsonObj);
                    } else {
                        casePersonEntity.setAddress(addressJsonObj);
                    }
                } catch (Exception ex) {
                    casePersonEntity.setAddress(new JSONObject());
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return casePersonEntity;
    }

    /**
     * 提取当事人的角色和名称信息
     *
     * @param content
     * @return e.g. ['原告', 'xxxx有限公司']  ['法定代表人', '张三']
     */
    private static RoleEntity getCaseRole(String content) {
        RoleEntity roleEntity = null;
        for (String keyword : filterHeadKeywords) {
            if (content.startsWith(keyword)) {
                roleEntity = new RoleEntity();
                roleEntity.setRole(keyword);
                roleEntity.setName(content.replace(keyword, "").replace("：", ""));
                break;
            }
        }
        return roleEntity;
    }

    /**
     * 过滤原始文书内容，仅保留涉及到当事人信息的部分文本
     */
    private static String filterSegmentByHeadKeyword(String content) {
        try {
            if (StringUtils.isNotBlank(content)) {
                for (String keyword : filterHeadKeywords) {
                    if (content.startsWith(keyword)) {
                        // 标点符号的后续处理
                        String result = content.replace("(", "（")
                                .replace(")", "）")
                                .replace(",", "，")
                                .replace(":", "：");
                        return result;
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return "";
    }
//
    public static void main(String[] args) {
//        System.out.println(evaluate("<div style=\"TEXT-ALIGN: center; LINE-HEIGHT: 25pt; MARGIN: 0.5pt 0cm; FONT-FAMILY: 宋体; FONT-SIZE: 22pt;\">  内蒙古自治区扎兰屯市人民法院 </div> <a type=\"dir\" name=\"AJJBQK\"></a> <div style=\"TEXT-ALIGN: center; LINE-HEIGHT: 30pt; MARGIN: 0.5pt 0cm; FONT-FAMILY: 仿宋; FONT-SIZE: 26pt;\">  民 事 裁 定 书 </div> <div style=\"TEXT-ALIGN: right; LINE-HEIGHT: 30pt; MARGIN: 0.5pt 0cm;  FONT-FAMILY: 仿宋;FONT-SIZE: 16pt; \">  （2017）内0783民初3934号之一 </div> <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  原告:扎兰屯市鼎晟物业管理有限责任公司，住所地内蒙古自治区扎兰屯市。 </div> <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  法定代表人：李树启。 </div> <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  被告：邹军，男，住内蒙古自治区扎兰屯市。 </div> <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  原告扎兰屯市鼎晟物业管理有限责任公司与被告邹军物业服务合同纠纷一案，本院于2017年12月21日立案受理后，原告扎兰屯市鼎晟物业管理有限责任公司于2017年12月22日向本院提出撤诉申请。 </div> <a type=\"dir\" name=\"CPYZ\"></a> <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  本院认为，原告扎兰屯市鼎晟物业管理有限责任公司的申请符合国家相关法律规定。 </div> <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  依照《中华人民共和国民事诉讼法》第一百四十五条第一款的规定，裁定如下： </div> <a type=\"dir\" name=\"PJJG\"></a> <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  准许原告扎兰屯市鼎晟物业管理有限责任公司撤诉。 </div> <div style=\"LINE-HEIGHT: 25pt;TEXT-ALIGN:justify;TEXT-JUSTIFY:inter-ideograph; TEXT-INDENT: 30pt; MARGIN: 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  案件受理费25元,免予收取。 </div> <a type=\"dir\" name=\"WBWB\"></a> <div style=\"TEXT-ALIGN: right; LINE-HEIGHT: 25pt; MARGIN: 0.5pt 72pt 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  审判长　　周政宇 </div> <div style=\"TEXT-ALIGN: right; LINE-HEIGHT: 25pt; MARGIN: 0.5pt 72pt 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  审判员　　张雯雯 </div> <div style=\"TEXT-ALIGN: right; LINE-HEIGHT: 25pt; MARGIN: 0.5pt 72pt 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  审判员　　李　英 </div> <br> <div style=\"TEXT-ALIGN: right; LINE-HEIGHT: 25pt; MARGIN: 0.5pt 72pt 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  二〇一七年十二月二十二日 </div> <div style=\"TEXT-ALIGN: right; LINE-HEIGHT: 25pt; MARGIN: 0.5pt 72pt 0.5pt 0cm;FONT-FAMILY: 仿宋; FONT-SIZE: 16pt;\">  书记员　　李娜娜 </div>"));
    }
}
