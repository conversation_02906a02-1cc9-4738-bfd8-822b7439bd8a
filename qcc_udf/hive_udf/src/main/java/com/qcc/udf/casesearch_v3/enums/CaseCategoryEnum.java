package com.qcc.udf.casesearch_v3.enums;

import lombok.Getter;

/**
 * @Auther: z<PERSON>qiang
 * @Date: 2020/11/11 17:54
 * @Description:司法案件来源维度枚举
 */
@Getter
public enum CaseCategoryEnum {
    SX(1,"失信"),
    ZX(2,"被执行"),
    XG(3,"限高"),
    CPWS(4,"裁判文书"),
    PCCZ(5,"破产重组"),
    ZB(6,"终本"),
    XJPG(7,"询价评估"),
    GQDJ(8,"股权冻结"),
    SDGG(9,"送达公告"),
    FYGG(10,"法院公告"),
    KTGG(11,"开庭公告"),
    LA(12,"立案"),
    HBCF(13,"环保处罚"),
    XZCF(14,"行政处罚"),
    SFPM(15,"司法拍卖"),
    SQTJ(16,"诉前调解"),
    XZCJ(17,"限制出境"),
    XDPGJG(18,"选定评估机构"),
    XSGG(19,"悬赏公告");

    CaseCategoryEnum(int type,String desc) {
        this.type = type;
        this.desc = desc;
    }

    private int type;
    private String desc;
}
