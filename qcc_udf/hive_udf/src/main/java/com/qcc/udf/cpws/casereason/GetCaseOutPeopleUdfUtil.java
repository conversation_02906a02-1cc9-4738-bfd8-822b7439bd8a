package com.qcc.udf.cpws.casereason;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date ：Created in 2022/12/13
 * @description ：获得案外人
 */
public class GetCaseOutPeopleUdfUtil {

    public static void main(String[] args) {
        String lawJudgeParty = "在本院审理南通建工集团股份有限公司（以下简称南通建工公司）与被申请人江阴润泽置业有限公司（以下简称江阴润泽公司）建设工程施工合同纠纷一案过程中，案外人林旦明对本院冻结江阴润泽公司银行存款提出执行异议。本院受理后，依法组成合议庭进行审查，本案现已审查终结。";
        // String lawJudgeParty = "本院在执行申请执行人高宝山与被执行人金地佳公司、张永娟股权转让纠纷一案中，案外人红钻二手车公司在执行期间向本院提出书面异议，本院受理后，依法进行审查，现已审查终结。  案外人红钻二手车公司称：2016年5月30日，红钻二手车公司股东谢玉芳与被执行人金地佳公司达成股权转让协议，金地佳公司用汤国用2015第0310号，宗地号11-11-11号，面积为42459.5平方米的土地使用权及地上物作为出资入股红钻二手车公司。2017年3月6日，汤原县人民法院出具调解书确认了股权转让协议的效力。2017年3月28日，经红钻二手车公司申请，汤原县人民法院作出了（2017）黑0828民初377号民事裁定书，查封了上述不动产。2019年9月6日，申请执行人申请恢复执行，续查封了上述不动产。综上，案外人依据《中华人民共和国民事诉讼法》第二百二十七条之规定，提起案外人执行异议，请求依法裁决。";
        // String lawJudgeParty = "案外人吕世涛向本院提出书面异议。本院受理后，依法组成合议庭进行了审查，现已审查终结。 案外人吕世涛称，其于2016年11月15日购买了尚缘公司开发建设的位于金堂县房屋（以下简称案涉房屋），并付清全部房款。同日，尚缘国际公司完成商品房备案手续（合同签约备案号：86246、86854、86248）。吕世涛对上述房屋享有合法的权益。因金堂县人民法院（以下简称金堂法院）对尚缘公司所有的案涉土地及所对应的房屋进行查封，致使吕世涛无法办理该三套案涉房屋的不动产证手续。故请求解除该三套案涉房屋及所对应土地的查封。";
        // String lawJudgeParty = "本院认为，中太公司申请执行聚仁公司建设工程施工合同纠纷一案，诉讼中，本院于2016年1月18日依法查封了聚仁公司开发的睢县波尔多公馆小区1、3、5、6、7、8、10、11、12号楼中的37套房屋，制作了查封笔录，在查封标的物处张贴了查封公告，到睢县房地产产权交易中心办理了查封登记，查封行为合法有效。案外人杨树昂提供了其和聚仁公司于2015年8月10日签订的睢县波尔多公馆小区1、3、5、6、7、8、10、11、12号楼中的30套房屋商品房买卖合同，原因是因他人欠其工程款，聚仁公司用涉案房屋替他人抵的债，上述抵债房屋案外人杨树昂并未实际支付购房款，也不是用于自身居住，不能够排除本案执行。综上所述，本院执行诉讼中查封的上述房屋正确，案外人杨树昂的异议理由不能成立，其请求本院不予支持。依照《中华人民共和国民事诉讼法》第二百二十七条、《最高人民法院关于适用<中华人民共和国民事诉讼法>执行程序若干问题的解释》第十五条、《最高人民法院关于人民法院办理执行异议和复议案件若干问题的规定》第二十四条、第二十九条之规定，裁定如下：";
        // String lawJudgeParty = "查封行为合法有效。案外人杨树昂提供了其和聚仁公司于2015年8月10日签订的睢县波尔多公馆小区1、3、5、6、7、8、10、11、12号楼中的30套房屋商品房买卖合同，综上所述，本院执行诉讼中查封的上述房屋正确，案外人杨树昂的异议理由不能成立，其请求本院不予支持。";
        // String lawJudgeParty = "案外人刘芳，住重庆市忠县汝溪镇镇江社区居委四组36号。 案外人江海龙，住重庆市忠县汝溪镇镇江社区居委四组36号。 申请执行人刘华，住**。 被执行人重庆明光房地产开发有限公司，住所地重庆市忠县忠州镇州屏环路4号。 法定代表人刘光明，董事长。";

        getCaseOutPeople(lawJudgeParty, 1);
    }

    public static String getCaseOutPeople(String content, int type) {

        String caseOutPeople = "";
        if (StringUtils.isEmpty(content)) {
            return caseOutPeople;
        }
        String contentForMatch = convertWithLineFeed(content)
                .replace("(", "（").replace(")", "）").replace(":", "：")
                .replace(";", "；").replace(".", "。").replace(",", "，");

        List<String> segmentList = Arrays.stream(contentForMatch.split("[\n。 ，；]")) // 分割段落
                .map(e -> e.replace(" ", "").replace("　", ""))
                .map(StringUtils::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        // 文书提取到的涉案人员提取信息对象映射集合
        Map<String, String> caseRoleExtractEntityMap = new LinkedHashMap<>();

        List<String> keywordList = Arrays.asList("案外人（××）", "案外人（证人）", "案外人（异议人）", "案外人（案外人）", "案外人（承诺人）", "案外人（上访人）",
                "案外人（申请人）", "案外人（申诉人）", "案外人（拍得人）", "案外人（第三人）", "案外人（买受人）", "案外人（担保人）", "案外人（受害人）",
                "案外人（代表人）", "案外人（信访人）", "案外人（借款人）", "案外人（债权人）", "案外人（保证人）", "案外人（承租人）", "案外人（竞买人）",
                "案外人（原异议人）", "案外人（被执行人）", "案外人（被申请人）", "案外人（被复议人）", "案外人（被保全人）", "案外人（原参与人）", "案外人﹙异议人﹚",
                "案外人（即异议人）", "案外人（再审原告）", "案外人（抵押权人）", "案外人（异议申请人）", "案外人（利害关系人）", "案外人（申请异议人）",
                "案外人（执行担保人）", "案外人（申请执行人）", "案外人（原案异议人）", "案外人（协助义务人）", "案外人（申请复议人）", "案外人（协助执行人）",
                "案外人（执行异议人）", "案外人（复议申请人）", "案外人（原审被告人）", "案外人（原审案外人）", "案外人（诉讼代表人）", "案外人（原审异议人）",
                "案外人（再审申请人）", "案外人（暨被执行人）", "案外人（保全异议人）", "案外人（系被执行人）", "案外人（原被执行人）", "案外人（执行保证人）",
                "案外人（一审异议人）", "案外人（被申请执行人）", "案外人（原案被执行人）", "案外人（被申请复议人）", "案外人（协助执行单位）", "案外人（原异议申请人）",
                "案外人（原申请执行人）", "案外人（追加被执行人）", "案外人（本案评估机构）", "案外人（执行异议申请人）", "案外人（原案申请执行人）", "案外人（另案申请执行人）",
                "案外人（诉讼保全担保人）", "案外人（原案利害关系人）", "案外人（抵押财产共有人）", "案外人（拍卖房产买受人）", "案外人（参与财产分配人）", "案外人（刑事案件受害人）",
                "案外人（查封异议申请人）", "案外人（被追加申请执行人）", "案外人（被追加的被执行人）", "案外人（保全担保物担保人）", "案外人（诉前保全被申请人）",
                "案外人（司法拍卖的买受人）", "案外人（被执行债务加入方）", "案外人（部分案件的被执行人）", "案外人（附带民事诉讼原告人）", "案外人（本案其他执行债权人）",
                "案外人（实际上的委托代理人）", "案外人（被执行人的到期债务人）", "案外人（本案被拍卖房屋买受人）", "案外人（变更保全标的物担保人）", "案外人（原案申诉人）",
                "案外人（不予执行仲裁裁决申请人）", "案外人（复议申请人、异议人）", "案外人（异议人、利害关系人）", "案外人（异议人、申请复议人）",
                "案外人（原异议人、利害关系人）", "案外人（复议申请人，申请执行人）", "原案外人（异议人）", "案外人（被申请人、被告）", "案外人（拍卖标的物买受人）",
                "案外人（房屋买受人）", "案外人（参与分配执行案件的利害关系人）", "案外人（诉讼中财产保全担保人）", "案外人（异议竞买人）", "案外人（分配参与人）",
                "案外人（异议人、原财产保全被申请人）", "案外人（异议人、复议申请人）", "案外人（即申请执行人）", "案外人（申请参与分配债权人）", "案外人");

        for (String segment : segmentList) {
//            if (segment.contains(keyword)) { // 不是首字母
//                String unitName = extractCaseInvolvedUnitName(keyword, segment.substring(segment.indexOf(keyword)), type);
//                if (caseInvolvedUnitIsCompanyOrPersonName(unitName)) {
//                    caseRoleExtractEntityMap.put(unitName, keyword);
//                }
//            }
            String res = "";
            for (String keyword : keywordList) {
                // 不是第一次赋值
                segment = StringUtils.isEmpty(res) ? segment : res;
                // 将系案外人去除
                segment = segment.replaceAll("系" + keyword, "");
                if (segment.contains(keyword)) {
                    //判断是否在括号内
                    Matcher matcher = Pattern.compile("(?<=\\（)[^\\）]+").matcher(segment);
                    int first = 0, end = 0;
                    String tempVal = "";
                    while (matcher.find()) {
                        if (matcher.group().contains(keyword)) {
                            end = matcher.end();
                            tempVal += segment.substring(first, end).replace(keyword, "");
                            first = end;
                        }
                    }
                    if (end > 0) {
                        res = tempVal + segment.substring(end, segment.length());
                    } else {
                        res = segment;
                    }
                } else {
                    res = segment;
                }
                while (res.startsWith(keyword)) {
                    //截取每个匹配的数据进行处理
                    String unitName = extractCaseInvolvedUnitName(keyword, res, type);
                    res = res.replaceFirst(keyword, "");
                    if (caseInvolvedUnitIsCompanyOrPersonName(unitName)) {
                        caseRoleExtractEntityMap.put(unitName, keyword);
                    }
                }
            }
        }
//        caseOutPeople = JSON.toJSONString(caseRoleExtractEntityMap);

//        String outsider_c = "";
//        outsider_c = caseRoleExtractEntityMap.keySet().stream().filter(it -> it.length() >= 5).collect(Collectors.joining(","));
//        System.out.println("outsider_c========" + outsider_c);
//        caseRoleExtractEntityMap.keySet().forEach(it-> {
//            if(it.length() >= 5) {
//
//            } else {
//
//            }
//        });
        caseOutPeople = String.join(",", caseRoleExtractEntityMap.keySet());
        System.out.println("caseOutPeople========" + caseOutPeople);
        return caseOutPeople;
    }

    /**
     * 当事人段落提取
     *
     * @param keyword
     * @param segment
     * @return
     */
    private static String extractCaseInvolvedUnitName(String keyword, String segment, int type) {
        String unitName = "";
        if (StringUtils.isNotBlank(segment) && StringUtils.isNotBlank(keyword)) {

            String extractSegment = formatMatchInfo(segment);
//            Matcher matcher = Pattern.compile("^" + keyword + "[：,，、；;]?([\\u4e00-\\u9fa5,，、；;（）()《》X×x0-9a-zA-Z：:·ＸＸ１９８ＭＣ\\-\\*＊—{}&/“‘’”．?\\[\\]【】♯#＃－Ａ]{2,})$").matcher(extractSegment);
            Matcher matcher = Pattern.compile("^" + keyword + "[:：，;；,、{]{1,}([\\s\\S]{2,})$").matcher(extractSegment);
            if (matcher.find()) {
                unitName = matcher.group(1).split("[:：，;；,、{]")[0];
                if (StringUtils.isNotEmpty(unitName)) {
                    unitName = unitName.split("（公民身份|（个人独资企业|（原|（注|（英文|（甲方|（乙方|（丙方|（丁方")[0];
                }
            } else {
                Matcher matcherNew = Pattern.compile("^" + keyword + "[:：，;；,、{]{0,}([\\s\\S]{2,})$").matcher(extractSegment);
                if (matcherNew.find()) {
                    unitName = matcherNew.group(1).split("[:：，;；,、{]")[0];
                    if (StringUtils.isNotEmpty(unitName)) {
                        unitName = unitName.split("（公民身份|（个人独资企业|（原|（注|（英文|（甲方|（乙方|（丙方|（丁方")[0];
                    }
                    List<String> list2 = Arrays.asList("称", "对", "在", "提", "的", "已", "签", "是", "雇", "得", "拒", "系",
                            "驾驶", "承担", "转让", "名下", "所有", "支付", "详见", "代替", "代为", "以保", "于****年",
                            "不服", "出具", "请求", "认为", "乘坐", "为了", "员工", "住所", "自愿", "证明", "缴纳",
                            "以其", "为该", "以向", "因向", "因为", "因以", "通过", "属于", "属于", "等曾", "于同",
                            "等1", "等2", "等3", "等4", "等5", "等6", "等7", "等8", "等9", "&gt", "于1", "于2",
                            "向本院", "向我院", "向法院", "向被告", "向江苏", "向贵州", "向安徽", "向人民法院", "进行了", "为申请",
                            "暨委托", "负事故", "以购买", "向房管局", "委托代理", "执行异议", "共同委托", "委托诉讼");

                    for (String key : list2) {
                        if (StringUtils.isNotEmpty(unitName) && unitName.contains(key) && !unitName.contains("美的")) {
                            String[] split = unitName.split(key);
                            unitName = split.length > 0 ? split[0] : "";
                        }
                    }
                }
            }

            // 去掉都为*的名字
            if (unitName.contains("*") && unitName.matches("[\\*]{1,}")) {
                unitName = "";
            }
            if (unitName.equals("Ｘ") || unitName.equals("ＸＸ") || unitName.equals("ＸＸＸ") || unitName.equals("ＸＸＸＸ") ||
                    unitName.equals("X") || unitName.equals("XX") || unitName.equals("XXX") || unitName.equals("XXXX") ||
                    unitName.equals("x") || unitName.equals("xx") || unitName.equals("xxx") || unitName.equals("xxxx") ||
                    unitName.equals("×") || unitName.equals("××") || unitName.equals("×××") || unitName.equals("××××") ||
                    unitName.equals("＊") || unitName.equals("＊＊") || unitName.equals("＊＊＊")) {
                unitName = "";
            }
            if (unitName.startsWith(")") || unitName.startsWith("）") || unitName.startsWith("﹚")) {
                unitName = "";
            }
            if (unitName.startsWith("\\]") || unitName.startsWith("】") || unitName.startsWith("]")) {
                unitName = "";
            }
            if (unitName.startsWith("异议") || unitName.startsWith("人）") || unitName.startsWith("女") || unitName.startsWith("原告") ||
                    unitName.startsWith("之") || unitName.startsWith("……") || unitName.startsWith("\\u000B")) {
                unitName = "";
            }
            // 所有段落处理名字
            if (unitName.equals("当事人") || unitName.equals("所") || unitName.equals("处") || unitName.equals("”") || unitName.equals("“") ||
                    unitName.equals("存") || unitName.equals("认") || unitName.equals("未") || unitName.equals("作") || unitName.equals("故") ||
                    unitName.equals("针") || unitName.equals("原") || unitName.equals("南") || unitName.equals("其") || unitName.equals("信") ||
                    unitName.equals("（") || unitName.equals("但") || unitName.equals("接") || unitName.equals("还") || unitName.equals("取") ||
                    unitName.equals("或") || unitName.equals("湘") || unitName.equals("合") || unitName.equals("将") || unitName.equals("如") ||
                    unitName.equals("间") || unitName.equals("而") || unitName.equals("即") || unitName.equals("仅") || unitName.equals("制") ||
                    unitName.equals("一") || unitName.equals("员") || unitName.equals("正") || unitName.equals("外") || unitName.equals("有") ||
                    unitName.equals("居") || unitName.equals("涉") || unitName.equals("参") || unitName.equals("联") || unitName.equals("予") ||
                    unitName.equals("中") || unitName.equals("下") || unitName.equals("现") || unitName.equals("后") || unitName.equals("若") ||
                    unitName.equals("行") || unitName.equals("亦") || unitName.equals("发") || unitName.equals("串") || unitName.equals("又") ||
                    unitName.equals("都") || unitName.equals("被") || unitName.equals("呼") || unitName.equals("按") || unitName.equals("获") ||
                    unitName.equals("暂") || unitName.equals("只") || unitName.equals("收") || unitName.equals("多") || unitName.equals("业") ||
                    unitName.equals("非") || unitName.equals("国") || unitName.equals("私") || unitName.equals("做") || unitName.equals("时") ||
                    unitName.equals("可") || unitName.equals("代") || unitName.equals("既") || unitName.equals("系") || unitName.equals("遂") ||
                    unitName.equals("以") || unitName.equals("应") || unitName.equals("为") || unitName.equals("均") || unitName.equals("订") ||
                    unitName.equals("合伙") || unitName.equals("公司") || unitName.equals("被告") || unitName.equals("上海") || unitName.equals("抵押") ||
                    unitName.equals("房屋") || unitName.equals("三方") || unitName.equals("债权") || unitName.equals("担保") || unitName.equals("管理") ||
                    unitName.equals("单方") || unitName.equals("中国") || unitName.equals("”）") || unitName.equals("故本案") || unitName.equals("当事人如") ||
                    unitName.equals("案外人") || unitName.equals("抵押权") || unitName.equals("房屋居") || unitName.equals("利害关") || unitName.equals("不") ||
                    unitName.equals("第三人") || unitName.equals("上诉人") || unitName.equals("上海某") || unitName.equals("本案中") || unitName.equals("于***") ||
                    unitName.equals("（一）") || unitName.equals("生活至今") || unitName.equals("（四）") || unitName.equals("被上诉人") ||
                    unitName.equals("（一）查封") || unitName.equals("（四）债务") || unitName.equals("或被害人认") || unitName.equals("（六）人民法院") ||
                    unitName.equals("法院将异议人") || unitName.equals("人民法院决定扣押") || unitName.equals("（六）人民法院认") || unitName.equals("法院先后多轮")) {
                unitName = "";
            }

            Set<String> filterSet = new LinkedHashSet<>();
            filterSet.add("基本情况");
            filterSet.add("诉请要点");
            filterSet.add("答辩要点");
            filterSet.add("本院刑庭");
            filterSet.add("答辩意见");
            filterSet.add("诉讼请求");
            filterSet.add("(一审原告");
            filterSet.add("(一审被告");
            filterSet.add("(原审原告");
            filterSet.add("(原审被告");
            filterSet.add("(反诉被告");
            filterSet.add("(反诉原告");
            filterSet.add("已提供担保");
            filterSet.add("对此无异议");
            filterSet.add("未到庭应诉");

            if (StringUtils.isNotEmpty(unitName)) {
                for (String str : filterSet) {
                    if (ToDBC(str).equals(ToDBC(unitName))) {
                        unitName = "";
                    }
                }
            }

        }

        if (StringUtils.isNotEmpty(unitName)) {
            if (unitName.contains("（") && !unitName.contains("）")) {
                unitName = unitName.split("（")[0];
            }
        }

        if (StringUtils.isNotEmpty(unitName)) {
            if ((unitName.contains("（") && unitName.endsWith("）")) &&
                    (!unitName.contains("有限合伙") && !unitName.contains("普通合伙") && !unitName.contains("有限公司") && !unitName.contains("特殊普通合伙"))) {
                unitName = unitName.split("（")[0];
            }
        }
        return unitName;
    }

    public static String ToDBC(String input) {
        char c[] = input.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == '\u3000') {
                c[i] = ' ';
            } else if (c[i] > '\uFF00' && c[i] < '\uFF5F') {
                c[i] = (char) (c[i] - 65248);
            }
        }
        return new String(c);
    }

    /**
     * 文本标签过滤
     *
     * @param content
     * @return
     */
    public static String convertWithLineFeed(String content) {
        Pattern p_script, p_style, p_html, p_html1;
        Matcher m_script, m_style, m_html, m_html1;

        // 定义script的正则表达式{或<script[^>]*?>[\\s\\S]*?<\\/script> }
        String regEx_script = "<[\\s]*?script[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?script[\\s]*?>";
        // 定义style的正则表达式{或<style[^>]*?>[\\s\\S]*?<\\/style> }
        String regEx_style = "<[\\s]*?style[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?style[\\s]*?>";
        String regEx_html = "<[^>]+>"; // 定义HTML标签的正则表达式
        String regEx_html1 = "<[^>]+";
        p_script = Pattern.compile(regEx_script, Pattern.CASE_INSENSITIVE);
        m_script = p_script.matcher(content);
        content = m_script.replaceAll(""); // 过滤script标签

        p_style = Pattern.compile(regEx_style, Pattern.CASE_INSENSITIVE);
        m_style = p_style.matcher(content);
        content = m_style.replaceAll(""); // 过滤style标签

        p_html = Pattern.compile(regEx_html, Pattern.CASE_INSENSITIVE);
        m_html = p_html.matcher(content);
        content = m_html.replaceAll("\n"); // 过滤html标签

        p_html1 = Pattern.compile(regEx_html1, Pattern.CASE_INSENSITIVE);
        m_html1 = p_html1.matcher(content);
        content = m_html1.replaceAll("\n"); // 过滤html标签

        return content;// 返回文本字符串
    }

    /**
     * 剔除掉能影响到身份及当事人名称提取的干扰信息
     */
    private static String formatMatchInfo(String matchInfo) {
        if (StringUtils.isBlank(matchInfo)) {
            return matchInfo;
        }
        // 1 剔除"曾用名"的干扰项
        if (matchInfo.contains("曾用名")) {
            matchInfo = matchInfo.replaceAll("（曾用名.*）$|曾用名.*", "");
        }
        // 2 剔除"原公司名称"的干扰项
        if (matchInfo.contains("原公司名称")) {
            matchInfo = matchInfo.replaceAll("（原公司名称.*）|原公司名称.*", "");
        }
        // 3 剔除"以下简称"的干扰项
        if (matchInfo.contains("以下简称")) {
            matchInfo = matchInfo.replaceAll("（以下简称.*|（以下简称：.*）|以下简称.*", "");
        }
        // 4 剔除"原名"的干扰项（该项与前面有差异，仅过滤包含括号的情况）
        if (matchInfo.contains("（原名")) {
            matchInfo = matchInfo.replaceAll("（原名.*）", "");
        }
        // 5 剔除"别名"的干扰项
        if (matchInfo.contains("别名")) {
            matchInfo = matchInfo.replaceAll("（别名.*）|别名.*", "");
        }
        //  6 剔除"组织机构代码"的干扰项
        if (matchInfo.contains("组织机构代码")) {
            matchInfo = matchInfo.replaceAll("（组织机构代码.*）|组织机构代码.*", "");
        }
        //  7 剔除"绰号"的干扰项
        if (matchInfo.contains("绰号")) {
            matchInfo = matchInfo.replaceAll("（绰号.*）|绰号.*", "");
        }
        // 8 剔除"又名"的干扰项
        if (matchInfo.contains("又名")) {
            matchInfo = matchInfo.replaceAll("（又名.*）|又名.*", "");
        }
        // 9 剔除"又名"的干扰项
        if (matchInfo.contains("统一社会信用")) {
            matchInfo = matchInfo.replaceAll("（统一社会信用.*）|统一社会信用.*", "");
        }
        if (matchInfo.contains("社会统一信用")) {
            matchInfo = matchInfo.replaceAll("（社会统一信用.*）|社会统一信用.*", "");
        }
        // 10 剔除"原申请执行人"的干扰项
        if (matchInfo.contains("（原申请执行人")) {
            matchInfo = matchInfo.replaceAll("（原申请执行人.*）$", "");
        }

        if (matchInfo.contains("（中文名")) {
            matchInfo = matchInfo.replaceAll("（中文名.*）", "");
        }

        return matchInfo;
    }

    /**
     * 判断提取出的涉案单位字符串是公司名或者人名
     */
    private static Boolean caseInvolvedUnitIsCompanyOrPersonName(String name) {
        /**
         * 判断依据： 1 长度x<=4 默认为人名   2 后缀在集合范围内的字符串
         */
        if (StringUtils.isNotBlank(name)) {
            name = getCompanyNameByName(name);

            if (name.contains("·")) {
                if (name.length() <= 12) {
                    return Boolean.TRUE;
                }
            }
            if (name.length() <= 4) {
                return Boolean.TRUE;
            }
            if (name.contains("公司") || name.contains("企业") || name.contains("股份") || name.contains("有限") || name.contains("机构") || name.contains("酒楼")
                    || name.contains("集团") || name.contains("基金") || name.contains("中心") || name.contains("学校") || name.contains("市场")
                    || name.contains("小学") || name.contains("中学") || name.contains("大学") || name.contains("门诊") || name.contains("医院")
                    || name.contains("村委会") || name.contains("居委会") || name.contains("委员会") || name.contains("旅行社") || name.contains("信用社")
                    || name.contains("合作社") || name.contains("敬老院") || name.contains("养老院") || name.contains("经销处")
                    || name.endsWith("场") || name.endsWith("厂") || name.endsWith("局") || name.endsWith("组") || name.endsWith("队")
                    || name.endsWith("委") || name.endsWith("店") || name.endsWith("房") || name.endsWith("厅") || name.endsWith("行")
                    || name.endsWith("庄") || name.endsWith("县") || name.endsWith("所") || name.endsWith("部") || name.endsWith("站")
                    || name.endsWith("摊") || name.endsWith("处") || name.endsWith("馆") || name.endsWith("家") || name.endsWith("园")
                    || name.endsWith("室") || name.endsWith("区") || name.endsWith("市") || name.endsWith("村") || name.endsWith("圃")
                    || name.endsWith("城") || name.endsWith("业") || name.endsWith("社") || name.endsWith("坊") || name.endsWith("楼")
                    || name.endsWith("汇") || name.endsWith("户") || name.endsWith("档") || name.endsWith("帮") || name.endsWith("岗")
                    || name.endsWith("亭") || name.endsWith("铺") || name.endsWith("会") || name.endsWith("司") || name.endsWith("院")
                    || name.endsWith("屯") || name.endsWith("矿") || name.endsWith("校") || name.endsWith("庭") || name.endsWith("台")
                    || name.endsWith("学") || name.endsWith("吧") || name.endsWith("段") || name.endsWith("团") || name.endsWith("库")
                    || name.endsWith("专柜") || name.endsWith("门市") || name.endsWith("食堂") || name.endsWith("沙龙") || name.endsWith("商铺")
                    || name.endsWith("政府") || name.endsWith("超市") || name.endsWith("浴池") || name.endsWith("基地") || name.endsWith("餐饮")
                    || name.endsWith("发艺") || name.endsWith("农家乐") || name.endsWith("（有限合伙）") || name.endsWith("（普通合伙）")
                    || name.endsWith("（有限公司）") || name.endsWith("（特殊普通合伙）")) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 公司名称中括号的处理（全英文为半角括号，有中文字符时为全角括号）
     */
    public static String getCompanyNameByName(Object param) {
        // 如果全空，则返回空值
        if (param == null || StringUtils.isEmpty(param.toString())) {
            return "";
        }
        // 全部英文的情况下，返回英文括号
        Matcher m = Pattern.compile("[\u4e00-\u9fa5]").matcher(param.toString());
        if (!m.find()) {
            return param.toString().replace("（", "(").replace("）", ")");
        } else {
            // 其他情况，返回中文括号
            return param.toString().replace("(", "（").replace(")", "）");
        }
    }

}
