package com.qcc.udf.register;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.risk_analysis.entity.NameKeyDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2021/07/14 13:54
 * @description ：获取非原告被告第三人的其他当事人
 */
public class GetOtherParties extends UDF {

    /**
     * Desc:获取其他当事人
     *
     * */
    public String evaluate(String pltfKeynoArray, String defdKeynoArray, String tdptKeynoArray, String keyNoArray) {
        //其他当事人
        List<NameKeyDto> result = new ArrayList<>();
        try{
            //原告
            List<NameKeyDto> plaintiffList;
            //被告
            List<NameKeyDto> defendantList;
            //第三人
            List<NameKeyDto> thirdPartyList;
            //当事人合集
            List<NameKeyDto> keyNoArrayList;

            if (StringUtils.isEmpty(pltfKeynoArray)) {
                plaintiffList = new ArrayList<>();
            } else {
                plaintiffList = JSONArray.parseArray(pltfKeynoArray, NameKeyDto.class);
            }
            List<String> plaintiffKeyNos = plaintiffList.stream().filter(e -> StringUtils.isNotEmpty(e.getKeyNo())).map(e -> e.getKeyNo()).collect(Collectors.toList());

            if (StringUtils.isEmpty(defdKeynoArray)) {
                defendantList = new ArrayList<>();
            } else {
                defendantList = JSONArray.parseArray(defdKeynoArray, NameKeyDto.class);
            }
            List<String> defendantKeyNos = defendantList.stream().filter(e -> StringUtils.isNotEmpty(e.getKeyNo())).map(e -> e.getKeyNo()).collect(Collectors.toList());

            if (StringUtils.isEmpty(tdptKeynoArray)) {
                thirdPartyList = new ArrayList<>();
            } else {
                thirdPartyList = JSONArray.parseArray(tdptKeynoArray, NameKeyDto.class);
            }
            List<String> thirdPartyKeyNos = thirdPartyList.stream().filter(e -> StringUtils.isNotEmpty(e.getKeyNo())).map(e -> e.getKeyNo()).collect(Collectors.toList());

            if (StringUtils.isEmpty(keyNoArray)) {
                keyNoArrayList = new ArrayList<>();
            } else {
                keyNoArrayList = JSONArray.parseArray(keyNoArray, NameKeyDto.class);
            }

            if (CollectionUtils.isNotEmpty(keyNoArrayList)) {
                for(NameKeyDto item : keyNoArrayList) {
                    String keyNo = item.getKeyNo();
                    if (StringUtils.isEmpty(keyNo)) {
                        continue;
                    }
                    if (!plaintiffKeyNos.contains(keyNo) && !defendantKeyNos.contains(keyNo) && !thirdPartyKeyNos.contains(keyNo)) {
                        result.add(item);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return JSONObject.toJSONString(result);
    }

    public static void main(String[] args) {
        GetOtherParties getOtherParties = new GetOtherParties();
        String pltfKeynoArray = "[{\"KeyNo\":\"\",\"Name\":\"李敏\",\"Org\":-1},{\"KeyNo\":\"e9a2af52a49806f020fc1f6c59d66cc6\",\"Name\":\"湖北庄祥投资有限公司\",\"Org\":0}]";
        String defdKeynoArray = "[]";
        String tdptKeynoArray = "[]";
        String keyNoArray = "[{\"KeyNo\":\"\",\"Name\":\"李敏\",\"Org\":-1},{\"KeyNo\":\"e9a2af52a49806f020fc1f6c59d66cc6\",\"Name\":\"湖北庄祥投资有限公司\",\"Org\":0},{\"KeyNo\":\"f2fbb21dfc90dfa4c99aebd92d198333\",\"Name\":\"湖北治历实业有限责任公司\",\"Org\":0}]";
        String result = getOtherParties.evaluate(pltfKeynoArray, defdKeynoArray, tdptKeynoArray, keyNoArray);
        System.out.println(result);
    }
}
