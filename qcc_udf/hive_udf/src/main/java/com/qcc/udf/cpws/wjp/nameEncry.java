package com.qcc.udf.cpws.wjp;

import cn.hutool.core.util.StrUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.Collections;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class nameEncry extends UDF {
    //空白字符
    public static final String BLANK_REGEX = "\\s*|\r|\n";
    public static final Pattern BLANK_CHAR_PATTERN = Pattern.compile(BLANK_REGEX);
    /* 已知的复姓 */
    private static final String[] SURNAME = {
            "上官", "欧阳", "夏侯", "诸葛", "闻人", "东方", "赫连", "皇甫", "羊舌", "尉迟", "公羊", "澹台", "公冶", "宗正", "濮阳", "淳于",
            "单于", "太叔", "申屠", "公孙", "仲孙", "轩辕", "令狐", "钟离", "宇文", "长孙", "慕容", "鲜于", "闾丘", "司徒", "司空", "兀官",
            "司寇", "南门", "呼延", "子车", "颛孙", "端木", "巫马", "公西", "漆雕", "车正", "壤驷", "公良", "拓跋", "夹谷", "宰父", "谷梁",
            "段干", "百里", "东郭", "微生", "梁丘", "左丘", "东门", "西门", "南宫", "第五", "公仪", "公乘", "太史", "仲长", "叔孙", "屈突",
            "尔朱", "东乡", "相里", "胡母", "司城", "张廖", "雍门", "毋丘", "贺兰", "綦毋", "屋庐", "独孤", "南郭", "北宫", "王孙", "万俟",
            "司马"
    };
    /* 包含特殊字符脱敏 */
    private static final String[] SpecialName = {
            "公司", "中心", "银行", "支行", "商行", "联社", "医院", "合作社", "村委会", "分行", "厂", "经营部", "委员会", "书店", "政府",
            "大学", "检察院", "财险", "学校", "中国", "保险", "协会", "邮政储蓄", "方正证券", "服务部", "超市", "公安局", "学院",
            "部", "酒店", "中华联合", "组织", "商店", "置业", "邮政", "报社", "中学", "集团", "中银富登", "加油站", "财保", "工作室",
            "局", "人民财保", "教育机构", "批发部", "店", "小学", "办公室", "居委会", "中联重科", "单位", "监狱", "法院", "农信", "煤矿",
            "经营", "会社", "农场", "院", "旅馆", "电视台", "信用联社", "破产管理"
    };
    //上述复姓的正则匹配
    private static final String SURNAME_REG = Arrays.asList(SURNAME).stream().collect(Collectors.joining("|", "^(", ")"));
    private static final Pattern SURNAME_REG_PATTERN = Pattern.compile(SURNAME_REG);

    //少数名族 姓或名的正则匹配
    public static final String MINORITY_NAMES_REGEX = "哈木|阿卜杜拉|阿斯兰|阿日音|吾拉克|奥尔洛夫|依斯玛科夫|木塔尔|吾拉孜|阿布都热扎克|艾合力买买提|苏尔坚|赛福韦尔|克孜勒苏买买提明|麦麦提艾山|艾则孜|玉素甫江|阿不都|吾布力|麦麦提|买买提|图尔荪|苏莱曼|乌斯曼|库尔班|扎尔格拉|努尔加马|阿不都拉|米吉提|拜亚尔|索列|古来哈迪|穆罕默德|库尔别克|别克托格拉克|肯纳达尔|阿布杜热吾甫尔|艾合买提|买买提明|阿卜杜热吾甫尔|麦麦提明|阿地力|依布拉音|努尔加马尔|艾因先|阿孜儿勒|哈力比|西迪克|阿不都里米提|帕尔哈提|麦麦提吾斯曼|阿布力克木|买合苏提|阿卜杜热斯|巴依尔|阿不都沙拉木|索尔古丽|托合提|阿布力米提|奥斯曼|吾守尔|巴洛|阿卜杜日扎克|乌代|巴合提|木合塔尔|阿力木江|热依扎|阿布拉卡斯|伊斯逊|黄星|阿巴斯|阿吉艾尼|马特福尔|吐尔洪|努尔加玛尔|马木提|古力克买提|图雷|古力娜扎|拜合提亚尔|尼格买提|迪丽热巴|迪力木拉提|哈妮克孜|阿尔法|麦迪娜|买地娜|哈丽雅|艾尔肯|加奈那|那扎开提|买合木提";
    public static final Pattern MINORITY_NAMES_PATTERN = Pattern.compile(MINORITY_NAMES_REGEX);

    // 等xxx人的正则匹配
    private static final String ET_AL_NAMES_SUFFIX = "等(\\d+|[零一二三四五六七八九个十百千万]+)?人$";
    private static Pattern ET_AL_NAMES_PATTERN = Pattern.compile(ET_AL_NAMES_SUFFIX);

    public static final String STAR_FLAG = "*";
    public static final String DESENSITIZATION_TERM = String.join(StringUtils.EMPTY, Collections.nCopies(2, STAR_FLAG));

    public static String evaluate(String name) {
        return nameEncry(name);
    }

    /**
     * 脱敏人名
     *
     * @param name 人名
     * @return String  已脱敏的人名
     */
    public static String nameEncry(String name) {
        if (StringUtils.isBlank(name)) {
            return name;
        }
        //剔除空白字符
        String showName = name;

        name = replaceBlank(name);
        if (!name.matches(".*[.·・•].*")) {
            if (name.length() > 4 || (name.contains("银行") && name.length() == 4)) {
                // 等XX人处理逻辑
                String elStr = StrUtil.EMPTY;
                if (name.contains("等") && name.endsWith("人")) {
                    Matcher elMatcher = ET_AL_NAMES_PATTERN.matcher(name);
                    if (elMatcher.find()) {
                        String group = elMatcher.group();
                        if (name.endsWith(name)) {
                            elStr = group;
                            name = name.substring(0, name.lastIndexOf(group));
                            showName = nameEncryV2(name);

                        }
                    }
                }
                if (MINORITY_NAMES_PATTERN.matcher(name).find()) {
                    showName = nameEncryV2(name);
                }

                // ShowName 等XX人拼接
                if (StringUtils.isNotBlank(showName) && StringUtils.isNotBlank(elStr)) {

                    showName = showName + elStr;
                }
                return showName;
            }
            for (String s : SpecialName) {
                if (s.length() == 1 && name.length() > 3 && (name.contains(s))) {
                    return showName;
                }
                if (name.length() > 3 && (name.contains(s))) {
                    return showName;
                }
                if (name.length() == 3 && name.contains(s) && (s.length() > 1 && name.matches("^[某甲乙丙0-9A-ZxX\\*].*"))) {
                    return showName;
                }
            }
        }

        return nameEncryV2(name);
    }

    /**
     * 脱敏人名  +  少数民族姓名处理
     *
     * @param name 人名
     * @return String  已脱敏的人名
     */
    public static String nameEncryV2(String name) {
        if (StringUtils.isBlank(name = replaceBlank(name))) {
            return name;
        }
        if (name.length() > 4) {
            if (MINORITY_NAMES_PATTERN.matcher(name).find()) {
                return name.substring(0, 1) + DESENSITIZATION_TERM;
            } else {
                if ( name.matches(".*[.·・•].*")) {
                    return name.substring(0, 1) + DESENSITIZATION_TERM;
                }
                return name;
            }
        } else {
            if (SURNAME_REG_PATTERN.matcher(name).find()) {
                return name.substring(0, 2) + String.join(StringUtils.EMPTY, Collections.nCopies(name.length() - 2, STAR_FLAG));
            } else {
                return name.substring(0, 1) + String.join(StringUtils.EMPTY, Collections.nCopies(name.length() - 1, STAR_FLAG));
            }
        }
    }

    public static String replaceBlank(String str) {
        String dest = null;
        if (str == null) {
            return dest;
        } else {
            Matcher m = BLANK_CHAR_PATTERN.matcher(str);
            dest = m.replaceAll("");
            return dest;
        }
    }

    public static void main(String[] args) {
        System.out.println(evaluate("丁中国"));
    }
}
