package com.qcc.udf.risk_graph;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 风险关系对象
 * @date 2021/3/4
 */
public class RiskRelationObject {
    /**
     * 数据主键
     */
    @JSONField(name = "Id")
    private String id;

    /** 对象类型 */
    @JSONField(name = "Type")
    private String type;

    /** 风险对象名称 */
    @JSONField(name = "Object")
    private String Object;

    /** 风险对象keyno */
    @JSONField(name = "KeyNo")
    private String keyNo;

    /** 风险对象Org */
    @JSONField(name = "Org")
    private Integer org;

    /**
     * 风险维度cnt
     */
    @JSONField(name = "InfoList")
    private List<RiskRelationListEntity> infoList;
}
