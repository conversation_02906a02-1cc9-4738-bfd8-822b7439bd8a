package com.qcc.udf.risk_graph;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2021/3/5
 */
@Data
public class RiskRelationOutputEntity {

    /**
     * 数据主键
     */
    @JSONField(name = "Id")
    private String id;

    /**
     * keyNo1
     */
    @JSONField(name = "MainkeyNo")
    private String mainkeyNo;

    /**
     * name1
     */
    @JSONField(name = "MainName")
    private String mainName;

    /**
     * org1
     */
    @JSONField(name = "MainOrg")
    private int mainOrg;

    /**
     * type1:企业，人员，政府法院
     */
    @JSONField(name = "MainType")
    private int mainType = 1;

    /**
     * keyNo1
     */
    @JSONField(name = "SecondKeyNo")
    private String secondKeyNo;

    /**
     * name1
     */
    @JSONField(name = "SecondName")
    private String secondName;

    /**
     * org1
     */
    @JSONField(name = "SecondOrg")
    private int secondOrg;

    /**
     * type1:企业，人员，政府法院
     */
    @JSONField(name = "SecondType")
    private int secondType = 1;

    /**
     * 风险维度统计
     */
    @JSONField(name = "Count")
    private int count;

    /**
     * 指向标识
     */
    @JSONField(name = "ForwardFlag")
    private int forwardFlag;

    /**
     * 数据状态
     */
    @JSONField(name = "DataStatus")
    private int dataStatus = 1;

    /**
     * 风险维度详情
     */
    @JSONField(name = "InfoList")
    private List<RiskDataInfo> infoList;

    /**
     * 风险维度详情
     */
    @JSONField(name = "InfoList2")
    private List<RiskDataInfo2> infoList2;

    /**
     * 风险维度统计
     */
    @JSONField(name = "ShowFlag")
    private int showFlag = 1;

    /**
     * 风险维度名称
     */
    @JSONField(name = "WdNames")
    private String wdNames;

    public RiskRelationOutputEntity(String mainkeyNo, int mainType, String secondKeyNo, int secondtype) {
        this.mainkeyNo = mainkeyNo;
        this.mainType = mainType;
        this.secondKeyNo = secondKeyNo;
        this.secondType = secondtype;
    }


    public RiskRelationOutputEntity(String startid, String endid) {
        this.mainkeyNo = startid;
        this.secondKeyNo = endid;
    }


}
