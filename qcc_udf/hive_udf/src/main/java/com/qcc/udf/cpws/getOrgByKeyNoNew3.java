package com.qcc.udf.cpws;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

public class getOrgByKeyNoNew3 extends UDF {
    public int evaluate(String wd, String keyNo, String name) {
        if (StringUtils.isBlank(keyNo)) {
            if (name.length() > 4){
                if (wd.equals("失信") || wd.equals("限高") || wd.equals("被执行") || wd.equals("终本")
                        || wd.equals("限制出境") || wd.equals("股权冻结") || wd.equals("行政处罚") || wd.equals("环保处罚")){
                    if (name.contains("公司") || name.contains("企业") || name.contains("股份") || name.contains("有限") || name.contains("机构")
                            || name.contains("集团") || name.contains("基金") || name.contains("中心") || name.contains("村委会") || name.contains("居委会")
                            || name.contains("委员会") || name.contains("学校") || name.contains("旅行社") || name.contains("信用社") || name.contains("会")
                            || name.contains("小学") || name.contains("中学") || name.contains("大学") || name.contains("门诊") || name.contains("医院")
                            || name.contains("合作社") || name.contains("敬老院") || name.endsWith("场") || name.endsWith("厂") || name.endsWith("局")
                            || name.endsWith("委") || name.endsWith("店") || name.endsWith("房") || name.endsWith("沙龙") || name.endsWith("厅")
                            || name.endsWith("庄") || name.endsWith("县") || name.endsWith("所") || name.endsWith("商铺") || name.endsWith("部")
                            || name.endsWith("专柜") || name.endsWith("行") || name.endsWith("门市") || name.endsWith("食堂") || name.endsWith("队")
                            || name.endsWith("政府") || name.endsWith("超市") || name.endsWith("站") || name.endsWith("摊") || name.endsWith("农家乐")
                            || name.endsWith("室") || name.endsWith("区") || name.endsWith("市") || name.endsWith("村") || name.endsWith("圃")
                            || name.endsWith("处") || name.endsWith("浴池") || name.endsWith("馆") || name.endsWith("家") || name.endsWith("园")
                            || name.endsWith("业") || name.endsWith("基地") || name.endsWith("社") || name.endsWith("坊") || name.endsWith("楼")
                            || name.endsWith("城") || name.endsWith("餐饮") || name.endsWith("汇") || name.endsWith("户") || name.endsWith("档")
                            || name.endsWith("帮") || name.endsWith("岗") || name.endsWith("亭") || name.endsWith("铺") || name.endsWith("发艺")
                            || name.endsWith("组") ){
                        return -1;
                    }else {
                        return -2;
                    }
                }else{
                    return -1;
                }
            }else{
                return -2;
            }
        }

        int org = 0;
        if (keyNo.startsWith("s")) {
            org = 1;
        } else if (keyNo.startsWith("h")) {
            org = 3;
        } else if (keyNo.startsWith("t")) {
            org = 5;
        } else if (keyNo.startsWith("g") || keyNo.startsWith("x") || keyNo.startsWith("w") || keyNo.startsWith("j")) {
            org = 4;
        } else if (keyNo.startsWith("y")) {
            org = 7;
        } else if (keyNo.startsWith("o")) {
            org = 8;
        } else if (keyNo.startsWith("z")) {
            org = 9;
        } else if (keyNo.startsWith("p")) {
            org = 2;
        }
        return org;
    }

    public static void main(String[] args) {
        System.out.println(new getOrgByKeyNoNew3().evaluate("","", "大学合作123123"));
    }
}
