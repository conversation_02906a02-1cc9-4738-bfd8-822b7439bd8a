package com.qcc.udf.overseas;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.overseas.constant.ChangeHistoryConstant;
import com.qcc.udf.overseas.constant.Time;
import com.qcc.udf.overseas.constant.UsStateEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * 业务UDF（海外企业）获取企业名称变更历史信息（企业的最新名称以及曾用名变更记录）
 * ---------------------------------------------------------------------------------------------------------
 * add jar hdfs://ldh/data/hive/udf/qcc_udf.jar;
 * create temporary function GetActivityHistory as 'com.qcc.udf.overseas.GetActivityHistoryUDF';
 * ---------------------------------------------------------------------------------------------------------
 * select GetActivityHistory ('entName', 'activityHistory', 'source');
 * 结果: ['', '']
 */
public class GetActivityHistoryUDF extends UDF {

    /**
     * 获取企业名称变更历史信息（企业的最新名称以及曾用名变更记录）
     * @param entName 企业名称
     * @param activityHistory 变更历史信息
     * @param source 数据来源
     * @return 返回list 第一个元素为比对后的最新企业名，第二个元素为企业名变更历史信息（json字符串，包括变更类型/生效日期/变更前信息/变更后信息）
     */
    public List<String> evaluate(String entName, String activityHistory, String source) {
        List<String> changeList = Arrays.asList(entName, "");
        try {
            if (StringUtils.isNotBlank(source) && StringUtils.isNotBlank(entName) && StringUtils.isNotBlank(activityHistory)) {
                UsStateEnum usStateEnum = UsStateEnum.getStateEnumByName(source);
                switch (usStateEnum) {
                    case KENTUCKY:
                        changeList = compareAndGetChangeList(entName, activityHistory);
                        break;
                }
            }
        } catch (Exception e) {

        }
        return changeList;
    }

    private static List<String> compareAndGetChangeList(String entName, String activityHistory) {
        try {
            JSONArray changeHistoryArray = JSONObject.parseArray(activityHistory);
            if (changeHistoryArray != null && changeHistoryArray.size() > 0) {

                /**
                 * 步骤
                 * 1 遍历jsonarray 获取filiing='Amendment subsequent name'的记录，并按照EffectiveDate的时间戳递增排序
                 * 2 根据1的结果，组装为符合要求的json对象，放入jsonarray中
                 * 3 拿到最后一个Referenced的值，即为entname字段的值
                 */
                Map<Long, JSONObject> map = new TreeMap<>();
                for (int i = 0; i < changeHistoryArray.size(); i++) {
                    JSONObject changeHistory = changeHistoryArray.getJSONObject(i);
                    Long effectDate = CommonUtil.getTimeStampLongFromDateField(changeHistory.getString("EffectiveDate"), Time.Formatter_T1);
                    String type = changeHistory.getString("Filing").trim();
                    if (effectDate != null && (
                            StringUtils.equalsIgnoreCase("Amendment subsequent name", type) ||
                                    StringUtils.equalsIgnoreCase("Amendment previous name", type))) {
                        map.put(effectDate, changeHistory);
                    }
                }

                String currentName = entName;
                JSONArray changeResultArray = new JSONArray();
                for (Long item : map.keySet()) {
                    JSONObject sourceJson = map.get(item);
                    String type = sourceJson.getString("Filing");
                    String itemName = StringUtils.upperCase(sourceJson.getString("Referenced"));

                    if (StringUtils.isNotBlank(itemName)) {
                        JSONObject json = new JSONObject();
                        switch (type) {
                            case "Amendment subsequent name":
                                json.put(ChangeHistoryConstant.ChangeType, "Company Name Change");
                                json.put(ChangeHistoryConstant.ChangeDate, item);
                                json.put(ChangeHistoryConstant.ChangeBefore, currentName);
                                json.put(ChangeHistoryConstant.ChangeAfter, itemName);
                                changeResultArray.add(json);
                                currentName = itemName;
                                break;
                            case "Amendment previous name":
                                json.put(ChangeHistoryConstant.ChangeType, "Company Name Change");
                                json.put(ChangeHistoryConstant.ChangeDate, item);
                                json.put(ChangeHistoryConstant.ChangeBefore, itemName);
                                json.put(ChangeHistoryConstant.ChangeAfter, currentName);
                                changeResultArray.add(json);
                                break;
                        }
                    }
                }
                return changeResultArray.size() > 0 ?
                        Arrays.asList(currentName, changeResultArray.toString()) :
                        Arrays.asList(entName, "");
            }
        } catch (Exception e) {
        }
        return Arrays.asList(entName, "");
    }

    public static void main(String[] args) {
        String entName = "aaaa";
        String source = "KY";
        String activityHistory = "[{\"EffectiveDate\": \"10/7/1976\", \"Referenced\": \"PINO'S ITALIAN RESTAURANT, INC.\", \"Filing\": \"Amendment subsequent name\", \"FileDate\": \"10/7/1976\"}]";
        List<String> resList = new GetActivityHistoryUDF().evaluate(entName, activityHistory, source);

        JSONArray jsonArray = JSONArray.parseArray(resList.get(1));
        System.out.println(jsonArray);
    }
}
