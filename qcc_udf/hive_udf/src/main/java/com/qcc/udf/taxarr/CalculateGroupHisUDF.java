package com.qcc.udf.taxarr;

import com.alibaba.fastjson.JSON;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 欠税公告 历史计算 udf
 * 同发布机关 不同发布日期 依次两两比较 返回少的
 * <AUTHOR>
 **/
public class CalculateGroupHisUDF extends UDF {

    public String evaluate(List<String> groupIds) {
        Map<String, List<String>> map = new TreeMap<>();
        if (!groupIds.isEmpty()) {
            for (String groupId : groupIds) {
                map.put(groupId.split("-")[0], Arrays.asList(groupId.split("-")[1].split(",").clone()));
            }
        }
        List<String> kafkaStr = new ArrayList<>();
        List<List<String>> mapList = map.values().stream().filter(e -> !e.isEmpty()).collect(Collectors.toList());
        for (int i = 1; i < mapList.size(); i++) {
            List<String> before = mapList.get(i - 1);
            List<String> after = mapList.get(i);
            for (String b : before) {
                if (!after.contains(b)) {
                    kafkaStr.add(b);
                }
            }
        }
        return JSON.toJSONString(kafkaStr);
    }

    public static void main(String[] args) {
        CalculateGroupHisUDF r = new CalculateGroupHisUDF();
        ArrayList<String> ss = new ArrayList<>();
        ss.add("\"20211109-bf0c0ce6c228c1275871e52eff387cfc,d4bdfc6bc6434910a72633e4bd2df596,addc7ef9246bee9a5e0c67282dee0442,aa4cfe714644115542603873edf01648,370c73df0a708f90e4c6965df005a741,6b15cd08c8141a5f73e6c5c3f45be10a");
//        ss.add("20190528-50d469515ba4d2e0073f0e5cfec73b37,ce313d222cda4a2f1e3e1f154dfea946,b231ad0b90d01012e0bf374c8d007243,0edc8baf674904336073bf3a0afaff10");
        ss.add("20201106-fa378cb33cfb99d256a490b38f23f5f4");
        System.out.println(r.evaluate(ss));
    }
}
