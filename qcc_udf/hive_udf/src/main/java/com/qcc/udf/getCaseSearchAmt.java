package com.qcc.udf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * @Auther: shengdong
 * @Date: 2021/02/20 11:52
 * @Description:
 */
public class getCaseSearchAmt extends UDF {
    public static String evaluate(String amtInfo, String keyNo) {
        String result = "";

        if (StringUtils.isEmpty(amtInfo)){
            return "";
        }
        try {
            JSONObject jsonObject = JSON.parseObject(amtInfo);
            if (jsonObject.containsKey(keyNo)) {
                JSONObject amtObject = jsonObject.getJSONObject(keyNo);
                result = amtObject.getOrDefault("Amt", "").toString();
            }

        } catch (Exception e) {

        }
        return result;
    }

    public static void main(String[] args) {
        String amtInfo = "{\"596ed2df80d487dc426262470c06207a\":{\"Type\":\"案件金额\",\"Amt\":\"50.00\",\"IsValid\":\"1\"}}";
        System.out.println(evaluate(amtInfo, "596ed2df80d487dc426262470c06207a"));
    }
}
