package com.qcc.udf.tag.dimtype;

import com.google.common.collect.Lists;
import com.qcc.udf.tag.TagEntity;
import com.qcc.udf.tag.keywordEnum.ZLKeywordMatchEnum;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 产品质量问题-相关维度关键词
 */
public enum ZLDimtypeKeywordEnum {
    //行政处罚
    XING_ZHENG_CHU_FA(DimensionEnum.XING_ZHENG_CHU_FA, new ZLKeywordMatchEnum[]{ZLKeywordMatchEnum.WZSC, ZLKeywordMatchEnum.WZ_ZHI_ZAO, ZLKeywordMatchEnum.WZ_ZHI_ZUO, ZLKeywordMatchEnum.CPZH, ZLKeywordMatchEnum.JYBHG, ZLKeywordMatchEnum.JMWLZL,ZLKeywordMatchEnum.JIA, ZLKeywordMatchEnum.XJXC}),
    //食品安全
    SHI_PING_AN_QUAN(DimensionEnum.SHI_PING_AN_QUAN, new ZLKeywordMatchEnum[]{ZLKeywordMatchEnum.JGBHG}),
    //产品召回
    CHAN_PING_ZHAO_HUI(DimensionEnum.CHAN_PING_ZHAO_HUI, null),
    //抽查检查
    SPOT_CHECK(DimensionEnum.SPOT_CHECK, new ZLKeywordMatchEnum[]{ZLKeywordMatchEnum.WZSC, ZLKeywordMatchEnum.WZ_ZHI_ZAO, ZLKeywordMatchEnum.WZ_ZHI_ZUO, ZLKeywordMatchEnum.JMWLZL, ZLKeywordMatchEnum.XJXC, ZLKeywordMatchEnum.CPJYBHG}),
    //双随机抽查
    DOUBLE_RANDOM_CHECK(DimensionEnum.DOUBLE_RANDOM_CHECK, new ZLKeywordMatchEnum[]{ZLKeywordMatchEnum.BHG, ZLKeywordMatchEnum.JMWL, ZLKeywordMatchEnum.DEFAULT}),
    //产品抽查
    //药品抽检
    //假冒化妆品
    //严重违法
    YAN_ZHONG_WEI_FA(DimensionEnum.YAN_ZHONG_WEI_FA, new ZLKeywordMatchEnum[]{ZLKeywordMatchEnum.ZDZL_AQSG}),

    ;


    private DimensionEnum dimensionEnum;
    private ZLKeywordMatchEnum[] zLKeywordMatchEnum;

    ZLDimtypeKeywordEnum(DimensionEnum dimensionEnum, ZLKeywordMatchEnum[] zLKeywordMatchEnum) {
        this.dimensionEnum = dimensionEnum;
        this.zLKeywordMatchEnum = zLKeywordMatchEnum;
    }

    private static final Map<DimensionEnum, ZLKeywordMatchEnum[]> lookup = new LinkedHashMap<>();

    static {
        EnumSet.allOf(ZLDimtypeKeywordEnum.class).stream().forEach(e -> {
                    lookup.put(e.dimensionEnum, e.zLKeywordMatchEnum);
                }
        );
    }

    public static List<TagEntity> find(DimensionEnum dimensionEnum, String keyword) {
        List<TagEntity> tagEntities = new LinkedList<>();
        if (DimensionEnum.CHAN_PING_ZHAO_HUI == dimensionEnum) {
            tagEntities.add(ZLKeywordMatchEnum.CPZH.getTagEnum().getTagEntity());
            return tagEntities;
        }
        ZLKeywordMatchEnum[] zLKeywordMatchEnums = getZLKeywordMatchEnums(dimensionEnum);
        if (zLKeywordMatchEnums == null) {
            return tagEntities;
        }
        for (ZLKeywordMatchEnum kmEnum : zLKeywordMatchEnums) {
            if (ZLKeywordMatchEnum.DEFAULT == kmEnum){
                continue;
            }
            if (checkLikeRole(keyword, kmEnum.getKeyword())) {
                tagEntities.add(kmEnum.getTagEnum().getTagEntity());
            }
        }
        if (tagEntities.size() > 0) {
            return tagEntities;
        }
        boolean flag = Arrays.stream(zLKeywordMatchEnums).anyMatch(e -> e == ZLKeywordMatchEnum.DEFAULT);
        if (flag) {
            return Lists.newArrayList(ZLKeywordMatchEnum.DEFAULT.getTagEnum().getTagEntity());
        }
        return tagEntities;
    }


    public static ZLKeywordMatchEnum[] getZLKeywordMatchEnums(DimensionEnum dimensionEnum) {
        return lookup.get(dimensionEnum);
    }

    private static boolean checkLikeRole(String role, String regex) {
        Pattern p = Pattern.compile(regex);
        Matcher matcher = p.matcher(role);
        return matcher.find();
    }

    public static void main(String[] args) {
        DimensionEnum dimensionEnum = DimensionEnum.XING_ZHENG_CHU_FA;
        String keyword = "虚假宣传";
        System.out.println(find(dimensionEnum, keyword));
    }
}
