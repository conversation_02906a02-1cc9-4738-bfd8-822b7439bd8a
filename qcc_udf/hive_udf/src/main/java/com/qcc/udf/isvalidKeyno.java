
package com.qcc.udf;


import org.apache.hadoop.hive.ql.exec.UDF;
import org.apache.hadoop.hive.ql.metadata.HiveException;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class isvalidKeyno extends UDF {
    /**
     * 判断keyno是否符合规范 ^[a-z0-9][a-f0-9]{31}$
     *
     * @param
     * @throws HiveException
     * @returnmvn
     */
    public static boolean evaluate(String keyNo) {
        boolean flag = false;
        Pattern p = Pattern.compile("^[a-z0-9][a-f0-9]{31}$");
        if (keyNo != null) {
            Matcher mc = p.matcher(keyNo);
            flag = mc.matches();
        }
        return flag;
    }
/*
    public static void main(String[] args) {
        String k = "a7d3f023c28280180817c1d8h12ec657";
        System.out.println(evaluate(k));
    }*/

}
