package com.qcc.udf.cpws;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 清洗代理客户的工商信息
 * 字段格式化
 */
public class AgentCleanCompany extends UDF {
    public static void main(String[] args) {



        System.out.println(evaluate("{\"Province\":\"广东省\",\"City\":\"东莞市\",\"County\":\"\"}",
                "[{\"Key\":3,\"Value\":\"2\"},{\"Key\":5,\"Value\":\"兴京华\"},{\"Key\":6,\"Value\":\"{\\\"KeyNo\\\":\\\"pd38a5ece23499a6bf72b771f5d85a62\\\",\\\"Name\\\":\\\"戴建宁\\\"}\"},{\"Key\":7,\"Value\":\"[{\\\"KeyNo\\\":\\\"pd38a5ece23499a6bf72b771f5d85a62\\\",\\\"Name\\\":\\\"戴建宁\\\"}]\"},{\"Key\":17,\"Value\":\"[{\\\"e\\\":\\\"<EMAIL>\\\",\\\"s\\\":\\\"2022\\\"}]\"},{\"Key\":18,\"Value\":\"[{\\\"K\\\":\\\"pd38a5ece23499a6bf72b771f5d85a62\\\",\\\"N\\\":\\\"戴建宁\\\",\\\"T\\\":{\\\"IsAC\\\":\\\"1\\\",\\\"IsB\\\":\\\"1\\\",\\\"BPL\\\":\\\"100%\\\",\\\"IsBP\\\":\\\"1\\\"}}]\"},{\"Key\":19,\"Value\":\"100\"},{\"Key\":22,\"Value\":\"[{\\\"A\\\":\\\"***********\\\",\\\"K\\\":\\\"45c0fa3e8a33fe8d\\\",\\\"C\\\":\\\"2\\\"}]\"},{\"Key\":24,\"Value\":\"{\\\"Lng\\\":\\\"114.228493\\\",\\\"Lat\\\":\\\"22.833201\\\"}\"},{\"Key\":25,\"Value\":\"{\\\"c\\\":2,\\\"s\\\":\\\"2023\\\"}\"},{\"Key\":26,\"Value\":\"[{\\\"k\\\":\\\"***********\\\",\\\"v\\\":\\\"1\\\"},{\\\"k\\\":\\\"***********\\\",\\\"v\\\":\\\"1\\\"}]\"},{\"Key\":27,\"Value\":\"{\\\"Bank\\\":\\\"工商银行东莞清溪支行\\\",\\\"BankAccount\\\":\\\"2010028909200127887\\\"}\"},{\"Key\":28,\"Value\":\"441900\"},{\"Key\":32,\"Value\":\"[{\\\"Ac\\\":\\\"ensh.22\\\",\\\"An\\\":\\\"传统行业\\\",\\\"Bc\\\":\\\"ensh.22.13\\\",\\\"Bn\\\":\\\"化学工业\\\",\\\"Cc\\\":\\\"ensh.22.13.6\\\",\\\"Cn\\\":\\\"塑料制品\\\"}]\"},{\"Key\":33,\"Value\":\"{\\\"s\\\":3,\\\"y\\\":\\\"\\\"}\"},{\"Key\":34,\"Value\":\"80\"},{\"Key\":35,\"Value\":\"增值税一般纳税人\"},{\"Key\":37,\"Value\":\"{\\\"TotalAmt\\\":10.857653}\"},{\"Key\":47,\"Value\":\"微型企业\"},{\"Key\":49,\"Value\":\"{\\\"Ac\\\":\\\"QCC02\\\",\\\"An\\\":\\\"原材料\\\",\\\"Bc\\\":\\\"QCC0201\\\",\\\"Bn\\\":\\\"化工\\\",\\\"Cc\\\":\\\"QCC020104\\\",\\\"Cn\\\":\\\"塑料\\\",\\\"Dc\\\":\\\"\\\",\\\"Dn\\\":\\\"\\\"}\"},{\"Key\":52,\"Value\":\"{\\\"organizational_code\\\":\\\"*********\\\",\\\"economic_code\\\":\\\"************\\\",\\\"es_code\\\":\\\"*********,************,002006\\\"}\"},{\"Key\":55,\"Value\":\"2023\"},{\"Key\":75,\"Value\":\"[{\\\"CT\\\":0,\\\"D\\\":\\\"5\\\",\\\"Y\\\":\\\"2021\\\",\\\"S\\\":2},{\\\"CT\\\":0,\\\"D\\\":\\\"4\\\",\\\"Y\\\":\\\"2022\\\",\\\"S\\\":2},{\\\"CT\\\":0,\\\"D\\\":\\\"2\\\",\\\"Y\\\":\\\"2023\\\",\\\"S\\\":2}]\"},{\"Key\":78,\"Value\":\"{\\\"Name\\\":\\\"兴京华塑料\\\"}\"},{\"Key\":79,\"Value\":\"{\\\"Ac\\\":\\\"16\\\",\\\"An\\\":\\\"基础化工\\\",\\\"Bc\\\":\\\"1604\\\",\\\"Bn\\\":\\\"橡胶和塑料制品业\\\",\\\"Cc\\\":\\\"160402\\\",\\\"Cn\\\":\\\"塑料制品业\\\",\\\"Dc\\\":\\\"16040204\\\",\\\"Dn\\\":\\\"泡沫塑料制造\\\"}\"},{\"Key\":81,\"Value\":\"少于50人\"},{\"Key\":10037,\"Value\":\"{\\\"TotalAmt\\\":10.857653}\"}]",
                "{\"IndustryCode\":\"C\",\"SubIndustryCode\":29,\"Industry\":\"制造业\",\"SubIndustry\":\"橡胶和塑料制品业\"}",
                "在业",
                "{\"IndustryCode\":\"C\",\"Industry\":\"制造业\",\"SubIndustryCode\":\"29\",\"SubIndustry\":\"橡胶和塑料制品业\",\"MiddleCategoryCode\":\"292\",\"MiddleCategory\":\"塑料制品业\",\"SmallCategoryCode\":null,\"SmallCategory\":null}"
                , "{\"OperType\":1,\"OperList\":[{\"Org\":2,\"KeyNo\":\"pd38a5ece23499a6bf72b771f5d85a62\",\"Name\":\"戴建宁\",\"HasImage\":false,\"CompanyCount\":4}]}",
                "{\"$numberLong\":\"1303660800\"}"
        ));
    }

    public static String evaluate(String area, String commonList, String Industry, String shortStatus, String industryV3, String oper, String startDate) {

        JSONObject item = new JSONObject();
        //[QBD-46039] 【清洗】代理客户-新增筛选项和字段
        String city = "";
        if (StringUtils.isNotEmpty(commonList)) {
            List<JSONObject> commonOut = JSON.parseArray(commonList, JSONObject.class);

            for (JSONObject jsonObject : commonOut) {
                if (jsonObject.getInteger("Key") == 28) {
                    city = jsonObject.getString("Value");
                }
            }
        }
        if (StringUtils.isNotEmpty(Industry)) {
            JSONObject in = JSON.parseObject(Industry);
            String industry = in.getString("IndustryCode");
            item.put("Industry", industry);
        }
        if (StringUtils.isNotEmpty(area)) {
            JSONObject areaJson = JSON.parseObject(area);
            item.put("Province", BaseProvinceUtil.getProvinceCode(areaJson.getString("Province")));
        }

        if (industryV3 != null) {
            JSONObject inV3 = JSON.parseObject(industryV3);
            TreeSet set = new TreeSet();
            if (StringUtils.isNotEmpty(inV3.getString("SubIndustryCode"))) {
                set.add(inV3.getString("SubIndustryCode"));
            }
            if (StringUtils.isNotEmpty(inV3.getString("MiddleCategoryCode"))) {
                set.add(inV3.getString("MiddleCategoryCode"));
            }
            if (StringUtils.isNotEmpty(inV3.getString("SmallCategoryCode"))) {
                set.add(inV3.getString("SmallCategoryCode"));
            }
            item.put("SubIndustryCode", String.join(",", set));
        }
        if (StringUtils.isNotEmpty(city) && city.length() > 4) {
            city = city.substring(0, 4) + "," + city;
        }
        item.put("AreaCode", city);
        item.put("ShortStatus", getCompanyShortStatusCode(shortStatus, true) + "");
        if (StringUtils.isNotEmpty(oper)) {
            JSONObject operJson = JSON.parseObject(oper);
            JSONArray operList = operJson.getJSONArray("OperList");
            Set<String> operName = operList.stream().map(x -> {
                JSONObject object = (JSONObject) x;
                return object.getString("Name");
            }).collect(Collectors.toSet());
            item.put("Oper", String.join(",", operName));
        }
        if (StringUtils.isNotEmpty(startDate)) {
            //{"$numberLong":"1375891200"}
            item.put("regdate", invertLongToDateString(JSON.parseObject(startDate).getLongValue("$numberLong")));
        }

        return item.toJSONString();
    }

    public static String invertLongToDateString(Long date) {
        String returnDate = null;
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Long time = date * 1000;
            returnDate = format.format(time);
        } catch (Exception e) {
        }
        return returnDate;
    }

    /**
     * getCompanyShortStatusCode
     *
     * @param status
     * @param isFormate
     * @return
     */
    public static Integer getCompanyShortStatusCode(String status, Boolean isFormate) {
        String shortStatus = isFormate ? getCompanyShortStatus(status) : status;
        return ShortStatusCodeList.getOrDefault(shortStatus, 0);
    }

    private static final Map<String, Integer> ShortStatusCodeList = new HashMap<String, Integer>() {
        {
            this.put("在业", 10);
            this.put("正常", 10);
            this.put("存续", 20);
            this.put("筹建", 30);
            this.put("清算", 40);
            this.put("迁入", 50);
            this.put("迁出", 60);
            this.put("停业", 70);
            this.put("歇业", 75);
            this.put("撤销", 80);
            this.put("责令关闭", 85);
            this.put("除名", 87);
            this.put("吊销", 90);
            this.put("注销", 99);
            this.put("仍注册", 92);
            this.put("其他", 93);
            this.put("已告解散", 94);
            this.put("已终止营业地点", 95);
            this.put("不再是独立的实体", 96);
            this.put("休止活动", 97);
            this.put("废止", 100);
            this.put("废止清算完结", 101);
            this.put("废止许可", 102);
            this.put("废止许可完结", 103);
            this.put("废止认许", 104);
            this.put("废止认许完结", 105);
            this.put("接管", 106);
            this.put("撤回认许", 107);
            this.put("撤回认许完结", 108);
            this.put("撤销设立", 110);
            this.put("撤销完结", 111);
            this.put("撤销无需清算", 112);
            this.put("撤销许可", 113);
            this.put("撤销认许", 114);
            this.put("撤销认许完结", 115);
            this.put("核准报备", 116);
            this.put("核准设立", 117);
            this.put("设立但已解散", 118);
            this.put("核准许可报备", 119);
            this.put("核准许可登记", 120);
            this.put("核准认许", 121);
            this.put("清理", 122);
            this.put("清理完结", 123);
            this.put("破产", 124);
            this.put("破产清算完结", 125);
            this.put("破产程序终结", 126);
            this.put("解散", 127);
            this.put("解散清算完结", 128);
            this.put("重整", 129);
            this.put("合并解散", 130);
            this.put("终止破产", 131);
            this.put("涂销破产", 132);
            this.put("核准许可", 133);
            this.put("核准登记", 134);
            this.put("分割解散", 135);
            this.put("废止登记完结", 136);
            this.put("废止登记", 137);
            this.put("撤销登记完结", 138);
            this.put("撤销登记", 139);
            this.put("撤回登记完结", 140);
            this.put("撤回登记", 141);
            this.put("设立失败", 142);
            this.put("撤回", 143);
        }
    };


    /**
     * getCompanyShortStatus
     *
     * @param status
     * @return
     */
    public static String getCompanyShortStatus(String status) {
        if (StringUtils.isEmpty(status) || StringUtils.isBlank(status)) {
            return "";
        }
        status = removeBlank(CommonUtil.full2Half(status));
        String result = "";
        if ((Stream.of("被吊销", "吊销,未注销", "吊销,已注销", "吊销后注销", "吊销未注销", "已吊销", "吊销企业", "暂时吊销").anyMatch(status::contains) || status.equals("吊销") || (status.contains("吊销") && status.contains("未注销"))) && !status.contains("已注销")) {
            result = "吊销";
        } else if (Stream.of("迁出注销", "迁移异地", "迁出", "迁往市外").anyMatch(status::contains)) {
            result = "迁出";
        } else if (Stream.of("已注销", "注吊销", "注销", "注销登记中", "吊销已注销", "注销企业", "企业已注销").anyMatch(status::contains)) {
            result = "注销";
        } else if (status.contains("筹建")) {
            result = "筹建";
        } else if (status.contains("歇业")) {
            result = "歇业";
        } else if (Stream.of("存-(在营、开业、在册)", "存续").anyMatch(status::contains)) {
            result = "存续";
        } else if (status.contains("迁入")) {
            result = "迁入";
        } else if (status.contains("清算")) {
            result = "清算";
        } else if (status.contains("停业")) {
            result = "停业";
        } else if (Stream.of("已撤销登记", "已撤销企业", "撤消登记", "撤销").anyMatch(status::contains)) {
            result = "撤销";
        } else if (Stream.of("开业登记中", "在营(开业)企业", "开业", "在业", "在册", "在营(开业)", "正常执业", "登记成立", "在营", "正常在业", "已开业", "开业/正常经营").anyMatch(status::contains)) {
            result = "在业";
        } else if (isSameDBC(status, "正常") || status.contains("待注销")) {
            result = "正常";
        } else if (status.contains("责令关闭")) {
            result = "责令关闭";
        } else if (isSameDBC(status, "除名")) {
            result = "除名";
        } else {
            result = "其他";
        }
        return result;
    }

    public static String removeBlank(String source) {
        return StringUtils.isEmpty(source) ? "" : source.replace(" ", "").replace("　", "");
    }

    public static Boolean isSameDBC(String source, String name) {
        return full2Half(source).replace(" ", "").toUpperCase().equals(full2Half(name).replace(" ", "").toUpperCase());
    }

    public static String full2Half(String input) {
        if (StringUtils.isEmpty(input)) {
            return "";
        } else {
            char[] c = input.toCharArray();

            for (int i = 0; i < c.length; ++i) {
                if (c[i] == 12288) {
                    c[i] = ' ';
                } else if (c[i] > '\uff00' && c[i] < '｟') {
                    c[i] -= 'ﻠ';
                }
            }

            return new String(c);
        }
    }

}
