package com.qcc.udf;

import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * @Auther: liulh
 * @Date: 2020/6/2 14:54
 * @Description:
 * 获取两个字符串中相同的数据
 * @param "param1" 字符串1，以逗号分隔的多个字符串组成的数据
 * @param "param2" 字符串2，以逗号分隔的多个字符串组成的数据
 * @return 计算结果
 */
public class getSameCaseSearchId extends UDF {
    public static String  evaluate(String param1, String param2) {
        String result = "";
        if (StringUtils.isEmpty(param1) || StringUtils.isEmpty(param2)){
            return "";
        }
        String[] arr1 = param1.split(",");
        String[] arr2 = param2.split(",");

        for (String sub1 : arr1){
            for (String sub2 : arr2){
                if (sub1.equals(sub2)){
                    result = result.concat(",").concat(sub1);
                }
            }
        }

        result = result.length() > 0 ? result.substring(1) : result;

        return result;
    }
}
