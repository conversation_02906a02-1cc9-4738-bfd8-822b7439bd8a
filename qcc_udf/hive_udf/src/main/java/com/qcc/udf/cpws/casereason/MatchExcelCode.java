package com.qcc.udf.cpws.casereason;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Charsets;
import com.google.common.io.Resources;
import com.qcc.udf.casesearch_v3.role.CheckPartyRoleUtil;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 匹配Excel中的code，读取标签
 *
 * <AUTHOR>
 */
public class MatchExcelCode {

    private static final ThreadLocal<Map<String, List<CaseLabel>>> LOCAL_VARS = new ThreadLocal<>();
    private static final ThreadLocal<List<CaseLabel>> LOCAL_LIST_VARS = new ThreadLocal<>();

    public static void main(String[] args) {
        List<String> list = new ArrayList<>();
        String code1 = "B,B04,B0401,B040124,*********";
        // String code2 = "B,B05,B0502,B050216";
        list.add(code1);
        // list.add(code2);

        String excelList2 = null;
        for (String s : list) {
            // excelList2 = handTxt(s);
            if (excelList2 != null) {
                System.out.println("excelList2" + excelList2);
            }
        }
    }

    /**
     * txt解析读取
     */
    public static String handTxt(String code) {
        Map<String, List<CaseLabel>> collect = LOCAL_VARS.get();
        List<CaseLabel> listRisk = null;

        if (collect != null && collect.size() > 0) {
            listRisk = collect.get(code);
        } else {
            List<CaseLabel> list = new ArrayList<>();
            try {
                List<String> lines;
                lines = Resources.asCharSource(
                        Resources.getResource("/case_reason_sort.txt"), Charsets.UTF_8).readLines();


                for (String str : lines) {
                    String[] firstAndSecond = str.substring(0, str.indexOf('"')).split(",");
                    String first = firstAndSecond.length > 0 ? firstAndSecond[0].trim() : "";
                    String second = firstAndSecond.length > 1 ? firstAndSecond[1].trim() : "";
                    CaseLabel obj = new CaseLabel();
                    obj.setFirstLabel(first);
                    obj.setSecondLabel(second);
                    String substring = str.substring(str.indexOf('"')).replaceAll("\"", "");
                    obj.setId(substring);
                    list.add(obj);
                }
                collect = list.stream().collect(Collectors.groupingBy(it -> it.getId()));
                LOCAL_VARS.set(collect);

                listRisk = collect.get(code);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        String jsonRisk = null;
        if (listRisk != null && listRisk.size() > 0) {
            jsonRisk = JSONObject.toJSONString(listRisk);
        }
        return jsonRisk;
    }

    /**
     * txt解析读取
     */
    public static List<CaseLabel> getTxtList() {
        List<CaseLabel> listRisk = LOCAL_LIST_VARS.get();

        if (listRisk != null && listRisk.size() > 0) {
            return listRisk;
        }
        listRisk = new ArrayList<>();
        InputStream isr = null;
        BufferedReader br = null;
        try {
            isr = CheckPartyRoleUtil.class.getResourceAsStream("/case_reason_sort.txt");
            br = new BufferedReader(new InputStreamReader(isr));
        } catch (Exception e) {
            e.printStackTrace();
        }

        String line = "";
        try {
            while ((line = br.readLine()) != null) {
                if (line.indexOf('"') < 0) {
                    continue;
                }
                String[] firstAndSecond = line.substring(0, line.indexOf('"')).split(",");
                String first = firstAndSecond.length > 0 ? firstAndSecond[0].trim() : "";
                String second = firstAndSecond.length > 1 ? firstAndSecond[1].trim() : "";
                CaseLabel obj = new CaseLabel();
                obj.setFirstLabel(first);
                obj.setSecondLabel(second);
                String substring = line.substring(line.indexOf('"')).replaceAll("\"", "");
                obj.setId(substring);
                listRisk.add(obj);
            }
            LOCAL_LIST_VARS.set(listRisk);

        } catch (Exception ex) {
        } finally {
            try {
                br.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                isr.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return listRisk;
    }
}
