package com.qcc.udf.casesearch_v3.test;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import com.qcc.udf.casesearch_v3.entity.input.CaseRoleEntity;
import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.output.CaseRoleSort;
import com.qcc.udf.casesearch_v3.entity.output.LawSuitV3OutputEntity;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import org.apache.commons.collections.CollectionUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2021年06月24日 20:15
 */
public class CaseDebtTestUDF extends UDF {
    public static String evaluate(String json) {
        LawSuitV3OutputEntity entity = JSON.parseObject(json, LawSuitV3OutputEntity.class);
        Set<String> noticeSet = new HashSet<>();
        if( entity.getAmtInfo() != null && entity.getCaseRole() != null){
            Set<String> excludeCaseRole = excludeCaseRole(entity.getCaseRole());
            entity.getAmtInfo().forEach((k,v)->{
                if("案件金额".equals(v.getType()) && excludeCaseRole.contains(k)){
                    noticeSet.add(k);
                }
            });
        }

        Map<String,Object> out = new HashMap<>();
        out.put("size",noticeSet.size());
        out.put("keySet",JSON.toJSONString(noticeSet));

        return JSON.toJSONString(out);
    }

    public static void main(String[] args) {
        String json = "[{\"AmtInfo\":{\"0c82966e4015353be8fbe3c8581f8678\":{\"Amt\":\"48810278.71\",\"IsValid\":\"1\",\"Type\":\"案件金额\"},\"522eaea91e9023281737b0f71b4042c2\":{\"Amt\":\"48810278.71\",\"IsValid\":\"1\",\"Type\":\"案件金额\"},\"9d2d9a41a60273868879579f68e561c1\":{\"Amt\":\"48810278.71\",\"IsValid\":\"1\",\"Type\":\"案件金额\"},\"a385c097ccfa88c0ae833b380cb54d97\":{\"Amt\":\"48810278.71\",\"IsValid\":\"1\",\"Type\":\"案件金额\"},\"aafb85c76d8bac7e58821063d3ca2f5c\":{\"Amt\":\"48810278.71\",\"IsValid\":\"1\",\"Type\":\"案件金额\"},\"da02a5dd835d417655f66fc9f5fabd73\":{\"Amt\":\"48810278.71\",\"IsValid\":\"1\",\"Type\":\"案件金额\"},\"p3ea565ae4b4be51522322684be80d09\":{\"Amt\":\"48810278.71\",\"IsValid\":\"1\",\"Type\":\"案件金额\"},\"p3ff79415471e171567e91bc001c0f01\":{\"Amt\":\"48810278.71\",\"IsValid\":\"1\",\"Type\":\"案件金额\"}},\"AnNoList\":\"（2016）粤0104民初9036号\",\"AnnoCnt\":1,\"CaseCnt\":1,\"CaseName\":\"平安银行股份有限公司广州花园支行与广州市烨盈电器贸易有限公司,广州市焜建贸易有限公司,广州市睿隽商贸有限公司等合同纠纷的案件\",\"CaseReason\":\"合同纠纷\",\"CaseRole\":\"[{\\\"D\\\":\\\"一审原告\\\",\\\"N\\\":\\\"0c82966e4015353be8fbe3c8581f8678\\\",\\\"O\\\":0,\\\"P\\\":\\\"平安银行股份有限公司广州花园支行\\\",\\\"R\\\":\\\"原告\\\",\\\"RL\\\":[{\\\"LR\\\":\\\"8\\\",\\\"R\\\":\\\"原告\\\",\\\"T\\\":\\\"一审\\\"}]},{\\\"D\\\":\\\"一审被告\\\",\\\"N\\\":\\\"da02a5dd835d417655f66fc9f5fabd73\\\",\\\"O\\\":0,\\\"P\\\":\\\"广州市烨盈电器贸易有限公司\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"LR\\\":\\\"9\\\",\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"}]},{\\\"D\\\":\\\"一审被告\\\",\\\"N\\\":\\\"9d2d9a41a60273868879579f68e561c1\\\",\\\"O\\\":0,\\\"P\\\":\\\"广州市焜建贸易有限公司\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"LR\\\":\\\"9\\\",\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"}]},{\\\"D\\\":\\\"一审被告\\\",\\\"N\\\":\\\"a385c097ccfa88c0ae833b380cb54d97\\\",\\\"O\\\":0,\\\"P\\\":\\\"广州市睿隽商贸有限公司\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"LR\\\":\\\"9\\\",\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"}]},{\\\"D\\\":\\\"一审被告\\\",\\\"N\\\":\\\"p3ea565ae4b4be51522322684be80d09\\\",\\\"O\\\":2,\\\"P\\\":\\\"李建华\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"LR\\\":\\\"9\\\",\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"}]},{\\\"D\\\":\\\"一审被告\\\",\\\"N\\\":\\\"p3ff79415471e171567e91bc001c0f01\\\",\\\"O\\\":2,\\\"P\\\":\\\"李文隽\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"LR\\\":\\\"9\\\",\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"}]},{\\\"D\\\":\\\"一审被告\\\",\\\"N\\\":\\\"aafb85c76d8bac7e58821063d3ca2f5c\\\",\\\"O\\\":0,\\\"P\\\":\\\"苏宁云商集团股份有限公司\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"LR\\\":\\\"11\\\",\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"}]}]\",\"CaseType\":\"民事案件\",\"CfdfCnt\":0,\"CfgsCnt\":0,\"CfxyCnt\":0,\"CompanyKeywords\":\"0c82966e4015353be8fbe3c8581f8678,522eaea91e9023281737b0f71b4042c2,9d2d9a41a60273868879579f68e561c1,a385c097ccfa88c0ae833b380cb54d97,aafb85c76d8bac7e58821063d3ca2f5c,da02a5dd835d417655f66fc9f5fabd73,p3ea565ae4b4be51522322684be80d09,p3ff79415471e171567e91bc001c0f01,平安银行股份有限公司广州花园支行,广州市于文服装有限公司,广州市于文贸易有限公司,广州市烨盈电器贸易有限公司,广州市焜建贸易有限公司,广州市睿隽商贸有限公司,建贸易有限公司,李建华,李文隽,江苏苏宁交家电有限公司,江苏苏宁交家电集团有限公司,深圳发展银行广州分行广东国际大厦办事处,深圳发展银行广州分行广东国际大厦支行,深圳发展银行广州分行花园支行,深圳发展银行股份有限公司广州花园支行,苏宁云商集团股份有限公司,苏宁云商集团股份有限公司苏宁采购中心,苏宁交家电(集团)有限公司,苏宁易购集团股份有限公司,苏宁易购集团股份有限公司苏宁采购中心,苏宁电器股份有限公司,苏宁电器股份有限公司南京采购中心,苏宁电器股份有限公司苏宁采购中心,苏宁电器连锁集团股份有限公司,苏宁电器连锁集团股份有限公司南京采购中心,苏宁电器连锁集团股份有限公司采购中心\",\"CourtList\":\"广东省广州市越秀区人民法院\",\"EarliestDate\":1467129600,\"EarliestDateType\":\"民事一审|立案日期\",\"FyggCnt\":0,\"GqdjCnt\":0,\"GroupId\":\"b8b7d87ac9b7e3e066c12bc1018185cc\",\"HbcfCnt\":0,\"Id\":\"24d099c97fc8fdc038f1895d18cb59fe\",\"InfoList\":[{\"AnNo\":\"（2016）粤0104民初9036号\",\"CaseList\":[{\"Amt\":\"48810278.71\",\"CaseType\":\"民事判决书\",\"DocType\":\"判决日期\",\"Id\":\"71124c24a67ac82f83ed8622af65797c0\",\"IsValid\":1,\"JudgeDate\":1528992000,\"Result\":\"一、在本判决发生法律效力之日起十日内，被告广州市睿隽商贸有限公司向原告平安银行股份有限公司广州花园支行清偿融资本金12810278.71元及利息（含罚息、复利，截至2016年6月1日利息、罚息、复利合共3112764.48元，自2016年6月2日起罚息按照《贷款合同》的约定计算至清偿之日止，罚息部分不再计收复利）；  二、被告广州市睿隽商贸有限公司不履行本判决第一项给付义务时，则依法处分抵押物（被告李建华名下位于广州市天河区天河北路179号901、902、903、904、905、906、907、908房房产），原告平安银行股份有限公司广州花园支行有权在最高额本金36000000元及相应利息、罚息、复利限额内就所得价款优先受偿；  三、被告广州市焜建贸易有限公司、被告广州市烨盈电器贸易有限公司、被告李建华、被告李文隽分别在最高额本金36000000元及相应利息、罚息、复利限额内对本判决第一项给付义务承担连带清偿责任。\",\"ResultType\":\"判决结果\",\"ShieldCaseFlag\":0}],\"CaseReason\":\"合同纠纷\",\"CaseType\":\"民事案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"广东省广州市越秀区人民法院\",\"Defendant\":[{\"KeyNo\":\"da02a5dd835d417655f66fc9f5fabd73\",\"LR\":\"9\",\"Name\":\"广州市烨盈电器贸易有限公司\",\"Org\":0,\"Role\":\"被告\"},{\"KeyNo\":\"9d2d9a41a60273868879579f68e561c1\",\"LR\":\"9\",\"Name\":\"广州市焜建贸易有限公司\",\"Org\":0,\"Role\":\"被告\"},{\"KeyNo\":\"a385c097ccfa88c0ae833b380cb54d97\",\"LR\":\"9\",\"Name\":\"广州市睿隽商贸有限公司\",\"Org\":0,\"Role\":\"被告\"},{\"KeyNo\":\"p3ea565ae4b4be51522322684be80d09\",\"LR\":\"9\",\"Name\":\"李建华\",\"Org\":2,\"Role\":\"被告\"},{\"KeyNo\":\"p3ff79415471e171567e91bc001c0f01\",\"LR\":\"9\",\"Name\":\"李文隽\",\"Org\":2,\"Role\":\"被告\"},{\"KeyNo\":\"aafb85c76d8bac7e58821063d3ca2f5c\",\"LR\":\"11\",\"Name\":\"苏宁云商集团股份有限公司\",\"Org\":0,\"Role\":\"被告\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1528992000,\"LianList\":[{\"Id\":\"71124c24a67ac82f83ed8622af65797c\",\"IsValid\":1,\"LianDate\":1467129600,\"NameAndKeyNo\":[{\"KeyNo\":\"0c82966e4015353be8fbe3c8581f8678\",\"Name\":\"平安银行股份有限公司广州花园支行\",\"Org\":0},{\"KeyNo\":\"9d2d9a41a60273868879579f68e561c1\",\"Name\":\"广州市焜建贸易有限公司\",\"Org\":0},{\"KeyNo\":\"\",\"Name\":\"李文隽\",\"Org\":-2},{\"KeyNo\":\"\",\"Name\":\"李建华\",\"Org\":-2},{\"KeyNo\":\"aafb85c76d8bac7e58821063d3ca2f5c\",\"Name\":\"苏宁云商集团股份有限公司\",\"Org\":0},{\"KeyNo\":\"a385c097ccfa88c0ae833b380cb54d97\",\"Name\":\"广州市睿隽商贸有限公司\",\"Org\":0},{\"KeyNo\":\"da02a5dd835d417655f66fc9f5fabd73\",\"Name\":\"广州市烨盈电器贸易有限公司\",\"Org\":0}]}],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"0c82966e4015353be8fbe3c8581f8678\",\"LR\":\"8\",\"Name\":\"平安银行股份有限公司广州花园支行\",\"Org\":0,\"Role\":\"原告\"}],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"民事一审\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]}],\"KtggCnt\":0,\"LastestDate\":1528992000,\"LastestDateType\":\"民事一审|判决日期\",\"LatestTrialRound\":\"民事一审\",\"LawyerIds\":\"29dda50c3bc3d385dd81e2b9a979eb1f\",\"LianCnt\":1,\"PcczCnt\":0,\"ProcuratorateList\":\"\",\"Province\":\"GD\",\"SdggCnt\":0,\"Source\":\"OT\",\"SxCnt\":0,\"Tags\":\"4,12\",\"Type\":1,\"XgCnt\":0,\"XjpgCnt\":0,\"XzcfCnt\":0,\"ZbCnt\":0,\"ZxCnt\":0}] ";
        List<LawSuitV3OutputEntity> entity = JSON.parseArray(json, LawSuitV3OutputEntity.class);
        for (LawSuitV3OutputEntity outputEntity : entity) {
            System.out.println(evaluate(JSON.toJSONString(outputEntity)));;
        }

    }

    /**
     * 无需计算债务的KeyNo汇总
     * @param caseRole
     * @return
     */
    static Set<String> excludeCaseRole(String caseRole){
        Set<String> excludeKeyNoSet = new HashSet<>();
        try{
            List<CaseRoleEntity>  caseRoleList = JSON.parseArray(caseRole, CaseRoleEntity.class);
            for (CaseRoleEntity item : caseRoleList) {
                if(!Strings.isNullOrEmpty(item.getN()) && !CollectionUtils.isEmpty(item.getRoleList())){
                    for (CaseRoleSort caseRoleSort : item.getRoleList()) {
                        //被告+不承担责任
                        if(BEI_GA0_NAME_SET.contains(caseRoleSort.getRole())
                                && EXCLUDE_AMOUNT_LW_SET.contains(caseRoleSort.getLawsuitResult())){
                            excludeKeyNoSet.add(item.getN());
                        }
                    }
                }

            }
        }catch (Exception e){

        }

        return excludeKeyNoSet;
    }

    static Set<String> EXCLUDE_AMOUNT_LW_SET = Sets.newHashSet("11");

    /**
     * 被告RoleName名称汇总
     */
    public static Set<String> BEI_GA0_NAME_SET = new LinkedHashSet<>();

    static {


        BEI_GA0_NAME_SET.add("被执行人");
        BEI_GA0_NAME_SET.add("被告");
        BEI_GA0_NAME_SET.add("被申请人");
        BEI_GA0_NAME_SET.add("被申请执行人");
        BEI_GA0_NAME_SET.add("原审被告");
        BEI_GA0_NAME_SET.add("被上诉人(原审被告)");
        BEI_GA0_NAME_SET.add("被上诉人(原审被告)");
        BEI_GA0_NAME_SET.add("上诉人(原审被告)");
        BEI_GA0_NAME_SET.add("被告(反诉原告)");
        BEI_GA0_NAME_SET.add("被告人");
        BEI_GA0_NAME_SET.add("上诉人(一审被告)");
        BEI_GA0_NAME_SET.add("被上诉人(一审被告)");
        BEI_GA0_NAME_SET.add("被上诉人");
        BEI_GA0_NAME_SET.add("上诉人(原审被告反诉原告)");
        BEI_GA0_NAME_SET.add("被告二");
        BEI_GA0_NAME_SET.add("被告一");
        BEI_GA0_NAME_SET.add("原告(被告)");
        BEI_GA0_NAME_SET.add("被申请人(一审被告二审被上诉人)");
        BEI_GA0_NAME_SET.add("被申请人(原审被告)");
        BEI_GA0_NAME_SET.add("再审申请人(一审被告二审上诉人)");
        BEI_GA0_NAME_SET.add("再审申请人(原审被告)");
        BEI_GA0_NAME_SET.add("被申请人(仲裁被申请人)");
        BEI_GA0_NAME_SET.add("被申请人(原被执行人)");
        BEI_GA0_NAME_SET.add("再审被申请人");
        BEI_GA0_NAME_SET.add("上诉人(原审被告原审原告)");
        BEI_GA0_NAME_SET.add("原审被告单位");
        BEI_GA0_NAME_SET.add("被起诉人");
        BEI_GA0_NAME_SET.add("被告单位");
    }

}
