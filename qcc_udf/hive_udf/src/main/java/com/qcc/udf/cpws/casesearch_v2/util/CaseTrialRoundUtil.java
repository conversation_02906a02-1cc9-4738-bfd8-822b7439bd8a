package com.qcc.udf.cpws.casesearch_v2.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 裁判文书清洗UDF：从案号中提取出对应的审判程序
 * ---------------------------------------------------------------------------------------------------------
 * create temporary function extractCaseTrialRound as 'com.qcc.udf.cpws.ExtractCaseTrialRoundUDF' using jar 'hdfs:///data/hive/udf/qcc_udf.jar';
 * ---------------------------------------------------------------------------------------------------------
 * select extractCaseTrialRound ('（2018）xxxx');
 * 结果: '民事一审'
 */
public class CaseTrialRoundUtil {
    private final static Map<String, String> caseNoTrialRoundMap;
    static {
        caseNoTrialRoundMap = new LinkedHashMap<>();
        try {
            Map<String, String> map = new HashMap<>();
            try (InputStream is = CaseTrialRoundUtil.class.getResourceAsStream("/casenoToToundMap.csv")) {
                BufferedReader br = new BufferedReader(new InputStreamReader(is));
                String line;
                while ((line = br.readLine()) != null) {
                    String[] splits = line.split("\t");
                    if (splits != null && splits.length == 2) {
                        String caseNoChar = splits[0].trim();
                        String trialRound = splits[1].trim();
                        map.put(caseNoChar, trialRound);
                    }
                }
                br.close();
            }

            List<String> keyList = map.keySet().stream().sorted((e1, e2) -> e2.length()-e1.length()).collect(Collectors.toList());
            for (String key : keyList) {
                caseNoTrialRoundMap.put(key, map.get(key));
            }
        } catch (Exception ex) {
        }
    }

    public String evaluate(String caseNo) {
        try {
            if (StringUtils.isBlank(caseNo)) {
                return "";
            }
            caseNo = caseNo.replace("（", "").replace("）", "");

            for (String searchKey : caseNoTrialRoundMap.keySet()) {
                if (caseNo.contains(searchKey)) {
                    return caseNoTrialRoundMap.get(searchKey);
                }
            }

            // 指定代字与审判程序的映射
            if (caseNo.contains("商辖") || caseNo.contains("商外辖") || caseNo.contains("商四辖")) {
                return "民事管辖";
            }

            // 根据指定关键字的出现，扩大范围做审判程序匹配（当前仅作一审二审类别案件所属审判程序的扩大提取）
            Pattern civilJurisdictionPattern = Pattern.compile("民.{1,2}辖");
            if (civilJurisdictionPattern.matcher(caseNo).find()) {
                return "民事管辖";
            }

            if (caseNo.contains("刑") && caseNo.contains("初")) {
                return "刑事一审";
            }

            if (caseNo.contains("刑") && caseNo.contains("终")) {
                return "刑事二审";
            }

            Pattern administrationFirstInstancePattern = Pattern.compile("行.{1,2}初");
            if (administrationFirstInstancePattern.matcher(caseNo).find()) {
                return "行政一审";
            }
            Pattern administrationFirstInstancePatternV2 = Pattern.compile("行\\(知\\)初");
            if (administrationFirstInstancePatternV2.matcher(caseNo).find()) {
                return "行政一审";
            }
            Pattern administrationAdjudicationPattern = Pattern.compile("行.{1,2}终");
            if (administrationAdjudicationPattern.matcher(caseNo).find()) {
                return "行政二审";
            }
            Pattern administrationAdjudicationPatternV2 = Pattern.compile("行\\(知\\)终");
            if (administrationAdjudicationPatternV2.matcher(caseNo).find()) {
                return "行政二审";
            }
            Pattern civilFirstInstancePattern = Pattern.compile("民.{1,2}初");
            if (civilFirstInstancePattern.matcher(caseNo).find()) {
                return "民事一审";
            }
            Pattern civilAdjudicationPattern = Pattern.compile("民.{1,2}终");
            if (civilAdjudicationPattern.matcher(caseNo).find()) {
                return "民事二审";
            }
            Pattern commercialFirstInstancePattern = Pattern.compile("商.{0,2}初|知.{0,2}初");
            if (commercialFirstInstancePattern.matcher(caseNo).find()) {
                return "民事一审";
            }
            Pattern commercialAdjudicationPattern = Pattern.compile("商.{0,2}终|知.{0,2}终");
            if (commercialAdjudicationPattern.matcher(caseNo).find()) {
                return "民事二审";
            }

            Pattern pPattern = Pattern.compile("财保");
            if (commercialAdjudicationPattern.matcher(caseNo).find()) {
                return "财产保全";
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return "";
    }

    public static void main(String[] args) {
        System.out.println(new CaseTrialRoundUtil().evaluate("（2020）浙0521执24号"));
    }
}
