package com.qcc.udf.kzz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 招投标解析联系方式
 * <AUTHOR>
 * @date 2022/4/1
 */
public class GetTenderCompContact extends UDF {
    /**
     *
     * @param content
     * @param type 1-招标单位, 2-中标单位,3-委托单位
     * @return
     */
    public static String evaluate(String content,int type) {
        List<String> result = new ArrayList<>();
        try{
            if(StringUtils.isNotBlank(content) && StringUtils.length(content) > 5){
                JSONArray jsonArray = JSON.parseArray(content);
                if(!jsonArray.isEmpty()){
                    for (Object obj : jsonArray) {
                        JSONObject json = (JSONObject)obj;
                        String contact = json.getString("Contact");
                        String telNo = json.getString("TelNo");
                        int jsonType = json.getIntValue("Type");
                        if(type == jsonType){
                            if(StringUtils.isNotBlank(telNo)){
                                result.add(StringUtils.isNotBlank(contact)?contact+"/"+telNo:telNo);
                            }
                        }

                    }
                }
            }
        }catch (Exception e){

        }
        return result.stream().collect(Collectors.joining(","));
    }

//    public static void main(String[] args){
//        String content ="[{\"CompanyKeyNo\":\"g563d9a500ac2234744d664ed0fdc267\",\"CompanyName\":\"广西壮族自治区桂林冶金疗养院\",\"Contact\":\"卢春安\",\"Id\":\"aa99e098999d35d91ae9cfd87aa49922\",\"IsValidTel\":1,\"TelNo\":\"18577367009\",\"Type\":1},{\"CompanyKeyNo\":\"1b7f552d954bc044482cd9eed0063653\",\"CompanyName\":\"广西国盛招标有限公司\",\"Contact\":\"蒋工\",\"Id\":\"aa99e098999d35d91ae9cfd87aa49922\",\"IsValidTel\":1,\"TelNo\":\"0773-5838188\",\"Type\":3}]\n" +
//                "\n";
//       String tels =  evaluate(content,3);
//       System.out.println(tels);
//    }
}
