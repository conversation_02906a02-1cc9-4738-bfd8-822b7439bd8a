package com.qcc.udf.caseSearch;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Iterator;

public class getCaseRole extends UDF {
    public static String  evaluate(String roleArray, String keyno1, String keyno2) {
        JSONObject result = new JSONObject();

        keyno1 = keyno1 == null ? "" : keyno1;
        keyno2 = keyno2 == null ? "" : keyno2;

        String pArray = "";
        String dArray = "";

        if (StringUtils.isNotEmpty(roleArray)){
            JSONArray caseRoleJsonArray = JSONArray.parseArray(roleArray);
            Iterator<Object> it = caseRoleJsonArray.iterator();
            while(it.hasNext()){
                JSONObject jsonObject = (JSONObject)it.next();
                String keyword = "";
                if (StringUtils.isNotEmpty(jsonObject.getString("N")) && jsonObject.getString("N").length() == 32){
                    keyword = jsonObject.getString("N");
                }else{
                    keyword = jsonObject.getString("P");
                }

                if (keyno1.contains(keyword)){
                    pArray = pArray.concat(",").concat(keyword);
                }

                if (keyno2.contains(keyword)){
                    dArray = dArray.concat(",").concat(keyword);
                }
            }
        }

        pArray = pArray.length() > 0 ? pArray.substring(1) : pArray;
        dArray = dArray.length() > 0 ? dArray.substring(1) : dArray;

        result.put("P", pArray);
        result.put("D", dArray);

        return result.toString();
    }

    public static void main(String[] args) {
        System.out.println(evaluate("[{\"P\":\"孙\",\"R\":\"申请执行人\",\"N\":\"\",\"O\":-2},{\"P\":\"傅小林\",\"R\":\"被执行人\",\"N\":\"p446726fa59fd3612b585422bdef1943\",\"O\":2},{\"P\":\"李桂祥\",\"R\":\"被执行人\",\"N\":\"p3a90a83bc9613638a2c2e379f3393b0\",\"O\":2}]","孙","李桂祥,p3a90a83bc9613638a2c2e379f3393b0,傅小林,p446726fa59fd3612b585422bdef1943"));
    }
}
