package com.qcc.udf.temp;

import cn.hutool.core.util.ReUtil;
import com.alibaba.fastjson.JSON;
import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.output.LawSuitV3OutputEntity;
import com.qcc.udf.cpws.ExtractCaseTrialRoundUDF;
import com.qcc.udf.cpws.casesearch_v2.util.CaseTypeUtil;
import com.qcc.udf.cpws.casesearch_v2.util.CourtNameFromCaseNoUtil;
import com.qcc.udf.cpws.casesearch_v2.util.SbcToDbc;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *  根据案号，清洗出detailnfo信息
 * @Auther: wuql
 * @Date: 2020/11/17 10:00
 * @Description:
 */
public class DateHandlerUDF extends UDF {
    //合理范围
    public static Date PERMATION_MIN_DATA = Date.from(LocalDate.of(1969, 1, 1)
            .atStartOfDay()
            .atZone(ZoneId.systemDefault())
            .toInstant());

    //合理范围
    public static Date PERMATION_MAX_DATA = Date.from(LocalDate.of(2038, 1, 1)
            .atStartOfDay()
            .atZone(ZoneId.systemDefault())
            .toInstant());

    public String evaluate(String dataStr)  {
        String permissionDate = CommonUtil.removeSpecialContent(dataStr);
        permissionDate = permissionDate.replace("？", "");
        Date permissionDateNew = CommonDateUtil.convertStr2Date(permissionDate);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        if (permissionDateNew != null) {
            permissionDate = sdf.format(permissionDateNew);
            //合理日期范围
            if (permissionDateNew.before(PERMATION_MIN_DATA) || permissionDateNew.after(PERMATION_MAX_DATA)) {
                permissionDate = "";
            }
        }
        return permissionDate;
    }

    public static void main(String[] args) {
        String dataStr ="2015/04/02";
        DateHandlerUDF dateHandlerUDF = new DateHandlerUDF();
        String evaluate = dateHandlerUDF.evaluate(dataStr);
        System.out.println(evaluate);
    }
}
