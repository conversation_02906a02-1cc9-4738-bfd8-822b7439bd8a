package com.qcc.udf.CommonService;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDF;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.apache.kafka.common.serialization.StringSerializer;

import java.io.UnsupportedEncodingException;
import java.util.Properties;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

@Description(name = "HiveToKafkaWithCompression", value = "_FUNC_(String  topics,String msg);arguments max.request.size = 236870912; - Return offset")
public class HiveToKafkaWithCompression extends UDF {

    private static Properties props = new Properties() {{
        put("bootstrap.servers", "kafka.ld-hadoop.com:29092");
//        put("bootstrap.servers", "hd-051.ld-hadoop.com:9092,hd-052.ld-hadoop.com:9092,hd-053.ld-hadoop.com:9092,hd-054.ld-hadoop.com:9092,hd-055.ld-hadoop.com:9092,hd-056.ld-hadoop.com:9092,hd-057.ld-hadoop.com:9092");
        put("acks", "all");
//        put("min.insync.replicas", 2);
        put("retries", 3);
        put("batch.size", 16384);
        put("linger.ms", 1);
        put("buffer.memory", 236870912);
        put("max.request.size", 20000000);
        put("key.serializer", StringSerializer.class.getName());
        put("value.serializer", ByteArraySerializer.class.getName());
        put("compression.type", "gzip");
    }};

    private static KafkaProducer<String, byte[]> producer = new KafkaProducer<>(props);

    /**
     * @param topics
     * @param msg
     * @return
     * @throws Exception
     */
    public static String evaluate(String topics, String msg) throws ExecutionException, InterruptedException, UnsupportedEncodingException {
        try {
            if (StringUtils.isBlank(topics)) throw new InterruptedException("topic 不能为空");
            if (StringUtils.isBlank(msg)) return "0";
            Future<RecordMetadata> result = producer.send(new ProducerRecord<>(topics, msg.getBytes("UTF-8")));
//            producer.flush();
            return String.valueOf(result.get().offset());
        } catch (Exception ex) {
            throw ex;
        }
    }


    /**
     * @param topics
     * @param table
     * @param fields
     * @param data
     * @return
     * @throws ExecutionException
     * @throws InterruptedException
     */
    public static String evaluate(String topics, String table, String fields, String... data) throws ExecutionException, InterruptedException, UnsupportedEncodingException {
        try {
            if (StringUtils.isBlank(topics)) throw new InterruptedException("topic 不能为空");
            if (StringUtils.isBlank(fields)) throw new InterruptedException("fields 不能为空");
            if (data == null || data.length == 0) throw new InterruptedException("字段值 不能为空");
            String[] fieldArray = fields.split(",");
            if (fieldArray.length != data.length) throw new InterruptedException("字段名称和字段值个数必须相等");
            String msg = "";
            JSONObject jsonObject = new JSONObject();
            for (int i = 0; i < fieldArray.length; i++) {
                jsonObject.put(fieldArray[i], data[i]);
            }
            if (StringUtils.isNotBlank(table)) {
                JSONArray jsonArray = new JSONArray();
                JSONObject result = new JSONObject();
                jsonArray.add(jsonObject);
                result.put(table, jsonArray);
                msg = JSON.toJSONString(result, SerializerFeature.WriteMapNullValue);
            } else {
                msg = JSON.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue);
            }
            Future<RecordMetadata> result = producer.send(new ProducerRecord<>(topics, msg.getBytes("UTF-8")));
            return String.valueOf(result.get().offset());
        } catch (Exception ex) {
            throw ex;
        }
    }


//    public static void main(String[] args) throws ExecutionException, InterruptedException, UnsupportedEncodingException {
//        System.out.println(evaluate("dw_liwx_test_compression", "{\"extendOne\":\"设备\",\"extendThree\":\"环境试验设备\",\"extendTwo\":\"试验设备\",\"keyno\":\"247d942408373361cdd4f3630e89709c\",\"length\":8,\"name\":\"南京易诺环境试验设备有限公司\",\"shortname\":\"易诺环境试验设备\"}"));
//    }
}
