package com.qcc.udf.lawer;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * <AUTHOR>
 * @date ：Created in 2021/09/13 18:52
 * @description ：
 */
public class GetUsefulData extends UDF {

    public static String evaluate(String param, String defaultData) {
        String result = "";
        if (StringUtils.isEmpty(defaultData)) {
            defaultData = "";
        }
        result = defaultData;
        if (StringUtils.isEmpty(param)) {
            return result;
        }
        String[] paramStr = param.split(",");
        for(String item: paramStr) {
            if (StringUtils.isNotEmpty(item) && !item.equals(defaultData)) {
                result = item;
                break;
            }
        }
        return result;
    }


    public static void main(String[] args) {
        String param = "1,2,3,4";
        String defaultData = "1";
        System.out.println(evaluate(param,defaultData));
    }

}
