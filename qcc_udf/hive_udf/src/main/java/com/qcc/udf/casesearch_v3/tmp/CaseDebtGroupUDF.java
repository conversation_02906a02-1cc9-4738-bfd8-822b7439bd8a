package com.qcc.udf.casesearch_v3.tmp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.qcc.udf.casesearch_v3.entity.input.CaseRoleEntity;
import org.apache.hadoop.hive.ql.exec.UDF;
import parquet.Strings;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022年05月17日 14:27
 */
public class CaseDebtGroupUDF extends UDF {
    public static void main(String[] args) {
        String str = "[\"{\\\"type\\\":\\\"XG\\\",\\\"money\\\":\\\"\\\",\\\"sx_money\\\":null,\\\"sourceid\\\":\\\"a1dbaa5e090576e7622dfb6eed8184f1\\\",\\\"caseid\\\":\\\"5b5d6af6bedb17f7a5bf458a1ad1a4fb\\\"}\",\"{\\\"type\\\":\\\"XG\\\",\\\"money\\\":\\\"\\\",\\\"sx_money\\\":null,\\\"sourceid\\\":\\\"ea89c92d656167deac0b4e43409e17e7\\\",\\\"caseid\\\":\\\"d704d83184f485c3c3fe2b9995550066\\\"}\",\"{\\\"type\\\":\\\"CASE-3\\\",\\\"money\\\":\\\"3224848.08\\\",\\\"sx_money\\\":null,\\\"sourceid\\\":\\\"\\\",\\\"caseid\\\":\\\"0afcdcaa920b968a03e55b57eddd9321\\\"}\",\"{\\\"type\\\":\\\"CASE-3\\\",\\\"money\\\":\\\"\\\",\\\"sx_money\\\":null,\\\"sourceid\\\":\\\"\\\",\\\"caseid\\\":\\\"f5c517910b1f1cdf2588b8e071cacbe9\\\"}\",\"{\\\"type\\\":\\\"CASE-3\\\",\\\"money\\\":\\\"\\\",\\\"sx_money\\\":null,\\\"sourceid\\\":\\\"\\\",\\\"caseid\\\":\\\"1ef324b561ee0d6da01d358d081ac273\\\"}\",\"{\\\"type\\\":\\\"CASE-3\\\",\\\"money\\\":\\\"\\\",\\\"sx_money\\\":null,\\\"sourceid\\\":\\\"\\\",\\\"caseid\\\":\\\"99dd1c0437bed48bb44b1723361ff06e\\\"}\",\"{\\\"type\\\":\\\"CASE-3\\\",\\\"money\\\":\\\"\\\",\\\"sx_money\\\":null,\\\"sourceid\\\":\\\"\\\",\\\"caseid\\\":\\\"d7a104182837a90049983b8407a83a5d\\\"}\",\"{\\\"type\\\":\\\"CASE-3\\\",\\\"money\\\":\\\"\\\",\\\"sx_money\\\":null,\\\"sourceid\\\":\\\"\\\",\\\"caseid\\\":\\\"67a937dea605b462a212258b00f33e01\\\"}\",\"{\\\"type\\\":\\\"CASE-3\\\",\\\"money\\\":\\\"\\\",\\\"sx_money\\\":null,\\\"sourceid\\\":\\\"\\\",\\\"caseid\\\":\\\"f1b3baba87181cb738f9e9d38380cc1d\\\"}\",\"{\\\"type\\\":\\\"CASE-2\\\",\\\"money\\\":\\\"\\\",\\\"sx_money\\\":null,\\\"sourceid\\\":\\\"\\\",\\\"caseid\\\":\\\"288bba926041d2da34d533f627bde630\\\"}\",\"{\\\"type\\\":\\\"ZX\\\",\\\"money\\\":\\\"5269926\\\",\\\"sx_money\\\":null,\\\"sourceid\\\":\\\"422338f087619c87e3a56f3f1e31b6121\\\",\\\"caseid\\\":\\\"cfbc0fe6259bdecec13abb8da501adef\\\"}\",\"{\\\"type\\\":\\\"CASE-3\\\",\\\"money\\\":\\\"\\\",\\\"sx_money\\\":null,\\\"sourceid\\\":\\\"\\\",\\\"caseid\\\":\\\"cd7aecfaad96d0683fe46f4319fc25cc\\\"}\"] ";

        System.out.println(evaluate(JSONArray.parseArray(str, String.class)));
    }

    static BigDecimal YI_BAI = new BigDecimal(100);


    public static String evaluate(List<String> jsonList) {
        DecimalFormat FMT = new DecimalFormat("0.0000");
        List<JSONObject> dataList = new ArrayList<>();
        Set<String> caseIdSet = new HashSet<>();
        for (String str : jsonList) {
            JSONObject json = JSON.parseObject(str);
            String type = json.getString("type");
            String money = json.getString("money");
            String sx_money = json.getString("sx_money");
            String caseid  = json.getString("caseid");
            BigDecimal moneyDic = convertDecimal(money);
            if ("SX".equals(type)) {
                moneyDic = convertDecimal(sx_money);
            }
            json.put("money_dic", moneyDic.divide(new BigDecimal(10000)));
            dataList.add(json);
            caseIdSet.add(caseid);
        }
        Map<String, List<JSONObject>> mapPre = dataList.stream().collect(Collectors.groupingBy((x -> x.getString("type"))));
        Map<String, List<JSONObject>> map = new HashMap<>();

        mapPre.forEach((k, v) -> {
            if (k.equals("SX") || k.equals("ZX") || k.equals("XG")) {
                List<JSONObject> list = new ArrayList<>();
                List<JSONObject> listx = Lists.newArrayList(v.stream().collect(Collectors
                        .groupingBy(x -> x.getString("type") + x.getString("sourceid"),
                                Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(x -> x.getString("sourceid")))
                                        , Optional::get))).values());
                list.addAll(listx);
                map.put(k, list);
            } else {
                map.put(k, v);
            }
        });
        List<JSONObject> sx_list = map.getOrDefault("SX", new ArrayList<>());
        List<JSONObject> zx_list = map.getOrDefault("ZX", new ArrayList<>());
        List<JSONObject> xg_list = map.getOrDefault("XG", new ArrayList<>());
        List<JSONObject> case2_list = map.getOrDefault("CASE-2", new ArrayList<>());
        List<JSONObject> case3_list = map.getOrDefault("CASE-3", new ArrayList<>());
        BigDecimal sxMoney = sum(sx_list);
        BigDecimal zxMoney = sum(zx_list);
        BigDecimal case2Money = sum(case2_list);
        int sxCount = sx_list.size();
        int zxCount = zx_list.size();
        int xgCount = xg_list.size();
        int case2Count = case2_list.size();
        int case3Count = case3_list.size();
        BigDecimal sxRate = BigDecimal.ZERO;
        BigDecimal zxRate = BigDecimal.ZERO;
        BigDecimal xgRate = new BigDecimal(xgCount).multiply(new BigDecimal(0.1));
        BigDecimal case2Rate = BigDecimal.ZERO;
        BigDecimal case3Rate = new BigDecimal(case3Count).multiply(new BigDecimal(0.025));
        if (sxMoney.compareTo(YI_BAI) > 0) {
            sxRate = (new BigDecimal(sxCount).multiply(new BigDecimal(0.25)).add(sxMoney.divide(YI_BAI).multiply(new BigDecimal(0.25))));
        } else {
            sxRate = (new BigDecimal(sxCount).multiply(new BigDecimal(0.35)).add(sxMoney.divide(YI_BAI).multiply(new BigDecimal(0.15))));
        }
        if (zxMoney.compareTo(YI_BAI) > 0) {
            zxRate = (new BigDecimal(zxCount).multiply(new BigDecimal(0.15)).add(zxMoney.divide(YI_BAI).multiply(new BigDecimal(0.15))));
        } else {
            zxRate = (new BigDecimal(zxCount).multiply(new BigDecimal(0.21)).add(zxMoney.divide(YI_BAI).multiply(new BigDecimal(0.09))));
        }
        if (case2Money.compareTo(YI_BAI) > 0) {
            case2Rate = (new BigDecimal(case2Count).multiply(new BigDecimal(0.025)).add(case2Money.divide(YI_BAI).multiply(new BigDecimal(0.025))));
        } else {
            case2Rate = (new BigDecimal(case2Count).multiply(new BigDecimal(0.035)).add(case2Money.divide(YI_BAI).multiply(new BigDecimal(0.015))));
        }

        BigDecimal sum = sxRate.add(zxRate).add(xgRate).add(case2Rate).add(case3Rate);
        Map<String, Object> out = new HashMap<>();
        out.put("sxCount", sxCount);
        out.put("sxMoney", sxMoney);
        out.put("sxRate", FMT.format(sxRate));
        out.put("zxCount", zxCount);
        out.put("zxMoney", zxMoney);
        out.put("zxRate", FMT.format(zxRate));
        out.put("xgCount", xgCount);
        out.put("xgRate", FMT.format(xgRate));
        out.put("case2Count", case2Count);
        out.put("case2Money", case2Money);
        out.put("case2Rate", FMT.format(case2Rate));
        out.put("case3Count", case3Count);
        out.put("case3Rate", FMT.format(case3Rate));
        out.put("sumRate", FMT.format(sum));
        out.put("caseId", caseIdSet.stream().sorted().collect(Collectors.joining(",")));
        out.put("caseCount", caseIdSet.size());
        return JSON.toJSONString(out);
    }

    static BigDecimal sum(List<JSONObject> list) {
        BigDecimal zero = BigDecimal.ZERO;
        for (JSONObject jsonObject : list) {
            zero = zero.add(jsonObject.getBigDecimal("money_dic"));
        }
        return zero;
    }

    static BigDecimal convertDecimal(String str) {
        if (Strings.isNullOrEmpty(str)) {
            return BigDecimal.ZERO;
        }
        try {
            BigDecimal dic = new BigDecimal(str);
            return dic.compareTo(BigDecimal.ZERO) > 0 ? dic : BigDecimal.ZERO;
        } catch (Exception e) {

        }
        return BigDecimal.ZERO;
    }
}
