package com.qcc.udf.casesearch_v3;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.qcc.udf.casesearch_v3.entity.LawSuitV3Entity;
import com.qcc.udf.casesearch_v3.entity.input.CaseRoleEntity;
import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.input.OsPatent;
import com.qcc.udf.casesearch_v3.entity.input.Trademark;
import com.qcc.udf.casesearch_v3.entity.output.*;
import com.qcc.udf.casesearch_v3.util.CaseSearchUtil;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import com.qcc.udf.casesearch_v3.util.GroupSplitUtil;
import com.qcc.udf.court_notice.anUtils.MD5Util;
import org.apache.commons.collections.CollectionUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: zhangqiang
 * @Date: 2020/11/11 17:54
 * @Description:司法案件UDF 文档：http://doc.greatld.com/pages/viewpage.action?pageId=5713652
 */
public class GetDetailInfoV3UDF extends UDF {
    public static String evaluate(String annoId, String annoGourp, int type, String source, List<String> sourceSxList, List<String> sourceZxList, List<String> sourceXgList,
                                  List<String> sourceCaseList, List<String> sourcePcczList, List<String> sourceZbList,
                                  List<String> sourceXjpgList, List<String> sourceGqdjList, List<String> sourceSdggList,
                                  List<String> sourceFyggList, List<String> sourceKtggList, List<String> sourceLianList,
                                  List<String> sourceHbcfList, List<String> sourceXzcfList
            , List<String> sourceSfpmList, List<String> sourceSqtjList, List<String> sourceXzcjList, List<String> sourceXdpgjgList
            , List<String> sourceXsggList)
            throws InvocationTargetException, IllegalAccessException {

        //每天变更过多原因
        //caseRole当事人发生变化-进来的所有维度更具id排序

        List<LawSuitV3OutputEntity> outList;
        //标记删除的组，不需要处理
        if (type == 2) {
            outList = Lists.newArrayList(new LawSuitV3OutputEntity());
        } else {
            //实体映射
            LawSuitV3Entity input = new LawSuitV3Entity();
            input.convert(sourceSxList, sourceZxList, sourceXgList, sourceCaseList, sourcePcczList, sourceZbList, sourceXjpgList
                    , sourceGqdjList, sourceSdggList, sourceFyggList, sourceKtggList, sourceLianList, sourceHbcfList, sourceXzcfList
                    , sourceSfpmList, sourceSqtjList, sourceXzcjList, sourceXdpgjgList, sourceXsggList);
            //step1. 排序(减少对比数据时产生的变更)
            CommonV3Util.sort(input);
            //step2. 拆分,过滤案件
//            List<LawSuitV3Entity> caseSplitList = CommonV3Util.split_v0(input);
            List<LawSuitV3Entity> caseSplitList = GroupSplitUtil.split_v2(input);
            //step3. 组装数据
            outList = CommonV3Util.build(caseSplitList, annoGourp);

            //step4.出参简单排序
            for (LawSuitV3OutputEntity outputEntity : outList) {
                List<InfoListEntity> infoList = outputEntity.getInfoList().stream()
                        .sorted(Comparator.comparing(InfoListEntity::getAnno)).collect(Collectors.toList());
                //所有小list也需要排序
                for (InfoListEntity infoListEntity : infoList) {
                    //失信
                    infoListEntity.setSxList(infoListEntity.getSxList().stream()
                            .sorted(Comparator.comparing(SXListEntity::getId)).collect(Collectors.toList()));
                    //执行
                    infoListEntity.setZxList(infoListEntity.getZxList().stream()
                            .sorted(Comparator.comparing(ZXListEntity::getId)).collect(Collectors.toList()));
                    //限高
                    infoListEntity.setXgList(infoListEntity.getXgList().stream()
                            .sorted(Comparator.comparing(XGListEntity::getId)).collect(Collectors.toList()));

                    //裁判文书
                    infoListEntity.setCaseList(infoListEntity.getCaseList().stream()
                            .sorted(Comparator.comparing(CaseListEntity::getId)).collect(Collectors.toList()));

                    //立案
                    infoListEntity.setLianList(infoListEntity.getLianList().stream()
                            .sorted(Comparator.comparing(LianListEntity::getId)).collect(Collectors.toList()));

                    //法院公告
                    infoListEntity.setFyggList(infoListEntity.getFyggList().stream()
                            .sorted(Comparator.comparing(FyggListEntity::getId)).collect(Collectors.toList()));

                    //开庭公告
                    infoListEntity.setKtggList(infoListEntity.getKtggList().stream()
                            .sorted(Comparator.comparing(KtggListEntity::getId)).collect(Collectors.toList()));

                    //送达公告
                    infoListEntity.setSdggList(infoListEntity.getSdggList().stream()
                            .sorted(Comparator.comparing(SdggListEntity::getId)).collect(Collectors.toList()));

                    //破产重组
                    infoListEntity.setPcczList(infoListEntity.getPcczList().stream()
                            .sorted(Comparator.comparing(PcczListEntity::getId)).collect(Collectors.toList()));

                    //股权冻结
                    infoListEntity.setGqdjList(infoListEntity.getGqdjList().stream()
                            .sorted(Comparator.comparing(GqdjListEntity::getId)).collect(Collectors.toList()));

                    //询价评估
                    infoListEntity.setXjpgList(infoListEntity.getXjpgList().stream()
                            .sorted(Comparator.comparing(XjpgListEntity::getId)).collect(Collectors.toList()));
                    //终本
                    infoListEntity.setZbList(infoListEntity.getZbList().stream()
                            .sorted(Comparator.comparing(ZbListEntity::getId)).collect(Collectors.toList()));

                    //司法拍卖
                    if (CollectionUtils.isNotEmpty(infoListEntity.getSfpmList())) {
                        infoListEntity.setSfpmList(infoListEntity.getSfpmList().stream()
                                .sorted(Comparator.comparing(SFPMListEntity::getId)).collect(Collectors.toList()));
                    }

                    //诉前调解
                    if (CollectionUtils.isNotEmpty(infoListEntity.getSqtjList())) {
                        infoListEntity.setSqtjList(infoListEntity.getSqtjList().stream()
                                .sorted(Comparator.comparing(SQTJListEntity::getId)).collect(Collectors.toList()));
                    }
                    //限制出境
                    if (CollectionUtils.isNotEmpty(infoListEntity.getXzcjList())) {
                        infoListEntity.setXzcjList(infoListEntity.getXzcjList().stream()
                                .sorted(Comparator.comparing(XZCJListEntity::getId)).collect(Collectors.toList()));
                    }
                    //选定评估机构
                    if (CollectionUtils.isNotEmpty(infoListEntity.getXdpgjgList())) {
                        infoListEntity.setXdpgjgList(infoListEntity.getXdpgjgList().stream()
                                .sorted(Comparator.comparing(XDPGJGListEntity::getId)).collect(Collectors.toList()));
                    }
                    //悬赏公告
                    if (CollectionUtils.isNotEmpty(infoListEntity.getXsggList())) {
                        infoListEntity.setXsggList(infoListEntity.getXsggList().stream()
                                .sorted(Comparator.comparing(XSGGListEntity::getId)).collect(Collectors.toList()));
                    }
                }

                //行政处罚
                if (CollectionUtils.isNotEmpty(outputEntity.getXzcfList())) {
                    outputEntity.setXzcfList(outputEntity.getXzcfList().stream()
                            .sorted(Comparator.comparing(XZCFListEntity::getId)).collect(Collectors.toList()));
                }
                //环保处罚
                if (CollectionUtils.isNotEmpty(outputEntity.getHbcfList())) {
                    outputEntity.setHbcfList(outputEntity.getHbcfList().stream()
                            .sorted(Comparator.comparing(HbcfListEntity::getId)).collect(Collectors.toList()));
                }
                //商标
                if (CollectionUtils.isNotEmpty(outputEntity.getSBList())) {
                    outputEntity.setSBList(outputEntity.getSBList().stream()
                            .sorted(Comparator.comparing(TrademarkListEntity::getId)).collect(Collectors.toList()));
                }
                //专利
                if (CollectionUtils.isNotEmpty(outputEntity.getZLList())) {
                    outputEntity.setZLList(outputEntity.getZLList().stream()
                            .sorted(Comparator.comparing(PatentListEntity::getId)).collect(Collectors.toList()));
                }
                outputEntity.setInfoList(infoList);

                //生成系列案件标识
//                outputEntity.setGroupMark(CaseSearchUtil.buildSeriesGroupId(outputEntity));
//                outputEntity.setAnNoEnd(CaseSearchUtil.getAnNoEnd(outputEntity.getAnNoList()));

            }
        }

        //step5.生成主键
        for (LawSuitV3OutputEntity item : outList) {
            item.setGroupId(annoId);//组id
            item.setType(type);//操作类型 0-更新 1-新增  2-删除
            item.setSource(source);//来源 RT-实时计算 OT-离线任务
            if (type != 2) {
                //组id+最小案号+当事人+省份
                String id = annoId
                        + item.getAnNoList().split(",")[0]
                        + JSONArray.parseArray(item.getCaseRole(), CaseRoleEntity.class)
                        .stream().map(CaseRoleEntity::getP).collect(Collectors.toSet())
                        .stream().sorted().collect(Collectors.joining(","))
                        + item.getProvince();
                item.setId(MD5Util.ecodeByMD5(id));

//                if(Strings.isNullOrEmpty(item.getGroupMark())){
//                    item.setGroupMark(item.getId());
//                }
            }
        }
        if (type != 2) {
            Map<String, List<LawSuitV3OutputEntity>> mapList = outList.stream().collect(Collectors
                    .groupingBy(LawSuitV3OutputEntity::getId));
            //防止ID重复
            for (List<LawSuitV3OutputEntity> value : mapList.values()) {
                if (value.size() > 1) {
                    value.sort(Comparator.comparing(LawSuitV3OutputEntity::getCompanyKeywords));
                    for (int i = 0; i < value.size(); i++) {
                        if (i > 0) {
                            value.get(i).setId(MD5Util.ecodeByMD5(value.get(i).getId() + "_" + i));
                        }
                    }
                }
            }
            outList = outList.stream()
                    .sorted(Comparator.comparing(LawSuitV3OutputEntity::getId))
                    .collect(Collectors.toList());
        }

        //生成空案件的数据需要标记删除
        if (CollectionUtils.isEmpty(outList)) {
            LawSuitV3OutputEntity item = new LawSuitV3OutputEntity();
            item.setGroupId(annoId);//组id
            item.setType(2);//操作类型 0-更新 1-新增  2-删除
            item.setSource(source);//来源 RT-实时计算 OT-离线任务
            outList.add(item);
        }

        return JSON.toJSONString(outList, SerializerFeature.DisableCircularReferenceDetect);
    }


    public static void main(String[] args) throws InvocationTargetException, IllegalAccessException {
        List<String> sxList = new ArrayList();
        List<String> zxList = new ArrayList();

        List<String> xgList = new ArrayList();

        List<String> caseList = new ArrayList();

        List<String> pcczList = new ArrayList<>();      // 破产重整

        List<String> zbList = new ArrayList<>();        // 终本案件

        List<String> xjpgList = new ArrayList<>();      // 询价评估

        List<String> gqdjList = new ArrayList<>();      // 股权冻结

        List<String> sdggList = new ArrayList<>();      // 送达公告

        List<String> fyggList = new ArrayList<>();      // 法院公告

        List<String> ktggList = new ArrayList<>();      // 开庭公告


        List<String> lianList = new ArrayList<>();      // 立案信息

        List<String> hbcfList = new ArrayList<>();      // 环保处罚
        List<String> xzcfList = new ArrayList<>();      // 行政处罚HLJ

        List<String> sfpmList = new ArrayList<>();      // 司法拍卖

        List<String> sqtjList = new ArrayList<>();      // 诉前调解

        List<String> xzcjList = new ArrayList<>();      // 限制出境

        List<String> xdpgjgList = new ArrayList<>();      // 选定评估机构

        List<String> xsggList = new ArrayList<>();      // 悬赏公告

        String anno = "(2011)太刑初字第150号,(2012)宜刑终字第23号,(2014)合刑执字第6774号,(2015)合刑执字第506号";

        ktggList.add("{\"id\":\"32f35079bc1aad944b0e116347fa282d\",\"anno\":\"（2019）赣0924民初1595号\",\"provincecode\":\"JX\",\"companynames\":\"宜丰城市管理局,江西强源市政工程有限公司,16bb4977d60efb62a0564b4c8d584157,廊坊华越管道工程技术有限公司,0855f3cbb039b37c363263a12864c4e6\",\"isvalid\":\"1\",\"liandate\":1573174800,\"casereason\":\"建设工程施工合同纠纷\",\"courtname\":\"江西省宜春市宜丰县人民法院\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Name\\\":\\\"宜丰城市管理局\\\",\\\"Org\\\":-1,\\\"Role\\\":\\\"被告\\\",\\\"RoleTag\\\":1,\\\"RoleType\\\":21,\\\"ShowName\\\":\\\"宜丰城市管理局\\\",\\\"Source\\\":0},{\\\"KeyNo\\\":\\\"16bb4977d60efb62a0564b4c8d584157\\\",\\\"Name\\\":\\\"江西强源市政工程有限公司\\\",\\\"Org\\\":0,\\\"Role\\\":\\\"被告\\\",\\\"RoleTag\\\":1,\\\"RoleType\\\":21,\\\"ShowName\\\":\\\"江西强源市政工程有限公司\\\",\\\"Source\\\":0,\\\"T\\\":\\\"6\\\"},{\\\"KeyNo\\\":\\\"0855f3cbb039b37c363263a12864c4e6\\\",\\\"LawFirmList\\\":[{\\\"LY\\\":[{\\\"N\\\":\\\"\\\",\\\"P\\\":\\\"金鼎\\\",\\\"R\\\":\\\"委托诉讼代理人\\\"}],\\\"N\\\":\\\"\\\",\\\"O\\\":0,\\\"P\\\":\\\"\\\"}],\\\"Name\\\":\\\"廊坊华越管道工程技术有限公司\\\",\\\"Org\\\":0,\\\"Role\\\":\\\"原告\\\",\\\"RoleTag\\\":0,\\\"RoleType\\\":11,\\\"ShowName\\\":\\\"廊坊华越管道工程技术有限公司\\\",\\\"Source\\\":0,\\\"T\\\":\\\"5\\\"}]\",\"prosecutorlist\":\"[{\\\"KeyNo\\\":\\\"0855f3cbb039b37c363263a12864c4e6\\\",\\\"LawFirmList\\\":[{\\\"LY\\\":[{\\\"N\\\":\\\"\\\",\\\"P\\\":\\\"金鼎\\\",\\\"R\\\":\\\"委托诉讼代理人\\\"}],\\\"N\\\":\\\"\\\",\\\"O\\\":0,\\\"P\\\":\\\"\\\"}],\\\"Name\\\":\\\"廊坊华越管道工程技术有限公司\\\",\\\"Org\\\":0,\\\"ShowName\\\":\\\"廊坊华越管道工程技术有限公司\\\",\\\"T\\\":\\\"5\\\"}]\",\"defendantlist\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Name\\\":\\\"宜丰城市管理局\\\",\\\"Org\\\":-1,\\\"ShowName\\\":\\\"宜丰城市管理局\\\"},{\\\"KeyNo\\\":\\\"16bb4977d60efb62a0564b4c8d584157\\\",\\\"Name\\\":\\\"江西强源市政工程有限公司\\\",\\\"Org\\\":0,\\\"ShowName\\\":\\\"江西强源市政工程有限公司\\\",\\\"T\\\":\\\"6\\\"}]\",\"executeunite\":\"第四审判庭\"}");

        caseList.add("{\"id\":\"adb9c973cd1ec63547a86b9db20490f90\",\"anno\":\"（2019）赣0924民初1595号\",\"isvalid\":\"1\",\"courtname\":\"江西省宜春市宜丰县人民法院\",\"companynames\":\"0855f3cbb039b37c363263a12864c4e6,江西强源市政工程有限公司,ga416b2046bc05bb59f1b91b429d02a0,宜丰县城市管理局,16bb4977d60efb62a0564b4c8d584157,廊坊华越管道工程技术有限公司\",\"provincecode\":\"JX\",\"defendant\":\"江西强源市政工程有限公司,ga416b2046bc05bb59f1b91b429d02a0,宜丰县城市管理局,16bb4977d60efb62a0564b4c8d584157\",\"prosecutor\":\"0855f3cbb039b37c363263a12864c4e6,廊坊华越管道工程技术有限公司\",\"submitdate\":1577030400,\"judgedate\":1572969600,\"courtdate\":1577030400,\"casereason\":\"建设工程施工合同纠纷\",\"caserole\":\"[{\\\"P\\\":\\\"廊坊华越管道工程技术有限公司\\\",\\\"R\\\":\\\"原告\\\",\\\"ShowName\\\":\\\"廊坊华越管道工程技术有限公司\\\",\\\"N\\\":\\\"0855f3cbb039b37c363263a12864c4e6\\\",\\\"O\\\":0},{\\\"P\\\":\\\"江西强源市政工程有限公司\\\",\\\"R\\\":\\\"被告\\\",\\\"ShowName\\\":\\\"江西强源市政工程有限公司\\\",\\\"N\\\":\\\"16bb4977d60efb62a0564b4c8d584157\\\",\\\"O\\\":0},{\\\"P\\\":\\\"宜丰县城市管理局\\\",\\\"R\\\":\\\"被告\\\",\\\"ShowName\\\":\\\"宜丰县城市管理局\\\",\\\"N\\\":\\\"ga416b2046bc05bb59f1b91b429d02a0\\\",\\\"O\\\":4}]\",\"protestorgan\":\"\",\"amountinvolved\":\"\",\"defendantpaytotal\":\"\",\"doctype\":\"adj\",\"judgeresult\":\"准许原告廊坊华越管道工程技术有限公司撤诉。\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"16bb4977d60efb62a0564b4c8d584157\\\",\\\"Org\\\":0,\\\"ShowName\\\":\\\"江西强源市政工程有限公司\\\",\\\"Name\\\":\\\"江西强源市政工程有限公司\\\"},{\\\"KeyNo\\\":\\\"ga416b2046bc05bb59f1b91b429d02a0\\\",\\\"Org\\\":4,\\\"ShowName\\\":\\\"宜丰县城市管理局\\\",\\\"Name\\\":\\\"宜丰县城市管理局\\\"},{\\\"KeyNo\\\":\\\"0855f3cbb039b37c363263a12864c4e6\\\",\\\"Org\\\":0,\\\"ShowName\\\":\\\"廊坊华越管道工程技术有限公司\\\",\\\"Name\\\":\\\"廊坊华越管道工程技术有限公司\\\"}]\",\"beforecaseno\":\"\",\"casename\":\"廊坊华越管道工程技术有限公司与江西强源市政工程有限公司、宜丰县城市管理局建设工程施工合同纠纷一审民事裁定书\",\"trialprocess\":\"廊坊华越管道工程技术有限公司与江西强源市政工程有限公司、宜丰县城市管理局建设工程施工合同纠纷一案，本院于2019年10月17日立案。原告廊坊华越管道工程技术有限公司于2019年10月29日以双方已协商解决纠纷，被告已支付绝大部分工程款为由向本院提出撤诉申请。\",\"shieldcaseflag\":\"0\",\"trademark_detail\":\"\",\"patents_detail\":\"\",\"lawsuitresultnew\":\"[{\\\"P\\\":\\\"廊坊华越管道工程技术有限公司\\\",\\\"T\\\":\\\"5\\\",\\\"ShowName\\\":\\\"廊坊华越管道工程技术有限公司\\\",\\\"N\\\":\\\"0855f3cbb039b37c363263a12864c4e6\\\"},{\\\"P\\\":\\\"江西强源市政工程有限公司\\\",\\\"T\\\":\\\"6\\\",\\\"ShowName\\\":\\\"江西强源市政工程有限公司\\\",\\\"N\\\":\\\"16bb4977d60efb62a0564b4c8d584157\\\"},{\\\"P\\\":\\\"宜丰县城市管理局\\\",\\\"T\\\":\\\"6\\\",\\\"ShowName\\\":\\\"宜丰县城市管理局\\\",\\\"N\\\":\\\"ga416b2046bc05bb59f1b91b429d02a0\\\"}]\",\"lawyer_info\":\"[{\\\"Role\\\":0,\\\"LawFirm\\\":\\\"\\\",\\\"Name\\\":\\\"金鼎\\\"}]\",\"caserolelawjudgeparty\":\"[{\\\"Role\\\":\\\"原告\\\",\\\"KeyNo\\\":\\\"0855f3cbb039b37c363263a12864c4e6\\\",\\\"LawFirmList\\\":[{\\\"LawFirm_KeyNo\\\":\\\"\\\",\\\"LawFirm_Name\\\":\\\"\\\",\\\"LawyerList\\\":[{\\\"Lawyer_Name\\\":\\\"金鼎\\\",\\\"Lawyer_Role\\\":\\\"委托诉讼代理人\\\",\\\"Lawyer_KeyNo\\\":\\\"\\\"}]}],\\\"RoleTag\\\":0,\\\"Name\\\":\\\"廊坊华越管道工程技术有限公司\\\"}, {\\\"Role\\\":\\\"被告\\\",\\\"KeyNo\\\":\\\"16bb4977d60efb62a0564b4c8d584157\\\",\\\"LawFirmList\\\":[],\\\"RoleTag\\\":1,\\\"Name\\\":\\\"江西强源市政工程有限公司\\\"}, {\\\"Role\\\":\\\"被告\\\",\\\"KeyNo\\\":\\\"ga416b2046bc05bb59f1b91b429d02a0\\\",\\\"LawFirmList\\\":[],\\\"RoleTag\\\":1,\\\"Name\\\":\\\"宜丰县城市管理局\\\"}]\"}");

        lianList.add("{\"id\":\"adb9c973cd1ec63547a86b9db20490f9\",\"companynames\":\"廊坊华越管道工程技术有限公司,江西强源市政工程有限公司,宜丰县城市管理局,0855f3cbb039b37c363263a12864c4e6,16bb4977d60efb62a0564b4c8d584157,ga416b2046bc05bb59f1b91b429d02a0\",\"anno\":\"（2019）赣0924民初1595号\",\"provincecode\":\"JX\",\"courtname\":\"江西省宜春市宜丰县人民法院\",\"punishdate\":1571241600,\"isvalid\":0,\"prosecutorlist\":\"[{\\\"KeyNo\\\":\\\"0855f3cbb039b37c363263a12864c4e6\\\",\\\"Name\\\":\\\"廊坊华越管道工程技术有限公司\\\",\\\"Org\\\":0,\\\"ShowName\\\":\\\"廊坊华越管道工程技术有限公司\\\",\\\"T\\\":\\\"5\\\"}]\",\"defendantlist\":\"[{\\\"KeyNo\\\":\\\"16bb4977d60efb62a0564b4c8d584157\\\",\\\"Name\\\":\\\"江西强源市政工程有限公司\\\",\\\"Org\\\":0,\\\"ShowName\\\":\\\"江西强源市政工程有限公司\\\",\\\"T\\\":\\\"6\\\"},{\\\"KeyNo\\\":\\\"ga416b2046bc05bb59f1b91b429d02a0\\\",\\\"Name\\\":\\\"宜丰县城市管理局\\\",\\\"Org\\\":4,\\\"ShowName\\\":\\\"宜丰县城市管理局\\\",\\\"T\\\":\\\"6\\\"}]\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"0855f3cbb039b37c363263a12864c4e6\\\",\\\"Name\\\":\\\"廊坊华越管道工程技术有限公司\\\",\\\"Org\\\":0,\\\"Role\\\":\\\"原告\\\",\\\"RoleTag\\\":0,\\\"RoleType\\\":11,\\\"ShowName\\\":\\\"廊坊华越管道工程技术有限公司\\\",\\\"Source\\\":1},{\\\"KeyNo\\\":\\\"16bb4977d60efb62a0564b4c8d584157\\\",\\\"Name\\\":\\\"江西强源市政工程有限公司\\\",\\\"Org\\\":0,\\\"Role\\\":\\\"被告\\\",\\\"RoleTag\\\":1,\\\"RoleType\\\":21,\\\"ShowName\\\":\\\"江西强源市政工程有限公司\\\",\\\"Source\\\":1},{\\\"KeyNo\\\":\\\"ga416b2046bc05bb59f1b91b429d02a0\\\",\\\"Name\\\":\\\"宜丰县城市管理局\\\",\\\"Org\\\":4,\\\"Role\\\":\\\"被告\\\",\\\"RoleTag\\\":1,\\\"RoleType\\\":21,\\\"ShowName\\\":\\\"宜丰县城市管理局\\\",\\\"Source\\\":1}]\"}");


        String output = new GetDetailInfoV3UDF().evaluate(MD5Util.ecodeByMD5(anno), anno, 1, "RT", sxList, zxList, xgList, caseList
                , pcczList, zbList, xjpgList, gqdjList, sdggList, fyggList, ktggList, lianList, hbcfList
                , xzcfList, sfpmList, sqtjList, xzcjList, xdpgjgList, xsggList);
        System.out.println(output);
    }


}
