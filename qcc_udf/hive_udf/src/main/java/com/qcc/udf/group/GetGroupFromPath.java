package com.qcc.udf.group;

import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import com.qcc.udf.graph.PartnerPathOut;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class GetGroupFromPath extends UDF {
    //    public static void main(String[] args) {
//        String msg = "663be690e77ae7443f882ffe8315dd29-8f98a06429fb5a799b279673ff601710-h43613e95d04ba1393a76f4c5fe4d82f-6282c30fe71baa364185b5daedc27ec9-";
//        String result = evaluate(msg);
//        System.out.printf(result);
//    }
    private static final String ZYHJTZ_REG = "((?!78c200dd24178853e4a985d918c6bc2e)[0-9a-z]){32}-210b64792e1e20d8b2cd21a65720e471";

    public static String evaluate(String path) {
        GroupOutItem outItem = new GroupOutItem();
        try {
            if (StringUtils.isBlank(path) || path.length() <= 32 || !path.contains("-")) {
                throw new Exception("path不正确");
            }
            if (RegexHelper.isFind(path, "[thoz]{1}[0-9a-f]{31}-$")) {
                path = path.replaceAll("-$", "");
            }
            List<String> nodeList = Arrays.asList(path.split("-", -1));
            if (CollectionUtils.isEmpty(nodeList) || nodeList.size() <= 1) {
                throw new Exception("path不正确");
            }
            String endNode = nodeList.get(nodeList.size() - 1);
            //先判断最终节点是不是重复节点
            boolean isDuplicate = StringUtils.isNotBlank(endNode) && path.indexOf(endNode) != path.lastIndexOf(endNode);
            if (isDuplicate) {
                String truncationPath = path.substring(0, path.indexOf(endNode) - 1);
                if (StringUtils.isNotBlank(truncationPath) && truncationPath.contains("-")) {
                    nodeList = Arrays.asList(truncationPath.split("-"));
                    if (CollectionUtils.isNotEmpty(nodeList) && nodeList.size() >= 2) {
                        outItem.setKeyNo(nodeList.get(nodeList.size() - 1));
                        outItem.setPosition(nodeList.size() - 1);
                    }
                }
            } else {
                if (RegexHelper.isFind(path, ZYHJTZ_REG)) {
                    int index = nodeList.indexOf("210b64792e1e20d8b2cd21a65720e471");
                    if (index >= 2) {
                        outItem.setKeyNo(nodeList.get(index - 1));
                        outItem.setPosition(index - 1);
                    }
                }
                //如果是pgsxj，则取下一级；如果实控人是空，则path必须大于2个，取下一级
                else if (RegexHelper.isFind(endNode, "^[pgsxj]") || StringUtils.isBlank(endNode)) {
                    if (nodeList.size() > 2) {
                        outItem.setKeyNo(nodeList.get(nodeList.size() - 2));
                        outItem.setPosition(nodeList.size() - 2);
                    }
                }
                //如果实控人是正常的公司，则path必须大于1个，取当前
                else if (RegexHelper.isFind(endNode, "^[0-9a-f]")) {
                    if (nodeList.size() > 1) {
                        outItem.setKeyNo(nodeList.get(nodeList.size() - 1));
                        outItem.setPosition(nodeList.size() - 1);
                    }
                }
                //如果是非大陆企业的，取第一个出现的
                else if (nodeList.stream().anyMatch(i -> RegexHelper.isFind(i, "^[thoz]"))) {
                    String firstItem = nodeList.stream().filter(i -> RegexHelper.isFind(i, "^[thoz]")).findFirst().get();
                    int idx = nodeList.indexOf(firstItem);
                    if (idx > 0) {
                        outItem.setKeyNo(firstItem);
                        outItem.setPosition(idx);
                    }
                }
            }
        } catch (Exception e) {
        }
        return JSONObject.toJSONString(outItem);
    }
}