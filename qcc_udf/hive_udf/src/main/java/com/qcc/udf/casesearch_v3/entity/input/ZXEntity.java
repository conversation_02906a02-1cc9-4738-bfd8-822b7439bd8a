package com.qcc.udf.casesearch_v3.entity.input;

import com.alibaba.fastjson.JSON;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.casesearch_v3.enums.CaseCategoryEnum;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import com.qcc.udf.cpws.casesearch_v2.AnnoStandard;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import com.google.common.base.Strings;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: zhangqiang
 * @Date: 2020/11/11 17:54
 * @Description:被执行
 */
@Data
public class ZXEntity extends BaseCaseEntity {

    private String id;
    private String anno;
    private String companynames;
    private String isvalid;
    private String courtname;
    private String provincecode;

    private String nameandkeyno;
    private long liandate;
    private String sqrinfo;
    private String biaodi;

    private String beforecaseno;

    private List<NameAndKeyNoEntity> nameandkeynoEntityList;
    private List<NameAndKeyNoEntity> sqrinfoEntityList;

    public static List<ZXEntity> convert(List<String> jsonList) {
        List<ZXEntity> list = new ArrayList<>();
        ZXEntity entity = null;
        if(CollectionUtils.isEmpty(jsonList)){
            return list;
        }
        AnnoStandard annoStandard = new AnnoStandard();
        for (String json : jsonList) {
            if(Strings.isNullOrEmpty(json)){
                continue;
            }
            entity = JSON.parseObject(json, ZXEntity.class);
            if(entity == null || Strings.isNullOrEmpty(entity.getId())){
                continue;
            }

            String str = entity.getNameandkeyno();

            if (!Strings.isNullOrEmpty(str)) {
                entity.setNameandkeynoEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getNameandkeynoEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                }
            }else{
                entity.setNameandkeynoEntityList(new ArrayList<>());
            }
            str = entity.getSqrinfo();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setSqrinfoEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getSqrinfoEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                }
            } else {
                entity.setSqrinfoEntityList(new ArrayList<>());
            }

            //公共字段赋值
            entity.setBaseCaseNo(entity.getAnno());
            entity.setBaseCaseCategoryEnum(CaseCategoryEnum.ZX);
            if(!Strings.isNullOrEmpty(entity.getCompanynames())){
                entity.setBaseSearchWordSet(Arrays.stream(entity.getCompanynames().split(","))
                        .collect(Collectors.toSet()));
            }

            entity.setBaseCourt(entity.getCourtname());
            entity.setBaseProvinceCode(entity.getProvincecode());
            entity.setBaseNameKeyNoList(entity.getNameandkeynoEntityList());
            entity.setBaseId(entity.getBaseCaseCategoryEnum().getType()+"_"+entity.getId());
            String caseType= CommonV3Util.getCaseType(CommonV3Util.getCaseNo(entity.getBaseCaseNo()));

            Set<String> baseBeforeNoSet =new HashSet<>();
            baseBeforeNoSet.addAll(CommonV3Util.getAllCaseNo(annoStandard.evaluate(entity.getBeforecaseno())));
            entity.setBaseBeforeNoSet(baseBeforeNoSet);

            //案件类型为空的数据直接过滤
            if(Strings.isNullOrEmpty(caseType)){
                continue;
            }
            list.add(entity);
        }
        return list;
    }

}
