package com.qcc.udf.tag.dimtype;

import com.google.common.collect.Lists;
import com.qcc.udf.tag.TagEntity;
import com.qcc.udf.tag.keywordEnum.LDKeywordMatchEnum;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 劳动争议-相关维度关键词
 */
public enum LDDimtypeKeywordEnum {
    //行政处罚
    XZCF(DimensionEnum.XING_ZHENG_CHU_FA, new LDKeywordMatchEnum[]{LDKeywordMatchEnum.LDF, LDKeywordMatchEnum.LDGX, LDKeywordMatchEnum.LDHT, LDKeywordMatchEnum.TG, LDKeywordMatchEnum.TQ, LDKeywordMatchEnum.W_FF, LDKeywordMatchEnum.W_ZF, LDKeywordMatchEnum.DEFAULT}),
    //劳动仲裁-开庭
    LDZC_KT(DimensionEnum.LAO_DONG_ZHONG_CAI_KAI_TING_GONG_GAO, new LDKeywordMatchEnum[]{LDKeywordMatchEnum.HT, LDKeywordMatchEnum.LDGX, LDKeywordMatchEnum.WY, LDKeywordMatchEnum.BC, LDKeywordMatchEnum.SB, LDKeywordMatchEnum.BX, LDKeywordMatchEnum.DEFAULT}),
    //劳动仲裁-送达
    LDZC_SD(DimensionEnum.LAO_DONG_ZHONG_CAI_SONG_DA_GONG_GAO, new LDKeywordMatchEnum[]{LDKeywordMatchEnum.DEFAULT}),
    //黑名单
    HMD(DimensionEnum.HEI_MING_DAN, new LDKeywordMatchEnum[]{LDKeywordMatchEnum.TQ, LDKeywordMatchEnum.LDBZ_TQ}),
    //严重违法
    YZWF(DimensionEnum.YAN_ZHONG_WEI_FA, new LDKeywordMatchEnum[]{LDKeywordMatchEnum.TQGZ_YZWF}),
    //双随机抽查
    SSJCC(DimensionEnum.DOUBLE_RANDOM_CHECK, new LDKeywordMatchEnum[]{LDKeywordMatchEnum.LDGX, LDKeywordMatchEnum.LDHT, LDKeywordMatchEnum.SB, LDKeywordMatchEnum.BX, LDKeywordMatchEnum.TG, LDKeywordMatchEnum.DEFAULT}),
    //抽查检查
    CYJC(DimensionEnum.CHOU_CHA_JIAN_CHA, new LDKeywordMatchEnum[]{LDKeywordMatchEnum.LDGX, LDKeywordMatchEnum.LDHT, LDKeywordMatchEnum.TQ, LDKeywordMatchEnum.W_FF, LDKeywordMatchEnum.W_ZF, LDKeywordMatchEnum.SB, LDKeywordMatchEnum.BX, LDKeywordMatchEnum.DEFAULT}),

    ;


    private DimensionEnum dimensionEnum;
    private LDKeywordMatchEnum[] lDKeywordMatchEnum;

    LDDimtypeKeywordEnum(DimensionEnum dimensionEnum, LDKeywordMatchEnum[] lDKeywordMatchEnum) {
        this.dimensionEnum = dimensionEnum;
        this.lDKeywordMatchEnum = lDKeywordMatchEnum;
    }

    private static final Map<DimensionEnum, LDKeywordMatchEnum[]> lookup = new LinkedHashMap<>();

    static {
        EnumSet.allOf(LDDimtypeKeywordEnum.class).stream().forEach(e -> {
                    lookup.put(e.dimensionEnum, e.lDKeywordMatchEnum);
                }
        );
    }

    public static List<TagEntity> find(DimensionEnum dimensionEnum, String keyword) {
        List<TagEntity> tagEntities = new LinkedList<>();
        LDKeywordMatchEnum[] ldKeywordMatchEnums = getLDKeywordMatchEnums(dimensionEnum);
        if (ldKeywordMatchEnums == null) {
            return tagEntities;
        }
        for (LDKeywordMatchEnum ldkmEnum : ldKeywordMatchEnums) {
            if (LDKeywordMatchEnum.DEFAULT == ldkmEnum){
                continue;
            }
            if (checkLikeRole(keyword, ldkmEnum.getKeyword())) {
                tagEntities.add(ldkmEnum.getTagEnum().getTagEntity());
            }
        }
        if (tagEntities.size() > 0) {
            return tagEntities;
        }
        boolean flag = Arrays.stream(ldKeywordMatchEnums).anyMatch(e -> e == LDKeywordMatchEnum.DEFAULT);
        if (flag) {
            return Lists.newArrayList(LDKeywordMatchEnum.DEFAULT.getTagEnum().getTagEntity());
        }
        return tagEntities;
    }


    public static LDKeywordMatchEnum[] getLDKeywordMatchEnums(DimensionEnum dimensionEnum) {
        return lookup.get(dimensionEnum);
    }

    private static boolean checkLikeRole(String role, String regex) {
        Pattern p = Pattern.compile(regex);
        Matcher matcher = p.matcher(role);
        return matcher.find();
    }

    public static void main(String[] args) {
        DimensionEnum dimensionEnum = DimensionEnum.DEFAULT_VALUE;
        String keyword = "未发放员工薪酬";
        System.out.println(find(dimensionEnum, keyword));
    }
}
