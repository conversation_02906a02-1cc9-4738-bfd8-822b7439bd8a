package com.qcc.udf.cpws.wjp;


import java.util.EnumSet;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 身份关键词（相似身份）
 */
public enum PartyRoleNamelikeEnum {
    YG("^原告.+", PartyRoleCodeEnum.YG),
    SQZXR("^申请执行.+", PartyRoleCodeEnum.SQZXR),
    SSR("^上诉.+", PartyRoleCodeEnum.SSR),
    SQR("^申请[^执行].*", PartyRoleCodeEnum.SQR),
    TBCX_SQR(".*[^被]申请人$", PartyRoleCodeEnum.SQR),
    ZSSQR("^再审申请人.+", PartyRoleCodeEnum.SQR),
    BG("^被告[^人].*", PartyRoleCodeEnum.BG),
    BZXR("^被执行人.+", PartyRoleCodeEnum.BZXR),
    BSSR("^被上诉人.+", PartyRoleCodeEnum.BSSR),
    ZS_BSQR("再审被申请人.+", PartyRoleCodeEnum.BSQR),
    TBCX_BSQR("^被申请[^执行].*", PartyRoleCodeEnum.BSQR),
    BSQR(".*被申请人$", PartyRoleCodeEnum.BSQR),
    DSR("^第三人.+", PartyRoleCodeEnum.DSR),
    ;


    private String name;
    private PartyRoleCodeEnum roleCodeEnum;

    PartyRoleNamelikeEnum(String name, PartyRoleCodeEnum roleCodeEnum) {
        this.name = name;
        this.roleCodeEnum = roleCodeEnum;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public PartyRoleCodeEnum getRoleCodeEnum() {
        return roleCodeEnum;
    }

    public void setRoleCodeEnum(PartyRoleCodeEnum roleCodeEnum) {
        this.roleCodeEnum = roleCodeEnum;
    }

    private static final Map<String, PartyRoleCodeEnum> lookup = new LinkedHashMap<>();
    static {
        EnumSet.allOf(PartyRoleNamelikeEnum.class).stream().forEach(e -> {
                    lookup.put(e.name, e.roleCodeEnum);
                }
        );
    }

    public static PartyRoleCodeEnum find(String role) {
        if (role == null) {
            return PartyRoleCodeEnum.QT;
        }
        for (Map.Entry<String, PartyRoleCodeEnum> entry : lookup.entrySet()) {
            if (checkLikeRole(role,entry.getKey())) {
                return entry.getValue();
            }
        }
        return PartyRoleCodeEnum.QT;
    }

    private static boolean checkLikeRole(String role,String regex){
        Pattern p = Pattern.compile(regex);
        Matcher matcher = p.matcher(role);
        return matcher.find();
    }

    public static void main(String[] args) {
        String role ="原审申请人";
        String regex = ".*[^被]申请人$";
        System.out.println(checkLikeRole(role,regex));
    }
}
