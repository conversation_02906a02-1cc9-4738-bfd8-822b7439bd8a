package com.qcc.udf.kzz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * 提取案件相关金额
 *
 * <AUTHOR>
 * @date 2022/4/14
 */
public class GetCaseAmount extends UDF {

    private static String[] types = new String[]{"", "案件金额", "执行标的", "未履行金额"};

    /**
     * @param amtInfo 金额信息
     * @param keyNo   企业keyNo
     * @param type    1-案件金额,2-案件执行标的金额,3-未履行金额
     * @return
     */
    public static double evaluate(String amtInfo, String keyNo, int type) {
        double result = 0;
        try {
            if (StringUtils.isNotBlank(amtInfo) && StringUtils.isNotBlank(keyNo) && type > 0 && type < 4) {
                JSONObject jsonObject = JSON.parseObject(amtInfo);

                for (String key : jsonObject.keySet()) {
                    if (key.equals(keyNo)) {
                        JSONObject amtJson = jsonObject.getJSONObject(key);
                        String amt = amtJson.getString("Amt");
                        int isValid = amtJson.getIntValue("IsValid");
                        String typeStr = amtJson.getString("Type");
                        if (isValid == 1 && StringUtils.isNotBlank(typeStr) && typeStr.equals(types[type]) && StringUtils.isNotBlank(amt)) {
                            result = Double.valueOf(amt);
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {

        }
        return result;
    }
//    public static void main(String[] args) {
//        String content = "{\"pd18bae82c6e6ac579b6121740a6fb03\":{\"Amt\":\"51574\",\"IsValid\":\"1\",\"Type\":\"未履行金额\"}}";
//        double region = evaluate(content,"pd18bae82c6e6ac579b6121740a6fb03",3);
//        System.out.println(region);
//    }
}
