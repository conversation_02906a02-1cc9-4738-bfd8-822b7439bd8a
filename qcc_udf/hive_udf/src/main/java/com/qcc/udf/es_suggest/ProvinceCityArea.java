package com.qcc.udf.es_suggest;


import com.qcc.udf.es_suggest.util.AdministrativeDistrictFilter;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDTF;
import org.apache.hadoop.hive.serde2.lazy.LazyInteger;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2020/6/9
 * @Description 省市区划分
 */
public class ProvinceCityArea extends GenericUDTF {

    @Override
    public void process(Object[] os) throws HiveException {
        Integer code = ((<PERSON>zyInteger) os[2]).getWritableObject().get();
        if (!Column.NAME.equals(Column.get(code)) && !Column.ADDRESS.equals(Column.get(code))) {
            forward(os);
            return;
        }

        String text = os[0].toString().trim();
        Set<String> result = new HashSet<>();

        String all = text;
        String value = AdministrativeDistrictFilter.smartDeleteAdministrativeDistrict(text);
        result.add(all);
        text = all.substring(0, all.indexOf(value)).replaceAll("[省市区]", "") + value;
        result.add(text);

        all = AdministrativeDistrictFilter.deleteProvinceAtBeginning(all);
        result.add(all);
        text = all.substring(0, all.indexOf(value)).replaceAll("[省市区]", "") + value;
        result.add(text);

        all = AdministrativeDistrictFilter.deleteCityAtBeginning(all);
        result.add(all);
        text = all.substring(0, all.indexOf(value)).replaceAll("[省市区]", "") + value;
        result.add(text);

        all = AdministrativeDistrictFilter.deleteCountyAtBeginning(all);
        text = all.substring(0, all.indexOf(value)).replaceAll("[省市区]", "") + value;
        result.add(text);
        result.remove("");

        Object[] r;
        for (String s : result) {
            r = new Object[3];
            r[0] = s;
            r[1] = os[1];
            r[2] = os[2];
            forward(r);
        }
    }

    @Override
    public void close() {

    }

    @Override
    public StructObjectInspector initialize(ObjectInspector[] args) {
        return ObjectInspectorFactory.getStandardStructObjectInspector(Column.fields(), Column.inspectors());
    }
}
