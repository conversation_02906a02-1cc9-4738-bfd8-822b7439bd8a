package com.qcc.udf.cpws.wjp;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 *
 * 嘗試提取時分秒
 *
 * <AUTHOR>
 * @Date 2024/2/29
 **/
public class SessionTimeUdf {

    public static String evaluate(String text) {
        String rp = text;
        try {
            rp = extDate(text.replace("年年","年"));
            if (StringUtils.isEmpty(rp)) {
                rp = CommonDateCleanUtil.cleanDate(text,"yyyy-MM-dd HH:mm:ss" );
            }
        } catch (Exception ignored) {
        }
        //兜底，返回原始的ai time
        if (StringUtils.isEmpty(rp)){
            return text;
        }
        return rp;
    }

    public static String extDate(String text) {
        // 匹配 "14时30分, 2022年10月28日" 类型的时间格式
        Pattern pattern1 = Pattern.compile("(\\d{1,2})时(\\d{1,2})分[^0-9]*(\\d{4})年(\\d{1,2})月(\\d{1,2})日");
        Matcher matcher1 = pattern1.matcher(text);
        if (matcher1.find()) {
            String hour = String.format("%02d", Integer.parseInt(matcher1.group(1)));
            String minute = String.format("%02d", Integer.parseInt(matcher1.group(2)));
            String year = matcher1.group(3);
            String month = String.format("%02d", Integer.parseInt(matcher1.group(4)));
            String day = String.format("%02d", Integer.parseInt(matcher1.group(5)));
            return year + "-" + month + "-" + day + " " + hour + ":" + minute + ":00";
        }
        String rege2 = "(\\d{4})[-年](\\d{1,2})[-月](\\d{1,2})[日 ]?(上午|下午)?[^0-9]*(\\d{1,2})?[时点:]?([0-5]?\\d)?(?:分|时分|时整)?";
//        String rege2="(\\d{4})[-|年](\\d{1,2})[-|月](\\d{1,2})[日| ]?(?:上午|下午)?[^0-9]*(\\d{1,2})?[时点:]?([0-5]?\\d)?(?:分|时分|时整)?";
        Pattern pattern2 = Pattern.compile(rege2);
        Matcher matcher2 = pattern2.matcher(text);
        if (matcher2.find()) {
            String year = matcher2.group(1);
            String month = String.format("%02d", Integer.parseInt(matcher2.group(2)));
            String day = String.format("%02d", Integer.parseInt(matcher2.group(3)));
            String ampm= StringUtils.isNotEmpty(matcher2.group(4))?matcher2.group(4):"";
            String hour = String.format("%02d", Integer.parseInt(matcher2.group(5)));
            String minute = "00";  // 默认分钟为00
            if (matcher2.group(6) != null) {
                minute = String.format("%02d", Integer.parseInt(matcher2.group(6)));
            }
             return year + "-" + month + "-" + day + " "+ampm+" " + hour + ":" + minute + ":00";
        }
        return "";
    }

}
