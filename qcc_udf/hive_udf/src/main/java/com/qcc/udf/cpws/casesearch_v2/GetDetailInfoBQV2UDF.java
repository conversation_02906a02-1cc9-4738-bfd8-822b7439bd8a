package com.qcc.udf.cpws.casesearch_v2;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.cpws.ExtractCaseTrialRoundUDF;
import com.qcc.udf.cpws.ExtractCaseTypeUDF;
import com.qcc.udf.cpws.ExtractCourtNameFromCaseNoUDF;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

public class GetDetailInfoBQV2UDF extends UDF {

    public String evaluate(List<String> sourceLianList, List<String> sourceKtggList,
                           List<String> sourceSdggList, List<String> sourceFyggList,
                           List<String> sourceCaseList,List<String> sourceGqdjList) {

        Collections.sort(sourceLianList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceKtggList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceSdggList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceFyggList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceCaseList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceGqdjList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });

        LawSuitV2Entity inputLawSuitEntity = new LawSuitV2Entity();
        inputLawSuitEntity.setLianList(sourceLianList);
        inputLawSuitEntity.setKtggList(sourceKtggList);
        inputLawSuitEntity.setSdggList(sourceSdggList);
        inputLawSuitEntity.setFyggList(sourceFyggList);
        inputLawSuitEntity.setCaseList(sourceCaseList);
        inputLawSuitEntity.setGqdjList(sourceGqdjList);

        List<LawSuitV2Entity> lawSuitEntityList = CommonV2Util.getLawSuitEntityList(inputLawSuitEntity, "bq");

        JSONArray detailInfoArray = new JSONArray();
        for (LawSuitV2Entity lawSuitEntity : lawSuitEntityList) {
            List<String> lianList = lawSuitEntity.getLianList();
            List<String> ktggList = lawSuitEntity.getKtggList();
            List<String> sdggList = lawSuitEntity.getSdggList();
            List<String> fyggList = lawSuitEntity.getFyggList();
            List<String> caseList = lawSuitEntity.getCaseList();
            List<String> gqdjList = lawSuitEntity.getGqdjList();

            Set<String> provinceCodeSet = CommonV2Util.collectProvinceCode(lianList, ktggList, sdggList, fyggList, caseList,gqdjList);
            for (String provinceCode : provinceCodeSet) {
                JSONObject result = new JSONObject();
                try {
                    /**
                     * 各审理程序列表对应字段
                     */
                    // 审理程序总数
                    Set<String> caseNoSet = new LinkedHashSet<>();
                    // 搜索关键字集合
                    Set<String> searchWordSet = new TreeSet<>();
                    // 所有时间节点的map集合（key-> 时间戳; value->表示当前的节点状态，审判程序 + (立案/法院公告/开庭公告/送达公告)发布日期 或 判决日期 或 裁定日期）
                    Map<Long, String> dateNodeMap = new HashMap<>();

                    // 案号分组
                    Map<String, JSONObject> anNoMap = new LinkedHashMap<>();
                    Set<String> lianIdSet = new LinkedHashSet<>();
                    Set<String> ktggIdSet = new LinkedHashSet<>();
                    Set<String> sdggIdSet = new LinkedHashSet<>();
                    Set<String> fyggIdSet = new LinkedHashSet<>();
                    Set<String> caseIdSet = new LinkedHashSet<>();
                    Set<String> gqdjIdSet = new LinkedHashSet<>();

                    dataClean(provinceCode, lianList, 1, anNoMap, caseNoSet, lianIdSet, searchWordSet, dateNodeMap);
                    dataClean(provinceCode, ktggList, 2, anNoMap, caseNoSet, ktggIdSet, searchWordSet, dateNodeMap);
                    dataClean(provinceCode, sdggList, 3, anNoMap, caseNoSet, sdggIdSet, searchWordSet, dateNodeMap);
                    dataClean(provinceCode, fyggList, 4, anNoMap, caseNoSet, fyggIdSet, searchWordSet, dateNodeMap);
                    dataClean(provinceCode, caseList, 5, anNoMap, caseNoSet, caseIdSet, searchWordSet, dateNodeMap);
                    dataClean(provinceCode, gqdjList, 8, anNoMap, caseNoSet, gqdjIdSet, searchWordSet, dateNodeMap);

                    // 按照案号显示数据
                    JSONArray array = new JSONArray();
                    for (String str : caseNoSet) {
                        /**
                         * 兼容执行案件中的infoList项
                         */
                        JSONObject jsonObj = anNoMap.get(str);
                        if (jsonObj != null) {
                            jsonObj = CommonV2Util.addExternalFieldToJsonStruct(jsonObj);
                            Long latestTimestamp = CommonV2Util.getLatestTimestampFromInfoListItem(jsonObj);
                            jsonObj.put("LatestTimestamp", latestTimestamp);
                            array.add(jsonObj);
                        }
                    }
                    result.put("InfoList", array);
                    result.put("AnNoList", StringUtils.join(caseNoSet, ","));

                    /**
                     * 案件统计字段
                     */
                    // 最新审理程序
                    result.put("LatestTrialRound", CommonV2Util.getLatestTrialRoundFromInfoList(array));
                    // 审理程序总数
                    result.put("AnnoCnt", caseNoSet.size());
                    // 关联裁判文书数
                    result.put("CaseCnt", caseIdSet.size());
                    // 关联立案信息数
                    result.put("LianCnt", lianIdSet.size());
                    // 关联开庭公告数
                    result.put("KtggCnt", ktggIdSet.size());
                    // 关联送达公告数
                    result.put("SdggCnt", sdggIdSet.size());
                    // 关联法院公告数
                    result.put("FyggCnt", fyggIdSet.size());
                    // 关联股权冻结数
                    result.put("GqdjCnt", gqdjIdSet.size());

                    // 兼容字段处理
                    // 关联破产公告数
                    result.put("PcczCnt", 0);
                    // 关联被执行数
                    result.put("ZxCnt", 0);
                    // 关联失信数
                    result.put("SxCnt", 0);
                    // 关联限高数
                    result.put("XgCnt", 0);
                    // 关联终本案件数
                    result.put("ZbCnt", 0);
                    // 关联询价评估数
                    result.put("XjpgCnt", 0);
                    // 关联环保处罚数
                    result.put("HbcfCnt", 0);
                    // 关联行政处罚（工商）
                    result.put("CfgsCnt", 0);
                    // 关联行政处罚（信用中国）
                    result.put("CfxyCnt", 0);
                    // 关联行政处罚（地方）
                    result.put("CfdfCnt", 0);

                    /**
                     * 案件基础字段
                     */
                    // 分组法院信息
                    result.put("GroupCourt", lawSuitEntity.getCourt());
                    // 所在省份编码
                    result.put("Province", provinceCode);
                    // 案件名称
                    result.put("CaseName", CommonV2Util.getCaseNameFromInfoList(array, "bq"));
                    // 案件类型
                    result.put("CaseType", "保全案件");
                    // 关联的公司或个人信息
                    result.put("CompanyKeywords", CommonV2Util.getCompanyKeywordsFromSearchWordSet(searchWordSet));
                    // 相关案号
                    result.put("AnNoList", CommonV2Util.getKeywordsFromInfoList(array, "AnNo"));
                    // 列表中的案由
                    result.put("CaseReason", CommonV2Util.getCaseReasonFromInfoList(array));

                    String caseRoleInfo = CommonV2Util.getCaseRoleFromInfoList(array);
                    result.put("CaseRole", caseRoleInfo);
                    // 相关法院
                    result.put("CourtList", CommonV2Util.getKeywordsFromInfoList(array, "Court"));
                    // 相关检察院
                    result.put("ProcuratorateList", CommonV2Util.getKeywordsFromInfoList(array, "Procuratorate"));

                    Map<Long, String> sortedDateNodeMap = new LinkedHashMap<>();
                    dateNodeMap.entrySet().stream()
                            .sorted(Map.Entry.comparingByKey())
                            .forEachOrdered(e -> sortedDateNodeMap.put(e.getKey(), e.getValue()));

                    result.put("EarliestDate", -1L);
                    result.put("EarliestDateType", "");
                    result.put("LastestDate", -1);
                    result.put("LastestDateType", "");

                    Long earliestDate = -1L;
                    String earliestDateType = "";
                    Long lastestDate = -1L;
                    String lastestDateType = "";
                    boolean flag = true;
                    for (Map.Entry<Long, String> sortedDateNodeEntry : sortedDateNodeMap.entrySet()) {
                        if (flag) {
                            earliestDate = sortedDateNodeEntry.getKey();
                            earliestDateType = sortedDateNodeEntry.getValue();
                            flag = false;
                        }
                        lastestDate = sortedDateNodeEntry.getKey();
                        lastestDateType = sortedDateNodeEntry.getValue();
                    }
                    result.put("EarliestDate", earliestDate);
                    result.put("EarliestDateType", CommonV2Util.getDataTypeWithoutTrialRound(earliestDateType));
                    if (sortedDateNodeMap.size() > 1) {
                        result.put("LastestDate", lastestDate);
                        result.put("LastestDateType", CommonV2Util.getDataTypeWithoutTrialRound(lastestDateType));
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                detailInfoArray.add(result);
            }
        }

        JSONArray resDetailInfoJSONArray = new JSONArray();
        Iterator iterator = detailInfoArray.iterator();
        while (iterator.hasNext()) {
            try {
                JSONObject jsonObject = (JSONObject) iterator.next();
                if (jsonObject.getLong("AnnoCnt") > 0) {
                    resDetailInfoJSONArray.add(jsonObject);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return resDetailInfoJSONArray.toJSONString();
    }

    /**
     *
     * @param provinceCode
     * @param infoList
     * @param type 1->立案信息 2->开庭公告 3->送达公告 4->法院公告 5->裁判文书 8->股权冻结
     * @param anNoMap
     * @param caseNoSet
     * @param idSet
     * @param searchWordSet
     * @param dateNodeMap
     */
    public static void dataClean(String provinceCode, List<String> infoList, int type, Map<String, JSONObject> anNoMap,
                                 Set<String> caseNoSet, Set<String> idSet, Set<String> searchWordSet, Map<Long, String> dateNodeMap){
        // 编辑数据
        if (infoList != null && infoList.size() > 0) {
            Iterator it = infoList.iterator();
            while(it.hasNext()){
                JSONObject json = JSONObject.parseObject(it.next().toString());
                if (json == null || json.isEmpty()) {
                    continue;
                }

                if (!json.getString("province").equals(provinceCode)) {
                    continue;
                }

                // 获取案号
                String anNo = CommonV2Util.full2Half(json.getString("anno"));
                if (type == 5){
                    anNo = CommonV2Util.full2Half(json.getString("caseno"));
                }else if (type == 8) {
                    anNo = CommonV2Util.full2Half(json.getString("executionnoticenum"));
                }
                // 过滤掉案号没有对应到执行案件类型的记录
                if (!new ExtractCaseTypeUDF().evaluate(anNo).equals("非诉保全审查案件")) {
                    continue;
                }
                anNo = anNo.split("之")[0];
                Set<String> anNoSet = Arrays.stream(anNo.split(",")).collect(Collectors.toSet());
                caseNoSet.add(String.join(",",anNoSet));

                // 部分字段的汇总逻辑
                try {
                    List<String> companyNameList = new ArrayList<>();
                    if (type == 1) {
                        companyNameList = Arrays.stream(json.getString("companykeywords").split(","))
                                .collect(Collectors.toList());
                    } else if (type == 2 || type == 3 || type == 5) {
                        companyNameList = Arrays.stream(json.getString("companynames").split(","))
                                .collect(Collectors.toList());
                    } else if (type == 4) {
                        JSONArray jsonArray = JSONArray.parseArray(json.getString("nameandkeyno"));
                        for (int i = 0; i < jsonArray.size(); i++) {
                            JSONObject jsonObject = jsonArray.getJSONObject(i);
                            companyNameList.add(jsonObject.getString("Name"));
                            companyNameList.add(jsonObject.getString("KeyNo"));
                        }
                    }else if (type == 8) {
                        companyNameList.add(json.getString("companyname"));
                        companyNameList.add(json.getString("keyno"));
                    }

                    // 汇总关联公司或个人信息
                    for (String companyName : companyNameList) {
                        searchWordSet.add(companyName);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

                // 判断该案号是否已经存在
                JSONObject jsonObject = new JSONObject();
                JSONArray itemArray = new JSONArray();
                JSONObject itemJson = new JSONObject();

                // 不存在，则创建新的对象
                if (!anNoMap.keySet().contains(anNo)){
                    // 列表数据
                    itemJson = editItemJson(json, type, dateNodeMap, anNo);
                }else{
                    // 存在则获取原有列表，进行数据补充
                    jsonObject = anNoMap.get(anNo);
                    // 列表数据
                    if (type == 1){
                        itemArray = jsonObject.getJSONArray("LianList");
                    }else if (type == 2){
                        itemArray = jsonObject.getJSONArray("KtggList");
                    }else if (type == 3){
                        itemArray = jsonObject.getJSONArray("SdggList");
                    }else if (type == 4){
                        itemArray = jsonObject.getJSONArray("FyggList");
                    }else if (type == 5){
                        itemArray = jsonObject.getJSONArray("CaseList");
                    }else if (type == 8) {
                        itemArray = jsonObject.getJSONArray("GqdjList");
                    }
                    if (!idSet.contains(json.getString("id"))){
                        itemJson = editItemJson(json, type, dateNodeMap, anNo);
                    }
                }
                idSet.add(json.getString("id"));
                itemArray = itemArray == null ? new JSONArray() : itemArray;
                itemArray.add(itemJson);
                if (type == 1){
                    jsonObject.put("LianList", itemArray);
                }else if (type == 2){
                    jsonObject.put("KtggList", itemArray);
                }else if (type == 3){
                    jsonObject.put("SdggList", itemArray);
                }else if (type == 4){
                    jsonObject.put("FyggList", itemArray);
                }else if (type == 5){
                    jsonObject.put("CaseList", itemArray);
                }else if (type == 8) {
                    jsonObject.put("GqdjList", itemArray);
                }
                jsonObject.put("AnNo", anNo);
                jsonObject.put("TrialRound", new ExtractCaseTrialRoundUDF().evaluate(anNo));
                jsonObject.put("CaseReason", "");
                jsonObject.put("Court", new ExtractCourtNameFromCaseNoUDF().evaluate(anNo));
                jsonObject.put("Procuratorate", "");
                jsonObject.put("ExecuteNo", "");        // 执行依据文书号

                if (type == 1 || type == 2) {
                    String caseRole = CommonV2Util.getCaseRoleFromLianOrKtggList(Arrays.asList(json.toJSONString()), type);
                    JSONArray caseRoleJsonArray = JSONArray.parseArray(caseRole);
                    if (caseRoleJsonArray != null && caseRoleJsonArray.size() > 0) {
                        JSONArray prosecutorArray = new JSONArray();
                        JSONArray defendantArray = new JSONArray();
                        for (int i = 0; i < caseRoleJsonArray.size(); i++) {
                            JSONObject caseRoleJson = caseRoleJsonArray.getJSONObject(i);

                            JSONObject jsonObj = new JSONObject();
                            jsonObj.put("Name", caseRoleJson.getString("P"));
                            jsonObj.put("KeyNo", caseRoleJson.getString("N"));
                            jsonObj.put("Role", caseRoleJson.getString("R"));
                            jsonObj.put("Org", caseRoleJson.getInteger("O"));

                            if (caseRoleJson.getString("R").equals("原告")) {
                                prosecutorArray.add(jsonObj);
                            } else if (caseRoleJson.getString("R").equals("被告")) {
                                defendantArray.add(jsonObj);
                            }
                        }
                        jsonObject.put("Prosecutor", prosecutorArray);
                        jsonObject.put("Defendant", defendantArray);
                    } else {
                        jsonObject.put("Prosecutor", new JSONArray());
                        jsonObject.put("Defendant", new JSONArray());
                    }

                    // 提取法院公告中的案由信息
                    if (type == 2) {
                        String caseReason = json.getString("casereason");
                        if (StringUtils.isNotBlank(caseReason)) {
                            jsonObject.put("CaseReason", caseReason);
                        }
                    }

                    // 法院提取
                    String court = json.getString("executegov");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }
                } else if (type == 3 || type == 4) {
                    if (jsonObject.getJSONArray("Defendant") == null) {
                        jsonObject.put("Defendant", new JSONArray());
                    }

                    if (jsonObject.getJSONArray("Prosecutor") == null) {
                        jsonObject.put("Prosecutor", new JSONArray());
                    }

                    String caseReason = json.getString("casereason");
                    if (StringUtils.isNotBlank(caseReason)) {
                        jsonObject.put("CaseReason", caseReason);
                    }

                    String caseRole = CommonV2Util.getCaseRoleFromLianOrKtggList(Arrays.asList(json.toJSONString()), 1);
                    JSONArray caseRoleJsonArray = JSONArray.parseArray(caseRole);
                    if (caseRoleJsonArray != null && caseRoleJsonArray.size() > 0) {
                        JSONArray prosecutorArray = new JSONArray();
                        JSONArray defendantArray = new JSONArray();
                        for (int i = 0; i < caseRoleJsonArray.size(); i++) {
                            JSONObject caseRoleJson = caseRoleJsonArray.getJSONObject(i);

                            JSONObject jsonObj = new JSONObject();
                            jsonObj.put("Name", caseRoleJson.getString("P"));
                            jsonObj.put("KeyNo", caseRoleJson.getString("N"));
                            jsonObj.put("Role", caseRoleJson.getString("R"));
                            jsonObj.put("Org", caseRoleJson.getInteger("O"));

                            if (caseRoleJson.getString("R").equals("原告")) {
                                prosecutorArray.add(jsonObj);
                            } else if (caseRoleJson.getString("R").equals("被告")) {
                                defendantArray.add(jsonObj);
                            }
                        }
                        jsonObject.put("Prosecutor", prosecutorArray);
                        jsonObject.put("Defendant", defendantArray);
                    } else {
                        jsonObject.put("Prosecutor", new JSONArray());
                        jsonObject.put("Defendant", new JSONArray());
                    }

                    // 法院提取
                    String court = json.getString("courtname");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }
                    String anotherCourt = json.getString("court");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", anotherCourt);
                    }
                } else if (type == 5) { // 从裁判文书信息中提取案由 / 当事人（双方）/ 执行法院
                    String trialRound = json.getString("trialround");
                    if (StringUtils.isNotBlank(trialRound)) {
                        jsonObject.put("TrialRound", trialRound);
                    }

                    String caseReason = json.getString("casereason");
                    if (StringUtils.isNotBlank(caseReason)) {
                        jsonObject.put("CaseReason", caseReason);
                    }

                    JSONArray prosecutorArray = CommonV2Util.getLitigantJSONArray(json.getString("prosecutor"), json.getString("caserole"));
                    if (prosecutorArray != null && prosecutorArray.size() > 0) {
                        jsonObject.put("Prosecutor", prosecutorArray);
                    }

                    JSONArray defendantArray = CommonV2Util.getLitigantJSONArray(json.getString("defendant"), json.getString("caserole"));
                    if (defendantArray != null && defendantArray.size() > 0) {
                        jsonObject.put("Defendant", defendantArray);
                    }

                    String court = json.getString("court");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }

                    JSONObject protestorganJsonObj = JSONObject.parseObject(json.getString("protestorgan"));
                    if (protestorganJsonObj != null && protestorganJsonObj.containsKey("name")) {
                        jsonObject.put("Procuratorate", protestorganJsonObj.getString("name"));
                    }
                }else if (type == 8) { // 股权冻结
                    JSONArray defendantJsonArray = new JSONArray();
                    JSONObject nameAndKeyNo = json.getJSONObject("relatedcompanyinfo");
                    if (nameAndKeyNo != null) {
                        JSONObject defendantJson = new JSONObject();
                        defendantJson.put("Name", nameAndKeyNo.getString("Name"));
                        defendantJson.put("KeyNo", nameAndKeyNo.getString("KeyNo"));
                        defendantJson.put("Org", nameAndKeyNo.getInteger("Org"));
                        defendantJson.put("Role", "当事人");
                        if (!defendantJsonArray.toJSONString().contains(defendantJson.getString("Name"))) {
                            defendantJsonArray.add(defendantJson);
                        }
                    }

                    if (jsonObject.getJSONArray("Defendant") == null || jsonObject.getJSONArray("Defendant").size() == 0) {
                        jsonObject.put("Defendant", defendantJsonArray);
                    }

                    String court = json.getString("enforcementcourt");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }
                }
                anNoMap.put(anNo, jsonObject);
            }
        }
    }

    public static JSONObject editItemJson(JSONObject jsonObject, int type, Map<Long, String> dateNodeMap, String anNo){
        JSONObject result = new JSONObject();

        String trialRound = new ExtractCaseTrialRoundUDF().evaluate(anNo);
        // 编辑字段
        result.put("Id", jsonObject.getString("id"));
        result.put("IsValid", jsonObject.getInteger("isvalid"));

        if (type == 1) {        // 立案信息（lianList）
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = CommonV2Util.parseDateToTimeStamp(jsonObject.getString("punishdate"));
            result.put("LianDate", publishDate);
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "立案日期");
            }
        } else if (type == 2) { // 开庭公告（ktggList）
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long openDate = CommonV2Util.parseDateToTimeStamp(jsonObject.getString("liandate"));
            result.put("OpenDate", openDate);
            if (openDate != -1) {
                dateNodeMap.put(openDate, trialRound + "|" + "开庭时间");
            }
        } else if (type == 3) { // 送达公告（sdggList）
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = -1L;
            if (jsonObject.containsKey("publishdate")){
                String publishdateStr = jsonObject.getString("publishdate");
                if (StringUtils.isNotBlank(publishdateStr) && StringUtils.isNumeric(publishdateStr.trim())){
                    publishDate = Long.parseLong(publishdateStr);
                }
            }
            result.put("PublishDate", publishDate);
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "送达公告发布日期");
            }
        } else if (type == 4) { // 法院公告（fyggList）
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("Category", jsonObject.getString("category"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = CommonV2Util.parseDateToTimeStamp(jsonObject.getString("publishdate"));
            result.put("PublishDate", publishDate);
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "法院公告刊登日期");
            }
        } else if (type == 5) { // 裁判文书（caseList）
            result.put("Id", jsonObject.getString("id"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));
            Long judgeDate = CommonV2Util.parseDateToTimeStamp(jsonObject.getString("judgedate"));
            result.put("JudgeDate", judgeDate);

            String docType = jsonObject.getString("doctype");
            if (StringUtils.isNotBlank(docType)) {
                if (docType.equals("ver")) {
                    result.put("DocType", "保全判决日期");
                    dateNodeMap.put(judgeDate, trialRound + "|" + "判决日期");
                    result.put("ResultType", "判决结果");
                } else {
                    result.put("DocType", "保全裁定日期");
                    dateNodeMap.put(judgeDate, trialRound + "|" + "裁定日期");
                    result.put("ResultType", "裁定结果");
                }
                result.put("Result", jsonObject.getString("judgeresult") == null ? "" : jsonObject.getString("judgeresult"));
            }
            result.put("Amt", jsonObject.getString("amountinvolved") == null ? "" : jsonObject.getString("amountinvolved"));
        }else if (type == 8) {
            result.put("Id", jsonObject.getString("no"));   // 股权冻结没有该字段，置空

            JSONArray nameAndKeyNoJsonArray = new JSONArray();
            nameAndKeyNoJsonArray.add(jsonObject.getJSONObject("relatedcompanyinfo"));
            result.put("NameAndKeyNo", nameAndKeyNoJsonArray);

            String category = jsonObject.getString("statuesdetail") != null ?
                    jsonObject.getString("statuesdetail") : "冻结";
            result.put("Category", "股权" + category);
            result.put("IsValid", 1);

            Long publishDate = -1L;
            try {
                JSONObject detailJson = jsonObject.getJSONObject("equityfreezedetail");
                if (detailJson == null) {
                    detailJson = jsonObject.getJSONObject("equityunfreezedetail");
                }

                if (detailJson != null && detailJson.containsKey("PublicDate")) {
                    String publicDate = detailJson.getString("PublicDate");
                    if (StringUtils.isNotBlank(publicDate)){
                        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                        publishDate = format.parse(detailJson.getString("PublicDate")).getTime() / 1000;
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            result.put("PublishDate", publishDate);
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "股权" + category + "公示日期");
            }
            // 被执行人
            JSONArray tmpArr = new JSONArray();
            JSONObject zhixingJson = new JSONObject();
            try {
                if (StringUtils.isNotEmpty(jsonObject.getString("executedby"))){
                    zhixingJson.put("Name", CommonV2Util.getStringOrEmptyString(jsonObject.getString("executedby")));
                    zhixingJson.put("KeyNo", CommonV2Util.getStringOrEmptyString(jsonObject.getString("executedkeyno")));
                    zhixingJson.put("Org", CommonV2Util.getOrgByKeyNo(
                            zhixingJson.getString("KeyNo"), zhixingJson.getString("Name")));
                    tmpArr.add(zhixingJson);
                }
            } catch (Exception ex) {
            }
            result.put("ZxNameAndKeyNo", tmpArr);
            result.put("EquityAmount", jsonObject.getString("equityamount") == null ? "" : jsonObject.getString("equityamount"));
        }

        return result;
    }

    public static void main(String[] args) {

        List<String> lianList = new ArrayList<>();

        List<String> ktggList = new ArrayList<>();



        List<String> sdggList= new ArrayList<>();

        List<String> fyggList = new ArrayList<>();
        List<String> caseList = new ArrayList<>();
        String caseStr1 ="{\"id\":\"e56d9d0715d1c955f9af61714b7de0a50\",\"defendant\":\"刘琳扬,张凯,杨贵真,张庆峰,姬秀华,杨玉顺,吕振兴,张维生\",\"prosecutor\":\"东光县青隆村镇银行股份有限公司\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"东光县青隆村镇银行股份有限公司\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"张凯\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"张庆峰\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"杨贵真\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"张维生\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"姬秀华\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"吕振兴\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"刘琳扬\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"杨玉顺\\\"}]\",\"caseno\":\"（2017）冀0923财保209号\",\"submitdate\":\"2019-06-25T00:00:00+08:00\",\"judgedate\":\"2017-12-15T00:00:00+08:00\",\"courtdate\":\"2019-06-25T00:00:00+08:00\",\"casereason\":\"其他民事\",\"isvalid\":\"1\",\"trialround\":\"非诉财产保全审查\",\"court\":\"东光县人民法院\",\"companynames\":\"刘琳扬,张凯,杨贵真,张庆峰,姬秀华,杨玉顺,吕振兴,东光县青隆村镇银行股份有限公司,张维生\",\"caserole\":\"[{\\\"P\\\":\\\"东光县青隆村镇银行股份有限公司\\\",\\\"R\\\":\\\"申请人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-1},{\\\"P\\\":\\\"张凯\\\",\\\"R\\\":\\\"被申请人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"张庆峰\\\",\\\"R\\\":\\\"被申请人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"杨贵真\\\",\\\"R\\\":\\\"被申请人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"张维生\\\",\\\"R\\\":\\\"被申请人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"姬秀华\\\",\\\"R\\\":\\\"被申请人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"吕振兴\\\",\\\"R\\\":\\\"被申请人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"刘琳扬\\\",\\\"R\\\":\\\"被申请人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"杨玉顺\\\",\\\"R\\\":\\\"被申请人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2}]\",\"doctype\":\"adj\",\"protestorgan\":\"\",\"province\":\"HB\",\"amountinvolved\":\"550000.00\",\"judgeresult\":\"查封、扣押、冻结被申请人张凯、张庆峰、杨贵真、张维生、姬秀华、吕振兴、刘琳扬、杨玉顺的银行存款550000元或其同等数额的财产。\"}";
        caseList.add(caseStr1);
        List<String> gqdjList = new ArrayList<>();

        String output = new GetDetailInfoBQV2UDF().evaluate(lianList, ktggList, sdggList, fyggList, caseList,gqdjList);
        System.out.println(output);

    }
}
