package com.qcc.udf.court_notice.anCleanMethods;

import com.qcc.udf.court_notice.anUtils.Util;

import java.util.HashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 清洗开庭公告
 * <AUTHOR>
 *
 */
public class CleanCourtUtil {

    public static String[] words2 = new String[]{"因", "涉嫌犯", "民间借贷", "擅自使用", "申请重审", "出资纠纷", "经济补偿", "出资纠纷", "侵害商标权", "商标权", "股权转让", "申请执行人执行异议", "执行异议",
            "遗赠扶养", "劳动争议", "土地承包", "相邻关系纠纷", "股东资格确认", "宣扬", "股权转让纠纷", "宣告失踪", "变更监护人", "债务清偿", "赠扶养协议", "信用卡纠纷", "名誉权纠纷", "行政非诉",
            "扶养协议", "公示催告程序", "排除妨害纠纷", "煤矿", "恢复原状", "申请宣告", "承包地征收补偿", "工伤保险", "强奸罪", "案外人执行", "盗伐林木", "人格权纠", "互易纠纷", "骗取",
            "滥用职权", "不服", "提供", "单位行贿罪", "玩忽职守", "容留他人", "金融借款", "借款合同", "广告合同", "侵犯", "受害责任", "走私", "婚姻家庭", "偷越国", "生命权", "故意伤害", "抢夺", "非法",
            "破坏", "运输合同", "持有", "房屋", "买卖", "建设工程施工合同", "供用", "承揽", "土地租赁", "租赁合同", "金融借款", "诉讼、仲裁、人民调解代理合同纠纷", "确认合同无效",
            "多式联运合同纠纷", "技术咨询合同纠纷", "挂靠经营合同纠纷", "劳务合同纠纷", "财产损失保险合同纠纷", "装饰装修合同纠纷", "技术转让合同纠纷", "拆迁合同纠纷",
            "委托合同纠纷", "责任保险合同纠纷", "变更抚养关系纠纷", "储蓄存款合同纠纷", "物业服务合同纠纷",
            "合同纠纷", "强迫", "婚约财产纠纷", "离婚", "盗窃", "窝藏", "机动车", "掩饰", "金融不良债权追偿纠纷",
            "买卖合同", "交通肇事", "财产损失", "故意", "抢劫", "财产损害", "医疗损害", "道路交通事故", "损害", "寻衅", "人事", "追偿", "合同诈骗", "诈骗", "赌博", "返还", "专利", "著作权权属", "侵权", "危险", "赡养", "拐卖", "受贿",
            "生命", "健康", "身体", "义务", "妨害", "同居", "过失", "敲诈", "合同", "抚养", "物权", "保险纠纷", "职务", "建设工程", "法定继承", "继承纠纷", "贪污",
            "其他一案", "合伙协议", "不当得利", "销售", "企业借贷", "不服治安", "滥伐", "所有权", "责任纠纷", "公示催告程序", "分家析产", "外人执行", "债权",
            "票据纠纷", "适用特殊程序", "申请", "采矿权", "相邻污染侵害", "侵害", "代位求偿", "基地使用", "养老", "抵押", "彩票", "奖券", "福利", "建设用地", "借记卡",
            "物权", "不正当", "票据追索", "解散纠纷", "典当纠纷", "引诱", "要求发给", "拒绝履行", "产品", "失火", "逃税", "假冒", "相邻", "建筑物", "行贿", "监护权",
            "劳动", "共有物", "修理", "重作", "更换", "合伙", "票据", "决议纠纷", "请求", "组织、领导", "拒不执行", "退伙", "行政", "荣誉", "挪用", "变更",
            "脱逃", "银行卡", "强制", "重大", "盈余分配", "解除", "放火", "嫖宿", "聚众", "开设", "猥亵", "传播", "占有", "夫妻财产", "共有", "与公司有关",
            "不动产登记", "决议效力", "信息公开", "著作权", "再审", "失业保险", "错误", "土地", "婚姻", "扶养费", "信用卡"
            , "收养关系", "商品房预约纠纷", "追索劳动报酬纠纷", "协助送达司法文书", "工伤认定", "撤销"
            , "医疗"
            , "借款"
            , "贩卖毒品"
            , "运输毒品"
            , "故意杀人"
            , "一案", "股东知情权纠纷", "离退休人员", "CLEAN_FLAG"};

    //执行法庭
    public static String getExecuteUnite(String context, String sourceFrom) {
        try {
            String ExecuteUnite = Util.parseRegex(context, "(在|于第| 在).*{1,10}(法庭）|审判庭|法庭)", -1).replaceAll("(在|于第|本院| 在|该院)", "");
            if ("法庭".equals(ExecuteUnite.trim())) {
                return null;
            }
            return ExecuteUnite;
        } catch (NullPointerException e) {
            return null;
        }
    }

    /**
     * 提取执行法院
     *
     * @param context：文本内容
     * @param sourceFrom：省份
     * @return
     */
    public static String getExecuteGov(String context, String sourceFrom) {
        if (Util.isEmpty(context)) {
            return null;
        }
        if ("天津".equals(sourceFrom)) {
            if (context.contains("定于")) {
                return context.substring(0, context.indexOf("定于"));
            }
        }
        if ("新疆".equals(sourceFrom)) {
            return Util.parseRegex(context, "在.*{4,20}法院", -1).replaceAll("(在)", "");
        }
        if ("陕西".equals(sourceFrom)) {
            String executeGov = Util.parseRegex(context, "(,|，|分|时).*{4,10}法院", -1);
            if (Util.isEmpty(executeGov)) {
                return null;
            }
            String str[] = {",", "，", "分", "时"};
            for (String string : str) {
                if (executeGov.contains(string)) {
                    executeGov = executeGov.substring(executeGov.indexOf(string) + 1, executeGov.length());
                    break;
                }
            }
            return executeGov;
        }
        return null;
    }

    //开庭时间
    public static String getOpneTime(String context) {
        //去除空格
        if (!Util.isEmpty(context)) {
            context = context.replaceAll(" ", "-");
            //案号可能会影响时间提取，这里除去案号再处理
            if (Util.notEmpty(getCaseNo(context))) {
                context = context.replaceAll(getCaseNo(context), "");
            }
        }
        try {
            //有的日期〇写成了○
            context = context.replaceAll("○|�", "〇");
            //防止第一院等特殊带一的情况干扰正则匹配时间
            if (context.contains("二〇")) {
                return Util.parseRegex(context, "二〇(\\d|:|：|-|年|月|日|时|分|秒|点|上午|下午|〇|一|二|三|四|五|六|七|八|九|十)+", -1);
            } else {
                return Util.parseRegex(context, "(\\d|:|：|-|年|月|日|时|分|秒|点|上午|下午)+", -1);
            }
        } catch (Exception e) {
            return null;
        }
    }

    //原因
    public static String getCaseReason(String contex) {
        // 原告
        String case_reason = "";
        try {
            // 被告
            if (contex.contains("合议庭成员")) {
                contex = contex.split("合议庭成员")[0];
            }
            int defentIndex = 0;
            for (String word : words2) {
                if (contex.indexOf(word) > 0) {
                    defentIndex = contex.lastIndexOf(word);
                    break;
                }
            }
            String[] reasonWord = new String[]{"等", "公司", "部", "厂"};
            // 原因
            if (defentIndex != 0) {
                case_reason = contex.substring(defentIndex);
            } else {
                for (String Word : reasonWord) {
                    if (contex.lastIndexOf(Word) >= 0) {
                        case_reason = contex.substring(contex.lastIndexOf(Word) + Word.length());
                        break;
                    }
                }
            }

            Pattern pattern = Pattern.compile("(特此公告|一案|。)");
            Matcher matcher = pattern.matcher(case_reason);
            int endIndex = 0;
            if (matcher.find()) {
                endIndex = matcher.end(1);
                case_reason = case_reason.substring(0, endIndex);
            }
            case_reason = case_reason.replaceAll("(特此公告|一案|。)", "");
            //西藏的特殊处理
            if (case_reason.trim().contains(" ")) {
                case_reason = case_reason.substring(0, case_reason.indexOf(" "));
            }
            return case_reason;
        } catch (Exception e) {
            return null;
        }
    }

    //当事人
    public static String getParty(String contex) {
        String party = getPartyContext(contex);
        //未能成功提取当事人
        if (Util.isEmpty(party)) {
            return null;
        }
        //成功提取，去掉前缀后缀
        party = party.replaceAll("(被上诉人|被申诉人|抗诉|原告：|被告：|被告人|原告人|被申请人)", "").replaceAll("被告|原告|申诉人|上诉人|申请人", "").replaceAll("(与|诉|及)", ",");
        if (party.startsWith(",")) {
            party = party.substring(1, party.length());
        }
        return party;
    }

    /**
     * 提取当事人的方法，带前缀后缀
     *
     * @param contex
     * @return
     */
    public static String getPartyContext(String contex) {
        if (Util.isEmpty(contex)) {
            return null;
        }
        String party = null;
        int flag = 0;
        String[] verbs = "审理,审,受理的,受理,".split(",");
        try {
            //去除特殊字符
            contex = contex.replaceAll("\\?|�", "");
            //提取当事人
            //西藏的比较特殊，审理后面紧跟案号，这里统一处理，把案号全部置空
            if (Util.notEmpty(getCaseNo(contex))) {
                contex = contex.replace(getCaseNo(contex), "");
            }
            contex = contex.split("自本公告发出之日")[0];
            contex = contex.replaceAll("审判", "").split("因")[0] + "CLEAN_FLAG";
            for (String verb : verbs) {
                if (contex.contains(verb) && flag == 0) {
                    for (String word : words2) {
                        if (contex.indexOf(word) > 0) {
                            //处理合伙、有限合伙
                            if ("合伙".equals(word) && contex.contains("有限合伙")) {
                                continue;
                            }
                            party = contex.substring(contex.indexOf(verb) + verb.length(), contex.lastIndexOf(word));
                            flag = 1;
                            break;
                        }
                    }
                }
            }
 			/*if(contex.contains("审理")){
				for (String word : words2) {
					if (contex.indexOf(word) > 0) {
						//处理合伙、有限合伙
						if("合伙".equals(word) && contex.contains("有限合伙")){
							continue;
						}
						party = contex.substring(contex.indexOf("审理") + 2, contex.lastIndexOf(word));
						break;
					}
				}
			}else if(contex.contains("审")&&contex.indexOf("主审法官")==0){
				for (String word : words2) {
					if (contex.indexOf(word) > 0) {
						//处理合伙、有限合伙
						if("合伙".equals(word) && contex.contains("有限合伙")){
							continue;
						}
						party = contex.substring(contex.indexOf("审") + 1, contex.lastIndexOf(word));
						break;
					}
				}
			}else{*/
            if (flag == 0) {
                contex = contex.replaceAll("(上诉人|申诉人|原告：|原告人)", "原告").replaceAll("原告", "原告").replaceAll("审理审理原告", "原告");
                contex = contex.replaceAll("被审理原告", "被告");
                for (String word : words2) {
                    if (contex.indexOf(word) > 0) {
                        //处理合伙、有限合伙
                        if ("合伙".equals(word) && contex.contains("有限合伙")) {
                            continue;
                        }
                        party = contex.substring(0, contex.lastIndexOf(word));
                        break;
                    }
                }
            }

            if (party.length() > 255) {//超长的情况，过滤掉
                return null;
            }
        } catch (Exception e) {
            return null;
        }
        return party.replaceAll("原审|再审申请人|第三人|:|：", "").replaceAll("再审|\\([\u4e00-\u9fa5]+\\)", "");
    }

    //被告、原告
    private static String getDefendantAndProsecutor(String contex) {
        /**
         * 先把当事人提取出来，带原告被告前缀名称
         */
        String party = getPartyContext(contex);
        if (Util.isEmpty(party)) {
            return null;
        }
        party = party.replaceAll("抗诉", "");
        //原告前缀全部置空
        String pro = "(上诉人|申诉人|原告：|原告人|申请人|申请再审人)";//这里替换掉例如申述人，下面的被申述人就变成了被
        String pro_1 = "原告";
        //被告前缀
        String def = "(被告：|被告人|被申请人|被申请再审人)";
        String def_1 = "被告|被";
        party = party.replaceAll(pro, "*").replaceAll(pro_1, "*");
        party = party.replaceAll(def, "~").replaceAll(def_1, "~").replace("~*", "~").replaceAll("(与|、)~", "诉~");//上诉人武汉市中联房地产开发有限公司与被上诉人武汉海龙实业集团有限公司、被上诉人武汉海润房地产开发有限公司这种带两个被上述人的也可以处理
        return party;
    }

    //得到被告
    public static String getDefendant(String contex) {
        String party = getDefendantAndProsecutor(contex);
        if (Util.isEmpty(party)) {
            return null;
        }
        //先用符号分隔被告、原告
        if (party.contains("诉")) {
            if (!party.contains("*")) {
                party = "*" + party;
            }
            if (!party.contains("~")) {
                party = party.replace("诉", "诉~");
            }
        }
        if (party.contains("诉")) {
            party = party.replace("诉", "&");
        } else if (party.contains("与")) {
            party = party.replace("与", "&");
        }
        if (party.contains("&")) {//被告、原告都有
            Set<String> list = new HashSet<String>();
            for (String str : party.split("&")) {
                if (str.contains("~")) {//被告表标识
                    str = str.substring(str.indexOf("~"), str.length());
                    str = str.replaceAll("(~|等| )", "").replaceAll("和|及|与", ",");
                    for (String singleName : str.split(",|、|，")) {
                        //单独处理公司名称不规范的
                        if (singleName.contains("*")) {//含有被告
                            break;
                        }
                        //单独处理公司名称不规范的
                        if (singleName.contains("公司") && singleName.endsWith("分行")) {
                            singleName = singleName.substring(0, singleName.indexOf("分行") + 2);
                        }
                        if (singleName.contains("公司") && singleName.endsWith("支行")) {
                            singleName = singleName.substring(0, singleName.indexOf("支行") + 2);
                        }
                        if (singleName.contains("公司") && !singleName.endsWith("公司") && !singleName.endsWith("分行") && !singleName.endsWith("支行")) {
                            singleName = singleName.substring(0, singleName.indexOf("公司") + 2);
                        }
                        list.add(singleName);
                    }
                }
            }
            party = list.toString().replaceAll("\\[|\\]| ", "");
            if (Util.isEmpty(party)) {
                return null;
            }
            return party;
        } else {//被告、原告只有一个的情况
            //含有被告
            if (party.contains("~")) {
                Set<String> list = new HashSet<String>();
                party = party.substring(party.indexOf("~"), party.length());
                party = party.replaceAll("(~|等| )", "").replaceAll("和|及|与", ",");
                for (String singleName : party.split(",|、|，")) {
                    //防止混入原告数据
                    if (singleName.contains("*")) {
                        break;
                    }
                    //单独处理公司名称不规范的
                    if (singleName.contains("公司") && singleName.endsWith("分行")) {
                        singleName = singleName.substring(0, singleName.indexOf("分行") + 2);
                    }
                    if (singleName.contains("公司") && singleName.endsWith("支行")) {
                        singleName = singleName.substring(0, singleName.indexOf("支行") + 2);
                    }
                    if (singleName.contains("公司") && !singleName.endsWith("公司") && !singleName.endsWith("分行") && !singleName.endsWith("支行")) {
                        singleName = singleName.substring(0, singleName.indexOf("公司") + 2);
                    }
                    list.add(singleName);
                }
                party = list.toString().replaceAll("\\[|\\]| ", "");
                if (Util.isEmpty(party)) {
                    return null;
                }
                return party;
            } else {
                //只有原告，因为这里只返回被告，所以原告不考虑
                return null;
            }
        }
    }

    //得到原告
    public static String getProsecutor(String contex) {
        String party = getDefendantAndProsecutor(contex);

        if (Util.isEmpty(party)) {
            return null;
        }
        //先用符号分隔被告、原告
        //王七顺诉王彩涛的情况，包含诉，却没有被告原告
        if (party.contains("诉")) {
            if (!party.contains("*")) {
                party = "*" + party;
            }
            if (!party.contains("~")) {
                party = party.replace("诉", "诉~");
            }
        }
        if (party.contains("诉")) {
            party = party.replace("诉", "&");
        } else if (party.contains("与")) {
            party = party.replace("与", "&");
        }
        if (party.contains("&")) {//被告、原告都有
            for (String str : party.split("&")) {
                if (str.contains("*")) {//被告表标识
                    str = str.substring(str.indexOf("*"), str.length());
                    str = str.replaceAll("(\\*|等| )", "").replaceAll("和|及|与", ",");
                    Set<String> list = new HashSet<String>();
                    for (String singleName : str.split(",|、|，")) {
                        //单独处理公司名称不规范的
                        if (singleName.contains("~")) {//防止混入被告
                            break;
                        }
                        //单独处理公司名称不规范的
                        if (singleName.contains("公司") && singleName.endsWith("分行")) {
                            singleName = singleName.substring(0, singleName.indexOf("分行") + 2);
                        }
                        if (singleName.contains("公司") && singleName.endsWith("支行")) {
                            singleName = singleName.substring(0, singleName.indexOf("支行") + 2);
                        }
                        if (singleName.contains("公司") && !singleName.endsWith("公司") && !singleName.endsWith("分行") && !singleName.endsWith("支行")) {
                            singleName = singleName.substring(0, singleName.indexOf("公司") + 2);
                        }
                        list.add(singleName);
                    }
                    str = list.toString().replaceAll("\\[|\\]| ", "");
                    return str;
                }
            }
            return null;
        } else {//被告、原告只有一个的情况
            //含有原告
            if (party.contains("*")) {
                Set<String> list = new HashSet<String>();
                party = party.substring(party.indexOf("*"), party.length());
                party = party.replaceAll("(\\*|等| )", "").replaceAll("和|及|与", ",");
                for (String singleName : party.split(",|、|，")) {
                    if (singleName.contains("~")) {//防止混入被告
                        break;
                    }
                    //单独处理公司名称不规范的
                    if (singleName.contains("公司") && singleName.endsWith("分行")) {
                        singleName = singleName.substring(0, singleName.indexOf("分行") + 2);
                    }
                    if (singleName.contains("公司") && singleName.endsWith("支行")) {
                        singleName = singleName.substring(0, singleName.indexOf("支行") + 2);
                    }
                    if (singleName.contains("公司") && !singleName.endsWith("公司") && !singleName.endsWith("分行") && !singleName.endsWith("支行")) {
                        singleName = singleName.substring(0, singleName.indexOf("公司") + 2);
                    }
                    list.add(singleName);
                }
                party = list.toString().replaceAll("\\[|\\]| ", "");
                return party;
            } else {
                //只有原告，因为这里只返回被告，所以原告不考虑
                return null;
            }
        }

    }


    //案号
    public static String getCaseNo(String context) {
        //(\\(\\d{4}\\)|\\d{4}):(2016)或者2016  \\(正则匹配(
        return Util.parseRegex(context, "(\\(\\d{4}\\)|\\d{4}|〔\\d{4}〕|（\\d{4}）)[\u4e00-\u9fa5|\\d+|、]{1,20}号", -1);
    }

    public static void main(String[] args) {
        String str = "";
        String party = "申请执行人 ：深圳市新风向科技有限公司__;__被申请执行人 ：深圳市新感觉信息技术有限公司";
        System.out.println(Util.parseRegex(party, "(上诉人|申诉人|原告|原审原告|申请执行人|申请人|债权人)[:：]([^;； ]+)", 2));
        String defendant = "", prosecutor = "";
       //System.out.println(getOpneTime(str));
       //System.out.println(getParty(str));
       //System.out.println(getCaseNo(str));
       //System.out.println(getProsecutor(str));
        String[] defendantWords = {"被告人 ：", "被告 ：", " 被申请人 ：", "被申请执行人 ：", "被申请再审人 ："};
        String[] prosecutorWords = {"原告人 ：", "原告 ：", "申请人 ：", "申请执行人 ：", "申请再审人 ："};
        for (String string : party.replaceAll(":", "：").split("__;__")) {
            if (string.indexOf("被告") >= 0 || string.indexOf("被申请") >= 0) {
                for (String dwords : defendantWords) {
                    if (string.indexOf(dwords) >= 0) {
                        if (string.indexOf(dwords) >= 0) {
                            if (defendant != null && defendant.length() > 0) {
                                defendant = defendant + ",";
                            } else {
                                defendant = "";
                            }
                            String singleName = string.substring(string.indexOf(dwords) + dwords.length()).trim()
                                    .replace(" ", ",");
                            singleName=Util.deal_com_singlename(singleName);
                            defendant = defendant + singleName;
                            continue;
                        }
                    }
                }

            } else if (string.indexOf("原告") >= 0 || string.indexOf("申请") >= 0) {
                for (String pwords : prosecutorWords) {
                    if (string.indexOf(pwords) >= 0) {
                        if (prosecutor != null && prosecutor.length() > 0) {
                            prosecutor = prosecutor + ",";
                        } else {
                            prosecutor = "";
                        }
                        String singleName = string.substring(string.indexOf(pwords) + pwords.length()).trim()
                                .replace(" ", ",");
                        singleName=Util.deal_com_singlename(singleName);
                        prosecutor = prosecutor + singleName;
                        continue;
                    }
                }
            }
        }
        System.out.println(defendant + prosecutor);
    }


}