/*
package com.qcc.udf.liu;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.cpws.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.LinkedHashSet;
import java.util.Set;

public class keywordsZhankai extends UDF {

    public static String evaluate(String keywords)  throws Exception{
        JSONArray array = new JSONArray();

        if (StringUtils.isNotEmpty(keywords)){
            Set<String> companySet = new LinkedHashSet<>();
            Set<String> personSet = new LinkedHashSet<>();
            String[] arr = keywords.split(",");
            for (String str : arr){
                if (CommonUtil.isKeyword(str) && str.startsWith("p")){
                    personFlag = 1;
                    personSet.add(str);
                }
                if (CommonUtil.isKeyword(str) && !str.startsWith("p")){
                    companyFlag = 1;
                    companySet.add(str);
                }
            }

            if (personFlag == 1 && companyFlag == 1){
                for (String str : companySet){
                    for (String person : personSet){
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("KeyNo", str);
                        jsonObject.put("PersonId", person);

                        array.add(jsonObject);
                    }
                }
            }
        }

        return array.toString();
    }
}
*/
