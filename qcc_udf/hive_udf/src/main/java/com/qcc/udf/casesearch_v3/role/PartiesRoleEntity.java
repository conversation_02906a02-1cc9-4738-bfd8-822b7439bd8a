package com.qcc.udf.casesearch_v3.role;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2022/07/20 10:27
 * @description ：当事人身份实体
 */
@Data
public class PartiesRoleEntity {
    public static final String NULL_ARRAY_STRING = "[]";

    /**
     * 爬虫当事人
     */
    private String spiderParties;

    /**
     * 案号
     */
    private String caseNo;

    /**
     * 原告kn数组
     */
    private String pltfKeynoArray;

    /**
     * 被告kn数组
     */
    private String defdKeynoArray;

    /**
     * 第三人kn数组
     */
    private String tdptKeynoArray;

    /**
     * 所有当事人kn数组
     */
    private String keynoArray;

    public PartiesRoleEntity(String spiderParties, String caseNo, String pltfKeynoArray, String defdKeynoArray, String tdptKeynoArray, String keynoArray) {
        this.spiderParties = spiderParties;
        this.caseNo = caseNo;
        this.pltfKeynoArray = pltfKeynoArray;
        this.defdKeynoArray = defdKeynoArray;
        this.tdptKeynoArray = tdptKeynoArray;
        this.keynoArray = keynoArray;
    }

    /**
     * 获取name和roleTag的map集合
     */
    public Map<String, Integer> getPartiesRoleTagMap() {
        Map<String, Integer> partiesRoleTagMap = Collections.EMPTY_MAP;
        List<NameAndKeyNoLrInfo> keynoList = Collections.EMPTY_LIST;
        try {
            keynoList = JSON.parseArray(this.getKeynoArray(), NameAndKeyNoLrInfo.class);
        } catch (Exception e) {
        }
        if (CollectionUtils.isEmpty(keynoList)) {
            return partiesRoleTagMap;
        }

        partiesRoleTagMap = keynoList.stream().filter(e -> e != null)
                .collect(Collectors.toMap(e -> e.getName(), e -> this.findRoleTag(e.getName()), (oldValue, newVale) -> newVale))
        ;
        return partiesRoleTagMap;
    }

    /**
     * 获取roleTag
     */
    public int findRoleTag(String name) {
        Map<String, Integer> nameRoleTagMap = new HashMap<>();
        nameRoleTagMap.putAll(getKeynoRoleTagMap(this.getPltfKeynoArray(),0));
        nameRoleTagMap.putAll(getKeynoRoleTagMap(this.getDefdKeynoArray(),1));
        nameRoleTagMap.putAll(getKeynoRoleTagMap(this.getTdptKeynoArray(),2));
        Integer roleTag = nameRoleTagMap.get(name);
        if (roleTag == null){
            roleTag = 3;
        }

        return roleTag;
    }
    public Map<String, Integer> getKeynoRoleTagMap(String nameAndKeyNo,Integer roleTag) {
        Map<String, Integer> KeynoRoleTagMap = Collections.EMPTY_MAP;
        KeynoRoleTagMap = getNameKeyNoEntitys(nameAndKeyNo).stream().filter(e -> e != null).collect(Collectors.toMap(e -> e.getName(), e -> roleTag, (oldValue, newVale) -> newVale));
        return KeynoRoleTagMap;
    }


    public List<NameAndKeyNoLrInfo> getNameKeyNoEntitys(String nameAndKeyNo) {
        List<NameAndKeyNoLrInfo> nameAndKeyNoList = new ArrayList<>();
        if (StringUtils.isNotEmpty(nameAndKeyNo) && !NULL_ARRAY_STRING.equals(nameAndKeyNo)) {
            nameAndKeyNoList = JSON.parseArray(nameAndKeyNo, NameAndKeyNoLrInfo.class);
        }
        return nameAndKeyNoList;
    }
}
