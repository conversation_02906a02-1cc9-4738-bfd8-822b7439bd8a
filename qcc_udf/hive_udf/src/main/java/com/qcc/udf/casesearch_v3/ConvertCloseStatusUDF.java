package com.qcc.udf.casesearch_v3;

import org.apache.hadoop.hive.ql.exec.UDF;
import parquet.Strings;

/**
 * <AUTHOR>
 * @date 2021年09月24日 11:49
 */
public class ConvertCloseStatusUDF extends UDF {
    public static String evaluate(String caseStatus,String closeStatus){
        if(Strings.isNullOrEmpty(caseStatus)){
            return "";
        }
        if(caseStatus.contains("结案状态")){
            if(caseStatus.contains("[结案状态-1]")){
                if("1".equals(closeStatus)){
                    return caseStatus.replace("[结案状态-1]","结案");
                }else{
                    return caseStatus.replace("[结案状态-1]","立案");
                }
            }
            if(caseStatus.contains("[结案状态-2]")){
                if("1".equals(closeStatus)){
                    return caseStatus.replace(",可上诉[结案状态-2]","");
                }else{
                    return caseStatus.replace("[结案状态-2]","");
                }
            }
        }

        return caseStatus;
    }

    public static void main(String[] args) {
        System.out.println(evaluate("民事一审已判决,可上诉[结案状态-2]","0"));
        System.out.println(evaluate("民事一审[结案状态-1]","0"));
    }
}
