package com.qcc.udf.xg;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class gerReasonFromContent3 extends UDF {

    public static String evaluate(String content){
        String result = "";

        if (StringUtils.isNotEmpty(content)){
            Pattern pattern = Pattern.compile("(申请执行你)(单位)?([\\u4e00-\\u9fa5、,()（）;])+(一案)");
            Matcher matcher = pattern.matcher(content);
            if (matcher.find()){
                result = matcher.group().replace("申请执行你", "").replace("单位", "").replace("一案", "");
            }
        }

        return result;
    }

    public static void main(String[] args) {
        System.out.println(evaluate("立案执行申请人申请执行你罚金、责令退赔（诈骗罪）一案，因你未按执行通知书指定的期间履行生效法律文书确定的给付义务，本院依"));
    }
}
