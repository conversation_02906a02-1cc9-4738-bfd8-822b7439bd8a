package com.qcc.udf.casesearch_v3.test;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021年06月01日 9:19
 */
public class PunishMoneyUDFTest extends UDF {

    public static void main(String[] args) {
        String content = "作出责令限期改正，于2019年4月2日前完成整改，并处叁万元罚款的行政处罚。  \n";
        System.out.println( evaluate(content));
    }


    public static String evaluate(String content)  {
        Map result = new HashMap();
        List<String> moneyList = findMoney(content);
        Set<String> moneyResult = new HashSet<>();
        for (String money : moneyList) {
            BigDecimal dec = cleanMoney(money);
            if(dec != null){
                moneyResult.add(dec.toString());
            }
        }

        result.put("count",moneyResult.size());
        result.put("money_list",moneyResult.stream().collect(Collectors.joining(",")));

        return JSON.toJSONString(result);
    }

    //1万5 类似金额
    static Pattern NUM_WAN_NUM = Pattern.compile("^(\\d+)万(\\d+)$");
    static List<String> cnMoneyList = Lists.newArrayList("零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "一", "二", "两", "三", "四", "五", "六", "七", "八", "九", "十", "厘", "分", "角", "圆", "拾", "佰", "仟");
    static BigDecimal YI_WAN = new BigDecimal(10000);
    static BigDecimal YI_QIAN = new BigDecimal(1000);
    static BigDecimal MAX_MONEY = new BigDecimal(500000000);
    static BigDecimal MIN_MONEY = new BigDecimal(1);

    static List<PattenEntity> PATTEN_LIST = new ArrayList<>();

    static {
        //罚没合计11,597.791638万元
        PATTEN_LIST.add(PattenEntity.builder()
                .group(1)
                .pattern(Pattern.compile("罚没合计([^罚至。:;、款发票其他倍警告合计没收\\-\\n【】《》]*?)(圆|元)")).build());

        //罚款,警告,责令限期改正3000
        PATTEN_LIST.add(PattenEntity.builder()
                .group(1)
                .pattern(Pattern.compile("罚款,警告,责令限期改正(\\d+(.|)\\d+)$")).build());

        //违法行为给予200元人民币的罚款处罚
        PATTEN_LIST.add(PattenEntity.builder()
                .group(1)
                .pattern(Pattern.compile("违法行为给予([^罚。:;、款金额发票其他倍警告改正合计没收\\-\\n【】《》]*)()人民币的罚款处罚")).build());

        PATTEN_LIST.add(PattenEntity.builder()
                .group(2)
                .pattern(Pattern.compile("(处以|罚款地|罚|处)([^罚。:;、款金额发票其他倍警告改正合计没收之造价\\-\\n【】《》]*)(元。|元)$")).build());

        PATTEN_LIST.add(PattenEntity.builder()
                .group(2)
                .pattern(Pattern.compile("罚款,(即罚款金额|计币|即|既)([^罚。:;、款金额发票其他倍警告合计没收之\\-\\n【】《》]*?)(圆|元|)(整|)")).build());

        PATTEN_LIST.add(PattenEntity.builder()
                .group(4)
                .pattern(Pattern.compile("(共计|合计|计)(:|)(人民币|￥|)([^罚。:;、款金额发票其他倍警告改正合计没收之造价\\-\\n【】《》]*)(元|圆)整")).build());

        PATTEN_LIST.add(PattenEntity.builder()
                .group(4)
                .pattern(Pattern.compile("(共计|合计|计)(元|圆)")).build());

        //处罚款（人民币）432825.68元；
        PATTEN_LIST.add(PattenEntity.builder()
                .group(1)
                .pattern(Pattern.compile("处罚款\\(人民币\\)([^罚至。:;、款税金额发票其他警告合计没收\\-\\n【】《》]*?)元;")).build());


        PATTEN_LIST.add(PattenEntity.builder()
                .group(2)
                .pattern(Pattern.compile("罚款计(人民币|￥|)([^罚至。:;、款税金额发票其他警告合计没收\\-\\n【】《》]*?)(元|圆)")).build());
        PATTEN_LIST.add(PattenEntity.builder()
                .group(3)
                .pattern(Pattern.compile("(处以金额|拟罚|处以|处于|处予|予以|处|以)(人民币|￥|)([^罚至。:;、款每平方米税以下金额发票其他倍警告合计没收之造价\\-\\n【】《》]*?)(元|圆)(整|)(金额|)(的|)(罚款|处罚)")).build());

        PATTEN_LIST.add(PattenEntity.builder()
                .group(3)
                .pattern(Pattern.compile("(处以金额|拟罚|处以|处于|处予|予以|处|以)(人民币|￥|)([^罚至。:;、款每平方米税以下金额发票其他倍警告合计没收之造价\\-\\n【】《》]*?)(元(整|)人民币(的|)|)(罚款|处罚)")).build());

        PATTEN_LIST.add(PattenEntity.builder()
                .group(1)
                .pattern(Pattern.compile("fakuan(\\d+(.|)\\d+)yuan")).build());
        PATTEN_LIST.add(PattenEntity.builder()
                .group(1)
                .pattern(Pattern.compile("罚款(\\d+(.|)\\d+)$")).build());
        PATTEN_LIST.add(PattenEntity.builder()
                .group(1)
                .pattern(Pattern.compile("缴纳([^罚至。:;、款发票其他倍警告合计没收\\-\\n【】《》]*?)元罚款")).build());
        PATTEN_LIST.add(PattenEntity.builder()
                .group(1)
                .pattern(Pattern.compile("人民币([^,。:\\-\\n]*?)(元|圆)(整|)(\\((([^罚至。:;、款金额发票其他倍警告合计没收\\-\\n【】《》]*?)元(整|))\\)|)罚款")).build());
//
        PATTEN_LIST.add(PattenEntity.builder()
                .group(6)
                .pattern(Pattern.compile("(处予罚款,金额|罚款数额|处罚金额|罚款金额|给予处罚|拟处罚金|处罚款|罚款|处罚金|处罚|计罚)(计|为|)(:|)(人民币|￥|)(\\(|)([^罚至。:;、款税金额发票其他倍警告合计没收建设工程造价\\-\\n【】《》]*?)元")).build());

        PATTEN_LIST.add(PattenEntity.builder()
                .group(3)
                .pattern(Pattern.compile("^(处予罚款,金额|处罚金额|罚款|罚金|处罚|罚)(:|)([^罚至。:;、款税金额发票其他倍警告合计没收建设工程造价\\-\\n【】《》]*)(圆|元|)$")).build());

        PATTEN_LIST.add(PattenEntity.builder()
                .group(1)
                .pattern(Pattern.compile("^([.\\d]*(万|))(圆|元)")).build());
        PATTEN_LIST.add(PattenEntity.builder()
                .group(1)
                .pattern(Pattern.compile("罚款人民币([^罚至。:;、款税金额发票其他倍警告合计没收\\-\\n【】《》]*)整")).build());

        PATTEN_LIST.add(PattenEntity.builder()
                .group(2)
                .pattern(Pattern.compile("罚款(￥|人民币)([^罚至。:;、款税金额发票其他倍警告合计没收\\-\\n【】《》]*)\\(.*大写.*\\)")).build());

        PATTEN_LIST.add(PattenEntity.builder()
                .group(1)
                .pattern(Pattern.compile("罚款:([^罚。:;、款税金额发票其他倍警告合计没收\\-\\n【】《》]*)(;|,|。)")).build());

        PATTEN_LIST.add(PattenEntity.builder()
                .group(2)
                .pattern(Pattern.compile("(、|,|;|。)([^罚。:;、款税金额发票其他倍警告合计没收\\-\\n【】《》]*)(圆|元)处罚")).build());

        PATTEN_LIST.add(PattenEntity.builder()
                .group(1)
                .pattern(Pattern.compile("^([.\\d]*万)$")).build());

        PATTEN_LIST.add(PattenEntity.builder()
                .group(1)
                .pattern(Pattern.compile("行政处罚:罚款([^罚。:;、款金额发票其他倍警告合计没收\\-\\n【】《》]*)(整|)")).build());
        PATTEN_LIST.add(PattenEntity.builder()
                .group(1)
                .pattern(Pattern.compile("处罚([^罚。:;、款金额发票其他倍警告合计没收\\-\\n【】《》]*)$")).build());

        PATTEN_LIST.add(PattenEntity.builder()
                .group(2)
                .pattern(Pattern.compile("(给予|作出)([^罚。:;、款金额发票其他倍警告合计没收\\-\\n【】《》]*)(圆|元)(处罚决定|罚款)")).build());
        PATTEN_LIST.add(PattenEntity.builder()
                .group(3)
                .pattern(Pattern.compile("(罚款金额|罚款|罚金)(:|)([^罚。:;、款金额发票其他倍警告改正合计没收合计没收建设工程造价\\-\\n【】《》]*)(,|;|。|)")).build());
        PATTEN_LIST.add(PattenEntity.builder()
                .group(1)
                .pattern(Pattern.compile("^([^罚。:;、款金额发票其他倍警告合计没收\\-\\n【】《》]*)(圆|元)整$")).build());
        PATTEN_LIST.add(PattenEntity.builder()
                .group(2)
                .pattern(Pattern.compile("(既|即)([^罚。:;、款金额发票其他倍警告合计没收\\-\\n【】《》]*)(圆|元)的罚款")).build());
        //人民币叁万元整的行政处罚
        PATTEN_LIST.add(PattenEntity.builder()
                .group(1)
                .pattern(Pattern.compile("人民币([^罚。:;、款金额发票其他倍警告合计没收\\-\\n【】《》]*)(圆|元)(整|)的行政处罚")).build());

    }

    public static BigDecimal cleanMoney(String money) {
        try {
//            System.out.println(money);
            BigDecimal fmtMoney = convertMoney(money);

            //超过1亿的认为是错数据 TODO 这些数据需要走人工审核
            if (fmtMoney != null && fmtMoney.compareTo(MAX_MONEY) > 0) {
                fmtMoney = null;
            }
            //小于1的处罚金额也认为是无效数据
            if (fmtMoney != null && fmtMoney.compareTo(MIN_MONEY) < 0) {
                fmtMoney = null;
            }

            //.00结尾金额
            if (fmtMoney != null && fmtMoney.toString().endsWith(".00")) {
                return new BigDecimal(fmtMoney.stripTrailingZeros().toPlainString());
            }
            return fmtMoney;
        } catch (Exception e) {
			e.printStackTrace();
        }
        return null;
    }

    /**
     * 从处罚结果 或 出发原因中提取罚款金额
     *
     * @param punishResult
     * @return
     */
    public static  List<String> findMoney( String punishResult) {
        punishResult = CommonV3Util.full2Half(punishResult);

        punishResult = punishResult.replaceAll(" | |(\\((元|大写)\\))", "");


        List<String> moneyList = new ArrayList<>();

        for (PattenEntity pattenEntity : PATTEN_LIST) {
            Matcher matcher = pattenEntity.getPattern().matcher(punishResult);
            while (matcher.find()) {
                String money = matcher.group(pattenEntity.getGroup());
                if (!Strings.isNullOrEmpty(money) && !Strings.isNullOrEmpty(money.replaceAll("\\.", ""))) {
                    moneyList.add(money);
//                    break;
                }

            }
        }

        if (CollectionUtils.isEmpty(moneyList)) {
            punishResult = punishResult.replace(",", "");
            Pattern pattern = Pattern.compile("^(\\d+(.|)\\d+)(元。|元|)(整|)$");
            Matcher matcher = pattern.matcher(punishResult);
            if (matcher.find()) {
                String money = matcher.group(1);
                moneyList.add(money);
            }
        }

        List<String> result = new ArrayList<>();

        for (String money : moneyList) {
            money = money.replace(",", "");
            if (money.endsWith("元")) {
                money = money.substring(0, money.lastIndexOf("元"));
            }
            if (money.endsWith("元整")) {
                money = money.substring(0, money.lastIndexOf("元整"));
            }
            money = money.replace("\"", "");

            String[] moneyArr = money.split("以上");
            if (moneyArr.length > 0) {
                money = moneyArr[0];
            }

            money = money.replaceAll("人民币|整", "");

            //处理70000.00.类似数据
            while (money.endsWith(".")) {
                money = money.substring(0, money.length() - 1);
            }
            //处理1.9929.00
            String[] moneyArray = money.split("\\.");
            //包含多个[.]只保留最后一个点
            if (moneyArray.length == 3 && !money.contains("万")) {
                //小于10w的可以纠正
                if (Long.valueOf(moneyArray[0] + moneyArray[1]) < 100000) {
                    money = moneyArray[0] + moneyArray[1] + "." + moneyArray[2];
                }
            }

            //处理1万5 这种
            Matcher matcher = NUM_WAN_NUM.matcher(money);
            if (matcher.find()) {
                money = String.valueOf(Integer.valueOf(matcher.group(1)) * 10000 + Integer.valueOf(matcher.group(2)) * 1000);
            }

            //处理类似9500(玖仟伍佰)
            money = money.replaceAll("\\(.*\\)", "");
            result.add(money);
        }



        return result;
    }


    public static BigDecimal convertMoney(String money) {
        boolean isCnMoney = cnMoneyList.stream().anyMatch(item -> money.contains(item));

        //中文金额
        if (isCnMoney) {
            return ChineseAmountUtil.chinese2Number(money);
        } else {

            String moneyNew = money.replaceAll("万|千", "");
            if (!isNumber(moneyNew)) {
                return null;
            }


            if (money.endsWith("万")) {
                return new BigDecimal(moneyNew).multiply(YI_WAN).setScale(2, BigDecimal.ROUND_DOWN);
            } else if (money.endsWith("千")) {
                return new BigDecimal(moneyNew).multiply(YI_QIAN).setScale(2, BigDecimal.ROUND_DOWN);
            } else {
                return new BigDecimal(money).setScale(2, BigDecimal.ROUND_DOWN);
            }
        }
    }

    public static boolean isNumber(String number) {
        if (Strings.isNullOrEmpty(number)) {
            return false;
        }

        Pattern pattern = Pattern.compile("^[-\\+]?[.\\d]*$");
        Matcher matcher = pattern.matcher(number);
        return matcher.matches();
    }
}


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
class PattenEntity {
    //正则匹配group
    private int group;
    //正则表达式
    Pattern pattern;
}

