package com.qcc.udf.overseas;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.List;

/**
 * 业务UDF（海外企业）将日本纪年改为时间戳
 * ---------------------------------------------------------------------------------------------------------
 * create temporary function convertJapanEntCode as 'com.qcc.udf.overseas.ConvertJapanIdentifyNoToEntCode' using jar 'hdfs:///data/hive/udf/qcc_udf.jar';
 * ---------------------------------------------------------------------------------------------------------
 * select convertJapanEntCode ('180001033042');
 * '6180001033042'
 */
public class ConvertJapanIdentifyNoToEntCode extends UDF {

    public String evaluate(String identifyNo) {
        String entCode = "";
        try {
            if (StringUtils.isNotBlank(identifyNo) && identifyNo.length() == 12) {
                int oddSum = 0;     // 奇数位总和
                int evenSum = 0;    // 偶数位总和
                List<String> identifyNoList = Arrays.asList(identifyNo.split(""));
                for (int i = 1; i <= identifyNoList.size(); i++) {
                    if (i % 2 == 0) {
                        evenSum += Integer.parseInt(identifyNoList.get(i - 1));
                    } else {
                        oddSum += Integer.parseInt(identifyNoList.get(i - 1));
                    }
                }
                int checkCode = (9 - ((oddSum * 2 + evenSum ) % 9));
                entCode = (checkCode + identifyNo);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return entCode;
    }

    public static void main(String[] args) {
        String identifyNo = "200001002507";
        System.out.println(new ConvertJapanIdentifyNoToEntCode().evaluate(identifyNo));
    }
}
