package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Set;
import java.util.TreeSet;


/**
 * 字符串根据逗号TreeSet排序
 **/
public class Sort_by_treesetUDF extends UDF {
    public String evaluate(String names) {
        Set<String> nameSet = new TreeSet<String>();
        if (names != null && names.trim().length() != 0) {

            for (String name : names.split(",")) {
                if (!"".equals(name.trim())) {
                    nameSet.add(name.trim());
                }
            }
        }
        names = "";
        for (String name : nameSet) {
            names = names + name + ",";
        }
        if (names.endsWith(",")) {
            names = names.substring(0, names.length() - 1);
        }
        if (names.trim().length() == 0) {
            names = null;
        }
        return names;

    }

 /*   public static void main(String[] args) {
        String str = "a,e,b,f,c";
        System.out.println(new Sort_by_treesetUDF().evaluate(str));
    }*/

}
