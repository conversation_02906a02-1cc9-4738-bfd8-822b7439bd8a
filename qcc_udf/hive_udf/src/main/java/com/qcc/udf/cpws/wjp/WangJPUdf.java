package com.qcc.udf.cpws.wjp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class WangJPUdf extends UDF {

    public static String evaluate(String sortStr) {
        try {
            String regex = ".*[一二三四五六七八九零0-9]+$";
            Pattern pattern = Pattern.compile(regex);
            if (StringUtils.isNotEmpty(sortStr)) {
                List<JSONObject> objectList = JSON.parseArray(sortStr, JSONObject.class);
                for (JSONObject object : objectList) {
                    String role = object.getString("Role");
                    role = role.replace("(", "（").replace(")", "）");
                    Matcher matcher = pattern.matcher(role);
                    if (matcher.find()) {
                        role = role.substring(0, role.length() - 1);
                    }
                    if (role.contains("（")) {
                        // 定义匹配括号前数字的正则表达式
                        // 进行替换操作
                        StringBuilder stringBuilder = new StringBuilder();
                        String[] sp = role.split("（");
                        for (String s : sp) {
                            Matcher matcher2 = pattern.matcher(s);
                            if (matcher2.find()) {
                                s = s.substring(0, s.length() - 1);
                            }
                            stringBuilder.append(s).append("（");
                        }
                        role = stringBuilder.substring(0, stringBuilder.length() - 1);
                    }
                    if (role.contains("）")) {
                        // 定义匹配括号前数字的正则表达式
                        // 进行替换操作
                        StringBuilder stringBuilder = new StringBuilder();
                        String[] sp = role.split("）");
                        int i = 0;
                        for (String s : sp) {
                            Matcher matcher2 = pattern.matcher(s);
                            if (matcher2.find()) {
                                s = s.substring(0, s.length() - 1);
                            }
                            i++;
                            if (i % 2 == 1) {
                                stringBuilder.append(s).append("）");
                            } else {
                                stringBuilder.append(s);
                            }
                        }
                        role = stringBuilder.toString();
                    }

                    object.put("Role", role);
                }
                return JSON.toJSONString(objectList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "ERROR:" + sortStr;
        }

        return "";
    }

    public static void main(String[] args) {

        System.out.println(evaluate("[{\"Role\":\"上诉人（原审原告一）\",\"LawyerTag\":0,\"DetailList\":[{\"KeyNo\":\"\",\"Org\":-2,\"JR\":\"15\",\"JudgeResult\":\"\",\"ShowName\":\"袁**\",\"Name\":\"袁李润\",\"LawyerList\":[]}],\"RoleTag\":0},{\"Role\":\"上诉人（原审原告二）\",\"LawyerTag\":0,\"DetailList\":[{\"KeyNo\":\"\",\"Org\":-2,\"JR\":\"15\",\"JudgeResult\":\"\",\"ShowName\":\"袁**\",\"Name\":\"袁学良\",\"LawyerList\":[]}],\"RoleTag\":0},{\"Role\":\"代理律师\",\"LawyerTag\":1,\"DetailList\":[{\"KeyNo\":\"wec634e163b8204264b2696ac3708030\",\"Org\":4,\"JR\":\"15\",\"JudgeResult\":\"\",\"ShowName\":\"广东九韬律师事务所\",\"Name\":\"广东九韬律师事务所\",\"LawyerList\":[{\"KeyNo\":\"d6814752b61d1afdb6ef2eee12ff909b\",\"Org\":-2,\"Name\":\"杨红山\"}]}],\"RoleTag\":0},{\"Role\":\"被上诉人一（原审被告一）\",\"LawyerTag\":0,\"DetailList\":[{\"KeyNo\":\"\",\"Org\":-2,\"JR\":\"22\",\"JudgeResult\":\"\",\"ShowName\":\"骆*\",\"Name\":\"骆基\",\"LawyerList\":[]}],\"RoleTag\":1},{\"Role\":\"被上诉人二（原审被告二）\",\"LawyerTag\":0,\"DetailList\":[{\"KeyNo\":\"78cbb59235b1ee5959fcf432d70e588a\",\"Org\":0,\"JR\":\"22\",\"JudgeResult\":\"\",\"ShowName\":\"惠州市汽车运输集团有限公司博罗分公司\",\"Name\":\"惠州市汽车运输集团有限公司博罗分公司\",\"LawyerList\":[]}],\"RoleTag\":1},{\"Role\":\"被上诉人\",\"LawyerTag\":0,\"DetailList\":[{\"KeyNo\":\"\",\"Org\":-2,\"JR\":\"22\",\"JudgeResult\":\"\",\"ShowName\":\"三\",\"Name\":\"三\",\"LawyerList\":[]}],\"RoleTag\":1},{\"Role\":\"代理律师\",\"LawyerTag\":1,\"DetailList\":[{\"KeyNo\":\"w902fd18f2ea2cc41ce72e3dd9969df6\",\"Org\":4,\"JR\":\"22\",\"JudgeResult\":\"\",\"ShowName\":\"广东宝晟律师事务所\",\"Name\":\"广东宝晟律师事务所\",\"LawyerList\":[{\"KeyNo\":\"2f62dafa648d9612c7d803e9a0f210ed\",\"Org\":-2,\"Name\":\"陈镜明\"}]},{\"KeyNo\":\"wb4d7379ac34d7d74304a370a44ecaac\",\"Org\":4,\"JR\":\"22\",\"JudgeResult\":\"\",\"ShowName\":\"广东瑞轩律师事务所\",\"Name\":\"广东瑞轩律师事务所\",\"LawyerList\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"杜嘉敏\"}]}],\"RoleTag\":1}]"));
    }
}
