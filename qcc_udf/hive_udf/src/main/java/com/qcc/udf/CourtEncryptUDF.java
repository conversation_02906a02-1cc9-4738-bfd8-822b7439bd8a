package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;

/**
 * @Auther: zhangqiang
 * @Date: 2020/4/8 13:48
 * @Description:法院公告id加密
 */
public class CourtEncryptUDF extends UDF {
    private final static String algorithmDES = "DES";

    public String evaluate(String input) {
        String key = "1qwe2plm";
        String ivKey = "edc1rfv2";
        try {
            return byte2String(encryptDes(input.getBytes(), key.getBytes(), ivKey.getBytes()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static byte[] encryptDes(byte[] src, byte[] key, byte[] iv) throws Exception {
        // DES算法要求有一个可信任的随机数源
        // SecureRandom sr = new SecureRandom();
        IvParameterSpec ivKey = new IvParameterSpec(iv);

        // 从原始密匙数据创建DESKeySpec对象
        DESKeySpec dks = new DESKeySpec(key);
        // 创建一个密匙工厂，然后用它把DESKeySpec转换成一个SecretKey对象
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(algorithmDES);
        SecretKey securekey = keyFactory.generateSecret(dks);
        // Cipher对象实际完成加密操作，使用SecureRandom 则修改为DES
        Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");

        // 用密匙初始化Cipher对象
        cipher.init(Cipher.ENCRYPT_MODE, securekey, ivKey);
        // 正式执行加密操作
        return cipher.doFinal(src);
    }

    public static byte[] encryptByIv(byte[] src, byte[] key, byte[] iv) throws Exception {
        // DES算法要求有一个可信任的随机数源，可以使用SecureRandom sr = new SecureRandom()替代
        IvParameterSpec ivKey = new IvParameterSpec(iv);

        // 从原始密匙数据创建DESKeySpec对象
        DESKeySpec dks = new DESKeySpec(key);
        // 创建一个密匙工厂，然后用它把DESKeySpec转换成一个SecretKey对象
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(algorithmDES);
        SecretKey securekey = keyFactory.generateSecret(dks);
        // Cipher对象实际完成加密操作
        Cipher cipher = Cipher.getInstance(algorithmDES);
        // 用密匙初始化Cipher对象
        cipher.init(Cipher.ENCRYPT_MODE, securekey, ivKey);

        // 正式执行加密操作
        return cipher.doFinal(src);
    }

    /**
     * @param value 密码
     * @param key   加密字符串
     * @return
     */
    public final static String encrypt(String value, String key, String ivKey) {
        try {
            return byte2String(encryptDes(value.getBytes(), key.getBytes(), ivKey.getBytes()));
        } catch (Exception e) {
        }
        return null;
    }

    public static String byte2String(byte[] b) {
        String hs = "";
        String stmp = "";
        for (int n = 0; n < b.length; n++) {
            stmp = (java.lang.Integer.toHexString(b[n] & 0XFF));
            if (stmp.length() == 1) {
                hs = hs + "0" + stmp;
            } else {
                hs = hs + stmp;
            }
        }
        return hs.toUpperCase();
    }


    public static void main(String[] args) {
        CourtEncryptUDF courtEncryptUDF = new CourtEncryptUDF();
        System.out.println(courtEncryptUDF.evaluate("6132184"));
    }
}
