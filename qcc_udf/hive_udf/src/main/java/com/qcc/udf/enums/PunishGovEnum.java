package com.qcc.udf.enums;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 处罚机构
 */
public enum PunishGovEnum {
    FINANCE_GOV_1("人民银行", PunishGovCodeEnum.FINANCE_GOV),
    FINANCE_GOV_2("央行派出机构", PunishGovCodeEnum.FINANCE_GOV),
    FINANCE_GOV_3("证监会", PunishGovCodeEnum.FINANCE_GOV),
    FINANCE_GOV_4("证监会派出机构", PunishGovCodeEnum.FINANCE_GOV),
    FINANCE_GOV_5("银保监会", PunishGovCodeEnum.FINANCE_GOV),
    FINANCE_GOV_6("银保监会派出机构", PunishGovCodeEnum.FINANCE_GOV),
    FINANCE_GOV_7("香港证监会", PunishGovCodeEnum.FINANCE_GOV),
    FINANCE_GOV_8("国家外汇管理局", PunishGovCodeEnum.FINANCE_GOV),
    FINANCE_GOV_9("人行", PunishGovCodeEnum.FINANCE_GOV),

    EXCHANGE_GOV_1("上交所", PunishGovCodeEnum.EXCHANGE_GOV),
    EXCHANGE_GOV_2("深交所", PunishGovCodeEnum.EXCHANGE_GOV),
    EXCHANGE_GOV_3("北交所", PunishGovCodeEnum.EXCHANGE_GOV),
    EXCHANGE_GOV_4("股转系统", PunishGovCodeEnum.EXCHANGE_GOV),
    EXCHANGE_GOV_5("上期所", PunishGovCodeEnum.EXCHANGE_GOV),
    EXCHANGE_GOV_6("郑商所", PunishGovCodeEnum.EXCHANGE_GOV),
    EXCHANGE_GOV_7("中金所", PunishGovCodeEnum.EXCHANGE_GOV),
    EXCHANGE_GOV_8("联交所", PunishGovCodeEnum.EXCHANGE_GOV),
    EXCHANGE_GOV_9("股权交易中心", PunishGovCodeEnum.EXCHANGE_GOV),

    ASSOCIATION_GOV_1("证券业协会", PunishGovCodeEnum.ASSOCIATION_GOV),
    ASSOCIATION_GOV_2("基金业协会", PunishGovCodeEnum.ASSOCIATION_GOV),
    ASSOCIATION_GOV_3("期货业协会", PunishGovCodeEnum.ASSOCIATION_GOV),
    ASSOCIATION_GOV_4("交易商协会", PunishGovCodeEnum.ASSOCIATION_GOV),
    ASSOCIATION_GOV_5("资产评估协会", PunishGovCodeEnum.ASSOCIATION_GOV),
    ASSOCIATION_GOV_6("会计师协会", PunishGovCodeEnum.ASSOCIATION_GOV),
    ASSOCIATION_GOV_7("律师协会", PunishGovCodeEnum.ASSOCIATION_GOV),
    ASSOCIATION_GOV_8("协会", PunishGovCodeEnum.ASSOCIATION_GOV),
    ASSOCIATION_GOV_9("联合会", PunishGovCodeEnum.ASSOCIATION_GOV),
    ASSOCIATION_GOV_10("残联", PunishGovCodeEnum.ASSOCIATION_GOV),

    GOVERMMENT_GOV_1("工商", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_2("生态环境", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_3("监督", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_4("药监", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_5("财政", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_6("应急管理", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_7("住建部", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_8("税务局", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_9("海关", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_10("委", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_11("部", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_12("署", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_13("办", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_14("局", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_15("室", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_16("队", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_17("统计", PunishGovCodeEnum.GOVERMMENT_GOV),
//    GOVERMMENT_GOV_18("医院", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_19("政府", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_20("能源", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_21("水利", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_22("交通", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_23("农业", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_24("人社", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_25("卫健", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_26("邮政", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_27("林草", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_28("民防", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_29("海事", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_30("版权", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_31("民航", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_32("医保", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_33("储备", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_34("烟草", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_35("公安", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_36("城管", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_37("中心", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_38("数育", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_39("广电", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_40("司法", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_41("教育", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_42("网信办", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_43("消防", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_45("交警", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_46("所", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_47("站", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_48("科", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_49("厅", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_50("托管机构", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_51("股", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_52("处", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_53("人力资源", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_54("社会保障", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_55("公积金", PunishGovCodeEnum.GOVERMMENT_GOV),
    GOVERMMENT_GOV_56("组", PunishGovCodeEnum.GOVERMMENT_GOV),



    COURT_GOV_1("高院", PunishGovCodeEnum.COURT_GOV),
    COURT_GOV_2("中院", PunishGovCodeEnum.COURT_GOV),
    COURT_GOV_3("法院", PunishGovCodeEnum.COURT_GOV),
    DEFAULT("其他", PunishGovCodeEnum.DEFAULT),
    ;


    private String govName;
    private PunishGovCodeEnum govCodeEnum;

    PunishGovEnum(String govName, PunishGovCodeEnum govCodeEnum) {
        this.govName = govName;
        this.govCodeEnum = govCodeEnum;
    }

    public String getGovName() {
        return govName;
    }

    public PunishGovCodeEnum getGovCodeEnum() {
        return govCodeEnum;
    }

    private static final Map<String, PunishGovEnum> lookup = new LinkedHashMap<>();

    static {
        EnumSet.allOf(PunishGovEnum.class).stream().forEach(e -> {
                    lookup.put(e.govName, e);
                }
        );
    }

    public static PunishGovEnum find(String keyword) {
        if (StringUtils.isBlank(keyword)){
            return PunishGovEnum.DEFAULT;
        }
        for (Map.Entry<String, PunishGovEnum> entry : lookup.entrySet()){
            if (keyword.contains(entry.getKey())){
                return entry.getValue();
            }
        }
        return PunishGovEnum.DEFAULT;
    }

    public static void main(String[] args) {
        String office = "知识产权保护协调室";
        String punishCode = "";
        if (StringUtils.isNotBlank(office)){
            Set<String> codeSet = Arrays.asList(office.split(",")).stream().filter(str -> StringUtils.isNotBlank(str)).map(str -> {
                PunishGovEnum punishGovEnum = PunishGovEnum.find(str);
                return punishGovEnum.getGovCodeEnum().getCode();
            }).filter(str -> StringUtils.isNotBlank(str)).collect(Collectors.toSet());

            if (CollectionUtils.isNotEmpty(codeSet)){
                punishCode = StringUtils.join(codeSet,",");
            }
        }
        System.out.println(punishCode);
    }

}
