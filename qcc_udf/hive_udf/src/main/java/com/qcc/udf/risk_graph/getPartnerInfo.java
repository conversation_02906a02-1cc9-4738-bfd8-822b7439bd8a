package com.qcc.udf.risk_graph;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.Set;

/**
 * @Auther: liulh
 * @Date: 2020/6/11 17:54
 * @Description:
 */
public class getPartnerInfo extends UDF {
    public static String evaluate(String keyNo,String name, String ipoPartner, String partner) {
        JSONArray result = new JSONArray();
        if (StringUtils.isNotEmpty(ipoPartner)){
            JSONArray array = JSONArray.parseArray(ipoPartner);
            Iterator<Object> it = array.iterator();
            while (it.hasNext()){
                JSONObject jsonObject = (JSONObject)it.next();
                String subKeyNo = jsonObject.getString("KeyNo") == null ? "" : jsonObject.getString("KeyNo");

                JSONObject json = new JSONObject();
                json.put("EndKeyNo", keyNo);
                json.put("EndName", name == null ? "" : name);
                json.put("StartKeyNo", subKeyNo);
                json.put("StartName", jsonObject.getString("StockName") == null ? "" : jsonObject.getString("StockName"));
                json.put("StockPercent", jsonObject.getString("StockPercent") == null ? "" : jsonObject.getString("StockPercent"));
                json.put("InDate", jsonObject.getJSONObject("PublicDate") == null ? 0 : jsonObject.getJSONObject("PublicDate").getLongValue("$numberLong"));

                result.add(json);
            }
        }else if (StringUtils.isNotEmpty(partner)){
            JSONArray array = JSONArray.parseArray(partner);
            Iterator<Object> it = array.iterator();
            while (it.hasNext()){
                JSONObject jsonObject = (JSONObject)it.next();
                String subKeyNo = jsonObject.getString("KeyNo") == null ? "" : jsonObject.getString("KeyNo");

                JSONObject json = new JSONObject();
                json.put("EndKeyNo", keyNo);
                json.put("EndName", name == null ? "" : name);
                json.put("StartKeyNo", subKeyNo);
                json.put("StartName", jsonObject.getString("StockName") == null ? "" : jsonObject.getString("StockName"));
                json.put("StockPercent", jsonObject.getString("StockPercent") == null ? "" : jsonObject.getString("StockPercent"));
                json.put("InDate", jsonObject.getJSONObject("InDate") == null ? 0 : jsonObject.getJSONObject("InDate").getLongValue("$numberLong"));

                result.add(json);
            }
        }

        return result.toString();
    }

    public static void main(String[] args) {
        System.out.println(evaluate("keyno1","keyno2","", "[{\"Org\":0,\"StockName\":\"深圳市巨邦饮食服务有限公司\",\"HasImage\":false,\"StockPercent\":\"70.00%\",\"CompanyCount\":44,\"TotalShouldAmount\":\"140\",\"ShouldCapi\":\"140\",\"InvestType\":null,\"KeyNo\":\"3454df2ea003c501f9e63dffc37a4343\",\"ShoudDate\":null,\"IdentifyType\":\"非公示项\",\"InDate\":{\"$numberLong\":\"1554652800\"},\"CapiDate\":null,\"StockType\":\"企业法人\",\"IdentifyNo\":null,\"RealCapi\":null,\"PartnerShouldDetailList\":[{\"ShoudDate\":null,\"ShouldCapi\":\"140\",\"InvestType\":null}],\"InvestName\":null},{\"Org\":0,\"StockName\":\"深圳市厨创投资股份有限公司\",\"HasImage\":false,\"StockPercent\":\"20.00%\",\"CompanyCount\":2,\"TotalShouldAmount\":\"40\",\"ShouldCapi\":\"40\",\"InvestType\":null,\"KeyNo\":\"11fd8b453f942da119fab978a2fc57e3\",\"ShoudDate\":\"2019-04-08\",\"IdentifyType\":\"非公示项\",\"InDate\":{\"$numberLong\":\"1554652800\"},\"CapiDate\":null,\"StockType\":\"企业法人\",\"IdentifyNo\":null,\"RealCapi\":null,\"PartnerShouldDetailList\":[{\"ShoudDate\":\"2019-04-08\",\"ShouldCapi\":\"40\",\"InvestType\":null}],\"InvestName\":null},{\"Org\":2,\"StockName\":\"刘文\",\"HasImage\":false,\"StockPercent\":\"10.00%\",\"CompanyCount\":6,\"TotalShouldAmount\":\"20\",\"ShouldCapi\":\"20\",\"InvestType\":null,\"KeyNo\":\"p0261a719b0cbd325c6edcb294dd03fe\",\"ShoudDate\":null,\"IdentifyType\":\"非公示项\",\"InDate\":{\"$numberLong\":\"1554652800\"},\"CapiDate\":null,\"StockType\":\"自然人股东\",\"IdentifyNo\":null,\"RealCapi\":null,\"PartnerShouldDetailList\":[{\"ShoudDate\":null,\"ShouldCapi\":\"20\",\"InvestType\":null}],\"Job\":\"监事\",\"InvestName\":null}] "));
    }

}
