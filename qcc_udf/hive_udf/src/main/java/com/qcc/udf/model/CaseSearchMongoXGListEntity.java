package com.qcc.udf.model;

import lombok.Data;

import java.util.LinkedList;
import java.util.List;

@Data
public class CaseSearchMongoXGListEntity {
    private String id = "";
    private List<CaseSearchMongoNameAndKeyNoEntity> nameAndKeyNo = new LinkedList<>();
    private List<CaseSearchMongoNameAndKeyNoEntity> companyInfo = new LinkedList<>();
    private Long publishDate = 0L;
    private Integer isValid = 0;
    private List<CaseSearchMongoNameAndKeyNoEntity> xglNameAndKeyNo = new LinkedList<>();
    private List<CaseSearchMongoNameAndKeyNoEntity> glNameAndKeyNo = new LinkedList<>();
}
