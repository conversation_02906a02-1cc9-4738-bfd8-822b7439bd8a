package com.qcc.udf;

/**
 * @Auther: nixb
 * @Date: 2020/5/13 19:07
 * @Description:
 */
import org.apache.hadoop.hive.ql.exec.UDF;
import java.util.*;
import java.util.stream.Collectors;

public class distinctstring extends UDF{
    public String evaluate (String str,String separator){
        if(str==null || str=="")
        {
            return "";
        }

        String result=str;
        try
        {
            if(str.contains(",")){
                List<String> list= Arrays.asList(str .split(",")).stream().map(s -> s.trim()).distinct().collect(Collectors.toList());
                result=String.join(",",list);
            }
        }
        catch (Exception e) {
        }
        return result;
    }
}
