package com.qcc.udf.lawer;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2021/03/03 13:34
 * @description ：获取省份名称
 */
public class GetProvinceNameByCode extends UDF {

    public static final Map<String, String> codeMapName = new HashMap<>();

    static {
        codeMapName.put("HB", "河北");
        codeMapName.put("SX", "山西");
        codeMapName.put("LN", "辽宁");
        codeMapName.put("JL", "吉林");
        codeMapName.put("HLJ", "黑龙江");
        codeMapName.put("JS", "江苏");
        codeMapName.put("ZJ", "浙江");
        codeMapName.put("AH", "安徽");
        codeMapName.put("FJ", "福建");
        codeMapName.put("JX", "江西");
        codeMapName.put("SD", "山东");
        codeMapName.put("TW", "台湾");
        codeMapName.put("HEN", "河南");
        codeMapName.put("HUB", "湖北");
        codeMapName.put("HUN", "湖南");
        codeMapName.put("GD", "广东");
        codeMapName.put("HAIN", "海南");
        codeMapName.put("SC", "四川");
        codeMapName.put("GZ", "贵州");
        codeMapName.put("YN", "云南");
        codeMapName.put("SAX", "陕西");
        codeMapName.put("GS", "甘肃");
        codeMapName.put("QH", "青海");
        codeMapName.put("BJ", "北京");
        codeMapName.put("TJ", "天津");
        codeMapName.put("SH", "上海");
        codeMapName.put("CQ", "重庆");
        codeMapName.put("NMG", "内蒙古");
        codeMapName.put("GX", "广西");
        codeMapName.put("NX", "宁夏");
        codeMapName.put("XZ", "西藏");
        codeMapName.put("XJ", "新疆");
        codeMapName.put("HK", "香港");
        codeMapName.put("AM", "澳门");
    }

    public static String evaluate(String param) {
        String result = "";
        if (StringUtils.isEmpty(param)) {
            return result;
        }
        result = codeMapName.getOrDefault(param, "");
        return result;
    }

    public static void main(String[] args) {
        String param = "NX";
        System.out.println(evaluate(param));
    }
}
