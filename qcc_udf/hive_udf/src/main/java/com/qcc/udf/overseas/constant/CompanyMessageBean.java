package com.qcc.udf.overseas.constant;

/**
 * kafka中存储的企业基本信息对象格式
 */
public class CompanyMessageBean {
    private Integer oper;    // 处理类型（删除/更新/新增）
    private String data;    // 企业基本信息
    private String countryCode; // 国家编码

    public Integer getOper() {
        return oper;
    }

    public void setOper(Integer oper) {
        this.oper = oper;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }
}
