package com.qcc.udf.company;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * <AUTHOR>
 */
public class getGroupCompLevel extends UDF {

    public String evaluate(String computeType, String actControllerKeyno, String computeTypeSub, String level) {
        //成员计算类型为空或者(3工商披露集团计算) 成员层级返回(999其他)
        if (StringUtils.isEmpty(computeType) || "3".equals(computeType)) {
            return "999";
        }

        //成员计算类型为空或者(0主公司自身)
        //如果实际控制人是人 成员层级返回(201直接控制) 否则返回(1集团本级)
        if ("0".equals(computeType)) {
            if (actControllerKeyno.startsWith("p")) {
                return "201";
            } else {
                return "1";
            }
        }

        //如果实际控制人是人
        if (actControllerKeyno.startsWith("p")) {
            //如果是从主公司计算过来的
            if ("1.1".equals(computeTypeSub) || "2.1".equals(computeTypeSub)) {
                if ("1".equals(level)) {
                    return "202";
                } else if ("2".equals(level)) {
                    return "203";
                } else if ("3".equals(level)) {
                    return "204";
                } else if ("4".equals(level)) {
                    return "205";
                } else if ("5".equals(level)) {
                    return "206";
                } else {
                    return "999";
                }
            }
            //如果是从实际控制人计算过来的
            else if ("1.2".equals(computeTypeSub) || "2.2".equals(computeTypeSub)) {
                if ("1".equals(level)) {
                    return "201";
                } else if ("2".equals(level)) {
                    return "202";
                } else if ("3".equals(level)) {
                    return "203";
                } else if ("4".equals(level)) {
                    return "204";
                } else if ("5".equals(level)) {
                    return "205";
                } else if ("6".equals(level)) {
                    return "206";
                } else {
                    return "999";
                }
            } else {
                return "999";
            }
        }
        //如果实际控制人不是人
        else {
            //根据level判断 超过5返回999
            if ("1".equals(level)) {
                return "101";
            } else if ("2".equals(level)) {
                return "102";
            } else if ("3".equals(level)) {
                return "103";
            } else if ("4".equals(level)) {
                return "104";
            } else if ("5".equals(level)) {
                return "105";
            } else {
                return "999";
            }
        }
    }

    public static void main(String[] args) {
        getGroupCompLevel model = new getGroupCompLevel();
        String result = model.evaluate("0", "p123", "0.0", "1");
        System.out.println(result);
    }
}