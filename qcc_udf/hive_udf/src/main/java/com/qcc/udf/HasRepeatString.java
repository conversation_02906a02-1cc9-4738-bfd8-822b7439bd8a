package com.qcc.udf;

/**
 * @Auther: wangbin
 * @Date: 2019/7/17 17:19
 * @Description:统计字符串中是否有重复的字符串
 */

import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

public class HasRepeatString extends UDF {

    public static boolean evaluate(String str){
        if(str == null || str == "")
            return false;

        if(!str.contains(","))
            return false;

        String[] temparray = str.split(",");
        Collection<String> list = Arrays.asList(temparray);
        Set<String> set = new HashSet(list);
        return !(temparray.length == set.size());
    }



}
