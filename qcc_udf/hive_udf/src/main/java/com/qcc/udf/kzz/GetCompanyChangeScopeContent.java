package com.qcc.udf.kzz;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 根据beforeinfos和afterinfos提取变更内容
 * <AUTHOR>
 * @date 2021/9/15
 */
public class GetCompanyChangeScopeContent extends UDF {
    private static Pattern pattern = Pattern.compile("(?<=<em>).*?(?=</em>)");

//    public static void main(String[] args) throws IOException {
//        String content = "[{\"KeyNo\":null,\"Org\":0,\"Content\":\"计算机软件开发、计算机信息技术服务\",\"Name\":null},{\"KeyNo\":null,\"Org\":0,\"Content\":\"通信系统自动化软硬件的开发,并提供技术咨询、技术服务\",\"Name\":null},{\"KeyNo\":null,\"Org\":0,\"Content\":\"计算机软硬件的销售及维护\",\"Name\":null},{\"KeyNo\":null,\"Org\":0,\"Content\":\"软件设计及技术转让,并提供相关技术服务\",\"Name\":null},{\"KeyNo\":null,\"Org\":0,\"Content\":\"企业管理咨询\",\"Name\":null},{\"KeyNo\":null,\"Org\":0,\"Content\":\"市场调查\",\"Name\":null},{\"KeyNo\":null,\"Org\":0,\"Content\":\"企业征信业务\",\"Name\":null},{\"KeyNo\":null,\"Org\":0,\"Content\":\"企业信用评估\",\"Name\":null},{\"KeyNo\":null,\"Org\":0,\"Content\":\"信用管理咨询。(依法须经批准的项目,经相关部门批准后方可开展经营活动)\",\"Name\":null}] ";
//
//        String contentResult = evaluate(content);
//        System.out.println(contentResult);
//    }



    public static String evaluate(String infos) {
        try{
            List<String> contents = new ArrayList<>();
            if(StringUtils.isNotBlank(infos)){
                JSONArray contentArray = JSONArray.parseArray(infos);
                if(null!=contentArray&&!contentArray.isEmpty()){
                    for (Object arr : contentArray) {
                        JSONObject object = (JSONObject)arr;
                        String detail = object.getString("Content");
                        if(StringUtils.isNotBlank(detail)&&detail.contains("<em>")){
                            Matcher matcher = pattern.matcher(detail);
                            while(matcher.find()){
                                contents.add(matcher.group());
                            }
                        }
                    }
                }
            }
            return contents.stream().collect(Collectors.joining("|"));
        }catch (Exception e){
            return "";
        }

    }
}
