package com.qcc.udf.sivs;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 投资机构清洗UDF：根据集团企业的过程表拆分，获取实际控制逻辑下（公司制为大股东，有限合伙为执行事务合伙人）每一环节往下的公司链路
 * ---------------------------------------------------------------------------------------------------------
 * create temporary function getControllerCompanyLink as 'com.qcc.udf.sivs.GetControllerCompanyLinkUDF' using jar 'hdfs:///data/hive/udf/qcc_udf.jar';
 */
public class GetControllerCompanyLinkUDF extends UDF {
    public String evaluate(String finalKeyNo, String keyNoLink) {
        JSONArray jsonArray = new JSONArray();
        if (StringUtils.isNotBlank(finalKeyNo) && StringUtils.isNotBlank(keyNoLink)) {
            String allKeyNoLink = keyNoLink + "-" + finalKeyNo;
            List<String> orginalKeyNoList = Arrays.stream(allKeyNoLink.split("\\-")).collect(Collectors.toList());
            List<String> linkedKeyNoList = new ArrayList<>();
            for (int i = orginalKeyNoList.size() - 1; i >= 0; i--) {
                linkedKeyNoList.add(orginalKeyNoList.get(i));
            }

            if (CollectionUtils.isNotEmpty(linkedKeyNoList) && linkedKeyNoList.size() > 1) {
                for (int i=0; i<linkedKeyNoList.size(); i++) {
                    String startKeyNo = linkedKeyNoList.get(i);
                    List<String> list = new ArrayList<>();
                    for (int j=i+1; j<linkedKeyNoList.size(); j++) {
                        list.add(linkedKeyNoList.get(j));
                    }

                    if (CollectionUtils.isNotEmpty(list)) {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("keyNo", startKeyNo);
                        jsonObject.put("link", StringUtils.join(list, ","));
                        jsonArray.add(jsonObject);
                    }
                }
            }
        }
        return jsonArray.toJSONString();
    }

    public static void main(String[] args) {

        String finalKeyNo = "p768792fd211e7758071a120055a01cd";
        String keyNoLink = "00026b2fa6bbc7244094f4482473acbf-05ee79b31d13899596c004b410e7f0bb-b1c79a9f8d67ec26c7caa010d43beece-293ea2f32a04e6a6c34b02db17363a02-d87d6ba0e4573494c22169f454bcddcb";
        String result = new GetControllerCompanyLinkUDF().evaluate(finalKeyNo, keyNoLink);
        System.out.println(result);
    }
}
