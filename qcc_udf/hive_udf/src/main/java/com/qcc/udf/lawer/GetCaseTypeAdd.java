package com.qcc.udf.lawer;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * <AUTHOR>
 * @date ：Created in 2021/06/07 11:05
 * @description ：获取案件类型
 */
public class GetCaseTypeAdd extends UDF {

    public String evaluate(String annoList, String caseType) {
        if (StringUtils.isEmpty(caseType)) {
            caseType = "";
        }
        if (StringUtils.isEmpty(annoList)) {
            return caseType;
        }
        if (StringUtils.isEmpty(caseType)) {
            if (annoList.contains("辖")) {
                return "管辖案件";
            }
        } else {
            if (annoList.contains("辖")) {
                return caseType.concat(",管辖案件");
            }
        }
        return caseType;
    }

    public static void main(String[] args) {
        GetCaseTypeAdd getCaseTypeAdd = new GetCaseTypeAdd();
        String annoList = "（2019）苏03行初224号,（2019）苏03行初224号";
        String caseType = "执行案件,民事案件";
        System.out.println(getCaseTypeAdd.evaluate(annoList, caseType));
    }
}
