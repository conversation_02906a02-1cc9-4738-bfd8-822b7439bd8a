package com.qcc.udf.property_clue;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.qcc.udf.court_notice.anUtils.MD5Util;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Iterator;

/**
 * @Auther: liulh
 * @Date: 2021/9/15 20:33
 * @Description:
 */
public class test extends UDF {
    public static String  evaluate(String param) {
        JSONArray result = new JSONArray();

        if (StringUtils.isEmpty(param)){
            return "";
        }

        JSONObject infoJson = JSONObject.parseObject(param);
        JSONArray guaranteeArray = infoJson.getJSONArray("guarantee_array") == null ? new JSONArray() : infoJson.getJSONArray("guarantee_array");
        JSONArray voucheeArray = infoJson.getJSONArray("vouchee_array") == null ? new JSONArray() : infoJson.getJSONArray("vouchee_array");
        String id = infoJson.getString("id") == null ? "" : infoJson.getString("id");
        String code = infoJson.getString("symbol") == null ? "" : infoJson.getString("symbol");
        String guaranteeMoney = infoJson.getString("guarantee_money") == null ? "0" : infoJson.getString("guarantee_money");
        String guaranteeCurrency = infoJson.getString("guarantee_currency") == null ? "" : infoJson.getString("guarantee_currency");
        String guaranteeCurrencyCorrect = infoJson.getString("guarantee_currency_correct") == null ? "" : infoJson.getString("guarantee_currency_correct");
        String announcementDate = infoJson.getString("announcement_date") == null ? "" : infoJson.getString("announcement_date");
        String reportDate = infoJson.getString("report_date") == null ? "" : infoJson.getString("report_date");
        String tradeDate = infoJson.getString("trade_date") == null ? "" : infoJson.getString("trade_date");
        int riskLevel = 3;
        if (Double.parseDouble(guaranteeMoney) > 5000){
            riskLevel = 2;
        }else{
            riskLevel = 3;
        }

        Iterator<Object> it1 = guaranteeArray.iterator();
        while (it1.hasNext()){
            JSONObject json = (JSONObject) it1.next();
            String keyNo = json.getString("KeyNo");
            String name = json.getString("Name");

            if (StringUtils.isNotEmpty(keyNo)){
                // changeExtend字段
                JSONObject extend = new JSONObject();
                extend.put("A", guaranteeMoney);
                extend.put("B", guaranteeCurrency);
                extend.put("C", code);
                extend.put("D", guaranteeArray);
                extend.put("E", voucheeArray);
                extend.put("T", 1);

                JSONObject item = new JSONObject();
                item.put("KeyNo", keyNo);
                item.put("Name", name);
                item.put("RiskLevel", riskLevel);
                item.put("Category", 101);
                item.put("ChangeExtend", extend);
                item.put("ObjectId", id);
                item.put("ChangeDate", announcementDate);
                item.put("Id", MD5Util.ecodeByMD5(id.concat(keyNo).concat("1")));

                result.add(item);
            }
        }

        Iterator<Object> it2 = voucheeArray.iterator();
        while (it2.hasNext()){
            JSONObject json = (JSONObject) it2.next();
            String keyNo = json.getString("KeyNo");
            String name = json.getString("Name");

            if (StringUtils.isNotEmpty(keyNo)){
                // changeExtend字段
                JSONObject extend = new JSONObject();
                extend.put("A", guaranteeMoney);
                extend.put("B", guaranteeCurrency);
                extend.put("C", code);
                extend.put("D", guaranteeArray);
                extend.put("E", voucheeArray);
                extend.put("T", 2);

                JSONObject item = new JSONObject();
                item.put("KeyNo", keyNo);
                item.put("Name", name);
                item.put("RiskLevel", riskLevel);
                item.put("Category", 101);
                item.put("ChangeExtend", extend);
                item.put("ObjectId", id);
                item.put("ChangeDate", announcementDate);
                item.put("Id", MD5Util.ecodeByMD5(id.concat(keyNo).concat("2")));

                result.add(item);
            }
        }

        return JSONArray.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect);
    }

    public static void main(String[] args) {
        String info ="{\"id\":\"39c42bcb41531688b707536fa8fa11bf\",\"symbol\":\"002835\",\"guarantee_array\":\"[{\\\"KeyNo\\\":\\\"d717a0c26e72288d3935ebc9dc91fe96\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"山西漳泽电力股份有限公司\\\"}]\",\"vouchee_array\":\"[{\\\"KeyNo\\\":\\\"7e97fddfc7a6a4e72e7289c7e4876ad5\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"山西临汾热电有限公司\\\"}]\",\"guarantee_money\":\"7715.2100\",\"guarantee_currency\":\"人民币\",\"guarantee_currency_correct\":\"CNY\",\"announcement_date\":1492704000,\"report_date\":1483113600,\"trade_date\":1432224000} ";
        System.out.println(evaluate(info));
    }
}
