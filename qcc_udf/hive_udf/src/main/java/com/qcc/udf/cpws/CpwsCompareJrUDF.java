package com.qcc.udf.cpws;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


public class CpwsCompareJrUDF extends UDF {

    public static String evaluate(String caserole, String lawresult)
    {
        List<JSONObject> caserolenew = new ArrayList<>();
      if(StringUtils.isNotEmpty(caserole)){
          caserolenew = JSON.parseArray(caserole,JSONObject.class);
      }
        List<JSONObject> lawresultnew = new ArrayList<>();
        if(StringUtils.isNotEmpty(lawresult)){
            lawresultnew = JSON.parseArray(lawresult,JSONObject.class);
        }

        Map<String,String> CaseResp = new HashedMap();
        for (JSONObject object : caserolenew) {
           JSONArray list =object.getJSONArray("DetailList");
            for (Object o : list) {
                JSONObject de= (JSONObject) o;
                if(StringUtils.isNotEmpty(de.getString("JR"))){
                    CaseResp.put(de.getString("Name"),de.getString("JR"));}
            }
        }
        Map<String,String> resultResp = new HashedMap();

        for (JSONObject object : lawresultnew) {
            if(StringUtils.isNotEmpty(object.getString("JR"))) {
                resultResp.put(object.getString("P"),object.getString("JR"));
            }
        }

        if(CaseResp.size()!=resultResp.size()){
            for (String s : resultResp.keySet()) {
                if(!resultResp.get(s).equals(CaseResp.get(s))){
                    return s;
                }
            }

        for (String s : CaseResp.keySet()) {
            if(!CaseResp.get(s).equals(resultResp.get(s))){
                return s;
            }
        }
        }
        return "false";

    }

    public static void main(String[] args) {
        String a= "[{\"Role\":\"申请执行人\",\"LawyerTag\":0,\"DetailList\":[{\"KeyNo\":\"\",\"Org\":-2,\"JR\":\"\",\"JudgeResult\":\"\",\"ShowName\":\"王**\",\"Name\":\"王从利\",\"LawyerList\":[]}],\"RoleTag\":0},{\"Role\":\"被执行人\",\"LawyerTag\":0,\"DetailList\":[{\"KeyNo\":\"0418e695228efb5a1b2b34a7e64c2a80\",\"Org\":0,\"JR\":\"28\",\"JudgeResult\":\"21\",\"ShowName\":\"浙江圣宇成套电气有限公司\",\"Name\":\"浙江圣宇成套电气有限公司\",\"LawyerList\":[]}],\"RoleTag\":1}]";
        String b="[{\"P\":\"浙江圣宇成套电气有限公司\",\"T\":\"21\",\"JR\":\"28\",\"ShowName\":\"浙江圣宇成套电气有限公司\",\"N\":\"0418e695228efb5a1b2b34a7e64c2a80\"}]";
        System.out.println(evaluate(null,null));

    }


}
