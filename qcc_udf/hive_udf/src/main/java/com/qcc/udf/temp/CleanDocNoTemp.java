package com.qcc.udf.temp;

import cn.hutool.core.util.ReUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Pattern;

/**
 * 清洗doc no
 */
public class CleanDocNoTemp extends UDF {

    public String evaluate(String s) {
        return cleanFieldMethodA(s);
    }

    public static void main(String[] args) {
        CleanDocNoTemp d = new CleanDocNoTemp();
        System.out.println(d.evaluate("昆建罚字(2021)第270号"));
    }
    public String cleanFieldMethodA(String input) {
        if (StringUtils.isEmpty(input)) {
            return "";
        } else {
            String step1 = input.replaceAll("(\\s|\\*|null| )", "");
            String step2 = ReUtil.replaceAll(step1, Pattern.compile("\\pP{3,}"), "");
            return ReUtil.replaceAll(step2, Pattern.compile("\\(|\\)|\\（|\\）|\\[|\\]|\\【|\\】|\\{|\\}"), "");
        }
    }
}
