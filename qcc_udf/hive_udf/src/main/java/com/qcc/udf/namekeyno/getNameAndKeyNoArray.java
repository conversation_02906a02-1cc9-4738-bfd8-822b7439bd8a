package com.qcc.udf.namekeyno;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.hadoop.hive.ql.exec.UDF;

public class getNameAndKeyNoArray extends UDF {

    public String evaluate(String keyNo, String name) {
        JSONArray array = new JSONArray();
        JSONObject result = new JSONObject();
        result.put("Name", name == null ? "" : name);
        result.put("KeyNo", keyNo == null ? "" : keyNo);
        result.put("Org", NameUtil.getOrgByKeyNo(keyNo));

        array.add(result);
        return array.toString();
    }

    public static void main(String[] args) {

    }
}
