package com.qcc.udf.casesearch_v3.util;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Strings;
import com.qcc.udf.casesearch_v3.entity.input.CaseRoleEntity;
import com.qcc.udf.casesearch_v3.entity.output.LawSuitV3OutputEntity;
import com.qcc.udf.temp.MD5Util;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021年04月15日 11:17
 */
public class CaseSearchUtil {
    private static Set<String> CASE_REASON_SET_1 = new HashSet<>();
    static {
        //著作权权属、侵权纠纷 案由匯總
        CASE_REASON_SET_1.add("著作权权属、侵权纠纷");
        CASE_REASON_SET_1.add("著作权权属纠纷");
        CASE_REASON_SET_1.add("侵害作品发表权纠纷");
        CASE_REASON_SET_1.add("侵害作品署名权纠纷");
        CASE_REASON_SET_1.add("侵害作品修改权纠纷");
        CASE_REASON_SET_1.add("侵害保护作品完整权纠纷");
        CASE_REASON_SET_1.add("侵害作品复制权纠纷");
        CASE_REASON_SET_1.add("侵害作品发行权纠纷");
        CASE_REASON_SET_1.add("侵害作品出租权纠纷");
        CASE_REASON_SET_1.add("侵害作品展览权纠纷");
        CASE_REASON_SET_1.add("侵害作品表演权纠纷");
        CASE_REASON_SET_1.add("侵害作品放映权纠纷");
        CASE_REASON_SET_1.add("侵害作品广播权纠纷");
        CASE_REASON_SET_1.add("侵害作品信息网络传播权纠纷");
        CASE_REASON_SET_1.add("侵害作品摄制权纠纷");
        CASE_REASON_SET_1.add("侵害作品改编权纠纷");
        CASE_REASON_SET_1.add("侵害作品翻译权纠纷");
        CASE_REASON_SET_1.add("侵害作品汇编权纠纷");
        CASE_REASON_SET_1.add("侵害其他著作财产权纠纷");
        CASE_REASON_SET_1.add("出版者权权属纠纷");
        CASE_REASON_SET_1.add("表演者权权属纠纷");
        CASE_REASON_SET_1.add("录音录像制作者权权属纠纷");
        CASE_REASON_SET_1.add("广播组织权权属纠纷");
        CASE_REASON_SET_1.add("侵害出版者权纠纷");
        CASE_REASON_SET_1.add("侵害表演者权纠纷");
        CASE_REASON_SET_1.add("侵害录音录像制作者权纠纷");
        CASE_REASON_SET_1.add("侵害广播组织权纠纷");
        CASE_REASON_SET_1.add("计算机软件著作权权属纠纷");
        CASE_REASON_SET_1.add("侵害计算机软件著作权纠纷");
    }
    /**
     * 生成组标识
     * @param outputEntity
     * @return
     */
    public static String buildSeriesGroupId(LawSuitV3OutputEntity outputEntity){
        String id = outputEntity.getId();

        if(Strings.isNullOrEmpty(id)){
            id = "";
        }

        try{
            StringBuilder sb = new StringBuilder();
            String fmt = "[%s]";
            String caseRole = outputEntity.getCaseRole();
            String caseReason = outputEntity.getCaseReason();
            String latestTrialRound = outputEntity.getLatestTrialRound();

            //无当事人 有Id 使用id作为组id
            if("[]".equals(caseRole) || Strings.isNullOrEmpty(caseRole) || Strings.isNullOrEmpty(caseReason)){
                return id;
            }

//            //只做[著作权权属、侵权纠纷]类型案件
//            if(!CASE_REASON_SET_1.contains(caseReason)){
//                return id;
//            }

            if(Strings.isNullOrEmpty(caseReason)){
                return id;
            }

            if(caseReason.indexOf("其他") == 0){
                return id;
            }

            //案号年份，法院代字
            String[] anNoList =  outputEntity.getAnNoList().split(",");
            String courtList = outputEntity.getCourtList();
            long earliestDate  = outputEntity.getEarliestDate();
            long lastestDate  = outputEntity.getLastestDate();

            if(Strings.isNullOrEmpty(latestTrialRound)
                    || Strings.isNullOrEmpty(outputEntity.getAnNoList())
                    ||Strings.isNullOrEmpty(courtList)
                    || earliestDate == 0
                    || lastestDate == 0){
                return id;
            }



            Set<Integer> anNoYearSet = new HashSet<>();
            Set<String> courtCodeSet = new HashSet<>();
            for (String anNo : anNoList) {
                anNoYearSet.add(getAnNoYear(anNo));
                courtCodeSet.add(getCourtCode(anNo));
            }

            sb.append(String.format(fmt,convertCaseRole(caseRole)));
            sb.append(String.format(fmt,caseReason));
            sb.append(String.format(fmt,latestTrialRound));
            sb.append(String.format(fmt,  anNoYearSet.stream().map(data->data.toString())
                    .sorted().collect(Collectors.joining(","))));
            sb.append(String.format(fmt,  courtCodeSet.stream().sorted().collect(Collectors.joining(","))));
            sb.append(String.format(fmt,courtList));
            sb.append(String.format(fmt,courtList));
            sb.append(String.format(fmt,convertYMD(earliestDate)));
            sb.append(String.format(fmt,convertYMD(lastestDate)));
            return MD5Util.encode(sb.toString());
        }catch (Exception e){
            e.printStackTrace();
        }

        //异常情况下使用Id做为系列案件分组 最大限度防止串组
        return id;

    }

    public static String convertCaseRole(String caseRole){
        List<CaseRoleEntity> role = JSONArray.parseArray(caseRole,CaseRoleEntity.class);
        for (CaseRoleEntity roleEntity : role) {
            roleEntity.setD(null);
            roleEntity.setO(0);
            roleEntity.setRoleList(null);
            roleEntity.setT(null);
        }

        return JSON.toJSONString(role);
    }

    public static String getCourtCode(String anNo){
        // 根据案号获取
        Pattern p = Pattern.compile("([京皖鲁赣闽甘冀青吉粤沪琼最内苏津晋渝黑藏陕黔云辽鄂宁浙川桂新京兵湘豫]+\\d*)");
        Matcher m = p.matcher(anNo);

        if (m.find()) {
            return m.group();
        }
        return "-";
    }

    public static int getAnNoYear(String anNo){
        String year = ReUtil.get("\\d{4}", anNo, 0);
        if (StringUtils.isNotBlank(year) && StringUtils.isNumeric(year)) {
            int yearNum = Integer.parseInt(year);
            if (yearNum > 1980 &&yearNum <= DateUtil.thisYear()+5) {
               return yearNum;
            }
        }
        return 0;
    }

    static String convertYMD(long date){
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return dtf.format(LocalDateTime.ofEpochSecond(date,0, ZoneOffset.ofHours(8)));
    }


    public static String getAnNoEnd(String annoList){
        Set<String> annoSet =new HashSet<>();
        String[] annoArray = annoList.split(",");
        for (String anno : annoArray) {
            String end =  ReUtil.get("((\\d+-\\d+)|(\\d+))号", anno, 0);
            if(!Strings.isNullOrEmpty(end)){
                annoSet.add(end);
            }
        }

        return annoSet.stream().sorted().collect(Collectors.joining(","));
    }

    public static void main(String[] args) {
//        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//        String anno = "2020苏0922执保588号";
//        System.out.println(getAnNoYear(anno));
//        System.out.println(getCourtCode(anno));
//        System.out.println(new Date(0L));
//        System.out.println(dtf.format(LocalDateTime.ofEpochSecond(1200412800L,0, ZoneOffset.ofHours(8))));

//        String json = "{\"AmtInfo\":{},\"AnNoList\":\"（2019）黑0208民初121号\",\"AnnoCnt\":1,\"CaseCnt\":1,\"CaseName\":\"孙丽香与鄂晓伟追偿权纠纷的案件\",\"CaseReason\":\"侵害作品发表权纠纷\",\"CaseRole\":\"[]\",\"CaseType\":\"民事案件\",\"CfdfCnt\":0,\"CfgsCnt\":0,\"CfxyCnt\":0,\"CompanyKeywords\":\"孙丽香,鄂晓伟\",\"CourtList\":\"黑龙江省齐齐哈尔市梅里斯达斡尔族区人民法院\",\"EarliestDate\":1546790400,\"EarliestDateType\":\"民事一审|立案日期\",\"FyggCnt\":1,\"GqdjCnt\":0,\"GroupId\":\"487ec460e5f8b0a1d0ea85fc7dad7ce1\",\"GroupMark\":\"[[{\\\"N\\\":\\\"\\\",\\\"O\\\":0,\\\"P\\\":\\\"孙丽香\\\",\\\"R\\\":\\\"原告\\\"},{\\\"N\\\":\\\"\\\",\\\"O\\\":0,\\\"P\\\":\\\"鄂晓伟\\\",\\\"R\\\":\\\"被告\\\"}]][追偿权纠纷][民事一审][2019][黑0208][黑龙江省齐齐哈尔市梅里斯达斡尔族区人民法院][黑龙江省齐齐哈尔市梅里斯达斡尔族区人民法院][1970-01-01][1970-01-01]\",\"HbcfCnt\":0,\"Id\":\"dcb1b0f01dafdd8bf02f242df82354e7\",\"InfoList\":[{\"AnNo\":\"（2019）黑0208民初121号\",\"CaseList\":[{\"Amt\":\"27200.00\",\"CaseType\":\"民事判决书\",\"DocType\":\"判决日期\",\"Id\":\"a52e048d701611f80e5f05231e4e5d4a0\",\"IsValid\":1,\"JudgeDate\":1558972800,\"Result\":\"一、被告鄂晓伟于本判决生效后给付原告孙丽香代为偿还的借款本息人民币17200元；  二、被告鄂晓伟于本判决生效后按本金10000元自2017年7月23日按月利率1.5分计息，利随本清。\",\"ResultType\":\"判决结果\",\"ShieldCaseFlag\":0}],\"CaseReason\":\"追偿权纠纷\",\"CaseType\":\"民事案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"黑龙江省齐齐哈尔市梅里斯达斡尔族区人民法院\",\"Defendant\":[{\"KeyNo\":\"\",\"Name\":\"鄂晓伟\",\"Org\":-2,\"Role\":\"被告\"}],\"ExecuteNo\":\"\",\"FyggList\":[{\"Category\":\"裁判文书\",\"Id\":\"6002182508\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"鄂晓伟\",\"Org\":-2},{\"KeyNo\":\"\",\"Name\":\"孙丽香\",\"Org\":-2}],\"PublishDate\":1569081600}],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[{\"ExecuteUnite\":\"\",\"Id\":\"5e3ae4000c9f271e9d49f9596167367e5\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"孙丽香\",\"Org\":-2},{\"KeyNo\":\"\",\"Name\":\"鄂晓伟\",\"Org\":-2}],\"OpenDate\":1558972800}],\"LatestTimestamp\":1569081600,\"LianList\":[{\"Id\":\"a52e048d701611f80e5f05231e4e5d4a\",\"IsValid\":1,\"LianDate\":1546790400,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"孙丽香\",\"Org\":-2},{\"KeyNo\":\"\",\"Name\":\"鄂晓伟\",\"Org\":-2}]}],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"\",\"Name\":\"孙丽香\",\"Org\":-2,\"Role\":\"原告\"}],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"民事一审\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]}],\"KtggCnt\":1,\"LastestDate\":1569081600,\"LastestDateType\":\"民事一审|法院公告刊登日期\",\"LatestTrialRound\":\"民事一审\",\"LianCnt\":1,\"PcczCnt\":0,\"ProcuratorateList\":\"\",\"Province\":\"HLJ\",\"SdggCnt\":0,\"Source\":\"OT\",\"SxCnt\":0,\"Tags\":\"4,10,11,12\",\"Type\":1,\"XgCnt\":0,\"XjpgCnt\":0,\"XzcfCnt\":0,\"ZbCnt\":0,\"ZxCnt\":0}";
//        LawSuitV3OutputEntity entity = JSON.parseObject(json,LawSuitV3OutputEntity.class);
//        System.out.println(buildSeriesGroupId(entity));
//        System.out.println( ReUtil.get("((\\d+-\\d+)|(\\d+))号", "（2018）粤0604民初205-15号", 0));
//
        System.out.println("其他名师".indexOf("其他"));

        System.out.println("名师其他".indexOf("其他"));

    }
}
