package com.qcc.udf.risk_graph;


import org.apache.commons.lang3.StringUtils;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2021/3/5
 */

public enum RiskWdEnum {
    PCCZ(1,"破产重整"),
    XG(2,"限制高消费"),
    SX(5,"失信"),
    ZX(6,"被执行"),
    SFAJ(7,"民事起诉"),
    SFPM(9,"司法拍卖"),
    XJPG(10,"询价评估"),
    GQDJ(11,"股权冻结"),
    YZWF(12,"严重违法"),
    JYYC(13,"经营异常"),
    XZCF(14,"行政处罚"),
    HBCF(15,"环保处罚"),
    SSWF(16,"税收违法"),
    QSGG(17,"欠税公告"),
    WGCL(18,"违规处理"),
    CCJC(19,"抽查检查"),
    GQCZ(20,"股权出质"),
    DCDY(21,"动产抵押"),
    TDDY(22,"土地抵押"),
    DWDB(23,"担保信息"),
    GSCG(24,"公示催示"),
    JYXX(25,"交易信息"),
    TZXX(26,"投资信息"),
    RZXX(27,"任职信息"),
    ;


    private int type;
    private String desc;

    RiskWdEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }


    public String getDesc() {
        return desc;
    }

    private static final Map<String, RiskWdEnum> lookup = new HashMap<String, RiskWdEnum>();

    static {
        for (RiskWdEnum e : EnumSet.allOf(RiskWdEnum.class)) {
            lookup.put(e.toString(), e);
        }
    }

    public static RiskWdEnum find(String name) {
        if (StringUtils.isBlank(name)){
            return null;
        }

        RiskWdEnum data = lookup.get(name.toUpperCase());
        if (name == null) {
            return null;
        }
        return data;
    }
}
