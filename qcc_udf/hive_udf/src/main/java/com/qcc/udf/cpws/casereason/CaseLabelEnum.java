package com.qcc.udf.cpws.casereason;

/**
 * 裁判文书标签枚举类
 * <AUTHOR>
 */
public enum CaseLabelEnum {
    PC_JF("PC00", "破产纠纷"),
    PC_QS_JF("PC01", "破产清算纠纷"),
    PC_ZQ_QR_JF("PC02", "破产债权确认纠纷"),
    QT_PC_JF("PC03", "其他破产纠纷"),

    MM_JF("MM00", "买卖纠纷"),

    ZTB_JF("ZB00", "招投标纠纷"),
    CB_JF("ZB01", "串标纠纷"),
    QT_ZTB_JF("ZB02", "其他招投标纠纷"),

    JD_JF("JD00", "借贷纠纷"),

    SW_JF("SW00", "涉外纠纷"),
    XYZ_JF("SW01", "信用证纠纷"),
    SW_ZC("SW02", "涉外仲裁"),

    TW_SH("TW00", "贪污受贿"),
    TW("TW01", "贪污"),
    XH("TW02", "行贿"),
    SH("TW03", "受贿"),

    CP_ZL("CP00", "产品质量"),
    JM_WL_CP("CP01", "假冒伪劣产品"),
    CP_ZR_JF("CP02", "产品责任纠纷"),

    LD_ZY("LD00", "劳动争议"),
    FF_GY_TG("LD01", "非法雇佣童工"),
    FS_PC("LD02", "工伤赔偿"),
    LD_HT_ZY("LD03", "劳动合同争议"),
    XG_LD_GX("LD04", "虚构劳动关系"),
    SB_JF("LD05", "社保纠纷"),
    QT_LD_ZY("LD06", "其他劳动争议"),

    ZSCQ_JF("ZC00", "知识产权纠纷"),
    ZZQ_JF("ZC01", "著作权纠纷"),
    SHANG_BIAO_JF("ZC02", "商标纠纷"),
    ZL_JF("ZC03", "专利纠纷"),
    QT_ZSCQ_JF("ZC04", "其他知识产权纠纷"),

    HS_HS("HS00", "海事海商"),

    BX_JF("BX00", "保险纠纷"),

    PJ_JF("PJ00", "票据纠纷"),

    GQ_JF("GQ00", "股权纠纷"),
    GD_CZ_JF("GQ01", "股东出资纠纷"),
    GQ_QR_JF("GQ02", "股权确认纠纷"),
    GQ_ZR_JF("GQ03", "股权转让纠纷"),
    GD_QL_JF("GQ04", "股东权利纠纷"),
    QT_GQ_JF("GQ05", "其他股权纠纷"),

    BZD_JF("BZ00", "不正当竞争"),
    ;

    private String code;
    private String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    CaseLabelEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(String code) {
        for (CaseLabelEnum typeEnum : CaseLabelEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getDesc();
            }
        }
        return "";
    }

    public static String getCodeByDesc(String desc) {
        for (CaseLabelEnum typeEnum : CaseLabelEnum.values()) {
            if (typeEnum.getDesc().equals(desc)) {
                return typeEnum.getCode();
            }
        }
        return "";
    }
}
