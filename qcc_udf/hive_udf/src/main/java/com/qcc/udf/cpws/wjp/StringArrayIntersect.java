package com.qcc.udf.cpws.wjp;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

// 注册UDF
public class StringArrayIntersect extends UDF {

        public static boolean evaluate(String str1, String str2) {
        if (StringUtils.isEmpty(str1) | StringUtils.isEmpty(str2)) return false;
        Set<String> set1 = new HashSet<>(Arrays.asList(str1.split(",")));
        Set<String> set2 = new HashSet<>(Arrays.asList(str2.split(",")));
        set1.retainAll(set2);
        return !set1.isEmpty();
    }

    public static void main(String[] args) {
        System.out.println(evaluate(null,""));
    }
}