package com.qcc.udf.lawer;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/06/07 11:05
 * @description ：获取案件类型code
 */
public class GetCaseTypeCode extends UDF {

    public String evaluate(String caseType) {
        String caseTypeCode = "";
        if (StringUtils.isEmpty(caseType)) {
            caseType = "";
        }
        List<String> codeSet = new LinkedList<>();
        if (caseType.contains("民事")){
            codeSet.add("1");
        }
        if (caseType.contains("刑事")){
            codeSet.add("2");
        }
        if (caseType.contains("行政")){
            codeSet.add("3");
        }
        if (caseType.contains("管辖")){
            codeSet.add("4");
        }
        if (caseType.contains("保全")){
            codeSet.add("5");
        }
        if (caseType.contains("执行")){
            codeSet.add("6");
        }
        Collections.sort(codeSet);

        for (String str : codeSet){
            caseTypeCode = caseTypeCode.concat("_").concat(str);
        }

        caseTypeCode = caseTypeCode.length() > 0 ? caseTypeCode.substring(1) : "";

        return caseTypeCode;
    }

    public static void main(String[] args) {
        GetCaseTypeCode getCaseTypeCode = new GetCaseTypeCode();
        String caseType = "执行案件";
        System.out.println(getCaseTypeCode.evaluate(caseType));
    }
}
