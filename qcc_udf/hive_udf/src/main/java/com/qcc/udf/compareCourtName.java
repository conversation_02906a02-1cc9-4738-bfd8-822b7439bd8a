package com.qcc.udf;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.text.DecimalFormat;

/**
 * @Auther: liulh
 * @Date: 2020/6/1 17:54
 * @Description:
 */
public class compareCourtName extends UDF {
    public static int evaluate(String obj1, String obj2) {
        int result = 0;

        if (StringUtils.isEmpty(obj1) || StringUtils.isEmpty(obj2)){
            return result;
        }
        // 去掉通用词汇之后，再次比较
        obj1 = obj1.replaceAll("(中级人民法院)|(人民法院)|(法院)|(省)|(市)|(县)|(区)", "");
        obj2 = obj2.replaceAll("(中级人民法院)|(人民法院)|(法院)|(省)|(市)|(县)|(区)", "");

        if (obj1.equals(obj2)){
            result = 1;
        }else {
            if (obj1.length() == 1 || obj2.length() == 1){
                result  = 0;
            }else{
                if (obj1.contains(obj2) || obj2.contains(obj1)){
                    result = 1;
                }
            }
        }


        return result;
    }

    public static void main(String[] args) {
        System.out.println(evaluate("安徽省怀安县人民法院", "怀安县人民法院"));
    }

}
