package com.qcc.udf.cpws;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.LinkedHashSet;
import java.util.Set;

public class getAdjClassify extends UDF {
    public String evaluate(String caseName, String result, String caseNo) {
        String code = "";

        caseNo = caseNo == null ? "" : caseNo;
        caseName = caseName == null ? "" : caseName;
        result = result == null ? "" : result;

        Set<String> codeSet = new LinkedHashSet<>();
        if (caseName.contains("不予受理") && result.contains("本院不予受理")){
            codeSet.add("不予受理");
        }
        if (caseName.contains("管辖") && ((result.contains("管辖权") && result.contains("异议"))) || caseNo.contains("辖")){
            codeSet.add("管辖权异议");
        }
        if (caseName.contains("驳回起诉") && result.contains("驳回") && (result.contains("的起诉") || result.contains("的再审申请"))){
            codeSet.add("驳回起诉");
        }
        if (caseName.contains("驳回上诉")){
            codeSet.add("驳回上诉");
        }
        if (caseName.contains("驳回再审申请") && result.contains("驳回") && result.contains("的再审申请")){
            codeSet.add("驳回再审");
        }
        if (caseName.contains("财产保全")){
            codeSet.add("保全");
        }
        if (result.contains("撤回起诉") || result.contains("撤回上诉") || result.contains("撤诉")){
            codeSet.add("准许或不准许撤诉");
        }
        if (result.contains("中止诉讼") || result.contains("诉讼中止") || result.contains("终结诉讼") || result.contains("诉讼终结")){
            codeSet.add("中止或终结诉讼");
        }
        if(result.contains("笔误")){
            codeSet.add("补正判决书中的笔误");
        }
        if ((result.contains("中止") || result.contains("终结")) && result.contains("执行")){
            codeSet.add("中止或终结执行");
        }
        if (caseName.contains("申请撤销仲裁裁决")){
            codeSet.add("撤销或不予执行仲裁裁决");
        }
        if (result.contains("终结") && result.contains("具有强制执行效力的债权文书公证书")){
            codeSet.add("不予执行公证机关赋予强制执行效力的债权文书");
        }
        if (result.contains("本裁定为终审裁定")){
            codeSet.add("终审裁定");
        }


        if (codeSet.size() > 0){
            code = String.join(",", codeSet);
        }

        return code;
    }

    public static void main(String[] args) {
        System.out.println(new getAdjClassify().evaluate("固原大来投资有限公司与杨永忠、马存花、杨永军、马宗海民间借贷纠纷一审民事裁定书", "驳回原告固原大来投资有限公司的起诉。", ""));
    }
}
