package com.qcc.udf.es_suggest;


import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDTF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;
import org.apache.hadoop.io.IntWritable;

/**
 * <AUTHOR>
 * @Date 2020/6/9
 * @Description 搜索推荐的字段
 */
public class SuggestColumnSplit extends GenericUDTF {

    @Override
    public void process(Object[] os) throws HiveException {
        if (os.length != Column.values().length) {
            return;
        }

        Integer weight = ((IntWritable) os[0]).get();
        Column column;
        for (int i = 1; i < os.length; i++) {
            if (os[i] == null || "".equals(os[i].toString())) {
                continue;
            }

            column = Column.values()[i];
            switch (column) {
                case CONTACTNUMBER:
                case WECHAT:
                case EMAIL:
                case FEATURELIST:
                case PERSONSEARCH:
                case PRODUCT:
                case STOCKINFO:
                    String[] texts = os[i].toString().split("。");
                    for (String text : texts) {
                        forward(text, weight, column);
                    }
                    continue;
                default:
            }
            forward(os[i].toString(), weight, column);
        }
    }

    private void forward(String text, Integer weight, Column column) throws HiveException {
        if (text.length() < 2) {
            return;
        }
        if (weight > 0) {
            weight = (int) (weight * column.getWeight());
        } else if (weight == 0) {
            weight = (int) (5 * column.getWeight());
        } else {
            weight = (int) (weight - weight * column.getWeight());
        }
        Object[] result = new Object[3];
        result[0] = text;
        result[1] = weight;
        result[2] = column.getCode();
        forward(result);
    }

    @Override
    public void close() {

    }

    @Override
    public StructObjectInspector initialize(ObjectInspector[] args) {
        return ObjectInspectorFactory.getStandardStructObjectInspector(Column.fields(), Column.inspectors());
    }
}
