package com.qcc.udf.mns;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;
import org.mortbay.util.ajax.JSON;


import com.aliyun.mns.client.CloudAccount;
import com.aliyun.mns.client.CloudQueue;
import com.aliyun.mns.client.MNSClient;
import com.aliyun.mns.model.Message;
import com.aliyun.mns.model.Message.MessageBodyType;

/**
 * <AUTHOR>
 * @date 2021年09月23日 10:02
 */
public class SendMns extends UDF {
    //    private static final String ENDPOINT_DEV = "http://****************.mns.cn-hangzhou.aliyuncs.com";
    private static final String ENDPOINT_PRO = "http://****************.mns.cn-hangzhou-internal-vpc.aliyuncs.com";
    private static final String ACCESS_KEY_ID = "LTAIw7FdWj5zalXR";
    private static final String ACCESS_KEY_SECRET = "kpBSgyndLpaJGTeuc3FuYGIsSQVZOd";
    private static final String QUEUE_NAME = "QccCompanyInfoBatch";
    static CloudAccount account = new CloudAccount(ACCESS_KEY_ID, ACCESS_KEY_SECRET, ENDPOINT_PRO);


    public static boolean evaluate(String keyNo, String queueName, int priority, int category, String source) {
        int retry = 0;
        if (StringUtils.isEmpty(keyNo)) {
            return false;
        } else {
            MNSClient mnsclient = account.getMNSClient();;
            try {
                if (mnsclient == null) {
                    mnsclient = getMqClient();
                }

                if (StringUtils.isEmpty(queueName)) {
                    queueName = QUEUE_NAME;
                }

                while (retry < 5) {
                    ++retry;
                    CloudQueue queue = mnsclient.getQueueRef(queueName);
                    String msgBody = category > 0 ? keyNo + "_" + category : keyNo;
                    msgBody = StringUtils.isNotEmpty(source) ? msgBody + "_" + source : msgBody;
                    Message message = new Message();
                    message.setMessageBody("\"" + msgBody + "\"", MessageBodyType.RAW_STRING);
                    if ( priority != 0) {
                        message.setPriority(priority);
                    }

                    message = queue.putMessage(message);
                    if (StringUtils.isNotEmpty(message.getMessageId())) {
                        return true;
                    }
                }
            } catch (Exception var10) {
                throw new RuntimeException("catch e:"+var10.getMessage());
//                return JSON.toString(var10);
            } finally {
//                if (mnsclient != null) {
//                    mnsclient.close();
//                }
            }
            return false;
        }
    }


    public static void main(String[] args) {
//        System.out.println(11);
        System.out.println( evaluate("f625a5b661058ba5082ca508f99ffe1b", "QccCompanyInfo", 1, 2, "test"));
        System.out.println( evaluate("f625a5b661058ba5082ca508f99ffe1b", "QccCompanyInfo", 1, 2, "test"));
//        System.out.println(222);
    }

    private static MNSClient getMqClient() {
        CloudAccount account;
        account = new CloudAccount(ACCESS_KEY_ID, ACCESS_KEY_SECRET, ENDPOINT_PRO);
         return account.getMNSClient();
    }
}
