package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * @Auther: shengr
 * @Date: 2022/05/25
 * @Description: 此UDF函数 是为了修正hive中的to_date函数而编写的.存在返回值是NULL值的情况
 */
public class ToDate extends UDF {
    public static String evaluate(String ye) {
        try {
            boolean isDate;
            if (isDate(ye)) {
                return ye.substring(0, 10);
            } else {
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }


    public static boolean isDate(String timeStr) {
        //2021-01-01 00:00:00 格式
        String format1 = "^(((20[0-3][0-9]-(0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|(20[0-3][0-9]-(0[2469]|11)-(0[1-9]|[12][0-9]|30))) (20|21|22|23|[0-1][0-9]):[0-5][0-9]:[0-5][0-9])$";

        //2021-01-01 格式
        String format2 = "((19|20)[0-9]{2})-(0?[1-9]|1[012])-(0?[1-9]|[12][0-9]|3[01])";

        boolean flag = timeStr.matches(format1) || timeStr.matches(format2);

        return flag;
    }


    public static void main(String[] args) {

        System.out.println(evaluate("2021-01-01 10:55:55"));
    }
}