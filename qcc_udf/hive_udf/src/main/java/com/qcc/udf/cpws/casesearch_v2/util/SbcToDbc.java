package com.qcc.udf.cpws.casesearch_v2.util;

/**
 * <AUTHOR>
 * @desc
 * @date 2020/11/17
 */
public class SbcToDbc {
    /**
     * 全角转半角
     * @param sBCStr 全角字符
     * @return 返回半角字符
     */
    public static String convertSBCToDBC(String sBCStr) {
        if (sBCStr == null || sBCStr.trim().length() == 0) {
            return "";
        }
        char[] c = sBCStr.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] >= 65281 && c[i] <= 65374) {
                c[i] = (char) (c[i] - 65248);
            } else if (c[i] == 12288) {
                c[i] = (char) 32;
            }
        }
        return new String(c);
    }
}
