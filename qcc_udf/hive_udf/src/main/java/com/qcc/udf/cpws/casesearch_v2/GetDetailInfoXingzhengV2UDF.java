package com.qcc.udf.cpws.casesearch_v2;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.cpws.ExtractCaseTrialRoundUDF;
import com.qcc.udf.cpws.ExtractCaseTypeUDF;
import com.qcc.udf.cpws.ExtractCourtNameFromCaseNoUDF;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.stream.Collectors;

public class GetDetailInfoXingzhengV2UDF extends UDF {

    public String evaluate(List<String> sourceLianList, List<String> sourceKtggList,
                           List<String> sourceSdggList, List<String> sourceFyggList,
                           List<String> sourceCaseList) {

        //排序
        Collections.sort(sourceLianList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceKtggList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceSdggList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceFyggList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });

        Collections.sort(sourceCaseList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });

        LawSuitV2Entity inputLawSuitEntity = new LawSuitV2Entity();
        inputLawSuitEntity.setLianList(sourceLianList);
        inputLawSuitEntity.setKtggList(sourceKtggList);
        inputLawSuitEntity.setSdggList(sourceSdggList);
        inputLawSuitEntity.setFyggList(sourceFyggList);
        inputLawSuitEntity.setCaseList(sourceCaseList);

        List<LawSuitV2Entity> lawSuitEntityList = CommonV2Util.getLawSuitEntityList(inputLawSuitEntity, "xz");

        JSONArray detailInfoArray = new JSONArray();
        for (LawSuitV2Entity lawSuitEntity : lawSuitEntityList) {
            List<String> lianList = lawSuitEntity.getLianList();
            List<String> ktggList = lawSuitEntity.getKtggList();
            List<String> sdggList = lawSuitEntity.getSdggList();
            List<String> fyggList = lawSuitEntity.getFyggList();
            List<String> caseList = lawSuitEntity.getCaseList();

            Set<String> provinceCodeSet = CommonV2Util.collectProvinceCode(lianList, ktggList, sdggList, fyggList, caseList);
            for (String provinceCode : provinceCodeSet) {
                JSONObject result = new JSONObject();
                try {
                    /**
                     * 各审理程序列表对应字段
                     */
                    // 审理程序总数
                    Set<String> caseNoSet = new LinkedHashSet<>();
                    // 搜索关键字集合
                    Set<String> searchWordSet = new HashSet<>();
                    // 所有时间节点的map集合（key-> 时间戳; value->表示当前的节点状态，审判程序 + (立案/法院公告/开庭公告/送达公告)发布日期 或 判决日期 或 裁定日期）
                    Map<Long, String> dateNodeMap = new HashMap<>();

                    // 案号分组
                    Map<String, JSONObject> anNoMap = new LinkedHashMap<>();
                    Set<String> lianIdSet = new LinkedHashSet<>();
                    Set<String> ktggIdSet = new LinkedHashSet<>();
                    Set<String> sdggIdSet = new LinkedHashSet<>();
                    Set<String> fyggIdSet = new LinkedHashSet<>();
                    Set<String> caseIdSet = new LinkedHashSet<>();

                    dataClean(provinceCode, lianList, 1, anNoMap, caseNoSet, lianIdSet, searchWordSet, dateNodeMap);
                    dataClean(provinceCode, ktggList, 2, anNoMap, caseNoSet, ktggIdSet, searchWordSet, dateNodeMap);
                    dataClean(provinceCode, sdggList, 3, anNoMap, caseNoSet, sdggIdSet, searchWordSet, dateNodeMap);
                    dataClean(provinceCode, fyggList, 4, anNoMap, caseNoSet, fyggIdSet, searchWordSet, dateNodeMap);
                    dataClean(provinceCode, caseList, 5, anNoMap, caseNoSet, caseIdSet, searchWordSet, dateNodeMap);

                    // 按照案号显示数据
                    JSONArray array = new JSONArray();
                    for (String str : caseNoSet) {
                        /**
                         * 兼容执行案件中的infoList项
                         */
                        JSONObject jsonObj = anNoMap.get(str);
                        if (jsonObj != null) {
                            jsonObj = CommonV2Util.addExternalFieldToJsonStruct(jsonObj);
                            Long latestTimestamp = CommonV2Util.getLatestTimestampFromInfoListItem(jsonObj);
                            jsonObj.put("LatestTimestamp", latestTimestamp);
                            array.add(jsonObj);
                        }
                    }
                    result.put("InfoList", array);
                    result.put("AnNoList", StringUtils.join(caseNoSet, ","));

                    /**
                     * 案件统计字段
                     */
                    // 最新审理程序
                    result.put("LatestTrialRound", CommonV2Util.getLatestTrialRoundFromInfoList(array));
                    // 审理程序总数
                    result.put("AnnoCnt", caseNoSet.size());
                    // 关联裁判文书数
                    result.put("CaseCnt", caseIdSet.size());
                    // 关联立案信息数
                    result.put("LianCnt", lianIdSet.size());
                    // 关联开庭公告数
                    result.put("KtggCnt", ktggIdSet.size());
                    // 关联送达公告数
                    result.put("SdggCnt", sdggIdSet.size());
                    // 关联法院公告数
                    result.put("FyggCnt", fyggIdSet.size());
                    // 关联破产公告数
                    result.put("PcczCnt", 0);

                    // 兼容字段处理
                    // 关联被执行数
                    result.put("ZxCnt", 0);
                    // 关联失信数
                    result.put("SxCnt", 0);
                    // 关联限高数
                    result.put("XgCnt", 0);
                    // 关联终本案件数
                    result.put("ZbCnt", 0);
                    // 关联询价评估数
                    result.put("XjpgCnt", 0);
                    // 关联股权冻结数
                    result.put("GqdjCnt", 0);
                    // 关联环保处罚数
                    result.put("HbcfCnt", 0);
                    // 关联行政处罚（工商）
                    result.put("CfgsCnt", 0);
                    // 关联行政处罚（信用中国）
                    result.put("CfxyCnt", 0);
                    // 关联行政处罚（地方）
                    result.put("CfdfCnt", 0);

                    /**
                     * 案件基础字段
                     */
                    // 分组法院信息
                    result.put("GroupCourt", lawSuitEntity.getCourt());
                    // 所在省份编码
                    result.put("Province", provinceCode);
                    // 案件名称
                    result.put("CaseName", CommonV2Util.getCaseNameFromInfoList(array, "xz"));
                    // 案件类型
                    result.put("CaseType", "行政案件");
                    // 关联的公司或个人信息
                    result.put("CompanyKeywords", CommonV2Util.getCompanyKeywordsFromSearchWordSet(searchWordSet));
                    // 相关案号
                    result.put("AnNoList", CommonV2Util.getKeywordsFromInfoList(array, "AnNo"));
                    // 列表中的案由
                    result.put("CaseReason", CommonV2Util.getCaseReasonFromInfoList(array));
                    // 列表中的案件身份
                    /*String caseRoleInfo = getCaseRoleInfo(caseList, lianList, ktggList, fyggList, sdggList, provinceCode);
                    if (caseRoleInfo.equals("[]")) {
                        caseRoleInfo = CommonV2Util.getCaseRoleFromInfoList(array);
                    }*/
                    String caseRoleInfo = CommonV2Util.getCaseRoleFromInfoList(array);
                    result.put("CaseRole", caseRoleInfo);
                    // 相关法院
                    result.put("CourtList", CommonV2Util.getKeywordsFromInfoList(array, "Court"));
                    // 相关检察院
                    result.put("ProcuratorateList", CommonV2Util.getKeywordsFromInfoList(array, "Procuratorate"));

                    Map<Long, String> sortedDateNodeMap = new LinkedHashMap<>();
                    dateNodeMap.entrySet().stream()
                            .sorted(Map.Entry.comparingByKey())
                            .forEachOrdered(e -> sortedDateNodeMap.put(e.getKey(), e.getValue()));

                    result.put("EarliestDate", -1L);
                    result.put("EarliestDateType", "");
                    result.put("LastestDate", -1);
                    result.put("LastestDateType", "");

                    Long earliestDate = -1L;
                    String earliestDateType = "";
                    Long lastestDate = -1L;
                    String lastestDateType = "";
                    boolean flag = true;
                    for (Map.Entry<Long, String> sortedDateNodeEntry : sortedDateNodeMap.entrySet()) {
                        if (flag) {
                            earliestDate = sortedDateNodeEntry.getKey();
                            earliestDateType = sortedDateNodeEntry.getValue();
                            flag = false;
                        }
                        lastestDate = sortedDateNodeEntry.getKey();
                        lastestDateType = sortedDateNodeEntry.getValue();
                    }
                    result.put("EarliestDate", earliestDate);
                    result.put("EarliestDateType", CommonV2Util.getDataTypeWithoutTrialRound(earliestDateType));
                    if (sortedDateNodeMap.size() > 1) {
                        result.put("LastestDate", lastestDate);
                        result.put("LastestDateType", CommonV2Util.getDataTypeWithoutTrialRound(lastestDateType));
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                detailInfoArray.add(result);
            }
        }

        JSONArray resDetailInfoJSONArray = new JSONArray();
        Iterator iterator = detailInfoArray.iterator();
        while (iterator.hasNext()) {
            try {
                JSONObject jsonObject = (JSONObject) iterator.next();
                if (jsonObject.getLong("AnnoCnt") > 0) {
                    resDetailInfoJSONArray.add(jsonObject);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return resDetailInfoJSONArray.toJSONString();
    }

    /**
     *
     * @param provinceCode
     * @param infoList
     * @param type 1->立案信息 2->开庭公告 3->送达公告 4->法院公告 5->裁判文书 6->破产公告
     * @param anNoMap
     * @param caseNoSet
     * @param idSet
     * @param searchWordSet
     * @param dateNodeMap
     */
    public static void dataClean(String provinceCode, List<String> infoList, int type, Map<String, JSONObject> anNoMap,
                                 Set<String> caseNoSet, Set<String> idSet, Set<String> searchWordSet, Map<Long, String> dateNodeMap){
        // 编辑数据
        if (infoList != null && infoList.size() > 0) {
            Iterator it = infoList.iterator();
            while(it.hasNext()){
                JSONObject json = JSONObject.parseObject(it.next().toString());
                if (json == null || json.isEmpty()) {
                    continue;
                }
                // 过滤涉诉历史信息记录
                /*if (json.getInteger("isvalid") != 1) {
                    continue;
                }*/
                if (!json.getString("province").equals(provinceCode)) {
                    continue;
                }

                // 获取案号
                String anNo = CommonV2Util.full2Half(json.getString("anno"));
                if (type == 5 || type == 6){
                    anNo = CommonV2Util.full2Half(json.getString("caseno"));
                }
                // 过滤掉案号没有对应到行政案件类型的记录
                if (!new ExtractCaseTypeUDF().evaluate(anNo).equals("行政案件")) {
                    continue;
                }

                caseNoSet.add(anNo);

                // 部分字段的汇总逻辑
                try {
                    List<String> companyNameList = new ArrayList<>();
                    if (type == 1) {
                        companyNameList = Arrays.stream(json.getString("companykeywords").split(","))
                                .collect(Collectors.toList());
                    } else if (type == 2 || type == 3 || type == 5) {
                        companyNameList = Arrays.stream(json.getString("companynames").split(","))
                                .collect(Collectors.toList());
                    } else if (type == 4) {
                        JSONArray jsonArray = JSONArray.parseArray(json.getString("nameandkeyno"));
                        for (int i = 0; i < jsonArray.size(); i++) {
                            JSONObject jsonObject = jsonArray.getJSONObject(i);
                            companyNameList.add(jsonObject.getString("Name"));
                            companyNameList.add(jsonObject.getString("KeyNo"));
                        }
                    } else if (type == 6) {
                        JSONArray respondentJsonArray = JSONArray.parseArray(json.getString("respondentnameandkeyno"));
                        for (int i = 0; i < respondentJsonArray.size(); i++) {
                            JSONObject jsonObject = respondentJsonArray.getJSONObject(i);
                            companyNameList.add(jsonObject.getString("Name"));
                            companyNameList.add(jsonObject.getString("KeyNo"));
                        }
                        JSONArray applicantJsonArray = JSONArray.parseArray(json.getString("applicantnameandkeyno"));
                        for (int i = 0; i < applicantJsonArray.size(); i++) {
                            JSONObject jsonObject = respondentJsonArray.getJSONObject(i);
                            companyNameList.add(jsonObject.getString("Name"));
                            companyNameList.add(jsonObject.getString("KeyNo"));
                        }
                    }

                    // 汇总关联公司或个人信息
                    for (String companyName : companyNameList) {
                        searchWordSet.add(companyName);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

                // 判断该案号是否已经存在
                JSONObject jsonObject = new JSONObject();
                JSONArray itemArray = new JSONArray();
                JSONObject itemJson = new JSONObject();

                // 不存在，则创建新的对象
                if (!anNoMap.keySet().contains(anNo)){
                    // 列表数据
                    itemJson = editItemJson(json, type, dateNodeMap, anNo);
                }else{
                    // 存在则获取原有列表，进行数据补充
                    jsonObject = anNoMap.get(anNo);
                    // 列表数据
                    if (type == 1){
                        itemArray = jsonObject.getJSONArray("LianList");
                    }else if (type == 2){
                        itemArray = jsonObject.getJSONArray("KtggList");
                    }else if (type == 3){
                        itemArray = jsonObject.getJSONArray("SdggList");
                    }else if (type == 4){
                        itemArray = jsonObject.getJSONArray("FyggList");
                    }else if (type == 5){
                        itemArray = jsonObject.getJSONArray("CaseList");
                    }else if (type == 6) {
                        itemArray = jsonObject.getJSONArray("PcczList");
                    }
                    if (!idSet.contains(json.getString("id"))){
                        itemJson = editItemJson(json, type, dateNodeMap, anNo);
                    }
                }
                idSet.add(json.getString("id"));
                itemArray = itemArray == null ? new JSONArray() : itemArray;
                itemArray.add(itemJson);
                if (type == 1){
                    jsonObject.put("LianList", itemArray);
                }else if (type == 2){
                    jsonObject.put("KtggList", itemArray);
                }else if (type == 3){
                    jsonObject.put("SdggList", itemArray);
                }else if (type == 4){
                    jsonObject.put("FyggList", itemArray);
                }else if (type == 5){
                    jsonObject.put("CaseList", itemArray);
                }else if (type == 6) {
                    jsonObject.put("PcczList", itemArray);
                }
                jsonObject.put("AnNo", anNo);
                jsonObject.put("TrialRound", new ExtractCaseTrialRoundUDF().evaluate(anNo));
                jsonObject.put("CaseReason", "");
                jsonObject.put("Court", new ExtractCourtNameFromCaseNoUDF().evaluate(anNo));
                jsonObject.put("Procuratorate", "");
                jsonObject.put("ExecuteNo", "");        // 执行依据文书号

                if (type == 1 || type == 2) {
                    String caseRole = CommonV2Util.getCaseRoleFromLianOrKtggList(Arrays.asList(json.toJSONString()), type);
                    JSONArray caseRoleJsonArray = JSONArray.parseArray(caseRole);
                    if (caseRoleJsonArray != null && caseRoleJsonArray.size() > 0) {
                        JSONArray prosecutorArray = new JSONArray();
                        JSONArray defendantArray = new JSONArray();
                        for (int i = 0; i < caseRoleJsonArray.size(); i++) {
                            JSONObject caseRoleJson = caseRoleJsonArray.getJSONObject(i);

                            JSONObject jsonObj = new JSONObject();
                            jsonObj.put("Name", caseRoleJson.getString("P"));
                            jsonObj.put("KeyNo", caseRoleJson.getString("N"));
                            jsonObj.put("Role", caseRoleJson.getString("R"));
                            jsonObj.put("Org", caseRoleJson.getInteger("O"));

                            if (caseRoleJson.getString("R").equals("原告")) {
                                prosecutorArray.add(jsonObj);
                            } else if (caseRoleJson.getString("R").equals("被告")) {
                                defendantArray.add(jsonObj);
                            }
                        }
                        jsonObject.put("Prosecutor", prosecutorArray);
                        jsonObject.put("Defendant", defendantArray);
                    } else {
                        jsonObject.put("Prosecutor", new JSONArray());
                        jsonObject.put("Defendant", new JSONArray());
                    }

                    // 提取法院公告中的案由信息
                    if (type == 2) {
                        String caseReason = json.getString("casereason");
                        if (StringUtils.isNotBlank(caseReason)) {
                            jsonObject.put("CaseReason", caseReason);
                        }
                    }

                    // 法院提取
                    String court = json.getString("executegov");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }
                } else if (type == 3 || type == 4) {
                    if (jsonObject.getJSONArray("Defendant") == null) {
                        jsonObject.put("Defendant", new JSONArray());
                    }

                    if (jsonObject.getJSONArray("Prosecutor") == null) {
                        jsonObject.put("Prosecutor", new JSONArray());
                    }

                    String caseRole = CommonV2Util.getCaseRoleFromLianOrKtggList(Arrays.asList(json.toJSONString()), 1);
                    JSONArray caseRoleJsonArray = JSONArray.parseArray(caseRole);
                    if (caseRoleJsonArray != null && caseRoleJsonArray.size() > 0) {
                        JSONArray prosecutorArray = new JSONArray();
                        JSONArray defendantArray = new JSONArray();
                        for (int i = 0; i < caseRoleJsonArray.size(); i++) {
                            JSONObject caseRoleJson = caseRoleJsonArray.getJSONObject(i);

                            JSONObject jsonObj = new JSONObject();
                            jsonObj.put("Name", caseRoleJson.getString("P"));
                            jsonObj.put("KeyNo", caseRoleJson.getString("N"));
                            jsonObj.put("Role", caseRoleJson.getString("R"));
                            jsonObj.put("Org", caseRoleJson.getInteger("O"));

                            if (caseRoleJson.getString("R").equals("原告")) {
                                prosecutorArray.add(jsonObj);
                            } else if (caseRoleJson.getString("R").equals("被告")) {
                                defendantArray.add(jsonObj);
                            }
                        }
                        jsonObject.put("Prosecutor", prosecutorArray);
                        jsonObject.put("Defendant", defendantArray);
                    } else {
                        jsonObject.put("Prosecutor", new JSONArray());
                        jsonObject.put("Defendant", new JSONArray());
                    }

                    // 法院提取
                    String court = json.getString("courtname");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }
                    String anotherCourt = json.getString("court");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", anotherCourt);
                    }
                } else if (type == 5) { // 从裁判文书信息中提取案由 / 当事人（双方）/ 执行法院
                    String trialRound = json.getString("trialround");
                    if (StringUtils.isNotBlank(trialRound)) {
                        jsonObject.put("TrialRound", trialRound);
                    }

                    String caseReason = json.getString("casereason");
                    if (StringUtils.isNotBlank(caseReason)) {
                        jsonObject.put("CaseReason", caseReason);
                    }

                    JSONArray prosecutorArray = CommonV2Util.getLitigantJSONArray(json.getString("prosecutor"), json.getString("caserole"));
                    if (prosecutorArray != null && prosecutorArray.size() > 0) {
                        jsonObject.put("Prosecutor", prosecutorArray);
                    }

                    JSONArray defendantArray = CommonV2Util.getLitigantJSONArray(json.getString("defendant"), json.getString("caserole"));
                    if (defendantArray != null && defendantArray.size() > 0) {
                        jsonObject.put("Defendant", defendantArray);
                    }

                    String court = json.getString("court");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }

                    JSONObject protestorganJsonObj = JSONObject.parseObject(json.getString("protestorgan"));
                    if (protestorganJsonObj != null && protestorganJsonObj.containsKey("name")) {
                        jsonObject.put("Procuratorate", protestorganJsonObj.getString("name"));
                    }
                } else if (type == 6) {
                    jsonObject.put("TrialRound", "破产");

                    String court = json.getString("courtname");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }

                    JSONArray applicantJsonArray = CommonV2Util.getLitigantJSONArrayFromPccz(
                            json.getString("applicantnameandkeyno"), "申请人");
                    jsonObject.put("Prosecutor", applicantJsonArray);

                    JSONArray respondentJsonArray = CommonV2Util.getLitigantJSONArrayFromPccz(
                            json.getString("respondentnameandkeyno"), "被申请人");
                    jsonObject.put("Defendant", respondentJsonArray);
                }
                anNoMap.put(anNo, jsonObject);
            }
        }
    }

    public static JSONObject editItemJson(JSONObject jsonObject, int type, Map<Long, String> dateNodeMap, String anNo){
        JSONObject result = new JSONObject();

        String trialRound = new ExtractCaseTrialRoundUDF().evaluate(anNo);
        // 编辑字段
        result.put("Id", jsonObject.getString("id"));
        result.put("IsValid", jsonObject.getInteger("isvalid"));

        if (type == 1) {        // 立案信息（lianList）
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = CommonV2Util.parseDateToTimeStamp(jsonObject.getString("punishdate"));
            result.put("LianDate", publishDate);
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "立案日期");
            }
        } else if (type == 2) { // 开庭公告（ktggList）
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long openDate = CommonV2Util.parseDateToTimeStamp(jsonObject.getString("liandate"));
            result.put("OpenDate", openDate);
            if (openDate != -1) {
                dateNodeMap.put(openDate, trialRound + "|" + "开庭时间");
            }
        } else if (type == 3) { // 送达公告（sdggList）
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = CommonV2Util.parseDateToTimeStamp(jsonObject.getString("publishdate"));
            result.put("PublishDate", publishDate);
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "送达公告发布日期");
            }
        } else if (type == 4) { // 法院公告（fyggList）
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("Category", jsonObject.getString("category"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = CommonV2Util.parseDateToTimeStamp(jsonObject.getString("publishdate"));
            result.put("PublishDate", publishDate);
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "法院公告刊登日期");
            }
        } else if (type == 5) { // 裁判文书（caseList）
            result.put("Id", jsonObject.getString("id"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));
            Long judgeDate = CommonV2Util.parseDateToTimeStamp(jsonObject.getString("judgedate"));
            result.put("JudgeDate", judgeDate);

            String docType = jsonObject.getString("doctype");
            if (StringUtils.isNotBlank(docType)) {
                if (docType.equals("ver")) {
                    result.put("DocType", "行政判决日期");
                    dateNodeMap.put(judgeDate, trialRound + "|" + "判决日期");
                    result.put("ResultType", "判决结果");
                } else {
                    result.put("DocType", "行政裁定日期");
                    dateNodeMap.put(judgeDate, trialRound + "|" + "裁定日期");
                    result.put("ResultType", "裁定结果");
                }
                result.put("Result", jsonObject.getString("judgeresult") == null ? "" : jsonObject.getString("judgeresult"));
            }
            result.put("Amt", jsonObject.getString("amountinvolved") == null ? "" : jsonObject.getString("amountinvolved"));
        } else if (type == 6) { // 破产重整(pcczList)
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("applicantnameandkeyno"));

            String category = "破产";
            String caseType = jsonObject.getString("casetype").trim();
            if (StringUtils.isNotBlank(caseType) && !caseType.equals("案件")) {
                category = caseType.replace("案件", "");
            }
            result.put("Category", category);
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = jsonObject.getLong("riskdate");
            result.put("PublishDate", publishDate != null ? publishDate : -1L);
            if (publishDate != null) {
                dateNodeMap.put(publishDate, "破产" + "|" + category + "公开日期");
            }
        }

        return result;
    }

    /**
     * 提取案件角色信息
     */
    private static String getCaseRoleInfo(List<String> caseList, List<String> lianList, List<String> ktggList, List<String> fyggList, List<String> sdggList, String provinceCode) {
        /**
         * 提取规则
         * 1，如果有裁判文书，取裁判文书中的当事人信息 - caserole
         * 2，如果裁判文书缺失，取开庭公告中的当事人信息 - prosecutorlistos / defendantlistos
         * 3，如果1、2都缺失，取立案信息中的当事人信息 - prosecutorlistos / defendantlistos
         * 4，如果1、2、3都缺失，取破产公告中的申请人和被申请人信息 - respondentnameandkeyno / applicantnameandkeyno
         */
        String caseRole = CommonV2Util.getCaseRoleFromCaseList(caseList, provinceCode);
        if (caseRole.equals(new JSONArray().toJSONString())) {
            caseRole = CommonV2Util.getCaseRoleFromLianOrKtggList(ktggList, 2);
        }
        if (caseRole.equals(new JSONArray().toJSONString())) {
            caseRole = CommonV2Util.getCaseRoleFromLianOrKtggList(lianList, 1);
        }
        if (caseRole.equals(new JSONArray().toJSONString())) {
            caseRole = CommonV2Util.getCaseRoleFromLianOrKtggList(sdggList, 1);
        }
        if (caseRole.equals(new JSONArray().toJSONString())) {
            caseRole = CommonV2Util.getCaseRoleFromLianOrKtggList(fyggList, 1);
        }
        return caseRole;
    }

    public static void main(String[] args) {

        List<String> lianList = new ArrayList<>();

        List<String> ktggList = new ArrayList<>();
        //ktggList.add("{\"id\":\"635aa31fd800f7a7a33ea8144827b1835\",\"liandate\":\"2020-04-28T09:04:00+08:00\",\"casereason\":\"\",\"executegov\":\"温州市中级人民法院\",\"anno\":\"（2020）浙03行初65号\",\"province\":\"ZJ\",\"companynames\":\"应光合,林兰花,陈玉林,叶进平,叶文瑞,李松文,应光华,周希洪,叶加升,陈培,郑元奇,陈秀明,陈秀和,朱胜恩,应池娒,叶进光,叶林平,应池明,应作强,应传生,应建益,应崇林,胡爱兰,应长和,应超俊,应长兴,郑祥法,管小兰,陈崇良,谢婉君,应爱妹...\",\"isvalid\":\"1\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"应光合\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"林兰花\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"陈玉林\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"叶进平\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"叶文瑞\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"李松文\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"应光华\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"周希洪\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"叶加升\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"陈培\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"郑元奇\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"陈秀明\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"陈秀和\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"朱胜恩\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"应池娒\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"叶进光\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"叶林平\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"应池明\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"应作强\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"应传生\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"应建益\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"应崇林\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"胡爱兰\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"应长和\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"应超俊\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"应长兴\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"郑祥法\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"管小兰\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"陈崇良\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"谢婉君\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"应爱妹...\\\"}]\",\"prosecutorlistos\":\"[{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"应光合\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"林兰花\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"陈玉林\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"叶进平\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"叶文瑞\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"李松文\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"应光华\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"周希洪\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"叶加升\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"陈培\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"郑元奇\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"陈秀明\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"陈秀和\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"朱胜恩\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"应池娒\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"叶进光\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"叶林平\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"应池明\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"应作强\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"应传生\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"应建益\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"应崇林\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"胡爱兰\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"应长和\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"应超俊\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"应长兴\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"郑祥法\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"管小兰\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"陈崇良\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"谢婉君\\\"},{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"应爱妹...\\\"}]\",\"defendantlistos\":\"[]\"} ");

        //ktggList.add("{\"id\":\"9f195a051220b305dbad589ae41e66b95\",\"liandate\":\"2020-02-03T09:00:00+08:00\",\"casereason\":\"其他\",\"executegov\":\"北京市高级人民法院\",\"anno\":\"（2019）京行再12号\",\"province\":\"BJ\",\"companynames\":\"国家工商行政管理总局商标评审委员会\",\"isvalid\":\"1\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"国家工商行政管理总局商标评审委员会\\\"}]\",\"prosecutorlistos\":\"[]\",\"defendantlistos\":\"[{\\\"keyno\\\":\\\"\\\",\\\"Org\\\":-1,\\\"name\\\":\\\"国家工商行政管理总局商标评审委员会\\\"}]\"}");

        List<String> sdggList= new ArrayList<>();

        List<String> fyggList = new ArrayList<>();

        List<String> caseList = new ArrayList<>();
        caseList.add("{\"id\":\"2bc78ee3ca243261a0d11ae69bd3316a0\",\"defendant\":\"日照市岚山区和平岭固废处置有限公司,3b1a9d146efc351ea9463c03eea2136e\",\"prosecutor\":\"日照市生态环境局,gec24643f652223b9a845f25a450328c\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"gec24643f652223b9a845f25a450328c\\\",\\\"Org\\\":4,\\\"Name\\\":\\\"日照市生态环境局\\\"},{\\\"KeyNo\\\":\\\"3b1a9d146efc351ea9463c03eea2136e\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"日照市岚山区和平岭固废处置有限公司\\\"}]\",\"caseno\":\"（2019）鲁1103行审29号\",\"submitdate\":\"2019-11-06T00:00:00+08:00\",\"judgedate\":\"2019-09-19T00:00:00+08:00\",\"courtdate\":\"2019-11-06T00:00:00+08:00\",\"casereason\":\"其它类型纠纷\",\"isvalid\":\"1\",\"trialround\":\"非诉行政行为申请执行审查\",\"court\":\"山东省日照市岚山区人民法院\",\"companynames\":\"gec24643f652223b9a845f25a450328c,3b1a9d146efc351ea9463c03eea2136e,日照市生态环境局,日照市岚山区和平岭固废处置有限公司\",\"caserole\":\"[{\\\"P\\\":\\\"日照市生态环境局\\\",\\\"R\\\":\\\"申请人\\\",\\\"N\\\":\\\"gec24643f652223b9a845f25a450328c\\\",\\\"O\\\":4},{\\\"P\\\":\\\"日照市岚山区和平岭固废处置有限公司\\\",\\\"R\\\":\\\"被申请人\\\",\\\"N\\\":\\\"3b1a9d146efc351ea9463c03eea2136e\\\",\\\"O\\\":0}]\",\"doctype\":\"adj\",\"protestorgan\":\"\",\"province\":\"SD\",\"amountinvolved\":\"\",\"judgeresult\":\"驳回申请人日照市生态环境局的执行申请。\"} ");

        String output = new GetDetailInfoXingzhengV2UDF().evaluate(lianList, ktggList, sdggList, fyggList, caseList);
        System.out.println(output);

    }
}
