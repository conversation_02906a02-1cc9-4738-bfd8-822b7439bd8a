package com.qcc.udf.temp;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 *  根据案号，清洗出detailnfo信息
 * @Auther: wuql
 * @Date: 2020/11/17 10:00
 * @Description:
 */
public class LcenseGroupIdUDF extends UDF {


    public String evaluate(String keyNo,int source,String companyName,String documentNo,String permissionNo,String permissionContent,String startDate)  {
        String groupContent ="";
        String compFlag =keyNo;
        if (StringUtils.isBlank(compFlag)){
            compFlag = companyName;
        }
        if (StringUtils.isBlank(compFlag)){
            return "";
        }

        String docNoFlag = documentNo;
        if(StringUtils.isBlank(docNoFlag)){
            if (source==1){
                docNoFlag =permissionNo;
            }
        }

        if(StringUtils.isNotBlank(docNoFlag)){
            groupContent = StringUtils.join(compFlag,docNoFlag);
        }else {
            groupContent = StringUtils.join(compFlag,permissionContent,startDate);
        }

        groupContent = CommonUtil.cleanPunct(groupContent).toLowerCase();
        //根据keyno_comp,document_no,start_date,permission_content生成的md5值，作为同条数据的标识
        return MD5Util.encode(groupContent);
    }



    public static void main(String[] args) {
        String keyNo="98169af42b334ed9888eda394eac5a70";
        int source=2;
        String companyName="韩城市鑫美日化商行";
        String documentNo="JY16105810071623";
        String permissionNo="1a1f28138590f5da3c4bd396bc4d1c97";
        String permissionContent="特殊食品销售,保健食品";
        String startDate="20171227";
        LcenseGroupIdUDF lcenseGroupIdUDF = new LcenseGroupIdUDF();
        String evaluate = lcenseGroupIdUDF.evaluate(keyNo, source, companyName, documentNo, permissionNo, permissionContent, startDate);
        System.out.println(evaluate);

    }
}
