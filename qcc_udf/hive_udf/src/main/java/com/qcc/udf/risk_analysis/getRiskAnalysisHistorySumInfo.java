package com.qcc.udf.risk_analysis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.qcc.udf.court_notice.anUtils.MD5Util;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class getRiskAnalysisHistorySumInfo extends UDF {

	//限制详情count数
	private static final Integer LIMIT_COUNT = 100;
	//分隔符
	private static final String SPLIT_KEY = ",";

	public String evaluate(String keyNo, List<String> pcczinfo, List<String> xginfo, List<String> sxinfo, List<String> zxinfo,
	                       List<String> zbinfo, List<String> cpwsinfo, List<String> lianinfo, List<String> ktgginfo, List<String> fygginfo,
	                       List<String> sdgginfo, List<String> gqdjinfo,
						   List<String> jyycinfo, List<String> gqczinfo, List<String> dcdyinfo, List<String> tddyinfo,
						   List<String> xzcfinfo, List<String> hbcfinfo, List<String> sqtjinfo, List<String> zcczinfo) {
		// 全量数据
		JSONArray array = new JSONArray();

		// 破产重整
		editPcczInfo(keyNo, pcczinfo, array);
		// 限高
		editXgInfo(keyNo, xginfo, array);
		// 失信
		editSxInfo(keyNo, sxinfo, array);
		// 被执行
		editZxInfo(keyNo, zxinfo, array);
		// 获取终本信息
		editZbInfo(keyNo, zbinfo, array);
		// 裁判文书
		editCpwsInfo(keyNo, cpwsinfo, array);
		// 立案
		editLianInfo(keyNo, lianinfo, array);
		// 开庭公告
		editKtggInfo(keyNo, ktgginfo, array);
		// 法院公告
		editFyggInfo(keyNo, fygginfo, array);
		// 送达公告
		editSdggInfo(keyNo, sdgginfo, array);
		// 股权冻结
		editGqdjInfo(keyNo, gqdjinfo, array);
		// 经营异常
		editJyycInfo(keyNo, jyycinfo, array);
		// 股权出质
		editGqczInfo(keyNo, gqczinfo, array);
		// 动产抵押
		editDcdyInfo(keyNo, dcdyinfo, array);
		// 土地抵押
		editTddyInfo(keyNo, tddyinfo, array);
		// 行政处罚
		editXzcfInfo(keyNo, xzcfinfo, array);
		// 环保处罚
		editHbcfInfo(keyNo, hbcfinfo, array);
		// 诉前调解
		editSqtjInfo(keyNo, sqtjinfo, array);

		//资产出质
		editZccZInfo(keyNo,zcczinfo,array);

		// 全维度统计
		editAllInfo(keyNo, array);
		return JSON.toJSONString(array, SerializerFeature.DisableCircularReferenceDetect);
	}

	/**
	 * 编辑全维度cnt信息
	 *
	 * @param keyNo 公司keyno
	 * @param array 基础数据信息
	 */
	public void editAllInfo(String keyNo, JSONArray array) {
		JSONObject type0Json = new JSONObject();
		type0Json.put("KeyNo", keyNo);
		type0Json.put("Type", WeiduEnum.ALL.getCode());
		type0Json.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.ALL.getCode().toString() + "|0")));

		Set<Integer> wdSet = new LinkedHashSet<>();
		for (int i = 1; i <= 28; i++) {
			wdSet.add(i);
		}

		JSONArray detailJson = new JSONArray();
		for (Object o : array) {
			JSONObject jsonObject = (JSONObject) o;
			JSONObject item = new JSONObject();

			// 破产重整
			if (WeiduEnum.PCCZ.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.PCCZ.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 限高
			if (WeiduEnum.XG.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.XG.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 失信
			if (WeiduEnum.SX.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.SX.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
				item.put("B", 0);
				JSONArray array1 = jsonObject.getJSONObject("DetailInfo").getJSONArray("B");
				if (array1 != null && !array1.isEmpty()) {
					Iterator<Object> it = array1.iterator();
					while (it.hasNext()) {
						JSONObject jsonObject1 = (JSONObject) it.next();
						if ("1".equals(jsonObject1.getString("A"))) {
							item.put("B", jsonObject1.getInteger("B"));
						}
					}
				}
			}

			// 被执行人
			if (WeiduEnum.ZX.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.ZX.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
				item.put("B", jsonObject.getJSONObject("DetailInfo").getBigDecimal("B"));
			}

			// 终本
			if (WeiduEnum.ZB.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.ZB.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
				item.put("B", jsonObject.getJSONObject("DetailInfo").getBigDecimal("C"));
			}

			// 裁判文书
			if (WeiduEnum.CPWS.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.CPWS.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
				item.put("B", jsonObject.getJSONObject("DetailInfo").getBigDecimal("B"));
			}

			// 立案
			if (WeiduEnum.LIAN.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.LIAN.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 开庭公告
			if (WeiduEnum.KTGG.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.KTGG.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 法院公告
			if (WeiduEnum.FYGG.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.FYGG.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 送达公告
			if (WeiduEnum.SDGG.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.SDGG.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 股权冻结
			if (WeiduEnum.GQDJ.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.GQDJ.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 经营异常
			if (WeiduEnum.JYYC.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.JYYC.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 股权出质
			if (WeiduEnum.GQCZ.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.GQCZ.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 动产抵押
			if (WeiduEnum.DCDY.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.DCDY.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 土地抵押
			if (WeiduEnum.TDDY.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.TDDY.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 行政处罚
			if (WeiduEnum.XZCF.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.XZCF.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
				item.put("B", jsonObject.getJSONObject("DetailInfo").getString("B"));
			}

			// 环保处罚
			if (WeiduEnum.HBCF.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.HBCF.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
				item.put("B", jsonObject.getJSONObject("DetailInfo").getString("D"));
			}

			//诉前调解
			if (WeiduEnum.SQTJ.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.SQTJ.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			//知产出质
			if (WeiduEnum.ZSCQCZ.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.ZSCQCZ.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
				item.put("B", jsonObject.getJSONObject("DetailInfo").getString("B"));
			}

			detailJson.add(item);
			wdSet.remove(jsonObject.getInteger("Type"));
		}

		for (Integer i : wdSet) {
			JSONObject item = new JSONObject();
			item.put("Type", i);
			item.put("A", 0);
			detailJson.add(item);
		}

		type0Json.put("DetailInfo", detailJson);

		array.add(type0Json);
	}

	/**
	 * 编辑破产重整信息
	 *
	 * @param keyNo    公司keynoSX
	 * @param pcczinfo 破产重整信息
	 * @param array    列表数据
	 */
	public void editPcczInfo(String keyNo, List<String> pcczinfo, JSONArray array) {
		if (pcczinfo != null && pcczinfo.size() > 0) {
			for (String str : pcczinfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", jsonObject.getInteger("def_sum"));
					cntInfo.put("C", jsonObject.getInteger("self_sum"));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.PCCZ.getCode().toString()) + "|0"));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.PCCZ.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
	}

	/**
	 * 编辑限高信息
	 *
	 * @param keyNo  公司keyno
	 * @param xginfo 限高
	 * @param array  列表数据
	 */
	public void editXgInfo(String keyNo, List<String> xginfo, JSONArray array) {
		if (xginfo != null && xginfo.size() > 0) {
			for (String str : xginfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.XG.getCode().toString()) + "|0"));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.XG.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
	}

	/**
	 * 编辑失信信息
	 *
	 * @param keyNo  公司keyno
	 * @param sxinfo 失信
	 * @param array  列表数据
	 */
	public void editSxInfo(String keyNo, List<String> sxinfo, JSONArray array) {
		if (sxinfo != null && sxinfo.size() > 0) {
			for (String str : sxinfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					String courtLevel = jsonObject.getString("exestatus");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(courtLevel)) {
							List<String> list = JSON.parseArray(courtLevel, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", sub.split("#FGF#")[0]);
								jsonObject1.put("B", Integer.valueOf(sub.split("#FGF#")[1]));

								array1.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("B", array1);

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.SX.getCode().toString()) + "|0"));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.SX.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    总金额
		// C    法院层级
		// D    案由
	}

	/**
	 * 编辑被执行人信息
	 *
	 * @param keyNo  公司keyno
	 * @param zxinfo 被执行人信息
	 * @param array  列表数据
	 */
	public void editZxInfo(String keyNo, List<String> zxinfo, JSONArray array) {
		if (zxinfo != null && zxinfo.size() > 0) {
			for (String str : zxinfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", new BigDecimal(jsonObject.getString("biaodi")));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.ZX.getCode().toString()) + "|0"));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.ZX.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    执行标的
	}

	/**
	 * 编辑终本信息
	 *
	 * @param keyNo  公司keyno
	 * @param zbinfo 终本信息
	 * @param array  列表数据
	 */
	public void editZbInfo(String keyNo, List<String> zbinfo, JSONArray array) {
		if (zbinfo != null && zbinfo.size() > 0) {
			for (String str : zbinfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", new BigDecimal(jsonObject.getString("biaodi")));
					cntInfo.put("C", new BigDecimal(jsonObject.getString("failureact")));
					cntInfo.put("D", jsonObject.getString("failurepercent"));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.ZB.getCode().toString()) + "|0"));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.ZB.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    执行标的
		// C    未履行金额
		// D    未履行比例
	}

	/**
	 * 编辑裁判文书信息
	 *
	 * @param keyNo    公司keyno
	 * @param cpwsinfo 裁判文书信息
	 * @param array    列表数据
	 */
	public void editCpwsInfo(String keyNo, List<String> cpwsinfo, JSONArray array) {
		if (cpwsinfo != null && cpwsinfo.size() > 0) {
			for (String str : cpwsinfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", jsonObject.getBigDecimal("money"));

					String courtLevel = jsonObject.getString("courtlevel");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(courtLevel)) {
							List<String> list = JSON.parseArray(courtLevel, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								String[] subStr = sub.split("#FGF#");
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								if (subStr.length > 2) {
									jsonObject1.put("C", limitDetailsCount(subStr[2], SPLIT_KEY));
								} else {
									jsonObject1.put("C", "");
								}
								array1.add(jsonObject1);
							}
						}
					} catch (Exception ex) {
						ex.printStackTrace();
					}
					cntInfo.put("C", array1);
					String courtReason = jsonObject.getString("casereason");
					JSONArray array2 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(courtReason)) {
							List<String> list2 = JSON.parseArray(courtReason, String.class);
							for (String sub : list2) {
								JSONObject jsonObject1 = new JSONObject();
								String[] subStr = sub.split("#FGF#");
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								if (subStr.length > 2) {
									jsonObject1.put("C", limitDetailsCount(subStr[2], SPLIT_KEY));
								} else {
									jsonObject1.put("C", "");
								}
								array2.add(jsonObject1);
							}
						}
					} catch (Exception ex) {
						ex.printStackTrace();
					}
					cntInfo.put("D", array2);


					cntInfo.put("E", 0);
//                    cntInfo.put("F", new BigDecimal(0.00));
					cntInfo.put("F", "0");
					String pro_sum = jsonObject.getString("pro_sum");
					try {
						if (StringUtils.isNotEmpty(pro_sum)) {
							String[] subStr = pro_sum.split("#FGF#");
							cntInfo.put("E", new Integer(subStr[0]));
                            cntInfo.put("F", new BigDecimal(subStr[1]));
							if (subStr.length > 2) {
								cntInfo.put("I", limitDetailsCount(subStr[2], SPLIT_KEY));
							} else {
								cntInfo.put("I", "");
							}
						}
					} catch (Exception ex) {
						ex.printStackTrace();
					}
					cntInfo.put("G", 0);
//                    cntInfo.put("H",new BigDecimal(0.00));
					cntInfo.put("H", "0");
					String def_sum = jsonObject.getString("def_sum");
					try {
						if (StringUtils.isNotEmpty(def_sum)) {
							String[] subStr = def_sum.split("#FGF#");
							cntInfo.put("G", new Integer(subStr[0]));
                            cntInfo.put("H", new BigDecimal(subStr[1]));
							if (subStr.length > 2) {
								cntInfo.put("J", limitDetailsCount(subStr[2], SPLIT_KEY));
							} else {
								cntInfo.put("J", "");
							}
						}
					} catch (Exception ex) {
						ex.printStackTrace();
					}

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.CPWS.getCode().toString()) + "|0"));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.CPWS.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    总金额
		// C    法院层级
		// D    案由
	}

	/**
	 * 编辑立案信息
	 *
	 * @param keyNo    公司keyno
	 * @param lianInfo 立案信息
	 * @param array    列表数据
	 */
	public void editLianInfo(String keyNo, List<String> lianInfo, JSONArray array) {
		if (lianInfo != null && lianInfo.size() > 0) {
			for (String str : lianInfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					String courtLevel = jsonObject.getString("court");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(courtLevel)) {
							List<String> list = JSON.parseArray(courtLevel, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								String[] subStr =  sub.split("#FGF#");
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								if (subStr.length > 2) {
									jsonObject1.put("C", limitDetailsCount(subStr[2], SPLIT_KEY));
								} else {
									jsonObject1.put("C", "");
								}
								array1.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("B", array1);
					cntInfo.put("C",jsonObject.getInteger("ygcnt"));
					cntInfo.put("D", jsonObject.getInteger("bgcnt"));
					cntInfo.put("E", limitDetailsCount(jsonObject.getString("ygids"), SPLIT_KEY));
					cntInfo.put("F", limitDetailsCount(jsonObject.getString("bgids"), SPLIT_KEY));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.LIAN.getCode().toString()) + "|0"));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.LIAN.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    法院层级
	}

	/**
	 * 编辑开庭公告
	 *
	 * @param keyNo    公司keyno
	 * @param ktggInfo 开庭公告
	 * @param array    列表数据
	 */
	public void editKtggInfo(String keyNo, List<String> ktggInfo, JSONArray array) {
		if (ktggInfo != null && ktggInfo.size() > 0) {
			for (String str : ktggInfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					String courtLevel = jsonObject.getString("casereason");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(courtLevel)) {
							List<String> list = JSON.parseArray(courtLevel, String.class);
							for (String sub : list) {
								String[] subStr = sub.split("#FGF#");
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								if (subStr.length > 2) {
									jsonObject1.put("C", limitDetailsCount(subStr[2], SPLIT_KEY));
								} else {
									jsonObject1.put("C", "");
								}
								array1.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("B", array1);
					cntInfo.put("C", jsonObject.getInteger("pro_sum"));
					cntInfo.put("D", jsonObject.getInteger("def_sum"));
					cntInfo.put("E", limitDetailsCount(jsonObject.getString("ygids"), SPLIT_KEY));
					cntInfo.put("F", limitDetailsCount(jsonObject.getString("bgids"), SPLIT_KEY));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.KTGG.getCode().toString() + "|0")));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.KTGG.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    案由
	}

	/**
	 * 编辑法院公告信息
	 *
	 * @param keyNo    公司keyno
	 * @param fyggInfo 法院公告
	 * @param array    列表数据
	 */
	public void editFyggInfo(String keyNo, List<String> fyggInfo, JSONArray array) {
		if (fyggInfo != null && fyggInfo.size() > 0) {
			for (String str : fyggInfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					String category = jsonObject.getString("category");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(category)) {
							List<String> list = JSON.parseArray(category, String.class);
							for (String sub : list) {
								String[] subStr = sub.split("#FGF#");
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								if (subStr.length > 2) {
									jsonObject1.put("C", limitDetailsCount(subStr[2], SPLIT_KEY));
								} else {
									jsonObject1.put("C", "");
								}

								array1.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("C", array1);

					String casereason = jsonObject.getString("casereason");
					JSONArray array2 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(casereason)) {
							List<String> list2 = JSON.parseArray(casereason, String.class);
							for (String sub : list2) {
								String[] subStr = sub.split("#FGF#");
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								if (subStr.length > 2) {
									jsonObject1.put("C", limitDetailsCount(subStr[2], SPLIT_KEY));
								} else {
									jsonObject1.put("C", "");
								}
								array2.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("B", array2);

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.FYGG.getCode().toString() + "|0")));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.FYGG.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    案由
		// C    公告类型
	}

	/**
	 * 编辑送达公告信息
	 *
	 * @param keyNo    公司keyno
	 * @param sdggInfo 送达公告
	 * @param array    列表数据
	 */
	public void editSdggInfo(String keyNo, List<String> sdggInfo, JSONArray array) {
		if (sdggInfo != null && sdggInfo.size() > 0) {
			for (String str : sdggInfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					String category = jsonObject.getString("category");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(category)) {
							List<String> list = JSON.parseArray(category, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								String[] subStr = sub.split("#FGF#");
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								if (subStr.length > 2) {
									jsonObject1.put("C", limitDetailsCount(subStr[2], SPLIT_KEY));
								} else {
									jsonObject1.put("C", "");
								}
								array1.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("C", array1);

					String casereason = jsonObject.getString("casereason");
					JSONArray array2 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(casereason)) {
							List<String> list2 = JSON.parseArray(casereason, String.class);
							for (String sub : list2) {
								JSONObject jsonObject1 = new JSONObject();
								String[] subStr = sub.split("#FGF#");
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								if (subStr.length > 2) {
									jsonObject1.put("C", limitDetailsCount(subStr[2], SPLIT_KEY));
								} else {
									jsonObject1.put("C", "");
								}
								array2.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("B", array2);

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.SDGG.getCode().toString() + "|0")));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.SDGG.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    案由
		// C    公告类型
	}


	/**
	 * 编辑股权冻结信息
	 *
	 * @param keyNo    公司keyno
	 * @param infoList 股权冻结
	 * @param array    列表数据
	 */
	public void editGqdjInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (infoList != null && infoList.size() > 0) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", jsonObject.getBigDecimal("amount"));
					cntInfo.put("C", jsonObject.getInteger("zxcnt"));
					cntInfo.put("D", jsonObject.getBigDecimal("zxamount"));
					cntInfo.put("E", jsonObject.getInteger("zxcasecnt"));
					cntInfo.put("F", jsonObject.getInteger("bdcnt"));
					cntInfo.put("G", jsonObject.getBigDecimal("bdamount"));
					cntInfo.put("H", jsonObject.getInteger("bdcasecnt"));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.GQDJ.getCode().toString() + "|0")));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.GQDJ.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
	}

	/**
	 * 编辑经营异常信息
	 *
	 * @param keyNo    公司keyno
	 * @param infoList 经营异常
	 * @param array    列表数据
	 */
	public void editJyycInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (infoList != null && infoList.size() > 0) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", jsonObject.getInteger("removecnt"));
					cntInfo.put("C", jsonObject.getInteger("addcnt"));

					String category = jsonObject.getString("court");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(category)) {
							String[] categoryStr = category.split("#GFG#");
							for (String sub : categoryStr) {
								JSONObject jsonObject1 = new JSONObject();
								String[] subStr = sub.split("#FGF#");
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								//json处理
								JSONArray jsonArrayResult = new JSONArray();
								if (subStr.length > 2) {
									JSONArray jsonArray = JSON.parseArray(subStr[2]);
									int sum = 0;
									for(int i = 0; i < jsonArray.size(); i ++) {
										if (sum >= LIMIT_COUNT) {
											break;
										}
										JSONObject details = jsonArray.getJSONObject(i);
										parseDateFromJson(details, "PublishDate");
										parseDateFromJson(details, "AddDate");
										parseDateFromJson(details, "RemoveDate");
										details.put("AnnualReportYear", details.getOrDefault("AnnualReportYear", "") == null? "" : details.getOrDefault("AnnualReportYear", "").toString());
										jsonArrayResult.add(details);
										sum ++;
									}
								}
								jsonObject1.put("C", jsonArrayResult.toJSONString());
								array1.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("D", array1);

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.JYYC.getCode().toString() + "|0")));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.JYYC.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    移除数
		// C    移入数
		// D    原因
	}

	/**
	 * 编辑股权出质信息
	 *
	 * @param keyNo    公司keyno
	 * @param infoList 股权出质
	 * @param array    列表数据
	 */
	public void editGqczInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (infoList != null && infoList.size() > 0) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", jsonObject.getBigDecimal("amount"));
					cntInfo.put("C", jsonObject.getInteger("czrcnt"));
					cntInfo.put("D", jsonObject.getBigDecimal("czramount"));
					cntInfo.put("E", jsonObject.getInteger("zqrcnt"));
					cntInfo.put("F", jsonObject.getBigDecimal("zqramount"));
					cntInfo.put("G", jsonObject.getInteger("bdcnt"));
					cntInfo.put("H", jsonObject.getBigDecimal("bdamount"));

					cntInfo.put("I", limitDetailsCount(jsonObject.getString("ids"), ","));
					cntInfo.put("J", limitDetailsCount(jsonObject.getString("czrids"), ","));
					cntInfo.put("K", limitDetailsCount(jsonObject.getString("zqrids"), ","));
					cntInfo.put("L", limitDetailsCount(jsonObject.getString("bdids"), ","));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.GQCZ.getCode().toString() + "|0")));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.GQCZ.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
	}

	/**
	 * 编辑动产抵押信息
	 *
	 * @param keyNo    公司keyno
	 * @param infoList 动产抵押
	 * @param array    列表数据
	 */
	public void editDcdyInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (infoList != null && infoList.size() > 0) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", jsonObject.getBigDecimal("amount"));
					cntInfo.put("C", jsonObject.getInteger("zwrcnt"));
					cntInfo.put("D", jsonObject.getBigDecimal("zwramount"));
					cntInfo.put("E", jsonObject.getInteger("dyqrcnt"));
					cntInfo.put("F", jsonObject.getBigDecimal("dyqramount"));
					cntInfo.put("G", jsonObject.getString("ids"));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.DCDY.getCode().toString() + "|0")));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.DCDY.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
	}

	/**
	 * 编辑土地抵押信息
	 *
	 * @param keyNo    公司keyno
	 * @param infoList 土地抵押
	 * @param array    列表数据
	 */
	public void editTddyInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (infoList != null && infoList.size() > 0) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", jsonObject.getInteger("mortgagor_count"));
					cntInfo.put("C", jsonObject.getString("mortgagor_count_percent"));
					cntInfo.put("D", jsonObject.getBigDecimal("mortgagor_area"));
					cntInfo.put("E", jsonObject.getBigDecimal("mortgagor_price"));
					cntInfo.put("F", jsonObject.getInteger("mortgage_count"));
					cntInfo.put("G", jsonObject.getString("mortgage_count_percent"));
					cntInfo.put("H", jsonObject.getBigDecimal("mortgage_area"));
					cntInfo.put("I", jsonObject.getBigDecimal("mortgage_price"));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.TDDY.getCode().toString() + "|0")));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.TDDY.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
	}

	/**
	 * 编辑行政处罚信息
	 *
	 * @param keyNo    公司keyno
	 * @param infoList 行政处罚
	 * @param array    列表数据
	 */
	public void editXzcfInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (infoList != null && infoList.size() > 0) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", jsonObject.getBigDecimal("money"));

					String category = jsonObject.getString("punish_reason");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(category)) {
							List<String> list = JSON.parseArray(category, String.class);
							for (String sub : list) {
								String[] subStr = sub.split("#FGF#");
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								if (subStr.length > 2) {
									jsonObject1.put("C", limitDetailsCount(subStr[2], SPLIT_KEY));
								} else {
									jsonObject1.put("C", "");
								}

								array1.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("C", array1);

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.XZCF.getCode().toString() + "|0")));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.XZCF.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
	}

	/**
	 * 编辑环保处罚信息
	 *
	 * @param keyNo    公司keyno
	 * @param hbcfInfo 环保处罚
	 * @param array    列表数据
	 */
	public void editHbcfInfo(String keyNo, List<String> hbcfInfo, JSONArray array) {
		if (hbcfInfo != null && hbcfInfo.size() > 0) {
			for (String str : hbcfInfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					String category = jsonObject.getString("illegaltype");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(category)) {
							List<String> list = JSON.parseArray(category, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								String[] subStr = sub.split("#FGF#");
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								if (subStr.length > 2) {
									jsonObject1.put("C", limitDetailsCount(subStr[2], SPLIT_KEY));
								} else {
									jsonObject1.put("C", "");
								}

								array1.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("C", array1);

					String casereason = jsonObject.getString("implementaion");
					JSONArray array2 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(casereason)) {
							List<String> list2 = JSON.parseArray(casereason, String.class);
							for (String sub : list2) {
								JSONObject jsonObject1 = new JSONObject();
								String[] subStr = sub.split("#FGF#");
								jsonObject1.put("A", subStr[0]);
								jsonObject1.put("B", new Integer(subStr[1]));
								if (subStr.length > 2) {
									jsonObject1.put("C", limitDetailsCount(subStr[2], SPLIT_KEY));
								} else {
									jsonObject1.put("C", "");
								}

								array2.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("B", array2);
					cntInfo.put("D", jsonObject.getBigDecimal("amt"));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.HBCF.getCode().toString() + "|0")));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.HBCF.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    执行情况
		// C    违法类型
	}



	/**
	 * 知产出质   出质次数 / 出质类型 / 出质年份
	 * @param keyNo
	 * @param zcczinfo
	 * @param array
	 */
	public void editZccZInfo(String keyNo, List<String> zcczinfo, JSONArray array){
		if (zcczinfo != null && zcczinfo.size() > 0) {
			for (String str : zcczinfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject c = new JSONObject();
					c.put("A", jsonObject.getInteger("cznums"));
					c.put("B", jsonObject.getInteger("cztimes"));

					String ctype = jsonObject.getString("type");
					try {
						if (StringUtils.isNotEmpty(ctype)) {
							List<String> list = JSON.parseArray(ctype, String.class);
							for (String sub : list) {
								String type = sub.split("#FGF#")[0];
								if (type.equals("1")) {
									c.put("C", Integer.valueOf(sub.split("#FGF#")[1]));
									c.put("D", sub.split("#FGF#")[2]);
								} else if (type.equals("2")) {
									c.put("E", Integer.valueOf(sub.split("#FGF#")[1]));
									c.put("F", sub.split("#FGF#")[2]);
								}
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}

					String pyear = jsonObject.getString("pyear");
					JSONArray pyeararray = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(pyear)) {
							List<String> list = JSON.parseArray(pyear, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", sub.split("#FGF#")[0]);
								jsonObject1.put("B", Integer.valueOf(sub.split("#FGF#")[1]));
								pyeararray.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					c.put("G", pyeararray);


					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.ZSCQCZ.getCode().toString()).concat("|0")));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.ZSCQCZ.getCode());
					result.put("DetailInfo", c);
					array.add(result);
				}
			}
		}
	}



	/**
	 * Desc: 处理json中的date
	 *
	 * */
	private void parseDateFromJson(JSONObject jsonObject, String keyWords) {
		if (jsonObject.containsKey(keyWords)) {
			String object = jsonObject.getString(keyWords);
			if (StringUtils.isNotEmpty(object)) {
				jsonObject.put(keyWords, object);
			} else {
				jsonObject.put(keyWords, "0");
			}
		} else {
			jsonObject.put(keyWords, "0");
		}
	}

	/**
	 * Desc:处理id限制数
	 *
	 * */
	private String limitDetailsCount(String details, String splitKey) {
		if (StringUtils.isEmpty(details)) {
			return "";
		}
		String[] str = details.split(splitKey);
		List<String> list = new ArrayList<>();
		int sum = 0;
		for(String item : str) {
			if (StringUtils.isEmpty(item)) {
				continue;
			}
			if (sum >= LIMIT_COUNT) {
				break;
			}
			list.add(item);
			sum ++;
		}
		String result = list.stream().collect(Collectors.joining(splitKey));
		return result;
 	}

	/**
	 * Desc:诉前调解 A:总数,B:各年份总数
	 *
	 * */
	public void editSqtjInfo(String keyNo, List<String> infoList, JSONArray array) {
		if (CollectionUtils.isNotEmpty(infoList)) {
			for (String str : infoList) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);
					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					String courtinfos = jsonObject.getString("courtinfos");
					JSONArray jsonArray = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(courtinfos)) {
							List<String> list = JSON.parseArray(courtinfos, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", sub.split("#FGF#")[0]);
								jsonObject1.put("B", Integer.valueOf(sub.split("#FGF#")[1]));
								jsonObject1.put("C", sub.split("#FGF#")[2]);

								jsonArray.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("B", jsonArray);

					String roleinfos = jsonObject.getString("roleinfos");
					try {

						if (StringUtils.isNotEmpty(roleinfos)) {
							List<String> list = JSON.parseArray(roleinfos, String.class);
							for (String sub : list) {
								String type = sub.split("#FGF#")[0];
								if (type.equals("1")) {
									//原告
									cntInfo.put("C", Integer.valueOf(sub.split("#FGF#")[1]));
									cntInfo.put("D", sub.split("#FGF#")[2]);
								} else if (type.equals("2")) {
									//原告
									cntInfo.put("E", Integer.valueOf(sub.split("#FGF#")[1]));
									cntInfo.put("F", sub.split("#FGF#")[2]);
								}
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.SQTJ.getCode().toString()).concat("|0")));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.SQTJ.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
	}

	public static void main(String[] args) {
		getRiskAnalysisHistorySumInfo job = new getRiskAnalysisHistorySumInfo();
		List<String> zbInfoList = new LinkedList<>();
//		zbInfoList.add("{\"keyno\":\"0000121249f7d918e45ffa9d9102ed95\",\"cnt\":1,\"removecnt\":0,\"addcnt\":1,\"court\":\"被列入经营异常名录届满3年仍未履行相关义务#FGF#1#FGF#[{\\\"No\\\":\\\"e6ab52a8951f5d1d562610f87ddd2b0e\\\",\\\"Type\\\":\\\"严重违法失信企业名单\\\",\\\"AddDate\\\":{\\\"$numberLong\\\":\\\"1538928000\\\"},\\\"RemoveReason\\\":\\\"\\\",\\\"RemoveDate\\\":{\\\"$numberLong\\\":\\\"0\\\"},\\\"AddReason\\\":\\\"被列入经营异常名录届满3年仍未履行相关义务\\\",\\\"AddOffice\\\":\\\"天津市市场监督管理委员会\\\",\\\"RemoveOffice\\\":\\\"\\\"}]\"}");
//		zbInfoList.add("{\"keyno\":\"000337f13c4a5bc1fc94ac67c0f737f0\",\"cnt\":1,\"amount\":\"0\",\"czrcnt\":0,\"czramount\":\"0\",\"zqrcnt\":0,\"zqramount\":\"0\",\"bdcnt\":1,\"bdamount\":\"0\",\"ids\":\",8ab1e759a05b7836b0c8cb7999bcd146\",\"czrids\":\"\",\"zqrids\":\"\",\"bdids\":\",8ab1e759a05b7836b0c8cb7999bcd146\"}\n");
		zbInfoList.add("{\"keyno\":\"1223aabb6d71b8c5399d0dd1d7a5473e\",\"cztimes\":1,\"cznums\":103,\"type\":\"[\\\"2#FGF#103#FGF#076ece58affc006d3b4da53dbb974db2,28171c0c7706a4677d654d1f81b029bd,7b41b59ed0c59b72e2b4200e4eee0419,3ba6824ea887f77c2f36e11b5710f6ad,6647971605c709d56830920492689e35,bcdf947eb8e79c92b683601ba16b15d7,3565d1bc9b69121d2c51c9b5147f074c,e174b83f42a6d048e68663543da59deb,386a29dbbf81f74d8841c3665c156c78,2415a28f027b29b017b1ef563b880394,135a674aebb36d1268b38452e6fe8a01,ffd865053b26bc9053de0b2a2121eedb,a0c67199b8c1bdd6334a9cdc1149792c,969af3ec3a465ed1bac01270c5897e2b,c4fe623afb756d5dc4cc593bdc9227a1,8e1f1eb8d91e2869b408ceae67ee26dc,26e7f5223015faa17b3f7cd820e48838,9ef7f234734e551eb2b275ce92bc7ee3,4dd8b05b6c090fc85209de2ed0733370,0ce07057bc95b3c20176ce2b34f2c4c8,d034c7c38500fea0023c2bd87c406c9c,193e026a53f8bcdd65a00aa19916d632,ffd8928ffe11926fe109aba5c1079850,376fad3d432f88e2e65111759ed2b056,aee7018a4db3ed503be8e9bf5ae3cfbb,3d29e0233e3d1b7641f10db5c83c0a6a,f1cbe6e46351072d1764fa39216ad918,86069db4e1642fcd38ae8a2ed63d22b4,1c439a2b039809048daa0edf81894630,ce4202963d0e0493aebc08b9cbaf74f2,b4e3d404329040cbabd422ee131ef283,743aae78d87d7462e10ac9f465445f91,574b048c137d78f91aab81ba81a06c83,bef7b3f5cf1e8830e6480816b8fe6023,51582ca82fedc2849ae65ef1e7de57fa,4db4a1dcd316250368ed0cc556d8e3bc,7a2318dfcbef22609c57f4dfd6c90751,761f37d332afbaf4f83c5bf2e2b62334,f6aed61749377738891635834da3e28e,ed843c088e36d3a7f6f67327ab1a7382,3cea35c20df02e2f583dbc004ade2d99,e2d4a30b67af0f9b491c8a5f5d17c96b,2cf9ae35266de26cbf39c705a8c25140,99774f4c02ae9e8e5d9a49fb4005dafe,2a73d1afb6f182f14fdabd3bc7112466,435c0e7a8fcc258809abf0e2c3890844,616f0505ca166238d75a11d91492042e,90b0c803ebf12268300ab43e730bd2c1,0f1c4d046d4da68b9927af168c6dabcd,302a6c27d42214373d50ebe9385d75c1,be5f2576ac11d50bf13c6d893d248002,67b3055b86a0c84aeb3ef15621dcaf64,e4094881866fb7528e0040a2f2d01e83,1f15b0a4c4f2f3102aec231e71943415,9a24644618d54faaf9791db6322d80b3,da10b700d4247021adf493f316c4bdba,11db1054944a32672fad01ef4c359839,32a5c5c4696b7de143bb6acac85a1f22,145694540633c3a6fa9aa8b3fe53a5f2,1966b1231ad82ff3c7731a28aa3bd313,3ff67adb97056020da88515b5aab5591,30de81434671f87c902742b890322f34,58644452f655688fe81fc9efb1b09e6f,f844907936848d9eee95d8fa96301809,05cc34ee1b5668e440f9bfa2983bc881,26c8edcacfc5cd2a313e9228d000db62,37a623c7cf1720c2834daf87220830f4,371aa26949ec5f41091ac70b4789674f,693d0cc99d1bab8ec45f07ded5851d5d,6df9c9090966800193cc1fd00d2b07f9,0f1a7fc729942bca71c7a8a96651d506,e934d5fa223311298500c35da9a07ed4,4b56a196954137cef4f81997ea4fcdf2,ca895c4133d15a9b11c9f79bb2777457,b7709c4691ba2dd5971e03562663c79a,b1be25b04dc85db2e17dd6febb24c04d,eeaa8f8a360fdcf46ad0d1d8c652bc7d,2492360396725bd0ce2191187ef62f55,0fecd89c4f53a639e609871195650e12,574ed8611b8a06a544047189fe6230ed,9d12a40989454a0500c8573cc0ef385d,a60db204c58fe0db4d9923c92b406937,6e9d2f7105ef9682ff4fc0a5215a6051,a1d84712081a056ddff1b2747c95c6cf,3fe73532c5ae4dcfd150200cb7d32ffe,481b0382846b1f3feb3d9ca6aa046361,f8479209eaadc5308cef3300acd39a6d,ed660af04a4b47ebd9c7d561df21ab2c,4538d44f9536e48297b708d969c34997,c876d03deaf4a376ea00416619e5b649,8e27e5d136900ea27a6411655a5503f0,57cb723d0e135ed5f08a5e4857c62d0f,00b743d012d1d8bfbd735f7ce2f8df92,9de937cde7940ea154de35a8a39ed517,14fedac30d9a5485bd4552c724acea75,4daea4033a0837fc6138268bc05b73a5,1fc7198a4c7ecade84e87ef35a097f4d,11a13648296a1beba91d3fbf9068d1a6,2adb0031342eba74b4b8238eda37ca0c,fd439b696a8b005918514cd8622f42f3,87c74e379a6f651a6b61d5eb47888697,b34724c59dbf11ff966b2b4a2c0e55a8,64105269d9b59c288110f23085fa357c\\\"]\",\"pyear\":\"[\\\"2019#FGF#103#FGF#076ece58affc006d3b4da53dbb974db2,28171c0c7706a4677d654d1f81b029bd,7b41b59ed0c59b72e2b4200e4eee0419,3ba6824ea887f77c2f36e11b5710f6ad,6647971605c709d56830920492689e35,bcdf947eb8e79c92b683601ba16b15d7,3565d1bc9b69121d2c51c9b5147f074c,e174b83f42a6d048e68663543da59deb,386a29dbbf81f74d8841c3665c156c78,2415a28f027b29b017b1ef563b880394,135a674aebb36d1268b38452e6fe8a01,ffd865053b26bc9053de0b2a2121eedb,a0c67199b8c1bdd6334a9cdc1149792c,969af3ec3a465ed1bac01270c5897e2b,c4fe623afb756d5dc4cc593bdc9227a1,8e1f1eb8d91e2869b408ceae67ee26dc,26e7f5223015faa17b3f7cd820e48838,9ef7f234734e551eb2b275ce92bc7ee3,4dd8b05b6c090fc85209de2ed0733370,0ce07057bc95b3c20176ce2b34f2c4c8,d034c7c38500fea0023c2bd87c406c9c,193e026a53f8bcdd65a00aa19916d632,ffd8928ffe11926fe109aba5c1079850,376fad3d432f88e2e65111759ed2b056,aee7018a4db3ed503be8e9bf5ae3cfbb,3d29e0233e3d1b7641f10db5c83c0a6a,f1cbe6e46351072d1764fa39216ad918,86069db4e1642fcd38ae8a2ed63d22b4,1c439a2b039809048daa0edf81894630,ce4202963d0e0493aebc08b9cbaf74f2,b4e3d404329040cbabd422ee131ef283,743aae78d87d7462e10ac9f465445f91,574b048c137d78f91aab81ba81a06c83,bef7b3f5cf1e8830e6480816b8fe6023,51582ca82fedc2849ae65ef1e7de57fa,4db4a1dcd316250368ed0cc556d8e3bc,7a2318dfcbef22609c57f4dfd6c90751,761f37d332afbaf4f83c5bf2e2b62334,f6aed61749377738891635834da3e28e,ed843c088e36d3a7f6f67327ab1a7382,3cea35c20df02e2f583dbc004ade2d99,e2d4a30b67af0f9b491c8a5f5d17c96b,2cf9ae35266de26cbf39c705a8c25140,99774f4c02ae9e8e5d9a49fb4005dafe,2a73d1afb6f182f14fdabd3bc7112466,435c0e7a8fcc258809abf0e2c3890844,616f0505ca166238d75a11d91492042e,90b0c803ebf12268300ab43e730bd2c1,0f1c4d046d4da68b9927af168c6dabcd,302a6c27d42214373d50ebe9385d75c1,be5f2576ac11d50bf13c6d893d248002,67b3055b86a0c84aeb3ef15621dcaf64,e4094881866fb7528e0040a2f2d01e83,1f15b0a4c4f2f3102aec231e71943415,9a24644618d54faaf9791db6322d80b3,da10b700d4247021adf493f316c4bdba,11db1054944a32672fad01ef4c359839,32a5c5c4696b7de143bb6acac85a1f22,145694540633c3a6fa9aa8b3fe53a5f2,1966b1231ad82ff3c7731a28aa3bd313,3ff67adb97056020da88515b5aab5591,30de81434671f87c902742b890322f34,58644452f655688fe81fc9efb1b09e6f,f844907936848d9eee95d8fa96301809,05cc34ee1b5668e440f9bfa2983bc881,26c8edcacfc5cd2a313e9228d000db62,37a623c7cf1720c2834daf87220830f4,371aa26949ec5f41091ac70b4789674f,693d0cc99d1bab8ec45f07ded5851d5d,6df9c9090966800193cc1fd00d2b07f9,0f1a7fc729942bca71c7a8a96651d506,e934d5fa223311298500c35da9a07ed4,4b56a196954137cef4f81997ea4fcdf2,ca895c4133d15a9b11c9f79bb2777457,b7709c4691ba2dd5971e03562663c79a,b1be25b04dc85db2e17dd6febb24c04d,eeaa8f8a360fdcf46ad0d1d8c652bc7d,2492360396725bd0ce2191187ef62f55,0fecd89c4f53a639e609871195650e12,574ed8611b8a06a544047189fe6230ed,9d12a40989454a0500c8573cc0ef385d,a60db204c58fe0db4d9923c92b406937,6e9d2f7105ef9682ff4fc0a5215a6051,a1d84712081a056ddff1b2747c95c6cf,3fe73532c5ae4dcfd150200cb7d32ffe,481b0382846b1f3feb3d9ca6aa046361,f8479209eaadc5308cef3300acd39a6d,ed660af04a4b47ebd9c7d561df21ab2c,4538d44f9536e48297b708d969c34997,c876d03deaf4a376ea00416619e5b649,8e27e5d136900ea27a6411655a5503f0,57cb723d0e135ed5f08a5e4857c62d0f,00b743d012d1d8bfbd735f7ce2f8df92,9de937cde7940ea154de35a8a39ed517,14fedac30d9a5485bd4552c724acea75,4daea4033a0837fc6138268bc05b73a5,1fc7198a4c7ecade84e87ef35a097f4d,11a13648296a1beba91d3fbf9068d1a6,2adb0031342eba74b4b8238eda37ca0c,fd439b696a8b005918514cd8622f42f3,87c74e379a6f651a6b61d5eb47888697,b34724c59dbf11ff966b2b4a2c0e55a8,64105269d9b59c288110f23085fa357c\\\"]\"}");
		System.out.println(job.evaluate("196daa1a3356d36508ebcc417ee0b2ea", null, null, null, null, null, null, null, null,
				null, null, null, null, null, null, null, null, null,null,zbInfoList));
	}
}
