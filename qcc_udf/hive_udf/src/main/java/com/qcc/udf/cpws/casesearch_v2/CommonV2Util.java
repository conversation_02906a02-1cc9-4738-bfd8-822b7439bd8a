package com.qcc.udf.cpws.casesearch_v2;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.cpws.ExtractCaseTrialRoundUDF;
import com.qcc.udf.cpws.casesearch_v2.enums.CaseTypeEnum;
import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class CommonV2Util {

    /**
     * 拆分所有的涉诉维度记录，将没有通过验证的某条记录拆分到另一组
     * @param inputLawSuitEntity
     * @param type
     * @return
     */
    /*public static List<LawSuitV2Entity> getLawSuitEntityList(LawSuitV2Entity inputLawSuitEntity, String type) {
        List<LawSuitV2Entity> lawSuitEntityList = new ArrayList<>();
        try {
            if (inputLawSuitEntity != null && StringUtils.isNotBlank(type)) {
                if (type.equals("zx")) {
                    *//**
                     * 执行案件的分组逻辑：
                     * 1 根据立案日期（精确到日）分组sx/zx/xg维度的数据，根据立案时间戳"LianTs"分组创建一个map<LianTs, LawSuitEneity>
                     * 2 将第1步中每组的汇总当事人列表，创建一个Map<当事人keyword, LawSuitEntity>
                     * 3 遍历裁判文书当事人列表，如果任一个当事人出现在keyword中，则认为该文书有关联；没有则该条文书单独存档
                     * 4 遍历破产重整当事人列表，如果任一个当事人出现在keyword中，则认为该破产重整有关联；没有则该条破产重整单独存档
                     * 5 遍历终本案件当事人列表，如果任一个当事人出现在keyword中，则认为该终本案件有关联；没有则该条终本案件单独存档
                     * 6 遍历询价评估当事人列表，如果任一个当事人出现在keyword中，则认为该询价评估有关联；没有则该条询价评估单独存档
                     * 7 遍历股权冻结当事人列表，如果任一个当事人出现在keyword中，则认为该股权冻结有关联；没有则该条股权冻结单独存档
                     * 8 遍历送达公告当事人列表，如果任一个当事人出现在keyword中，则认为该送达公告有关联；没有则该条送达公告单独存档
                     * 9 遍历法院公告当事人列表，如果任一个当事人出现在keyword中，则认为该法院公告有关联；没有则该条法院公告单独存档
                     * 10 遍历开庭公告当事人列表，如果任一个当事人出现在keyword中，则认为该开庭公告有关联；没有则该条开庭公告单独存档
                     * 11 遍历立案信息当事人列表，如果任一个当事人出现在keyword中，则认为该立案信息有关联；没有则该条立案信息单独存档
                     * 12 遍历环保处罚当事人列表，如果任一个当事人出现在keyword中，则认为该立案信息有关联；没有则舍弃
                     * 13 遍历行政处罚当事人列表，如果任一个当事人出现在keyword中，则认为该行政处罚有关联；没有则舍弃
                     *//*
                    Map<Long, LawSuitV2Entity> lianTsWithLawSuitEntityMap = new HashMap<>();

                    for (String sxInfo : inputLawSuitEntity.getSxList()) {
                        try {
                            Long lianTs = CommonV2Util.parseDateToTimeStamp(JSONObject.parseObject(sxInfo).getString("liandate"));
                            if (lianTsWithLawSuitEntityMap.get(lianTs) != null) {
                                LawSuitV2Entity lawSuitEntity = lianTsWithLawSuitEntityMap.get(lianTs);
                                List<String> sourceSxList = (lawSuitEntity.getSxList() != null ? lawSuitEntity.getSxList() : new ArrayList<>());
                                sourceSxList.add(sxInfo);
                                lawSuitEntity.setSxList(sourceSxList);
                                lianTsWithLawSuitEntityMap.put(lianTs, lawSuitEntity);
                            } else {
                                LawSuitV2Entity lawSuitEntity = new LawSuitV2Entity("", new ArrayList<>(Arrays.asList(sxInfo)), new ArrayList<>(), new ArrayList<>());
                                lianTsWithLawSuitEntityMap.put(lianTs, lawSuitEntity);
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }
                    for (String zxInfo : inputLawSuitEntity.getZxList()) {
                        try {
                            Long lianTs = CommonV2Util.parseDateToTimeStamp(JSONObject.parseObject(zxInfo).getString("liandate"));
                            if (lianTsWithLawSuitEntityMap.get(lianTs) != null) {
                                LawSuitV2Entity lawSuitEntity = lianTsWithLawSuitEntityMap.get(lianTs);
                                List<String> sourceZxList = (lawSuitEntity.getZxList() != null ? lawSuitEntity.getZxList() : new ArrayList<>());
                                sourceZxList.add(zxInfo);
                                lawSuitEntity.setZxList(sourceZxList);
                                lianTsWithLawSuitEntityMap.put(lianTs, lawSuitEntity);
                            } else {
                                LawSuitV2Entity lawSuitEntity = new LawSuitV2Entity("", new ArrayList<>(), new ArrayList<>(Arrays.asList(zxInfo)), new ArrayList<>());
                                lianTsWithLawSuitEntityMap.put(lianTs, lawSuitEntity);
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }
                    for (String xgInfo : inputLawSuitEntity.getXgList()) {
                        try {
                            Long judgeDate = JSONObject.parseObject(xgInfo).getLong("judgedate");
                            Long lianTs = (judgeDate != null ? judgeDate : -1L);
                            if (lianTsWithLawSuitEntityMap.get(lianTs) != null) {
                                LawSuitV2Entity lawSuitEntity = lianTsWithLawSuitEntityMap.get(lianTs);
                                List<String> sourceXgList = (lawSuitEntity.getXgList() != null ? lawSuitEntity.getXgList() : new ArrayList<>());
                                sourceXgList.add(xgInfo);
                                lawSuitEntity.setXgList(sourceXgList);
                                lianTsWithLawSuitEntityMap.put(lianTs, lawSuitEntity);
                            } else {
                                LawSuitV2Entity lawSuitEntity = new LawSuitV2Entity("", new ArrayList<>(), new ArrayList<>(), new ArrayList<>(Arrays.asList(xgInfo)));
                                lianTsWithLawSuitEntityMap.put(lianTs, lawSuitEntity);
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }

                    // 将有关联的案号对应的LawSuitEntity拼接到一起
                    *//**
                     * lianTsWithLawSuitEntityMap各项提取出当事人信息列表a,b,c
                     * 以a做依据，当b中至少有一个当事人在a的列表中，则将b合并到a，否则将b单独存放
                     *//*
                    Map<String, LawSuitV2Entity> keyWordWithLawSuitEntityMap = new HashMap<>();
                    if (lianTsWithLawSuitEntityMap != null && lianTsWithLawSuitEntityMap.size() > 0) {
                        for (LawSuitV2Entity lawSuitEntity : lianTsWithLawSuitEntityMap.values()) {
                            Set<String> mainKeySet = new HashSet<>();
                            getNameSetFromNameAndKeyNoField(lawSuitEntity.getSxList()).stream()
                                    .filter(StringUtils::isNotBlank).forEach(e -> {
                                mainKeySet.add(e);
                            });
                            getNameSetFromNameAndKeyNoField(lawSuitEntity.getZxList()).stream()
                                    .filter(StringUtils::isNotBlank).forEach(e -> {
                                mainKeySet.add(e);
                            });
                            getXgNameSetFromPersonNameAndCompanyName(lawSuitEntity.getXgList()).stream()
                                    .filter(StringUtils::isNotBlank).forEach(e -> {
                                mainKeySet.add(e);
                            });

                            String key = "";
                            for (String lawSuitKey : keyWordWithLawSuitEntityMap.keySet()) {
                                for (String name : mainKeySet) {
                                    if (lawSuitKey.contains(name)) {
                                        key = lawSuitKey;
                                    }
                                }
                            }

                            if (StringUtils.isNotBlank(key)) {
                                // 将两个lawsuitEntity对象合并
                                LawSuitV2Entity mainLawSuitEntity = keyWordWithLawSuitEntityMap.get(key);

                                List<String> mainSxList = mainLawSuitEntity.getSxList() != null ? mainLawSuitEntity.getSxList() : new ArrayList<>();
                                lawSuitEntity.getSxList().stream()
                                        .filter(StringUtils::isNotBlank).forEach(e -> {
                                    mainSxList.add(e);
                                });
                                List<String> mainZxList = mainLawSuitEntity.getZxList() != null ? mainLawSuitEntity.getZxList() : new ArrayList<>();
                                lawSuitEntity.getZxList().stream()
                                        .filter(StringUtils::isNotBlank).forEach(e -> {
                                    mainZxList.add(e);
                                });
                                List<String> mainXgList = mainLawSuitEntity.getXgList() != null ? mainLawSuitEntity.getXgList() : new ArrayList<>();
                                lawSuitEntity.getXgList().stream()
                                        .filter(StringUtils::isNotBlank).forEach(e -> {
                                    mainXgList.add(e);
                                });

                                mainLawSuitEntity.setSxList(mainSxList);
                                mainLawSuitEntity.setZxList(mainZxList);
                                mainLawSuitEntity.setXgList(mainXgList);
                                keyWordWithLawSuitEntityMap.put(key, mainLawSuitEntity);
                            } else {
                                String nameKeyWord = StringUtils.join(mainKeySet, ",");
                                keyWordWithLawSuitEntityMap.put(nameKeyWord, lawSuitEntity);
                            }
                        }
                    }

                    *//**裁判文书维度的聚合*//*
                    List<String> caseList = inputLawSuitEntity.getCaseList();
                    for (String caseItem : caseList) {
                        try {
                            Set<String> caseNameSet = getNameSetFromNameAndKeyNoField(new ArrayList<>(Arrays.asList(caseItem)));
                            String key = "";
                            for (String lawSuitKey : keyWordWithLawSuitEntityMap.keySet()) {
                                for (String caseName : caseNameSet) {
                                    if (lawSuitKey.contains(caseName)) {
                                        key = lawSuitKey;
                                    }
                                }
                            }

                            if (StringUtils.isNotBlank(key)) {
                                LawSuitV2Entity lawSuitEntity = keyWordWithLawSuitEntityMap.get(key);
                                List<String> sourceCaseList = (lawSuitEntity.getCaseList() != null ? lawSuitEntity.getCaseList() : new ArrayList<>());
                                sourceCaseList.add(caseItem);
                                lawSuitEntity.setCaseList(sourceCaseList);
                                keyWordWithLawSuitEntityMap.put(key, lawSuitEntity);
                            } else {
                                String newKeyWord = StringUtils.join(caseNameSet, ",");
                                LawSuitV2Entity lawSuitEntity = new LawSuitV2Entity(newKeyWord, new ArrayList<>(Arrays.asList(caseItem)));
                                keyWordWithLawSuitEntityMap.put(newKeyWord, lawSuitEntity);
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }

                    *//**破产重整公告维度的聚合*//*
                    List<String> pcczList = inputLawSuitEntity.getPcczList();
                    for (String pcczItem : pcczList) {
                        *//**依次遍历lawSuitEntityMap，当任一方名称出现在keyword中，则将破产重整公告放到对应value中，全不存在则单独存放*//*
                        Set<String> nameSet = getNameSetFromApplicantAndRespondentField(pcczItem);
                        String searchKey = getSearchKeyFromLawsuitEntityMap(nameSet, keyWordWithLawSuitEntityMap.keySet());

                        if (StringUtils.isNotBlank(searchKey) && keyWordWithLawSuitEntityMap.get(searchKey) != null) {
                            LawSuitV2Entity lawSuitV2Entity = keyWordWithLawSuitEntityMap.get(searchKey);
                            List<String> sourcePcczList = (lawSuitV2Entity.getPcczList() != null ?
                                    lawSuitV2Entity.getPcczList() : new ArrayList<>());
                            sourcePcczList.add(pcczItem);
                            lawSuitV2Entity.setPcczList(sourcePcczList);
                            keyWordWithLawSuitEntityMap.put(searchKey, lawSuitV2Entity);
                        } else {
                            String newKeyWord = StringUtils.join(nameSet, ",");
                            LawSuitV2Entity lawSuitV2Entity = LawSuitV2Entity.createSinglePcczListLawsuitV2Entity(newKeyWord,
                                    new ArrayList<>(Arrays.asList(pcczItem)));
                            keyWordWithLawSuitEntityMap.put(newKeyWord, lawSuitV2Entity);
                        }
                    }

                    *//**终本案件维度的聚合*//*
                    List<String> zbList = inputLawSuitEntity.getZbList();
                    for (String zbItem : zbList) {
                        *//**依次遍历lawSuitEntityMap，当任一方名称出现在keyword中，则将破产重整公告放到对应value中，全不存在则单独存放*//*
                        Set<String> nameSet = getNameSetFromNameField(zbItem);
                        String searchKey = getSearchKeyFromLawsuitEntityMap(nameSet, keyWordWithLawSuitEntityMap.keySet());

                        if (StringUtils.isNotBlank(searchKey) && keyWordWithLawSuitEntityMap.get(searchKey) != null) {
                            LawSuitV2Entity lawSuitV2Entity = keyWordWithLawSuitEntityMap.get(searchKey);
                            List<String> sourceZbList = (lawSuitV2Entity.getZbList() != null ?
                                    lawSuitV2Entity.getZbList() : new ArrayList<>());
                            sourceZbList.add(zbItem);
                            lawSuitV2Entity.setZbList(sourceZbList);
                            keyWordWithLawSuitEntityMap.put(searchKey, lawSuitV2Entity);
                        } else {
                            String newKeyWord = StringUtils.join(nameSet, ",");
                            LawSuitV2Entity lawSuitV2Entity = LawSuitV2Entity.createSingleZbListLawsuitV2Entity(newKeyWord,
                                    new ArrayList<>(Arrays.asList(zbItem)));
                            keyWordWithLawSuitEntityMap.put(newKeyWord, lawSuitV2Entity);
                        }
                    }

                    *//**询价评估维度的集合*//*
                    List<String> xjpgList = inputLawSuitEntity.getXjpgList();
                    for (String xjpgItem : xjpgList) {
                        *//**依次遍历lawSuitEntityMap，当任一方名称出现在keyword中，则将询价评估放到对应value中，全不存在则单独存放*//*
                        Set<String> nameSet = getNameSetFromNameAndKeyNoField(new ArrayList<>(Arrays.asList(xjpgItem)));
                        String searchKey = getSearchKeyFromLawsuitEntityMap(nameSet, keyWordWithLawSuitEntityMap.keySet());

                        if (StringUtils.isNotBlank(searchKey) && keyWordWithLawSuitEntityMap.get(searchKey) != null) {
                            LawSuitV2Entity lawSuitV2Entity = keyWordWithLawSuitEntityMap.get(searchKey);
                            List<String> sourceXjpgList = (lawSuitV2Entity.getXjpgList() != null ?
                                    lawSuitV2Entity.getXjpgList() : new ArrayList<>());
                            sourceXjpgList.add(xjpgItem);
                            lawSuitV2Entity.setXjpgList(sourceXjpgList);
                            keyWordWithLawSuitEntityMap.put(searchKey, lawSuitV2Entity);
                        } else {
                            String newKeyWord = StringUtils.join(nameSet, ",");
                            LawSuitV2Entity lawSuitV2Entity = LawSuitV2Entity.createSingleXjpgListLawsuitV2Entity(newKeyWord,
                                    new ArrayList<>(Arrays.asList(xjpgItem)));
                            keyWordWithLawSuitEntityMap.put(newKeyWord, lawSuitV2Entity);
                        }
                    }

                    *//**股权冻结维度的聚合*//*
                    List<String> gqdjList = inputLawSuitEntity.getGqdjList();
                    for (String gqdjItem : gqdjList) {
                        *//**依次遍历lawSuitEntityMap，当任一方名称出现在keyword中，则将股权冻结放到对应value中，全不存在则单独存放*//*
                        Set<String> nameSet = getNameSetFromCompanyNameField(gqdjItem);
                        String searchKey = getSearchKeyFromLawsuitEntityMap(nameSet, keyWordWithLawSuitEntityMap.keySet());

                        if (StringUtils.isNotBlank(searchKey) && keyWordWithLawSuitEntityMap.get(searchKey) != null) {
                            LawSuitV2Entity lawSuitV2Entity = keyWordWithLawSuitEntityMap.get(searchKey);
                            List<String> sourceGqdjList = (lawSuitV2Entity.getGqdjList() != null ?
                                    lawSuitV2Entity.getGqdjList() : new ArrayList<>());
                            sourceGqdjList.add(gqdjItem);
                            lawSuitV2Entity.setGqdjList(sourceGqdjList);
                            keyWordWithLawSuitEntityMap.put(searchKey, lawSuitV2Entity);
                        } else {
                            String newKeyWord = StringUtils.join(nameSet, ",");
                            LawSuitV2Entity lawSuitV2Entity = LawSuitV2Entity.createSingleGqdjListLawsuitV2Entity(newKeyWord,
                                    new ArrayList<>(Arrays.asList(gqdjItem)));
                            keyWordWithLawSuitEntityMap.put(newKeyWord, lawSuitV2Entity);
                        }
                    }

                    *//**送达公告维度的聚合*//*
                    List<String> sdggList = inputLawSuitEntity.getSdggList();
                    for (String sdggItem : sdggList) {
                        *//**依次遍历lawSuitEntityMap，当任一方名称出现在keyword中，则将送达公告放到对应value中，全不存在则单独存放*//*
                        Set<String> nameSet = new HashSet<>();
                        try {
                            nameSet = Arrays.stream(JSONObject.parseObject(sdggItem).getString("companynames").split(","))
                                    .filter(e -> !CommonV2Util.isKeyword(e)).filter(StringUtils::isNotBlank)
                                    .collect(Collectors.toSet());
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                        String searchKey = getSearchKeyFromLawsuitEntityMap(nameSet, keyWordWithLawSuitEntityMap.keySet());

                        if (StringUtils.isNotBlank(searchKey) && keyWordWithLawSuitEntityMap.get(searchKey) != null) {
                            LawSuitV2Entity lawSuitV2Entity = keyWordWithLawSuitEntityMap.get(searchKey);
                            List<String> sourceSdggList = (lawSuitV2Entity.getSdggList() != null ?
                                    lawSuitV2Entity.getSdggList() : new ArrayList<>());
                            sourceSdggList.add(sdggItem);
                            lawSuitV2Entity.setSdggList(sourceSdggList);
                            keyWordWithLawSuitEntityMap.put(searchKey, lawSuitV2Entity);
                        } else {
                            String newKeyWord = StringUtils.join(nameSet, ",");
                            LawSuitV2Entity lawSuitV2Entity = LawSuitV2Entity.createSingleSdggListLawsuitV2Entity(newKeyWord,
                                    new ArrayList<>(Arrays.asList(sdggItem)));
                            keyWordWithLawSuitEntityMap.put(newKeyWord, lawSuitV2Entity);
                        }
                    }

                    *//**法院公告维度的聚合*//*
                    List<String> fyggList = inputLawSuitEntity.getFyggList();
                    for (String fyggItem : fyggList) {
                        *//**依次遍历lawSuitEntityMap，当任一方名称出现在keyword中，则将法院公告放到对应value中，全不存在则单独存放*//*
                        Set<String> nameSet = new HashSet<>();
                        try {
                            JSONArray nameAndKeyNoJsonArray = JSONObject.parseObject(fyggItem).getJSONArray("nameandkeyno");
                            for (int i = 0; i < nameAndKeyNoJsonArray.size(); i++) {
                                JSONObject nameAndKeyNoJson = nameAndKeyNoJsonArray.getJSONObject(i);
                                String name = nameAndKeyNoJson.getString("Name");
                                if (StringUtils.isNotBlank(name)) {
                                    nameSet.add(name);
                                }
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                        String searchKey = getSearchKeyFromLawsuitEntityMap(nameSet, keyWordWithLawSuitEntityMap.keySet());

                        if (StringUtils.isNotBlank(searchKey) && keyWordWithLawSuitEntityMap.get(searchKey) != null) {
                            LawSuitV2Entity lawSuitV2Entity = keyWordWithLawSuitEntityMap.get(searchKey);
                            List<String> sourceFyggList = (lawSuitV2Entity.getFyggList() != null ?
                                    lawSuitV2Entity.getFyggList() : new ArrayList<>());
                            sourceFyggList.add(fyggItem);
                            lawSuitV2Entity.setFyggList(sourceFyggList);
                            keyWordWithLawSuitEntityMap.put(searchKey, lawSuitV2Entity);
                        } else {
                            String newKeyWord = StringUtils.join(nameSet, ",");
                            LawSuitV2Entity lawSuitV2Entity = LawSuitV2Entity.createSingleFyggListLawsuitV2Entity(newKeyWord,
                                    new ArrayList<>(Arrays.asList(fyggItem)));
                            keyWordWithLawSuitEntityMap.put(newKeyWord, lawSuitV2Entity);
                        }
                    }

                    *//**开庭公告维度的聚合*//*
                    List<String> ktggList = inputLawSuitEntity.getKtggList();
                    for (String ktggItem : ktggList) {
                        *//**依次遍历lawSuitEntityMap，当任一方名称出现在keyword中，则将开庭公告放到对应value中，全不存在则单独存放*//*
                        Set<String> nameSet = new HashSet<>();
                        try {
                            nameSet = Arrays.stream(JSONObject.parseObject(ktggItem).getString("companynames").split(","))
                                    .filter(e -> !CommonV2Util.isKeyword(e))
                                    .filter(StringUtils::isNotBlank)
                                    .collect(Collectors.toSet());
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                        String searchKey = getSearchKeyFromLawsuitEntityMap(nameSet, keyWordWithLawSuitEntityMap.keySet());

                        if (StringUtils.isNotBlank(searchKey) && keyWordWithLawSuitEntityMap.get(searchKey) != null) {
                            LawSuitV2Entity lawSuitV2Entity = keyWordWithLawSuitEntityMap.get(searchKey);
                            List<String> sourceKtggList = (lawSuitV2Entity.getKtggList() != null ?
                                    lawSuitV2Entity.getKtggList() : new ArrayList<>());
                            sourceKtggList.add(ktggItem);
                            lawSuitV2Entity.setKtggList(sourceKtggList);
                            keyWordWithLawSuitEntityMap.put(searchKey, lawSuitV2Entity);
                        } else {
                            String newKeyWord = StringUtils.join(nameSet, ",");
                            LawSuitV2Entity lawSuitV2Entity = LawSuitV2Entity.createSingleKtggListLawsuitV2Entity(newKeyWord,
                                    new ArrayList<>(Arrays.asList(ktggItem)));
                            keyWordWithLawSuitEntityMap.put(newKeyWord, lawSuitV2Entity);
                        }
                    }

                    *//**立案信息维度的聚合*//*
                    List<String> lianList = inputLawSuitEntity.getLianList();
                    for (String lianItem : lianList) {
                        *//**依次遍历lawSuitEntityMap，当任一方名称出现在keyword中，则将立案信息放到对应value中，全不存在则单独存放*//*
                        Set<String> nameSet = new HashSet<>();
                        try {
                            JSONArray nameAndKeyNoJsonArray = JSONObject.parseObject(lianItem).getJSONArray("nameandkeyno");
                            for (int i = 0; i < nameAndKeyNoJsonArray.size(); i++) {
                                JSONObject nameAndKeyNoJson = nameAndKeyNoJsonArray.getJSONObject(i);
                                String name = nameAndKeyNoJson.getString("Name");
                                if (StringUtils.isNotBlank(name)) {
                                    nameSet.add(name);
                                }
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                        String searchKey = getSearchKeyFromLawsuitEntityMap(nameSet, keyWordWithLawSuitEntityMap.keySet());

                        if (StringUtils.isNotBlank(searchKey) && keyWordWithLawSuitEntityMap.get(searchKey) != null) {
                            LawSuitV2Entity lawSuitV2Entity = keyWordWithLawSuitEntityMap.get(searchKey);
                            List<String> sourceLianList = (lawSuitV2Entity.getLianList() != null ?
                                    lawSuitV2Entity.getLianList() : new ArrayList<>());
                            sourceLianList.add(lianItem);
                            lawSuitV2Entity.setLianList(sourceLianList);
                            keyWordWithLawSuitEntityMap.put(searchKey, lawSuitV2Entity);
                        } else {
                            String newKeyWord = StringUtils.join(nameSet, ",");
                            LawSuitV2Entity lawSuitV2Entity = LawSuitV2Entity.createSingleLianListLawsuitV2Entity(newKeyWord,
                                    new ArrayList<>(Arrays.asList(lianItem)));
                            keyWordWithLawSuitEntityMap.put(newKeyWord, lawSuitV2Entity);
                        }
                    }

                    *//**环保处罚维度的聚合*//*
                    List<String> hbcfList = inputLawSuitEntity.getHbcfList();
                    for (String hbcfItem : hbcfList) {
                        *//**依次遍历lawSuitEntityMap，当任一方名称出现在keyword中，则将环保处罚放到对应value中，全部存在则舍弃*//*
                        Set<String> nameSet = new HashSet<>();
                        try {
                            String name = JSONObject.parseObject(hbcfItem).getString("company_c");
                            if (StringUtils.isNotBlank(name)) {
                                nameSet.add(name);
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                        String searchKey = getSearchKeyFromLawsuitEntityMap(nameSet, keyWordWithLawSuitEntityMap.keySet());

                        if (StringUtils.isNotBlank(searchKey) && keyWordWithLawSuitEntityMap.get(searchKey) != null) {
                            LawSuitV2Entity lawSuitV2Entity = keyWordWithLawSuitEntityMap.get(searchKey);
                            List<String> sourceHbcfList = (lawSuitV2Entity.getHbcfList() != null ?
                                    lawSuitV2Entity.getHbcfList() : new ArrayList<>());
                            sourceHbcfList.add(hbcfItem);
                            lawSuitV2Entity.setHbcfList(sourceHbcfList);
                            keyWordWithLawSuitEntityMap.put(searchKey, lawSuitV2Entity);
                        }
                    }

                    *//**行政处罚（工商）维度聚合*//*
                    List<String> cfgsList = inputLawSuitEntity.getCfgsList();
                    for (String cfgsItem : cfgsList) {
                        Set<String> nameSet = new HashSet<>();
                        try {
                            String name = JSONObject.parseObject(cfgsItem).getString("companyname");
                            if (StringUtils.isNotBlank(name)) {
                                nameSet.add(name);
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                        String searchKey = getSearchKeyFromLawsuitEntityMap(nameSet, keyWordWithLawSuitEntityMap.keySet());

                        if (StringUtils.isNotBlank(searchKey) && keyWordWithLawSuitEntityMap.get(searchKey) != null) {
                            LawSuitV2Entity lawSuitV2Entity = keyWordWithLawSuitEntityMap.get(searchKey);
                            List<String> sourceCfgsList = (lawSuitV2Entity.getCfgsList() != null ?
                                    lawSuitV2Entity.getCfgsList() : new ArrayList<>());
                            sourceCfgsList.add(cfgsItem);
                            lawSuitV2Entity.setCfgsList(sourceCfgsList);
                            keyWordWithLawSuitEntityMap.put(searchKey, lawSuitV2Entity);
                        }
                    }

                    *//**行政处罚（信用中国）维度聚合*//*
                    List<String> cfxyList = inputLawSuitEntity.getCfxyList();
                    for (String cfxyItem : cfxyList) {
                        Set<String> nameSet = new HashSet<>();
                        try {
                            String name = JSONObject.parseObject(cfxyItem).getString("ownername");
                            if (StringUtils.isNotBlank(name)) {
                                nameSet.add(name);
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                        String searchKey = getSearchKeyFromLawsuitEntityMap(nameSet, keyWordWithLawSuitEntityMap.keySet());

                        if (StringUtils.isNotBlank(searchKey) && keyWordWithLawSuitEntityMap.get(searchKey) != null) {
                            LawSuitV2Entity lawSuitV2Entity = keyWordWithLawSuitEntityMap.get(searchKey);
                            List<String> sourceCfxyList = (lawSuitV2Entity.getCfxyList() != null ?
                                    lawSuitV2Entity.getCfxyList() : new ArrayList<>());
                            sourceCfxyList.add(cfxyItem);
                            lawSuitV2Entity.setCfxyList(sourceCfxyList);
                            keyWordWithLawSuitEntityMap.put(searchKey, lawSuitV2Entity);
                        }
                    }

                    *//**行政处罚（地方）维度聚合*//*
                    List<String> cfdfList = inputLawSuitEntity.getCfdfList();
                    for (String cfdfItem : cfdfList) {
                        Set<String> nameSet = new HashSet<>();
                        try {
                            String name = JSONObject.parseObject(cfdfItem).getString("companyname");
                            if (StringUtils.isNotBlank(name)) {
                                nameSet.add(name);
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                        String searchKey = getSearchKeyFromLawsuitEntityMap(nameSet, keyWordWithLawSuitEntityMap.keySet());

                        if (StringUtils.isNotBlank(searchKey) && keyWordWithLawSuitEntityMap.get(searchKey) != null) {
                            LawSuitV2Entity lawSuitV2Entity = keyWordWithLawSuitEntityMap.get(searchKey);
                            List<String> sourceCfdfList = (lawSuitV2Entity.getCfdfList() != null ?
                                    lawSuitV2Entity.getCfdfList() : new ArrayList<>());
                            sourceCfdfList.add(cfdfItem);
                            lawSuitV2Entity.setCfdfList(sourceCfdfList);
                            keyWordWithLawSuitEntityMap.put(searchKey, lawSuitV2Entity);
                        }
                    }

                    // 遍历map结果，增加法院名称字段信息，汇总至list中
                    for (LawSuitV2Entity lawSuitEntity : keyWordWithLawSuitEntityMap.values()) {
                        String court = getFirstCourtNameFromLawSuitEntity(lawSuitEntity);
                        lawSuitEntity.setCourt(court);
                        lawSuitEntityList.add(lawSuitEntity);
                    }
                } else if (type.equals("ms")) {
                    *//**
                     * 民事案件的分组逻辑：
                     * 1 假定ktgg/fygg关联是正确的，提取四个维度下的所有当事人组成列表list
                     * 2 遍历裁判文书当事人列表，如果任一个当事人出现在keyword中，则认为该文书有关联；没有则该条文书单独存放
                     * 3 遍历破产公告当事人申请人和被申请人集合列表，如果任一个当事人出现在keyword中，则认为该公告有关联，没有则该公告单独存放
                     *//*
                    List<String> lianList = inputLawSuitEntity.getLianList();
                    List<String> ktggList = inputLawSuitEntity.getKtggList();
                    List<String> sdggList = inputLawSuitEntity.getSdggList();
                    List<String> fyggList = inputLawSuitEntity.getFyggList();

                    Set<String> mainKeySet = new HashSet<>();
                    getNameSetFromNameAndKeyNoField(ktggList).stream()
                            .filter(StringUtils::isNotBlank).forEach(e -> {
                        mainKeySet.add(e);
                    });
                    getNameSetFromNameAndKeyNoField(fyggList).stream()
                            .filter(StringUtils::isNotBlank).forEach(e -> {
                        mainKeySet.add(e);
                    });

                    // 主关联对象
                    String keyword = StringUtils.join(mainKeySet, ",");
                    LawSuitV2Entity mainLawSuitEntity = new LawSuitV2Entity(keyword, lianList, ktggList, sdggList, fyggList);

                    Map<String, LawSuitV2Entity> lawSuitEntityMap = new HashMap<>();
                    lawSuitEntityMap.put(keyword, mainLawSuitEntity);

                    List<String> caseList = inputLawSuitEntity.getCaseList();
                    for (String caseItem : caseList) {
                        try {
                            Set<String> caseNameSet = getNameSetFromNameAndKeyNoField(new ArrayList<>(Arrays.asList(caseItem)));
                            String key = "";
                            for (String lawSuitKey : lawSuitEntityMap.keySet()) {
                                for (String caseName : caseNameSet) {
                                    if (lawSuitKey.contains(caseName)) {
                                        key = lawSuitKey;
                                    }
                                }
                            }

                            if (StringUtils.isNotBlank(key)) {
                                LawSuitV2Entity lawSuitEntity = lawSuitEntityMap.get(key);
                                List<String> sourceCaseList = (lawSuitEntity.getCaseList() != null ? lawSuitEntity.getCaseList() : new ArrayList<>());
                                sourceCaseList.add(caseItem);
                                lawSuitEntity.setCaseList(sourceCaseList);
                                lawSuitEntityMap.put(key, lawSuitEntity);
                            } else {
                                String newKeyWord = StringUtils.join(caseNameSet, ",");
                                LawSuitV2Entity lawSuitEntity = new LawSuitV2Entity(newKeyWord, new ArrayList<>(Arrays.asList(caseItem)));
                                lawSuitEntityMap.put(newKeyWord, lawSuitEntity);
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }

                    List<String> pcczList = inputLawSuitEntity.getPcczList();
                    for (String pcczItem : pcczList) {
                        *//**依次遍历lawSuitEntityMap，当破产公告任一方名称出现在keyword中，则将破产重整公告放到对应value中，全不存在则单独存放*//*
                        Set<String> nameSet = getNameSetFromApplicantAndRespondentField(pcczItem);
                        // 遍历后匹配得到的对应key，遍历后为空字符串则表示没有任何匹配到
                        String searchKey = getSearchKeyFromLawsuitEntityMap(nameSet, lawSuitEntityMap.keySet());

                        if (StringUtils.isNotBlank(searchKey) && lawSuitEntityMap.get(searchKey) != null) {
                            LawSuitV2Entity lawSuitV2Entity = lawSuitEntityMap.get(searchKey);
                            List<String> sourcePcczList = (lawSuitV2Entity.getPcczList() != null ?
                                    lawSuitV2Entity.getPcczList() : new ArrayList<>());
                            sourcePcczList.add(pcczItem);
                            lawSuitV2Entity.setPcczList(sourcePcczList);
                            lawSuitEntityMap.put(searchKey, lawSuitV2Entity);
                        } else {
                            String newKeyWord = StringUtils.join(nameSet, ",");
                            LawSuitV2Entity lawSuitV2Entity = LawSuitV2Entity.createSinglePcczListLawsuitV2Entity(newKeyWord, new ArrayList<>(Arrays.asList(pcczItem)));
                            lawSuitEntityMap.put(newKeyWord, lawSuitV2Entity);
                        }
                    }

                    // 遍历map结果，增加法院名称字段信息，汇总至list中
                    for (LawSuitV2Entity lawSuitEntity : lawSuitEntityMap.values()) {
                        String court = getFirstCourtNameFromLawSuitEntity(lawSuitEntity);
                        lawSuitEntity.setCourt(court);
                        lawSuitEntityList.add(lawSuitEntity);
                    }
                }
                else if (type.equals("xz")) {
                    *//**
                     * 民事案件的分组逻辑：
                     * 1 假定ktgg/fygg关联是正确的，提取四个维度下的所有当事人组成列表list
                     * 2 遍历裁判文书当事人列表，如果任一个当事人出现在keyword中，则认为该文书有关联；没有则该条文书单独存放
                     * 3 遍历破产公告当事人申请人和被申请人集合列表，如果任一个当事人出现在keyword中，则认为该公告有关联，没有则该公告单独存放
                     *//*
                    List<String> lianList = inputLawSuitEntity.getLianList();
                    List<String> ktggList = inputLawSuitEntity.getKtggList();
                    List<String> sdggList = inputLawSuitEntity.getSdggList();
                    List<String> fyggList = inputLawSuitEntity.getFyggList();

                    Set<String> mainKeySet = new HashSet<>();
                    getNameSetFromNameAndKeyNoField(ktggList).stream()
                            .filter(StringUtils::isNotBlank).forEach(e -> {
                        mainKeySet.add(e);
                    });
                    getNameSetFromNameAndKeyNoField(fyggList).stream()
                            .filter(StringUtils::isNotBlank).forEach(e -> {
                        mainKeySet.add(e);
                    });

                    // 主关联对象
                    String keyword = StringUtils.join(mainKeySet, ",");
                    LawSuitV2Entity mainLawSuitEntity = new LawSuitV2Entity(keyword, lianList, ktggList, sdggList, fyggList);

                    Map<String, LawSuitV2Entity> lawSuitEntityMap = new HashMap<>();
                    lawSuitEntityMap.put(keyword, mainLawSuitEntity);

                    List<String> caseList = inputLawSuitEntity.getCaseList();
                    for (String caseItem : caseList) {
                        try {
                            Set<String> caseNameSet = getNameSetFromNameAndKeyNoField(new ArrayList<>(Arrays.asList(caseItem)));
                            String key = "";
                            for (String lawSuitKey : lawSuitEntityMap.keySet()) {
                                for (String caseName : caseNameSet) {
                                    if (lawSuitKey.contains(caseName)) {
                                        key = lawSuitKey;
                                    }
                                }
                            }

                            if (StringUtils.isNotBlank(key)) {
                                LawSuitV2Entity lawSuitEntity = lawSuitEntityMap.get(key);
                                List<String> sourceCaseList = (lawSuitEntity.getCaseList() != null ? lawSuitEntity.getCaseList() : new ArrayList<>());
                                sourceCaseList.add(caseItem);
                                lawSuitEntity.setCaseList(sourceCaseList);
                                lawSuitEntityMap.put(key, lawSuitEntity);
                            } else {
                                String newKeyWord = StringUtils.join(caseNameSet, ",");
                                LawSuitV2Entity lawSuitEntity = new LawSuitV2Entity(newKeyWord, new ArrayList<>(Arrays.asList(caseItem)));
                                lawSuitEntityMap.put(newKeyWord, lawSuitEntity);
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }

                    // 遍历map结果，增加法院名称字段信息，汇总至list中
                    for (LawSuitV2Entity lawSuitEntity : lawSuitEntityMap.values()) {
                        String court = getFirstCourtNameFromLawSuitEntity(lawSuitEntity);
                        lawSuitEntity.setCourt(court);
                        lawSuitEntityList.add(lawSuitEntity);
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return lawSuitEntityList;
    }*/
    public static List<LawSuitV2Entity> getLawSuitEntityList(LawSuitV2Entity inputLawSuitEntity, String type) {
        List<LawSuitV2Entity> lawSuitEntityList = new ArrayList<>();
        try {
            if (inputLawSuitEntity != null && StringUtils.isNotBlank(type)) {
                // 主关联对象
                String keyword = "";
                LawSuitV2Entity mainLawSuitEntity = new LawSuitV2Entity(keyword, inputLawSuitEntity.getLianList(), inputLawSuitEntity.getKtggList(),
                        inputLawSuitEntity.getSdggList(), inputLawSuitEntity.getFyggList(), inputLawSuitEntity.getCaseList(),
                        inputLawSuitEntity.getZxList(), inputLawSuitEntity.getSxList(), inputLawSuitEntity.getXgList(), inputLawSuitEntity.getPcczList(),
                        inputLawSuitEntity.getZbList(), inputLawSuitEntity.getXjpgList(), inputLawSuitEntity.getGqdjList(), inputLawSuitEntity.getHbcfList(),
                        inputLawSuitEntity.getCfgsList(), inputLawSuitEntity.getCfxyList(), inputLawSuitEntity.getCfdfList());
                Map<String, LawSuitV2Entity> lawSuitEntityMap = new HashMap<>();
                lawSuitEntityMap.put(keyword, mainLawSuitEntity);

                // 遍历map结果，增加法院名称字段信息，汇总至list中
                for (LawSuitV2Entity lawSuitEntity : lawSuitEntityMap.values()) {
                    String court = getFirstCourtNameFromLawSuitEntity(lawSuitEntity);
                    lawSuitEntity.setCourt(court);
                    lawSuitEntityList.add(lawSuitEntity);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return lawSuitEntityList;
    }

    /**
     * 遍历nameSet，匹配lawSuitEntityMapKeySet中的值，当包含时则返回该item，否则返回空字符串
     * @param nameSet 来自其它维度（破产重整/股权冻结/询价评估..）匹配当时人的集合列表
     * @param lawSuitEntityMapKeySet 被匹配的集合列表。当前者（nameSet）中的某个值包含于该集合的某个item中时，则认为nameSet属于该item
     * @return 返回lawSuitEntityMapKeySet中的某个item，均不匹配时返回空字符串
     */
    private static String getSearchKeyFromLawsuitEntityMap(Set<String> nameSet, Set<String> lawSuitEntityMapKeySet) {
        String searchKey = StringUtils.EMPTY;
        try {
            if (nameSet != null && nameSet.size() > 0
                    && lawSuitEntityMapKeySet != null && lawSuitEntityMapKeySet.size() > 0) {
                for (String lawSuitEntityMapKey :lawSuitEntityMapKeySet) {
                    Boolean isMatch = false;
                    for (String name : nameSet) {
                        if (lawSuitEntityMapKey.contains(name)) {
                            isMatch = true;
                        }
                    }
                    if (isMatch == true) {
                        searchKey = lawSuitEntityMapKey;
                        break;
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return searchKey;
    }

    /**
     * 从原被告字段中提取并返回涉案主体信息（x1、x2、x3，顿号分隔，最多取三个，超过三个则加上"等"尾缀）
     * @param jsonArray
     * @return
     */
    private static String getParticipants(JSONArray jsonArray) {
        Set<String> top3Names = jsonArray.stream()
                .map(e -> {
                    try {
                        return (JSONObject) e;
                    } catch (Exception ex) {
                        return null;
                    }
                })
                .filter(e -> e != null && e.containsKey("Name"))
                .map(e -> e.getString("Name"))
                .filter(StringUtils::isNotBlank)
                .limit(3)
                .collect(Collectors.toSet());
        if (top3Names != null && top3Names.size() > 0) {
            if (jsonArray.size() > 3) {
                return StringUtils.join(top3Names, "、") + "等";
            } else {
                return StringUtils.join(top3Names, "、");
            }
        } else {
            return "";
        }
    }

    /**
     * 从infoList中获取LastestTimeStamp最前一项的案由
     * @param infoList
     * @return
     */
    public static String getCaseReasonFromInfoList(JSONArray infoList) {
        String caseReason = "";
        try {
            List<JSONObject> jsonObjectList = infoList.stream()
                    .map(e -> {
                        try {
                            return (JSONObject) e;
                        } catch (Exception ex) {
                            return null;
                        }
                    })
                    .filter(e -> e != null && e.containsKey("LatestTimestamp"))
                    .sorted(Comparator.comparingLong(e -> e.getLong("LatestTimestamp")))
                    .collect(Collectors.toList());
            if (jsonObjectList != null && jsonObjectList.size() > 0) {
                JSONObject jsonObject = jsonObjectList.get(0);
                caseReason = jsonObject.getString("CaseReason");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return caseReason;
    }

    /**
     * 从infoList中获取LastestTimeStamp最前一项的原被告信息及案由，拼接为CaseName
     * @param infoList
     * @param caseType
     * @return
     */
    public static String getCaseNameFromInfoList(JSONArray infoList, String caseType) {
        try {
            List<JSONObject> jsonObjectList = infoList.stream()
                    .map(e -> {
                        try {
                            return (JSONObject) e;
                        } catch (Exception ex) {
                            return null;
                        }
                    })
                    .filter(e -> e != null && e.containsKey("LatestTimestamp"))
                    .sorted(Comparator.comparingLong(e -> e.getLong("LatestTimestamp")))
                    .collect(Collectors.toList());
            if (jsonObjectList == null || jsonObjectList.size() == 0) {
                return "";
            }

            JSONObject jsonObject = jsonObjectList.get(0);  // Prosecutor Defendant  CaseReason

            JSONArray prosecutorJsonArray =
                    jsonObject.getJSONArray("Prosecutor") != null ? jsonObject.getJSONArray("Prosecutor") : new JSONArray();
            JSONArray defendantJsonArray =
                    jsonObject.getJSONArray("Defendant") != null ? jsonObject.getJSONArray("Defendant") : new JSONArray();
            String caseReason =
                    jsonObject.getString("CaseReason") != null ? jsonObject.getString("CaseReason") : "";
            /**
             * CaseName拼接规则
             * 民事案件
             *     原被告+案由（或"民事案件"）
             *
             * 执行案件
             *     1 原被告均存在时
             *       -> 原被告+案由（或"执行案件"）
             *     2 原被告一方不存在时
             *       2.1 仅有申请执行人（原告信息） -> （申请执行人姓名）+“申请执行案件”
             *       2.2 仅有被执行人（被告信息）-> （被申请执行人姓名）+“被申请执行案件”
             */
            String prosecutor = "";
            if (prosecutorJsonArray.size() > 0) {
                prosecutor = CommonV2Util.getParticipants(prosecutorJsonArray);
            }
            String defendant = "";
            if (defendantJsonArray.size() > 0) {
                defendant = CommonV2Util.getParticipants(defendantJsonArray);
            }

            String caseTypeValue = CaseTypeEnum.valueOf(caseType.toUpperCase()).getName();
            String suffix = (StringUtils.isNotBlank(caseReason) ? caseReason : caseTypeValue);
            if (caseType.equals("zx")) {
                if (StringUtils.isNotBlank(prosecutor) && StringUtils.isBlank(defendant)) {
                    suffix = (StringUtils.isNotBlank(caseReason) ? caseReason : "申请执行案件");
                } else if (StringUtils.isBlank(prosecutor) && StringUtils.isNotBlank(defendant)) {
                    suffix = (StringUtils.isNotBlank(caseReason) ? caseReason : "被申请执行案件");
                }
            }

            String prefix = "";
            //案件名称中当事人进行去重，若原告与被告完全一样，则只显示原告即可
            if(prosecutor.equals(defendant)){
                defendant = "";
            }
            if (StringUtils.isNotBlank(prosecutor) && StringUtils.isNotBlank(defendant)) {
                prefix = prosecutor + "与" + defendant;
            } else {
                prefix = prosecutor + defendant;
            }
            return prefix + suffix;

        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return "";
    }

    /**
     * 从infoList中获取LastestTimeStamp最前一项的原被告信息，拼接为CaseRole的值
     * @param infoList
     * @return
     */
    public static String getCaseRoleFromInfoList(JSONArray infoList) {
        String caseRoleStr = new JSONArray().toJSONString();
        try {
            List<JSONObject> jsonObjectList = infoList.stream()
                    .map(e -> {
                        try {
                            return (JSONObject) e;
                        } catch (Exception ex) {
                            return null;
                        }
                    })
                    .filter(e -> e != null && e.containsKey("LatestTimestamp"))
                    .sorted(Comparator.comparingLong(e -> e.getLong("LatestTimestamp")))
                    .collect(Collectors.toList());
            if (jsonObjectList != null && jsonObjectList.size() > 0) {
                JSONObject jsonObject = jsonObjectList.get(0);  // Prosecutor Defendant  CaseReason

                JSONArray caseRoleJsonArray = new JSONArray();

                JSONArray prosecutorJsonArray =
                        jsonObject.getJSONArray("Prosecutor") != null ? jsonObject.getJSONArray("Prosecutor") : new JSONArray();
                for (int i = 0; i < prosecutorJsonArray.size(); i++) {
                    JSONObject prosecutorJson = prosecutorJsonArray.getJSONObject(i);
                    JSONObject caseRoleJson = new JSONObject();
                    caseRoleJson.put("P", prosecutorJson.getString("Name"));
                    caseRoleJson.put("N", prosecutorJson.getString("KeyNo"));
                    caseRoleJson.put("R", prosecutorJson.getString("Role"));
                    caseRoleJson.put("O", prosecutorJson.getInteger("Org"));
                    caseRoleJsonArray.add(caseRoleJson);
                }

                JSONArray defendantJsonArray =
                        jsonObject.getJSONArray("Defendant") != null ? jsonObject.getJSONArray("Defendant") : new JSONArray();
                for (int i = 0; i < defendantJsonArray.size(); i++) {
                    JSONObject defendantJson = defendantJsonArray.getJSONObject(i);
                    JSONObject caseRoleJson = new JSONObject();
                    caseRoleJson.put("P", defendantJson.getString("Name"));
                    caseRoleJson.put("N", defendantJson.getString("KeyNo"));
                    caseRoleJson.put("R", defendantJson.getString("Role"));
                    caseRoleJson.put("O", defendantJson.getInteger("Org"));
                    caseRoleJsonArray.add(caseRoleJson);
                }
                caseRoleStr = caseRoleJsonArray.toJSONString();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return caseRoleStr;
    }

    /**
     * 提取infoList每一项最晚一个审判程序节点的时间戳（不存在时默认为-1）
     * @param jsonObject
     * @return
     */
    public static Long getLatestTimestampFromInfoListItem(JSONObject jsonObject) {
        Long latestTimestamp = -1L;

        List<String> fieldNameList = Arrays.asList(
                "LianList", "FyggList", "KtggList", "SdggList", "CaseList", "SxList", "ZxList", "XgList",
                "PcczList", "ZbList", "XjpgList", "GqdjList"
        );
        try {
            for (String fieldName : fieldNameList) {
                JSONArray jsonArray = jsonObject.getJSONArray(fieldName);
                Iterator iterator = jsonArray.iterator();
                while (iterator.hasNext()) {
                    JSONObject itemJson = (JSONObject) iterator.next();
                    latestTimestamp = compareTimestamp(itemJson.getLong("LianDate"), latestTimestamp);
                    latestTimestamp = compareTimestamp(itemJson.getLong("OpenDate"), latestTimestamp);
                    latestTimestamp = compareTimestamp(itemJson.getLong("PublishDate"), latestTimestamp);
                    latestTimestamp = compareTimestamp(itemJson.getLong("JudgeDate"), latestTimestamp);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return latestTimestamp;
    }

    /**
     * 按照LatestTimestamp的递增顺序排列并提取合并指定字段的值（做去重处理）
     * @param infoList
     * @param fieldName
     * @return
     */
    public static String getKeywordsFromInfoList(JSONArray infoList, String fieldName) {
        Set<String> keyWordSet = new TreeSet<>();
        try {
            List<JSONObject> jsonObjectList = infoList.stream()
                    .map(e -> {
                        try {
                            return (JSONObject) e;
                        } catch (Exception ex) {
                            return null;
                        }
                    })
                    .filter(e -> e != null && e.containsKey("LatestTimestamp"))
                    .sorted(Comparator.comparingLong(e -> e.getLong("LatestTimestamp")))
                    .collect(Collectors.toList());
            if (jsonObjectList != null && jsonObjectList.size() > 0 && StringUtils.isNotBlank(fieldName)) {
                for (JSONObject jsonObj : jsonObjectList) {
                    if (jsonObj != null && jsonObj.containsKey(fieldName)) {
                        String value = jsonObj.getString(fieldName);
                        if (StringUtils.isNotBlank(value) && !keyWordSet.contains(value)) {
                            keyWordSet.addAll(Arrays.asList(value.split(",")));
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return StringUtils.join(keyWordSet, ",");
    }

    /**
     * 从searchWordSet中汇总所有的搜索关键词信息
     * @param searchWordSet
     * @return
     */
    public static String getCompanyKeywordsFromSearchWordSet(Set<String> searchWordSet) {
        List<String> companyKeyWords = searchWordSet.stream()
                .filter(StringUtils::isNotBlank)
                .filter(e -> e.length() > 4 || (e.length() <= 4 && !e.contains("某")))
                .sorted().collect(Collectors.toList());
        if (companyKeyWords != null && companyKeyWords.size() > 0) {
            return StringUtils.join(companyKeyWords, ",");
        } else {
            return "";
        }
    }

    /**
     * 汇总所有省份编码信息
     * @param itemLists
     * @return
     */
    public static Set<String> collectProvinceCode(List<String>... itemLists) {
        Set<String> provinceCodeSet = new HashSet<>();
        for (List<String> itemList : itemLists) {
            for (String item : itemList) {
                try {
                    JSONObject jsonObject = JSONObject.parseObject(item);
                    if (jsonObject.containsKey("province")) {
                        provinceCodeSet.add(jsonObject.getString("province"));
                    } else {
                        provinceCodeSet.add("");
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        }
        return provinceCodeSet;
    }

    public static Boolean isKeyword(String str) {
        String regex = "^[a-zA-Z0-9]+$";
        Pattern pattern = Pattern.compile(regex);
        Matcher match = pattern.matcher(str);
        return match.matches();
    }

    public static JSONObject getJsonObject(Object obj){
        /*String content = obj.toString().replace("\\\"","\"").replace("\\\\\"","\"").replace("\"[","[").replace("]\"","]");
        System.out.println(content);*/
        return JSONObject.parseObject(obj.toString());
    }

    /**
     * 返回字符串或者空值
     * @param strTypeObj
     * @return
     */
    public static String getStringOrEmptyString(Object strTypeObj) {
        String result = StringUtils.EMPTY;
        try {
            if (strTypeObj != null && strTypeObj instanceof String) {
                result = String.valueOf(strTypeObj);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return result;
    }


    /**
     * 将日期字符串转换为时间戳（精度到秒）
     * @param inputDate
     * @return
     */
    public static Long parseDateToTimeStamp(String inputDate) {
        Long timestamp = -1L;
        try {
            if (StringUtils.isNotBlank(inputDate)) {

                try {
                    if (inputDate.contains("T")) {
                        DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
                        timestamp = df.parse(inputDate).getTime() / 1000;
                    }
                } catch (Exception ex) {
                }

                if (timestamp == -1L) {
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    timestamp = format.parse(inputDate).getTime() / 1000;
                }
            }
        } catch (Exception e) {
        }
        return timestamp;
    }

    public static String full2Half(Object input) {
        if(input == null || "".equals(input)){
            return "";
        }
        char c[] = input.toString().toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == '\u3000') {
                c[i] = ' ';
            } else if (c[i] > '\uFF00' && c[i] < '\uFF5F') {
                c[i] = (char) (c[i] - 65248);

            }
        }
        String returnString = new String(c);

        return returnString;
    }

    /**
     * 返回按照审判日期排序的裁判文书列表
     * @param caseList
     * @return
     */
    public static List<JSONObject> getSortedCaseList(List<String> caseList, String provinceCode) {
        List<JSONObject> jsonObjectList = new ArrayList<>();
        if (caseList != null && caseList.size() > 0) {
            jsonObjectList =  caseList.stream()
                    .map(e -> {
                        try {
                            return JSONObject.parseObject(e);
                        } catch (Exception ex) {
                            return null;
                        }
                    })
                    .filter(e -> e != null)
                    .filter(e -> e.getString("province").equals(provinceCode))
                    .sorted(Comparator.comparing(e -> {
                        String judgeDate = e.getString("judgedate");
                        return StringUtils.isBlank(judgeDate) ? "" : judgeDate;
                    }))
                    .collect(Collectors.toList());
        }
        return jsonObjectList;
    }

    /**
     * 返回列表中按照审判日期排列的最早的一个文书的案件身份，若为空或"[]"则取下一个
     * @param caseList
     * @param provinceCode
     * @return
     */
    public static String getCaseRoleFromCaseList(List<String> caseList, String provinceCode) {
        String caseRole = new JSONArray().toJSONString();
        try {
            List<JSONObject> sortedCaseList = getSortedCaseList(caseList, provinceCode);
            if (sortedCaseList.size() > 0) {
                for (JSONObject caseJsonObj : sortedCaseList) {
                    String role = caseJsonObj.getString("caserole");
                    if (StringUtils.isNotBlank(role) && !role.equals("[]")) {
                        caseRole = role;
                        break;
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return caseRole;
    }


    /**
     * 从立案或开庭公告列表中提取并拼装案件身份，若为空或"[]"则取下一个
     * @param lianOrKtggList
     * @param type  1 -> 立案信息 2-> 开庭公告
     * @return
     */
    public static String getCaseRoleFromLianOrKtggList(List<String> lianOrKtggList, int type) {
        String caseRole = new JSONArray().toJSONString();
        try {
            if (lianOrKtggList != null && lianOrKtggList.size() > 0) {
                for (String lianOrKtggItem : lianOrKtggList) {
                    JSONObject lianOrKtggJsonObj = JSONObject.parseObject(lianOrKtggItem);
                    JSONArray prosecutorArray = lianOrKtggJsonObj.getJSONArray("prosecutorlistos");
                    JSONArray defendantArray = lianOrKtggJsonObj.getJSONArray("defendantlistos");
                    JSONArray caseRoleArray = new JSONArray();
                    buildCaseRole(type, prosecutorArray, caseRoleArray, "原告");

                    buildCaseRole(type, defendantArray, caseRoleArray, "被告");
                    caseRole = caseRoleArray.toJSONString();
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return caseRole;
    }

    private static void buildCaseRole(int type, JSONArray roleArray, JSONArray caseRoleArray, String caseRole) {
        if (roleArray != null && roleArray.size() > 0) {
            /**
             * [{\\\"KeyNo\\\":\\\"5f0587b86d2f67ec6b9f3fc0e6c43673\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"深圳市城市歌声娱乐有限公司\\\"}]
             */
            for (int i = 0; i < roleArray.size(); i++) {
                JSONObject jsonObject = roleArray.getJSONObject(i);
                if (jsonObject != null) {
                    JSONObject caseRoleJson = new JSONObject();
                    if (type == 1) {
                        caseRoleJson.put("P", jsonObject.getString("Name"));
                        caseRoleJson.put("N", jsonObject.getString("KeyNo"));
                        caseRoleJson.put("R", caseRole);
                        caseRoleJson.put("O", jsonObject.getInteger("Org"));
                    } else if (type == 2) {
                        caseRoleJson.put("P", jsonObject.getString("name"));
                        caseRoleJson.put("N", jsonObject.getString("keyno"));
                        caseRoleJson.put("R", caseRole);
                        caseRoleJson.put("O", jsonObject.getInteger("Org"));
                    }
                    caseRoleArray.add(caseRoleJson);
                }
            }
        }
    }


    /**
     * 从infoList中获取并返回LatestTimestamp最后的一个案号对应的审理程序
     *
     * @param infoList
     * @return
     */
    public static String getLatestTrialRoundFromInfoList(JSONArray infoList) {
        String caseNo = "";
        String trialRound = "";
        Long latestTimeStamp = -1L;

        Iterator iterator = infoList.iterator();
        while (iterator.hasNext()) {
            try {
                JSONObject jsonObject = (JSONObject) iterator.next();
                Long currentTimeStamp = jsonObject.getLong("LatestTimestamp");
                if (currentTimeStamp >= latestTimeStamp) {
                    latestTimeStamp = currentTimeStamp;
                    caseNo = jsonObject.getString("AnNo");
                    trialRound = jsonObject.getString("TrialRound");
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }

        String latestTrialRound = new ExtractCaseTrialRoundUDF().evaluate(caseNo);
        if (StringUtils.isBlank(latestTrialRound)) {
            latestTrialRound = trialRound;
        }
        return latestTrialRound;
    }

    /**
     * 提取诉讼双方当事人信息
     *
     * @param input
     * @param caseRole
     * @return
     */
    public static JSONArray getLitigantJSONArray(String input, String caseRole) {
        JSONArray litigantJsonArray = new JSONArray();
        try {
            Map<String, JSONObject> nameAndCaseRoleMap = new HashMap<>();
            JSONArray jsonArray = JSONArray.parseArray(caseRole);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject caseRoleJsonObj = jsonArray.getJSONObject(i);
                /**
                 * [{"P":"宁波东方电缆股份有限公司","R":"原告","N":"002f193a1039940a373750a3e1d3f994","O":0},
                 * {"P":"浙江凡心律师事务所","R":"代理律师事务所","N":"w244606064394cf429568b0d80139e80","O":4}]
                 */
                String name = CommonV2Util.full2Half(caseRoleJsonObj.getString("P"));

                JSONObject jsonObject = new JSONObject();
                jsonObject.put("Name", name);
                jsonObject.put("Role", CommonV2Util.full2Half(caseRoleJsonObj.getString("R")));
                jsonObject.put("KeyNo", caseRoleJsonObj.getString("N"));
                jsonObject.put("Org", caseRoleJsonObj.getInteger("O"));
                nameAndCaseRoleMap.put(name, jsonObject);
            }

            if (StringUtils.isNotBlank(input)) {
                for (String item : input.split(",")) {
                    if (!CommonV2Util.isKeyword(item)) {
                        JSONObject jsonObj = nameAndCaseRoleMap.get(CommonV2Util.full2Half(item));
                        if (jsonObj != null) {
                            litigantJsonArray.add(jsonObj);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return litigantJsonArray;
    }

    /**
     * 从破产重整维度中获取申请人/被申请人信息
     *
     * @param input
     * @param role  "申请人"/"被申请人"
     * @return
     */
    public static JSONArray getLitigantJSONArrayFromPccz(String input, String role) {
        JSONArray litigantJsonArray = new JSONArray();
        try {
            JSONArray jsonArray = JSONArray.parseArray(input);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject roleJsonObj = jsonArray.getJSONObject(i);

                JSONObject jsonObject = new JSONObject();
                jsonObject.put("Name", roleJsonObj.getString("Name"));
                jsonObject.put("Role", role);
                jsonObject.put("KeyNo", roleJsonObj.getString("KeyNo"));
                jsonObject.put("Org", roleJsonObj.getInteger("Org"));
                litigantJsonArray.add(jsonObject);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return litigantJsonArray;
    }

    /**
     * 根据KeyNo获取Org
     * 当keyNo为空时，根据name长度做二次判断：
     * 1 长度 > 4时，返回-1
     * 2 长度 <= 4时，返回-2
     *
     * @param keyNo
     * @param name
     * @return
     */
    public static int getOrgByKeyNo(String keyNo, String name) {
        if (StringUtils.isBlank(keyNo)) {
            return (name.length() > 4 ? -1 : -2);
        }

        int org = 0;
        if (keyNo.startsWith("s")) {
            org = 1;
        } else if (keyNo.startsWith("h")) {
            org = 3;
        } else if (keyNo.startsWith("t")) {
            org = 5;
        } else if (keyNo.startsWith("g") || keyNo.startsWith("x") || keyNo.startsWith("w") || keyNo.startsWith("j")) {
            org = 4;
        } else if (keyNo.startsWith("y")) {
            org = 7;
        } else if (keyNo.startsWith("o")) {
            org = 8;
        } else if (keyNo.startsWith("z")) {
            org = 9;
        } else if (keyNo.startsWith("p")) {
            org = 2;
        }
        return org;
    }

    /**
     * 时间节点文案信息的首字符为"|"时，剔除该字符
     *
     * @param dataType
     * @return
     */
    public static String getDataTypeWithoutTrialRound(String dataType) {
        if (StringUtils.isNotBlank(dataType) && dataType.startsWith("|")) {
            return dataType.substring(1);
        } else {
            return dataType;
        }
    }

    /**
     * 补全infoList中缺失的涉诉维度对象信息
     * @param jsonObj
     * @return
     */
    public static JSONObject addExternalFieldToJsonStruct(JSONObject jsonObj) {
        List<String> fieldNameList = Arrays.asList(
            "LianList", "FyggList", "KtggList", "SdggList", "CaseList", "SxList", "ZxList", "XgList", "PcczList",
            "ZbList", "XjpgList", "GqdjList", "HbcfList", "CfgsList", "CfxyList", "CfdfList"
        );
        for (String fieldName : fieldNameList) {
            if (!jsonObj.containsKey(fieldName)) {
                jsonObj.put(fieldName, new JSONArray());
            }
        }
        return jsonObj;
    }

    private static Long compareTimestamp(Long compareTimestamp, Long initTimestamp) {
        if (compareTimestamp == null || compareTimestamp == -1L) {
            return initTimestamp;
        } else {
            if (compareTimestamp > initTimestamp) {
                return compareTimestamp;
            } else {
                return initTimestamp;
            }
        }
    }

    /**
     * 从nameandkeyno字段中提取name信息并返回集合
     * @param itemList
     * @return
     */
    private static Set<String> getNameSetFromNameAndKeyNoField(List<String> itemList) {
        Set<String> nameSet = new HashSet<>();
        try {
            if (itemList != null && itemList.size() > 0) {
                for (String item : itemList) {
                    JSONObject jsonObj = JSONObject.parseObject(item);
                    if (jsonObj != null && jsonObj.containsKey("nameandkeyno")) {
                        JSONArray jsonArray = jsonObj.getJSONArray("nameandkeyno");
                        if (jsonArray != null) {
                            Iterator iterator = jsonArray.iterator();
                            while (iterator.hasNext()) {
                                JSONObject nameAndKeyNoJson = (JSONObject) iterator.next();
                                String name = nameAndKeyNoJson.getString("Name");
                                if (StringUtils.isNotBlank(name)) {
                                    nameSet.add(name);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return nameSet;
    }

    /**
     * 从破产重整详情中提取申请人和被申请人名信息并返回集合
     * @param pcczItem
     * @return
     */
    private static Set<String> getNameSetFromApplicantAndRespondentField(String pcczItem) {
        Set<String> nameSet = new HashSet<>();
        try {
            if (StringUtils.isNotBlank(pcczItem)) {
                JSONObject jsonObj = JSONObject.parseObject(pcczItem);
                if (jsonObj != null && jsonObj.containsKey("applicant") && jsonObj.containsKey("respondent")) {
                    String applicants = jsonObj.getString("applicant");
                    if (StringUtils.isNotBlank(applicants)) {
                        Arrays.stream(applicants.split(",")).filter(StringUtils::isNotBlank).forEach(e -> nameSet.add(e));
                    }
                    String respondents = jsonObj.getString("respondent");
                    if (StringUtils.isNotBlank(respondents)) {
                        Arrays.stream(respondents.split(",")).filter(StringUtils::isNotBlank).forEach(e -> nameSet.add(e));
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return nameSet;
    }

    /**
     * 从终本案件中提取当事人信息并返回集合
     * @param zbItem
     * @return
     */
    private static Set<String> getNameSetFromNameField(String zbItem) {
        Set<String> nameSet = new HashSet<>();
        try {
            if (StringUtils.isNotBlank(zbItem)) {
                JSONObject jsonObj = JSONObject.parseObject(zbItem);
                if (jsonObj != null && jsonObj.containsKey("name")) {
                    String name = jsonObj.getString("name");
                    if (StringUtils.isNotBlank(name)) {
                        nameSet.add(name);
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return nameSet;
    }

    private static Set<String> getNameSetFromCompanyNameField(String gqdjItem) {
        Set<String> nameSet = new HashSet<>();
        try {
            if (StringUtils.isNotBlank(gqdjItem)) {
                JSONObject jsonObj = JSONObject.parseObject(gqdjItem);
                if (jsonObj != null && jsonObj.containsKey("companyname")) {
                    String name = jsonObj.getString("companyname");
                    if (StringUtils.isNotBlank(name)) {
                        nameSet.add(name);
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return nameSet;
    }

    /**
     * 从xg维度中的companyname和personname字段汇总name信息并返回集合
     * @param xgList
     * @return
     */
    private static Set<String> getXgNameSetFromPersonNameAndCompanyName(List<String> xgList) {
        Set<String> nameSet = new HashSet<>();
        try {
            if (xgList != null && xgList.size() > 0) {
                for (String xgItem : xgList) {
                    JSONObject xgJson = JSONObject.parseObject(xgItem);
                    if (xgJson != null) {
                        String personName = xgJson.getString("personname");
                        if (StringUtils.isNotBlank(personName)) {
                            nameSet.add(personName);
                        }

                        String companyName = xgJson.getString("companyname");
                        if (StringUtils.isNotBlank(companyName)) {
                            nameSet.add(companyName);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return nameSet;
    }

    /**
     * 从lawSuitEntity对象中汇总所有的法院信息，递增排序后返回第一个法院名。（没有则返回空）
     * @param lawSuitEntity
     * @return
     */
    private static String getFirstCourtNameFromLawSuitEntity(LawSuitV2Entity lawSuitEntity) {
        String courtName = "";
        try {
            Set<String> courtNameSet = new HashSet<>();
            /**开庭公告法院提取*/
            List<String> ktggList = lawSuitEntity.getKtggList() != null ? lawSuitEntity.getKtggList() : new ArrayList<>();
            ktggList.stream().map(e -> {
                try {
                    return JSONObject.parseObject(e).getString("executegov");
                } catch (Exception ex) {
                    return "";
                }
            }).filter(StringUtils::isNotBlank).forEach(e-> {
                courtNameSet.add(e);
            });
            /**送达公告法院提取*/
            List<String> sdggList = lawSuitEntity.getSdggList() != null ? lawSuitEntity.getSdggList() : new ArrayList<>();
            sdggList.stream().map(e -> {
                try {
                    return JSONObject.parseObject(e).getString("courtname");
                } catch (Exception ex) {
                    return "";
                }
            }).filter(StringUtils::isNotBlank).forEach(e -> {
                courtNameSet.add(e);
            });
            /**法院公告法院提取*/
            List<String> fyggList = lawSuitEntity.getFyggList() != null ? lawSuitEntity.getFyggList() : new ArrayList<>();
            fyggList.stream().map(e -> {
                try {
                    return JSONObject.parseObject(e).getString("court");
                } catch (Exception ex) {
                    return "";
                }
            }).filter(StringUtils::isNotBlank).forEach(e -> {
                courtNameSet.add(e);
            });
            /**裁判文书法院提取*/
            List<String> caseList = lawSuitEntity.getCaseList() != null ? lawSuitEntity.getCaseList() : new ArrayList<>();
            caseList.stream().map(e -> {
                try {
                    return JSONObject.parseObject(e).getString("court");
                } catch (Exception ex) {
                    return "";
                }
            }).filter(StringUtils::isNotBlank).forEach(e -> {
                courtNameSet.add(e);
            });
            /**失信法院提取*/
            List<String> sxList = lawSuitEntity.getSxList() != null ? lawSuitEntity.getSxList() : new ArrayList<>();
            sxList.stream().map(e -> {
                try {
                    return JSONObject.parseObject(e).getString("executegov");
                } catch (Exception ex) {
                    return "";
                }
            }).filter(StringUtils::isNotBlank).forEach(e -> {
                courtNameSet.add(e);
            });
            /**执行法院提取*/
            List<String> zxList = lawSuitEntity.getZxList() != null ? lawSuitEntity.getZxList() : new ArrayList<>();
            zxList.stream().map(e -> {
                try {
                    return JSONObject.parseObject(e).getString("executegov");
                } catch (Exception ex) {
                    return "";
                }
            }).filter(StringUtils::isNotBlank).forEach(e -> {
                courtNameSet.add(e);
            });
            /**破产重整法院提取*/
            List<String> pcczList = lawSuitEntity.getPcczList() != null ? lawSuitEntity.getPcczList() : new ArrayList<>();
            pcczList.stream().map(e -> {
                try {
                    return JSONObject.parseObject(e).getString("courtname");
                } catch (Exception ex) {
                    return "";
                }
            }).filter(StringUtils::isNotBlank).forEach(e -> {
                courtNameSet.add(e);
            });
            /**终本案件法院提取*/
            List<String> zbList = lawSuitEntity.getZbList() != null ? lawSuitEntity.getZbList() : new ArrayList<>();
            zbList.stream().map(e -> {
                try {
                    return JSONObject.parseObject(e).getString("court");
                } catch (Exception ex) {
                    return "";
                }
            }).filter(StringUtils::isNotBlank).forEach(e -> {
                courtNameSet.add(e);
            });
            /**询价评估法院提取*/
            List<String> xjpgList = lawSuitEntity.getXjpgList() != null ? lawSuitEntity.getXjpgList() : new ArrayList<>();
            xjpgList.stream().map(e -> {
                try {
                    return JSONObject.parseObject(e).getString("courtname");
                } catch (Exception ex) {
                    return "";
                }
            }).filter(StringUtils::isNotBlank).forEach(e -> {
                courtNameSet.add(e);
            });

            if (courtNameSet != null && courtNameSet.size() > 0) {
                List<String> resCourtList = courtNameSet.stream().sorted().limit(1).collect(Collectors.toList());
                courtName = resCourtList.get(0);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return courtName;
    }

    public static void main(String[] args) {
        String inputDate = "1528819200";
        Long resTimeStamp = parseDateToTimeStamp(inputDate);
        System.out.println(resTimeStamp);
    }
}