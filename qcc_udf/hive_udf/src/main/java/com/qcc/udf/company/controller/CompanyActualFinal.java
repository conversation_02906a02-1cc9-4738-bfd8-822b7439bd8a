package com.qcc.udf.company.controller;

import java.util.List;

public class CompanyActualFinal {
    public CompanyActualController actualControl;
    public CompanyActualController controlPower;
    public List<CompanyPartner> groupList;

    public CompanyActualController getActualControl() {
        return actualControl;
    }

    public void setActualControl(CompanyActualController actualControl) {
        this.actualControl = actualControl;
    }

    public CompanyActualController getControlPower() {
        return controlPower;
    }

    public void setControlPower(CompanyActualController controlPower) {
        this.controlPower = controlPower;
    }

    public List<CompanyPartner> getGroupList() {
        return groupList;
    }

    public void setGroupList(List<CompanyPartner> groupList) {
        this.groupList = groupList;
    }
}
