package com.qcc.udf.common;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MaskColumn extends UDF {
    public static final String SPLIT_REGEX = "[。,;]";

    //身份证正则
    public static final String ID_CARD_REGEX = "[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]";
    public static final Pattern ID_CARD_NUMBER_PATTERN = Pattern.compile(ID_CARD_REGEX);

    public static String evaluate(String textString, String type) {
        return replaceIDcard(textString, type);
    }

    /**
     * 删除文本中的额证件号字段
     *
     * @param content
     * @param type    0删除1脱敏
     * @return
     */
    public static String replaceIDcard(String content, String type) {
        if (StringUtils.isBlank(content)) {
            return content;
        }
        if (type.equals("1")) {
            content = MaskBaseRegexUtil.handlerIdCard(content);
            return content;
        }
        content = content.replace("(", "（")
                .replace(")", "）")
                .replace("，", ",")
                .replaceAll("被执行人", "").replace("汉族", "");
        Matcher m = ID_CARD_NUMBER_PATTERN.matcher(content);
        while (m.find()) {
            String id = m.group();
            content = content.replace(id, "");
            content = content
                    .replace("居民身份证号码", "")
                    .replace("公民身份证号码", "")
                    .replace("公民身份号码为", "")
                    .replace("公民身份号码", "")
                    .replaceAll("身份证号码|身份号码|居民身份号码", "")
                    .replace("居民身份证", "")
                    .replace("身份证编号", "")
                    .replace("证件号码", "")
                    .replace("身份证号", "")
                    .replace("身份证", "")
                    .replaceAll("[:：]", "")
                    .replace("（）", "")
                    .replace("公民", "");
        }

        Pattern pattern = Pattern.compile("(?<!\\d)(?:(?:1[3456789]\\d{9}))(?!\\d)");
        Matcher matcher = pattern.matcher(content);
        if (!content.contains("*")) {
            while (matcher.find()) {
                String phoneNum = matcher.group();
                content = content.replace(phoneNum, "");
            }
        }
        String[] contentArr = content.split(SPLIT_REGEX);
        for (String str : contentArr) {
            if (StringUtils.isBlank(str)) {
                continue;
            }
            String regexpContent = str.replaceAll("(通讯地址|家庭地址|家庭住址|户籍地|住所地|住址|地址|现住)(.*[?<=市|县|区|室|号|村]).*", "");
            content = content.replace(str, regexpContent);
        }
        content = birthEncrypt(content);
        content=content.replace(",,", ",");
        content = content.startsWith(",") ? content.length() >= 2 ? content.substring(1) : content.replace(",", "") : content;
        content = content.endsWith(",") ? content.length() >= 2 ? content.substring(0, content.length() - 1) : content.replace(",", "") : content;
        return content;
    }

    private final static String REGEXP_BIRTH_ALL_DAY = "(\\d{2,4}年|[零一二三四五六七八九十\\d]{4}年)(\\d{0,2}月?|[一二三四五六七八九十]{0,2}月?)(\\d{0,2}|[零一二三四五六七八九十\\d]{0,3})";
    public static final Pattern REGEXP_BIRTH_ALL_DAY_PATTERN = Pattern.compile(REGEXP_BIRTH_ALL_DAY);

    private static String birthEncrypt(String content) {
        if (Pattern.compile(REGEXP_BIRTH_ALL_DAY).matcher(content).find()) {
            content = replaceAll("生于", content, "");
            content = replaceAll("", content, "日出生");
            content = replaceAll("", content, "号出生");
            content = replaceAll("", content, "日生(?!产)");
            content = replaceAll("", content, "号生");
            content = replaceAll("", content, "出生");
            content = replaceAll("", content, "生(?!产)");
            content = replaceAll("出生日期", content, "");
        }
        return content;
    }

    private static String replaceAll(String prefix, String content, String suffix) {
        Matcher m = REGEXP_BIRTH_ALL_DAY_PATTERN.matcher(content);
        while (m.find()) {
            content = content.replaceAll(prefix + REGEXP_BIRTH_ALL_DAY + suffix, "");
        }
        return content;
    }

    // The entry point of the UDF
    public static void main(String[] args) {
        Matcher 当事人于2012年09月01日生产的 = Pattern.compile("\\d+日生").matcher("当事人于2012年09月01日生产的");
        while(当事人于2012年09月01日生产的.find()){
            System.out.println(当事人于2012年09月01日生产的.group(0));
        }

        System.out.println(replaceIDcard(" 审理法院: 沙河市人民法院 开始时间: 10时30分 开庭地点: 第一审判庭 开庭日期(听证日期): 2021年08月10日 承办人: 刘淑萍 \n" +
                "案件名称: 刘现住贪污罪一案 案号: (2021)冀0582刑初128号 案由: 贪污罪 。\",\"Result\":{\"Caseno\":\"(2021)冀0582刑初128号","1"));
        System.out.println(replaceIDcard("当事人于2012年09月01日生产的","0"));











    }
}

