package com.qcc.udf.liu;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.cpws.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;
import parquet.Strings;

import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class getSxAmt extends UDF {
    public static String evaluate(String id, String param) {
        JSONArray result = new JSONArray();

        if (StringUtils.isNotEmpty(param)) {
            // 获取被执行/终本的所有数据
            JSONArray array = JSONObject.parseObject(param).getJSONArray("InfoList");
            if (array != null && !array.isEmpty()) {
                Iterator<Object> it = array.iterator();
                while (it.hasNext()) {
                    JSONObject json = (JSONObject) it.next();
                    // 获取限高
                    JSONArray xgArray = json.getJSONArray("XgList");
                    if (xgArray != null && !xgArray.isEmpty()) {
                        Iterator<Object> itXg = xgArray.iterator();
                        while (itXg.hasNext()) {
                            JSONObject xgJson = (JSONObject) itXg.next();
                            JSONArray nameAndKeyNoArray = xgJson.getJSONArray("XglNameAndKeyNo");
                            Iterator<Object> itName = nameAndKeyNoArray.iterator();
                            while (itName.hasNext()) {
                                JSONObject nameAndKeyNo = (JSONObject) itName.next();
                                String keyNo = nameAndKeyNo.getString("KeyNo") == null ? "" : nameAndKeyNo.getString("KeyNo");
                                String name = nameAndKeyNo.getString("Name") == null ? "" : CommonUtil.full2Half(nameAndKeyNo.getString("Name"));

                                String key = keyNo.concat(name);
                                Set<String> idSetSx = getInfoFromArray(json.getJSONArray("SxList"), key);
                                Set<String> idSetZb = getInfoFromArray(json.getJSONArray("ZbList"), key);

                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put("XgId", xgJson.getString("Id"));
                                jsonObject.put("SxId", Strings.join(idSetSx, ","));
                                jsonObject.put("ZbId", Strings.join(idSetZb, ","));

                                if (idSetSx.size() + idSetZb.size() > 0){
                                    result.add(jsonObject);
                                }
                            }

                        }
                    }
                }
            }
        }

        return result.toString();
    }

    public static Set<String> getInfoFromArray(JSONArray array, String key) {
        Set<String> idSet = new LinkedHashSet<>();
        if (array != null && !array.isEmpty()) {
            Iterator<Object> itXg = array.iterator();
            while (itXg.hasNext()) {
                JSONObject xgJson = (JSONObject) itXg.next();
                JSONArray nameAndKeyNoArray = xgJson.getJSONArray("NameAndKeyNo");
                Iterator<Object> itName = nameAndKeyNoArray.iterator();
                while (itName.hasNext()) {
                    JSONObject nameAndKeyNo = (JSONObject) itName.next();
                    String keyNo = nameAndKeyNo.getString("KeyNo") == null ? "" : nameAndKeyNo.getString("KeyNo");
                    String name = nameAndKeyNo.getString("Name") == null ? "" : CommonUtil.full2Half(nameAndKeyNo.getString("Name"));

                    if ((key.contains(keyNo) && StringUtils.isNotEmpty(keyNo)) || key.contains(name)){
                        idSet.add(xgJson.getString("Id"));
                    }
                }
            }
        }
        return idSet;
    }

    public static void main(String[] args) {
        System.out.println(evaluate("id123", "{\"KtggCnt\":0,\"LastestDateType\":\"首次执行|案件终本日期\",\"XjpgCnt\":0,\"ZxCnt\":5,\"CfgsCnt\":0,\"LastestDate\":1557417600,\"AmtInfo\":{\"p8eb654fa81cd2b8f87b532d77392d9a\":{\"Type\":\"未履行金额\",\"Amt\":\"17527\",\"IsValid\":\"1\"},\"pa3c140a448cb6fdda60437ea2defe06\":{\"Type\":\"未履行金额\",\"Amt\":\"17527\",\"IsValid\":\"1\"},\"c107187094b295803e5e7400a2e95e46\":{\"Type\":\"案件金额\",\"Amt\":\"43460.32\",\"IsValid\":\"1\"},\"p025604f6cc2d9b923e02f9b4a56074c\":{\"Type\":\"未履行金额\",\"Amt\":\"17527\",\"IsValid\":\"1\"}},\"EarliestDate\":1499184000,\"Source\":\"OT\",\"AnnoCnt\":2,\"EarliestDateType\":\"民事一审|立案日期\",\"XzcfCnt\":0,\"LawyerIds\":\",22ec6074ecc7daf6c5e225859ce72785,99e810f113f53c0dc28df7eb4afb3c0b,c9753fa2dfbab7addd231e8989a117e3,ff381760c314de31c1da65f460f25545\",\"CompanyKeywords\":\"c107187094b295803e5e7400a2e95e46,p025604f6cc2d9b923e02f9b4a56074c,p8eb654fa81cd2b8f87b532d77392d9a,pa3c140a448cb6fdda60437ea2defe06,中国邮政储蓄银行股份有限公司洛阳市分行,杨宁,杨玉发,王晓丹,申军平,马元英\",\"AnNoList\":\"（2017）豫0303民初2539号,（2018）豫0303执2240号\",\"GqdjCnt\":0,\"XgCnt\":5,\"Tags\":\"1,2,3,4,6,12\",\"FyggCnt\":0,\"ZbCnt\":5,\"LatestTrialRound\":\"首次执行\",\"CfdfCnt\":0,\"CaseName\":\"中国邮政储蓄银行股份有限公司洛阳市分行与杨宁,杨玉发,王晓丹等金融借款合同纠纷的案件\",\"CfxyCnt\":0,\"SxCnt\":5,\"Province\":\"HEN\",\"GroupId\":\"441c0627194e9afe8c257124a3038953\",\"LianCnt\":2,\"CaseNameClean\":\"中国邮政储蓄银行股份有限公司洛阳市分行与杨宁,杨玉发,王晓丹等金融借款合同纠纷的案件\",\"CaseCnt\":2,\"HbcfCnt\":0,\"PcczCnt\":0,\"Type\":1,\"CaseType\":\"执行案件,民事案件\",\"ProcuratorateList\":\"\",\"CaseRole\":\"[{\\\"D\\\":\\\"一审原告,首次执行申请执行人\\\",\\\"N\\\":\\\"c107187094b295803e5e7400a2e95e46\\\",\\\"O\\\":0,\\\"P\\\":\\\"中国邮政储蓄银行股份有限公司洛阳市分行\\\",\\\"R\\\":\\\"原告\\\",\\\"RL\\\":[{\\\"JR\\\":\\\"1\\\",\\\"LR\\\":\\\"3\\\",\\\"R\\\":\\\"原告\\\",\\\"T\\\":\\\"一审\\\"},{\\\"R\\\":\\\"申请执行人\\\",\\\"T\\\":\\\"首次执行\\\"}]},{\\\"D\\\":\\\"一审被告,首次执行被执行人\\\",\\\"N\\\":\\\"p025604f6cc2d9b923e02f9b4a56074c\\\",\\\"O\\\":2,\\\"P\\\":\\\"杨宁\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"JR\\\":\\\"12\\\",\\\"LR\\\":\\\"4\\\",\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"},{\\\"JR\\\":\\\"28\\\",\\\"LR\\\":\\\"21\\\",\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"首次执行\\\"}]},{\\\"D\\\":\\\"一审被告,首次执行被执行人\\\",\\\"N\\\":\\\"p8eb654fa81cd2b8f87b532d77392d9a\\\",\\\"O\\\":2,\\\"P\\\":\\\"杨玉发\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"JR\\\":\\\"12\\\",\\\"LR\\\":\\\"4\\\",\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"},{\\\"JR\\\":\\\"28\\\",\\\"LR\\\":\\\"21\\\",\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"首次执行\\\"}]},{\\\"D\\\":\\\"一审被告,首次执行被执行人\\\",\\\"N\\\":\\\"pa3c140a448cb6fdda60437ea2defe06\\\",\\\"O\\\":2,\\\"P\\\":\\\"王晓丹\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"JR\\\":\\\"12\\\",\\\"LR\\\":\\\"4\\\",\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"},{\\\"JR\\\":\\\"28\\\",\\\"LR\\\":\\\"21\\\",\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"首次执行\\\"}]},{\\\"D\\\":\\\"一审被告,首次执行被执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2,\\\"P\\\":\\\"申军平\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"JR\\\":\\\"12\\\",\\\"LR\\\":\\\"4\\\",\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"},{\\\"JR\\\":\\\"28\\\",\\\"LR\\\":\\\"21\\\",\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"首次执行\\\"}]},{\\\"D\\\":\\\"一审被告,首次执行被执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2,\\\"P\\\":\\\"马元英\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"JR\\\":\\\"12\\\",\\\"LR\\\":\\\"4\\\",\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"},{\\\"JR\\\":\\\"28\\\",\\\"LR\\\":\\\"21\\\",\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"首次执行\\\"}]}]\",\"CaseReason\":\"金融借款合同纠纷\",\"CourtList\":\"河南省洛阳市西工区人民法院\",\"Id\":\"f009d5b4c3eed56e84d1bace942639f8\",\"InfoList\":[{\"Defendant\":[{\"KeyNo\":\"p025604f6cc2d9b923e02f9b4a56074c\",\"Role\":\"被告\",\"Org\":2,\"LR\":\"4\",\"JR\":\"12\",\"Name\":\"杨宁\"},{\"KeyNo\":\"p8eb654fa81cd2b8f87b532d77392d9a\",\"Role\":\"被告\",\"Org\":2,\"LR\":\"4\",\"JR\":\"12\",\"Name\":\"杨玉发\"},{\"KeyNo\":\"pa3c140a448cb6fdda60437ea2defe06\",\"Role\":\"被告\",\"Org\":2,\"LR\":\"4\",\"JR\":\"12\",\"Name\":\"王晓丹\"},{\"KeyNo\":\"\",\"Role\":\"被告\",\"Org\":-2,\"LR\":\"4\",\"JR\":\"12\",\"Name\":\"申军平\"},{\"KeyNo\":\"\",\"Role\":\"被告\",\"Org\":-2,\"LR\":\"4\",\"JR\":\"12\",\"Name\":\"马元英\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"河南省洛阳市西工区人民法院\",\"LatestTimestamp\":1511222400,\"ZxList\":[],\"XzcffList\":[],\"CfdfList\":[],\"HbcfList\":[],\"CaseList\":[{\"CaseType\":\"民事判决书\",\"JudgeDate\":1511222400,\"Amt\":\"43460.32\",\"Id\":\"7d8b151f02ea5d03707332f4ee93c7f60\",\"ResultType\":\"判决结果\",\"DocType\":\"判决日期\",\"IsValid\":1,\"Result\":\"限被告杨宁、王晓丹于本判决生效后十日内偿还原告中国邮政储蓄银行股份有限公司洛阳市分行截止2017年6月1日借款本金19543.16元，拖欠利息、罚息4374元，拖欠金额合计23917.16元； 限被告杨宁、王晓丹于本判决生效后十日内支付原告中国邮政储蓄银行股份有限公司洛阳市分行借款本金19543.16元自2017年6月2日至实际履行之日止产生的利息和罚息（具体数额以中国邮政储蓄银行股份有限公司洛阳市分行电脑自动生成的数额为准）； 三、被告申军平、马元英、杨玉发对被告杨宁、王晓丹上述第一、二判项范围内的债务承担连带清偿责任。\",\"ShieldCaseFlag\":0}],\"TrialRound\":\"民事一审\",\"Prosecutor\":[{\"KeyNo\":\"c107187094b295803e5e7400a2e95e46\",\"Role\":\"原告\",\"Org\":0,\"LR\":\"3\",\"JR\":\"1\",\"LawFirmList\":[{\"P\":\"河南南云律师事务所\",\"LY\":[{\"P\":\"王璐璐\",\"R\":\"委托诉讼代理人\",\"N\":\"99e810f113f53c0dc28df7eb4afb3c0b\"},{\"P\":\"许佳鹏\",\"R\":\"委托诉讼代理人\",\"N\":\"22ec6074ecc7daf6c5e225859ce72785\"}],\"N\":\"w0e7adcd0c207c32c7701189b037ba06\",\"O\":4}],\"Name\":\"中国邮政储蓄银行股份有限公司洛阳市分行\"}],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2017）豫0303民初2539号\",\"CaseType\":\"民事案件\",\"LianList\":[{\"LianDate\":1499184000,\"NameAndKeyNo\":[{\"KeyNo\":\"c107187094b295803e5e7400a2e95e46\",\"Org\":0,\"Name\":\"中国邮政储蓄银行股份有限公司洛阳市分行\"},{\"KeyNo\":\"p8eb654fa81cd2b8f87b532d77392d9a\",\"Org\":2,\"Name\":\"杨玉发\"},{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"马元英\"},{\"KeyNo\":\"p025604f6cc2d9b923e02f9b4a56074c\",\"Org\":2,\"Name\":\"杨宁\"},{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"申军平\"},{\"KeyNo\":\"pa3c140a448cb6fdda60437ea2defe06\",\"Org\":2,\"Name\":\"王晓丹\"}],\"Id\":\"7d8b151f02ea5d03707332f4ee93c7f6\",\"IsValid\":1}],\"XgList\":[],\"CaseReason\":\"金融借款合同纠纷\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]},{\"Defendant\":[{\"KeyNo\":\"p025604f6cc2d9b923e02f9b4a56074c\",\"Role\":\"被执行人\",\"Org\":2,\"LR\":\"21\",\"JR\":\"28\",\"Name\":\"杨宁\"},{\"KeyNo\":\"p8eb654fa81cd2b8f87b532d77392d9a\",\"Role\":\"被执行人\",\"Org\":2,\"LR\":\"21\",\"JR\":\"28\",\"Name\":\"杨玉发\"},{\"KeyNo\":\"pa3c140a448cb6fdda60437ea2defe06\",\"Role\":\"被执行人\",\"Org\":2,\"LR\":\"21\",\"JR\":\"28\",\"Name\":\"王晓丹\"},{\"KeyNo\":\"\",\"Role\":\"被执行人\",\"Org\":-2,\"LR\":\"21\",\"JR\":\"28\",\"Name\":\"申军平\"},{\"KeyNo\":\"\",\"Role\":\"被执行人\",\"Org\":-2,\"LR\":\"21\",\"JR\":\"28\",\"Name\":\"马元英\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"河南省洛阳市西工区人民法院\",\"LatestTimestamp\":1557417600,\"ZxList\":[{\"LianDate\":1542038400,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"c107187094b295803e5e7400a2e95e46\",\"Org\":0,\"Name\":\"中国邮政储蓄银行股份有限公司洛阳市分行\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"p025604f6cc2d9b923e02f9b4a56074c\",\"Org\":2,\"Name\":\"杨宁\"}],\"Id\":\"215b038ca93e491a56b9df8cc2e2e29d1\",\"Biaodi\":\"24574\",\"IsValid\":0},{\"LianDate\":1542038400,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"c107187094b295803e5e7400a2e95e46\",\"Org\":0,\"Name\":\"中国邮政储蓄银行股份有限公司洛阳市分行\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"马元英\"}],\"Id\":\"405b03b06021478935636ba9fad7be891\",\"Biaodi\":\"24574\",\"IsValid\":0},{\"LianDate\":1542038400,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"c107187094b295803e5e7400a2e95e46\",\"Org\":0,\"Name\":\"中国邮政储蓄银行股份有限公司洛阳市分行\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"p8eb654fa81cd2b8f87b532d77392d9a\",\"Org\":2,\"Name\":\"杨玉发\"}],\"Id\":\"56fb6c4dc04439dfecdf0f0d1b9cb9711\",\"Biaodi\":\"24574\",\"IsValid\":0},{\"LianDate\":1542038400,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"c107187094b295803e5e7400a2e95e46\",\"Org\":0,\"Name\":\"中国邮政储蓄银行股份有限公司洛阳市分行\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"申军平\"}],\"Id\":\"a013ca49847306a5014554d2d7309e011\",\"Biaodi\":\"24574\",\"IsValid\":0},{\"LianDate\":1542038400,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"c107187094b295803e5e7400a2e95e46\",\"Org\":0,\"Name\":\"中国邮政储蓄银行股份有限公司洛阳市分行\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"pa3c140a448cb6fdda60437ea2defe06\",\"Org\":2,\"Name\":\"王晓丹\"}],\"Id\":\"ce1c28ffa9b2d6874d6c32f325a8ebd61\",\"Biaodi\":\"24574\",\"IsValid\":0}],\"XzcffList\":[],\"CfdfList\":[],\"HbcfList\":[],\"CaseList\":[{\"CaseType\":\"执行裁定书\",\"JudgeDate\":1557331200,\"Amt\":\"\",\"Id\":\"b0b07d0c726f16a943645aa0f7274a680\",\"ResultType\":\"裁定结果\",\"DocType\":\"裁定日期\",\"IsValid\":1,\"Result\":\"终结本次执行程序。\",\"ShieldCaseFlag\":0}],\"TrialRound\":\"首次执行\",\"Prosecutor\":[{\"KeyNo\":\"c107187094b295803e5e7400a2e95e46\",\"Role\":\"申请执行人\",\"Org\":0,\"Name\":\"中国邮政储蓄银行股份有限公司洛阳市分行\"}],\"ZbList\":[{\"FailureAct\":\"17527\",\"ExecuteObject\":\"24574\",\"JudgeDate\":1557417600,\"NameAndKeyNo\":[{\"KeyNo\":\"p025604f6cc2d9b923e02f9b4a56074c\",\"Org\":2,\"Name\":\"杨宁\"}],\"Id\":\"215b038ca93e491a56b9df8cc2e2e29d\",\"IsValid\":1},{\"FailureAct\":\"17527\",\"ExecuteObject\":\"24574\",\"JudgeDate\":1557417600,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"马元英\"}],\"Id\":\"405b03b06021478935636ba9fad7be89\",\"IsValid\":1},{\"FailureAct\":\"17527\",\"ExecuteObject\":\"24574\",\"JudgeDate\":1557417600,\"NameAndKeyNo\":[{\"KeyNo\":\"p8eb654fa81cd2b8f87b532d77392d9a\",\"Org\":2,\"Name\":\"杨玉发\"}],\"Id\":\"56fb6c4dc04439dfecdf0f0d1b9cb971\",\"IsValid\":1},{\"FailureAct\":\"17527\",\"ExecuteObject\":\"24574\",\"JudgeDate\":1557417600,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"申军平\"}],\"Id\":\"a013ca49847306a5014554d2d7309e01\",\"IsValid\":1},{\"FailureAct\":\"17527\",\"ExecuteObject\":\"24574\",\"JudgeDate\":1557417600,\"NameAndKeyNo\":[{\"KeyNo\":\"pa3c140a448cb6fdda60437ea2defe06\",\"Org\":2,\"Name\":\"王晓丹\"}],\"Id\":\"ce1c28ffa9b2d6874d6c32f325a8ebd6\",\"IsValid\":1}],\"ExecuteNo\":\"（2017）豫0303民初2539号\",\"SxList\":[{\"PublishDate\":1554652800,\"ActionType\":\"违反财产报告制度\",\"NameAndKeyNo\":[{\"KeyNo\":\"p025604f6cc2d9b923e02f9b4a56074c\",\"Org\":2,\"Name\":\"杨宁\"}],\"ExecuteStatus\":\"全部未履行\",\"Id\":\"215b038ca93e491a56b9df8cc2e2e29d2\",\"IsValid\":0},{\"PublishDate\":1554652800,\"ActionType\":\"违反财产报告制度\",\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"马元英\"}],\"ExecuteStatus\":\"全部未履行\",\"Id\":\"405b03b06021478935636ba9fad7be892\",\"IsValid\":0},{\"PublishDate\":1546444800,\"ActionType\":\"违反财产报告制度\",\"NameAndKeyNo\":[{\"KeyNo\":\"p8eb654fa81cd2b8f87b532d77392d9a\",\"Org\":2,\"Name\":\"杨玉发\"}],\"ExecuteStatus\":\"全部未履行\",\"Id\":\"56fb6c4dc04439dfecdf0f0d1b9cb9712\",\"IsValid\":0},{\"PublishDate\":1546444800,\"ActionType\":\"违反财产报告制度\",\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"申军平\"}],\"ExecuteStatus\":\"全部未履行\",\"Id\":\"a013ca49847306a5014554d2d7309e012\",\"IsValid\":0},{\"PublishDate\":1546444800,\"ActionType\":\"违反财产报告制度\",\"NameAndKeyNo\":[{\"KeyNo\":\"pa3c140a448cb6fdda60437ea2defe06\",\"Org\":2,\"Name\":\"王晓丹\"}],\"ExecuteStatus\":\"全部未履行\",\"Id\":\"ce1c28ffa9b2d6874d6c32f325a8ebd62\",\"IsValid\":0}],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2018）豫0303执2240号\",\"CaseType\":\"执行案件\",\"LianList\":[{\"LianDate\":1542038400,\"NameAndKeyNo\":[{\"KeyNo\":\"c107187094b295803e5e7400a2e95e46\",\"Org\":0,\"Name\":\"中国邮政储蓄银行股份有限公司洛阳市分行\"},{\"KeyNo\":\"p8eb654fa81cd2b8f87b532d77392d9a\",\"Org\":2,\"Name\":\"杨玉发\"},{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"马元英\"},{\"KeyNo\":\"p025604f6cc2d9b923e02f9b4a56074c\",\"Org\":2,\"Name\":\"杨宁\"},{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"申军平\"},{\"KeyNo\":\"pa3c140a448cb6fdda60437ea2defe06\",\"Org\":2,\"Name\":\"王晓丹\"}],\"Id\":\"b0b07d0c726f16a943645aa0f7274a68\",\"IsValid\":1}],\"XgList\":[{\"PublishDate\":1554652800,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"c107187094b295803e5e7400a2e95e46\",\"Org\":0,\"Name\":\"中国邮政储蓄银行股份有限公司洛阳市分行\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"p025604f6cc2d9b923e02f9b4a56074c\",\"Org\":2,\"Name\":\"杨宁\"}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"p025604f6cc2d9b923e02f9b4a56074c\",\"Org\":2,\"Name\":\"杨宁\"}],\"Id\":\"01079194ee311419da745b41a7a554e0\",\"CompanyInfo\":[{\"KeyNo\":\"p025604f6cc2d9b923e02f9b4a56074c\",\"Org\":2,\"Name\":\"杨宁\"}],\"GlNameAndKeyNo\":[],\"IsValid\":1},{\"PublishDate\":1546444800,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"c107187094b295803e5e7400a2e95e46\",\"Org\":0,\"Name\":\"中国邮政储蓄银行股份有限公司洛阳市分行\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"pa3c140a448cb6fdda60437ea2defe06\",\"Org\":2,\"Name\":\"王晓丹\"}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"pa3c140a448cb6fdda60437ea2defe06\",\"Org\":2,\"Name\":\"王晓丹\"}],\"Id\":\"451ba0720162bcac76e9bcea90f93cab\",\"CompanyInfo\":[{\"KeyNo\":\"pa3c140a448cb6fdda60437ea2defe06\",\"Org\":2,\"Name\":\"王晓丹\"}],\"GlNameAndKeyNo\":[],\"IsValid\":1},{\"PublishDate\":1554652800,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"c107187094b295803e5e7400a2e95e46\",\"Org\":0,\"Name\":\"中国邮政储蓄银行股份有限公司洛阳市分行\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"马元英\"}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"马元英\"}],\"Id\":\"70dff686049cf14b11be22914f817a40\",\"CompanyInfo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"马元英\"}],\"GlNameAndKeyNo\":[],\"IsValid\":1},{\"PublishDate\":1546444800,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"c107187094b295803e5e7400a2e95e46\",\"Org\":0,\"Name\":\"中国邮政储蓄银行股份有限公司洛阳市分行\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"p8eb654fa81cd2b8f87b532d77392d9a\",\"Org\":2,\"Name\":\"杨玉发\"}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"p8eb654fa81cd2b8f87b532d77392d9a\",\"Org\":2,\"Name\":\"杨玉发\"}],\"Id\":\"85a2f25b7441ee990564aa36b1dbaa3a\",\"CompanyInfo\":[{\"KeyNo\":\"p8eb654fa81cd2b8f87b532d77392d9a\",\"Org\":2,\"Name\":\"杨玉发\"}],\"GlNameAndKeyNo\":[],\"IsValid\":1},{\"PublishDate\":1546444800,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"c107187094b295803e5e7400a2e95e46\",\"Org\":0,\"Name\":\"中国邮政储蓄银行股份有限公司洛阳市分行\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"申军平\"}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"申军平\"}],\"Id\":\"f9967a6a42279b55ce01987fe8b8fc40\",\"CompanyInfo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"申军平\"}],\"GlNameAndKeyNo\":[],\"IsValid\":1}],\"CaseReason\":\"借款合同纠纷案件执行\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]}],\"SdggCnt\":0}"));
    }
}
