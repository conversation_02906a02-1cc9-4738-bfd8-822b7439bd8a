package com.qcc.udf.casesearch_v3.entity.tmp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 案号实体
 * <AUTHOR>
 * @date 2021年08月03日 11:23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CaseNoEntity {
    /**
     * 案号
     */
    private String caseNo;
    /**
     * 权重
     */
    private int score;

    public int getScore() {
        if(caseNo.contains("执")){
            return 0;
        }else{
            return 1;
        }
    }
}
