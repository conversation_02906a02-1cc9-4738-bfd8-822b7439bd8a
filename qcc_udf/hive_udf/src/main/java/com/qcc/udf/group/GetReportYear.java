package com.qcc.udf.group;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class GetReportYear extends UDF {
//    public static void main(String[] args) {
//        String msg = "2021年度报告";
//        int result = evaluate(msg);
//        System.out.printf(result + "");
//    }

    public static int evaluate(String year) {
        try {
            if (StringUtils.isEmpty(year)) {
                return 0;
            }

            List<String> strDate = RegexHelper.getGlobalRegex("(?<key>[0-9]+)", year);
            if (CollectionUtils.isNotEmpty(strDate)) {
                return parseToInt(strDate.get(0), 0);
            } else {
                return 0;
            }
        } catch (Exception e) {
        }
        return 0;
    }

    public static int parseToInt(Object val, int defaultValue) {
        if (val == null) {
            return defaultValue;
        } else {
            try {
                int num = Integer.parseInt(val.toString());
                return num;
            } catch (Exception var4) {
                return defaultValue;
            }
        }
    }
}