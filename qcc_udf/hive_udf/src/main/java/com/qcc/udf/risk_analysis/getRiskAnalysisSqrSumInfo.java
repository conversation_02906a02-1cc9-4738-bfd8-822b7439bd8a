package com.qcc.udf.risk_analysis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.qcc.udf.court_notice.anUtils.MD5Util;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class getRiskAnalysisSqrSumInfo extends UDF {

	//限制详情count数
	private static final Integer LIMIT_COUNT = 100;
	//分隔符
	private static final String SPLIT_KEY = ",";

	public String evaluate(String keyNo, List<String> xginfo, List<String> sxinfo, List<String> zxinfo, List<String> zbinfo) {
		// 全量数据
		JSONArray array = new JSONArray();
		// 限高
		editXgInfo(keyNo, xginfo, array);
		// 失信
		editSxInfo(keyNo, sxinfo, array);
		// 被执行
		editZxInfo(keyNo, zxinfo, array);
		// 获取终本信息
		editZbInfo(keyNo, zbinfo, array);

		// 全维度统计
		editAllInfo(keyNo, array);
		return JSON.toJSONString(array, SerializerFeature.DisableCircularReferenceDetect);
	}

	/**
	 * 编辑全维度cnt信息
	 *
	 * @param keyNo 公司keyno
	 * @param array 基础数据信息
	 */
	public void editAllInfo(String keyNo, JSONArray array) {
		JSONObject type0Json = new JSONObject();
		type0Json.put("KeyNo", keyNo);
		type0Json.put("Type", WeiduEnum.ALL.getCode());
		type0Json.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.ALL.getCode().toString() + "|sqr")));

		Set<Integer> wdSet = new LinkedHashSet<>();
		for (int i = 1; i <= 28; i++) {
			wdSet.add(i);
		}

		JSONArray detailJson = new JSONArray();
		for (Object o : array) {
			JSONObject jsonObject = (JSONObject) o;
			JSONObject item = new JSONObject();

			// 限高
			if (WeiduEnum.XG.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.XG.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
			}

			// 失信
			if (WeiduEnum.SX.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.SX.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
				item.put("B", 0);
				JSONArray array1 = jsonObject.getJSONObject("DetailInfo").getJSONArray("B");
				if (array1 != null && !array1.isEmpty()) {
					Iterator<Object> it = array1.iterator();
					while (it.hasNext()) {
						JSONObject jsonObject1 = (JSONObject) it.next();
						if ("1".equals(jsonObject1.getString("A"))) {
							item.put("B", jsonObject1.getInteger("B"));
						}
					}
				}
			}

			// 被执行人
			if (WeiduEnum.ZX.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.ZX.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
				item.put("B", jsonObject.getJSONObject("DetailInfo").getBigDecimal("B"));
			}

			// 终本
			if (WeiduEnum.ZB.getCode().equals(jsonObject.getInteger("Type"))) {
				item.put("Type", WeiduEnum.ZB.getCode());
				item.put("A", jsonObject.getJSONObject("DetailInfo").getInteger("A"));
				item.put("B", jsonObject.getJSONObject("DetailInfo").getBigDecimal("C"));
			}

			detailJson.add(item);
			wdSet.remove(jsonObject.getInteger("Type"));
		}

		for (Integer i : wdSet) {
			JSONObject item = new JSONObject();
			item.put("Type", i);
			item.put("A", 0);
			detailJson.add(item);
		}

		type0Json.put("DetailInfo", detailJson);

		array.add(type0Json);
	}

	/**
	 * 编辑限高信息
	 *
	 * @param keyNo  公司keyno
	 * @param xginfo 限高
	 * @param array  列表数据
	 */
	public void editXgInfo(String keyNo, List<String> xginfo, JSONArray array) {
		if (xginfo != null && xginfo.size() > 0) {
			for (String str : xginfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.XG.getCode().toString()) + "|sqr"));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.XG.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
	}

	/**
	 * 编辑失信信息
	 *
	 * @param keyNo  公司keyno
	 * @param sxinfo 失信
	 * @param array  列表数据
	 */
	public void editSxInfo(String keyNo, List<String> sxinfo, JSONArray array) {
		if (sxinfo != null && sxinfo.size() > 0) {
			for (String str : sxinfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));

					String courtLevel = jsonObject.getString("exestatus");
					JSONArray array1 = new JSONArray();
					try {
						if (StringUtils.isNotEmpty(courtLevel)) {
							List<String> list = JSON.parseArray(courtLevel, String.class);
							for (String sub : list) {
								JSONObject jsonObject1 = new JSONObject();
								jsonObject1.put("A", sub.split("#FGF#")[0]);
								jsonObject1.put("B", Integer.valueOf(sub.split("#FGF#")[1]));

								array1.add(jsonObject1);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					cntInfo.put("B", array1);

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.SX.getCode().toString()) + "|sqr"));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.SX.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    总金额
		// C    法院层级
		// D    案由
	}

	/**
	 * 编辑被执行人信息
	 *
	 * @param keyNo  公司keyno
	 * @param zxinfo 被执行人信息
	 * @param array  列表数据
	 */
	public void editZxInfo(String keyNo, List<String> zxinfo, JSONArray array) {
		if (zxinfo != null && zxinfo.size() > 0) {
			for (String str : zxinfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", new BigDecimal(jsonObject.getString("biaodi")));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.ZX.getCode().toString()) + "|sqr"));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.ZX.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    执行标的
	}

	/**
	 * 编辑终本信息
	 *
	 * @param keyNo  公司keyno
	 * @param zbinfo 终本信息
	 * @param array  列表数据
	 */
	public void editZbInfo(String keyNo, List<String> zbinfo, JSONArray array) {
		if (zbinfo != null && zbinfo.size() > 0) {
			for (String str : zbinfo) {
				if (StringUtils.isNotEmpty(str)) {
					JSONObject jsonObject = JSONObject.parseObject(str);

					JSONObject cntInfo = new JSONObject();
					cntInfo.put("A", jsonObject.getInteger("cnt"));
					cntInfo.put("B", new BigDecimal(jsonObject.getString("biaodi")));
					cntInfo.put("C", new BigDecimal(jsonObject.getString("failureact")));
					cntInfo.put("D", jsonObject.getString("failurepercent"));

					JSONObject result = new JSONObject();
					result.put("Id", MD5Util.ecodeByMD5(keyNo.concat(WeiduEnum.ZB.getCode().toString()) + "|sqr"));
					result.put("KeyNo", keyNo);
					result.put("Type", WeiduEnum.ZB.getCode());
					result.put("DetailInfo", cntInfo);

					array.add(result);
				}
			}
		}
		// A    总数
		// B    执行标的
		// C    未履行金额
		// D    未履行比例
	}

	public static void main(String[] args) {
		getRiskAnalysisSqrSumInfo job = new getRiskAnalysisSqrSumInfo();
		List<String> zbInfoList = new LinkedList<>();
		System.out.println(job.evaluate("0013f73b095e5d01b7978fcb4b3b9703", null, null, null, null));
	}
}
