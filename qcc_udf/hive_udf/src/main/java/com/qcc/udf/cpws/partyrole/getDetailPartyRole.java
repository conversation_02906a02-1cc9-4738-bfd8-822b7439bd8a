package com.qcc.udf.cpws.partyrole;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.cpws.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

/**
 * <AUTHOR>
 */
public class getDetailPartyRole extends UDF {

    public String evaluate(String keynos, String caseRole) {
        String result = "";

        Map<String, String> roleMap = new LinkedHashMap<>();
        if (StringUtils.isNotEmpty(caseRole)){
            try {
                JSONArray array = JSONArray.parseArray(caseRole);
                Iterator<Object> it = array.iterator();
                while (it.hasNext()){
                    JSONObject json = (JSONObject)it.next();
                    if (StringUtils.isNotEmpty(json.getString("N"))){
                        roleMap.put(json.getString("N"), json.getString("R") == null ? "" : json.getString("R"));
                    }
                }

            }catch (Exception ex){
                ex.printStackTrace();
            }
        }

        if (StringUtils.isNotEmpty(keynos)){
            String[] arr = keynos.split(",");
            for (String str : arr){
                if (CommonUtil.isKeyword(str)){
                    int code = -2;
                    if (roleMap.keySet().contains(str)){
                        String role = roleMap.get(str);
                        code = PartyRoleCodeEnum.findCode(role);

                    }
                    result = result.concat(",") + (str.concat("_") + code);
                }
            }
        }

        result = result.length() > 0 ? result.substring(1) : result;
        return result;
    }

    public static void main(String[] args) {
        //System.out.println(new getDetailPartyRole().evaluate("周先树,北京市金龙腾装饰股份有限公司,f5e9639a46ac1d0e74f0a35e952f21c4", "[{\"P\":\"北京市金龙腾装饰股份有限公司\",\"R\":\"上诉人（原审被告）\",\"ShowName\":\"北京市金龙腾装饰股份有限公司\",\"N\":\"f5e9639a46ac1d0e74f0a35e952f21c4\",\"O\":0},{\"P\":\"周先树\",\"R\":\"上诉人（原审原告）\",\"ShowName\":\"周**\",\"N\":\"\",\"O\":-2},{\"P\":\"北京市安衡律师事务所\",\"R\":\"代理律师事务所\",\"ShowName\":\"北京市安衡律师事务所\",\"N\":\"w1348bf4727d83bfba359e4169ec975e\",\"O\":4}]"));
        System.out.println(false || false && true);
    }
}
