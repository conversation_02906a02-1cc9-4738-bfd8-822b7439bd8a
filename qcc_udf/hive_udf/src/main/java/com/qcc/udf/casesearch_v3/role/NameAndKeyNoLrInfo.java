package com.qcc.udf.casesearch_v3.role;

import com.alibaba.fastjson.annotation.JSONField;
import com.qcc.udf.casesearch_v3.entity.input.CPWSLawyerFirmGroupInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
public class NameAndKeyNoLrInfo {
    @JSONField(name = "Name")
    private String name = "";
    @JSONField(name = "ShowName")
    private String showName;
    @JSONField(name = "KeyNo")
    private String keyNo = "";
    @J<PERSON><PERSON>ield(name = "Org")
    private Integer org = 0;
    @J<PERSON><PERSON>ield(name = "Role")
    private String role;
    @J<PERSON><PERSON>ield(name="Source")
    private Integer source;
    @JSONField(name="RoleTag")
    private Integer roleTag;
    @JSONField(name="RoleType")
    private Integer roleType;
    @J<PERSON><PERSON>ield(name="RoleTypeDesc")
    private String roleTypeDesc;
    /**
     * 判决结果
     */
    @J<PERSON><PERSON>ield(name = "LR")
    private String lawsuitResult;

    @J<PERSON>NField(name = "LawFirmList")
    private List<CPWSLawyerFirmGroupInfo> LawFirmList;

    public NameAndKeyNoLrInfo() {
    }

    public NameAndKeyNoLrInfo(String role, String name, String keyNo, Integer org, Integer roleTag) {
        this.role = role;
        this.name = name;
        this.keyNo = keyNo;
        this.org = org;
        this.roleTag = roleTag;
    }
}
