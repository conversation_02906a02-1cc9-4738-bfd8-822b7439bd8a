package com.qcc.udf.casesearch_v3.entity.input;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.casesearch_v3.enums.CaseCategoryEnum;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: zhangqiang
 * @Date: 2020/11/11 17:54
 * @Description:裁判文书
 */
@Data
public class CPWSEntity extends BaseCaseEntity {

    private String id;
    private String anno;
    private String companynames;
    private String isvalid;
    private String courtname;
    private String provincecode;

    private String defendant;
    private String prosecutor;

    private long submitdate;
    private long judgedate;
    private long courtdate;

    private String casereason;
    private String caserole;
    private String trialround;
    private String protestorgan;
    private String amountinvolved;
    private String judgeresult;
    private String doctype;
    private String defendantpaytotal;
    private String nameandkeyno;

    private String beforecaseno;
    /**
     * 案件名称
     */
    private String casename;
    /**
     * 审理经过
     */
    private String trialprocess;


    /**
     * 商标
     */
    private String trademark_detail;

    /**
     * 专利
     */
    private String patents_detail;

    /**
     * 律师律所
     */
    private String lawyer_info;

    /**
     * 判决结果标签
     */
    private String lawsuitresultnew;

    /**
     * 商标
     */
    private List<Trademark> tmList;

    /**
     * 专利
     */
    private List<OsPatent> ptList;

    /**
     * 律师律所
     */
    private List<LawyerInfo> lawyerList;

    /**
     * 人与角色对应关系
     */
    private List<CaseRoleEntity> caseRoleEntityList;
    private ProtestorganEntity protestorganEntity;
    private List<NameAndKeyNoEntity> nameAndKeyNoEntityList;

    /*该文书是否为无内容文书（1：是 0：否）*/
    private Integer shieldCaseFlag;

    /**
     * 包含律师律师信息的新型CaseRole对象
     */
    private String caseRoleLawJudgeParty;


    /**
     * 判决结果标签
     */
    private List<CPWSLawsuitResult> lawsuitResultEntityList;

    /**
     * 包含律师律师信息的新型CaseRole对象
     */
    private List<CPWSCaseRoleLawJudgeParty> caseRoleLawJudgePartyEntityList;


    public static List<CPWSEntity> convert(List<String> jsonList) {
        List<CPWSEntity> list = new ArrayList<>();
        CPWSEntity entity = null;
        if(CollectionUtils.isEmpty(jsonList)){
            return list;
        }
        for (String json : jsonList) {
            if(Strings.isNullOrEmpty(json)){
                continue;
            }
            entity = JSON.parseObject(json, CPWSEntity.class);
            if(entity == null  || Strings.isNullOrEmpty(entity.getId())){
                continue;
            }

            //商标专利可能出异常
            try{
                String trademarkinfo = entity.getTrademark_detail();
                List<Trademark> tmLsit = Collections.EMPTY_LIST;
                if(StringUtils.isNotBlank(trademarkinfo)){
                    tmLsit = JSON.parseArray(trademarkinfo, Trademark.class);
                }
                entity.setTmList(tmLsit);

                String patentinfo = entity.getPatents_detail();
                List<OsPatent> ptLsit = Collections.EMPTY_LIST;
                if(StringUtils.isNotBlank(patentinfo)){
                    ptLsit = JSON.parseArray(patentinfo, OsPatent.class);
                }
                entity.setPtList(ptLsit);
            }catch (Exception e){
                e.printStackTrace();
            }

            try{
                String lawyerInfo = entity.getLawyer_info();
                List<LawyerInfo> lawyerLsit = Collections.EMPTY_LIST;
                if(StringUtils.isNotBlank(lawyerInfo)){
                    lawyerLsit = JSON.parseArray(lawyerInfo, LawyerInfo.class);
                }
                entity.setLawyerList(lawyerLsit);
            }catch (Exception e){
                e.printStackTrace();
            }


            String str = entity.getNameandkeyno();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setNameAndKeyNoEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getNameAndKeyNoEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                }
            }else{
                entity.setNameAndKeyNoEntityList(new ArrayList<>());
            }

            str = entity.getCaserole();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setCaseRoleEntityList(JSON.parseArray(str, CaseRoleEntity.class));

            }
            //包含律师信息的CaseRole
            if(!Strings.isNullOrEmpty(entity.getCaseRoleLawJudgeParty())){
                List<CPWSCaseRoleLawJudgeParty> caseRolLawFirmList = JSON.parseArray(entity.getCaseRoleLawJudgeParty()
                        ,CPWSCaseRoleLawJudgeParty.class);
                entity.setCaseRoleLawJudgePartyEntityList(caseRolLawFirmList);
                if(CollectionUtils.isNotEmpty(caseRolLawFirmList)){
                    List<CaseRoleEntity> newCaseRoleList = new ArrayList<>();
                    CaseRoleEntity newCaseRole;
                    for (CPWSCaseRoleLawJudgeParty cpwsLawFirmInfo : caseRolLawFirmList) {
                        newCaseRole = new CaseRoleEntity();
                        newCaseRole.setP(cpwsLawFirmInfo.getName());
                        newCaseRole.setR(cpwsLawFirmInfo.getRole());
                        newCaseRole.setO(getOrgByKeyNo(cpwsLawFirmInfo.getKeyNo(),cpwsLawFirmInfo.getName()));
                        newCaseRole.setN(cpwsLawFirmInfo.getKeyNo());
                        //关联律师信息
                        List<CPWSLawFirmInfo> LawFirmList = cpwsLawFirmInfo.getLawFirmList();
                        Map<String,List<CPWSLawFirmInfo>> lawFirmMap = LawFirmList.stream().collect(Collectors
                                .groupingBy(data->Strings.isNullOrEmpty(data.getLawFirm_KeyNo())
                                        ?data.getLawFirm_Name():data.getLawFirm_KeyNo()));
                        List<CPWSLawyerFirmGroupInfo> groupList = new ArrayList<>();
                        for (List<CPWSLawFirmInfo> value : lawFirmMap.values()) {
                            CPWSLawFirmInfo firstValue = value.get(0);
                            CPWSLawyerFirmGroupInfo group = new CPWSLawyerFirmGroupInfo();
                            group.setLawFirmName(firstValue.getLawFirm_Name());
                            group.setLawFirmKeyNo(firstValue.getLawFirm_KeyNo());
                            group.setOrg(getOrgByKeyNo(firstValue.getLawFirm_KeyNo(),firstValue.getLawFirm_Name()));
                            List<CPWSLawyerGroupInfo> LY = new ArrayList<>();
                            for (CPWSLawFirmInfo lawFirmInfo : value) {
                                if(CollectionUtils.isEmpty(lawFirmInfo.getLawyerList())){
                                    continue;
                                }
                                for (CPWSLawyerInfo cpwsLawyerInfo : lawFirmInfo.getLawyerList()) {
                                    LY.add(CPWSLawyerGroupInfo.builder()
                                            .LawyerName(cpwsLawyerInfo.getLawyer_Name())
                                            .LawyerRole(cpwsLawyerInfo.getLawyer_Role())
                                            .LawyerKeyNo(cpwsLawyerInfo.getLawyer_KeyNo()).build());
                                }

                            }
                            group.setLY(LY);
                            groupList.add(group);
                        }
                        newCaseRole.setLawFirmList(groupList);
                        newCaseRoleList.add(newCaseRole);
                    }

                    //如果有新版的Role则覆盖老的
                    entity.setCaseRoleEntityList(newCaseRoleList);
                }

            }
            str = entity.getProtestorgan();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setProtestorganEntity(JSON.parseObject(str,ProtestorganEntity.class));
            }

            entity.setLawsuitResultEntityList(new ArrayList<>());
            //判决结果标签信息
            if(!Strings.isNullOrEmpty(entity.getLawsuitresultnew())){
                entity.setLawsuitResultEntityList(JSON.parseArray(entity.getLawsuitresultnew(),CPWSLawsuitResult.class));
                //遍历CaseRole信息 赋值判决结果标签
                if(CollectionUtils.isNotEmpty(entity.getLawsuitResultEntityList())){
                    //判决结果v1(对应T)
                    Map<String,String> resultTagMap = new HashMap<>();
                    //判决结果v2(对应JR)
                    Map<String,String> resultV2TagMap = new HashMap<>();
                    for (CPWSLawsuitResult result : entity.getLawsuitResultEntityList()) {
                        if(!Strings.isNullOrEmpty(result.getN())){
                            resultTagMap.put(result.getN(),result.getT());
                            resultV2TagMap.put(result.getN(),result.getJR());
                        }
                        if(!Strings.isNullOrEmpty(result.getP()) && !Strings.isNullOrEmpty(result.getP().trim())){
                            resultTagMap.put(CommonV3Util.full2Half(result.getP().trim()),result.getT());
                            resultV2TagMap.put(CommonV3Util.full2Half(result.getP().trim()),result.getJR());
                        }
                    }
                    //CaseRole设置判决结果
                    if(CollectionUtils.isNotEmpty(entity.getCaseRoleEntityList())){
                        //CaseRole设置判决结果 v1
                        setCaseRoleResult(resultTagMap, entity,1);
                        //CaseRole设置判决结果 v2
                        setCaseRoleResult(resultV2TagMap, entity,2);
                    }
                    //当事人设置判决结果
                    if(CollectionUtils.isNotEmpty(entity.getNameAndKeyNoEntityList())){
                        //当事人设置判决结果 v1
                        setNameAndKeyNoResult(resultTagMap, entity, 1);
                        //CaseRole设置判决结果 v2
                        setNameAndKeyNoResult(resultV2TagMap, entity,2);
                    }
                }
            }


            //公共字段赋值
            entity.setBaseCaseNo(entity.getAnno());
            entity.setBaseCaseCategoryEnum(CaseCategoryEnum.CPWS);
            if(!Strings.isNullOrEmpty(entity.getCompanynames())){
                entity.setBaseSearchWordSet(Arrays.stream(entity.getCompanynames().split(","))
                        .collect(Collectors.toSet()));
            }

            entity.setBaseCaseReason(entity.getCasereason());
            entity.setBaseCourt(entity.getCourtname());
            entity.setBaseNameKeyNoList(entity.getNameAndKeyNoEntityList());
            entity.setBaseProvinceCode(entity.getProvincecode());
            entity.setBaseId(entity.getBaseCaseCategoryEnum().getType()+"_"+entity.getId());

            entity.setRolePartyCount(entity.getCaseRoleEntityList().size());
            entity.setPartyCount(entity.getNameAndKeyNoEntityList().size());
            entity.setCaseTimeStamp(entity.getJudgedate());

            Set<String> baseBeforeNoSet =new HashSet<>();
            baseBeforeNoSet.addAll(CommonV3Util.getAllCaseNo(entity.getBeforecaseno()));
            entity.setBaseBeforeNoSet(baseBeforeNoSet);


            String caseType= CommonV3Util.getCaseType(CommonV3Util.getCaseNo(entity.getBaseCaseNo()));
            //案件类型为空的数据直接过滤
            if(Strings.isNullOrEmpty(caseType)){
                continue;
            }
            list.add(entity);
        }
        return list;
    }

    /**
     * 根据KeyNo获取Org
     * 当keyNo为空时，根据name长度做二次判断：
     * 1 长度 > 4时，返回-1
     * 2 长度 <= 4时，返回-2
     * @param keyNo
     * @param name
     * @return
     */
    public static int getOrgByKeyNo(String keyNo, String name) {
        if (StringUtils.isBlank(keyNo)) {
            return (name.length() > 4 ? -1 : -2);
        }

        int org = 0;
        if (keyNo.startsWith("s")) {
            org = 1;
        } else if (keyNo.startsWith("h")) {
            org = 3;
        } else if (keyNo.startsWith("t")) {
            org = 5;
        } else if (keyNo.startsWith("g") || keyNo.startsWith("x") || keyNo.startsWith("w") || keyNo.startsWith("j")) {
            org = 4;
        } else if (keyNo.startsWith("y")) {
            org = 7;
        } else if (keyNo.startsWith("o")) {
            org = 8;
        } else if (keyNo.startsWith("z")) {
            org = 9;
        } else if (keyNo.startsWith("p")) {
            org = 2;
        }
        return org;
    }

    /**
     * CaseRole设置判决结果
     * @param type:1-v1 2-v2
     */
    private static void setCaseRoleResult(Map<String,String> resultTagMap, CPWSEntity entity, int type) {
        for (CaseRoleEntity role : entity.getCaseRoleEntityList()) {
            String tag = resultTagMap.get(role.getN());
            if(!Strings.isNullOrEmpty(tag)){
                if (type == 1) {
                    role.setT(tag);
                } else {
                    role.setJR(tag);
                }
                continue;
            }
            tag = resultTagMap.get(CommonV3Util.full2Half(CommonV3Util.getString(role.getP()).trim()));
            if(!Strings.isNullOrEmpty(tag)){
                if (type == 1) {
                    role.setT(tag);
                } else {
                    role.setJR(tag);
                }
                continue;
            }
        }
    }

    /**
     * 当事人设置判决结果
     *
     * */
    private static void setNameAndKeyNoResult(Map<String,String> resultTagMap, CPWSEntity entity, int type) {
        for (NameAndKeyNoEntity role : entity.getNameAndKeyNoEntityList()) {
            String tag = resultTagMap.get(role.getKeyNo());
            if(!Strings.isNullOrEmpty(tag)){
                if (type == 1) {
                    role.setLawsuitResult(tag);
                } else {
                    role.setLawsuitResultV2(tag);
                }
                continue;
            }
            tag = resultTagMap.get(CommonV3Util.full2Half(CommonV3Util.getString(role.getName()).trim()));
            if(!Strings.isNullOrEmpty(tag)){
                if (type == 1) {
                    role.setLawsuitResult(tag);
                } else {
                    role.setLawsuitResultV2(tag);
                }
                continue;
            }
        }
    }

    public static void main(String[] args) {
//        List<String> jsonList = new ArrayList<>();
//        jsonList.add("{\"id\":\"005d2f910bd97a5fe4d7dc84806d055d0\",\"anno\":\"（2014）郴北民二初字第49号\",\"isvalid\":\"1\",\"courtname\":\"湖南省郴州市北湖区人民法院\",\"companynames\":\"雷玉林,王晓燕,p094c9fc0636ba2e475ce119bea601c8,a8973ca2e33f28fae0f55f8619122f9c,郴州市钜丰矿业有限责任公司\",\"provincecode\":\"HUN\",\"defendant\":\"雷玉林,王晓燕,p094c9fc0636ba2e475ce119bea601c8,郴州市钜丰矿业有限责任公司,a8973ca2e33f28fae0f55f8619122f9c\",\"prosecutor\":\"\",\"submitdate\":1397001600,\"judgedate\":1395100800,\"courtdate\":1397001600,\"casereason\":\"民间借贷纠纷\",\"caserole\":\"[{\\\"P\\\":\\\"郴州市钜丰矿业有限责任公司\\\",\\\"R\\\":\\\"被告\\\",\\\"N\\\":\\\"a8973ca2e33f28fae0f55f8619122f9c\\\",\\\"O\\\":0},{\\\"P\\\":\\\"湖南善道律师事务所\\\",\\\"R\\\":\\\"代理律师事务所\\\",\\\"N\\\":\\\"w49d7263030763b3230ea2f314d69fbf\\\",\\\"O\\\":4},{\\\"P\\\":\\\"雷玉林\\\",\\\"R\\\":\\\"被告\\\",\\\"N\\\":\\\"p094c9fc0636ba2e475ce119bea601c8\\\",\\\"O\\\":2},{\\\"P\\\":\\\"王晓燕\\\",\\\"R\\\":\\\"被告\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2}]\",\"protestorgan\":\"\",\"amountinvolved\":\"3990000.00\",\"defendantpaytotal\":\"4033720.00\",\"doctype\":\"ver\",\"judgeresult\":\"被告郴州市钜丰矿业有限责任公司、雷玉林、王晓燕于本判决生效后十日内偿还原告ＸＸＸ借款本金3990000元；  如果被告郴州市钜丰矿业有限责任公司、雷玉林、王晓燕未按本判决指定的期间履行给付金钱义务，应当依照《中华人民共和国民事诉讼法》第二百五十三条之规定，加倍支付迟延履行期间的债务利息。\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"a8973ca2e33f28fae0f55f8619122f9c\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"郴州市钜丰矿业有限责任公司\\\"},{\\\"KeyNo\\\":\\\"p094c9fc0636ba2e475ce119bea601c8\\\",\\\"Org\\\":2,\\\"Name\\\":\\\"雷玉林\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"王晓燕\\\"}]\",\"beforecaseno\":\"\",\"casename\":\"原告ＸＸＸ与被告郴州市钜丰矿业有限责任公司（以下简称钜丰公司）、雷玉林、王晓燕民间借贷纠纷一案民事一审判决书\",\"trialprocess\":\"原告ＸＸＸ与被告郴州市钜丰矿业有限责任公司（以下简称钜丰公司）、雷玉林、王晓燕民间借贷纠纷一案，本院于2014年1月7日立案受理，依法组成合议庭，公开开庭进行了审理。原告ＸＸＸ委托代理人刘纯、被告钜丰公司委托代理人肖峰到庭参加诉讼。被告雷玉林、王晓燕经本院合法传唤，未到庭参加诉讼，本院依法缺席审理。本案现已审理终结。\",\"shieldcaseflag\":\"0\",\"trademark_detail\":\"\",\"patents_detail\":\"\",\"lawsuitresultnew\":\"[{\\\"P\\\":\\\"郴州市钜丰矿业有限责任公司\\\",\\\"T\\\":\\\"4\\\",\\\"N\\\":\\\"a8973ca2e33f28fae0f55f8619122f9c\\\"},{\\\"P\\\":\\\"雷玉林\\\",\\\"T\\\":\\\"4\\\",\\\"N\\\":\\\"p094c9fc0636ba2e475ce119bea601c8\\\"},{\\\"P\\\":\\\"王晓燕\\\",\\\"T\\\":\\\"4\\\",\\\"N\\\":\\\"\\\"}]\"}");
//        convert(jsonList);
        String caseType= CommonV3Util.getCaseType(CommonV3Util.getCaseNo("（2014）高新民保字第779号"));
        System.out.println(caseType);
    }

}
