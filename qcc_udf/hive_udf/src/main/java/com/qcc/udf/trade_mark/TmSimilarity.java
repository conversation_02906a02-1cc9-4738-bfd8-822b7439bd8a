package com.qcc.udf.trade_mark;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

/**
 * <AUTHOR>
 */
public class TmSimilarity extends UDF {


    public static void main(String[] args) {
        String a = "abcd";
        String b = "ddac";
        TmSimilarity tmSimilarity = new TmSimilarity();
        System.out.println(tmSimilarity.evaluate(a, b));
    }

    /**
     * 比较拼音/字母
     */
    private boolean comparePy(String str, String target) {
        if (StringUtils.isEmpty(str) || StringUtils.isEmpty(target)) {
            return false;
        }
        try {
            Set<String> set1 = getPinyinStr(str);
            Set<String> set2 = getPinyinStr(target);
            if (CollectionUtils.isNotEmpty(set1) && CollectionUtils.isNotEmpty(set2)) {
                for (String s1 : set1) {
                    for (String s2 : set2) {
                        if (Objects.equals(s1, s2)) {
                            return true;
                        }
                    }
                }
            }
        } catch (BadHanyuPinyinOutputFormatCombination badHanyuPinyinOutputFormatCombination) {
            badHanyuPinyinOutputFormatCombination.printStackTrace();
        }
        return false;
    }

    private Set<String> getPinyinStr(String name) throws BadHanyuPinyinOutputFormatCombination {
        List<Set<String>> list = new ArrayList<>();
        char[] charArray = name.toCharArray();
        HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();
        //设置大小写格式
        defaultFormat.setCaseType(HanyuPinyinCaseType.UPPERCASE);
        //设置声调格式：
        defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        for (int i = 0; i < charArray.length; i++) {
            //匹配中文,非中文转换会转换成null
            if (Character.toString(charArray[i]).matches("[\\u4E00-\\u9FA5]+")) {
                String[] hanyuPinyinStringArray = PinyinHelper.toHanyuPinyinStringArray(charArray[i], defaultFormat);
                if (hanyuPinyinStringArray == null) {
                    return null;
                }
                Set<String> newStrSet = new HashSet<>();
                for (String py : hanyuPinyinStringArray) {
                    if (CollectionUtils.isNotEmpty(list)) {
                        Set<String> strSet = list.get(list.size() - 1);
                        for (String str : strSet) {
                            newStrSet.add(str + py);
                        }
                    } else {
                        newStrSet.add(py);
                    }
                }
                list.add(newStrSet);
            } else {
                Set<String> newStrSet = new HashSet<>();
                if (CollectionUtils.isNotEmpty(list)) {
                    Set<String> strSet = list.get(list.size() - 1);
                    for (String str : strSet) {
                        newStrSet.add(str + String.valueOf(charArray[i]).toUpperCase());
                    }
                } else {
                    newStrSet.add(String.valueOf(charArray[i]).toUpperCase());
                }
                list.add(newStrSet);
            }
        }

        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(list.size() - 1);
        }
        return null;
    }

    public int evaluate(String str1, String str2) {
        if (isEmptyName(str1) || isEmptyName(str2)) {
            return 0;
        }
        if (Objects.equals(str1.toUpperCase(), str2.toUpperCase())) {
            return 100;
        }
        if (comparePy(str1, str2)) {
            return 100;
        }
        String lStr = str1.length() > str2.length() ? str1.toUpperCase() : str2.toUpperCase();
        String sStr = str1.length() > str2.length() ? str2.toUpperCase() : str1.toUpperCase();
        StringBuilder sb = new StringBuilder();
        int m = lStr.length();
        int n = sStr.length();
        Set<Integer> set = new HashSet<>();
        for (int i = 0; i < m; i++) {
            char c1 = lStr.charAt(i);
            boolean flag = false;
            for (int j = 0; j < n; j++) {
                if (set.contains(j)) {
                    continue;
                }
                char c2 = sStr.charAt(j);
                if (c1 == c2) {
                    flag = true;
                    set.add(j);
                    sb.append(c1);
                    break;
                }
            }
            if (!flag) {
                sb.append('\001');
            }
        }


        return (int) (getSimilarityRatio(lStr, sb.toString()) * 100);
    }

    /**
     * 比较两个字符串的相识度
     * 核心算法：用一个二维数组记录每个字符串是否相同，如果相同记为0，不相同记为1，每行每列相同个数累加
     * 则数组最后一个数为不相同的总数，从而判断这两个字符的相识度
     *
     * @param str
     * @param target
     * @return
     */
    private int compare(String str, String target) {
        int d[][];              // 矩阵
        int n = str.length();
        int m = target.length();
        int i;                  // 遍历str的
        int j;                  // 遍历target的
        char ch1;               // str的
        char ch2;               // target的
        int temp;               // 记录相同字符,在某个矩阵位置值的增量,不是0就是1
        if (n == 0) {
            return m;
        }
        if (m == 0) {
            return n;
        }
        d = new int[n + 1][m + 1];
        // 初始化第一列
        for (i = 0; i <= n; i++) {
            d[i][0] = i;
        }
        // 初始化第一行
        for (j = 0; j <= m; j++) {
            d[0][j] = j;
        }
        for (i = 1; i <= n; i++) {
            // 遍历str
            ch1 = str.charAt(i - 1);
            // 去匹配target
            for (j = 1; j <= m; j++) {
                ch2 = target.charAt(j - 1);
                if (ch1 == ch2) {
                    temp = 0;
                } else {
                    temp = 1;
                }
                // 左边+1,上边+1, 左上角+temp取最小
                d[i][j] = min(d[i - 1][j] + 1, d[i][j - 1] + 1, d[i - 1][j - 1] + temp);
            }
        }
        return d[n][m];
    }

    /**
     * 获取最小的值
     */
    private int min(int one, int two, int three) {
        return (one = one < two ? one : two) < three ? one : three;
    }

    /**
     * 获取两字符串的相似度
     */
    public float getSimilarityRatio(String str, String target) {
        int max = Math.max(str.length(), target.length());
        return 1 - (float) compare(str, target) / max;
    }

    public boolean isEmptyName(String name) {
        if (StringUtils.isEmpty(name)) {
            return true;
        }
        if (StringUtils.isEmpty(name.trim())) {
            return true;
        }
        if (StringUtils.isEmpty(name.trim().replace("图形", ""))) {
            return true;
        }
        return "无".equals(name);
    }
}
