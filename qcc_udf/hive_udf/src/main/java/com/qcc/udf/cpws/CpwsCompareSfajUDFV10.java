package com.qcc.udf.cpws;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;


public class CpwsCompareSfajUDFV10 extends UDF {
    public static String evaluate(String role1) {
        return cleanOriginParties(role1);
    }

    /**
     * 清洗爬虫当事人字段
     *孙××
     * @param originParties 爬虫当事人新
     * @return 清洗结果
     */
    public static String cleanOriginParties(String originParties) {
        List<String> appellor = new ArrayList<>();
        if (StringUtils.isNotEmpty(originParties)) {
            String[] opSplit = originParties.split("[,，]");
            for (String s : opSplit) {
                s = s.replace("（", "(").replace("）", ")").replace("\"", "").replace("[", "").replace("]", "");
                if (s.length() < 2) {
                    continue;
                }
                if (s.matches(".*[某x×X*ｘＸ].*")) {
                    continue;
                }
                if (s.contains("执行案外人") || s.contains("立案庭") || s.contains("审判庭") || s.contains("败诉的") ||
                        s.contains("已预交") || s.contains("刑庭") || s.contains("本院") || s.equals("汽汽车金融有限公司")
                        || s.equals("汽-大众汽车限公司") || s.equals("三)") || s.equals("基于该宣传广告") || s.equals("上述损失总计")) {
                    continue;
                }
                if (s.contains("(原")) {
                    s = (s.split("\\(原")[0]);
                }
                if (s.contains("(下称")) {
                    s = (s.split("\\(下称")[0]);
                }
                if (s.contains("&times")) {
                    s = (s.replace("&times", ""));
                }
                if (s.contains("＆ｌｄｑｕｏ")) {
                    s = (s.replace("＆ｌｄｑｕｏ", ""));
                }
                if (s.contains("＆ｔｉｍｅｓ")) {
                    s = (s.replace("＆ｔｉｍｅｓ", ""));
                }
                if (s.contains("被执行人")) {
                    s = (s.replace("被执行人", ""));
                }
                if (s.contains("执行人")) {
                    s = (s.replace("执行人", ""));
                }
                if (s.contains("公民身份号码")) {
                    s = (s.replace("公民身份号码", ""));
                }
                if (s.contains("公民")) {
                    s = (s.replace("公民", ""));
                }
                if (s.contains("身份证号")) {
                    s = (s.replace("身份证号", ""));
                }
//司|行|队|会|院|局|部|社|厂|厅|所|店|中心|政府|企业|基地|超市|处|矿|室|场|校|城|园|馆|站|组|庭|台|学|吧|庄|户|段|团|村|房|人|家|坊|公寓|库
                if (s.length() > 5) {
                    if (s.contains("·")
                            || s.endsWith("司") || s.endsWith("行") || s.endsWith("队") || s.endsWith("会")
                            || s.endsWith("院") || s.endsWith("局") || s.endsWith("部") || s.endsWith("社")
                            || s.endsWith("厂") || s.endsWith("厅") || s.endsWith("所") || s.endsWith("店")
                            || s.endsWith("中心") || s.endsWith("政府") || s.endsWith("企业") || s.endsWith("基地")
                            || s.endsWith("超市") || s.endsWith("处") || s.endsWith("矿") || s.endsWith("室")
                            || s.endsWith("场") || s.endsWith("校") || s.endsWith("城") || s.endsWith("园")
                            || s.endsWith("馆") || s.endsWith("站") || s.endsWith("组") || s.endsWith("庭")
                            || s.endsWith("台") || s.endsWith("学") || s.endsWith("吧") || s.endsWith("庄")
                            || s.endsWith("户") || s.endsWith("段") || s.endsWith("团") || s.endsWith("村")
                            || s.endsWith("房") || s.endsWith("人") || s.endsWith("家") || s.endsWith("坊")
                            || s.endsWith("公寓") || s.endsWith("库")
                    ) {
                        appellor.add(s);
                    }
                } else {
                    appellor.add(s);
                }
            }

        }
        return String.join(",", appellor);
    }


}
