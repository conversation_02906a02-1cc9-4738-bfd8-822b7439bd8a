package com.qcc.udf.casesearch_v3;

import com.qcc.udf.cpws.casesearch_v2.CommonV2Util;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class getDocNo extends UDF {
    public static String evaluate(String param) {
        String result = "";
        if (StringUtils.isNotEmpty(param)){
            param = CommonV2Util.full2Half(param).replace(" ","");
            Pattern pattern = Pattern.compile("[0-9\\u4e00-\\u9fa5]");
            Matcher m = pattern.matcher(param);
            if (m.find()){
                result = m.group();
            }
        }

        return result;
    }

    public static void main(String[] args) {
        System.out.println(evaluate("稷地-税 简罚 〔2015〕 337 号"));
    }
}
