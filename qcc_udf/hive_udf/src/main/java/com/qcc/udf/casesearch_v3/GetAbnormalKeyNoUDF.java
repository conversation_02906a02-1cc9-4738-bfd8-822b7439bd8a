package com.qcc.udf.casesearch_v3;


import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.risk_analysis.entity.NameKeyDto;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 判断同一个案号下的同一个人员,是否存在不同KeyNo,存在则返回1,否则0
 * @date 2022/10/19 17:33
 */
public class GetAbnormalKeyNoUDF extends UDF {
    private static final String PERSON_FLAG = "p";
    private static final Integer SUCCESS = 0;
    private static final Integer ERROR = 1;


    public Integer evaluate(String nameAndKeyNos) {
        if(StringUtils.isBlank(nameAndKeyNos)){
            return SUCCESS;
        }
        try {
            List<NameKeyDto> nameKeyDtos = JSONObject.parseArray(nameAndKeyNos, NameKeyDto.class);
            Map<String, List<NameKeyDto>> nameAndKeyNoMap = nameKeyDtos.stream().
                    filter(item -> item != null && StringUtils.isNotBlank(item.getKeyNo()) && StringUtils.isNotBlank(item.getName()) && item.getKeyNo().startsWith(PERSON_FLAG)).
                    collect(Collectors.groupingBy(NameKeyDto::getName));
            for (Map.Entry<String, List<NameKeyDto>> stringListEntry : nameAndKeyNoMap.entrySet()) {
                long count = stringListEntry.getValue().stream().map(NameKeyDto::getKeyNo).collect(Collectors.toSet()).stream().count();
                if (count > 1) {
                    return ERROR;
                }
            }
        }catch(Exception e){
            e.printStackTrace();
        }
        return SUCCESS;
    }

    public static void main(String[] args) {
        String value = "[{\"KeyNo\":\"p12341465\",\"Name\":\"徐学洋\",\"Org\":-1,\"Role\":\"被告\",\"RoleTag\":1,\"RoleType\":21,\"ShowName\":\"徐**\",\"Source\":1},{\"KeyNo\":\"p1234465\",\"Name\":\"徐学洋\",\"Org\":-1,\"Role\":\"被告\",\"RoleTag\":1,\"RoleType\":21,\"ShowName\":\"郑**\",\"Source\":1},{\"KeyNo\":\"p000000\",\"Name\":\"张远佳\",\"Org\":-1,\"Role\":\"被告\",\"RoleTag\":1,\"RoleType\":21,\"ShowName\":\"张**\",\"Source\":1},{\"KeyNo\":\"p00932\",\"Name\":\"胡庆阳\",\"Org\":-1,\"Role\":\"被告\",\"RoleTag\":1,\"RoleType\":21,\"ShowName\":\"胡**\",\"Source\":1},{\"KeyNo\":\"\",\"Name\":\"李艺才\",\"Org\":-1,\"Role\":\"被告\",\"RoleTag\":1,\"RoleType\":21,\"ShowName\":\"李**\",\"Source\":1},{\"KeyNo\":\"\",\"Name\":\"卢盼\",\"Org\":-1,\"Role\":\"被告\",\"RoleTag\":1,\"RoleType\":21,\"ShowName\":\"卢*\",\"Source\":1},{\"KeyNo\":\"\",\"Name\":\"朱洋\",\"Org\":-1,\"Role\":\"被告\",\"RoleTag\":1,\"RoleType\":21,\"ShowName\":\"朱*\",\"Source\":1},{},{},{},{},{},{},{},{},{},{}]";
        System.out.println(new GetAbnormalKeyNoUDF().evaluate(value));
    }

}
