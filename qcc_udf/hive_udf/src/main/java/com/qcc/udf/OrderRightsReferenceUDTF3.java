package com.qcc.udf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentLengthException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDTF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用于解析改版后的用户权益时间段
 */
public class OrderRightsReferenceUDTF3 extends GenericUDTF {


    static DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public StructObjectInspector initialize(StructObjectInspector argOIs) throws UDFArgumentException {
        if (argOIs.getAllStructFieldRefs().size() != 1) {
            throw new UDFArgumentLengthException("ExplodeRiskMap takes only one argument");
        }
        // 输出
        List<String> fieldNames = new ArrayList<String>(3);
        List<ObjectInspector> fieldOIs = new ArrayList<ObjectInspector>(3);
        fieldNames.add("order_code");
        fieldNames.add("rights_start_time");
        fieldNames.add("rights_end_time");
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        return ObjectInspectorFactory.getStandardStructObjectInspector(fieldNames, fieldOIs);
    }

    @Override
    public void process(Object[] objects) throws HiveException {
        JSONObject jsonObject = JSONObject.parseObject(objects[0].toString());
        JSONArray orders = jsonObject.getJSONArray("orders");

        //判断老权益有没有过期  如过期则置位null  6.10订单权益改版
        LocalDateTime date = LocalDateTime.of(2021, 6, 10, 17, 44, 0);


        //判断是否是10.27改版之后的数据 改版之后数据不做处理
        List<Object> filterAfter = orders.stream().filter(temp -> ((JSONObject) temp).containsKey("rights_start_time")
                && !"NULL".equals(((JSONObject) temp).getString("rights_start_time"))).collect(Collectors.toList());
        if (filterAfter.size() == orders.size()){
            export(filterAfter);
            return;
        }
        orders.removeAll(filterAfter);


        ArrayList<LocalDateTime> vipEndTime = new ArrayList<LocalDateTime>();
        ArrayList<LocalDateTime> SvipStartTime = new ArrayList<LocalDateTime>();
        ArrayList<LocalDateTime> SvipEndTime = new ArrayList<LocalDateTime>();
        ArrayList<JSONObject> orderRight = new ArrayList<JSONObject>();


        //按照pay_time排序
        Map<Object, List<Object>> collect = orders.stream().sorted(Comparator.comparing(temp -> ((JSONObject) temp).getTimestamp("pay_time")))
                .collect(Collectors.groupingBy(temp -> ((JSONObject) temp).getString("goods_type")));

        //判断是否存在svip
        //优先筛选出svip订单  根据svip订单再确定vip订单的时间

        List<Object> SVIPList = collect.get("SVIP");
        List<Object> VIPList = collect.get("VIP");

        //svip 权益持续时间
        List<JSONObject> SVIPRightTimeList = new ArrayList<JSONObject>();


        if (SVIPList != null && SVIPList.size() > 0) {
            int i = 0;
            for (Object k : SVIPList) {
                JSONObject order = (JSONObject) k;
                JSONObject right = new JSONObject();
                String orderCode = order.getString("order_code");
                String payTime = order.getString("pay_time");
                String endTime = order.getString("end_time");
                Integer validPeriodMonths = order.getInteger("valid_period_months");
                Integer realGiveDays = order.getInteger("real_give_days");
                LocalDateTime payLocalDateTime = LocalDateTime.parse(payTime, dateTimeFormatter);
                LocalDateTime endLocalDateTime = LocalDateTime.parse(endTime, dateTimeFormatter);

                if (SvipEndTime.size() == 0) {
                    // 无svip权益  则下单时间则为svip开始时间
                    right.put("order_code", orderCode);
                    right.put("rights_start_time", payTime);
                    right.put("rights_end_time", endTime);
                    orderRight.add(right);
                    right.put("rights_start_dateTime", payLocalDateTime);
                    right.put("rights_end_dateTime", endLocalDateTime);
                    right.put("valid_period_months", validPeriodMonths);
                    right.put("real_give_days", realGiveDays);
                    SvipEndTime.add(endLocalDateTime);
                    SvipStartTime.add(payLocalDateTime);
                } else {
                    // 有svip权益
                    if (SvipEndTime.get(SvipEndTime.size() - 1).isBefore(payLocalDateTime)) {
                        //svip权益早于下单时间 说明已过期 则下单时间则为svip开始时间
                        right.put("order_code", orderCode);
                        right.put("rights_start_time", payTime);
                        right.put("rights_end_time", endTime);
                        orderRight.add(right);
                        right.put("rights_start_dateTime", payLocalDateTime);
                        right.put("rights_end_dateTime", endLocalDateTime);
                        right.put("valid_period_months", validPeriodMonths);
                        right.put("real_give_days", realGiveDays);
                        SvipStartTime.add(payLocalDateTime);
                        SvipEndTime.add(endLocalDateTime);
                    } else {
                        //svip权益晚于下单时间 说明未过期 则SVIP时间继续累加
                        LocalDateTime plus = SvipEndTime.get(SvipEndTime.size() - 1).plusMonths(validPeriodMonths).plusDays(realGiveDays);
                        right.put("order_code", orderCode);
                        right.put("rights_start_time", dateTimeFormatter.format(SvipEndTime.get(SvipEndTime.size() - 1)));
                        right.put("rights_end_time", dateTimeFormatter.format(plus));
                        orderRight.add(right);
                        right.put("rights_start_dateTime", SvipEndTime.get(SvipEndTime.size() - 1));
                        right.put("rights_end_dateTime", plus);
                        right.put("valid_period_months", validPeriodMonths);
                        right.put("real_give_days", realGiveDays);
                        SvipStartTime.add(SvipEndTime.get(SvipEndTime.size() - 1));
                        SvipEndTime.add(plus);
                    }
                }
                right.put("next", i );
                SVIPRightTimeList.add(right);
                i++;
            }
        }


        // vip  需要比较是否是6.10老权益  ，如果不是老权益 则需要一一比较svip权益
        // vip  6.10之前老权益 不处理  ，时间跨越6.10号 单独处理
        ArrayList<Object> VIPOrderList = new ArrayList<Object>();
        if (VIPList != null && VIPList.size() > 0){
                LocalDateTime preVipEndTime = null;
                for (Object k : VIPList) {
                    JSONObject order = (JSONObject) k;
                    JSONObject right = new JSONObject();
                    String orderCode = order.getString("order_code");
                    String payTime = order.getString("pay_time");
                    String endTime = order.getString("end_time");
                    Integer validPeriodMonths = order.getInteger("valid_period_months");
                    Integer realGiveDays = order.getInteger("real_give_days");
                    LocalDateTime payLocalDateTime = LocalDateTime.parse(payTime, dateTimeFormatter);
                    LocalDateTime endLocalDateTime = LocalDateTime.parse(endTime, dateTimeFormatter);
                    LocalDateTime plus = null;
                    LocalDateTime rightsStartTime = null;
                    LocalDateTime rightsEndTime = null;

                    if (preVipEndTime == null) {
                        plus = endLocalDateTime;
                        rightsStartTime = payLocalDateTime;
                        rightsEndTime = plus;
                        right.put("order_code", orderCode);
                        right.put("rights_start_time", payTime);
                        right.put("rights_end_time", dateTimeFormatter.format(plus));
                        right.put("valid_period_months", validPeriodMonths);
                        right.put("real_give_days", realGiveDays);
                        right.put("split", 0);
                    } else{
                        if (preVipEndTime.isBefore(payLocalDateTime)) {
                            //vip权益过期
                            plus = endLocalDateTime;
                            rightsStartTime = payLocalDateTime;
                            rightsEndTime = plus;
                            right.put("order_code", orderCode);
                            right.put("rights_start_time", payTime);
                            right.put("rights_end_time", dateTimeFormatter.format(plus));
                            right.put("valid_period_months", validPeriodMonths);
                            right.put("real_give_days", realGiveDays);
                            right.put("split", 0);
                        } else {
                            //上一单权益 覆盖
                            plus = preVipEndTime.plusMonths(validPeriodMonths).plusDays(realGiveDays);
                            rightsStartTime = preVipEndTime;
                            rightsEndTime = plus;
                            right.put("order_code", orderCode);
                            right.put("rights_start_time", dateTimeFormatter.format(preVipEndTime));
                            right.put("rights_end_time", dateTimeFormatter.format(plus));
                            right.put("valid_period_months", validPeriodMonths);
                            right.put("real_give_days", realGiveDays);
                            right.put("split", 0);
                        }
                    }

                    if (rightsStartTime.isBefore(date) && rightsEndTime.isBefore(date)){
                        orderRight.add(right);
                        preVipEndTime = plus;
                    }else if (rightsStartTime.isBefore(date) && rightsEndTime.isAfter(date)){
                        //切割成两份   另一份add到viplist进行后续权益处理
                        right.put("rights_end_time", dateTimeFormatter.format(date));
                        orderRight.add(right);
                        preVipEndTime = plus;

                        JSONObject temp = new JSONObject();
                        temp.put("order_code", orderCode);
                        temp.put("pay_time",payTime);
                        temp.put("end_time",endTime);
                        temp.put("rights_start_time", dateTimeFormatter.format(date));
                        temp.put("rights_end_time", dateTimeFormatter.format(plus));
                        temp.put("valid_period_months", validPeriodMonths);
                        temp.put("real_give_days", realGiveDays);
                        temp.put("split", Duration.between(rightsStartTime,date).getSeconds());
                        VIPOrderList.add(temp);
                    }else{
                        //权益完全在6.10号之后
                        VIPOrderList.add(right);
                        preVipEndTime = plus;
                    }

                }
            }

            //重新排序
            List<Object> VIPNewList = VIPOrderList.stream().sorted(Comparator.comparing(temp -> ((JSONObject) temp).getTimestamp("rights_start_time"))).collect(Collectors.toList());

            if (VIPNewList != null && VIPNewList.size() > 0) {
                LocalDateTime preVipEndTime = null;
                for (Object k : VIPNewList) {
                    JSONObject order = (JSONObject) k;
                    JSONObject right = new JSONObject();
                    String orderCode = order.getString("order_code");
                    String rightsStartTime = order.getString("rights_start_time");
                    String rightsEndTime = order.getString("rights_end_time");
                    Integer validPeriodMonths = order.getInteger("valid_period_months");
                    Integer realGiveDays = order.getInteger("real_give_days");
                    Long splite = order.getLong("split");
                    LocalDateTime payLocalDateTime = order.getTimestamp("rights_start_time").toLocalDateTime();
                    LocalDateTime endLocalDateTime = order.getTimestamp("rights_end_time").toLocalDateTime();

                    if (vipEndTime.size() > 0){
                        preVipEndTime = vipEndTime.get(vipEndTime.size() - 1);
                    }


                    if (vipEndTime.size() == 0 && SvipEndTime.size() == 0) {
                        //无vip svip权益 必定是第一单

                        orderRight.add(order);
                        vipEndTime.add(endLocalDateTime);

                    } else if (vipEndTime.size() > 0 && SvipEndTime.size() == 0) {
                        //有 vip权益 无svip权益
                        orderRight.add(order);
                        continue;

                    }else if (SVIPList.size() > 0){
                        //有vip 有svip权益  先消耗svip时间  再算vip时间  同样分为前中后 只是需要考虑之前未结束的 vip svip
                        //因为多笔不连续的svip订单没法判断是否过期  还是先前中后
                        LocalDateTime plus = null;
                        if (preVipEndTime != null){
                            plus = preVipEndTime.plusMonths(validPeriodMonths).plusDays(realGiveDays);
                        }else{
                            preVipEndTime = payLocalDateTime;
                            plus = endLocalDateTime;
                        }

                        if (endLocalDateTime.isBefore(SvipStartTime.get(0)) && plus.isBefore(SvipStartTime.get(0))){
                            //前
                            if (preVipEndTime.isBefore(payLocalDateTime)) {
                                //vip 权益过期
                                right.put("order_code", orderCode);
                                right.put("rights_start_time", rightsStartTime);
                                right.put("rights_end_time", rightsEndTime);
                                orderRight.add(right);
                                vipEndTime.add(endLocalDateTime);
                            } else {
                                right.put("order_code", orderCode);
                                right.put("rights_start_time", dateTimeFormatter.format(preVipEndTime));
                                right.put("rights_end_time", dateTimeFormatter.format(plus));
                                orderRight.add(right);
                                vipEndTime.add(plus);
                            }

                        } else if (SvipEndTime.get(SvipEndTime.size() - 1).isBefore(payLocalDateTime)) {
                            //后
                            if (preVipEndTime.isBefore(payLocalDateTime)) {
                                //vip 权益过期
                                right.put("order_code", orderCode);
                                right.put("rights_start_time", rightsStartTime);
                                right.put("rights_end_time", rightsEndTime);
                                orderRight.add(right);
                                vipEndTime.add(endLocalDateTime);
                            } else {
                                right.put("order_code", orderCode);
                                right.put("rights_start_time", dateTimeFormatter.format(preVipEndTime));
                                right.put("rights_end_time", dateTimeFormatter.format(plus));
                                orderRight.add(right);
                                vipEndTime.add(plus);
                            }
                        } else {
                            //中 交集  确定previptime 时间   之前的时间段全部固定  循环处理  previp之后的时间
                            LocalDateTime endTemp = endLocalDateTime;
                            LocalDateTime startTemp = payLocalDateTime;
                            long duration = Duration.between(startTemp, endTemp).getSeconds();
                            for (JSONObject temp : SVIPRightTimeList) {
                                LocalDateTime rights_start_dateTime = (LocalDateTime) temp.get("rights_start_dateTime");
                                LocalDateTime rights_end_dateTime = (LocalDateTime) temp.get("rights_end_dateTime");
                                Integer svipValidPeriodMonths = temp.getInteger("valid_period_months");
                                Integer svipRealGiveDays = temp.getInteger("real_give_days");
                                JSONObject rightTemp = new JSONObject();
                                int next = temp.getInteger("next");

                                //比较endTemp 和 preVipEndTime
                                if (preVipEndTime != null && preVipEndTime.isAfter(startTemp)){
                                    startTemp = preVipEndTime;
                                    endTemp = startTemp.plusMonths(validPeriodMonths).plusDays(realGiveDays);
                                }

                                if (endTemp.isBefore(rights_start_dateTime) ){
                                    //前
                                    rightTemp.put("order_code", orderCode);
                                    rightTemp.put("rights_start_time", dateTimeFormatter.format(startTemp));
                                    rightTemp.put("rights_end_time", dateTimeFormatter.format(endTemp));
                                    orderRight.add(rightTemp);
                                    vipEndTime.add(endTemp);
                                    break;
                                }else if(startTemp.isAfter(rights_end_dateTime)){
                                    //后
                                    //判断是否和下一个无交集
                                    if (next == SVIPRightTimeList.size() -1){
                                        rightTemp.put("order_code", orderCode);
                                        rightTemp.put("rights_start_time", dateTimeFormatter.format(startTemp));
                                        rightTemp.put("rights_end_time", dateTimeFormatter.format(endTemp));
                                        orderRight.add(rightTemp);
                                        vipEndTime.add(endTemp);
                                        break;
                                    }else if (next < SVIPRightTimeList.size() -1  && endTemp.isBefore((LocalDateTime) SVIPRightTimeList.get(next+1).get("rights_start_dateTime"))){
                                        rightTemp.put("order_code", orderCode);
                                        rightTemp.put("rights_start_time", dateTimeFormatter.format(startTemp));
                                        rightTemp.put("rights_end_time", dateTimeFormatter.format(endTemp));
                                        orderRight.add(rightTemp);
                                        vipEndTime.add(endTemp);
                                        break;
                                    }
                                }else {
                                    // 交集  vip早于svip
                                    if ((startTemp.isBefore(rights_start_dateTime) && endTemp.isAfter(rights_start_dateTime))
                                            && !startTemp.isEqual(rights_start_dateTime)){
                                        //第一段  相等也会进入
                                        rightTemp.put("order_code", orderCode);
                                        rightTemp.put("rights_start_time", dateTimeFormatter.format(startTemp));
                                        rightTemp.put("rights_end_time", dateTimeFormatter.format(rights_start_dateTime));
                                        orderRight.add(rightTemp);

                                        startTemp = rights_end_dateTime;
                                        //延迟权益
                                        endTemp = endTemp.plusMonths(svipValidPeriodMonths).plusDays(svipRealGiveDays);

                                        if (startTemp.isAfter(endTemp)){
                                            //代表权益时间消耗结束
                                            break;
                                        }

                                    }else if(startTemp.isBefore(rights_end_dateTime) && endTemp.isAfter(rights_end_dateTime)){
                                        //vip晚于svip
                                        long dur = Duration.between(startTemp, endTemp).toDays();
                                        startTemp = rights_end_dateTime;
                                        endTemp = startTemp.plusDays(dur);
                                    }else{
                                        // svip完全包含 vip
                                       /* startTemp = rights_end_dateTime;
                                        endTemp = endTemp.plusMonths(svipValidPeriodMonths).plusDays(svipRealGiveDays);*/
                                        long dur = Duration.between(startTemp, endTemp).toDays();
                                        startTemp = rights_end_dateTime;
                                        endTemp = startTemp.plusDays(dur);
                                    }
                                    if ( next == SVIPRightTimeList.size() -1){
                                        JSONObject clone = (JSONObject) rightTemp.clone();
                                        clone.put("order_code", orderCode);
                                        clone.put("rights_start_time", dateTimeFormatter.format(startTemp));
                                        clone.put("rights_end_time", dateTimeFormatter.format(endTemp));
                                        orderRight.add(clone);
                                        vipEndTime.add(endTemp);
                                    }
                                }

                            }
                        }
                    }
                }


        }

        List<Object> finalList = orderRight.stream().sorted(Comparator.comparing(temp -> temp.getTimestamp("rights_start_time"))).collect(Collectors.toList());
        //在加上新版本未处理的订单权益
        finalList.addAll(filterAfter);
        export(finalList);

    }

    public void export(List<Object> order){
        System.out.println(order);
        order.forEach(temp -> {
            JSONObject json = (JSONObject) temp;
            String[] strs = new String[3];
            strs[0] = json.getString("order_code");
            strs[1] = json.getString("rights_start_time");
            strs[2] = json.getString("rights_end_time");
            try {
                if (strs[0] != null){
                    forward(strs);
                }

            } catch (HiveException e) {
                e.printStackTrace();
            }
        });
    }

    @Override
    public void close() throws HiveException {

    }

    /**
     * 静态方法，用于解析改版后的用户权益时间段
     */
    public static void main(String[] args) throws HiveException {
        Object[] item = new Object[1];
        //item[0] = "{\"vip_end_time\":\"2022-01-01 00:00:00\",\"svip_end_time\":\"NULL\",\"orders\":[{\"order_code\":\"1623806573509072\",\"order_total\":1.0,\"goods_type\":\"VIP\",\"pay_time\":\"2021-07-01 00:00:00\",\"end_time\":\"2022-07-01 00:00:00\",\"valid_period_months\":12},{\"order_code\":\"1623805619110054\",\"order_total\":1.0,\"goods_type\":\"SVIP\",\"pay_time\":\"2021-09-01 00:00:00\",\"end_time\":\"2022-09-01 00:00:00\",\"valid_period_months\":12}]}";
        //item[0] = "{\"vip_end_time\":\"2022-01-01 00:00:00\",\"svip_end_time\":\"NULL\",\"orders\":[{\"order_code\":\"1623806573509072\",\"order_total\":1.0,\"goods_type\":\"VIP\",\"pay_time\":\"2021-07-01 00:00:00\",\"end_time\":\"2022-07-01 00:00:00\",\"valid_period_months\":12},{\"order_code\":\"1623805619110054\",\"order_total\":1.0,\"goods_type\":\"SVIP\",\"pay_time\":\"2022-02-01 00:00:00\",\"end_time\":\"2023-02-01 00:00:00\",\"valid_period_months\":12}]}";
        //item[0] = "{\"vip_end_time\":\"2022-01-01 00:00:00\",\"svip_end_time\":\"NULL\",\"orders\":[{\"order_code\":\"1623806573509072\",\"order_total\":1.0,\"goods_type\":\"SVIP\",\"pay_time\":\"2021-07-01 00:00:00\",\"end_time\":\"2022-07-01 00:00:00\",\"valid_period_months\":12,\"real_give_days\":0},{\"order_code\":\"1623805619110054\",\"order_total\":1.0,\"goods_type\":\"VIP\",\"pay_time\":\"2021-09-01 00:00:00\",\"end_time\":\"2022-09-01 00:00:00\",\"valid_period_months\":12,\"real_give_days\":0}]}";
        //item[0] = "{\"vip_end_time\":\"NULL\",\"svip_end_time\":\"2022-01-01 00:00:00\",\"orders\":[{\"order_code\":\"1623806573509072\",\"order_total\":1.0,\"goods_type\":\"SVIP\",\"pay_time\":\"2021-07-01 00:00:00\",\"end_time\":\"2022-07-01 00:00:00\",\"valid_period_months\":12,\"real_give_days\":0},{\"order_code\":\"1623805619110054\",\"order_total\":1.0,\"goods_type\":\"VIP\",\"pay_time\":\"2021-09-01 00:00:00\",\"end_time\":\"2022-09-01 00:00:00\",\"valid_period_months\":12,\"real_give_days\":0}]}";
        //item[0]= "{\"vip_end_time\":\"2021-11-20 16:56:17\",\"svip_end_time\":\"2021-11-20 16:56:17\",\"orders\":[{\"order_code\":\"1653620864754122\",\"order_total\":360,\"goods_type\":\"VIP\",\"pay_time\":\"2022-05-27 11:07:56\",\"end_time\":\"2023-07-26 11:07:56\",\"valid_period_months\":12,\"real_give_days\":60},{\"order_code\":\"1638159087571333\",\"order_total\":980,\"goods_type\":\"SVIP\",\"pay_time\":\"2021-11-29 12:11:37\",\"end_time\":\"2022-05-29 12:11:37\",\"valid_period_months\":6,\"real_give_days\":0},{\"order_code\":\"1657618152743958\",\"order_total\":1800,\"goods_type\":\"SVIP\",\"pay_time\":\"2022-07-12 17:29:21\",\"end_time\":\"2023-07-12 17:29:21\",\"valid_period_months\":12,\"real_give_days\":0}]}";
        //item[0] = "{\"vip_end_time\":\"NULL\",\"svip_end_time\":\"NULL\",\"orders\":[{\"order_code\":\"1717471557431814\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-01-27 14:56:15\",\"end_time\":\"2020-01-27 14:56:15\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"3440551470155478\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-01-20 11:27:51\",\"end_time\":\"2021-01-20 11:27:51\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6759515049560468\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-03-04 12:49:20\",\"end_time\":\"2020-03-04 12:49:20\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1101017414611941\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-02 09:21:50\",\"end_time\":\"2020-11-02 09:21:50\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5674194411175171\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-02 09:21:55\",\"end_time\":\"2020-11-02 09:21:55\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1425827501382439\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-10-11 15:04:23\",\"end_time\":\"2020-10-11 15:04:23\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6857211404862187\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-05 09:08:00\",\"end_time\":\"2019-11-05 09:08:00\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5106594546470441\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-14 21:02:53\",\"end_time\":\"2019-11-14 21:02:53\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1844684147841751\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-10-22 11:20:14\",\"end_time\":\"2020-10-22 11:20:14\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1785308015598460\",\"goods_type\":\"SVIP\",\"pay_time\":\"2018-09-19 14:58:08\",\"end_time\":\"2021-09-19 14:58:08\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"2551558880014412\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-12-19 15:50:50\",\"end_time\":\"2020-12-19 15:50:50\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5151402908921584\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-16 14:59:56\",\"end_time\":\"2020-09-16 14:59:56\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6548614513119148\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-27 14:05:16\",\"end_time\":\"2019-09-27 14:05:16\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8909190648489815\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-10-13 16:09:43\",\"end_time\":\"2020-10-13 16:09:43\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1191278813438732\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-19 11:22:16\",\"end_time\":\"2020-09-19 11:22:16\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8373193041644894\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-12-04 14:43:14\",\"end_time\":\"2020-12-04 14:43:14\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"7972640035627212\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-23 13:59:42\",\"end_time\":\"2019-11-23 13:59:42\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5066655556417051\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-03-25 17:22:47\",\"end_time\":\"2020-03-25 17:22:47\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6450369925210655\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-02-19 11:34:25\",\"end_time\":\"2021-02-19 11:34:25\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6635101921663175\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-24 12:06:56\",\"end_time\":\"2020-09-24 12:06:56\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"0776177867739217\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-24 12:03:13\",\"end_time\":\"2020-09-24 12:03:13\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"3108373933258271\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-10-12 09:12:12\",\"end_time\":\"2020-10-12 09:12:12\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5474356379107055\",\"goods_type\":\"SVIP\",\"pay_time\":\"2018-11-05 16:43:13\",\"end_time\":\"2018-12-05 16:43:13\",\"valid_period_months\":1,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1549593354741511\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-12-05 10:38:19\",\"end_time\":\"2020-12-05 10:38:19\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8844118554447531\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-09 15:44:41\",\"end_time\":\"2019-11-09 15:44:41\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6151347243182416\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-12-07 10:52:08\",\"end_time\":\"2019-12-07 10:52:08\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5111761518491114\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-09 14:26:59\",\"end_time\":\"2019-11-09 14:26:59\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8547153455363335\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-03-27 16:31:47\",\"end_time\":\"2020-03-27 16:31:47\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4565987108438003\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-28 10:08:15\",\"end_time\":\"2019-09-28 10:08:15\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1609623497394260\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-28 18:36:09\",\"end_time\":\"2019-09-28 18:36:09\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5831175551135972\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-05 11:57:37\",\"end_time\":\"2020-09-05 11:57:37\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6436954159064201\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-10-13 16:11:03\",\"end_time\":\"2020-10-13 16:11:03\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"7408029713995155\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-08-21 10:42:36\",\"end_time\":\"2019-08-21 10:42:36\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6863831785331833\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-01-24 16:31:21\",\"end_time\":\"2021-01-24 16:31:21\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6881255549538085\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-03-13 20:08:20\",\"end_time\":\"2021-03-13 20:08:20\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8340050910010100\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-27 11:35:10\",\"end_time\":\"2020-09-27 11:35:10\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5024589505168193\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-12-19 11:11:29\",\"end_time\":\"2019-12-19 11:11:29\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"9845918272843032\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-27 14:14:26\",\"end_time\":\"2019-09-27 14:14:26\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4781299215009285\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-23 14:01:22\",\"end_time\":\"2019-11-23 14:01:22\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"0928424662341564\",\"goods_type\":\"SVIP\",\"pay_time\":\"2018-11-14 17:16:35\",\"end_time\":\"2019-11-14 17:16:35\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5108046736881461\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-02-24 13:42:58\",\"end_time\":\"2021-02-24 13:42:58\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"3334142466174059\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-08-13 15:43:41\",\"end_time\":\"2019-08-13 15:43:41\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1094850798745417\",\"goods_type\":\"SVIP\",\"pay_time\":\"2018-11-12 17:20:52\",\"end_time\":\"2020-11-12 17:20:52\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"0215295211841357\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-12-04 20:19:34\",\"end_time\":\"2019-12-04 20:19:34\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5338385695083535\",\"goods_type\":\"SVIP\",\"pay_time\":\"2018-08-27 13:45:53\",\"end_time\":\"2020-08-27 13:45:53\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4451317474245639\",\"goods_type\":\"SVIP\",\"pay_time\":\"2018-09-11 13:44:02\",\"end_time\":\"2020-09-11 13:44:02\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4705056265227568\",\"goods_type\":\"SVIP\",\"pay_time\":\"2018-08-20 09:26:40\",\"end_time\":\"2020-08-20 09:26:40\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4213540555034228\",\"goods_type\":\"SVIP\",\"pay_time\":\"2018-10-23 13:09:14\",\"end_time\":\"2020-10-23 13:09:14\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5086546722396837\",\"goods_type\":\"SVIP\",\"pay_time\":\"2019-03-13 20:09:37\",\"end_time\":\"2021-03-13 20:09:37\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8271651284240512\",\"goods_type\":\"SVIP\",\"pay_time\":\"2019-01-23 09:51:45\",\"end_time\":\"2021-01-23 09:51:45\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4660379641695989\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-01-08 19:14:21\",\"end_time\":\"2021-01-08 19:14:21\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5361715532382219\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-03-12 13:28:28\",\"end_time\":\"2021-03-12 13:28:28\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1199820558026579\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-08-12 12:48:03\",\"end_time\":\"2021-08-12 12:48:03\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"3413423846936696\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-01-28 09:57:44\",\"end_time\":\"2020-01-28 09:57:44\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1613887703365699\",\"goods_type\":\"SVIP\",\"pay_time\":\"2018-09-03 13:51:30\",\"end_time\":\"2020-09-03 13:51:30\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1633230475069626\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-12-11 10:00:57\",\"end_time\":\"2019-12-11 10:00:57\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4644771565048587\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-05 16:44:16\",\"end_time\":\"2019-11-05 16:44:16\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1411366440984825\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-01-22 16:44:29\",\"end_time\":\"2020-01-22 16:44:29\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8240828800871039\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-10-14 20:28:03\",\"end_time\":\"2021-10-14 20:28:03\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"9240700822278181\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-10-14 20:27:01\",\"end_time\":\"2021-10-14 20:27:01\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"0355915385263137\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-10-12 15:52:05\",\"end_time\":\"2021-10-12 15:52:05\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4648214625511552\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-08 10:04:48\",\"end_time\":\"2019-11-08 10:04:48\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"9426571201301886\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-01-15 14:10:40\",\"end_time\":\"2021-01-15 14:10:40\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"7797744733385795\",\"goods_type\":\"SVIP\",\"pay_time\":\"2018-09-12 11:34:57\",\"end_time\":\"2020-09-12 11:34:57\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"0013070925833823\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-16 16:28:17\",\"end_time\":\"2019-09-16 16:28:17\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"3757577291748326\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-22 14:56:19\",\"end_time\":\"2020-11-22 14:56:19\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4218097680811618\",\"goods_type\":\"SVIP\",\"pay_time\":\"2018-11-20 11:32:37\",\"end_time\":\"2020-11-20 11:32:37\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"7349107559915315\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-01-24 11:29:39\",\"end_time\":\"2021-01-24 11:29:39\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8903632636490142\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-23 17:50:23\",\"end_time\":\"2020-11-23 17:50:23\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"7497445411211644\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-01-28 09:49:21\",\"end_time\":\"2021-01-28 09:49:21\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8601015646614022\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-02-21 18:58:50\",\"end_time\":\"2021-02-21 18:58:50\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1933543641316951\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-19 16:31:59\",\"end_time\":\"2019-09-19 16:31:59\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1572139635187964\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-01-14 16:57:23\",\"end_time\":\"2020-01-14 16:57:23\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"7746170876575995\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-12 14:38:35\",\"end_time\":\"2019-09-12 14:38:35\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4099520553296951\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-01-15 16:08:25\",\"end_time\":\"2020-01-15 16:08:25\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1155991064389264\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-12-10 12:19:26\",\"end_time\":\"2019-12-10 12:19:26\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1494657537320621\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-10-24 13:53:53\",\"end_time\":\"2019-10-24 13:53:53\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6752425431771085\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-04-09 10:39:48\",\"end_time\":\"2022-04-09 10:39:48\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"7540720474103840\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-21 13:40:40\",\"end_time\":\"2019-09-21 13:40:40\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"3197633951254758\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-12-21 15:06:19\",\"end_time\":\"2019-12-21 15:06:19\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5411199993979595\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-10-19 09:59:57\",\"end_time\":\"2019-10-19 09:59:57\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1246459504595011\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-12-05 14:27:09\",\"end_time\":\"2019-12-05 14:27:09\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"}]}";
        //item[0] = "{\"vip_end_time\":\"NULL\",\"svip_end_time\":\"NULL\",\"orders\":[{\"order_code\":\"9748913500514310\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-07-02 15:04:45\",\"end_time\":\"2020-07-02 15:04:45\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5621377380741335\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-06-29 14:59:37\",\"end_time\":\"2020-06-29 14:59:37\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"9000745321800532\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-06-29 19:22:38\",\"end_time\":\"2021-06-29 19:22:38\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"2500776115831051\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-06-29 11:11:45\",\"end_time\":\"2021-06-29 11:11:45\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"7945654132698167\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-20 11:39:58\",\"end_time\":\"2021-09-20 11:39:58\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5334154763056107\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-21 09:57:26\",\"end_time\":\"2021-09-21 09:57:26\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6968703911411109\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-12 16:22:21\",\"end_time\":\"2019-09-12 16:22:21\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1498722543851231\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-06-09 17:49:48\",\"end_time\":\"2019-06-09 17:49:48\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"0688926929465967\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-06-19 11:33:30\",\"end_time\":\"2019-06-19 11:33:30\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6365561896313451\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-07-27 09:46:27\",\"end_time\":\"2019-07-27 09:46:27\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"3725381823541933\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-19 14:16:08\",\"end_time\":\"2019-09-19 14:16:08\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"3114551444318450\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-20 11:39:09\",\"end_time\":\"2019-09-20 11:39:09\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8401105550631971\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-06-28 18:42:38\",\"end_time\":\"2019-06-28 18:42:38\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"9309390625090698\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-07-26 15:28:31\",\"end_time\":\"2019-07-26 15:28:31\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4953055561443887\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-06-28 19:32:38\",\"end_time\":\"2019-06-28 19:32:38\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8417581344875448\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-20 11:39:37\",\"end_time\":\"2019-09-20 11:39:37\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4613872007160802\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-06-14 10:41:06\",\"end_time\":\"2019-06-14 10:41:06\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1817333321217310\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-18 10:34:05\",\"end_time\":\"2019-09-18 10:34:05\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"9525325168252245\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-06-29 14:53:48\",\"end_time\":\"2019-06-29 14:53:48\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"0841393888415296\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-20 19:25:57\",\"end_time\":\"2019-09-20 19:25:57\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"0307863116933043\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-21 11:41:52\",\"end_time\":\"2019-09-21 11:41:52\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4284703305515157\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-07-25 14:34:13\",\"end_time\":\"2019-07-25 14:34:13\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"2981512951851222\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-28 14:18:56\",\"end_time\":\"2019-09-28 14:18:56\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6061190633769368\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-07-03 15:31:29\",\"end_time\":\"2019-07-03 15:31:29\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"7970226445965406\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-10-25 17:53:48\",\"end_time\":\"2019-10-25 17:53:48\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"9168106083848573\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-14 11:46:41\",\"end_time\":\"2019-09-14 11:46:41\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"7017803875345897\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-17 16:22:59\",\"end_time\":\"2019-09-17 16:22:59\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"2218814243173332\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-20 19:27:20\",\"end_time\":\"2019-09-20 19:27:20\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"3935537965373046\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-06-19 11:33:59\",\"end_time\":\"2019-06-19 11:33:59\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"7946991652933534\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-07-25 11:41:31\",\"end_time\":\"2019-07-25 11:41:31\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5455155110737179\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-28 11:39:09\",\"end_time\":\"2019-09-28 11:39:09\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"2009092270359342\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-06-14 10:40:41\",\"end_time\":\"2019-06-14 10:40:41\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"3300403303348028\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-07-06 11:05:23\",\"end_time\":\"2019-07-06 11:05:23\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"9738835920234999\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-07-25 14:15:48\",\"end_time\":\"2019-07-25 14:15:48\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"0338373983019171\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-30 11:49:07\",\"end_time\":\"2019-09-30 11:49:07\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"7311521727359891\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-07-05 14:36:37\",\"end_time\":\"2019-07-05 14:36:37\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"2023221702925308\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-30 11:48:47\",\"end_time\":\"2019-09-30 11:48:47\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"2240212006270300\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-06-29 17:20:22\",\"end_time\":\"2019-06-29 17:20:22\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1147154120641158\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-28 14:17:27\",\"end_time\":\"2019-09-28 14:17:27\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8732048754655971\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-17 15:53:27\",\"end_time\":\"2019-09-17 15:53:27\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"9995190858822681\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-08-09 18:06:34\",\"end_time\":\"2019-08-09 18:06:34\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5708148211535530\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-28 14:22:28\",\"end_time\":\"2019-09-28 14:22:28\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1772475432153799\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-06-19 11:33:49\",\"end_time\":\"2019-06-19 11:33:49\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"}]}";
        //item[0] = "{\"vip_end_time\":\"NULL\",\"svip_end_time\":\"NULL\",\"orders\":[{\"order_code\":\"2681587888886058\",\"goods_type\":\"VIP\",\"pay_time\":\"2017-03-07 14:41:58\",\"end_time\":\"2020-03-07 14:41:58\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6104150948544119\",\"goods_type\":\"VIP\",\"pay_time\":\"2017-01-11 22:45:44\",\"end_time\":\"2020-01-11 22:45:44\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"2015101658018451\",\"goods_type\":\"VIP\",\"pay_time\":\"2015-10-16 09:55:49\",\"end_time\":\"2017-10-16 09:55:49\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8229040867780361\",\"goods_type\":\"VIP\",\"pay_time\":\"2016-08-20 23:26:34\",\"end_time\":\"2018-08-20 23:26:34\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"7537395589111992\",\"goods_type\":\"VIP\",\"pay_time\":\"2016-08-23 20:52:35\",\"end_time\":\"2017-08-23 20:52:35\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"7919053078888144\",\"goods_type\":\"VIP\",\"pay_time\":\"2017-01-15 21:28:29\",\"end_time\":\"2018-01-15 21:28:29\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5966511419923834\",\"goods_type\":\"VIP\",\"pay_time\":\"2016-07-28 21:45:13\",\"end_time\":\"2017-07-28 21:45:13\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1539941649349709\",\"goods_type\":\"VIP\",\"pay_time\":\"2017-01-14 00:18:11\",\"end_time\":\"2018-01-14 00:18:11\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"0248406640474046\",\"goods_type\":\"VIP\",\"pay_time\":\"2017-01-15 18:26:49\",\"end_time\":\"2018-01-15 18:26:49\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"0729074689799353\",\"goods_type\":\"VIP\",\"pay_time\":\"2016-11-19 15:15:57\",\"end_time\":\"2017-11-19 15:15:57\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8440904450255530\",\"goods_type\":\"VIP\",\"pay_time\":\"2016-11-13 11:59:09\",\"end_time\":\"2017-11-13 11:59:09\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1215584196456444\",\"goods_type\":\"VIP\",\"pay_time\":\"2017-01-15 22:31:18\",\"end_time\":\"2018-01-15 22:31:18\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"9347157784080058\",\"goods_type\":\"VIP\",\"pay_time\":\"2016-09-14 15:44:17\",\"end_time\":\"2017-09-14 15:44:17\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8448714014717908\",\"goods_type\":\"VIP\",\"pay_time\":\"2017-01-15 18:26:38\",\"end_time\":\"2018-01-15 18:26:38\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6739517794233829\",\"goods_type\":\"VIP\",\"pay_time\":\"2016-11-19 15:15:42\",\"end_time\":\"2017-11-19 15:15:42\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6467959111104520\",\"goods_type\":\"VIP\",\"pay_time\":\"2017-01-17 19:31:42\",\"end_time\":\"2018-01-17 19:31:42\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8864828440882019\",\"goods_type\":\"VIP\",\"pay_time\":\"2016-08-24 22:16:39\",\"end_time\":\"2017-08-24 22:16:39\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8199378473238771\",\"goods_type\":\"VIP\",\"pay_time\":\"2016-11-19 15:15:53\",\"end_time\":\"2017-11-19 15:15:53\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"0120621161221476\",\"goods_type\":\"VIP\",\"pay_time\":\"2016-08-20 23:26:12\",\"end_time\":\"2017-08-20 23:26:12\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5413566152292990\",\"goods_type\":\"VIP\",\"pay_time\":\"2017-04-15 21:12:09\",\"end_time\":\"2018-04-15 21:12:09\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1572940245443873\",\"goods_type\":\"SVIP\",\"pay_time\":\"2019-11-05 15:50:45\",\"end_time\":\"2020-11-05 15:50:45\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1173874471152894\",\"goods_type\":\"SVIP\",\"pay_time\":\"2018-11-24 17:28:21\",\"end_time\":\"2019-11-24 17:28:21\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8207bd6c37faea53656c62e85452f7cc\",\"goods_type\":\"SVIP\",\"pay_time\":\"2019-09-06 17:32:51\",\"end_time\":\"2020-09-06 17:32:51\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8155175675202206\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-12 16:02:13\",\"end_time\":\"2019-09-12 16:02:13\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"3927752074143733\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-12 15:56:42\",\"end_time\":\"2019-09-12 15:56:42\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8961919916164190\",\"goods_type\":\"SVIP\",\"pay_time\":\"2019-01-05 21:47:14\",\"end_time\":\"2022-01-05 21:47:14\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6465611063410701\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-07-03 18:45:10\",\"end_time\":\"2019-07-03 18:45:10\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5265007416464973\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-07-03 18:45:42\",\"end_time\":\"2019-07-03 18:45:42\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"0756266175122156\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-03-12 10:52:41\",\"end_time\":\"2019-03-12 10:52:41\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"9460178704487661\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-07-03 18:46:50\",\"end_time\":\"2019-07-03 18:46:50\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"0278225835574406\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-03-26 23:16:06\",\"end_time\":\"2021-03-26 23:16:06\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"9572346761169502\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-12 16:32:48\",\"end_time\":\"2021-09-12 16:32:48\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1613791056555605\",\"goods_type\":\"VIP\",\"pay_time\":\"2021-02-20 11:17:36\",\"end_time\":\"2024-02-20 11:17:36\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1599124274408476\",\"goods_type\":\"VIP\",\"pay_time\":\"2020-09-03 17:11:14\",\"end_time\":\"2023-09-03 17:11:14\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"2356078765511173\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-10-26 19:03:22\",\"end_time\":\"2021-10-26 19:03:22\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6243751806978483\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-12 15:55:21\",\"end_time\":\"2021-09-12 15:55:21\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1623394005545593\",\"goods_type\":\"VIP\",\"pay_time\":\"2021-06-11 14:46:45\",\"end_time\":\"2024-06-11 14:46:45\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1599124288300964\",\"goods_type\":\"VIP\",\"pay_time\":\"2020-09-03 17:11:28\",\"end_time\":\"2023-09-03 17:11:28\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"7620228842711328\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-07-04 11:10:07\",\"end_time\":\"2020-07-04 11:10:07\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4106862655556035\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-07-04 11:07:35\",\"end_time\":\"2022-07-04 11:07:35\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"0238205567626497\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-07-04 11:12:53\",\"end_time\":\"2020-07-04 11:12:53\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"2831990429334105\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-07-04 11:11:44\",\"end_time\":\"2020-07-04 11:11:44\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1623603268540712\",\"goods_type\":\"VIP\",\"pay_time\":\"2021-06-14 00:54:38\",\"end_time\":\"2021-07-14 00:54:38\",\"valid_period_months\":1,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1612497814293958\",\"goods_type\":\"SVIP\",\"pay_time\":\"2021-02-05 12:03:34\",\"end_time\":\"2024-02-05 12:03:34\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1637033444441902\",\"goods_type\":\"SVIP\",\"pay_time\":\"2021-11-16 11:30:44\",\"end_time\":\"2024-11-16 11:30:44\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1577340011714389\",\"goods_type\":\"SVIP\",\"pay_time\":\"2019-12-26 14:00:11\",\"end_time\":\"2022-12-26 14:00:11\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6691439431711166\",\"goods_type\":\"SVIP\",\"pay_time\":\"2019-08-20 15:45:53\",\"end_time\":\"2022-08-20 15:45:53\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1623303813281668\",\"goods_type\":\"SVIP\",\"pay_time\":\"2021-06-10 13:43:33\",\"end_time\":\"2024-06-10 13:43:33\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1590361823027260\",\"goods_type\":\"SVIP\",\"pay_time\":\"2020-05-25 07:10:23\",\"end_time\":\"2023-05-25 07:10:23\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1606911608844643\",\"goods_type\":\"SVIP\",\"pay_time\":\"2020-12-02 20:20:08\",\"end_time\":\"2023-12-02 20:20:08\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"3103080815055850\",\"goods_type\":\"SVIP\",\"pay_time\":\"2019-05-22 13:36:22\",\"end_time\":\"2022-05-22 13:36:22\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1576130574667569\",\"goods_type\":\"SVIP\",\"pay_time\":\"2019-12-12 14:02:54\",\"end_time\":\"2022-12-12 14:02:54\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1583233202321399\",\"goods_type\":\"SVIP\",\"pay_time\":\"2020-03-03 19:00:02\",\"end_time\":\"2023-03-03 19:00:02\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1606892574162881\",\"goods_type\":\"SVIP\",\"pay_time\":\"2020-12-02 15:02:54\",\"end_time\":\"2023-12-02 15:02:54\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1578020869701993\",\"goods_type\":\"SVIP\",\"pay_time\":\"2020-01-03 11:07:49\",\"end_time\":\"2023-01-03 11:07:49\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"3992977140656030\",\"goods_type\":\"SVIP\",\"pay_time\":\"2019-05-09 10:41:10\",\"end_time\":\"2022-05-09 10:41:10\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6102936216943991\",\"goods_type\":\"SVIP\",\"pay_time\":\"2019-08-28 19:15:34\",\"end_time\":\"2022-08-28 19:15:34\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"}]}";
        //item[0] = "{\"vip_end_time\":\"NULL\",\"svip_end_time\":\"NULL\",\"orders\":[{\"order_code\":\"1638064801533795\",\"goods_type\":\"SVIP\",\"pay_time\":\"2021-11-28 10:00:02\",\"end_time\":\"2021-12-28 10:00:02\",\"valid_period_months\":1,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1635472804728154\",\"goods_type\":\"SVIP\",\"pay_time\":\"2021-10-29 10:00:06\",\"end_time\":\"2021-11-29 10:00:06\",\"valid_period_months\":1,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1656122403114081\",\"goods_type\":\"SVIP\",\"pay_time\":\"2022-06-25 10:00:04\",\"end_time\":\"2022-07-25 10:00:04\",\"valid_period_months\":1,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1632967215232478\",\"goods_type\":\"SVIP\",\"pay_time\":\"2021-09-30 10:00:19\",\"end_time\":\"2021-10-31 10:00:19\",\"valid_period_months\":1,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1643594401621170\",\"goods_type\":\"SVIP\",\"pay_time\":\"2022-01-31 10:00:03\",\"end_time\":\"2022-02-28 10:00:03\",\"valid_period_months\":1,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1646445601471276\",\"goods_type\":\"SVIP\",\"pay_time\":\"2022-03-05 10:00:02\",\"end_time\":\"2022-04-05 10:00:02\",\"valid_period_months\":1,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1640916002891388\",\"goods_type\":\"SVIP\",\"pay_time\":\"2021-12-31 10:00:04\",\"end_time\":\"2022-01-31 10:00:04\",\"valid_period_months\":1,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1601106681264407\",\"goods_type\":\"SVIP\",\"pay_time\":\"2020-09-26 15:53:40\",\"end_time\":\"2020-10-26 15:53:40\",\"valid_period_months\":1,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1651629602717675\",\"goods_type\":\"SVIP\",\"pay_time\":\"2022-05-04 10:00:06\",\"end_time\":\"2022-06-04 10:00:06\",\"valid_period_months\":1,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1630375201548663\",\"goods_type\":\"SVIP\",\"pay_time\":\"2021-08-31 10:00:04\",\"end_time\":\"2021-09-30 10:00:04\",\"valid_period_months\":1,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1649037601139678\",\"goods_type\":\"SVIP\",\"pay_time\":\"2022-04-04 10:00:02\",\"end_time\":\"2022-05-04 10:00:02\",\"valid_period_months\":1,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1599084089068117\",\"goods_type\":\"VIP\",\"pay_time\":\"2020-09-03 06:01:41\",\"end_time\":\"2021-09-03 06:01:41\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1646267484678971\",\"goods_type\":\"VIP\",\"pay_time\":\"2022-03-03 08:31:35\",\"end_time\":\"2025-03-03 08:31:35\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"}]}";
        //item[0] = "{\"vip_end_time\":\"NULL\",\"svip_end_time\":\"NULL\",\"orders\":[{\"order_code\":\"1576031454491234\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-12-11 10:31:09\",\"end_time\":\"2022-12-11 10:31:09\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1666674591278054\",\"goods_type\":\"SVIP\",\"pay_time\":\"2022-10-25 13:10:16\",\"end_time\":\"2023-10-25 13:10:16\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1640267113012619\",\"goods_type\":\"SVIP\",\"pay_time\":\"2021-12-23 21:45:28\",\"end_time\":\"2022-12-23 21:45:28\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"7402537283244712\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-24 16:30:34\",\"end_time\":\"2019-11-24 16:30:34\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"}]}";
        //item[0] = "{\"vip_end_time\":\"NULL\",\"svip_end_time\":\"NULL\",\"orders\":[{\"order_code\":\"1606284177730926\",\"goods_type\":\"SVIP\",\"pay_time\":\"2020-11-25 14:03:03\",\"end_time\":\"2021-11-25 14:03:03\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1598884089037488\",\"goods_type\":\"VIP\",\"pay_time\":\"2020-08-31 22:28:17\",\"end_time\":\"2021-08-31 22:28:17\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1661334254056718\",\"goods_type\":\"SVIP\",\"pay_time\":\"2022-08-24 17:44:21\",\"end_time\":\"2025-08-24 17:44:21\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1638876002811944\",\"goods_type\":\"SVIP\",\"pay_time\":\"2021-12-07 19:20:12\",\"end_time\":\"2022-06-07 19:20:12\",\"valid_period_months\":6,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"}]}";
        OrderRightsReferenceUDTF3 udf = new OrderRightsReferenceUDTF3();
        udf.process(item);
    }
}
