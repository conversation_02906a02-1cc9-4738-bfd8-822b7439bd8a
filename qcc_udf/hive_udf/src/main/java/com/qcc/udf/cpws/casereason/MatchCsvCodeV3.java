package com.qcc.udf.cpws.casereason;

import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * 匹配Excel中的code，读取标签
 *
 * <AUTHOR>
 */
public class MatchCsvCodeV3 extends UDF {


    public static String evaluate(String reasonCode, String relatedTags,
                                  String content, String nameA<PERSON><PERSON><PERSON><PERSON><PERSON>, String defendant, String prosecutor) {
        return CaseLabelUtil.getCaseLabel(reasonCode,relatedTags,content,nameAnd<PERSON><PERSON><PERSON><PERSON>,defendant,prosecutor);
    }


    public static void main(String[] args) {
        String str = "";
        String reasonCode = "A,A03,A0303,A030306";
        System.out.println(evaluate(reasonCode,str,str,str,str,str));
    }
}
