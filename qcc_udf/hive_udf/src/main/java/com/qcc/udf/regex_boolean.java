package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class regex_boolean  extends UDF{

    public String evaluate(String s,String c){
        String result = "0";
        if(s!=null&&c!=null){
            Matcher mat = Pattern.compile(c).matcher(s);
            if(mat.find()){
                result = "1";
            }
        }

        return result;
    }
}
