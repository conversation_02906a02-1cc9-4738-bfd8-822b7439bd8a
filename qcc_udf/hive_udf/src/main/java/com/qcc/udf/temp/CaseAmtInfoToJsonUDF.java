package com.qcc.udf.temp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 从文本中统计案号
 *
 * @Auther: wuql
 * @Date: 2020/11/17 10:00
 * @Description:
 */
public class CaseAmtInfoToJsonUDF extends UDF {

    public static String evaluate(String amtInfoStr) {
        // 获取json
        String amtInfoJson = "[]";
        if (StringUtils.isBlank(amtInfoStr)) {
            return amtInfoJson;
        }

        return getAmtInfoJson(amtInfoStr);
    }

    private static String getAmtInfoJson(String amtInfoStr) {
        Map<String, Object> map = new HashMap<>();
        List<AmtInfo> amtRoleInfos = new ArrayList<>();
        try {
            map = JSON.parseObject(amtInfoStr, Map.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String keyNo = entry.getKey();
            if (StringUtils.isBlank(keyNo)) {
                continue;
            }

            AmtInfo amtInfo = null;
            try {
                amtInfo = JSONObject.parseObject(entry.getValue().toString(), AmtInfo.class);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (amtInfo != null) {
                amtInfo.setKeyNo(keyNo);
                amtRoleInfos.add(amtInfo);
            }
        }
        String amtInfoJson = JSON.toJSONString(amtRoleInfos, SerializerFeature.DisableCircularReferenceDetect);
        if (StringUtils.isBlank(amtInfoJson) || (StringUtils.isNotBlank(amtInfoJson) && amtInfoJson.length() > 65535)) {
            amtInfoJson = "[]";
        }

        return amtInfoJson;
    }


    public static void main(String[] args) {
        String amtInfoStr = "{\"p4de09896750740e66caef3cec88814d\":{\"Amt\":\"4620.00\",\"IsValid\":\"1\",\"Type\":\"案件金额\"},\"se4ae2b58f6ba1a2aefd8dd93320361e\":{\"Amt\":\"4620.00\",\"IsValid\":\"1\",\"Type\":\"案件金额\"},\"27a7e3b2bb256fe2e08dde9fa4954aff\":{\"Amt\":\"4639\",\"IsValid\":\"0\",\"Type\":\"执行标的\"}}";
        String evaluate = evaluate(amtInfoStr);
        System.out.println(evaluate);
    }
}
