package com.qcc.udf.liu;

import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.cpws.CommonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.text.SimpleDateFormat;
import java.util.*;

public class getDcrzInfo3 extends UDF {

    public static String evaluate(List<String> infoList)  throws Exception{
        JSONObject result = new JSONObject();
        String priority = "3";
        String dataStatus = "1";

        if(CollectionUtils.isNotEmpty(infoList)){
            long maxDate = 0L;
            long maxRegDate = 0L;
            Set<Long> dateSet = new LinkedHashSet<>();
            JSONObject maxInfo = null;
            JSONObject maxRegInfo = null;
            for (String str : infoList){
                JSONObject json = JSONObject.parseObject(str);
                String createDate = json.getString("createdate");
                if (CommonUtil.parseDateToTimeStamp(createDate) > maxDate){
                    maxDate = CommonUtil.parseDateToTimeStamp(createDate);
                    maxInfo = json;
                }
                String regDate = json.getString("regdate");
                if (CommonUtil.parseDateToTimeStamp(regDate) > maxRegDate){
                    maxRegDate = CommonUtil.parseDateToTimeStamp(regDate);
                    maxRegInfo = json;
                }
                dateSet.add(CommonUtil.parseDateToTimeStamp(json.getString("regdate")));
            }

            if (StringUtils.isEmpty(maxInfo.getString("businesstype")) && StringUtils.isEmpty(maxInfo.getString("regdate")) && "1".equals(maxInfo.getString("datastatus"))){
                priority = "3";
                dataStatus = maxInfo.getString("datastatus");
            }else{
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(sdf.parse(sdf.format(new Date())));
                long today = calendar.getTimeInMillis() / 1000;
                        //long today = System.currentTimeMillis()/1000;
                // 最近一周
                Set<Long> dateSet7 = new LinkedHashSet<>();
                Set<Long> dateSet7_30 = new LinkedHashSet<>();
                Set<Long> dateSet30_180 = new LinkedHashSet<>();
                Set<Long> dateSet180 = new LinkedHashSet<>();
                for (Long date : dateSet){
                    if ((today - date) <= (7*86400)){
                        dateSet7.add(date);
                    }
                    if ((today - date) > (7*86400) && (today - date) <= (30*86400)){
                        dateSet7_30.add(date);
                    }
                    if ((today - date) > (30*86400) && (today - date) <= (180*86400)){
                        dateSet30_180.add(date);
                    }
                    if ((today - date) > (180*86400)){
                        dateSet180.add(date);
                    }
                }

                // t-7=<regdate（最新一次请求），且近1个月有多笔，优先级为1，t-7=<regdate（进一个月只有本周有融资记录），或近1个月有多笔（进7天无融资记录）， 优先级为2 ，否则进行下一步
                if (dateSet7.size() > 0 && dateSet7_30.size() > 0){
                    priority = "1";
                }
                if (dateSet7.size() > 0 && dateSet7_30.size() == 0){
                    priority = "2";
                }
                /*if (dateSet7.size() == 0 && dateSet7_30.size() > 0){
                    priority = "2";
                }*/
                if (priority.equals("3")){
                    if (dateSet7_30.size() > 0 && dateSet30_180.size() > 0){
                        priority = "1";
                    }
                    if (dateSet7_30.size() > 0 && dateSet30_180.size() == 0){
                        priority = "2";
                    }
                    if (dateSet7_30.size() == 0 && dateSet30_180.size() > 0){
                        priority = "2";
                    }
                    if (priority.equals("3")){
                        if (dateSet30_180.size() > 0){
                            priority = "2";
                        }
                    }
                }

                dataStatus = maxInfo.getString("datastatus");
            }
        }

        result.put("Priority", priority);
        result.put("DataStatus", dataStatus);


        return result.toString();
    }


    public static void main(String[] args) {
        try {

            List<String> infoList = new LinkedList<>();
            //infoList.add("{\"id\":\"074f9ed15b6749819a7b128966bb6364\",\"createdate\":\"2022-11-09 08:03:51.0\",\"regdate\":\"2021-03-15 10:23:53\",\"businesstype\":\"应收账款转让（保理）\",\"datastatus\":\"1\",\"regno\":\"10598552001264615559\"}");
            //infoList.add("{\"id\":\"a1dcb097df3841218bae3fa666af8e77\",\"createdate\":\"2022-11-09 08:03:50.0\",\"regdate\":\"2022-09-21 14:58:57\",\"businesstype\":\"应收账款转让（保理）\",\"datastatus\":\"1\",\"regno\":\"19366272002379876285\"}");
            infoList.add("{\"id\":\"13fe441e716d4010a5c87b0e071335e1\",\"createdate\":\"2022-11-09 08:03:51.0\",\"regdate\":\"2022-11-07 20:55:27\",\"businesstype\":\"应收账款转让（保理）\",\"datastatus\":\"1\",\"regno\":\"20098425002492055997\"}");
            //infoList.add("{\"id\":\"5f5f008677464b5596f206ba803869bd\",\"createdate\":\"2022-11-09 08:03:51.0\",\"regdate\":\"2021-10-19 21:37:15\",\"businesstype\":\"生产设备、原材料、半成品、产品抵押\",\"datastatus\":\"1\",\"regno\":\"13560142001620402424\"}");
            infoList.add("{\"id\":\"45a7cae01efe450b81f8fe2a5233f60b\",\"createdate\":\"2022-11-09 08:03:51.0\",\"regdate\":\"2022-10-14 10:53:02\",\"businesstype\":\"应收账款转让（保理）\",\"datastatus\":\"1\",\"regno\":\"19708120002424321759\"}");
            //infoList.add("{\"id\":\"94274cfe79384f0bbf2dd0e2be04ca1a\",\"createdate\":\"2022-11-09 08:03:50.0\",\"regdate\":\"2019-12-12 13:50:29\",\"businesstype\":\"应收账款转让（保理）\",\"datastatus\":\"1\",\"regno\":\"07206035000853975797\"}");
            //infoList.add("{\"id\":\"cadd28d945774060a30cccafea131e32\",\"createdate\":\"2022-11-09 08:03:50.0\",\"regdate\":\"2021-03-09 18:03:47\",\"businesstype\":\"应收账款转让（保理）\",\"datastatus\":\"1\",\"regno\":\"10539898001257662012\"}");
            //infoList.add("{\"id\":\"a23552c2868647e8bf016356e046da11\",\"createdate\":\"2022-11-09 08:03:51.0\",\"regdate\":\"2018-06-25 14:03:42\",\"businesstype\":\"融资租赁\",\"datastatus\":\"1\",\"regno\":\"04672240000559043624\"}");

            System.out.println(evaluate(infoList));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
