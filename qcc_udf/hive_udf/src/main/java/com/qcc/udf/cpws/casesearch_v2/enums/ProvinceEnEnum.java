package com.qcc.udf.cpws.casesearch_v2.enums;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * 省份
 */
public enum ProvinceEnEnum {
    SH("SH", "沪","Shanghai"),
    AH("AH", "徽","Anhui"),
    BJ("BJ", "京","Beijing"),
    FJ("FJ", "闽","Fujian"),
    GS("GS", "甘","Gansu"),
    GD("GD", "粤","Guangdong"),
    GX("GX", "桂","Guangxi"),
    GZ("GZ", "贵","Guizhou"),
    HAIN("HAIN", "琼","Hainan"),
    HB("HB", "冀","Hebei"),
    HEN("HEN", "豫","Henan"),
    HLJ("HLJ", "黑","Heilongjiang"),
    HUB("HUB", "鄂","Hubei"),
    HUN("HUN", "湘","Hunan"),
    JL("JL", "吉","<PERSON><PERSON>"),
    JS("JS", "苏","<PERSON>su"),
    JX("JX", "赣","Jiangxi"),
    NL("NL", "辽","Liaoning"),
    NMG("NMG", "蒙","Inner Mongolia"),
    NX("NX", "宁","Ningxia"),
    QH("QH", "青","Qinghai"),
    SD("SD", "鲁","Shandong"),
    SX("SX", "晋","Shanxi"),
    SAX("SAX", "陕","Shaanxi"),
    SC("SC", "川","Sichuan"),
    TJ("TJ", "津","Tianjin"),
    XZ("XZ", "藏","Tibet"),
    XJ("XJ", "新","Sinkiang"),
    YN("YN", "云","Yunnan"),
    ZJ("ZJ", "浙","Zhejiang"),
    CQ("CQ", "渝","Chongqing");


    private String code;
    private String descCn;
    private String descEn;

    ProvinceEnEnum(String code, String descCn, String descEn) {
        this.code = code;
        this.descCn = descCn;
        this.descEn = descEn;
    }

    public String getCode() {
        return code;
    }

    private static final Map<String, ProvinceEnEnum> lookup = new HashMap<String, ProvinceEnEnum>();

    static {
        for (ProvinceEnEnum e : EnumSet.allOf(ProvinceEnEnum.class)) {
            lookup.put(e.descCn, e);
        }
    }

    public static ProvinceEnEnum find(String descCn) {
        ProvinceEnEnum data = lookup.get(descCn);
        if (descCn == null) {
            return null;
        }
        return data;
    }

}
