package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @Auther: wangbin
 * @Date: 2019/7/11 11:45
 * @Description:时间格式转换，时间戳转换成指定时间格式
 */
public class FormatTimes extends UDF {
    public String evaluate(String s, String format,int type) {
        //若源字符串和类型数据不合法
        if (s == null || !s.matches("\\d+") || (type != 10 && type != 13)) {
            return "0";
        } else {
            //格式化规则为空或null时赋默认规则
            if (format == null || "".equals(format)) {
                format = "yyyy-MM-dd";
            }
            SimpleDateFormat simpleDateFormat = null;
            //格式化规则不合法，捕获异常，返回0
            try {
                simpleDateFormat = new SimpleDateFormat(format);
            }catch (Exception e){
                return "0";
            }
            long lt = new Long(s);
            //时间早于1970年1月1日时赋默认值
            if (lt < 0) {
                return simpleDateFormat.format(new Date(0));
            }
            //按照10位时间戳处理判断
            if (type == 10 && s.length() <= 10) {
                if (lt >= new Long("7258089600")) {
                    return simpleDateFormat.format(new Date(new Long("7258089600")));
                }else{
                    lt*=1000;
                }
                //按照13位时间戳处理判断
            } else if (type ==13 &&  s.length() <= 13 ) {
                if (lt >= new Long("7258089600000")) {
                    return simpleDateFormat.format(new Date(new Long("7258089600000")));
                }
            } else {
                return "0";
            }
            return simpleDateFormat.format(new Date(lt));
        }
    }
}
