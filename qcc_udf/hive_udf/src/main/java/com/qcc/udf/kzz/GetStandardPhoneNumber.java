package com.qcc.udf.kzz;

import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import jdk.nashorn.internal.runtime.regexp.joni.Regex;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.stream.Collectors;

public class GetStandardPhoneNumber extends UDF {
    private static Set<String> PHONE_CODE = new HashSet<String>();

    static {
        PHONE_CODE.add("010");
        PHONE_CODE.add("020");
        PHONE_CODE.add("021");
        PHONE_CODE.add("022");
        PHONE_CODE.add("023");
        PHONE_CODE.add("024");
        PHONE_CODE.add("025");
        PHONE_CODE.add("027");
        PHONE_CODE.add("028");
        PHONE_CODE.add("029");
        PHONE_CODE.add("0310");
        PHONE_CODE.add("0311");
        PHONE_CODE.add("0312");
        PHONE_CODE.add("0313");
        PHONE_CODE.add("0314");
        PHONE_CODE.add("0315");
        PHONE_CODE.add("0316");
        PHONE_CODE.add("0317");
        PHONE_CODE.add("0318");
        PHONE_CODE.add("0319");
        PHONE_CODE.add("0335");
        PHONE_CODE.add("0349");
        PHONE_CODE.add("0350");
        PHONE_CODE.add("0351");
        PHONE_CODE.add("0352");
        PHONE_CODE.add("0353");
        PHONE_CODE.add("0354");
        PHONE_CODE.add("0355");
        PHONE_CODE.add("0356");
        PHONE_CODE.add("0357");
        PHONE_CODE.add("0358");
        PHONE_CODE.add("0359");
        PHONE_CODE.add("0370");
        PHONE_CODE.add("0371");
        PHONE_CODE.add("0372");
        PHONE_CODE.add("0373");
        PHONE_CODE.add("0374");
        PHONE_CODE.add("0375");
        PHONE_CODE.add("0376");
        PHONE_CODE.add("0377");
        PHONE_CODE.add("0379");
        PHONE_CODE.add("0391");
        PHONE_CODE.add("0392");
        PHONE_CODE.add("0393");
        PHONE_CODE.add("0394");
        PHONE_CODE.add("0395");
        PHONE_CODE.add("0396");
        PHONE_CODE.add("0398");
        PHONE_CODE.add("0411");
        PHONE_CODE.add("0412");
        PHONE_CODE.add("0414");
        PHONE_CODE.add("0415");
        PHONE_CODE.add("0416");
        PHONE_CODE.add("0417");
        PHONE_CODE.add("0418");
        PHONE_CODE.add("0419");
        PHONE_CODE.add("0421");
        PHONE_CODE.add("0427");
        PHONE_CODE.add("0429");
        PHONE_CODE.add("0431");
        PHONE_CODE.add("0432");
        PHONE_CODE.add("0433");
        PHONE_CODE.add("0434");
        PHONE_CODE.add("0435");
        PHONE_CODE.add("0436");
        PHONE_CODE.add("0437");
        PHONE_CODE.add("0438");
        PHONE_CODE.add("0439");
        PHONE_CODE.add("0451");
        PHONE_CODE.add("0452");
        PHONE_CODE.add("0453");
        PHONE_CODE.add("0454");
        PHONE_CODE.add("0455");
        PHONE_CODE.add("0456");
        PHONE_CODE.add("0457");
        PHONE_CODE.add("0458");
        PHONE_CODE.add("0459");
        PHONE_CODE.add("0464");
        PHONE_CODE.add("0467");
        PHONE_CODE.add("0468");
        PHONE_CODE.add("0469");
        PHONE_CODE.add("0470");
        PHONE_CODE.add("0471");
        PHONE_CODE.add("0472");
        PHONE_CODE.add("0473");
        PHONE_CODE.add("0474");
        PHONE_CODE.add("0475");
        PHONE_CODE.add("0476");
        PHONE_CODE.add("0477");
        PHONE_CODE.add("0478");
        PHONE_CODE.add("0479");
        PHONE_CODE.add("0482");
        PHONE_CODE.add("0483");
        PHONE_CODE.add("0510");
        PHONE_CODE.add("0511");
        PHONE_CODE.add("0512");
        PHONE_CODE.add("0513");
        PHONE_CODE.add("0514");
        PHONE_CODE.add("0515");
        PHONE_CODE.add("0516");
        PHONE_CODE.add("0517");
        PHONE_CODE.add("0518");
        PHONE_CODE.add("0519");
        PHONE_CODE.add("0523");
        PHONE_CODE.add("0527");
        PHONE_CODE.add("0530");
        PHONE_CODE.add("0531");
        PHONE_CODE.add("0532");
        PHONE_CODE.add("0533");
        PHONE_CODE.add("0534");
        PHONE_CODE.add("0535");
        PHONE_CODE.add("0536");
        PHONE_CODE.add("0537");
        PHONE_CODE.add("0538");
        PHONE_CODE.add("0539");
        PHONE_CODE.add("0543");
        PHONE_CODE.add("0546");
        PHONE_CODE.add("0550");
        PHONE_CODE.add("0551");
        PHONE_CODE.add("0552");
        PHONE_CODE.add("0553");
        PHONE_CODE.add("0554");
        PHONE_CODE.add("0555");
        PHONE_CODE.add("0556");
        PHONE_CODE.add("0557");
        PHONE_CODE.add("0558");
        PHONE_CODE.add("0559");
        PHONE_CODE.add("0561");
        PHONE_CODE.add("0562");
        PHONE_CODE.add("0563");
        PHONE_CODE.add("0564");
        PHONE_CODE.add("0566");
        PHONE_CODE.add("0570");
        PHONE_CODE.add("0571");
        PHONE_CODE.add("0572");
        PHONE_CODE.add("0573");
        PHONE_CODE.add("0574");
        PHONE_CODE.add("0575");
        PHONE_CODE.add("0576");
        PHONE_CODE.add("0577");
        PHONE_CODE.add("0578");
        PHONE_CODE.add("0579");
        PHONE_CODE.add("0580");
        PHONE_CODE.add("0591");
        PHONE_CODE.add("0592");
        PHONE_CODE.add("0593");
        PHONE_CODE.add("0594");
        PHONE_CODE.add("0595");
        PHONE_CODE.add("0596");
        PHONE_CODE.add("0597");
        PHONE_CODE.add("0598");
        PHONE_CODE.add("0599");
        PHONE_CODE.add("0631");
        PHONE_CODE.add("0632");
        PHONE_CODE.add("0633");
        PHONE_CODE.add("0634");
        PHONE_CODE.add("0635");
        PHONE_CODE.add("0660");
        PHONE_CODE.add("0662");
        PHONE_CODE.add("0663");
        PHONE_CODE.add("0668");
        PHONE_CODE.add("0691");
        PHONE_CODE.add("0692");
        PHONE_CODE.add("0701");
        PHONE_CODE.add("0710");
        PHONE_CODE.add("0711");
        PHONE_CODE.add("0712");
        PHONE_CODE.add("0713");
        PHONE_CODE.add("0714");
        PHONE_CODE.add("0715");
        PHONE_CODE.add("0716");
        PHONE_CODE.add("0717");
        PHONE_CODE.add("0718");
        PHONE_CODE.add("0719");
        PHONE_CODE.add("0722");
        PHONE_CODE.add("0724");
        PHONE_CODE.add("0728");
        PHONE_CODE.add("0730");
        PHONE_CODE.add("0731");
        PHONE_CODE.add("0734");
        PHONE_CODE.add("0735");
        PHONE_CODE.add("0736");
        PHONE_CODE.add("0737");
        PHONE_CODE.add("0738");
        PHONE_CODE.add("0739");
        PHONE_CODE.add("0743");
        PHONE_CODE.add("0744");
        PHONE_CODE.add("0745");
        PHONE_CODE.add("0746");
        PHONE_CODE.add("0750");
        PHONE_CODE.add("0751");
        PHONE_CODE.add("0752");
        PHONE_CODE.add("0753");
        PHONE_CODE.add("0754");
        PHONE_CODE.add("0755");
        PHONE_CODE.add("0756");
        PHONE_CODE.add("0757");
        PHONE_CODE.add("0758");
        PHONE_CODE.add("0759");
        PHONE_CODE.add("0760");
        PHONE_CODE.add("0762");
        PHONE_CODE.add("0763");
        PHONE_CODE.add("0766");
        PHONE_CODE.add("0768");
        PHONE_CODE.add("0769");
        PHONE_CODE.add("0770");
        PHONE_CODE.add("0771");
        PHONE_CODE.add("0772");
        PHONE_CODE.add("0773");
        PHONE_CODE.add("0774");
        PHONE_CODE.add("0775");
        PHONE_CODE.add("0776");
        PHONE_CODE.add("0777");
        PHONE_CODE.add("0778");
        PHONE_CODE.add("0779");
        PHONE_CODE.add("0790");
        PHONE_CODE.add("0791");
        PHONE_CODE.add("0792");
        PHONE_CODE.add("0793");
        PHONE_CODE.add("0794");
        PHONE_CODE.add("0795");
        PHONE_CODE.add("0796");
        PHONE_CODE.add("0797");
        PHONE_CODE.add("0798");
        PHONE_CODE.add("0799");
        PHONE_CODE.add("0812");
        PHONE_CODE.add("0813");
        PHONE_CODE.add("0816");
        PHONE_CODE.add("0817");
        PHONE_CODE.add("0818");
        PHONE_CODE.add("0825");
        PHONE_CODE.add("0826");
        PHONE_CODE.add("0827");
        PHONE_CODE.add("0830");
        PHONE_CODE.add("0831");
        PHONE_CODE.add("0832");
        PHONE_CODE.add("0833");
        PHONE_CODE.add("0834");
        PHONE_CODE.add("0835");
        PHONE_CODE.add("0836");
        PHONE_CODE.add("0837");
        PHONE_CODE.add("0838");
        PHONE_CODE.add("0839");
        PHONE_CODE.add("0851");
        PHONE_CODE.add("0852");
        PHONE_CODE.add("0853");
        PHONE_CODE.add("0854");
        PHONE_CODE.add("0855");
        PHONE_CODE.add("0856");
        PHONE_CODE.add("0857");
        PHONE_CODE.add("0858");
        PHONE_CODE.add("0859");
        PHONE_CODE.add("0870");
        PHONE_CODE.add("0871");
        PHONE_CODE.add("0872");
        PHONE_CODE.add("0873");
        PHONE_CODE.add("0874");
        PHONE_CODE.add("0875");
        PHONE_CODE.add("0876");
        PHONE_CODE.add("0877");
        PHONE_CODE.add("0878");
        PHONE_CODE.add("0879");
        PHONE_CODE.add("0883");
        PHONE_CODE.add("0886");
        PHONE_CODE.add("0887");
        PHONE_CODE.add("0888");
        PHONE_CODE.add("0891");
        PHONE_CODE.add("0892");
        PHONE_CODE.add("0893");
        PHONE_CODE.add("0894");
        PHONE_CODE.add("0895");
        PHONE_CODE.add("0896");
        PHONE_CODE.add("0897");
        PHONE_CODE.add("0898");
        PHONE_CODE.add("0901");
        PHONE_CODE.add("0902");
        PHONE_CODE.add("0903");
        PHONE_CODE.add("0906");
        PHONE_CODE.add("0908");
        PHONE_CODE.add("0909");
        PHONE_CODE.add("0910");
        PHONE_CODE.add("0911");
        PHONE_CODE.add("0912");
        PHONE_CODE.add("0913");
        PHONE_CODE.add("0914");
        PHONE_CODE.add("0915");
        PHONE_CODE.add("0916");
        PHONE_CODE.add("0917");
        PHONE_CODE.add("0919");
        PHONE_CODE.add("0930");
        PHONE_CODE.add("0931");
        PHONE_CODE.add("0932");
        PHONE_CODE.add("0933");
        PHONE_CODE.add("0934");
        PHONE_CODE.add("0935");
        PHONE_CODE.add("0936");
        PHONE_CODE.add("0937");
        PHONE_CODE.add("0938");
        PHONE_CODE.add("0939");
        PHONE_CODE.add("0941");
        PHONE_CODE.add("0943");
        PHONE_CODE.add("0951");
        PHONE_CODE.add("0952");
        PHONE_CODE.add("0953");
        PHONE_CODE.add("0954");
        PHONE_CODE.add("0955");
        PHONE_CODE.add("0970");
        PHONE_CODE.add("0971");
        PHONE_CODE.add("0972");
        PHONE_CODE.add("0973");
        PHONE_CODE.add("0974");
        PHONE_CODE.add("0975");
        PHONE_CODE.add("0976");
        PHONE_CODE.add("0979");
        PHONE_CODE.add("0990");
        PHONE_CODE.add("0991");
        PHONE_CODE.add("0992");
        PHONE_CODE.add("0993");
        PHONE_CODE.add("0994");
        PHONE_CODE.add("0995");
        PHONE_CODE.add("0996");
        PHONE_CODE.add("0997");
        PHONE_CODE.add("0998");
        PHONE_CODE.add("0999");
    }

//    public static void main(String[] args) {
//        String testMsg = "13900000000";
//        String result = evaluate(testMsg);
//        System.out.printf(result);
//    }

    public static String evaluate(String multiSource) {
        try {
            String source = StringUtils.isNotBlank(multiSource) ? multiSource : "";
            source = CommonV3Util.full2Half(source);
            source = source.replace("、", ",");
            List<String> resultList = new ArrayList<>();
            List<String> sourceList = Arrays.asList(source.split(","));
            if (CollectionUtils.isNotEmpty(sourceList)) {
                for (String item : sourceList) {
                    String result = getNumber(item);
                    if (StringUtils.isBlank(result) || !IsCorrectNumber(result)) {
                        continue;
                    }
                    resultList.add(result);
                }
            }
            return StringUtils.join(resultList, ",");
        } catch (Exception e) {
            return "";
        }
    }

    private static String getNumber(String originNumber) {
        String newNumber = originNumber;
        //去除各种特殊字符
        newNumber = newNumber.replace((char) 12288, ' ').replaceAll("[\\s\\?#%\\[\\]\\{\\}【】\\(\\)\\+:=]+", "");
        // 去除邮箱脏数据
        String emailRegex = "[A-Za-z0-9_]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+";
        if (RegexHelper.isFind(newNumber, emailRegex)) {
            newNumber = newNumber.replaceAll(emailRegex, "");
        }
        //去除类似通讯地址编号
        String addressRegex = "[\\d]+(路|号|楼|层|座|室|幢|栋|省道|国道|县道|乡道)";
        if (RegexHelper.isFind(newNumber, addressRegex)) {
            newNumber = newNumber.replaceAll(addressRegex, "");
        }
        // 转.中文-转换为英文-
        newNumber = newNumber.replaceAll("((总机)?转)|\\.|—", "-");
        //去除所有的中文字符
        String chnEngRegex = "[\\u4e00-\\u9fa5a-zA-Z]+";
        if (RegexHelper.isFind(newNumber, chnEngRegex)) {
            newNumber = newNumber.replaceAll(chnEngRegex, "");
        }
        //去除连续的-字符
        newNumber = newNumber.replaceAll("-{2,}", "-");
        //去除+86等开头的数据
        newNumber = newNumber.replaceAll("^[^1]?86", "");
        newNumber = newNumber.replaceAll("^(-)", "").replaceAll("(-)$", "");
        newNumber = newNumber.replaceAll("[\\s\\\\]+", "");
        //电话号码纠正处理
        if (newNumber.length() <= 5) {
            return "";
        }
        //标准手机号直接通过
        if (newNumber.length() == 11 && RegexHelper.isMatch(newNumber, "^1\\d{10}$")) {
            return newNumber;
        }
        //非标准手机号
        if (RegexHelper.isMatch(newNumber, "^1\\d{2}-?\\d{4}-?\\d{4}$")) {
            newNumber = newNumber.replaceAll("-", "");
            return newNumber;
        }
        //标准400 800
        if (RegexHelper.isMatch(newNumber, "^(400|800)-?\\d{3}-?\\d{4}$")) {
            //newNumber = newNumber.replaceAll("-", "");
            //return newNumber.substring(0, 3) + "-" + newNumber.substring(3, 6) + "-" + newNumber.substring(6);
            return "";
        }
        //固话
        if (newNumber.length() < 9) {
            return "";
        }
        //标准固话直接返回
        if (RegexHelper.isMatch(newNumber, "^0\\d{2,3}-\\d{7,8}(-\\d{1,4})?$")) {
            if (PHONE_CODE.contains(newNumber.split("-")[0])) {
                return newNumber;
            } else {
                return "";
            }
        }
        //非标准化固话处理1
        if (RegexHelper.isMatch(newNumber, "^0?\\d{2,3}-?\\d{7,8}$")) {
            newNumber = newNumber.startsWith("0") ? newNumber : "0" + newNumber;
            if (newNumber.contains("-")) {
                String qh = newNumber.split("-")[0];
                if (PHONE_CODE.contains(qh)) {
                    return newNumber;
                } else {
                    return "";
                }
            } else {
                String threeQh = newNumber.substring(0, 3);
                String fourQh = newNumber.substring(0, 4);
                if (PHONE_CODE.contains(threeQh)) {
                    String phonePart = newNumber.replaceAll("^" + threeQh, "");
                    if (phonePart.length() >= 7 && phonePart.length() <= 8) {
                        return threeQh + "-" + phonePart;
                    } else {
                        return "";
                    }
                } else if (PHONE_CODE.contains(fourQh)) {
                    String phonePart = newNumber.replaceAll("^" + fourQh, "");
                    if (phonePart.length() >= 7 && phonePart.length() <= 8) {
                        return fourQh + "-" + phonePart;
                    } else {
                        return "";
                    }
                } else {
                    return "";
                }
            }
        }
        //非标准化固话处理2
        if (RegexHelper.isMatch(newNumber, "^0?\\d{2,3}-\\d{3,4}-\\d{3,4}$")) {
            newNumber = newNumber.startsWith("0") ? newNumber : "0" + newNumber;
            String[] splitArr = newNumber.split("-");
            String qh = splitArr[0];
            String part = splitArr[1] + splitArr[2];
            if (PHONE_CODE.contains(qh) && part.length() >= 7 && part.length() <= 8) {
                return qh + "-" + part;
            } else {
                return "";
            }
        }
        return "";
    }

    /**
     * 是否是正确的电话号码
     * */
    private static boolean IsCorrectNumber(String phoneNo) {
        boolean iFlag = true;
        if (StringUtils.isNotBlank(phoneNo)) {
            //座机
            if (phoneNo.contains("-")) {
                String partNo = phoneNo.split("-")[1];
                //固话以0/1开头都是错误
                if (partNo.startsWith("0") || partNo.startsWith("1")) {
                    iFlag = false;
                } else if (RegexHelper.isFind(partNo, "([\\d])\\1{6}")) {
                    //号码为重复号码
                    iFlag = false;
                } else if (IsSequenceNumber(partNo)) {
                    //号码为连续号码
                    iFlag = false;
                }
            } else {
                //手机号码
                if (RegexHelper.isFind(phoneNo, "([\\d])\\1{7}")) {
                    iFlag = false;
                } else if (IsSequenceNumber(phoneNo)) {
                    iFlag = false;
                }
            }
        } else {
            iFlag = false;
        }
        return iFlag;
    }

    /**
     * 是否是连续号码
     */
    private static boolean IsSequenceNumber(String no) {
        if (StringUtils.isBlank(no)) {
            return true;
        }
        List<Integer> tmpList = new ArrayList<>();
        for (char info : no.toCharArray()) {
            tmpList.add(Integer.valueOf(info));
        }
        if (tmpList.size() < 7) {
            return true;
        }
        List<Integer> stepList = new ArrayList<>();
        for (int i = 0; i < tmpList.size(); i++) {
            if (i + 1 == tmpList.size()) {
                break;
            }
            int firstItem = tmpList.get(i);
            int secondItem = tmpList.get(i + 1);
            int diff = secondItem - firstItem;
            stepList.add(diff);
        }
        stepList = stepList.stream().distinct().collect(Collectors.toList());
        //步长全部相同也是连续号码
        if (stepList.size() == 1) {
            return true;
        }
        return false;
    }
}