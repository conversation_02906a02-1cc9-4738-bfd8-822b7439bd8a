package com.qcc.udf;

import jodd.util.StringUtil;
import org.apache.hadoop.hive.ql.exec.UDF;

public class StringFilterEmoji extends UDF {

    public static boolean evaluate(String str) {
        StringBuffer out = new StringBuffer();

        if (str == null || ("".equals(str)))
            return true;
        char[] chars = str.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            if ((chars[i] >= 19968 && chars[i] <= 40869) //中日朝兼容形式的unicode编码范围： U+4E00——U+9FA5
                    || (chars[i] >= 11904 && chars[i] <= 42191)//中日朝兼容形式扩展
                    || (chars[i] >= 63744 && chars[i] <= 64255)//中日朝兼容形式扩展
                    || (chars[i] >= 65072 && chars[i] <= 65103)//中日朝兼容形式扩展
                    || (chars[i] >= 65280 && chars[i] <= 65519)//全角ASCII、全角中英文标点、半宽片假名、半宽平假名、半宽韩文字母的unicode编码范围：U+FF00——U+FFEF
                    || (chars[i] >= 32 && chars[i] <= 126)//半角字符的unicode编码范围：U+0020-U+007e
                    || (chars[i] >= 12289 && chars[i] <= 12319)//全角字符的unicode编码范围：U+3000——U+301F
                    || (chars[i] >=8217 && (chars[i] <=8222)) //“和”左右双引号183，8217
                    || (chars[i] >=8544 && (chars[i] <=8553))//罗马数字
                    || (chars[i] ==183
                    || chars[i] ==65117
                    || chars[i] ==65118
            )
            ) {
                out.append(chars[i]);
            } else
                return false;
        }
        return true;
    }

    //由月要好好学习天天向??
    //1、꧁꫞꯭西安分公司负责人，全面主持各项事务；
    //鐢辨湀瑕佸ソ濂藉涔犲ぉ澶╁悜涓?
    //    Ҫ ¨² ѧϰ
    //ç”±æœˆè¦ å¥½å¥½å­¦ä¹ å¤å¤å ‘ä¸Š
    public static void main(String[] args) {
        /*String evaluate = new StringFilterEmoji().evaluate("ÓÉÔÂÒªºÃºÃÑ§Ï°ÌìÌìÏòÉÏ电脑上面的");
        System.out.print(evaluate);*/
        System.out.println(evaluate("根据烟台市局《关于在全市开展长期停业不经营企业集中清理工作的通知》烟市监字〔2020〕8号的通知中附件3：长期不经营企业提醒公告名单中包括“招远市天鑫电力工程有限公司”，我局于2020年6月20日予以立案调查。"));
        /*char[] chars = "222被完全九五七年看完你".toCharArray();
        System.out.println(chars.length);*/
    }

}