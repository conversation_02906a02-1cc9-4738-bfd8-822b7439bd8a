package com.qcc.udf.cpws.wjp;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class CleanExecutedNoUdf extends UDF {
    public static String evaluate(String _no1, String _no2, String _no3, String _no4, String subjectCode) {
        if (StringUtils.isEmpty(subjectCode) || "0".equals(subjectCode)) {
            return "empty";
        }
        Set<String> noSet = new HashSet<>(4);
        noSet.add(_no1);
        noSet.add(_no2);
        noSet.add(_no3);
        noSet.add(_no4);
        String[] arr = subjectCode.split("\\*\\*\\*\\*");
        //判断是否相等或者相似
        int sameFlag = (int) noSet.stream().filter(StringUtils::isNotEmpty).filter(kn -> {
            // 相似度
            int likeFlag = (int) Arrays.stream(arr).filter(kn::contains).count();
            // 相同
            if (kn.contains(subjectCode)) {
                return true;
                // 每个分块相同（整体相似）
            } else {
                return likeFlag >= arr.length;
            }

        }).count();

        if (sameFlag >= 1) {
            return "true";
        }
        return "false";
    }


    public static void main(String[] args) {
        System.out.println(evaluate(null, null, null, null, "12****5"));
    }
}
