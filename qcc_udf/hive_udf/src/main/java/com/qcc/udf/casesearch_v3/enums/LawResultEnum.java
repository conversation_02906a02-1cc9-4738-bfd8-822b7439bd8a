package com.qcc.udf.casesearch_v3.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 判决结果枚举
 * <AUTHOR>
 * @date 2021年09月16日 15:52
 */
@Getter
public enum LawResultEnum {
    /**
     * * 1  不支持
     * * 2  对方不被支持
     * * 3  支持
     * * 4  对方被支持
     * * 5  撤诉
     * * 6  对方撤诉
     * * 7  驳回
     * * 8  部分支持
     * * 9  对方被部分支持
     * * 10  视为撤诉
     * * 11  不承担责任
     * * 12  诉讼中止
     * * 13  无
     * * 14  达成调解
     * * 15  查封、扣押、冻结
     * * 16  解除查封、扣押、冻结
     * * 17    执行完毕
     */

    BU_ZHI_CHI("1", ""),
    DUI_FANG_BU_BEI_ZHI_CHI("2", ""),
    ZHI_CHI("3", ""),
    DUI_FANG_BEI_ZHI_CHI("4", ""),
    CHE_SU("5", ""),
    DUI_FANG_CHE_SU("6", ""),
    BO_HUI("7", ""),
    BU_FEN_ZHI_CHI("8", ""),
    DUI_FANG_BEI_BU_FEN_ZHI_CHI("9", ""),
    SHI_WEI_CHE_SU("10", ""),
    BU_CHENG_DAN_ZE_RENG("11", ""),
    SU_SONG_ZHONG_ZHI("12", ""),
    WU("13", ""),
    DA_CHENG_TIAO_JIE("14", ""),
    CHA_FENG_ZAN_KOU_DONG_JIE("15", ""),
    JIE_CHU_CHA_FENG_ZAN_KOU_DONG_JIE("16", ""),
    ZHI_XING_WAN_B("17", "");

    private String code;
    private String desc;


    LawResultEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getSourceName(int source){
        for (LawResultEnum value : LawResultEnum.values()) {
            if(Objects.equals(source,value.getCode())){
                return value.getDesc();
            }
        }
        return "";
    }
}
