package com.qcc.udf.tender;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.dictionary.CustomDictionary;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 招投标高频词统计
 * @author: hugx
 * @create: 2022-04-06 13:44
 */
public class HighFrequencyWord extends UDF {
    private static List<String> stopWords = new ArrayList<>();

    static {
        // 中文停用词 ，后期放到表里面
        stopWords.add(" ");
    }

    /**
     * 招投标高频词统计
     *
     * @param value 正文数据
     * @return
     */
    public static String evaluate(String value) {
        JSONArray jsonArray = new JSONArray();
        //正文为空直接返回
        if (StringUtils.isBlank(value)) {
            return JSON.toJSONString(jsonArray);
        }
        List<String> termList = new ArrayList<>();
        termList.addAll(HanLP.segment(getNormalContent(getText(value))).stream().map(a -> a.word).collect(Collectors.toList()));
        //去掉招投标高频词
        termList.removeAll(stopWords);

        Map<String, Integer> map = new HashMap<>();
        for (String str : termList) {
            //定义一个计数器，用来记录重复数据的个数
            int i = 1;
            if (map.get(str) != null) {
                i = map.get(str) + 1;
            }
            map.put(str, i);
        }

        for (String word : map.keySet()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("key", word);
            jsonObject.put("value", map.get(word));
            jsonArray.add(jsonObject);
        }
//        //排名靠前的关键词
//        List<String> rankList = map.entrySet().stream()
//                .sorted((Map.Entry<String, Integer> o1, Map.Entry<String, Integer> o2) -> o2.getValue() - o1.getValue())
//                .map(entry -> entry.getKey()).collect(Collectors.toList())
//                .subList(0, rank);

        return JSON.toJSONString(jsonArray);
    }

    /**
     * html去除标签
     *
     * @param html
     * @return
     */
    public static String getText(String html) {
        Document doc = Jsoup.parse(html);
        String content = doc.text();
        StringBuilder builder = new StringBuilder(content);
        int index = 0;
        while (builder.length() > index) {
            char tmp = builder.charAt(index);
            if (Character.isSpaceChar(tmp) || Character.isWhitespace(tmp)) {
                builder.setCharAt(index, ' ');
            }
            index++;
        }
        return builder.toString().replaceAll(" +", " ").trim();
    }

    /**
     * 获取英文 中文 数字
     *
     * @param str 文本
     * @return content
     */
    public static String getNormalContent(String str) {
        if (StringUtils.isEmpty(str)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        if (str.contains("关键字：")) {
            String[] strs = str.split("关键字：");
            if (strs.length == 1 || (strs.length == 2 && strs[1].length() < 30)) {
                str = strs[0];
            }
        }

        if (str.contains("附件：")) {
            String[] strs = str.split("附件：");
            if (strs.length == 1 || (strs.length == 2 && strs[1].length() < 30)) {
                str = strs[0];
            }
        }

        for (char item : str.toCharArray()) {
            if (charReg(item)) {
                sb.append(item);
            }
        }
        return sb.toString().toLowerCase();
    }

    private static boolean charReg(char charValue) {
        return (charValue >= 0x4E00 && charValue <= 0X9FA5) || (charValue >= 'a' && charValue <= 'z')
                || (charValue >= 'A' && charValue <= 'Z') || (charValue >= '0' && charValue <= '9');
    }

    public static void main(String[] args) {
        String str = null;

        System.out.println(evaluate(str));
    }
}