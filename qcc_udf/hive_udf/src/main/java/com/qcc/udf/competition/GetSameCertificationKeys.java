package com.qcc.udf.competition;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/07/21 21:55
 * @description ：
 */
public class GetSameCertificationKeys extends UDF {

    public String evaluate(String certificationItem, String certificationOther) {

        String result = "0";
        if (StringUtils.isEmpty(certificationItem) || StringUtils.isEmpty(certificationOther)) {
            return result;
        }

        String[] itemStr = certificationItem.split(",");
        List<String> itemList = Arrays.asList(itemStr);

        String[] otherStr = certificationOther.split(",");
        List<String> otherList = Arrays.asList(otherStr);

        for(String item: itemList) {
            if (otherList.contains(item)) {
                result = "1";
                break;
            }
        }

        return result;
    }

    public static void main(String[] args) {
        GetSameCertificationKeys getSameCertificationKeys = new GetSameCertificationKeys();
        String one = "1,2,3,4";
        String two = "8,5,6,7";
        System.out.println(getSameCertificationKeys.evaluate(one, two));
    }

}
