package com.qcc.udf.risk_graph;

import cn.hutool.core.util.ReUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import com.qcc.udf.namekeyno.NameUtil;
import com.qcc.udf.temp.MD5Util;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2021/3/4
 */
public class GetRiskRelationUDF extends UDF {
    public String evaluate(String startid, int starttype, String endid, int endtype, List<String> sourceList) {
        RiskRelationOutputEntity relationOutput = new RiskRelationOutputEntity(startid, starttype, endid, endtype);
        Set<RiskRelationBaseResult> relationResults = new HashSet<>();
        sourceList.stream().filter(str -> StringUtils.isNotBlank(str))
                .forEach(item -> {
                    RiskRelationBaseResult relationResult = JSONObject.parseObject(item.toString(), RiskRelationBaseResult.class);
                    relationResults.add(relationResult);
                });

        String idstr = "";
        if (startid.compareTo(endid) < 0) {
            idstr = startid + endid;
        } else {
            idstr = endid + startid;
        }
        relationOutput.setId(MD5Util.encode(idstr));
        int org1 = NameUtil.getOrgByKeyNo(startid);
        relationOutput.setMainOrg(org1);
        int org2 = NameUtil.getOrgByKeyNo(endid);
        relationOutput.setSecondOrg(org2);
        setRiskDataInfos(relationResults, relationOutput);

        return JSON.toJSONString(relationOutput, SerializerFeature.DisableCircularReferenceDetect);
    }

    private int getForwardFlag(String startid, String endid, String semd5) {
        int forwardFlag = -1;
        //指向 startid -> endid  正向
        if (Objects.equals(semd5, MD5Util.encode(startid + endid))) {
            forwardFlag = 1;
        }
        if (Objects.equals(semd5, MD5Util.encode(endid + startid))) {
            forwardFlag = 0;
        }
        return forwardFlag;
    }

    private void setRiskDataInfos(Set<RiskRelationBaseResult> relationResults, RiskRelationOutputEntity relationOutput) {
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy/MM/dd");
        List<RiskDataInfo> infos = Lists.newArrayList();
        //根据风险维度分组
        Map<String, Set<RiskRelationBaseResult>> wdMap = relationResults.stream()
                .filter(e -> e != null && StringUtils.isNotBlank(e.getName())).collect(Collectors.groupingBy(e -> e.getName(), Collectors.toSet()));
        //count统计
        int count = 0;
        for (Map.Entry<String, Set<RiskRelationBaseResult>> entry : wdMap.entrySet()) {
            String name = entry.getKey().toUpperCase();
            //Map<String, Set<RiskRelationBaseResult>> semd5Map = entry.getValue().stream().filter(e -> e != null && StringUtils.isNotBlank(e.getSemd5())).collect(Collectors.groupingBy(e -> e.getSemd5(), Collectors.toSet()));
            Map<String, Set<RiskRelationBaseResult>> semd5Map = entry.getValue().stream()
                    .filter(e -> e != null && StringUtils.isNotBlank(e.getSemd5()))
                    .collect(
                            Collectors.groupingBy(e -> {
                                if (e.getName().equals("JYXX")) {
                                    return e.getSemd5() + e.getProp3();
                                } else {
                                    return e.getSemd5();
                                }
                            }, Collectors.toSet()));
            for (Map.Entry<String, Set<RiskRelationBaseResult>> fEntry : semd5Map.entrySet()) {
                RiskDataInfo riskDataInfo = new RiskDataInfo();
                RiskCategoryEnum riskCategoryEnum = RiskCategoryEnum.find(name);
                String wdDesc = "";
                String wdName = "";
                if (riskCategoryEnum != null) {
                    RiskWdEnum riskWd = riskCategoryEnum.getRiskWd();
                    wdDesc = riskWd.getDesc();
                    wdName = riskWd.name();
                }
                riskDataInfo.setRtName(name);
                riskDataInfo.setWdName(wdName);
                riskDataInfo.setWdDesc(wdDesc);
                if (Objects.equals("TZXX", name)){
                    riskDataInfo.setRtName("TZXX");
                    riskDataInfo.setWdName("TZXX");
                    riskDataInfo.setWdDesc("投资关系");
                }
                if (Objects.equals("RZXX", name)){
                    riskDataInfo.setRtName("RZXX");
                    riskDataInfo.setWdName("RZXX");
                    riskDataInfo.setWdDesc("任职信息");
                }

                String splitFlag =",";
                //WGCL,CCJC,DWDB,JYYC. 无id，特殊处理
                if (isDetails(name)){
                    splitFlag ="#";
                }

                Set<RiskRelationBaseResult> forwardEntry = fEntry.getValue();
                String ids = forwardEntry.stream().filter(e -> e != null && StringUtils.isNotBlank(e.getIds())).map(RiskRelationBaseResult::getIds).collect(Collectors.joining(splitFlag));
                Set<String> idsSet = Arrays.stream(ids.split(splitFlag)).filter(str -> StringUtils.isNotBlank(str)).collect(Collectors.toSet());
                /**
                 * 交易关系 0423
                 */
                if ("JYXX".equals(wdName)) {

                    List<String> nums = forwardEntry.stream().filter(Objects::nonNull).filter(e -> StringUtils.isNotBlank(e.getProp2())).map(RiskRelationBaseResult::getProp2)
                            .collect(Collectors.toList());
                    double sSum = 0;
                    for (String entity : nums) {
                        String[] split = entity.split(",");
                        for (int i = 0; i < split.length; i++) {
                            if (StringUtils.isNotEmpty(split[i])) {
                                sSum += Double.parseDouble(split[i]);
                            }
                        }
                        riskDataInfo.setTradeAmount(String.format("%.2f", sSum));
                    }
                    forwardEntry.forEach(e -> {
                        //rtName 处理
                        riskDataInfo.setForwardFlag(0);
                        riskDataInfo.setRtName(Integer.parseInt(e.getProp3()) == 0 ? "GYS" : "KH");
                    });
                }else if (Objects.equals("TZXX", name) || Objects.equals("RZXX", name)){

                }else {
                    // 金额计算
                    BigDecimal amt1 = new BigDecimal("0");
                    amt1.setScale(2, RoundingMode.HALF_UP);
                    BigDecimal amt2 = new BigDecimal("0");
                    amt2.setScale(2, RoundingMode.HALF_UP);
                    String prop2 = forwardEntry.stream().filter(e -> e != null && StringUtils.isNotBlank(e.getProp2())).map(RiskRelationBaseResult::getProp2).collect(Collectors.joining(splitFlag));
                    String prop3 = forwardEntry.stream().filter(e -> e != null && StringUtils.isNotBlank(e.getProp3())).map(RiskRelationBaseResult::getProp3).collect(Collectors.joining(splitFlag));
                    if (StringUtils.isNotEmpty(prop2)){
                        String[] arr = prop2.split(splitFlag);
                        for (String str : arr){
                            if (StringUtils.isNotEmpty(str)){
                                BigDecimal bd = new BigDecimal(str);
                                bd.setScale(2, RoundingMode.HALF_UP);
                                amt1 = amt1.add(bd);
                            }
                        }
                    }
                    riskDataInfo.setAmt1(amt1.toPlainString());
                    if (StringUtils.isNotEmpty(prop3)){
                        String[] arr = prop3.split(splitFlag);
                        for (String str : arr){
                            if (StringUtils.isNotEmpty(str)){
                                BigDecimal bd = new BigDecimal(str);
                                bd.setScale(2, RoundingMode.HALF_UP);
                                amt2 = amt2.add(bd);
                            }
                        }
                    }
                    riskDataInfo.setAmt2(amt2.toPlainString());

                }


                String idsRes ="";
                String details ="";
                if (isDetails(name)){
                    details = JSON.toJSONString(idsSet, SerializerFeature.DisableCircularReferenceDetect);
                    details = ReUtil.replaceAll(details,"\\{\\\\\"\\$numberLong\\\\\":\\\\\"(\\d+)\\\\\"\\}|\\{\\\"\\$numberLong\\\":\\\"(\\d+)\\\"\\}","$1");
                    if (Objects.equals("DWDB", name)){
                        JSONArray array = new JSONArray();
                        for (String str : idsSet){
                            try{
                                JSONArray items = JSONArray.parseArray(str);

                                array.addAll(items);
                            } catch (Exception e){
                            }
                        }
                        details = array.toJSONString();
                    }
                }else if (Objects.equals("TZXX", name)){
                    JSONArray array = new JSONArray();
                    List<String> tzxxList = forwardEntry.stream().map(RiskRelationBaseResult::getInfo).collect(Collectors.toList());
                    for (String str : tzxxList){
                        JSONObject jsonObject = new JSONObject();
                        JSONObject jsonObject1 = JSONObject.parseObject(str);
                        String startKeyNo = jsonObject1.getString("startid");
                        String startName = jsonObject1.getString("startname");
                        int startOrg = CommonV3Util.getOrgByKeyNo(startKeyNo,startName);
                        String endKeyNo = jsonObject1.getString("endid");
                        String endName = jsonObject1.getString("endname");
                        int endOrg = CommonV3Util.getOrgByKeyNo(endKeyNo,endName);
                        String sortDate = jsonObject1.getString("sortdate");
                        String stockPercent = jsonObject1.getString("stockpercent");

                        JSONObject startInfo = new JSONObject();
                        startInfo.put("KeyNo", startKeyNo);
                        startInfo.put("Name", startName);
                        startInfo.put("Org", startOrg);
                        JSONObject endInfo = new JSONObject();
                        endInfo.put("KeyNo", endKeyNo);
                        endInfo.put("Name", endName);
                        endInfo.put("Org", endOrg);

                        jsonObject.put("StartInfo", startInfo);
                        jsonObject.put("EndInfo", endInfo);
                        jsonObject.put("StockPercent", stockPercent.replace("%", ""));
                        jsonObject.put("Indate", Long.parseLong(sortDate));

                        array.add(jsonObject);
                    }
                    riskDataInfo.setDetails(array.toString());
                    details = array.toString();
                }else if (Objects.equals("RZXX", name)){
                    JSONArray array = new JSONArray();
                    List<String> tzxxList = forwardEntry.stream().map(RiskRelationBaseResult::getInfo).collect(Collectors.toList());
                    for (String str : tzxxList){
                        JSONObject jsonObject = new JSONObject();
                        JSONObject jsonObject1 = JSONObject.parseObject(str);
                        String startKeyNo = jsonObject1.getString("startid");
                        String startName = jsonObject1.getString("startname");
                        int startOrg = CommonV3Util.getOrgByKeyNo(startKeyNo,startName);
                        String endKeyNo = jsonObject1.getString("endid");
                        String endName = jsonObject1.getString("endname");
                        int endOrg = CommonV3Util.getOrgByKeyNo(endKeyNo,endName);
                        String sortDate = jsonObject1.getString("sortdate");

                        JSONObject json = getJob(jsonObject1.getString("job"));
                        int jobCnt = json.getIntValue("Cnt");
                        String job = json.getString("Job");

                        JSONObject startInfo = new JSONObject();
                        startInfo.put("KeyNo", startKeyNo);
                        startInfo.put("Name", startName);
                        startInfo.put("Org", startOrg);
                        JSONObject endInfo = new JSONObject();
                        endInfo.put("KeyNo", endKeyNo);
                        endInfo.put("Name", endName);
                        endInfo.put("Org", endOrg);

                        jsonObject.put("StartInfo", startInfo);
                        jsonObject.put("EndInfo", endInfo);
                        jsonObject.put("Job", job);
                        jsonObject.put("Indate", Long.parseLong(sortDate));
                        jsonObject.put("Cnt", jobCnt);
                        jsonObject.put("IsMain", json.getIntValue("IsMain"));

                        array.add(jsonObject);
                    }
                    riskDataInfo.setDetails(array.toString());
                    details = array.toString();
                }else {
                    idsRes = ids;
                }
                if (!"JYXX".equals(wdName)) {
                    forwardEntry.stream().forEach(e -> {
                        riskDataInfo.setForwardFlag(getForwardFlag(e.getStartid(), e.getEndid(), fEntry.getKey()));
                    });
                }
                int wdCount = idsSet.size();
                if (isDetails(name)){
                    if (Objects.equals("DWDB", name)){
                        wdCount = JSONArray.parseArray(details).size();
                    }
                }
                if (Objects.equals("TZXX", name)){
                    wdCount = JSONArray.parseArray(riskDataInfo.getDetails()).size();
                }
                if (Objects.equals("RZXX", name)){
                    wdCount = JSONArray.parseArray(riskDataInfo.getDetails()).getJSONObject(0).getInteger("Cnt");
                }
                count += wdCount;
                riskDataInfo.setWdCount(wdCount);
                riskDataInfo.setIds(idsRes);
                riskDataInfo.setDetails(details);
                infos.add(riskDataInfo);
            }
        }
        Set<String> wdSet = wdMap.keySet().stream().filter(str -> StringUtils.isNotBlank(str) && RiskCategoryEnum.find(str) != null).map(name -> RiskCategoryEnum.find(name).getRiskWd().toString()).collect(Collectors.toSet());
        relationOutput.setWdNames(StringUtils.join(wdSet,","));
        relationOutput.setCount(count);
        relationOutput.setInfoList(infos);

        // 计算是否在详情显示    1.count>=2；2.司法案件cnt=1时，限高，失信，被执行三个维度之外，其他维度cnt之和大于1
        int showFlag = 0;
        if (relationOutput.getCount() > 1){
            List<RiskDataInfo> infoList = relationOutput.getInfoList();
            int csCnt = 0;
            int otherCnt = 0;
            for (RiskDataInfo item : infoList){
                if ("SFAJ".equals(item.getWdName())){
                    csCnt = csCnt + item.getWdCount();
                }else{
                    if (!"SX".equals(item.getWdName()) && !"ZX".equals(item.getWdName()) && !"XG".equals(item.getWdName())){
                        otherCnt = otherCnt + item.getWdCount();
                    }
                }
            }

            if (csCnt == 1 && otherCnt == 0){
                showFlag = 0;
            }else{
                showFlag = 1;
            }
        }
        relationOutput.setShowFlag(showFlag);

        // 获取时间轴
        List<RiskDataInfo2> infoList = new LinkedList<>();
        List<String> sortList = new LinkedList<>();
        for (RiskRelationBaseResult item : relationResults){
            String info = item.getInfo();
            if (StringUtils.isNotEmpty(info)){
                String[] arr = info.split("######");
                for (String str : arr){
                    JSONObject jsonObject = JSONObject.parseObject(str);
                    if ("WGCL".equals(jsonObject.getString("wdname"))){
                        String[] aaa = JSONObject.parseObject(str).getString("wdid").split("#");
                        for (String a : aaa){
                            try {
                                JSONObject json = JSONObject.parseObject(a);
                                long sortDate = 0;
                                try {
                                    sortDate = sdf1.parse(json.getString("publicdate")).getTime()/1000;
                                }catch (Exception e){
                                }
                                sortList.add(String.valueOf(sortDate).concat("######").concat(JSON.toJSONString(json, SerializerFeature.DisableCircularReferenceDetect)).concat("######").concat(JSONObject.parseObject(str).getString("wdname")));

                            }catch (Exception e){

                            }
                        }
                    }else if ("DWDB".equals(jsonObject.getString("wdname"))){
                        String[] aaa = JSONObject.parseObject(str).getString("wdid").split("#");
                        for (String a : aaa){
                            try {
                                JSONArray array = JSONArray.parseArray(a);
                                Iterator<Object> it = array.iterator();
                                while(it.hasNext()){
                                    JSONObject json = (JSONObject)it.next();
                                    long sortDate = 0;
                                    try {
                                        sortDate = sdf2.parse(json.getString("PublicDate")).getTime()/1000;
                                    }catch (Exception e){
                                    }
                                    sortList.add(String.valueOf(sortDate).concat("######").concat(JSON.toJSONString(json, SerializerFeature.DisableCircularReferenceDetect)).concat("######").concat(JSONObject.parseObject(str).getString("wdname")));
                                }
                            }catch (Exception e){

                            }
                        }
                    }else if ("CCJC".equals(jsonObject.getString("wdname"))){
                        String[] aaa = JSONObject.parseObject(str).getString("wdid").split("#");
                        for (String a : aaa){
                            try {
                                JSONObject json = JSONObject.parseObject(a);
                                long sortDate = 0;
                                try {
                                    sortDate = Long.parseLong(json.getJSONObject("Date").getString("$numberLong"));
                                }catch (Exception e){
                                }
                                json.put("Date", sortDate);
                                sortList.add(String.valueOf(sortDate).concat("######").concat(JSON.toJSONString(json, SerializerFeature.DisableCircularReferenceDetect)).concat("######").concat(JSONObject.parseObject(str).getString("wdname")));

                            }catch (Exception e){

                            }
                        }
                    }else if ("JYYC".equals(jsonObject.getString("wdname"))){
                        String[] aaa = JSONObject.parseObject(str).getString("wdid").split("#");
                        for (String a : aaa){
                            try {
                                JSONObject json = JSONObject.parseObject(a);
                                long sortDate = 0;
                                try {
                                    sortDate = Long.parseLong(json.getJSONObject("PublishDate").getString("$numberLong"));
                                }catch (Exception e){
                                }
                                json.put("PublishDate", sortDate);

                                long addDate = 0;
                                try {
                                    addDate = Long.parseLong(json.getJSONObject("AddDate").getString("$numberLong"));
                                }catch (Exception e){
                                }
                                json.put("AddDate", addDate);

                                long removeDate = 0;
                                try {
                                    removeDate = Long.parseLong(json.getJSONObject("RemoveDate").getString("$numberLong"));
                                }catch (Exception e){
                                }
                                json.put("RemoveDate", removeDate);
                                sortList.add(String.valueOf(addDate).concat("######").concat(JSON.toJSONString(json, SerializerFeature.DisableCircularReferenceDetect)).concat("######").concat(JSONObject.parseObject(str).getString("wdname")));

                            }catch (Exception e){

                            }
                        }
                    } else if ("TZXX".equals(jsonObject.getString("wdname"))){
                        sortList.add(jsonObject.getString("sortdate").concat("######").concat(info).concat("######").concat(jsonObject.getString("wdname")));
                    } else if ("RZXX".equals(jsonObject.getString("wdname"))){
                        sortList.add(jsonObject.getString("sortdate").concat("######").concat(info).concat("######").concat(jsonObject.getString("wdname")));
                    } else if ("JYXX".equals(jsonObject.getString("wdname"))){
                        sortList.add(jsonObject.getString("sortdate").concat("######").concat(jsonObject.getString("wdid")).concat("######").concat(jsonObject.getString("wdname")).concat("######").concat(jsonObject.getString("rtname")));
                    }
                    else {
                        sortList.add(jsonObject.getString("sortdate").concat("######").concat(jsonObject.getString("wdid")).concat("######").concat(jsonObject.getString("wdname")));
                    }
                }
            }
        }
        Collections.sort(sortList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o2.compareTo(o1);
            }
        });

        for (String str : sortList){
            RiskDataInfo2 item = new RiskDataInfo2();
            if ("WGCL".equals(str.split("######")[2]) || "DWDB".equals(str.split("######")[2]) || "CCJC".equals(str.split("######")[2]) || "JYYC".equals(str.split("######")[2]) || "TZXX".equals(str.split("######")[2]) || "RZXX".equals(str.split("######")[2])){
                item.setDetails(str.split("######")[1]);
                if ("TZXX".equals(str.split("######")[2])){

                    JSONObject jsonObject = new JSONObject();
                    JSONObject jsonObject1 = JSONObject.parseObject(item.getDetails());
                    String startKeyNo = jsonObject1.getString("startid");
                    String startName = jsonObject1.getString("startname");
                    int startOrg = CommonV3Util.getOrgByKeyNo(startKeyNo,startName);
                    String endKeyNo = jsonObject1.getString("endid");
                    String endName = jsonObject1.getString("endname");
                    int endOrg = CommonV3Util.getOrgByKeyNo(endKeyNo,endName);
                    String sortDate = jsonObject1.getString("sortdate");
                    String stockPercent = jsonObject1.getString("stockpercent");

                    JSONObject startInfo = new JSONObject();
                    startInfo.put("KeyNo", startKeyNo);
                    startInfo.put("Name", startName);
                    startInfo.put("Org", startOrg);
                    JSONObject endInfo = new JSONObject();
                    endInfo.put("KeyNo", endKeyNo);
                    endInfo.put("Name", endName);
                    endInfo.put("Org", endOrg);

                    List<RiskDataInfo> tmpList = relationOutput.getInfoList();
                    for (RiskDataInfo sub : tmpList){
                        if ("TZXX".equals(sub.getWdName())){
                            JSONObject detail = JSONArray.parseArray(sub.getDetails()).getJSONObject(0);
                            if (sub.getForwardFlag() == 1){
                                startInfo = detail.getJSONObject("StartInfo");
                                endInfo = detail.getJSONObject("EndInfo");
                            }

                            if (sub.getForwardFlag() == 0){
                                startInfo = detail.getJSONObject("EndInfo");
                                endInfo = detail.getJSONObject("StartInfo");
                            }
                        }
                    }

                    jsonObject.put("StartInfo", startInfo);
                    jsonObject.put("EndInfo", endInfo);
                    jsonObject.put("StockPercent", stockPercent.replace("%", ""));
                    jsonObject.put("Indate", Long.parseLong(sortDate));

                    item.setDetails(jsonObject.toString());
                }

                if ("RZXX".equals(str.split("######")[2])){

                    JSONObject jsonObject = new JSONObject();
                    JSONObject jsonObject1 = JSONObject.parseObject(item.getDetails());
                    String startKeyNo = jsonObject1.getString("startid");
                    String startName = jsonObject1.getString("startname");
                    int startOrg = CommonV3Util.getOrgByKeyNo(startKeyNo,startName);
                    String endKeyNo = jsonObject1.getString("endid");
                    String endName = jsonObject1.getString("endname");
                    int endOrg = CommonV3Util.getOrgByKeyNo(endKeyNo,endName);
                    String sortDate = jsonObject1.getString("sortdate");
                    String job = getJob(jsonObject1.getString("job")).getString("Job");

                    JSONObject startInfo = new JSONObject();
                    startInfo.put("KeyNo", startKeyNo);
                    startInfo.put("Name", startName);
                    startInfo.put("Org", startOrg);
                    JSONObject endInfo = new JSONObject();
                    endInfo.put("KeyNo", endKeyNo);
                    endInfo.put("Name", endName);
                    endInfo.put("Org", endOrg);

                    List<RiskDataInfo> tmpList = relationOutput.getInfoList();
                    for (RiskDataInfo sub : tmpList){
                        if ("RZXX".equals(sub.getWdName())){
                            JSONObject detail = JSONArray.parseArray(sub.getDetails()).getJSONObject(0);
                            if (sub.getForwardFlag() == 1){
                                startInfo = detail.getJSONObject("StartInfo");
                                endInfo = detail.getJSONObject("EndInfo");
                            }

                            if (sub.getForwardFlag() == 0){
                                startInfo = detail.getJSONObject("EndInfo");
                                endInfo = detail.getJSONObject("StartInfo");
                            }
                        }
                    }

                    jsonObject.put("StartInfo", startInfo);
                    jsonObject.put("EndInfo", endInfo);
                    jsonObject.put("Job", job);
                    jsonObject.put("Indate", Long.parseLong(sortDate));

                    item.setDetails(jsonObject.toString());
                }
            }else {
                item.setId(str.split("######")[1]);
            }
            item.setWdName(str.split("######")[2]);
            item.setSortDate(Long.parseLong(str.split("######")[0]));

            if ("JYXX".equals(str.split("######")[2])){
                item.setRtName(str.split("######")[3]);
            }

            if (infoList.size() < 500){
                infoList.add(item);
            }
        }

        relationOutput.setInfoList2(infoList);
    }

    private boolean isDetails(String name) {
        if (Objects.equals("WGCL", name)){
            return true;
        }
        if (Objects.equals("CCJC", name)){
            return true;
        }
        if (Objects.equals("DWDB", name)){
            return true;
        }
        if (Objects.equals("JYYC", name)){
            return true;
        }
        return  false;
    }

    private JSONObject getJob(String param){
        JSONObject jsonObject = new JSONObject();
        String job = "";
        int cnt = 0;
        int isMain = 0;
        if (StringUtils.isNotEmpty(param)) {
            String[] jobArr = param.split("###");
            Set<String> jobSet = new LinkedHashSet<>();
            for (String str : jobArr) {
                jobSet.add(str);
            }
            // Comparator.comparing(CompanyKcbSpider::getUpdateDate,Comparator.reverseOrder()
            jobSet.stream().sorted(Comparator.reverseOrder());

            Set<String> jobSetNew = new LinkedHashSet<>();
            int main1 = 0;
            int main2 = 0;
            for (String str : jobSet) {
                if (str.contains("0-----")){
                    main1 = 1;
                }
                if (str.contains("1-----")){
                    main2 = 1;
                }
                String[] arr = str.replace("0-----", "").replace("1-----", "").split(",");
                for (String sub : arr) {
                    jobSetNew.add(sub);
                }
            }

            if (main1 == 1 && main2 == 0){
                isMain = 2;
            }
            if (main1 == 0 && main2 == 1){
                isMain = 1;
            }
            if (main1 == 1 && main2 == 1){
                isMain = 3;
            }

            if (jobSetNew.size() > 0) {
                job = String.join(",", jobSetNew);
            }
            cnt = jobSetNew.size();
        }

        jsonObject.put("Job", job);
        jsonObject.put("Cnt", cnt);
        jsonObject.put("IsMain", isMain);

        return jsonObject;
    }

    public static void main(String[] args) {
//String risk1 = "{\"name\":\"SQXG\",\"startid\":\"rr6b2b8decf66d1b17789cdf6fb3a795\",\"endid\":\"0000070574e7405c2c97e323d991e612\",\"ids\":\"9bd926453f77116ccdfe02cdcc74927e,338d1efcb77f5ded7207a57ccef9a44d,0c9405f7d5a733301f4a247989562f46\",\"semd5\":\"420d87a57968fb92e3bc694270918f29\"}";
        //String risk11 = "{\"name\":\"GLXG\",\"startid\":\"rr6b2b8decf66d1b17789cdf6fb3a795\",\"endid\":\"0000070574e7405c2c97e323d991e612\",\"ids\":\"9bd926453f77116ccdfe02cdcc74928e,438d1efcb77f5ded7207a57ccef9a44d,5c9405f7d5a733301f4a247989562f46\",\"semd5\":\"420d87a57968fb92e3bc694270918f29\"}";
        String risk2 = "{\"name\":\"RZXX\",\"startid\":\"pr1b0263756a68c7cd303c575c7fe796\",\"endid\":\"84c17a005a759a5e0d875c1ebb6c9846\",\"semd5\":\"aac7c05f59ed838818a71b88238973df\",\"ids\":\"\",\"prop2\":\"1-----董事长,董事,总经理1,0-----董事长,总经理\",\"prop3\":\"-1\",\"info\":\"{\\\"sortdate\\\":\\\"-1\\\",\\\"wdname\\\":\\\"RZXX\\\",\\\"startid\\\":\\\"pr1b0263756a68c7cd303c575c7fe796\\\",\\\"startname\\\":\\\"刘延峰\\\",\\\"endid\\\":\\\"84c17a005a759a5e0d875c1ebb6c9846\\\",\\\"endname\\\":\\\"乐视网信息技术（北京）股份有限公司\\\",\\\"job\\\":\\\"0-----董事长,总经理,1-----董事长,董事,总经理###\\\"}\"}";
 //       String risk21 = "{\"name\":\"TZXX\",\"startid\":\"p3483c261f318de061fb3ded0508bf90\",\"endid\":\"84c17a005a759a5e0d875c1ebb6c9846\",\"semd5\":\"83c994d0302369001bd809be0333e4c7\",\"ids\":\"\",\"prop2\":\"22.52%\",\"prop3\":\"1617120000\",\"info\":\"{\\\"sortdate\\\":\\\"1617120000\\\",\\\"wdname\\\":\\\"TZXX\\\",\\\"startid\\\":\\\"p3483c261f318de061fb3ded0508bf90\\\",\\\"startname\\\":\\\"贾跃亭\\\",\\\"endid\\\":\\\"84c17a005a759a5e0d875c1ebb6c9846\\\",\\\"endname\\\":\\\"乐视网信息技术（北京）股份有限公司\\\",\\\"stockpercent\\\":\\\"22.52%\\\"}\"}";
//        String risk3 = "{\"name\":\"WGCL\",\"startid\":\"rr6b2b8decf66d1b17789cdf6fb3a795\",\"endid\":\"0000070574e7405c2c97e323d991e612\",\"ids\":\"{\\\"BSecuredOrg\\\":0,\\\"IsTransactions\\\":\\\"否\\\",\\\"No\\\":\\\"ac5c4b1de97bb80233656e6c8ce0bb30\\\",\\\"SecuredKeyNo\\\":\\\"00026c64f6481ef40eb363ab9204bbf0\\\",\\\"ReportingType\\\":\\\"中报\\\",\\\"BSecuredParty\\\":\\\"海口市制药厂有限公司\\\",\\\"SecuredParty\\\":\\\"海南海药股份有限公司\\\",\\\"GuaranteePeriod\\\":\\\"--\\\",\\\"BSecuredKeyNo\\\":\\\"a62f4e611469e9c796fb41b014eb3c72\\\",\\\"SecuredType\\\":\\\"连带责任担保,保证担保\\\",\\\"GuaranteeContent\\\":\\\"--\\\",\\\"SecuredOrg\\\":0,\\\"SecuredAmount\\\":\\\"483.98\\\",\\\"GuaranteeStartDate\\\":\\\"2020/3/31\\\",\\\"GuaranteeEndDate\\\":\\\"\\\",\\\"Currency\\\":\\\"人民币\\\",\\\"PublicDate\\\":\\\"2020/8/27\\\",\\\"ReportingDate\\\":\\\"2020/6/30\\\",\\\"IsFinished\\\":\\\"否\\\",\\\"TransactionDate\\\":\\\"2020/3/31\\\",\\\"IsValid\\\":true}#{\\\"BSecuredOrg\\\":0,\\\"IsTransactions\\\":\\\"否\\\",\\\"No\\\":\\\"66a80dff8e54e08788af21e5f2c5cc8c\\\",\\\"SecuredKeyNo\\\":\\\"00026c64f6481ef40eb363ab9204bbf0\\\",\\\"ReportingType\\\":\\\"中报\\\",\\\"BSecuredParty\\\":\\\"海口市制药厂有限公司\\\",\\\"SecuredParty\\\":\\\"海南海药股份有限公司\\\",\\\"GuaranteePeriod\\\":\\\"--\\\",\\\"BSecuredKeyNo\\\":\\\"a62f4e611469e9c796fb41b014eb3c72\\\",\\\"SecuredType\\\":\\\"连带责任担保,保证担保\\\",\\\"GuaranteeContent\\\":\\\"--\\\",\\\"SecuredOrg\\\":0,\\\"SecuredAmount\\\":\\\"1573.31\\\",\\\"GuaranteeStartDate\\\":\\\"2019/6/6\\\",\\\"GuaranteeEndDate\\\":\\\"\\\",\\\"Currency\\\":\\\"人民币\\\",\\\"PublicDate\\\":\\\"2020/8/27\\\",\\\"ReportingDate\\\":\\\"2020/6/30\\\",\\\"IsFinished\\\":\\\"否\\\",\\\"TransactionDate\\\":\\\"2019/6/6\\\",\\\"IsValid\\\":true}#{\\\"BSecuredOrg\\\":0,\\\"IsTransactions\\\":\\\"否\\\",\\\"No\\\":\\\"a1951443bb3e0e8762dd4295e8545a52\\\",\\\"SecuredKeyNo\\\":\\\"00026c64f6481ef40eb363ab9204bbf0\\\",\\\"ReportingType\\\":\\\"中报\\\",\\\"BSecuredParty\\\":\\\"海口市制药厂有限公司\\\",\\\"SecuredParty\\\":\\\"海南海药股份有限公司\\\",\\\"GuaranteePeriod\\\":\\\"--\\\",\\\"BSecuredKeyNo\\\":\\\"a62f4e611469e9c796fb41b014eb3c72\\\",\\\"SecuredType\\\":\\\"连带责任担保,保证担保\\\",\\\"GuaranteeContent\\\":\\\"--\\\",\\\"SecuredOrg\\\":0,\\\"SecuredAmount\\\":\\\"2908.63\\\",\\\"GuaranteeStartDate\\\":\\\"2020/5/19\\\",\\\"GuaranteeEndDate\\\":\\\"\\\",\\\"Currency\\\":\\\"人民币\\\",\\\"PublicDate\\\":\\\"2020/8/27\\\",\\\"ReportingDate\\\":\\\"2020/6/30\\\",\\\"IsFinished\\\":\\\"否\\\",\\\"TransactionDate\\\":\\\"2020/5/19\\\",\\\"IsValid\\\":true}#{\\\"BSecuredOrg\\\":0,\\\"IsTransactions\\\":\\\"否\\\",\\\"No\\\":\\\"c55ac224acbecae573fece0374cd70d6\\\",\\\"SecuredKeyNo\\\":\\\"00026c64f6481ef40eb363ab9204bbf0\\\",\\\"ReportingType\\\":\\\"中报\\\",\\\"BSecuredParty\\\":\\\"海口市制药厂有限公司\\\",\\\"SecuredParty\\\":\\\"海南海药股份有限公司\\\",\\\"GuaranteePeriod\\\":\\\"--\\\",\\\"BSecuredKeyNo\\\":\\\"a62f4e611469e9c796fb41b014eb3c72\\\",\\\"SecuredType\\\":\\\"连带责任担保,保证担保\\\",\\\"GuaranteeContent\\\":\\\"--\\\",\\\"SecuredOrg\\\":0,\\\"SecuredAmount\\\":\\\"2977.66\\\",\\\"GuaranteeStartDate\\\":\\\"2020/4/8\\\",\\\"GuaranteeEndDate\\\":\\\"\\\",\\\"Currency\\\":\\\"人民币\\\",\\\"PublicDate\\\":\\\"2020/8/27\\\",\\\"ReportingDate\\\":\\\"2020/6/30\\\",\\\"IsFinished\\\":\\\"否\\\",\\\"TransactionDate\\\":\\\"2020/4/8\\\",\\\"IsValid\\\":true}\",\"semd5\":\"420d87a57968fb92e3bc694270918f29\"}";
        ArrayList<String> list = new ArrayList<>();
        //list.add(risk1);
        //list.add(risk11);
        list.add(risk2);
//        list.add(risk21);
//        list.add(risk3);

        GetRiskRelationUDF getRiskRelationUDF = new GetRiskRelationUDF();
        String startid = "pr1b0263756a68c7cd303c575c7fe796";
        String endid = "84c17a005a759a5e0d875c1ebb6c9846";

        String evaluate = getRiskRelationUDF.evaluate(startid, 1, endid, 1, list);
        System.out.println(evaluate);
        System.out.println(MD5Util.encode(endid + startid));
    }

}
