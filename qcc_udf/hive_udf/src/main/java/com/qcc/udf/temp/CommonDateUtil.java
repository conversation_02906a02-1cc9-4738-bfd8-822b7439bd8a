package com.qcc.udf.temp;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date ：Created in 2020/2/26 17:04
 * @description：
 */
public class CommonDateUtil {

	public static void main(String[] args) {
		System.out.println(convertStr2Date(""));
	}

	/**
	 * 是否合法正则
	 * @param regex
	 * @param date
	 * @return
	 */
	public static boolean isRegularRegx(String regex,String date) {
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(date);
		return matcher.matches();
	}


	public static Date convertStr2Date(String dateStr) {
		if (isRegularRegx("\\d{4}/\\d{1,2}/\\d{1,2}", dateStr)) {
			return parseStrToDate(dateStr, "yyyy/MM/dd");
		}
		if (isRegularRegx("\\d{1,2}/\\d{1,2}/\\d{4}", dateStr)) {
			return parseStrToDate(dateStr, "MM/dd/yyyy");
		}
		if (isRegularRegx("\\d{4}-\\d{1,2}-\\d{1,2}", dateStr)) {
			return parseStrToDate(dateStr, "yyyy-MM-dd");
		}
		if (isRegularRegx("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}\\.\\d{5}", dateStr)) {
			return parseStrToDate(dateStr, "yyyy-MM-dd");
		}
		return null;
	}

	/**
	 * 格式化String时间
	 *
	 * @param time       String类型时间
	 * @param timeFromat String类型格式
	 * @return 格式化后的Date日期
	 */
	public static Date parseStrToDate(String time, String timeFromat) {
		if (time == null || time.equals("")) {
			return null;
		}
		Date date = null;
		try {
			DateFormat dateFormat = new SimpleDateFormat(timeFromat);
			date = dateFormat.parse(time);
		} catch (Exception e) {

		}
		return date;
	}
}
