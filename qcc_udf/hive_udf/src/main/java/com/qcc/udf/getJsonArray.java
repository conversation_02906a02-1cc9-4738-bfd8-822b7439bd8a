package com.qcc.udf;

import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.io.Text;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONTokener;

import java.util.ArrayList;

public class getJsonArray extends UDF {
    /**
     * 解析json并返回对应子json字符串数组,例如
     * add jar qcc_udf.jar;
     * create temporary function getJsonArray as 'com.qcc.udf.GetJsonArray';
     * select getJsonArray(json字符串)
     *
     * @param jsonArrayStr
     * @return
     * @throws HiveException
     */
    public static ArrayList<Text> evaluate(String jsonArrayStr) throws JSONException {
        if (StringUtils.isBlank(jsonArrayStr) || StringUtils.isEmpty(jsonArrayStr)) {
            return null;
        }
        try {
            ArrayList<Text> textList = new ArrayList<Text>();
            if (!jsonArrayStr.trim().startsWith("[")) {
                textList.add(new Text(jsonArrayStr));
            } else {
                JSONArray jsonArray = new JSONArray(new JSONTokener(jsonArrayStr));

                for (int i = 0; i < jsonArray.length(); i++) {
                    String json = jsonArray.getJSONObject(i).toString();
                    textList.add(new Text(json));
                }
            }
            return textList;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }

}