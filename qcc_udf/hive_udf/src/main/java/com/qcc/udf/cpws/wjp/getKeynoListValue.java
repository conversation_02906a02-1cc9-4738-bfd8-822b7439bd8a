package com.qcc.udf.cpws.wjp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;

public class getKeynoListValue extends UDF {

    public static String evaluate(String jsonStr) {
        try {

            if (StringUtils.isEmpty(jsonStr)) {
                return StringUtils.EMPTY;
            }
            List<String> keyNos = new ArrayList<>();
            JSONArray jsonArray = JSON.parseArray(jsonStr);

            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject obj = jsonArray.getJSONObject(i);
                if (StringUtils.isNotEmpty(obj.getString("KeyNo"))) {
                    keyNos.add(obj.getString("KeyNo"));
                }
                if(obj.containsKey("SupNameAndKeyNo")){
                    JSONObject supObj = obj.getJSONObject("SupNameAndKeyNo");
                    if (StringUtils.isNotEmpty(supObj.getString("KeyNo"))) {
                        keyNos.add(supObj.getString("KeyNo"));
                    }
                }
            }

            return String.join(",", keyNos);
        } catch (Exception e) {
            e.printStackTrace();
            return "error";
        }
    }

    public static void main(String[] args) {
        System.out.println(evaluate("[{\"KeyNo\":\"\",\"Org\":-1,\"SupNameAndKeyNo\":{\"KeyNo\":\"\",\"Org\":-1,\"ShowName\":\"张**\",\"Name\":\"张玉云\"},\"ShowName\":\"张**\",\"Name\":\"张某云\"},{\"KeyNo\":\"\",\"Org\":-1,\"SupNameAndKeyNo\":{\"KeyNo\":\"739578987dbc144c328bcd5de17baa1c\",\"Org\":0,\"ShowName\":\"快马鲜生（厦门）科技有限公司安溪分公司\",\"Name\":\"快马鲜生（厦门）科技有限公司安溪分公司\"},\"ShowName\":\"某某科技有限公司安溪分公司\",\"Name\":\"某某科技有限公司安溪分公司\"}]"));
    }
}
