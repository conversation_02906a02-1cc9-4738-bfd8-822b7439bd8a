package com.qcc.udf;

import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class GetKeyNoFromCompanyKeywords extends UDF {

    private static Pattern p = Pattern.compile("^[a-z0-9][a-f0-9]{31}$");

    /**
     * 从companyKeywords 提取公司keyno
     *
     * @param companyKeywords 公司关键词 格式为公司名称和公司keyno用英文逗号连接
     * @return keyNo
     */
    public static String evaluate(String companyKeywords) {
        if (StringUtils.isNotEmpty(companyKeywords)) {
            for (String keyNo : companyKeywords.split(",")) {
                Matcher mc = p.matcher(keyNo);
                if (mc.matches()) {
                    return keyNo;
                }
            }
        }
        return "";
    }
}
