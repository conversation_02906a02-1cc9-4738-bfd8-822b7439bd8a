package com.qcc.udf.court_notice.anUtils;



import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Util {
	
	static String strNull;

	//判断是否有汉字

	
	public static boolean isEmptyKey(String val){
		if(val==null){
			return true;
		}
		String key[] = new String[]{"无","暂无","-","暂缺","空","\"空\"","NULL","null","\"null\""};
		for (String string : key) {
			if(val.trim().equals(string)){
				return true;
			}
		}
		return false;
	}

	public static Boolean isKeyword(String str) {
		String regex = "^[a-zA-Z0-9]+$";
		Pattern pattern = Pattern.compile(regex);
		Matcher match = pattern.matcher(str);
		return match.matches();
	}
	
	
	/**
	 * 正则
	 * @param str
	 * @param regex
	 * @return
	 */
    public static int parseRegexInt(final String str, final String regex) {
        final String aInt = parseRegex(str, regex);
        return notEmpty(aInt) ? Integer.parseInt(aInt) : 0;
    }
    
    public static String parseRegex(final String str, final String regex) {
        if (Util.isEmpty(str)) return "";
        return parseRegex(str, regex, 1);
    }

    public static String parseRegex(final String str, final String regex, final int index) {
    	if (Util.isEmpty(str)) return "";
        final Pattern p = Pattern.compile(regex);
        final Matcher m = p.matcher(str);
        if(index<0){
        	return m.find() ? m.group() : null;
        }
        return m.find() ? m.group(index) : null;
    }
    
    public static boolean notEmpty(final String str) {
        return !isEmpty(str);
    }
    public static boolean notEmpty(final Object obj) {
        return !isEmpty(obj);
    }
    
    public static boolean isEmpty(final String str) {
        return str == null || str.trim().length() == 0;
    }
    public static boolean isEmpty(final Object obj) {
        return obj == null || obj.toString().trim().length() == 0;
    }
    
    /**
     * 日期转字符串
     * @param date 要转的日期
     * @param format 格式
     * @return
     */
	public static String dateToString(Date date , String format){
		SimpleDateFormat sdf = new SimpleDateFormat(format);
		try{
			return sdf.format(date);
		}catch(Exception e){
			return null;
		}
	}
	
	/**
	 * 字符串转日期
	 * @param date 日期字符串
	 * @param format 格式
	 * @return
	 * 说明：odps的datetime类型兼容datetime和timestamp类型，datetime范围1000-9999年，timestamp范围1970-01-01 08:00:01 到2038-01-19 11:14:07
	 * 所以odps的datetime范围1970-01-01 08:00:01 到2038-01-19 11:14:07，超出范围或报错
	 */
	public static Date stringToDate(String date , String format){
		SimpleDateFormat sdf = new SimpleDateFormat(format);
		try{
			if(sdf.parse(date).getTime()<0 || sdf.parse(date).getTime()>2147483646000L){//2038-01-19 11:14:06
				return null;
			}else{
				return sdf.parse(date);
			}
		}catch(Exception e){
			return null;
		}
	}
	
	/**
	 * 通过传进来的字符串取转换成Date类型数据
	 * @param context
	 * @return
	 */
	public static Date getDateTest(String context){
		if(Util.isEmpty(context))
			return null;
		context = context.trim();
		//判断上午下午日期
		GregorianCalendar ca = new GregorianCalendar();
		//处理日期加减
		Calendar rightNow = Calendar.getInstance();
		String contextCopy = context;
		Date date;
		//处理汉字日期问题
		if(context.contains("二")){
			Map<String,String> dateMap = new HashMap<String,String>();
			dateMap.put("〇", "0");
			dateMap.put("一", "1");
			dateMap.put("二", "2");
			dateMap.put("三", "3");
			dateMap.put("四", "4");
			dateMap.put("五", "5");
			dateMap.put("六", "6");
			dateMap.put("七", "7");
			dateMap.put("八", "8");
			dateMap.put("九", "9");
			
			context = context.replace("十月", "一〇月");
			context = context.replace("十日", "一〇日");
			context = context.replace("二十日", "二〇日");
			context = context.replace("三十日", "三〇日");
			context = context.replace("年十", "年一");
			context = context.replace("月十", "月一");
			context = context.replace("月二十", "月二");
			context = context.replace("月三十", "月三");
			
			for(String key : dateMap.keySet()){
				context = context.replace(key, dateMap.get(key));
			}
		}
		
		//开始转换
		StringBuffer sb = new StringBuffer();
		StringBuffer contextSb = new StringBuffer();
		String str[] = new String[]{"yyyy","MM","dd","HH","mm","ss"};
		context = context.replaceAll("[^0-9]", "-");
		if(!context.endsWith("-")){
			context = context + "-";
		}
		while(context.contains("--")){
			context = context.replace("--", "-");
		}
		String contextStr[] = context.split("-");
		
		if(contextStr.length>str.length){
			return null;
		}
		
		/**
		 * 处理两点：
		 * 	1、根据处理后的时间字符串动态拼接时间格式
		 * 	2、将处理后的时间字符串为单个数的，补上十位，使其成为两位数，符合格式化
		 */
		for(int i = 0; i <= contextStr.length-1; i++){
			if(contextStr[i].length()==1){
				contextStr[i] = "0" + contextStr[i];
			}
			contextSb.append(contextStr[i]).append("-");
			sb.append(str[i]).append("-");
		}
		SimpleDateFormat sdf = new SimpleDateFormat(sb.toString());
		
		try {
			date =  sdf.parse(contextSb.toString());
		} catch (ParseException e) {
			return  null;
		}
		
		//处理个别时间包含汉字下午的，但是转换的日期是上午的，时间要加上12小时
		ca.setTimeInMillis(date.getTime());//将GregorianCalendar设置为date的时间再进行判断
		if(contextCopy.contains("下午") && ca.get(GregorianCalendar.AM_PM)==0){//结果为“0”是上午 结果为“1”是下午  
			rightNow.setTime(date);
			rightNow.add(Calendar.HOUR, 12);
			date = rightNow.getTime();
		}
		return date;
	}
	
	public static Date getDate(String context)  {
		if(Util.isEmpty(context))
			return null;
		context = context.trim();
		//判断上午下午日期
		GregorianCalendar ca = new GregorianCalendar();
		//处理日期加减
		Calendar rightNow = Calendar.getInstance();
		String contextCopy = context;
		Date date;
       if(Pattern.compile("^[\\d]{13}$").matcher(context).matches()){
            return new Date(Long.parseLong(context));
        }
        if(Pattern.compile("^[\\d]{10}$").matcher(context).matches()){
            return new Date(Long.parseLong(context+"000"));
        }
        if(Pattern.compile("^[^\\d]+[\\d]{4}-\\d\\d-\\d\\d \\d\\d:\\d\\d$").matcher(context).matches()){
           System.out.println(context.substring(context.length()-16,context.length()));
           SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd hh:mm");
           try {
               return sdf.parse(context.substring(context.length() - 16, context.length()));
           }catch (Exception e){
               return null;
           }
        }
		//处理汉字日期问题
		if(context.contains("二")){
			Map<String,String> dateMap = new HashMap<String,String>();
			dateMap.put("〇", "0");
			dateMap.put("一", "1");
			dateMap.put("二", "2");
			dateMap.put("三", "3");
			dateMap.put("四", "4");
			dateMap.put("五", "5");
			dateMap.put("六", "6");
			dateMap.put("七", "7");
			dateMap.put("八", "8");
			dateMap.put("九", "9");
			dateMap.put("十", "s");
			
			for(String key : dateMap.keySet()){
				context = context.replace(key, dateMap.get(key));
			}
		}
		//开始转换
		StringBuffer sb = new StringBuffer();
		StringBuffer contextSb = new StringBuffer();
		String str[] = new String[]{"yyyy","MM","dd","HH","mm","ss"};
		context = context.replaceAll("[^0-9|^s]", "-");
		if(!context.endsWith("-")){
			context = context + "-";
		}
		while(context.contains("--")){
			context = context.replace("--", "-");
		}
		String contextStr[] = context.split("-");
		
		/**
		 * 处理两点：
		 * 	1、根据处理后的时间字符串动态拼接时间格式
		 * 	2、将处理后的时间字符串为单个数的，补上十位，使其成为两位数，符合格式化
		 */
		for(int i = 0; i <= contextStr.length-1; i++){
			//先处理两位数的，防止第二步的补位造成影响
			if(contextStr[i].length()==2){
				if(contextStr[i].indexOf("s")==0){
					contextStr[i] = contextStr[i].replace("s", "1");
				}else if(contextStr[i].indexOf("s")==1){
					contextStr[i] = contextStr[i].replace("s", "0");
				}
			}
			//个位数十位补零
			if(contextStr[i].length()==1){
				if(contextStr[i].equals("s")){
					contextStr[i] = "10";
				}else{
					contextStr[i] = "0" + contextStr[i];
				}
			}
			//根据上面的转换类似四十五会转成三位的，把s抹掉：4s5
			if(contextStr[i].length()==3){
				contextStr[i] = contextStr[i].replace("s", "");
			}
			
			//长度越界，不再继续向下匹配
			if(i>str.length - 1)
				break;
			//出现长度不匹配，不再继续向下匹配
			if(contextStr[i].length()!=str[i].length()){
				break;
			}
			contextSb.append(contextStr[i]).append("-");
			sb.append(str[i]).append("-");
		}
		SimpleDateFormat sdf = new SimpleDateFormat(sb.toString());
		
		try {
			date =  sdf.parse(contextSb.toString());
		} catch (ParseException e) {
			return  null;
		}
		
		//处理个别时间包含汉字下午的，但是转换的日期是上午的，时间要加上12小时
		ca.setTimeInMillis(date.getTime());//将GregorianCalendar设置为date的时间再进行判断
		if(contextCopy.contains("下午") && ca.get(GregorianCalendar.AM_PM)==0){//结果为“0”是上午 结果为“1”是下午  
			rightNow.setTime(date);
			rightNow.add(Calendar.HOUR, 12);
			date = rightNow.getTime();
		}
		return date;
	}
	
	/**
	 * 常用的分隔名称的方法
	 * @param name
	 * @return
	 */
	public static Set<String> split(String name){
		Set<String> nameSet = new HashSet<String>();
		if(isEmpty(name)){
			return nameSet;
		}
		String keyArr[] = new String[]{",","、","，"};
		for (String key : keyArr) {
			if(name.contains(key)){
				for(String str : name.split(key)){
					if(!Util.isEmpty(str)){
						nameSet.add(str.trim());
					}
				}
			}
		}
		//如果为空，则塞入原字符
		if(nameSet.isEmpty()){
			nameSet.add(name.trim());
		}
		return nameSet;
	}
	
	@SuppressWarnings("rawtypes")
	public static Map<String, Object> mapRemoveWithNullByRecursion(Map<String, Object> map){
		Set<Entry<String, Object>> set = map.entrySet();
		Iterator<Entry<String, Object>> it = set.iterator();
		Map map2 = new HashMap();
		while(it.hasNext()){
			Entry<String, Object> en = it.next();
			if(!(en.getValue() instanceof Map)){
				if(null == en.getValue() || "".equals(en.getValue())){
					it.remove();
				}
			}else{
				map2 = (Map) en.getValue();
				mapRemoveWithNullByRecursion(map2);
			}
		}
		return map;
	}
	
	  
    /** 
     * 全角转半角: 
     * @param fullStr 
     * @return 
     */  
    public static final String full2Half(String fullStr) {  
        if(isEmpty(fullStr)){  
            return fullStr;  
        }  
        char[] c = fullStr.toCharArray();  
        for (int i = 0; i < c.length; i++) {  
            if (c[i] >= 65281 && c[i] <= 65374) {  
                c[i] = (char) (c[i] - 65248);  
            } else if (c[i] == 12288) { // 空格  
                c[i] = (char) 32;  
            }  
        }  
        return new String(c);  
    }  
  
    /** 
     * 半角转全角 
     * @param halfStr 
     * @return 
     */  
    public static final String half2Full(String halfStr) {  
        if(isEmpty(halfStr)){  
            return halfStr;  
        }  
        char[] c = halfStr.toCharArray();  
        for (int i = 0; i < c.length; i++) {  
            if (c[i] == 32) {  
                c[i] = (char) 12288;  
            } else if (c[i] < 127) {  
                c[i] = (char) (c[i] + 65248);  
            }  
        }  
        return new String(c);  
    }
    
    /**
     * 判断是否人名的方法
     * @param str
     * @return
     */
    public static String GetValidCompanyName(String str) {
		if (str == null || "".equals(str)) {
			return null;
		} else {
			Pattern p = Pattern.compile("[ \\d°！!@#$%^&*＊.,。，;；？?※]");
			Matcher m = p.matcher(str);
			String temp = m.replaceAll("");
			if (temp.length() < 4) {
				return null;
			}

			Pattern p1 = Pattern.compile("^[a-f0-9]{32}$");
			Matcher m1 = p1.matcher(str);
			if (m1.matches()) {
				return str;
			}

			Pattern p2 = Pattern.compile("^s[a-f0-9]{31}$");
			Matcher m2 = p2.matcher(str);
			if (m2.matches()) {
				return str;
			}

			return getSensitive(str, "(?<key>.*[\u4e00-\u9fa5]+[^-－A-aZ-z])");
		}
	}


	private static String getSensitive(String globWords, String regx) {
		String sensitive = "";
		Pattern pattern = Pattern.compile(regx);
		Matcher matcher = pattern.matcher(globWords);
		if (matcher.find()) {
			sensitive = matcher.group(1);
		}
		return sensitive;
	}

	
	public static void main(String[] args) {
		System.out.println(Util.getDate("审判长：谢唯诚 合议庭成员：朱光烁、王俊梅 书记员：王久荣2015-12-23 09:30"));
	}
	public static String remove(String content) {
		String returnString="";
		returnString = Util.parseRegex(content, "(上诉人|申诉人|原告|原审原告|申请执行人|申请人|债权人|被上诉人|原审被告|被告|原审被告|被申请执行人|被申诉人|被申请人|债务人|被告人)[:：]([^;； ]+)", 2);
		returnString = returnString == null ? content : returnString;
		return returnString;
	}
	public static  String deal_com_singlename(String singleName){
        if(singleName!=null) {
            if (singleName.contains("公司") && singleName.endsWith("分行")) {
                singleName = singleName.substring(0, singleName.indexOf("分行") + 2);
            }
            if (singleName.contains("公司") && singleName.endsWith("支行")) {
                singleName = singleName.substring(0, singleName.indexOf("支行") + 2);
            }
            if (singleName.contains("公司") && !singleName.endsWith("公司") && !singleName.endsWith("分行") && !singleName.endsWith("支行")) {
                singleName = singleName.substring(0, singleName.indexOf("公司") + 2);
            }
            return singleName;
        }else{
            return "";
        }
    }
}
