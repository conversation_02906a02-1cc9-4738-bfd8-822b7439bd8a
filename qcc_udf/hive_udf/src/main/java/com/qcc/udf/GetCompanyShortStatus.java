package com.qcc.udf;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;

public class GetCompanyShortStatus extends UDF {

    /**
     * 获取公司状态简称
     *
     * @param status 输入工商给的企业状态
     * @return 返回格式化后的企业状态
     */
    public static String evaluate(String status) throws Exception {
        if(StringUtils.isEmpty(status) || status.trim().length() == 0){
            return "";
        }

        String result = "在业";

        if ((isContainsWords(status, "被吊销", "吊销，未注销", "吊销，已注销", "吊销后注销", "吊销未注销", "已吊销", "吊销企业", "暂时吊销")
                || status.equals("吊销")
                || (isContainsWords(status, "吊销") && isContainsWords(status, "未注销"))) && !status.contains("已注销"))
            result = "吊销";
        else if (isContainsWords(status, "迁出注销", "迁移异地", "迁往市外", "迁出"))
            result = "迁出";
        else if (isContainsWords(status, "已注销", "+注吊销", "注销", "注销登记中", "吊销已注销", "注销企业", "企业已注销", "注吊销"))
            result = "注销";
        else if (isContainsWords(status, "筹建"))
            result = "筹建";
        else if (isContainsWords(status, "存 -(在营、开业、在册)", "存续"))
            result = "存续";
        else if (isContainsWords(status, "迁入"))
            result = "迁入";
        else if (isContainsWords(status, "清算"))
            result = "清算";
        else if (isContainsWords(status, "停业"))
            result = "停业";
        else if (isContainsWords(status, "已撤销登记", "已撤销企业", "撤消登记", "撤销"))
            result = "撤销";

        return result;
    }

    public static boolean isContainsWords(String source, String... args) {
        if (StringUtils.isEmpty(source) || args.length == 0) return false;

        return Arrays.stream(args).anyMatch(source::contains);
    }
}
