package com.qcc.udf;

import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;
import org.json.JSONException;
import org.json.JSONObject;
import org.json.JSONTokener;


public class getJsonObject extends UDF {
    /**
     * 解析json并返回对应的值。例如
     * add jar qcc_udf.jar;
     * create temporary function getJsonObject as 'com.qcc.udf.GetJsonArray';
     * select getJsonObject(json字符串,key值)
     *
     * @param jsonStr
     * @param objName
     * @return
     */
    public static String evaluate(String jsonStr, String objName) throws JSONException {
        if (StringUtils.isBlank(jsonStr) || StringUtils.isEmpty(objName)) {
            return null;
        }
        try {
            JSONObject jsonObject = new JSONObject(new JSONTokener(jsonStr));
            Object objValue = jsonObject.get(objName);
            if (objValue == null) {
                return null;
            }
            return objValue.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }

}