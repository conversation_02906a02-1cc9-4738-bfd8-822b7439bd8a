package com.qcc.udf.company.controller;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Auther: nixb
 * @Date: 2020/9/24 17:30
 * @Description:
 */
public class CompanyPartner {
    public String pkeyno;

    public String pcompanyname;

    public BigDecimal stockpercent;

    public BigDecimal controlratio;

    public Integer org;

    public boolean hasimage;

    public String shouldcapi;

    public String keynopath;

    public int isoper;

    public int isgp;

    public String gppath;

    public int haspartner;

    public String control;

    public CompanyActualController actualController;

    public String groupKey;

    public List<ActaulDataExtend> dataextend;

    public String job;

    private Integer jobLevel;

    public String getPkeyno() {
        return pkeyno;
    }

    public void setPkeyno(String pkeyno) {
        this.pkeyno = pkeyno;
    }

    public String getPcompanyname() {
        return pcompanyname;
    }

    public void setPcompanyname(String pcompanyname) {
        this.pcompanyname = pcompanyname;
    }

    public BigDecimal getStockpercent() {
        return stockpercent;
    }

    public void setStockpercent(BigDecimal stockpercent) {
        this.stockpercent = stockpercent;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public boolean isHasimage() {
        return hasimage;
    }

    public void setHasimage(boolean hasimage) {
        this.hasimage = hasimage;
    }

    public String getShouldcapi() {
        return shouldcapi;
    }

    public void setShouldcapi(String shouldcapi) {
        this.shouldcapi = shouldcapi;
    }

    public String getKeynopath() {
        return keynopath;
    }

    public void setKeynopath(String keynopath) {
        this.keynopath = keynopath;
    }

    public int getIsoper() {
        return isoper;
    }

    public void setIsoper(int isoper) {
        this.isoper = isoper;
    }

    public int getIsgp() {
        return isgp;
    }

    public void setIsgp(int isgp) {
        this.isgp = isgp;
    }

    public String getGppath() {
        return gppath;
    }

    public void setGppath(String gppath) {
        this.gppath = gppath;
    }

    public int getHaspartner() {
        return haspartner;
    }

    public void setHaspartner(int haspartner) {
        this.haspartner = haspartner;
    }

    public String getControl() {
        return control;
    }

    public void setControl(String control) {
        this.control = control;
    }

    public CompanyActualController getActualController() {
        return actualController;
    }

    public void setActualController(CompanyActualController actualController) {
        this.actualController = actualController;
    }

    public String getGroupKey() {
        return groupKey;
    }

    public void setGroupKey(String groupKey) {
        this.groupKey = groupKey;
    }

    public List<ActaulDataExtend> getDataextend() {
        return dataextend;
    }

    public void setDataextend(List<ActaulDataExtend> dataextend) {
        this.dataextend = dataextend;
    }

    public String getJob() {
        return job;
    }

    public void setJob(String job) {
        this.job = job;
    }

    public Integer getJobLevel() {
        return jobLevel;
    }

    public void setJobLevel(Integer jobLevel) {
        this.jobLevel = jobLevel;
    }

    public BigDecimal getControlratio() {
        return controlratio;
    }

    public void setControlratio(BigDecimal controlratio) {
        this.controlratio = controlratio;
    }
}
