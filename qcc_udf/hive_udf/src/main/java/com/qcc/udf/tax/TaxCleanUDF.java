package com.qcc.udf.tax;

import com.qcc.udf.enums.PunishGovEnum;
import com.qcc.udf.tax.CommonUtils;
import com.qcc.udf.tax.MyTaxArrUtil;
import com.qcc.udf.temp.MD5Util;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Auther: zhanqgiang
 * @Date: 2020/11/19 10:00
 * @Description: 案号简单清洗
 */
public class TaxCleanUDF extends UDF {

    public static String evaluate(String website, String taxtitle, String taxpayername, String publictime, String dateissue, String taxType, String taxbalance, String taxbalancethousand) {

        String taxName = MyTaxArrUtil.commonFilter(taxpayername);
        if (taxName.contains("|")) {
            taxName = taxName.split("\\|")[1];
        }

        //过滤条件2
        if (StringUtils.isEmpty(taxName) || !checkTaxName(taxName) || taxName.length() < 2) {
            return "";
        }

        String taxAuthority = MyTaxArrUtil.commonFilter(website);
        String noticeTitle = MyTaxArrUtil.commonFilter(taxtitle);

        //设置发布日期
        String publishDate = CommonUtils.cleanDate(publictime);
        if (StringUtils.isEmpty(publishDate)) {
            publishDate = CommonUtils.cleanDate(dateissue);
        }

        if (StringUtils.isBlank(taxType)) {
            return "";
        }
        BigDecimal taxBalance = cleanBigDecimal(taxbalance, taxbalancethousand);
        return MD5Util.encode(taxAuthority + noticeTitle + taxName + publishDate + taxType + taxBalance);
    }

    private static boolean checkTaxName(String taxName) {
        taxName = MyTaxArrUtil.full2Half(taxName);
        taxName = taxName.replaceAll("[\\*|/|@|#|￥|%|&|？|\\.|。|，|；|;|！|~|\\+|‘|’|\"|'|\\||(|)]", "");
        return MyTaxArrUtil.checkChineseChar(taxName);
    }

    private static BigDecimal cleanBigDecimal(String main, String second) {
        BigDecimal taxBalance = null;
        String taxBalanceStr = main;
        if (StringUtils.isBlank(taxBalanceStr)) {
            taxBalanceStr = second;
            if (StringUtils.isNotBlank(taxBalanceStr)) {
                taxBalance = MyTaxArrUtil.revertBigDecimal(taxBalanceStr).multiply(new BigDecimal(10000));
            }
        }
        if (null == taxBalance) {
            taxBalance = MyTaxArrUtil.revertBigDecimal(taxBalanceStr);
        }
        return taxBalance;
    }


    public static void main(String[] args) {
        System.out.println(evaluate("国家税务总局唐山市古冶区税务局", "古冶区税务局2019年第一季度单位或企业欠税公告", "唐山市秋明酒业有限公司", "", "", "城镇土地使用税", "1050.00", ""));
    }

}
