package com.qcc.udf.casesearch_v3.test;

import com.alibaba.fastjson.JSON;
import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.output.CaseListEntity;
import com.qcc.udf.casesearch_v3.entity.output.LawSuitV3OutputEntity;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CpwsShideFlagUDF extends UDF {

    public static String evaluate(String json)  {
        Map<String,Integer> cpwsMap = new HashMap<>();
        LawSuitV3OutputEntity jsonEntity = JSON.parseObject(json,LawSuitV3OutputEntity.class);

        if(jsonEntity != null && jsonEntity.getInfoList() != null){
            for (InfoListEntity item : jsonEntity.getInfoList()) {
                if(item.getCaseList() != null){
                    for (CaseListEntity cpws : item.getCaseList()) {
                        cpwsMap.put(cpws.getId(),cpws.getShieldCaseFlag());
                    }
                }
            }
        }

        return JSON.toJSONString(cpwsMap);
    }

    public static void main(String[] args) {

        String json = "[{\"AmtInfo\":{},\"AnNoList\":\"（2020）川0723执987号,（2020）川0723民初1182号\",\"AnnoCnt\":2,\"CaseCnt\":2,\"CaseName\":\"中国邮政储蓄银行股份有限公司盐亭县支行与何元辉信用卡纠纷的案件\",\"CaseReason\":\"信用卡纠纷\",\"CaseRole\":\"[{\\\"D\\\":\\\"一审原告,首次执行申请执行人\\\",\\\"N\\\":\\\"5ac346a7cc1159c9824ad28b83a11e5a\\\",\\\"O\\\":0,\\\"P\\\":\\\"中国邮政储蓄银行股份有限公司盐亭县支行\\\",\\\"R\\\":\\\"原告\\\"},{\\\"D\\\":\\\"一审被告,首次执行被执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2,\\\"P\\\":\\\"何元辉\\\",\\\"R\\\":\\\"被告\\\"}]\",\"CaseType\":\"执行案件,民事案件\",\"CfdfCnt\":0,\"CfgsCnt\":0,\"CfxyCnt\":0,\"CompanyKeywords\":\"5ac346a7cc1159c9824ad28b83a11e5a,中国邮政储蓄银行有限责任公司四川省绵阳市盐亭县支行,中国邮政储蓄银行有限责任公司盐亭县支行,中国邮政储蓄银行股份有限公司盐亭县支行,何元辉\",\"CourtList\":\"四川省绵阳市盐亭县人民法院\",\"EarliestDate\":1586966400,\"EarliestDateType\":\"民事一审|立案日期\",\"FyggCnt\":0,\"GqdjCnt\":0,\"GroupId\":\"7c8645e069114d3d2964adeb07f24933\",\"HbcfCnt\":0,\"Id\":\"d2505f7064c42910ce2daa52b8b3855d\",\"InfoList\":[{\"AnNo\":\"（2020）川0723执987号\",\"CaseList\":[{\"Amt\":\"\",\"CaseType\":\"执行裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"4098a29a622915b866bb11843d9ee6110\",\"IsValid\":1,\"JudgeDate\":1608134400,\"Result\":\"\",\"ResultType\":\"裁定结果\",\"ShieldCaseFlag\":0}],\"CaseReason\":\"借款合同纠纷案件执行\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"四川省绵阳市盐亭县人民法院\",\"Defendant\":[{\"KeyNo\":\"\",\"Name\":\"何元辉\",\"Org\":-2,\"Role\":\"被执行人\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1608134400,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"5ac346a7cc1159c9824ad28b83a11e5a\",\"Name\":\"中国邮政储蓄银行股份有限公司盐亭县支行\",\"Org\":0,\"Role\":\"申请执行人\"}],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"首次执行\",\"XgList\":[{\"CompanyInfo\":[{\"KeyNo\":\"\",\"Name\":\"何元辉\",\"Org\":-2}],\"GlNameAndKeyNo\":[],\"Id\":\"1b75fb1a83d647108f2b348c63b58bb9\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"何元辉\",\"Org\":-2}],\"PublishDate\":1603728000,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"5ac346a7cc1159c9824ad28b83a11e5a\",\"Name\":\"中国邮政储蓄银行股份有限公司盐亭县支行\",\"Org\":0}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"何元辉\",\"Org\":-2}]}],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[{\"ExecuteObject\":\"58761\",\"FailureAct\":\"23558\",\"Id\":\"9284ea42130617aaa434ac053b5f6db1\",\"IsValid\":1,\"JudgeDate\":1608134400,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"何元辉\",\"Org\":-2}]}],\"ZxList\":[{\"Biaodi\":\"58762\",\"Id\":\"9284ea42130617aaa434ac053b5f6db11\",\"IsValid\":0,\"LianDate\":1603123200,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"何元辉\",\"Org\":-2}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"5ac346a7cc1159c9824ad28b83a11e5a\",\"Name\":\"中国邮政储蓄银行股份有限公司盐亭县支行\",\"Org\":0}]}]},{\"AnNo\":\"（2020）川0723民初1182号\",\"CaseList\":[{\"Amt\":\"\",\"CaseType\":\"民事调解书\",\"DocType\":\"调解日期\",\"Id\":\"6a781e70e11cf1b2b5a237782490ab890\",\"IsValid\":1,\"JudgeDate\":1589126400,\"Result\":\"\",\"ResultType\":\"调解结果\",\"ShieldCaseFlag\":1}],\"CaseReason\":\"信用卡纠纷\",\"CaseType\":\"民事案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"四川省绵阳市盐亭县人民法院\",\"Defendant\":[{\"KeyNo\":\"\",\"Name\":\"何元辉\",\"Org\":-2,\"Role\":\"被告\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[{\"ExecuteUnite\":\"第二审判庭\",\"Id\":\"41b9eb3f29c9aa6b107221d54c1bda1a5\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"5ac346a7cc1159c9824ad28b83a11e5a\",\"Name\":\"中国邮政储蓄银行股份有限公司盐亭县支行\",\"Org\":0},{\"KeyNo\":\"\",\"Name\":\"何元辉\",\"Org\":-2}],\"OpenDate\":1589160660}],\"LatestTimestamp\":1589160660,\"LianList\":[{\"Id\":\"eb20832e996e112040f0cc8e32d9ad54\",\"IsValid\":1,\"LianDate\":1586966400,\"NameAndKeyNo\":[{\"KeyNo\":\"5ac346a7cc1159c9824ad28b83a11e5a\",\"Name\":\"中国邮政储蓄银行股份有限公司盐亭县支行\",\"Org\":0},{\"KeyNo\":\"\",\"Name\":\"何元辉\",\"Org\":-2}]}],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"5ac346a7cc1159c9824ad28b83a11e5a\",\"Name\":\"中国邮政储蓄银行股份有限公司盐亭县支行\",\"Org\":0,\"Role\":\"原告\"}],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"民事一审\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]}],\"KtggCnt\":1,\"LastestDate\":1608134400,\"LastestDateType\":\"首次执行|案件终本日期\",\"LatestTrialRound\":\"首次执行\",\"LianCnt\":1,\"PcczCnt\":0,\"ProcuratorateList\":\"\",\"Province\":\"SC\",\"SdggCnt\":0,\"Source\":\"OT\",\"SxCnt\":0,\"Tags\":\"2,3,4,6,11,12\",\"Type\":1,\"XgCnt\":1,\"XjpgCnt\":0,\"XzcfCnt\":0,\"ZbCnt\":1,\"ZxCnt\":1}]";

        List<LawSuitV3OutputEntity> jsonEntity = JSON.parseArray(json,LawSuitV3OutputEntity.class);
        for (LawSuitV3OutputEntity item : jsonEntity) {
            System.out.println(evaluate(JSON.toJSONString(item)));
        }

    }

}
