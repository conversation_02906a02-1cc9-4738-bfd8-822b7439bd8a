package com.qcc.udf.cpws;

import com.qcc.udf.tax.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class CpwsDataStatusCleanUDF1 extends UDF {

    public static String evaluate(String sortStr) {
        try {


            List<RiskJudgementCase> newList = new ArrayList<>();

//25f8cd138fc7a4b30d58b3628b59ff940##2023-02-15 07:13:55.0##1|270ab72ccdfb870dc30f28486840fd7c0##2023-02-15 06:59:19.0##1
            if (StringUtils.isNotEmpty(sortStr)) {
                List<RiskJudgementCase> oldData = new ArrayList<>();
                for (String s : sortStr.split("\\|")) {
                    RiskJudgementCase aCase = new RiskJudgementCase();
                    String[] sp = s.split("##");
                    String id = sp[0];
                    String time = sp[1];
                    aCase.setId(id);
                    aCase.setCreateDate(DateUtil.parseStrToDate(time, DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
                    aCase.setDataStatus(Integer.parseInt(sp[2]));
                    oldData.add(aCase);
                }
                // 所有重复数据按照创建时间排序
                // 分类 2不参与排序
                List<RiskJudgementCase> data2 = oldData.stream().filter(x -> x.getDataStatus() == 2).collect(Collectors.toList());
                // 所有重复数据按照创建时间排序
                List<RiskJudgementCase> dataNot2 = oldData.stream().filter(x -> x.getDataStatus() != 2).sorted(Comparator.comparing((Function<RiskJudgementCase, Date>) RiskJudgementCase::getCreateDate).thenComparing(RiskJudgementCase::getId).reversed()).collect(Collectors.toList());


                for (int i = 0; i < dataNot2.size(); i++) {
                    RiskJudgementCase executed = dataNot2.get(i);
                    //保留最新时间数据状态， 其他给3
                    if (i > 0) {
                        executed.setDataStatus(3);
                    }

                    newList.add(executed);
                }
                newList.addAll(data2);
            }
            Set<String> resp = new HashSet<>();
            for (RiskJudgementCase aCase : newList) {
                resp.add(aCase.getId() + "=" + aCase.getDataStatus());
            }
            return String.join(",", resp);
        } catch (Exception e) {
            return "ERROR:" + sortStr;
        }
    }

    public static void main(String[] args) {

        System.out.println(evaluate("25f8cd138fc7a4b30d58b3628b59ff940##2023-02-15 07:13:55.0##1|270ab72ccdfb870dc30f28486840fd7c0##2023-02-15 06:59:19.0##1"));
    }
}
