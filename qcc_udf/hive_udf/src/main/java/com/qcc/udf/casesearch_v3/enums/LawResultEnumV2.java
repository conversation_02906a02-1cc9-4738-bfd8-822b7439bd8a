package com.qcc.udf.casesearch_v3.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 判决结果枚举新版 对应字段 JR
 * <AUTHOR>
 * @date 2022年11月24日 15:52
 */
@Getter
public enum LawResultEnumV2 {

    /**
     * 1 支持
     * 2 撤诉
     * 3 诉讼中止
     * 4 同意发回重审
     * 5 达成调解
     * 6 驳回
     * 7 同意人身保护令
     * 8 同意管辖权异议
     * 9 适用普通程序
     * 10 适用简易程序
     * 11 撤回申请
     * 12 不被支持
     * 13 部分支持
     * 14 撤回上诉
     * 15 驳回上诉
     * 16 撤销原判
     * 17 同意追加被执行人
     * 18 财产保全
     * 19 解除财产保全
     * 20 对方撤诉
     * 21 对方被支持
     * 22 对方被驳回
     * 23 对方撤回申请
     * 24 不承担责任
     * 25 执行完毕
     * 26 执行中止
     * 27 执行法院变更
     * 28 终结本次执行
     * 29 申请人被驳回
     * 30 原告撤诉
     */


    ZHI_CHI("1", ""),
    CHE_SU("2",""),
    SU_SONG_ZHONG_ZHI("3", ""),
    TONG_YI_FA_HUI_CHONG_SHEN("4", ""),
    DA_CHENG_TIAO_JIE("5", ""),
    BO_HUI("6", ""),
    TONG_YI_REN_SHENG_BAO_HU_LING("7", ""),
    TONG_YI_GUAN_XIA_QUAN_YI_YI("8", ""),
    SHI_YONG_PU_TONG_CHENG_XU("9", ""),
    SHI_YONG_JIAN_YI_CHENG_XU("10", ""),
    CHE_HUI_SHEN_QING("11", ""),
    BU_BEI_ZHI_CHI("12", ""),
    BU_FEN_ZHI_CHI("13", ""),
    CHE_HUI_SHANG_SU("14", ""),
    BO_HUI_SHANG_SU("15", ""),
    CHE_XIAO_YUAN_PAN("16", ""),
    TONG_YI_ZHUI_JIA_BEI_ZHI_XING_REN("17", ""),
    CAI_CHAN_BAO_QUAN("18", ""),
    JIE_CHU_CAI_CHAN_BAO_QUAN("19", ""),
    DUI_FANG_CHE_SU("20", ""),
    DUI_FANG_BEI_ZHI_CHI("21", ""),
    DUI_FANG_BEI_BO_HUI("22", ""),
    DUI_FANG_CHE_HUI_SHEN_QING("23", ""),
    BU_CHEN_DAN_ZE_REN("24", ""),
    ZHI_XING_WAN_BI("25", ""),
    ZHI_XING_ZHONG_ZHI("26", ""),
    ZHI_XING_FA_YUAN_BIAN_GENG("27", ""),
    ZHONG_JIE_BEN_CI_ZHI_XING("28", ""),
    SHEN_QING_REN_BEI_BO_HUI("29", ""),
    YUAN_GAO_CHE_SU("30", "");

    private String code;
    private String desc;


    LawResultEnumV2(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getSourceName(int source){
        for (LawResultEnumV2 value : LawResultEnumV2.values()) {
            if(Objects.equals(source,value.getCode())){
                return value.getDesc();
            }
        }
        return "";
    }
}
