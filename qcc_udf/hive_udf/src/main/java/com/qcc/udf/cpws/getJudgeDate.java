package com.qcc.udf.cpws;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;

import java.text.SimpleDateFormat;
import java.util.LinkedHashSet;
import java.util.Set;

public class getJudgeDate extends UDF {
    public String evaluate(String content) {
        String judgeDate = "";
        try {
            Document document = Jsoup.parse(content);

            judgeDate = getSegmentByClassName(document, "qcc_law_judge_date");
        }catch (Exception e) {

        }

        return getJudgeDate(judgeDate);
    }

    public static String getSegmentByClassName(Document document, String className) {
        if (document != null && StringUtils.isNotBlank(className)) {
            Elements elements = document.getElementsByClass(className);
            if (elements != null && elements.size() > 0) {
                String segment = elements.get(0).toString();
                if (StringUtils.isNotBlank(segment)) {
                    return HtmlToTextUtil.convert(segment).replace("\n", "").trim();
                }
            }
        }
        return "";
    }

    public static String getJudgeDate(String judgeDate){
        String date = "";
        try {
            if (StringUtils.isEmpty(judgeDate)){
                return date;
            }
            String val = CommonUtil.full2Half(judgeDate);
            val = val.replace("三十一","31").replace("三十","30").replace("二十一","21").replace("二十二","22").replace("二十三","23")
                    .replace("二十四","24").replace("二十五","25").replace("二十六","26").replace("二十七","27").replace("二十八","28")
                    .replace("二十九","29");

            val = val.replace("二十","20");

            val = val.replace("一十一","11").replace("一十二","12").replace("一十三","13").replace("一十四","14").replace("一十五","15")
                    .replace("一十六","16").replace("一十七","17").replace("一十八","18").replace("一十九","19");

            val = val.replace("十一","11").replace("十二","12").replace("十三","13").replace("十四","14").replace("十五","15")
                    .replace("十六","16").replace("十七","17").replace("十八","18").replace("十九","19");

            val = val.replace("一","1").replace("二","2").replace("三","3").replace("四","4").replace("五","5")
                    .replace("六","6").replace("七","7").replace("八","8").replace("九","9").replace("十","10")
                    .replace("○","0").replace("〇","0").replace("O","0").replace("零","0").replace("Ｏ","0")
                    .replace("元","01").replace("О","0");

            val = val.replace("年", "-").replace("月", "-").replace("日", "").replace("号", "").replace(" ","");
            val = val.replaceAll("[\u4e00-\u9fa5]", "");
            String[] vals = val.split("-");
            if (vals.length == 3){
                val = vals[0].concat("-").concat(vals[1].length() < 2 ? "0".concat(vals[1]) : vals[1]).concat("-").concat(vals[2].length() < 2 ? "0".concat(vals[2]) : (vals[2].length() > 2 ? vals[2].substring(0, 2) : vals[2]));
            }

            date = val;

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            sdf.parse(date);

        }catch (Exception e){
            date = "";
        }
        return date;
    }

    public static void main(String[] args) {
        System.out.println(new getJudgeDate().evaluate("<div class=\"qcc_law_doc\"><div class=\"qcc_law_court\">      <span><a href=\"https://www.qcc.com/firm_g05c1eaa991fb3408059ccd25c09e1cd.html\" target=\"_blank\">浙江省高级人民法院</a></span></div><div class=\"qcc_law_paper_type\">     <span>民事裁定书</span></div><div class=\"qcc_law_case_no\">      <span>（2009）浙辖终字第128号</span></div><div class=\"qcc_law_judge_party_label\"><span>当事人信息</span></div><div class=\"qcc_law_judge_party\">              <span>上诉人（原审被告）浙江××实业集团股份有限公司，住所地浙江省××××号。</span>            <span>法定代表人徐××。</span>            <span>委托代理人汪×。</span>            <span>被上诉人（原审原告）梁××。</span>            <span>委托代理人罗×、许×。</span>    </div>    <div class=\"qcc_law_judge_trial_label\"><span>审理经过</span></div>    <div class=\"qcc_law_judge_trial\">                    <span>上诉人浙江××实业集团股份有限公司（以下简称华盛达××）因与被上诉人梁××证券虚假陈述赔偿纠纷一案，不服<a href=\"https://www.qcc.com/firm_gb6f65bc38e5566e52498c43194d55fe.html\" target=\"_blank\">杭州市中级人民法院</a>（2008）杭民二初字第309-1号民事裁定，向本院提起上诉。本院依法组成合议庭，审理了本案，现已审理终结。</span>            </div>    <div class=\"qcc_law_pre_court_review_label\"><span>前程序_法院查明</span></div>    <div class=\"qcc_law_pre_court_review\">                    <span>原审法院审查认为，《<a href=\"https://www.qcc.com/firm_g63843589f941d313f49661195831a7c.html\" target=\"_blank\">最高人民法院</a>关于审理证券市场因虚假陈述引发的民事赔偿案件的若干规定》第八条规定：“虚假陈述证券民事赔偿案件，由省、直辖市、自治区人民政府所在的市、计划单列市和经济特区中级人民法院管辖”。据此，原告梁××对被告华盛达××在该院提起诉讼符合法律规定。裁定驳回被告华盛达××对本案管辖权提出的异议。</span>            </div>    <div class=\"qcc_law_plaintiff_request_label\"><span>原告方诉求</span></div>    <div class=\"qcc_law_plaintiff_request\">                    <span>华盛达××上诉称，《<a href=\"https://www.qcc.com/firm_g63843589f941d313f49661195831a7c.html\" target=\"_blank\">最高人民法院</a>关于审理证券市场因虚假陈述引发的民事赔偿案件的若干规定》的法律效力不及新修改的《民事诉讼法》，应根据《民事诉讼法》的规定确定管辖。本案系侵权案件，侵权行为地及被告住所地均为浙江省湖州市德清县，根据《民事诉讼法》第二十九条的规定，应由湖州市中级人民法院管辖。同时由湖州市中级人民法院管辖更有利于查明案件事实，并可在一定程度上避免司法资源的浪费。请求撤销原审裁定，将案件移送至湖州市中级人民法院审理。</span>            </div>    <div class=\"qcc_law_current_review_label\"><span>法院查明</span></div>    <div class=\"qcc_law_current_review\">                    <span>经审查，原审原告梁××以华盛达××为被告向<a href=\"https://www.qcc.com/firm_gb6f65bc38e5566e52498c43194d55fe.html\" target=\"_blank\">杭州市中级人民法院</a>起诉称：2008年9月2日被告公告称收到中华乙共和国财政部出具的《财政部行政处罚决定书》，认定被告存在以下信息披露违法行为：一、虚计收入；二、大股东占用资金。根据《<a href=\"https://www.qcc.com/firm_g63843589f941d313f49661195831a7c.html\" target=\"_blank\">最高人民法院</a>关于审理证券市场因虚假陈述引发的民事赔偿案件的若干规定》，可以认定被告虚假陈述实施日为2007年4月28日，虚假陈述更正日为2008年8月30日。原告根据被告的运作情况、业绩表现、年报及其他文件和相关信息，得出被告已经进行充分、完整、及时、准确的信息披露的判断，并在2007年4月28日至2008年8月30日之间购买了其股票，并且在2008年8月30日以后卖出或持有其股票。原告由此遭受的损失人民币9194.13元与被告的虚假陈述行为之间具有法定的因果关系。请求判令被告赔偿原告经济损失人民币9194.13元并承担本案的公证费用及诉讼费用。梁××起诉，提供了证券帐户、行政处罚决定书、半年度报告、交易记录资料及损失计算、公证费发票等证据材料。</span>            </div>    <div class=\"qcc_law_current_identification_label\"><span>法院认为</span></div>    <div class=\"qcc_law_current_identification\">                    <span>本院认为，根据原审原告的诉称和提供的证据材料，本案系证券虚假陈述赔偿纠纷。《<a href=\"https://www.qcc.com/firm_g63843589f941d313f49661195831a7c.html\" target=\"_blank\">最高人民法院</a>关于审理证券市场因虚假陈述引发的民事赔偿案件的若干规定》第八条规定：虚假陈述证券民事赔偿案件，由省、直辖市、自治区人民政府所在的市、计划单列市和经济特区中级人民法院管辖。这是<a href=\"https://www.qcc.com/firm_g63843589f941d313f49661195831a7c.html\" target=\"_blank\">最高人民法院</a>针对证券虚假陈述赔偿纠纷案件的特别规定，本案应依据上述规定确定管辖。上诉人住所地为<a href=\"https://www.qcc.com/firm_g1124f4cbb30c378f14a609ff7e5603d.html\" target=\"_blank\">浙江省德清县</a>，<a href=\"https://www.qcc.com/firm_gb6f65bc38e5566e52498c43194d55fe.html\" target=\"_blank\">杭州市中级人民法院</a>作为浙江省人民政府所在的市中级人民法院有权对本案进行管辖。上诉人的上诉理由不能成立，其上诉请求本院不予支持。依照《中华乙共和国民事诉讼法》第一百五十四条及《<a href=\"https://www.qcc.com/firm_g63843589f941d313f49661195831a7c.html\" target=\"_blank\">最高人民法院</a>关于审理证券市场因虚假陈述引发的民事赔偿案件的若干规定》第八条之规定，裁定如下：</span>            </div>    <div class=\"qcc_law_judge_result_label\"><span>裁判结果</span></div>        <div class=\"qcc_law_judge_result\">                    <span>驳回上诉，维持原裁定。</span>                    <span>本裁定为终审裁定。</span>                    <span>（此页无正文）</span>            </div>    <div class=\"qcc_law_judge_collegiate_bench_label\"><span>审判员及陪审员</span></div>    <div class=\"qcc_law_judge_collegiate_bench\">                    <span>审判长潘华山</span>                    <span>审判员林诒高</span>                    <span>代理审判员徐乐盛</span>            </div><div class=\"qcc_law_judge_date_label\"><span>裁判日期</span></div><div class=\"qcc_law_judge_date\">    <span>二零零九年六月七日本件与原本核对无异</span></div><div class=\"qcc_law_judge_recorder_label\"><span>书记员及法官助理</span></div><div class=\"qcc_law_judge_recorder\">            <span>书记员孟德玖</span>    </div></div>"));
    }
}
