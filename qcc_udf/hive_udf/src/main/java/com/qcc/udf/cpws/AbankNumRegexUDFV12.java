package com.qcc.udf.cpws;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.List;
import java.util.Map;


public class AbankNumRegexUDFV12 extends UDF {

    public static String evaluate(String input, String input2, String aiResult) {
        try {
            input = StringUtils.isEmpty(input) ? "" : input;
            if (StringUtils.isEmpty(aiResult)) {
                return null;
            }
            aiResult = aiResult.replace("（", "(").replace("）", ")");
            List<CaseRoleExtractEntity> jsonArray = JSONObject.parseArray(aiResult, CaseRoleExtractEntity.class);

            Map<String, String> rolemap = new HashedMap();
            for (CaseRoleExtractEntity entity : jsonArray) {
                rolemap.put(entity.getName(), entity.getRole());
            }
            input = input.replace("（", "(").replace("）", ")");
            Map<String, String> map = new HashedMap();
            input2 = input2.replace("（", "(").replace("）", ")");
            String finalInput = input;
            String finalInput1 = input2;
            rolemap.forEach((k, v) -> {
                int nameIdx = finalInput.indexOf(k);
                int roleIdx = finalInput.indexOf(v);

                int nameIdx2 = finalInput1.indexOf(k);
                int roleIdx2 = finalInput1.indexOf(v);
                if ((nameIdx > -1 && roleIdx > -1)) {
                    if (nameIdx < roleIdx) {
                        map.put(k, v);
                    }

                } else if ((nameIdx2 > -1 && roleIdx2 > -1)) {
                    if (nameIdx2 < roleIdx2) {
                        map.put(k, v);
                    }
                }
            });
            if (map.size() > 0) {
                return JSON.toJSONString(map);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    public static void main(String[] args) {
        System.out.println(evaluate("原告：孟黑龙江省齐齐哈尔市建华区人民法院 民事判决书", "（2016）黑0203民初1509号 原告：孟某某，女，****年**月**日出生， 委托诉讼代理人：徐某某，黑龙江夙生律师事务所律师。 被告：齐齐哈尔市建华区住房和城乡建设局，住所地齐齐哈尔市建华区民乐街。 法定代表人：温某某，局长。 委托诉讼代理人：高某某，法律顾问。 被告：孟某某，男，****年**月**日出生，汉族， 委托诉讼代理人：王某某，黑龙ＸＸ天律师事务所律师。 原告孟某某与被告齐齐哈尔市建华区住房和城乡建设局、孟某某确认合同无效纠纷一案，本院于2016年8月25日立案后，依法适用简易程序，公开开庭进行了审理。原告孟某某的委托代理人徐某某、被告齐齐哈尔市建华区住房和城乡建设局的委托代理人高某某、被告孟某某的委托代理人王某某到庭参加诉讼。本案现已审理终结。",
                "[{\"Role\":\"原告\",\"KeyNo\":\"\",\"LawFirmList\":[],\"RoleTag\":0,\"Name\":\"孟黑龙江省齐齐哈尔市建华区人民法院\"}, {\"Role\":\"被告\",\"KeyNo\":\"g756f4912e63bab3134f7b2190eeb3ec\",\"LawFirmList\":[],\"RoleTag\":1,\"Name\":\"齐齐哈尔市建华区住房和城乡建设局\"}]"));
    }

}
