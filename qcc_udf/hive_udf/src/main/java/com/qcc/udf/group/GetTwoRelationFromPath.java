package com.qcc.udf.group;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class GetTwoRelationFromPath extends UDF {
//    public static void main(String[] args) {
//        String path = "00886400258e69eefa36271977c7050c-39bd2225c526147d9026feb6848e687c-cd17661c32b6b36704d5e839ba7fe87f-328d5471a6c05b899546c3b485951425-pc32f395c356c565b06e811dcd2c56a7";
//        String result = evaluate("cd17661c32b6b36704d5e839ba7fe87f", path);
//        System.out.printf(result.toString());
//    }

    public static String evaluate(String node, String path) {
        String twoRelation = "";
        try {
            if (StringUtils.isNotBlank(path) && path.contains("-") && StringUtils.isNotBlank(node)) {
                String regVal = "[0-9a-z]{32}-" + node;
                List<String> matchList = RegexHelper.getGlobalRegex(regVal, path);
                if (CollectionUtils.isNotEmpty(matchList)) {
                    twoRelation = matchList.get(0);
                }
            }
        } catch (Exception e) {
        }
        return twoRelation;
    }
}