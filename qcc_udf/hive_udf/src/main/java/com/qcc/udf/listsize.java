package com.qcc.udf;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;

public class listsize extends UDF {
    public static String evaluate(String s){
        ArrayList<Object> js2 = new ArrayList<Object>();
        Gson gson = new GsonBuilder().create();
        try{
            js2 = gson.fromJson(s, new TypeToken<ArrayList<Object>>() {}.getType());
            return js2.size()+"";
        }catch(Exception e){
            System.out.println(3);
            return 0+"";
        }
    }

}