package com.qcc.udf.cpws.wjp;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class ShiXinYiWuCleanUdf extends UDF {
    public static String evaluate(String inputText) {
        if (inputText == null || inputText.isEmpty()) {
            return inputText; // 如果输入为空，返回空字符串
        }
        inputText = addLineBreakBeforeKeywords(inputText);
        //基本案情: 审理法院: 乌兰县人民法院开始时间: 09时30分开庭地点: 第五法庭开庭日期（听证日期）: 2022年09月29日承办人: 和绿江案件名称: 青海乌兰农村商业银行股份有限公司诉马麒等金融借款合同纠纷一案案号: （2022）青2821民初250号案由: 金融借款合同纠纷
        Pattern caseNumberPattern = Pattern.compile("[(（]\\d{4}[)）][\u4e00-\u9fa5A-Za-z0-9]+号(之[0-9|零一二三四五六七八九十])?");
        List<String> keywords = Arrays.asList("本院判令:", "如不服", "现依法向你", "逾期", "特此公告", "原告诉讼请求", "自本公告", "自公告",
                "本院定于", "提出答辩状", "在审理过程", "本院依法作出", "裁定如下", "判决:",
                "开庭日期:", "承办人", "本案定于", "自发出公告", "本院联系");
        List<String> paragraphs = new ArrayList<>();
        StringBuilder currentParagraph = new StringBuilder();
        String[] sentences = inputText.split("(?<=。)"); // 按句号拆分为句子

        boolean isFirstSentence = true; // 标记是否为第一句

        for (String sentence : sentences) {
            String trimmedSentence = sentence.trim();

            if (isFirstSentence) {
                isFirstSentence = false;
                if (trimmedSentence.matches(".*法院公告.*")) {
                    int colonIndex = trimmedSentence.indexOf("法院公告") + 4;
                    paragraphs.add(trimmedSentence.substring(0, colonIndex).trim());
                    currentParagraph.append(trimmedSentence.substring(colonIndex).trim()).append(" ");
                    trimmedSentence = trimmedSentence.substring(colonIndex);
                }
                // 如果剩余文本包含案号，按案号规则换行
                if (caseNumberPattern.matcher(trimmedSentence).find()) {
                    int caseIndex = findCaseNumberIndex(trimmedSentence, caseNumberPattern);
                    int i = trimmedSentence.indexOf(",");
                    int i1 = trimmedSentence.indexOf(":");

                    if (!(i < caseIndex && i > 0) && !(i1 < caseIndex && i1 > 0)) {

                        paragraphs.add(trimmedSentence.substring(0, caseIndex).trim());
                        currentParagraph.append(trimmedSentence.substring(caseIndex).trim()).append(" ");
                        trimmedSentence = trimmedSentence.substring(caseIndex, trimmedSentence.length());
                    }
                }


                // 对第一句特殊处理：如果包含冒号或全角冒号，且冒号前没有括号以外的符号，且不包含"*"，则在冒号后换行
                if ((trimmedSentence.contains(":") || trimmedSentence.contains("：")) && !trimmedSentence.contains("基本案情")) {
                    int colonIndex = Math.max(trimmedSentence.indexOf(":"), trimmedSentence.indexOf("：")) + 1;

                    if (colonIndex != -1 && isWithinParentheses(trimmedSentence, colonIndex)) {
                        colonIndex = findNextColonOrEnd(trimmedSentence, colonIndex);
                    }

                    if (colonIndex != -1) {

                        paragraphs.add(trimmedSentence.substring(0, colonIndex).trim());
                        if (currentParagraph.length() > 1) {
                            currentParagraph = new StringBuilder();
                        }
                        currentParagraph.append(trimmedSentence.substring(colonIndex).trim()).append(" ");
                        continue;
                    }

                }
            }

            boolean addedToParagraph = false;
            // 对其他句子，若符合关键字规则，则在关键字后的冒号后换行
            for (String keyword : keywords) {
                if (trimmedSentence.startsWith(keyword)) {

                    paragraphs.add(currentParagraph.toString().trim());
                    currentParagraph.setLength(0);
                    paragraphs.add(trimmedSentence);

                    addedToParagraph = true;
                    break;
                }
            }

            if (!addedToParagraph && (!trimmedSentence.trim().contains(currentParagraph.toString().trim()) || StringUtils.isEmpty(currentParagraph.toString()))) {
                currentParagraph.append(trimmedSentence).append(" ");
            }
        }

        // 添加最后一段
        if (currentParagraph.length() > 0) {
            paragraphs.add(currentParagraph.toString().trim());
        }
        StringBuilder result = new StringBuilder();
        paragraphs = paragraphs.stream().filter(x -> x.length() > 0).collect(Collectors.toList());
        for (String s : paragraphs) {
            if (!s.matches(".*(\\d{4}-\\d{2}-\\d{2}|\\d{2}:\\d{2}).*")) {
                result.append(s).append("\n");
            } else {
                result.append(s);
            }
        }
        // 将段落用换行符连接返回
        return result.toString();
    }

    /**
     * 查找案号在文本中的索引
     */
    private static int findCaseNumberIndex(String text, Pattern caseNumberPattern) {
        Matcher matcher = caseNumberPattern.matcher(text);
        return matcher.find() ? matcher.end() : 0;
    }

    private static boolean isWithinParentheses(String text, int index) {
        int openParen = text.lastIndexOf('(', index);
        int closeParen = text.indexOf(')', index);
        return openParen != -1 && closeParen != -1 && openParen < index && closeParen > index;
    }

    private static int findNextColonOrEnd(String text, int currentIndex) {
        int nextColon = text.indexOf(":", currentIndex + 1);
        int endOfSegment = text.indexOf("号)：", currentIndex);
        if (endOfSegment != -1 && (nextColon == -1 || endOfSegment < nextColon)) {
            return endOfSegment + 2; // 包含“号)：”
        }
        return nextColon;
    }

    // 定义关键字数组，兼容全半角
    private static final String[] KEYWORDS = {
            "基本案情[:|：]", "审理法院[:|：]", "开始时间[:|：]", "开庭地点[:|：]", "开庭日期（听证日期）[:|：]",
            "承办人[:|：]", "案件名称[:|：]", "案号[:|：]", "案由[:|：]"
    };

    // UDF 方法实现
    public static String addLineBreakBeforeKeywords(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        // 构造正则表达式，兼容全半角
        StringBuilder regexBuilder = new StringBuilder();
        for (String keyword : KEYWORDS) {
            // 添加关键字匹配规则
            if (regexBuilder.length() > 0) {
                regexBuilder.append("|");
            }
            regexBuilder.append(keyword);
        }


        String regex = "(" + regexBuilder.toString() + ")";

        // 使用正则表达式匹配并替换
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        // 使用 StringBuffer 构建替换后的字符串
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(result, "\n" + matcher.group(1));
        }
        matcher.appendTail(result);

        return result.toString().replace("\n\n", "\n");
    }

    public static void main(String[] args) {
        System.out.println(evaluate("\n" +
                "基本案情:危险驾驶罪\n" +
                "审理法院:正阳县人民法院\n" +
                "开庭地点:第九法庭\n" +
                "承办人:李向东\n" +
                "案件名称:被告人刘齐危险驾驶罪一案\n" +
                "案号:（2023）豫1724刑初301号\n" +
                "案由:\n"));

    }

}
