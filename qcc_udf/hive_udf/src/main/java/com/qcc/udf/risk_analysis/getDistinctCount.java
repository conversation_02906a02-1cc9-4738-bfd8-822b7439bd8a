package com.qcc.udf.risk_analysis;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date ：Created in 2021/03/03 13:34
 * @description ：汇总去重后id的个数
 */
public class getDistinctCount  extends UDF {

    public static int evaluate(String param, String splitWord) {
        int cnt = 0;
        if (StringUtils.isNotEmpty(param)) {
            String[] items = param.split(splitWord);
            Set<String> itemSet = new HashSet<>();
            for(String item : items) {
                if (StringUtils.isNotEmpty(item)) {
                    itemSet.add(item);
                }
            }
            cnt = itemSet.size();
        }
        return cnt;
    }

    public static void main(String[] args) {
        String param = "1,2,3,,3,4,5";
        System.out.println(evaluate(param, ","));
    }
}
