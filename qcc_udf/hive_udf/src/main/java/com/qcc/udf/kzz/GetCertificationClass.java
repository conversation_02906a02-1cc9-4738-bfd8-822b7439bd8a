package com.qcc.udf.kzz;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;
import java.util.HashMap;
import java.util.Map;

/**
 * 计算资质证书分类
 *
 * <AUTHOR>
 * @date 2022/4/18
 */
public class GetCertificationClass extends UDF {

    /**
     * 10101 商业特许经营备案 被拆成单独的维度, 所以删除
     * CCC分类从安全生产许可证中拆出,单独一大类
     * 安全生产许可证单独一大类
     */
    private final static Map<String, CertificationClass> certificateCLassMap = new HashMap<>();

    static {
        certificateCLassMap.put("工程勘察工程测量专业丙级", new CertificationClass("030101", "工程勘察", "建筑资质"));
        certificateCLassMap.put("工程勘察水文地质勘察专业丙级", new CertificationClass("030102", "工程勘察", "建筑资质"));
        certificateCLassMap.put("工程勘察资质证书", new CertificationClass("030103", "工程勘察", "建筑资质"));
        certificateCLassMap.put("工程招标代理乙级", new CertificationClass("030201", "工程招标代理", "建筑资质"));
        certificateCLassMap.put("工程招标代理暂定级", new CertificationClass("030202", "工程招标代理", "建筑资质"));
        certificateCLassMap.put("工程招标代理甲级", new CertificationClass("030203", "工程招标代理", "建筑资质"));
        certificateCLassMap.put("招标代理机构资质证书", new CertificationClass("030204", "工程招标代理", "建筑资质"));
        certificateCLassMap.put("招标代理资格", new CertificationClass("030205", "工程招标代理", "建筑资质"));
        certificateCLassMap.put("招标代理资质证书", new CertificationClass("030206", "工程招标代理", "建筑资质"));
        certificateCLassMap.put("信息系统工程监理资质单位证书", new CertificationClass("030301", "工程监理", "建筑资质"));
        certificateCLassMap.put("信息系统工程监理资质工程师证书", new CertificationClass("030302", "工程监理", "建筑资质"));
        certificateCLassMap.put("工程监理企业资质证书", new CertificationClass("030303", "工程监理", "建筑资质"));
        certificateCLassMap.put("工程监理市政公用工程专业乙级", new CertificationClass("030304", "工程监理", "建筑资质"));
        certificateCLassMap.put("工程监理房屋建筑工程专业丙级", new CertificationClass("030305", "工程监理", "建筑资质"));
        certificateCLassMap.put("工程监理房屋建筑工程专业乙级", new CertificationClass("030306", "工程监理", "建筑资质"));
        certificateCLassMap.put("工程监理房屋建筑工程专业甲级", new CertificationClass("030307", "工程监理", "建筑资质"));
        certificateCLassMap.put("工程监理机电安装工程专业乙级", new CertificationClass("030308", "工程监理", "建筑资质"));
        certificateCLassMap.put("工程监理水利水电工程专业乙级", new CertificationClass("030309", "工程监理", "建筑资质"));
        certificateCLassMap.put("工程监理港口与航道工程专业乙级", new CertificationClass("030310", "工程监理", "建筑资质"));
        certificateCLassMap.put("工程监理资质证书", new CertificationClass("030311", "工程监理", "建筑资质"));
        certificateCLassMap.put("工程设计公路行业公路专业丙级", new CertificationClass("030401", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计市政行业排水工程专业乙级", new CertificationClass("030402", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计市政行业桥梁工程专业乙级", new CertificationClass("030403", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计市政行业给水工程专业乙级", new CertificationClass("030404", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计市政行业道路工程专业丙级", new CertificationClass("030405", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计市政行业道路工程专业乙级", new CertificationClass("030406", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计建筑幕墙工程专项乙级", new CertificationClass("030407", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计建筑智能化系统专项乙级", new CertificationClass("030408", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计建筑智能化系统专项甲级", new CertificationClass("030409", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计建筑行业（人防工程）乙级", new CertificationClass("030410", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计建筑行业（建筑工程）丁级", new CertificationClass("030411", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计建筑行业（建筑工程）丙级", new CertificationClass("030412", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计建筑行业（建筑工程）乙级", new CertificationClass("030413", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计建筑装饰工程专项丙级", new CertificationClass("030414", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计建筑装饰工程专项乙级", new CertificationClass("030415", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计建筑装饰工程专项甲级", new CertificationClass("030416", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计水利行业灌溉排涝专业丙级", new CertificationClass("030417", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计消防设施工程专项乙级", new CertificationClass("030418", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计消防设施工程专项甲级", new CertificationClass("030419", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计照明工程专项乙级", new CertificationClass("030420", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计环境工程专项（固体废物处理处置工程）乙级", new CertificationClass("030421", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计电力行业变电工程专业丙级", new CertificationClass("030422", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计电力行业新能源发电专业乙级", new CertificationClass("030423", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计电力行业水力发电（含抽水蓄能、潮汐）专业乙级", new CertificationClass("030424", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计电力行业甲级", new CertificationClass("030425", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计资质证书", new CertificationClass("030426", "工程设计", "建筑资质"));
        certificateCLassMap.put("工程设计风景园林工程专项乙级", new CertificationClass("030427", "工程设计", "建筑资质"));
        certificateCLassMap.put("建筑幕墙工程设计与施工二级", new CertificationClass("030428", "工程设计", "建筑资质"));
        certificateCLassMap.put("建设工程质量检测单位", new CertificationClass("030501", "工程质量检测", "建筑资质"));
        certificateCLassMap.put("工程造价咨询企业资质证书", new CertificationClass("030601", "工程造价咨询", "建筑资质"));
        certificateCLassMap.put("工程造价咨询甲级", new CertificationClass("030602", "工程造价咨询", "建筑资质"));
        certificateCLassMap.put("公路工程施工总承包三级", new CertificationClass("030701", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("公路工程施工总承包二级", new CertificationClass("030702", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("公路路基工程专业承包二级", new CertificationClass("030703", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("古建筑工程专业承包三级", new CertificationClass("030704", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("地基基础工程专业承包一级", new CertificationClass("030705", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("地基基础工程专业承包三级", new CertificationClass("030706", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("地基基础工程专业承包二级", new CertificationClass("030707", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("城市及道路照明工程专业承包一级", new CertificationClass("030708", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("城市及道路照明工程专业承包三级", new CertificationClass("030709", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("市政公用工程施工总承包一级", new CertificationClass("030710", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("市政公用工程施工总承包三级", new CertificationClass("030711", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("市政公用工程施工总承包二级", new CertificationClass("030712", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("建筑业企业资质", new CertificationClass("030713", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("建筑工程施工总承包一级", new CertificationClass("030714", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("建筑工程施工总承包三级", new CertificationClass("030715", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("建筑工程施工总承包二级", new CertificationClass("030716", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("建筑幕墙工程专业承包二级", new CertificationClass("030717", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("建筑机电安装工程专业承包三级", new CertificationClass("030718", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("建筑装修装饰工程专业承包一级", new CertificationClass("030719", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("建筑装修装饰工程专业承包二级", new CertificationClass("030720", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("机电工程施工总承包三级", new CertificationClass("030721", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("机电工程施工总承包二级", new CertificationClass("030722", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("模板脚手架专业承包不分等级", new CertificationClass("030723", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("水利水电工程施工总承包三级", new CertificationClass("030724", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("水利水电工程施工总承包二级", new CertificationClass("030725", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("河湖整治工程专业承包三级", new CertificationClass("030726", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("消防设施工程专业承包一级", new CertificationClass("030727", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("消防设施工程专业承包二级", new CertificationClass("030728", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("特种工程(建筑物纠偏和平移)专业承包不分等级", new CertificationClass("030729", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("特种工程(结构补强)专业承包不分等级", new CertificationClass("030730", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("环保工程专业承包一级", new CertificationClass("030731", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("环保工程专业承包三级", new CertificationClass("030732", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("环保工程专业承包二级", new CertificationClass("030733", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("电力工程施工总承包三级", new CertificationClass("030734", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("电子与智能化工程专业承包二级", new CertificationClass("030735", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("石油化工工程施工总承包一级", new CertificationClass("030736", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("石油化工工程施工总承包三级", new CertificationClass("030737", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("矿山工程施工总承包三级", new CertificationClass("030738", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("起重设备安装工程专业承包三级", new CertificationClass("030739", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("输变电工程专业承包一级", new CertificationClass("030740", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("输变电工程专业承包三级", new CertificationClass("030741", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("钢结构工程专业承包三级", new CertificationClass("030742", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("钢结构工程专业承包二级", new CertificationClass("030743", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("防水防腐保温工程专业承包一级", new CertificationClass("030744", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("防水防腐保温工程专业承包二级", new CertificationClass("030745", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("隧道工程专业承包三级", new CertificationClass("030746", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("预拌混凝土专业承包不分等级", new CertificationClass("030747", "建筑业企业资质", "建筑资质"));
        certificateCLassMap.put("房地产估价机构信用档案", new CertificationClass("030801", "房地产估价机构资质", "建筑资质"));
        certificateCLassMap.put("房地产开发企业资质", new CertificationClass("030901", "房地产开发企业资质", "建筑资质"));
        certificateCLassMap.put("建筑智能化工程设计与施工二级", new CertificationClass("031001", "设计施工一体化", "建筑资质"));
        certificateCLassMap.put("建筑装饰装修工程设计与施工三级", new CertificationClass("031002", "设计施工一体化", "建筑资质"));
        certificateCLassMap.put("建筑装饰装修工程设计与施工二级", new CertificationClass("031003", "设计施工一体化", "建筑资质"));
        certificateCLassMap.put("消防设施工程设计与施工二级", new CertificationClass("031004", "设计施工一体化", "建筑资质"));
        certificateCLassMap.put("设计与施工一体化资质", new CertificationClass("031005", "设计施工一体化", "建筑资质"));
        certificateCLassMap.put("设计施工一体化企业资质证书", new CertificationClass("031006", "设计施工一体化", "建筑资质"));
        certificateCLassMap.put("设计施工一体化资质证书", new CertificationClass("031007", "设计施工一体化", "建筑资质"));
        certificateCLassMap.put("排污许可证", new CertificationClass("040101", "排污许可证", "排污许可证"));
        certificateCLassMap.put("一般服务认证", new CertificationClass("050101", "一般服务认证", "服务认证"));
        certificateCLassMap.put("体育场所服务认证", new CertificationClass("050201", "体育场所服务认证", "服务认证"));
        certificateCLassMap.put("信息安全服务资质认证", new CertificationClass("050301", "信息安全服务资质认证", "服务认证"));
        certificateCLassMap.put("商品售后服务评价认证", new CertificationClass("050401", "商品售后服务评价认证", "服务认证"));
        certificateCLassMap.put("所有未列明的其他服务认证", new CertificationClass("050501", "所有未列明的其他服务认证", "服务认证"));
        certificateCLassMap.put("汽车玻璃零配安装服务认证", new CertificationClass("050601", "汽车玻璃零配安装服务认证", "服务认证"));
        certificateCLassMap.put("物业企业信息", new CertificationClass("050701", "物业服务", "服务认证"));
        certificateCLassMap.put("绿色市场认证", new CertificationClass("050801", "绿色市场认证", "服务认证"));
        certificateCLassMap.put("软件过程能力及成熟度评估认证", new CertificationClass("050901", "软件过程能力及成熟度评估认证", "服务认证"));
        certificateCLassMap.put("防爆电器检修服务认证", new CertificationClass("051001", "防爆电器检修服务认证", "服务认证"));
        certificateCLassMap.put("服务资质认证证书", new CertificationClass("060101", "服务资质认证证书", "服务资质认证证书"));
        certificateCLassMap.put("2006年度跨地区增值电信业务经营许可证书", new CertificationClass("070101", "电信业务经营许可证", "电信资质"));
        certificateCLassMap.put("2006年度跨地区增值电信业务经营许可证年检不合格企业", new CertificationClass("070102", "电信业务经营许可证", "电信资质"));
        certificateCLassMap.put("电信业务经营许可证审批", new CertificationClass("070103", "电信业务经营许可证", "电信资质"));
        certificateCLassMap.put("电信网码号资源使用审批", new CertificationClass("070104", "电信业务经营许可证", "电信资质"));
        certificateCLassMap.put("中国共产党基层组织建设质量管理体系", new CertificationClass("080101", "中国共产党基层组织建设质量管理体系", "管理体系认证"));
        certificateCLassMap.put("中国森林认证", new CertificationClass("080201", "中国森林认证", "管理体系认证"));
        certificateCLassMap.put("中国职业健康安全管理体系认证", new CertificationClass("080301", "中国职业健康安全管理体系认证", "管理体系认证"));
        certificateCLassMap.put("企业体系认证", new CertificationClass("080401", "企业体系认证", "管理体系认证"));
        certificateCLassMap.put("企业知识产权管理体系认证", new CertificationClass("080501", "企业知识产权管理体系认证", "管理体系认证"));
        certificateCLassMap.put("企业社会责任管理体系认证", new CertificationClass("080601", "企业社会责任管理体系认证", "管理体系认证"));
        certificateCLassMap.put("供应链安全管理体系认证", new CertificationClass("080701", "供应链安全管理体系认证", "管理体系认证"));
        certificateCLassMap.put("信息安全管理体系认证", new CertificationClass("080801", "信息安全管理体系认证", "管理体系认证"));
        certificateCLassMap.put("信息技术服务管理体系认证", new CertificationClass("080901", "信息技术服务管理体系认证", "管理体系认证"));
        certificateCLassMap.put("其它管理体系认证", new CertificationClass("081001", "其它管理体系认证", "管理体系认证"));
        certificateCLassMap.put("医疗器械质量管理体系认证", new CertificationClass("081101", "医疗器械质量管理体系认证", "管理体系认证"));
        certificateCLassMap.put("商品和服务在生命周期内的温室气体排放评价规范认证", new CertificationClass("081201", "商品和服务在生命周期内的温室气体排放评价", "管理体系认证"));
        certificateCLassMap.put("国际铁路行业质量管理体系认证", new CertificationClass("081301", "国际铁路行业质量管理体系认证", "管理体系认证"));
        certificateCLassMap.put("建设施工行业质量管理体系认证", new CertificationClass("081401", "建设施工行业质量管理体系认证", "管理体系认证"));
        certificateCLassMap.put("德国汽车工业协会质量管理体系认证", new CertificationClass("081501", "德国汽车工业协会质量管理体系认证", "管理体系认证"));
        certificateCLassMap.put("所有未列明的其他管理体系认证", new CertificationClass("081601", "所有未列明的其他管理体系认证", "管理体系认证"));
        certificateCLassMap.put("森林认证FSC", new CertificationClass("081701", "森林认证FSC", "管理体系认证"));
        certificateCLassMap.put("森林认证PEFC", new CertificationClass("081801", "森林认证PEFC", "管理体系认证"));
        certificateCLassMap.put("汽车行业质量管理体系认证", new CertificationClass("081901", "汽车行业质量管理体系认证", "管理体系认证"));
        certificateCLassMap.put("测量管理体系", new CertificationClass("082001", "测量管理体系", "管理体系认证"));
        certificateCLassMap.put("温室气体排放和清除的量化和报告的规范及指南认证", new CertificationClass("082101", "温室气体排放和清除的量化和报告的规范及指南认证", "管理体系认证"));
        certificateCLassMap.put("环境管理体系认证", new CertificationClass("082201", "环境管理体系认证", "管理体系认证"));
        certificateCLassMap.put("电气与电子元件和产品有害物质过程控制管理体系认证", new CertificationClass("082301", "电气与电子元件和产品有害物质过程控制管理体系认证", "管理体系认证"));
        certificateCLassMap.put("电讯业质量管理体系认证", new CertificationClass("082401", "电讯业质量管理体系认证", "管理体系认证"));
        certificateCLassMap.put("能源管理体系认证", new CertificationClass("082501", "能源管理体系认证", "管理体系认证"));
        certificateCLassMap.put("航空业质量管理体系认证", new CertificationClass("082601", "航空业质量管理体系认证", "管理体系认证"));
        certificateCLassMap.put("航空仓储销售商质量管理体系认证", new CertificationClass("082701", "航空仓储销售商质量管理体系认证", "管理体系认证"));
        certificateCLassMap.put("航空器维修质量管理体系认证", new CertificationClass("082801", "航空器维修质量管理体系认证", "管理体系认证"));
        certificateCLassMap.put("质量管理体系认证（ISO9000）", new CertificationClass("082901", "质量管理体系认证(ISO9000)", "管理体系认证"));
        certificateCLassMap.put("质量管理体系认证（ISO9001）", new CertificationClass("082901", "质量管理体系认证(ISO9000)", "管理体系认证"));
        certificateCLassMap.put("运输资产保护协会 运输供应商最低安全要求认证", new CertificationClass("083001", "运输资产保护协会 运输供应商最低安全要求认证", "管理体系认证"));
        certificateCLassMap.put("静电防护标准认证", new CertificationClass("083101", "静电防护标准认证", "管理体系认证"));
        certificateCLassMap.put("食品安全管理体系认证", new CertificationClass("083201", "食品安全管理体系认证", "管理体系认证"));
        certificateCLassMap.put("验证合格评定程序认证", new CertificationClass("083301", "验证合格评定程序认证", "管理体系认证"));
        certificateCLassMap.put("中国电子招标投标系统认证", new CertificationClass("090101", "中国电子招标投标系统认证", "自愿性产品认证"));
        certificateCLassMap.put("低碳产品认证", new CertificationClass("090201", "低碳产品认证", "自愿性产品认证"));
        certificateCLassMap.put("低碳产品认证-通用硅酸盐水泥", new CertificationClass("090301", "低碳产品认证", "自愿性产品认证"));
        certificateCLassMap.put("信息安全产品认证（未列入强制性产品认证目录内的信息安全产品）", new CertificationClass("090401", "信息安全产品认证(未列入强制性产品认证目录内的信息安全产品)", "自愿性产品认证"));
        certificateCLassMap.put("光伏产品认证", new CertificationClass("090501", "光伏产品认证", "自愿性产品认证"));
        certificateCLassMap.put("其他自愿性工业产品认证", new CertificationClass("090601", "其他自愿性工业产品认证", "自愿性产品认证"));
        certificateCLassMap.put("可再生能源/新能源", new CertificationClass("090701", "可再生能源", "自愿性产品认证"));
        certificateCLassMap.put("城市轨道交通产品认证", new CertificationClass("090801", "城市轨道交通产品认证", "自愿性产品认证"));
        certificateCLassMap.put("建筑节能产品认证", new CertificationClass("090901", "建筑节能产品认证", "自愿性产品认证"));
        certificateCLassMap.put("环保产品认证", new CertificationClass("091001", "环保产品认证", "自愿性产品认证"));
        certificateCLassMap.put("环境标志产品", new CertificationClass("091101", "环境标志产品", "自愿性产品认证"));
        certificateCLassMap.put("环境标志产品认证", new CertificationClass("091201", "环境标志产品认证", "自愿性产品认证"));
        certificateCLassMap.put("电子信息产品污染控制自愿性认证", new CertificationClass("091301", "电子信息产品污染控制自愿性认证", "自愿性产品认证"));
        certificateCLassMap.put("节水产品认证", new CertificationClass("091401", "节水产品认证", "自愿性产品认证"));
        certificateCLassMap.put("节能产品认证（不含建筑节能）", new CertificationClass("091501", "节能产品认证(不含建筑节能)", "自愿性产品认证"));
        certificateCLassMap.put("铁路产品认证", new CertificationClass("091601", "铁路产品认证", "自愿性产品认证"));
        certificateCLassMap.put("防爆电气产品认证", new CertificationClass("091701", "防爆电气产品认证", "自愿性产品认证"));
        certificateCLassMap.put("风电产品认证", new CertificationClass("091801", "风电产品认证", "自愿性产品认证"));
        certificateCLassMap.put("中国食品农产品认证", new CertificationClass("100101", "中国食品农产品认证", "食品农产品认证"));
        certificateCLassMap.put("乳制品生产企业危害分析与关键控制点(HACCP)体系认证", new CertificationClass("100201", "乳制品生产企业危害分析与关键控制点(HACCP)体系认证", "食品农产品认证"));
        certificateCLassMap.put("乳制品生产企业良好生产规范认证", new CertificationClass("100301", "乳制品生产企业良好生产规范认证", "食品农产品认证"));
        certificateCLassMap.put("危害分析与关键控制点认证", new CertificationClass("100401", "危害分析与关键控制点认证", "食品农产品认证"));
        certificateCLassMap.put("无公害农产品", new CertificationClass("100501", "无公害农产品", "食品农产品认证"));
        certificateCLassMap.put("有机产品（OGA）", new CertificationClass("100601", "有机产品(OGA)", "食品农产品认证"));
        certificateCLassMap.put("有机产品认证", new CertificationClass("100701", "有机产品认证", "食品农产品认证"));
        certificateCLassMap.put("良好农业规范（GAP）", new CertificationClass("100801", "良好农业规范(GAP)", "食品农产品认证"));
        certificateCLassMap.put("食品质量认证（酒类）", new CertificationClass("100901", "食品质量认证(酒类)", "食品农产品认证"));
        certificateCLassMap.put("饲料产品", new CertificationClass("101001", "饲料产品", "食品农产品认证"));
        certificateCLassMap.put("化妆品生产许可获证企业", new CertificationClass("110101", "化妆品生产许可证", "食药资质"));
        certificateCLassMap.put("医疗器械生产企业（备案）", new CertificationClass("110201", "医疗器械生产企业（备案）", "食药资质"));
        certificateCLassMap.put("医疗器械生产企业（许可）", new CertificationClass("110301", "医疗器械生产企业（许可）", "食药资质"));
        certificateCLassMap.put("医疗器械广告批准文号", new CertificationClass("110401", "医疗器械生产许可证(许可)", "食药资质"));
        certificateCLassMap.put("医疗器械经营企业（备案）", new CertificationClass("110501", "医疗器械经营企业(备案)", "食药资质"));
        certificateCLassMap.put("医疗器械经营企业（许可）", new CertificationClass("110601", "医疗器械经营企业(许可)", "食药资质"));
        certificateCLassMap.put("国产医疗器械产品（备案）", new CertificationClass("110701", "国产医疗器械产品(备案)", "食药资质"));
        certificateCLassMap.put("国产器械注册号", new CertificationClass("110801", "国产医疗器械产品(注册)", "食药资质"));
        certificateCLassMap.put("进口医疗器械产品（备案）", new CertificationClass("110901", "进口医疗器械产品(备案)", "食药资质"));
        certificateCLassMap.put("食品生产许可证", new CertificationClass("111001", "食品生产许可证", "食药资质"));
        certificateCLassMap.put("食品经营许可证", new CertificationClass("111101", "食品经营许可证", "食药资质"));
        certificateCLassMap.put("高新技术企业", new CertificationClass("120101", "高新技术企业", "高新技术企业"));
        certificateCLassMap.put("集成企业资质认证", new CertificationClass("130101", "集成企业资质认证", "集成企业资质认证"));
        certificateCLassMap.put("CCC", new CertificationClass("140101", "CCC", "CCC"));
        certificateCLassMap.put("CCC产品认证", new CertificationClass("140201", "CCC产品认证", "CCC"));
        certificateCLassMap.put("CCC工厂信息", new CertificationClass("140301", "CCC工厂信息", "CCC"));
        certificateCLassMap.put("CCC强制性产品查询", new CertificationClass("140401", "CCC强制性产品查询", "CCC"));
        certificateCLassMap.put("CCC证书", new CertificationClass("140501", "CCC证书", "CCC"));
        certificateCLassMap.put("安全生产许可证", new CertificationClass("150101", "安全生产许可证", "安全生产许可证"));
    }

    /**
     * @param name
     * @param type 0-code,1-一级分类名称,2-二级分类名称
     * @return
     */
    public static String evaluate(String name, int type) {
        String result = "";
        try {
            if (StringUtils.isNotBlank(name)) {
                CertificationClass classTuple = certificateCLassMap.get(name);
                if (null != classTuple) {
                    switch (type) {
                        case 0:
                            String temp = classTuple.getCode();
                            result = temp.substring(0, 2) + "," + temp.substring(0, 4) + "," + temp;
                            break;
                        case 1:
                            result = classTuple.getLevel1Name();
                            break;
                        case 2:
                            result = classTuple.getLevel2Name();
                            break;
                        default:
                    }
                } else {
                    switch (type) {
                        case 0:
                            result = "99,9999,999999";
                            break;
                        case 1:
                            result = "其他";
                            break;
                        case 2:
                            result = "其他";
                            break;
                        default:
                    }
                }

            }
        } catch (Exception e) {

        }
        return result;
    }

    private static class CertificationClass{
        private String code;
        private String level1Name;
        private String level2Name;

        public CertificationClass(String code, String level2Name,String level1Name) {
            this.code = code;
            this.level1Name = level1Name;
            this.level2Name = level2Name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getLevel1Name() {
            return level1Name;
        }

        public void setLevel1Name(String level1Name) {
            this.level1Name = level1Name;
        }

        public String getLevel2Name() {
            return level2Name;
        }

        public void setLevel2Name(String level2Name) {
            this.level2Name = level2Name;
        }
    }
//    public static void main(String[] args) {
//        String content = "医疗器械542广告批准文号";
//        String region = evaluate(content,2);
//        System.out.println(region);
//    }
}
