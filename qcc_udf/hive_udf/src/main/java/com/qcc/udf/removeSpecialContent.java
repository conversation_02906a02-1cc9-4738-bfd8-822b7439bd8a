package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

public class removeSpecialContent extends UDF {

    public static String evaluate (String param) {
        if (param == null || param.trim().length() == 0) {
            return "";
        }
        String key[] = new String[]{"无","暂无","-","暂缺","空","\"空\"","NULL","null","\"null\""};
        for (String string : key) {
            if(param.trim().equals(string)){
                return "";
            }
        }
        return param;
    }
}
