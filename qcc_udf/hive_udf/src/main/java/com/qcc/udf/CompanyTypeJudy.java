package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CompanyTypeJudy extends UDF {
    public static String evaluate(String econKind, String creditCode, String regNo) throws Exception {
        econKind = econKind == null ? "" : econKind;
        creditCode = creditCode == null ? "" : creditCode;
        regNo = regNo == null ? "" : regNo;

        String companyType = econKind;

        String econKind2 = removeBracket(removeBlank(full2Half(econKind)));
        if (econKind2.contains("有限责任公司") || econKind2.contains("有限公司")
                || econKind2.contains("独资企业") || econKind2.contains("合伙企业")
                || econKind2.contains("私营企业") || econKind2.contains("股份合作")
                || econKind2.contains("集体所有制") || econKind2.contains("全民所有制")) {
            return companyType;
        }

        if ((econKind.contains("个体") || econKind.contains("个人") || econKind.contains("家庭")) && !econKind.contains("企业")) {
            companyType = "个体工商户";
        } else if (isCreditCode(creditCode) && creditCode.startsWith("92")) {
            companyType = "个体工商户";
        } else if (isCreditCode(creditCode) && creditCode.startsWith("93")) {
            companyType = "农民专业合作经济组织";
        } else if ((econKind.contains("合作社") || econKind.contains("专业合作")) && !econKind.contains("分支机构")) {
            companyType = "农民专业合作经济组织";
        } else if (econKind == null || "".equals(econKind)
                || econKind.contains("正常")
                || (econKind.contains("其他") && !econKind.contains("其他有限责任公司") && !econKind.contains("其他股份有限公司"))
                || econKind.contains("来料加工")
                || !isPartMatch(econKind, "[\u4e00-\u9fa5]")) {
            if ((isCreditCode(creditCode) && creditCode.startsWith("92")) || (!creditCode.startsWith("91") && !creditCode.startsWith("93") && isRegNo(regNo) && regNo.substring(6, 7).compareTo("6") > 0 && !"N".equals(regNo.substring(6, 7)))) {
                companyType = "个体工商户";
            } else if ((isCreditCode(creditCode) && creditCode.startsWith("93")) || (!creditCode.startsWith("91") && !creditCode.startsWith("92") && isRegNo(regNo) && "N".equals(regNo.substring(6, 7)))) {
                companyType = "农民专业合作经济组织";
            }
        }
        return companyType;
    }

    /**
     * 判断字符串是否为全国统一信用代码
     *
     * @param value 待判断字符串
     * @return
     */
    public static Boolean isCreditCode(String value) {
        if (StringUtils.isEmpty(value) || charArrayDisticnt(value).length() < 3) {
            return false;
        }

        value = value.toUpperCase().replace(" ", "");
        if (value.length() != 18 || !isMatch(value, "^[1-9ANYG][\\d]{7}[\\dA-Z]{9,10}$")) {
            return false;
        }
        return true;
    }

    /**
     * 子串串中的char去重
     *
     * @param s
     * @return
     */
    public static String charArrayDisticnt(String s) {
        if (s == null) {
            return "";
        }
        StringBuffer sb = new StringBuffer();
        int i = 0;
        int len = s.length();
        while (i < len) {
            char c = s.charAt(i);
            sb.append(c);
            i++;
            while (i < len && s.charAt(i) == c) {//这个是如果这两个值相等，就让i+1取下一个元素
                i++;
            }
        }
        return sb.toString();
    }

    /**
     * 完全正则表达式
     *
     * @param regexStr 正则表达式
     * @param content  要查找的字符串
     * @return 匹配内容
     */
    public static boolean isMatch(String content, String regexStr) {
        Pattern p = Pattern.compile(regexStr);
        Matcher m = p.matcher(content);
        return m.matches();
    }

    /**
     * 部分匹配正则表达式
     *
     * @param regexStr 正则表达式
     * @param content  要查找的字符串
     * @return 匹配内容
     */
    public static boolean isPartMatch(String content, String regexStr) {

        Pattern p = Pattern.compile(regexStr);
        Matcher m = p.matcher(content);
        if (m.find()) {
            return true;
        }
        return false;
    }

    /**
     * 判断字符串是否为企业注册号
     *
     * @param value 待判断字符串
     * @return
     */
    public static Boolean isRegNo(String value) {
        if (StringUtils.isEmpty(value) || isCreditCode(value) || charArrayDisticnt(value).length() < 3) {
            return false;
        }

        value = value.trim();

        if (!isMatch(value, "^\\d{6}[\\dA-Z]{7,13}$")) {
            return false;
        }
        return true;
    }

    /**
     * 全角转半角
     *
     * @param input 输入
     * @return 半角文本
     */
    private static String full2Half(String input) {
        if (StringUtils.isEmpty(input)) {
            return "";
        }

        char c[] = input.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == '\u3000') {
                c[i] = ' ';
            } else if (c[i] > '\uFF00' && c[i] < '\uFF5F') {
                c[i] = (char) (c[i] - 65248);
            }
        }
        return new String(c);
    }

    /// <summary>
    /// 去掉字符串中的中英文括号
    /// </summary>
    /// <param name="source"></param>
    /// <returns></returns>
    private static String removeBracket(String source) {
        return StringUtils.isEmpty(source) ? source : source.replace("(", "").replace(")", "").replace("（", "").replace("）", "").replace("：", "").replace(":", "").trim();
    }

    /// <summary>
    /// 去掉字符串中的空格
    /// </summary>
    /// <param name="source"></param>
    /// <returns></returns>
    private static String removeBlank(String source) {
        return StringUtils.isEmpty(source) ? "" : source.replace(" ", "").replace("　", "");
    }

//    public static void main(String[] args) throws Exception {
//        String a1 = CompanyTypeJudy.evaluate("9500", "92441322MA7KAHHU2X", "441322601460976");
//        String a2 = CompanyTypeJudy.evaluate("", "92441322MA7KAHHU2X", "441322601460976");
//        String a3 = CompanyTypeJudy.evaluate("有限责任公司", "91320594088140947F", "320594000299470");
//        String a4 = CompanyTypeJudy.evaluate("有限责任公司", "93320594088140947F", "320594000299470");
//    }
}