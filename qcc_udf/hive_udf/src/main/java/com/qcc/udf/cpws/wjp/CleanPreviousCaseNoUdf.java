package com.qcc.udf.cpws.wjp;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class CleanPreviousCaseNoUdf extends UDF {
    public static final List list = Arrays.asList("执行依据文书文号", "执行依据法律文书号", "WindowTitle");

    // 正则表达式模式
    public static final String regex = "^([\\[\\{【])(.*?)([\\]\\}】]|\\+?[\\]\\}】])$";
    public static Pattern pattern = Pattern.compile(regex);

    // 年份数字的正则表达式模式
    public static final String yearRegex = "\\d{4}";
    public static Pattern yearPattern = Pattern.compile(yearRegex);

    public static String evaluate(String caseNo) {

        if (StringUtils.isNotEmpty(caseNo)) {
            LinkedList<String> filteredData = new LinkedList<>();
            String[] data = caseNo.replace("]及[", "],[").split("[,，、]");
            // 过滤并去除符号
            for (String item : data) {
                Matcher matcher = pattern.matcher(item);
                if (matcher.matches()) {
                    // 提取中间部分
                    String middlePart = matcher.group(2).trim();
                    // 检查中间部分是否包含四位数字年份且完全匹配
                    Matcher yearMatcher = yearPattern.matcher(middlePart);
                    if (!list.contains(middlePart) && (!yearMatcher.matches() || !yearMatcher.group().equals(middlePart))) {
                        middlePart = middlePart.replace("公证书编号：", "");
                        filteredData.add(middlePart);
                    }
                } else {
                    filteredData.add(item);
                }
            }
            List<String> stringList = filteredData.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            return String.join(",", stringList);
        }
        return caseNo;
    }


    public static void main(String[] args) {
        List<String> asList = Arrays.asList("【（2018）川0105民初10495号】",
                "{（南）安监罚[2018]299号}",
                "【三农(林)罚决字[2016]第6号】",
                "【（2018）川0105民初10495号】",
                "【2018鲁0402民初1942号】",
                "【武劳人仲东办裁字[2023]168号】",
                "【（2018）川0105民初10495号】",
                "【(2023)鲁0113民初4907号】",
                "【(2022)鲁1423民初826号】",
                "{(2017)川1302民初5681号}",
                "【（2020）粤0111民初17465号】",
                "【(2022)鲁1522民初2762号】",
                "[杨人社监（2015）理字第00208-1号]",
                "【(2017)川0105民初8193号】",
                "【（2016)粤1302民初3481号】",
                "【胶劳人仲案字(2022）第1634号】",
                "【（2020）粤0111民初17465号】",
                "[（2021）粤0604民初1659号]",
                "{(2017)川1302民初5685号}",
                "【2013】辽证执【23】",
                "【（2020）粤0111民初17465号】",
                "{(2017)川1302民初5687号}",
                "[（2022）粤1973民初5040号]",
                "{(2017)川1302民初5681号}",
                "{(南)安监罚[2018]45号}",
                "[(2017)鲁1626民初4304号]",
                "[杨人社监（2016）理字第00126号]",
                "【(2020)鲁04民终3430号】、【(2020)鲁0402民初1467号】",
                "【镇劳仲案字[2018]第01号裁决书】",
                "【甘劳人仲案字(2020)第270号】",
                "{(2017)川1302民初5677号}",
                "[杨人社监（2016）理字第00102-2号]",
                "{（南）安监罚[2018]41号}",
                "【(2017)南证执字第004号】",
                "【(2017)川0105民初8193号】",
                "【(2020)鲁04民终3430号】、【(2020)鲁0402民初1467号】",
                "[（2018）北国仲字第32526号]",
                "{(2017)川1302民初5687号}",
                "【(2017)川0105民初8193号】",
                "{(2017)川1302民初5677号}",
                "【胶劳人仲案字(2022）第1634号】",
                "【2018鲁0402民初1942号】",
                "【执行依据法律文书号】",
                "【（2018）川0105民初10495号】",
                "{(2017)川1302民初5685号}",
                "{(2017)川1302民初5677号}",
                "[(2022)鲁1622民初135号]",
                "【（2016)粤1302民初3481号】",
                "【〔2021〕中国贸仲京裁字第0618号】",
                "【(2022）鲁0402民初128号】",
                "[杨人社监（2016）理字第00103号]",
                "【（2019）苏0681民初3928号】",
                "【2018鲁0402民初1942号】",
                "【执行依据文书文号】",
                "{(2017)川1302民初5681号}",
                "【2019中国贸仲京裁字第1058号】",
                "[(2012)西莲证经字第1937号]",
                "【(2017)川0105民初8193号】",
                "【〔2021〕中国贸仲京裁字第0618号】",
                "【(2020)鲁04民终3430号】、【(2020)鲁0402民初1467号】",
                "【武劳人仲东办裁字 [2023] 154号】",
                "{(2017)川1302民初5687号}",
                "【(2019)鲁0613民初1457号】",
                "【北劳人仲案字[2020]第620号】",
                "[杨人社监（2016）理字第00150号]",
                "【执行依据法律文书号】",
                "【执行依据法律文书号】",
                "【（2020）川0107民初135号】",
                "【(2023)鲁0112民初823号】",
                "【(2023)鲁1503民初784号】",
                "[(2017)鲁1626民初4304号]",
                "【(2023)鲁0323民初4172号】",
                "[(2017)鲁1626民初4304号]"

        );
        for (String o : asList) {
            System.out.println(evaluate(o));

        }
    }


}