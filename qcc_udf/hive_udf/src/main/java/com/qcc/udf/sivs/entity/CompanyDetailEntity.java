package com.qcc.udf.sivs.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class CompanyDetailEntity {
    // 公司名
    private String name;
    // KeyNo
    private String keyNo;
    // 是否过期
    private String isExpire;
    // 关联的机构列表信息
    private String investList;
    // 注册日期时间戳
    private Long startDate;
    // 关联的产品信息
    private String product;
    // 公司状态
    private String shortStatus;
    // 关联的标签信息（只要找是否存在私募基金标签）
    private String tags;
}
