package com.qcc.udf.casesearch_v3.entity.input;

import com.alibaba.fastjson.JSON;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.casesearch_v3.enums.CaseCategoryEnum;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import com.qcc.udf.cpws.casesearch_v2.AnnoStandard;
import com.qcc.udf.getAnoFromContent;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import com.google.common.base.Strings;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: zhangqiang
 * @Date: 2020/11/11 17:54
 * @Description:失信
 */
@Data
public class SXEntity  extends BaseCaseEntity {

    //ToJsonUDF(named_struct('id',tbl.id,'anno',tbl.anno,'companynames',tbl.companynames,'isvalid',tbl.isvalid,'courtname',tbl.courtname,'provincecode',nvl(tbl.province, ''),'nameandkeyno',tbl.nameandkeyno,'liandate',tbl.liandate,'publicdate',tbl.publicdate, 'executeno', nvl(tbl.executeno, ''),'executestatus',nvl(tbl.executestatus,''),'actiontype',nvl(tbl.actiontypename,''))) end as sxinfo,

    private String id;
    private String anno;
    private String companynames;
    private String isvalid;
    private String courtname;
    private String provincecode;

    private String nameandkeyno;

    private long liandate;
    private long publicdate;

    private String executeno;
    private String executestatus;
    private String actiontype;

    private String yiwu;
    private List<NameAndKeyNoEntity> nameandkeynoEntityList;

    public static List<SXEntity> convert(List<String> jsonList) {
        List<SXEntity> list = new ArrayList<>();
        SXEntity entity = null;
        if(CollectionUtils.isEmpty(jsonList)){
            return list;
        }
        getAnoFromContent getAnoFromContent = new getAnoFromContent();

        for (String json : jsonList) {
            if(Strings.isNullOrEmpty(json)){
                continue;
            }
            entity = JSON.parseObject(json, SXEntity.class);
            if(entity == null  || Strings.isNullOrEmpty(entity.getId())){
                continue;
            }
            String str = entity.getNameandkeyno();

            if (!Strings.isNullOrEmpty(str)) {
                entity.setNameandkeynoEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getNameandkeynoEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                }
            }

            //公共字段赋值
            entity.setBaseCaseNo(entity.getAnno());
            entity.setBaseCaseCategoryEnum(CaseCategoryEnum.SX);
            if(!Strings.isNullOrEmpty(entity.getCompanynames())){
                entity.setBaseSearchWordSet(Arrays.stream(entity.getCompanynames().split(","))
                        .collect(Collectors.toSet()));
            }

            entity.setBaseCourt(entity.getCourtname());
            entity.setBaseProvinceCode(entity.getProvincecode());
            entity.setBaseNameKeyNoList(entity.getNameandkeynoEntityList());
            entity.setBaseId(entity.getBaseCaseCategoryEnum().getType()+"_"+entity.getId());
            String yiwuAnno = getAnoFromContent.evaluate(entity.getYiwu());
            Set<String> baseBeforeNoSet =new HashSet<>();
            baseBeforeNoSet.addAll(CommonV3Util.getAllCaseNo(yiwuAnno));
            baseBeforeNoSet.addAll(CommonV3Util.getAllCaseNo(getAnoFromContent.evaluate(entity.getExecuteno())));
            entity.setBaseBeforeNoSet(baseBeforeNoSet);

            String caseType= CommonV3Util.getCaseType(CommonV3Util.getCaseNo(entity.getBaseCaseNo()));
            //案件类型为空的数据直接过滤
            if(Strings.isNullOrEmpty(caseType)){
                continue;
            }
            list.add(entity);
        }
        return list;
    }
}
