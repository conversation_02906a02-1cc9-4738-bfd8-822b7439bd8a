package com.qcc.udf.cpws.wjp;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

public class PoChanCodeUdf extends UDF {
    public static String getCaseTypeCode(String caseType) {
        String code = "other";
        if (StringUtils.isNotEmpty(caseType)) {
            switch (caseType) {
                case "破产审查案件":
                    code = "pcsc";
                    break;
                case "破产案件":
                    code = "pcaj";
                    break;
                case "破产上诉案件":
                    code = "pcss";
                    break;
                case "破产监督案件":
                    code = "pcjd";
                    break;
                case "强制清算申请审查案件":
                    code = "qssq";
                    break;
                case "强制清算案件":
                    code = "qzqs";
                    break;
                case "强制清算上诉案件":
                    code = "qsss";
                    break;
                case "强制清算监督案件":
                    code = "qsjd";
                    break;
                default:
                    code = "other";
                    break;
            }

        }
        return code;
    }

    public static String evaluate(String caseNo) {
        return getCaseTypeCode(caseNo);
    }

}
