package com.qcc.udf.cpws.wjp;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2022/12/8 22:10
 **/
public class CaseFeeUtil extends UDF {
    // 使用正则表达式匹配受理费的模式，支持阿拉伯数字和汉字
    // 按照顺序执行，命中则返回
    public static List<String> regexes = Arrays.asList(

            "受理费(?:人民币|用|为|收取)?(([一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾]+)|([0-9,.，]+))元.*?(减半收取|减半计|减半收)(.*?)(([一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾]+)|([0-9,，.]+))元",
            "受理费(?:用|为|减半收取|减半收取为|减半收取计|人民币|减半交纳|减半缴纳|收取|减半收取即|减半计|减半收|已减半收取)?([0-9一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾,，.]+)元",
            "减半.*?受理费([0-9一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾,，.]+)元",
            "本案.*?受理费([0-9一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾,，.]+)元.*?已减半",
            "本案二审受理费([0-9一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾,，.]+)元",
            "管辖权异议受理费([0-9一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾,，.]+)元",
            "受理费(([一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾]+)|([0-9,，.]+))元"
    );
    public static List<String> yiShenFee = Arrays.asList(
            "(一审|原审)(?:案件)?(受理费|案件受理费)(?:人民币|用|为|收取)?(([一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾]+)|([0-9,，.]+))元.*?(减半收取|减半计|减半收)(.*?)(([一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾]+)|([0-9,，.]+))元",
            "(一审|原审)(?:案件)?(受理费|案件受理费)(?:人民币|用|为|收取)?(([一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾]+)|([0-9,，.]+))元",
            "(一审|原审)(?:案件)?受理费减半收取(([一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾]+)|([0-9,，.]+))元");

    public static List<String> erShenFee = Arrays.asList(
            "(二审|终审)(?:案件)?(受理费|案件受理费|受理费减半收取)(?:人民币|用|为|收取)?(([一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾]+)|([0-9,，.]+))元.*?(减半收取|减半收取|减半计|减半收)(.*?)(([一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾]+)|([0-9,，.]+))元",
            "(二审|终审)(?:案件)?(受理费|案件受理费|受理费减半收取)(?:人民币|用|为|收取)?(([一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾]+)|([0-9,，.]+))元",
            "(二审|终审)(?:案件)?受理费减半收取(?:人民币)?(([一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾]+)|([0-9,，.]+))元");

    public static List<String> bensuFee = Arrays.asList(
            "本诉(受理费|案件受理费|受理费减半收取)(?:人民币|用|为|收取)?(([一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾]+)|([0-9,，.]+))元.*?(减半收取|减半收取|减半计|减半收)(.*?)(([一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾]+)|([0-9,，.]+))元",
            "本诉(受理费|案件受理费|受理费减半收取)(?:人民币|用|为|收取)?(([一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾]+)|([0-9,，.]+))元",
            "本诉受理费减半收取(([一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾]+)|([0-9,，.]+))元");

    public static List<String> fansuFee = Arrays.asList(
            "反诉(受理费|案件受理费|受理费减半收取)(?:人民币|用|为|收取)?(([一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾]+)|([0-9,，.]+))元.*?(减半收取|减半收取|减半计|减半收)(.*?)(([一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾]+)|([0-9,，.]+))元",
            "反诉(受理费|案件受理费|受理费减半收取)(?:人民币|用|为|收取)?(([一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾]+)|([0-9,，.]+))元",
            "反诉受理费减半收取(([一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾]+)|([0-9,，.]+))元");

    public static String evaluate(String content) {
        if (StringUtils.isNotEmpty(content)) {
            return extractAcceptanceFee(content);
        }
        return null;
    }

    /**
     * 当前的受理费，可以分为二审受理费、一审受理费、反诉受理费、本诉受理费、其他受理费（不能按前面4类分类）
     *
     * @param text
     * @return
     */
    public static String extractAcceptanceFee(String text) {
        JSONObject feeJson = new JSONObject();
        if (StringUtils.isNotEmpty(text) && text.contains("受理费")) {
            String totalTemp = "";
            // 非空费用计数
            int notNullCount = 0;
            // 有限提取一审二审
            String first = "";
            for (String regex : yiShenFee) {
                Matcher matcher = Pattern.compile(regex).matcher(text);
                if (matcher.find() && isValidMatch(text, matcher.start(), matcher.end())) {
                    first = safeGroup(matcher);
                    // 提取一次后移除，后续不在提取
                    text = text.replace(printSentence(text, matcher.start(), matcher.end()), "");
                    notNullCount++;
                    totalTemp = first;
                    break;
                }
            }
            String sec = "";
            for (String regex : erShenFee) {
                Matcher matcher = Pattern.compile(regex).matcher(text);
                if (matcher.find() && isValidMatch(text, matcher.start(), matcher.end())) {
                    sec = safeGroup(matcher);
                    // 提取一次后移除，后续不在提取
                    text = text.replace(printSentence(text, matcher.start(), matcher.end()), "");
                    notNullCount++;
                    totalTemp = sec;
                    break;
                }
            }
            String ben = "";
            for (String regex : bensuFee) {
                Matcher matcher = Pattern.compile(regex).matcher(text);
                if (matcher.find() && isValidMatch(text, matcher.start(), matcher.end())) {
                    ben = safeGroup(matcher);
                    // 提取一次后移除，后续不在提取
                    text = text.replace(printSentence(text, matcher.start(), matcher.end()), "");
                    notNullCount++;
                    totalTemp = ben;
                    break;
                }
            }
            String fan = "";
            for (String regex : fansuFee) {
                Matcher matcher = Pattern.compile(regex).matcher(text);
                if (matcher.find() && isValidMatch(text, matcher.start(), matcher.end())) {
                    fan = safeGroup(matcher);
                    ;
                    // 提取一次后移除，后续不在提取
                    text = text.replace(printSentence(text, matcher.start(), matcher.end()), "");
                    notNullCount++;
                    totalTemp = fan;

                    break;
                }
            }
            String other = "";
            String total = "";
            if (StringUtils.isNotEmpty(first) || StringUtils.isNotEmpty(sec) || StringUtils.isNotEmpty(ben) || StringUtils.isNotEmpty(fan)) {
                for (String regex : regexes) {
                    Matcher matcher = Pattern.compile(regex).matcher(text);
                    if (matcher.find() && isValidMatch(text, matcher.start(), matcher.end())) {
                        String fee = safeGroup(matcher);
                        // 一句话中如果包含关键句型则跳过
                        String sentence = printSentence(text, matcher.start(), matcher.end());
                        text = text.replace(sentence, "");
                        if (!sentence.matches(".*(交纳|预缴|预交|负担).*受理费(?:人民币|用|为)?(([一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾]+)|([0-9,.]+))元")) {
                            // 受理费和数字元之间最多一个逗号

                            if (fee.chars().filter(ch -> ch == ',').count() <= 1) {
                                other = fee;
                                notNullCount++;
                                totalTemp = other;
                                break;
                            }
                        }
                    }
                }
            } else {
                for (String regex : regexes) {
                    Matcher matcher = Pattern.compile(regex).matcher(text);
                    if (matcher.find() && isValidMatch(text, matcher.start(), matcher.end())) {
                        String fee = safeGroup(matcher);
                        // 一句话中如果包含关键句型则跳过
                        String sentence = printSentence(text, matcher.start(), matcher.end());
                        text = text.replace(sentence, "");

                        if (!sentence.matches(".*(交纳|预缴|预交|负担).*受理费(?:人民币|用|为)?(([一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾]+)|([0-9,.]+))元")) {
                            // 受理费和数字元之间最多一个逗号
                            try {
                                if (fee.chars().filter(ch -> ch == ',').count() <= 1) {
                                    total = fee;
                                    other = fee;
                                    notNullCount++;
                                    totalTemp = total;
                                    break;
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                        }
                    }
                }
            }
            // 只有一个有值
            BigDecimal f = UrgeChineseAmountUtil.formatAmount(first);
            BigDecimal second = UrgeChineseAmountUtil.formatAmount(sec);
            BigDecimal counter = UrgeChineseAmountUtil.formatAmount(fan);
            BigDecimal claim = UrgeChineseAmountUtil.formatAmount(ben);
            BigDecimal others = UrgeChineseAmountUtil.formatAmount(other);

            feeJson.put("first", f);
            feeJson.put("second", second);
            feeJson.put("counter", counter);
            feeJson.put("claim", claim);
            feeJson.put("others", others);
            BigDecimal totalDeciaml = null;

            if (notNullCount == 1) {
                totalDeciaml = UrgeChineseAmountUtil.formatAmount(totalTemp);
            } else if (f != null && second != null) {
                //同一条数据，判决结果段落，既有一审受理费又有二审受理费，只取二审受理费
                totalDeciaml = second;
            } else if (counter != null && claim != null) {
                //同一条数据，判决结果段落，既有本诉受理费又有反诉受理费，要把两者累计求和
                totalDeciaml = counter.add(claim);
            } else if (notNullCount > 2) {
                totalDeciaml = null;
            } else if (others != null && (f != null || second != null || counter != null || claim != null)) {
                totalDeciaml = null;
            }

            feeJson.put("total", totalDeciaml);
            return feeJson.toJSONString();
        }
        return null;
    }

    /**
     * 打印匹配到的句子，返回完整句子
     *
     * @param text
     * @param start
     * @param end
     */
    public static String printSentence(String text, int start, int end) {
        int sentenceStart = Math.max(text.lastIndexOf('。', start), text.lastIndexOf('，', start)) + 1;
        int sentenceEnd = Math.min(
                (text.indexOf('。', end) == -1 ? text.length() : text.indexOf('。', end)),
                (text.indexOf('，', end) == -1 ? text.length() : text.indexOf('，', end))
        );
        return text.substring(sentenceStart, sentenceEnd).trim();
    }

    /**
     * 检查匹配的部分中是否最多包含一个逗号，且不包含其他符号
     *
     * @param text
     * @param start
     * @param end
     * @return
     */
    public static boolean isValidMatch(String text, int start, int end) {
        String match = text.substring(start, end);
        Matcher matcher = Pattern.compile("减半收取(.*?)(([一二三四五六七八九十百千万壹贰叁肆伍陆柒捌玖拾]+)|([0-9,，.]+))元").matcher(match);
        if (matcher.find()) {
            match = match.substring(matcher.start(), matcher.end());
        }
        long commaCount = match.chars().filter(ch -> ch == ',').count();
//        boolean hasOtherSymbols = match.matches(".*[。！？；:：；‘’“”\\[\\]{}<>【】『』].*");
        return commaCount <= 1;
    }

    /**
     * 安全地获取Matcher的group，避免空指针异常
     *
     * @param matcher
     * @return
     */
    public static String safeGroup(Matcher matcher) {
        try {
            int group = matcher.groupCount();
            String fee = "";
            while (group > 0) {
                fee = matcher.group(group);
                if (StringUtils.isEmpty(fee) && group > 1) {
                    group--;
                } else {
                    return fee;
                }
            }
            return fee;
        } catch (IllegalStateException | IndexOutOfBoundsException e) {
            e.printStackTrace();
            return null;
        }

    }


    public static void main(String[] args) {
        List<String> str = Arrays.asList(
                "一、驳回原告北京邓峰博雅文化传播有限公司的诉讼请求。 二、驳回被告内蒙古会展经济科学发展研究会反诉请求。 案件本诉受理费1636元，由北京邓峰博雅文化传播有限公司全部负担。反诉费受理费3320万元，由内蒙古会展经济科学发展研究会全部负担。 如不服本判决，可在判决书送达之日起十五日内向本院递交上诉状，并按对方当事人人数提出副本，上诉于呼和浩特市中级人民法院。");
        for (String t10 : str) {
            System.out.println(t10 + "\r\n" + extractAcceptanceFee(t10));

        }

    }

}
