package com.qcc.udf.group;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.List;

/**
 * <AUTHOR>
 */
public class GetOriginalValue extends UDF {
//    public static void main(String[] args) {
//        String result = evaluate("{\"KeyNo\":null,\"CompanyName\":null,\"UpdateDate\":{\"$numberLong\":\"0\"},\"TotalCount\":0,\"ChangeList\":null}\n" +
//                "{\n" +
//                "\t\"K\": \"FDAFDAFD\",\n" +
//                "\t\"V\": \"{\\\"KeyNo\\\":null,\\\"CompanyName\\\":null,\\\"UpdateDate\\\":{\\\"$numberLong\\\":NULL},\\\"TotalCount\\\":0,\\\"ChangeList\\\":null}\"\n" +
//                "}\n" +
//                "{\n" +
//                "    \"K\":\"FDAFDAFD\",\n" +
//                "    \"V\":\"{\\\"K\\\":\\\"FDAFDAFD\\\",\\\"V\\\":\\\"{\\\\\\\"KeyNo\\\\\\\":null,\\\\\\\"CompanyName\\\\\\\":null,\\\\\\\"UpdateDate\\\\\\\":{\\\\\\\"$numberLong\\\\\\\":\\\\\\\"0\\\\\\\"},\\\\\\\"TotalCount\\\\\\\":0,\\\\\\\"ChangeList\\\\\\\":null}\\\"}\"\n" +
//                "}");
//        System.out.printf(result);
//    }

    public static String evaluate(String input) {
        String output = input;
        try {
            if (StringUtils.isNotBlank(output)) {
                String numberLongRegex = "\\{(\\\\)*\"\\$numberLong(\\\\)*\":(((\\\\)*\"[0-9]+(\\\\)*\")|null|NULL)\\}";
                if (RegexHelper.isFind(output, numberLongRegex)) {
                    List<String> regexList = RegexHelper.getGlobalRegex(numberLongRegex, output);
                    if (CollectionUtils.isNotEmpty(regexList)) {
                        for (String source : regexList) {
                            String target = RegexHelper.getRegexValue("(?<key>(([0-9]+)|null|NULL))", source);
                            output = output.replace(source, target);
                        }
                    }
                }
            }
        } catch (Exception e) {
        }
        return output;
    }

/*
TEST STRING
{"KeyNo":null,"CompanyName":null,"UpdateDate":{"$numberLong":"0"},"TotalCount":0,"ChangeList":null}
{
	"K": "FDAFDAFD",
	"V": "{\"KeyNo\":null,\"CompanyName\":null,\"UpdateDate\":{\"$numberLong\":NULL},\"TotalCount\":0,\"ChangeList\":null}"
}
{
    "K":"FDAFDAFD",
    "V":"{\"K\":\"FDAFDAFD\",\"V\":\"{\\\"KeyNo\\\":null,\\\"CompanyName\\\":null,\\\"UpdateDate\\\":{\\\"$numberLong\\\":\\\"0\\\"},\\\"TotalCount\\\":0,\\\"ChangeList\\\":null}\"}"
}
*/
}