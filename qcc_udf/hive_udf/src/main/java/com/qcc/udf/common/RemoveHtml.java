package com.qcc.udf.common;

import org.apache.hadoop.hive.ql.exec.UDF;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;


public class RemoveHtml extends UDF {


    public static String evaluate(String textString) {
        return getTextFromTHML(textString);
    }

    public static String getTextFromTHML(String html) {
        Document document = Jsoup.parse(html);
        document.select("br").append("\\n");
        document.select("p").prepend("\\n\\n");
        return document.text().replace("\\n\\n", "\n");
    }

}
