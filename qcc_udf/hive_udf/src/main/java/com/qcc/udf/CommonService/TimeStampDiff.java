package com.qcc.udf.CommonService;

import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

@Description(name = "TimeStampDiff", value = "_FUNC_(String  unit,String startDate, String endDate);" +
        "unit:Year、Month、Day、Hour、Minute、Second; - Return Long")
public class TimeStampDiff extends UDF {

    public static Long evaluate(String unit, String startDate, String endDate) {

        try {
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date start = df.parse(startDate);
            Date end = df.parse(endDate);
            Calendar start1 = Calendar.getInstance();
            start1.setTime(start);
            Calendar end1 = Calendar.getInstance();
            end1.setTime(end);
            long diffTime = end.getTime() - start.getTime();
            Long result = 0L;
            switch (unit) {
                case "Year":
                    Integer a = end1.get(Calendar.YEAR) - start1.get(Calendar.YEAR);
                    result = a.longValue();
                    break;
                case "Month":
                    Integer month = end1.get(Calendar.MONTH) - start1.get(Calendar.MONTH);
                    result = month.longValue() + end1.get(Calendar.YEAR) * 12 - start1.get(Calendar.YEAR) * 12;
                    break;
                case "Day":
                    result = diffTime / (24 * 60 * 60 * 1000);
                    break;
                case "Hour":
                    result = diffTime / (60 * 60 * 1000);
                    break;
                case "Minute":
                    result = diffTime / (60 * 1000);
                    break;
                case "Second":
                    result = diffTime / 1000;
                    break;
                default:
                    break;
            }
            System.out.println(result);
            return result;
        } catch (Exception e) {
            //e.printStackTrace();
            return null;
        }
    }


//    public static void main(String[] args) {
//        evaluate("Year", "2020-08-01 00:00:00", "2021-03-03 04:00:00");
//    }
}
