package com.qcc.udf;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.apache.hadoop.hive.ql.exec.UDF;
import org.json.JSONException;

import java.io.IOException;
import java.net.URISyntaxException;


public class UDFChineseToPinYin extends UDF {

    public static String evaluate(String chinese) {

        if (chinese == null) {

            return null;

        }

        return ConvertToPinyin(chinese);

    }


    public static String ConvertToPinyin(String name) {

        HanyuPinyinOutputFormat pyFormat = new HanyuPinyinOutputFormat();

        pyFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE);

        pyFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);

        pyFormat.setVCharType(HanyuPinyinVCharType.WITH_V);

        String result = null;

        try {

            result = PinyinHelper.toHanyuPinyinString(name, pyFormat, "");

        } catch (BadHanyuPinyinOutputFormatCombination e) {

            return null;

        }

        return result;

    }

    public static void main(String[] args) throws IOException, URISyntaxException, JSONException {
        System.out.println(evaluate("剥皮"));
    }

}