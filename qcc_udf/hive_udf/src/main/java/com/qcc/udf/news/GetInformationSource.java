package com.qcc.udf.news;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.StringJoiner;
import java.util.stream.Collectors;

@Description(name = "GetInformationSource", value = "_FUNC_(String  information_source);arguments information_source; - Return 以type_id升序，逗号拼接的level_name")
public class GetInformationSource extends UDF {


    /**
     * @param information_source
     * @return
     * @throws Exception
     */
    public static String evaluate(String information_source) {
        try {
            if (StringUtils.isBlank(information_source)) return ",,";
            JSONArray jsonArray = JSON.parseArray(information_source);
            StringJoiner sj = new StringJoiner(",");
            for (int i = 1; i <= 3; i++) {
                int finalI = i;
                List<String> result = jsonArray.stream().filter(a -> JSON.parseObject(a.toString()).getIntValue("type_id") == finalI)
                        .map(a -> JSON.parseObject(a.toString()).getString("level_name"))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(result)) {
                    sj.add("");
                } else {
                    sj.add(result.get(0));
                }
            }

//            return String.valueOf(result.get().offset());
            return sj.toString();
        } catch (Exception ex) {
            return ",,";
        }
    }


    public static void main(String[] args) {
        String aa = "[{\"type_level_max\":1,\"level\":[\"3873\"],\"level_id\":\"3873\",\"type_name\":\"媒体等级\",\"type_id\":2,\"level_name\":\"C级\",\"level_num\":1}]";
        System.out.println(evaluate(aa));
    }
}