package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 获取企业类型Code
 *
 * @Auther: wangbin
 * @Date: 2019/6/25 11:30
 * @Description:
 */
public class GetEconKindCode extends UDF {

    public static String evaluate(String econKind) throws Exception {
        String econKindCode = "0";
        if (EconKindCollect(econKind) == "有限责任公司") {
            return econKindCode = "10";
        } else if (EconKindCollect(econKind) == "股份有限公司") {
            return econKindCode = "20";
        } else if (EconKindCollect(econKind) == "国企") {
            return econKindCode = "30";
        } else if (EconKindCollect(econKind) == "外商投资企业") {
            return econKindCode = "40";
        } else if (EconKindCollect(econKind) == "独资企业") {
            return econKindCode = "50";
        } else if (EconKindCollect(econKind) == "合伙制企业") {
            return econKindCode = "60";
        } else if (EconKindCollect(econKind) == "个体工商户") {
            return econKindCode = "70";
        } else if (EconKindCollect(econKind) == "联营企业") {
            return econKindCode = "80";
        } else if (EconKindCollect(econKind) == "集体所有制") {
            return econKindCode = "90";
        } else if (EconKindCollect(econKind) == "有限合伙") {
            return econKindCode = "100";
        } else if (EconKindCollect(econKind) == "普通合伙") {
            return econKindCode = "110";
        } else {
            return econKindCode = "0";
        }
    }

    public static String EconKindCollect(String econKind) {
        econKind = econKind == null ? "" : econKind;
        String enterpriseType = econKind;
        //正则表达式
        String regEx = ".*[非]+.*[独资]";
        Pattern pattern = Pattern.compile(regEx);
        Matcher matcher = pattern.matcher(enterpriseType);

        if ((econKind.contains("个体") || econKind.contains("个人") || econKind.contains("家庭"))
                && !econKind.contains("企业")) {
            enterpriseType = "个体工商户";
        } else if (enterpriseType.contains("有限责任公司")) {
            enterpriseType = "有限责任公司";
        } else if (enterpriseType.contains("股份有限公司")) {
            enterpriseType = "股份有限公司";
        } else if (enterpriseType.contains("国有") && !enterpriseType.contains("非国有")) {
            enterpriseType = "国企";
        } else if (enterpriseType.contains("中外合作") || enterpriseType.contains("中外合资") || enterpriseType.contains("外国") || enterpriseType.contains("外商")) {
            enterpriseType = "外商投资企业";
        } else if ((enterpriseType.contains("独资") && !matcher.matches()) || enterpriseType.contains("一人有限责任公司")) {
            enterpriseType = "独资企业";
        } else if (enterpriseType.contains("有限合伙") || enterpriseType.contains("普通合伙") || enterpriseType.contains("合伙企业")) {
            enterpriseType = "合伙制企业";
        } else if (enterpriseType.contains("有限合伙")) {
            enterpriseType = "有限合伙";
        } else if (enterpriseType.contains("普通合伙")) {
            enterpriseType = "普通合伙";
        } else if (enterpriseType.contains("联营") && !enterpriseType.contains("非联营")) {
            enterpriseType = "联营企业";
        } else if (enterpriseType.contains("集体") && !enterpriseType.contains("非集体") && !enterpriseType.contains("国有与集体企业联营")
                && !enterpriseType.contains("集体与股份联营") && !enterpriseType.contains("集体与私营联营") && !enterpriseType.contains("集体与中外合资联营") && !enterpriseType.contains("全民与集体联营")) {
            enterpriseType = "集体所有制";
        } else {
            enterpriseType = "其他";
        }
        return enterpriseType;
    }


       /* public static void main(String[] args){
        String econKind = "有限责任公司(自然人投资或控股)";
        try {
            String rst = evaluate(econKind);
            System.out.println(rst);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }*/
}
