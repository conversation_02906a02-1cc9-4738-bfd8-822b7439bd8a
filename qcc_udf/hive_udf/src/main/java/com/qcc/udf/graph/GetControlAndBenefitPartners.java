package com.qcc.udf.graph;

import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import com.qcc.udf.kzz.RegexHelper;
import com.qcc.udf.temp.MD5Util;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class GetControlAndBenefitPartners extends UDF {
//    public static void main(String[] args) {
//        String msg ="{\"KeyNo\":\"a37075532403f78cf62047203f78e596\",\"CompanyName\":\"厦门明绿搁浅电子商务有限公司\",\"Names\":[{\"KeyNo\":\"01cd398dd4d063475fe06037f3623bcf\",\"Name\":\"厦门志统生物科技有限公司\",\"Org\":\"0\",\"HasImage\":false,\"Percent\":\"\",\"PercentTotal\":\"99%\",\"Level\":\"1\",\"Paths\":[[{\"KeyNo\":\"01cd398dd4d063475fe06037f3623bcf\",\"Name\":\"厦门志统生物科技有限公司\",\"Org\":\"0\",\"HasImage\":false,\"Percent\":\"99%\",\"PercentTotal\":\"99%\",\"Level\":1}]]},{\"KeyNo\":\"1bbd680572753d9c7a04b07208f9f3dc\",\"Name\":\"厦门金穗子财务管理集团有限公司\",\"Org\":\"0\",\"HasImage\":false,\"Percent\":\"\",\"PercentTotal\":\"40.902%\",\"Level\":\"2,3\",\"Paths\":[[{\"KeyNo\":\"01cd398dd4d063475fe06037f3623bcf\",\"Name\":\"厦门志统生物科技有限公司\",\"Org\":\"0\",\"HasImage\":false,\"Percent\":\"99%\",\"PercentTotal\":\"99%\",\"Level\":1},{\"KeyNo\":\"f6438261bd005adb5bd51015269c9201\",\"Name\":\"厦门志越财税科技有限公司\",\"Org\":\"0\",\"DataType\":\"0\",\"HasImage\":false,\"Percent\":\"80%\",\"PercentTotal\":\"79.2%\",\"Level\":\"2\"},{\"KeyNo\":\"1bbd680572753d9c7a04b07208f9f3dc\",\"Name\":\"厦门金穗子财务管理集团有限公司\",\"Org\":\"0\",\"DataType\":\"0\",\"HasImage\":false,\"Percent\":\"51%\",\"PercentTotal\":\"40.392%\",\"Level\":\"3\"}],[{\"KeyNo\":\"d12c475152a28b678792c0a1903bc9ed\",\"Name\":\"厦门志赢专利代理有限公司\",\"Org\":\"0\",\"HasImage\":false,\"Percent\":\"1%\",\"PercentTotal\":\"1%\",\"Level\":1},{\"KeyNo\":\"1bbd680572753d9c7a04b07208f9f3dc\",\"Name\":\"厦门金穗子财务管理集团有限公司\",\"Org\":\"0\",\"DataType\":\"0\",\"HasImage\":false,\"Percent\":\"51%\",\"PercentTotal\":\"0.51%\",\"Level\":\"2\"}]]},{\"KeyNo\":\"d12c475152a28b678792c0a1903bc9ed\",\"Name\":\"厦门志赢专利代理有限公司\",\"Org\":\"0\",\"HasImage\":false,\"Percent\":\"\",\"PercentTotal\":\"1%\",\"Level\":\"1\",\"Paths\":[[{\"KeyNo\":\"d12c475152a28b678792c0a1903bc9ed\",\"Name\":\"厦门志赢专利代理有限公司\",\"Org\":\"0\",\"HasImage\":false,\"Percent\":\"1%\",\"PercentTotal\":\"1%\",\"Level\":1}]]},{\"KeyNo\":\"f6438261bd005adb5bd51015269c9201\",\"Name\":\"厦门志越财税科技有限公司\",\"Org\":\"0\",\"HasImage\":false,\"Percent\":\"\",\"PercentTotal\":\"79.2%\",\"Level\":\"2\",\"Paths\":[[{\"KeyNo\":\"01cd398dd4d063475fe06037f3623bcf\",\"Name\":\"厦门志统生物科技有限公司\",\"Org\":\"0\",\"HasImage\":false,\"Percent\":\"99%\",\"PercentTotal\":\"99%\",\"Level\":1},{\"KeyNo\":\"f6438261bd005adb5bd51015269c9201\",\"Name\":\"厦门志越财税科技有限公司\",\"Org\":\"0\",\"DataType\":\"0\",\"HasImage\":false,\"Percent\":\"80%\",\"PercentTotal\":\"79.2%\",\"Level\":\"2\"}]]},{\"KeyNo\":\"pr0355072b71b257d7c57faf2e008bfc\",\"Name\":\"龚坤\",\"Org\":\"2\",\"HasImage\":false,\"Percent\":\"\",\"PercentTotal\":\"47.4784%\",\"Level\":\"2,3,3,4\",\"Paths\":[[{\"KeyNo\":\"01cd398dd4d063475fe06037f3623bcf\",\"Name\":\"厦门志统生物科技有限公司\",\"Org\":\"0\",\"HasImage\":false,\"Percent\":\"99%\",\"PercentTotal\":\"99%\",\"Level\":1},{\"KeyNo\":\"f6438261bd005adb5bd51015269c9201\",\"Name\":\"厦门志越财税科技有限公司\",\"Org\":\"0\",\"DataType\":\"0\",\"HasImage\":false,\"Percent\":\"80%\",\"PercentTotal\":\"79.2%\",\"Level\":\"2\"},{\"KeyNo\":\"1bbd680572753d9c7a04b07208f9f3dc\",\"Name\":\"厦门金穗子财务管理集团有限公司\",\"Org\":\"0\",\"DataType\":\"0\",\"HasImage\":false,\"Percent\":\"51%\",\"PercentTotal\":\"40.392%\",\"Level\":\"3\"},{\"KeyNo\":\"pr0355072b71b257d7c57faf2e008bfc\",\"Name\":\"龚坤\",\"Org\":\"2\",\"DataType\":\"0\",\"HasImage\":false,\"Percent\":\"20%\",\"PercentTotal\":\"8.0784%\",\"Level\":\"4\"}],[{\"KeyNo\":\"01cd398dd4d063475fe06037f3623bcf\",\"Name\":\"厦门志统生物科技有限公司\",\"Org\":\"0\",\"HasImage\":false,\"Percent\":\"99%\",\"PercentTotal\":\"99%\",\"Level\":1},{\"KeyNo\":\"f6438261bd005adb5bd51015269c9201\",\"Name\":\"厦门志越财税科技有限公司\",\"Org\":\"0\",\"DataType\":\"0\",\"HasImage\":false,\"Percent\":\"80%\",\"PercentTotal\":\"79.2%\",\"Level\":\"2\"},{\"KeyNo\":\"pr0355072b71b257d7c57faf2e008bfc\",\"Name\":\"龚坤\",\"Org\":\"2\",\"DataType\":\"0\",\"HasImage\":false,\"Percent\":\"49%\",\"PercentTotal\":\"38.808%\",\"Level\":\"3\"}],[{\"KeyNo\":\"d12c475152a28b678792c0a1903bc9ed\",\"Name\":\"厦门志赢专利代理有限公司\",\"Org\":\"0\",\"HasImage\":false,\"Percent\":\"1%\",\"PercentTotal\":\"1%\",\"Level\":1},{\"KeyNo\":\"1bbd680572753d9c7a04b07208f9f3dc\",\"Name\":\"厦门金穗子财务管理集团有限公司\",\"Org\":\"0\",\"DataType\":\"0\",\"HasImage\":false,\"Percent\":\"51%\",\"PercentTotal\":\"0.51%\",\"Level\":\"2\"},{\"KeyNo\":\"pr0355072b71b257d7c57faf2e008bfc\",\"Name\":\"龚坤\",\"Org\":\"2\",\"DataType\":\"0\",\"HasImage\":false,\"Percent\":\"20%\",\"PercentTotal\":\"0.102%\",\"Level\":\"3\"}],[{\"KeyNo\":\"d12c475152a28b678792c0a1903bc9ed\",\"Name\":\"厦门志赢专利代理有限公司\",\"Org\":\"0\",\"HasImage\":false,\"Percent\":\"1%\",\"PercentTotal\":\"1%\",\"Level\":1},{\"KeyNo\":\"pr0355072b71b257d7c57faf2e008bfc\",\"Name\":\"龚坤\",\"Org\":\"2\",\"DataType\":\"0\",\"HasImage\":false,\"Percent\":\"49%\",\"PercentTotal\":\"0.49%\",\"Level\":\"2\"}]]},{\"KeyNo\":\"pr0c75cfb9a0fe3973c665f9ff2d1664\",\"Name\":\"李继清\",\"Org\":\"2\",\"HasImage\":false,\"Percent\":\"\",\"PercentTotal\":\"19.8%\",\"Level\":\"2\",\"Paths\":[[{\"KeyNo\":\"01cd398dd4d063475fe06037f3623bcf\",\"Name\":\"厦门志统生物科技有限公司\",\"Org\":\"0\",\"HasImage\":false,\"Percent\":\"99%\",\"PercentTotal\":\"99%\",\"Level\":1},{\"KeyNo\":\"pr0c75cfb9a0fe3973c665f9ff2d1664\",\"Name\":\"李继清\",\"Org\":\"2\",\"DataType\":\"0\",\"HasImage\":false,\"Percent\":\"20%\",\"PercentTotal\":\"19.8%\",\"Level\":\"2\"}]]},{\"KeyNo\":\"pr0ff0fea79661485ad991d810a3f495\",\"Name\":\"邓亚利\",\"Org\":\"2\",\"HasImage\":false,\"Percent\":\"\",\"PercentTotal\":\"32.7216%\",\"Level\":\"3,4\",\"Paths\":[[{\"KeyNo\":\"01cd398dd4d063475fe06037f3623bcf\",\"Name\":\"厦门志统生物科技有限公司\",\"Org\":\"0\",\"HasImage\":false,\"Percent\":\"99%\",\"PercentTotal\":\"99%\",\"Level\":1},{\"KeyNo\":\"f6438261bd005adb5bd51015269c9201\",\"Name\":\"厦门志越财税科技有限公司\",\"Org\":\"0\",\"DataType\":\"0\",\"HasImage\":false,\"Percent\":\"80%\",\"PercentTotal\":\"79.2%\",\"Level\":\"2\"},{\"KeyNo\":\"1bbd680572753d9c7a04b07208f9f3dc\",\"Name\":\"厦门金穗子财务管理集团有限公司\",\"Org\":\"0\",\"DataType\":\"0\",\"HasImage\":false,\"Percent\":\"51%\",\"PercentTotal\":\"40.392%\",\"Level\":\"3\"},{\"KeyNo\":\"pr0ff0fea79661485ad991d810a3f495\",\"Name\":\"邓亚利\",\"Org\":\"2\",\"DataType\":\"0\",\"HasImage\":false,\"Percent\":\"80%\",\"PercentTotal\":\"32.3136%\",\"Level\":\"4\"}],[{\"KeyNo\":\"d12c475152a28b678792c0a1903bc9ed\",\"Name\":\"厦门志赢专利代理有限公司\",\"Org\":\"0\",\"HasImage\":false,\"Percent\":\"1%\",\"PercentTotal\":\"1%\",\"Level\":1},{\"KeyNo\":\"1bbd680572753d9c7a04b07208f9f3dc\",\"Name\":\"厦门金穗子财务管理集团有限公司\",\"Org\":\"0\",\"DataType\":\"0\",\"HasImage\":false,\"Percent\":\"51%\",\"PercentTotal\":\"0.51%\",\"Level\":\"2\"},{\"KeyNo\":\"pr0ff0fea79661485ad991d810a3f495\",\"Name\":\"邓亚利\",\"Org\":\"2\",\"DataType\":\"0\",\"HasImage\":false,\"Percent\":\"80%\",\"PercentTotal\":\"0.408%\",\"Level\":\"3\"}]]}]}";
//        String result = evaluate(msg);
//        System.out.printf(result);
//    }

    public static String evaluate(String jsonString) {
        try {
            List<NebulaGraphCubeRelation> result = new ArrayList<>();
            jsonString = StringUtils.isNotBlank(jsonString) ? jsonString : "";
            IndirectPartnerDtoOut inItem = JSONObject.parseObject(jsonString, IndirectPartnerDtoOut.class);
            if (inItem != null) {
                List<PartnerPathOut> node = inItem.getNames();
                List<String> controlNos = new ArrayList<>();
                for (PartnerPathOut nodeItem : node) {
                    //获取节点的所有路径
                    List<List<PartnerPathOut>> totalPaths = nodeItem.getPaths();
                    for (List<PartnerPathOut> singlePath : totalPaths) {
                        boolean iFlag = singlePath.stream().allMatch(i -> {
                            double percent = getPercentValue(i.getPercent());
                            return percent > 50;
                        });
                        if (iFlag) {
                            if (!controlNos.contains(nodeItem.getKeyNo()) && StringUtils.isNotBlank(nodeItem.getKeyNo())) {
                                controlNos.add(nodeItem.getKeyNo());
                            }
                            break;
                        }
                    }
                }
                List<String> benefitNos = node.stream().filter(i -> {
                    double percentTotal = getPercentValue(i.getPercentTotal());
                    return StringUtils.isNotBlank(i.getKeyNo()) && !controlNos.contains(i.getKeyNo()) && percentTotal >= 5;
                }).map(i -> i.getKeyNo()).distinct().collect(Collectors.toList());

                for (String key : controlNos) {
                    PartnerPathOut nodeItem = node.stream().filter(i -> key.equals(i.getKeyNo())).findAny().orElse(null);
                    if (nodeItem != null) {
                        NebulaGraphCubeRelation outItem = new NebulaGraphCubeRelation();
                        outItem.setStartId(key);
                        outItem.setEndId(inItem.getKeyNo());
                        outItem.setMeta("control");
                        outItem.setStockPercent(getPercentValue(nodeItem.getPercentTotal()) + "");
                        outItem.setIsValid(true);
                        result.add(outItem);
                    }
                }

                for (String key : benefitNos) {
                    PartnerPathOut nodeItem = node.stream().filter(i -> key.equals(i.getKeyNo())).findAny().orElse(null);
                    if (nodeItem != null) {
                        NebulaGraphCubeRelation outItem = new NebulaGraphCubeRelation();
                        outItem.setStartId(key);
                        outItem.setEndId(inItem.getKeyNo());
                        outItem.setMeta("benefit");
                        outItem.setStockPercent(getPercentValue(nodeItem.getPercentTotal()) + "");
                        outItem.setIsValid(true);
                        result.add(outItem);
                    }
                }
            }
            return JSONObject.toJSONString(result);
        } catch (Exception e) {
            return "[]";
        }
    }

    private static double getPercentValue(String source) {
        double result = 0.0;
        try {
            source = getString(source);
            result = parseToDecimal(source.replace("%", ""), new BigDecimal(0), 4).doubleValue();
            if (result > 100) {
                result = 100;
            }
        } catch (Exception e) {

        }
        return result;
    }

    private static String getString(Object obj) {
        return obj == null ? "" : obj.toString().trim();
    }

    private static BigDecimal parseToDecimal(Object val, BigDecimal defaultValue, int decimals) {
        BigDecimal num = new BigDecimal(0);
        if ((val == null)) {
            return defaultValue;
        }

        if (StringUtils.isEmpty(val.toString())) {
            return defaultValue;
        }

        if (val instanceof BigDecimal || val instanceof Double) {
            BigDecimal valDeciaml = new BigDecimal(val.toString());
            return valDeciaml.setScale(decimals, BigDecimal.ROUND_HALF_UP);
        }

        try {
            Float.parseFloat(val.toString());
        } catch (Exception e) {
            return defaultValue;
        }

        BigDecimal valDeciaml = new BigDecimal(val.toString());
        return valDeciaml.setScale(decimals, BigDecimal.ROUND_HALF_UP);
    }
}