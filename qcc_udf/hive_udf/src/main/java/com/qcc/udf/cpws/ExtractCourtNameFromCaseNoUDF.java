package com.qcc.udf.cpws;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 裁判文书清洗UDF：从裁判文书案号中的代字对应出相应的法院名称
 * ---------------------------------------------------------------------------------------------------------
 * create temporary function extractCourtNameFromCaseNo as 'com.qcc.udf.cpws.ExtractCourtNameFromCaseNoUDF' using jar 'hdfs:///data/hive/udf/qcc_udf.jar';
 * ---------------------------------------------------------------------------------------------------------
 * select extractCourtNameFromCaseNo ('（2018）京0101执737号');
 * 结果: '北京市东城区人民法院'
 */



public class ExtractCourtNameFromCaseNoUDF extends UDF {
    private final static Map<String, String> caseNoCourtNameMap;
    static {
        caseNoCourtNameMap = new LinkedHashMap<>();
        try {
            try (InputStream is = ExtractCourtNameFromCaseNoUDF.class.getResourceAsStream("/casenoToCourtNameMap.csv")) {
                BufferedReader br = new BufferedReader(new InputStreamReader(is));
                String line;
                while ((line = br.readLine()) != null) {
                    String[] splits = line.split("\t");
                    if (splits != null && splits.length == 2) {
                        String caseNoChar = splits[0].trim();
                        String courtName = splits[1].trim();
                        caseNoCourtNameMap.put(caseNoChar, courtName);
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public String evaluate(String caseNo) {
        String courtName = StringUtils.EMPTY;
        try {
            if (StringUtils.isNotBlank(caseNo)) {
                /**
                 * 提取思路：
                 * 1 截取案号"民"/"执"/"商"/"刑"关键字后，去掉年份信息，得到代字关键词
                 * 2 根据代字关键词匹配map，得到法院名称或为空
                 */
                String[] splits = caseNo.replace("(", "（").replace(")", "）")
                        .split("民|执|商|刑");
                if (splits != null && splits.length == 2) {
                    String courtTmp = caseNoCourtNameMap.get(splits[0].split("）")[1]);
                    courtName = (courtTmp != null ? courtTmp : "");
                }
            }
        } catch (Exception ex) {
        }
        return courtName;
    }

    public static void main(String[] args) {
        System.out.println(new ExtractCourtNameFromCaseNoUDF().evaluate("（2020）浙0521执24号"));
    }
}
