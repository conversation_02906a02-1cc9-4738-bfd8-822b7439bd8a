package com.qcc.udf.cpws.wjp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class getErrorCompanyName extends UDF {
    public static void main(String[] args) {
        System.out.println(evaluate("[{\"Role\":\"原告\",\"LawyerTag\":0,\"DetailList\":[{\"KeyNo\":\"\",\"Org\":-1,\"JR\":\"\",\"JudgeResult\":\"\",\"ShowName\":\"努尔木卡买提·加库甫\",\"Name\":\"努尔木卡买提·加库甫\",\"LawyerList\":[]}],\"RoleTag\":0},{\"Role\":\"被告\",\"LawyerTag\":0,\"DetailList\":[{\"KeyNo\":\"\",\"Org\":-1,\"JR\":\"\",\"JudgeResult\":\"\",\"ShowName\":\"吾勒包西·伊力亚斯\",\"Name\":\"吾勒包西·伊力亚斯\",\"LawyerList\":[]}],\"RoleTag\":1}]"));
    }

    public static String evaluate(String caserole) {
        Set<String> names = new HashSet<>();
        if (StringUtils.isNotEmpty(caserole)) {
            List<JSONObject> caserolenew = new ArrayList<>();
            if (StringUtils.isNotEmpty(caserole)) {
                caserolenew = JSON.parseArray(caserole, JSONObject.class);
            }
            for (JSONObject object : caserolenew) {
                JSONArray list = object.getJSONArray("DetailList");
                for (Object o : list) {
                    JSONObject de = (JSONObject) o;
                    String s = de.getString("Name");
                    if (s.length() > 4 && StringUtils.isEmpty(de.getString("KeyNo"))) {
                        if (s.endsWith("司") || s.endsWith("行") || s.endsWith("队") || s.endsWith("会")
                                || s.endsWith("院") || s.endsWith("局") || s.endsWith("部") || s.endsWith("社")
                                || s.endsWith("厂") || s.endsWith("厅") || s.endsWith("所") || s.endsWith("店")
                                || s.endsWith("中心") || s.endsWith("政府") || s.endsWith("企业") || s.endsWith("基地")
                                || s.endsWith("超市") || s.endsWith("处") || s.endsWith("矿") || s.endsWith("室")
                                || s.endsWith("场") || s.endsWith("校") || s.endsWith("城") || s.endsWith("园")
                                || s.endsWith("馆") || s.endsWith("站") || s.endsWith("组") || s.endsWith("庭")
                                || s.endsWith("台") || s.endsWith("学") || s.endsWith("吧") || s.endsWith("庄")
                                || s.endsWith("户") || s.endsWith("段") || s.endsWith("团") || s.endsWith("村")
                                || s.endsWith("房") || s.endsWith("人") || s.endsWith("家") || s.endsWith("坊")
                                || s.endsWith("公寓") || s.endsWith("库") || s.contains("·")|| s.contains(".")||s.endsWith("1")
                                ||s.endsWith("2")||s.endsWith("3")||s.endsWith("4")||s.endsWith("X")||s.endsWith("x")) {

                        } else {
                            names.add(s);
                        }
                    }
                }
            }
        }
        return String.join(",", names);
    }
}
