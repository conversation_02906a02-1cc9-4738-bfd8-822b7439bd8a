package com.qcc.udf.liu;

import com.alibaba.fastjson.JSONArray;
import com.qcc.udf.cpws.CommonUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

public class compareJsonCaseRoleGroup2 extends UDF {

    public static String evaluate(String oldInfo, String newInfo)  throws Exception{
        List<entityInfoCaseRoleGroup> oldList = new LinkedList<>();
        if (StringUtils.isNotEmpty(oldInfo)){
            oldList = JSONArray.parseArray(oldInfo, entityInfoCaseRoleGroup.class);
        }

        List<entityInfoCaseRoleGroup> newList = new LinkedList<>();
        if (StringUtils.isNotEmpty(newInfo)){
            newList = JSONArray.parseArray(newInfo, entityInfoCaseRoleGroup.class);
        }

        if (oldList.size() != newList.size()){
            return "1";
        }else{
            List<String> oldSet = new LinkedList<>();
            for (entityInfoCaseRoleGroup item : oldList){
                String n = item.getRole() == null ? "" : item.getRole();
                String p = item.getLawyerTag() == null ? "" : item.getLawyerTag();
                String l = item.getRoleTag() == null ? "" : item.getRoleTag();

                String detailInfo = "";
                List<DetailInfo> DetailList = item.getDetailList();
                if (DetailList != null){
                    List<String> idList = new LinkedList<>();
                    for (DetailInfo detail : DetailList){
                        String keyNo = detail.getKeyNo() == null ? "" : detail.getKeyNo();
                        String org = detail.getOrg() == null ? "" : detail.getOrg();
                        String name = detail.getName() == null ? "" : detail.getName();
                        String showname = detail.getShowName() == null ? "" : detail.getShowName();
                        String jr = detail.getJR() == null ? "" : detail.getJR();
                        String judgeresult = detail.getJudgeResult() == null ? "" : detail.getJudgeResult();

                        String lyInfo = "";
                        List<LawyerInfo> lyList = detail.getLawyerList();
                        if (lyList != null){
                            List<String> infoList = new LinkedList<>();
                            for (LawyerInfo ly : lyList){
                                String keyNo1 = ly.getKeyNo() == null ? "" : ly.getKeyNo();
                                String org1 = ly.getOrg() == null ? "" : ly.getOrg();
                                String name1 = ly.getName() == null ? "" : ly.getName();
                                infoList.add(CommonUtil.full2Half(keyNo1.concat(org1).concat(name1)));

                            }
                            Collections.sort(infoList);
                            lyInfo = StringUtils.join(infoList, ",");
                        }
                        idList.add(CommonUtil.full2Half(keyNo.concat(org).concat(name).concat(showname).concat(judgeresult).concat(lyInfo)));
                    }
                    Collections.sort(idList);
                    detailInfo = StringUtils.join(idList, ",");
                }
                oldSet.add(CommonUtil.full2Half(n.concat(p).concat(l).concat(detailInfo)));
            }


            List<String> newSet = new LinkedList<>();
            for (entityInfoCaseRoleGroup item : newList){
                String n = item.getRole() == null ? "" : item.getRole();
                String p = item.getLawyerTag() == null ? "" : item.getLawyerTag();
                String l = item.getRoleTag() == null ? "" : item.getRoleTag();

                String detailInfo = "";
                List<DetailInfo> DetailList = item.getDetailList();
                if (DetailList != null){
                    List<String> idList = new LinkedList<>();
                    for (DetailInfo detail : DetailList){
                        String keyNo = detail.getKeyNo() == null ? "" : detail.getKeyNo();
                        String org = detail.getOrg() == null ? "" : detail.getOrg();
                        String name = detail.getName() == null ? "" : detail.getName();
                        String showname = detail.getShowName() == null ? "" : detail.getShowName();
                        String jr = detail.getJR() == null ? "" : detail.getJR();
                        String judgeresult = detail.getJudgeResult() == null ? "" : detail.getJudgeResult();

                        String lyInfo = "";
                        List<LawyerInfo> lyList = detail.getLawyerList();
                        if (lyList != null){
                            List<String> infoList = new LinkedList<>();
                            for (LawyerInfo ly : lyList){
                                String keyNo1 = ly.getKeyNo() == null ? "" : ly.getKeyNo();
                                String org1 = ly.getOrg() == null ? "" : ly.getOrg();
                                String name1 = ly.getName() == null ? "" : ly.getName();
                                infoList.add(CommonUtil.full2Half(keyNo1.concat(org1).concat(name1)));

                            }
                            Collections.sort(infoList);
                            lyInfo = StringUtils.join(infoList, ",");
                        }
                        idList.add(CommonUtil.full2Half(keyNo.concat(org).concat(name).concat(showname).concat(judgeresult).concat(lyInfo)));
                    }
                    Collections.sort(idList);
                    detailInfo = StringUtils.join(idList, ",");
                }
                newSet.add(CommonUtil.full2Half(n.concat(p).concat(l).concat(detailInfo)));
            }

            boolean flag = false;
            for (String old : oldSet){
                if (!newSet.contains(old)){
                    flag = true;
                }
            }
            for (String old : newSet){
                if (!oldSet.contains(old)){
                    flag = true;
                }
            }

            if (flag){
                return "1";
            }else{
                return "";
            }
        }
    }

    public static void main(String[] args) {
        try {
            System.out.println(evaluate("[{\"Role\":\"原告\",\"LawyerTag\":0,\"DetailList\":[{\"KeyNo\":\"\",\"Org\":-2,\"JR\":\"9\",\"JudgeResult\":\"1\",\"ShowName\":\"邱**\",\"Name\":\"邱华海\",\"LawyerList\":[]}],\"RoleTag\":0},{\"Role\":\"代理律师\",\"LawyerTag\":1,\"DetailList\":[{\"KeyNo\":\"w7fdef7f2b45d48830cd966ce1ce140f\",\"Org\":4,\"JR\":\"9\",\"JudgeResult\":\"\",\"ShowName\":\"河南朝野律师事务所\",\"Name\":\"河南朝野律师事务所\",\"LawyerList\":[{\"KeyNo\":\"22580ea005727ccd2b0359fb3d760a37\",\"Org\":-2,\"Name\":\"刘凯\"}]}],\"RoleTag\":0},{\"Role\":\"被告\",\"LawyerTag\":0,\"DetailList\":[{\"KeyNo\":\"\",\"Org\":-2,\"JR\":\"9\",\"JudgeResult\":\"17\",\"ShowName\":\"孙**\",\"Name\":\"孙钻伟\",\"LawyerList\":[]}],\"RoleTag\":1},{\"Role\":\"代理律师\",\"LawyerTag\":1,\"DetailList\":[{\"KeyNo\":\"w7fdef7f2b45d48830cd966ce1ce140f\",\"Org\":4,\"JR\":\"9\",\"JudgeResult\":\"\",\"ShowName\":\"河南朝野律师事务所\",\"Name\":\"河南朝野律师事务所\",\"LawyerList\":[{\"KeyNo\":\"943314e6a8774a6410a4813c0c3666f4\",\"Org\":-2,\"Name\":\"史柯\"}]}],\"RoleTag\":1}]",
                    "[{\"Role\":\"原告\",\"LawyerTag\":0,\"DetailList\":[{\"KeyNo\":\"\",\"Org\":-2,\"JR\":\"9\",\"JudgeResult\":\"1\",\"ShowName\":\"邱**\",\"Name\":\"邱华海\",\"LawyerList\":[]}],\"RoleTag\":0},{\"Role\":\"代理律师\",\"LawyerTag\":1,\"DetailList\":[{\"KeyNo\":\"w7fdef7f2b45d48830cd966ce1ce140f\",\"Org\":4,\"JR\":\"9\",\"JudgeResult\":\"\",\"ShowName\":\"河南朝野律师事务所\",\"Name\":\"河南朝野律师事务所\",\"LawyerList\":[{\"KeyNo\":\"22580ea005727ccd2b0359fb3d760a37\",\"Org\":-2,\"Name\":\"刘凯\"}]}],\"RoleTag\":0},{\"Role\":\"被告\",\"LawyerTag\":0,\"DetailList\":[{\"KeyNo\":\"\",\"Org\":-2,\"JR\":\"9\",\"JudgeResult\":\"17\",\"ShowName\":\"孙**\",\"Name\":\"孙钻伟\",\"LawyerList\":[]}],\"RoleTag\":1},{\"Role\":\"代理律师\",\"LawyerTag\":1,\"DetailList\":[{\"KeyNo\":\"w7fdef7f2b45d48830cd966ce1ce140f\",\"Org\":4,\"JR\":\"9\",\"JudgeResult\":\"\",\"ShowName\":\"河南朝野律师事务所\",\"Name\":\"河南朝野律师事务所\",\"LawyerList\":[{\"KeyNo\":\"943314e6a8774a6410a4813c0c3666f4\",\"Org\":-2,\"Name\":\"史柯\"}]}],\"RoleTag\":1}]"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
