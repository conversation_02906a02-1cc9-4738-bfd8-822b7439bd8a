package com.qcc.udf.cpws;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.casesearch_v3.enums.SourceEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 获取qcc_company 中commonlist的子值
 */
public class getCompanyList extends UDF {

    public static String evaluate(String commonList,int key) {

        JSONObject item = new JSONObject();
        //[QBD-46039] 【清洗】代理客户-新增筛选项和字段
        String value = "";
        if (StringUtils.isNotEmpty(commonList)) {
            List<JSONObject> commonOut = JSON.parseArray(commonList, JSONObject.class);

            for (JSONObject jsonObject : commonOut) {
                if (jsonObject.getInteger("Key") == key) {
                    value = jsonObject.getString("Value");
                }
            }
        }
        return value;
    }

    public static void main(String[] args) {
        List<String > caseList=new ArrayList<>();
        String beforeCaseNo="（2014）第0301号,（2014）第0148号";
        caseList.addAll(Arrays.asList(beforeCaseNo.split(",")));
        System.out.println(caseList);
    }
}
