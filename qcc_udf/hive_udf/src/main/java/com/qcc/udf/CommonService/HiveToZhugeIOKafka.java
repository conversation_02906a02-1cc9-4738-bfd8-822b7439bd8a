package com.qcc.udf.CommonService;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDF;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;

import java.util.Properties;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

@Description(name = "HiveToZhugeIOKafka", value = "_FUNC_(String  topics,String msg);arguments max.request.size = 236870912; - Return offset")
public class HiveToZhugeIOKafka extends UDF {

    private static Properties props = new Properties() {{
        put("bootstrap.servers", "*************:9092,*************:9092,*************:9092");
        put("acks", "1");
//        put("min.insync.replicas", 2);
        put("retries", 3);
//        put("batch.size", 16384);
//        put("linger.ms", 1);
        put("buffer.memory", 236870912);
        put("max.request.size", 20000000);
        put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
    }};
    private static KafkaProducer<String, String> producer = new KafkaProducer<>(props);

//    public static void main(String[] args) throws Exception {
//        evaluate("derek", "1");
//    }

    /**
     * @param topics
     * @param msg
     * @return
     * @throws Exception
     */
    public static String evaluate(String topics, String msg) throws ExecutionException, InterruptedException {
        try {
            if (StringUtils.isBlank(topics)) throw new InterruptedException("topic 不能为空");
            if (StringUtils.isBlank(msg)) return "0";
            Future<RecordMetadata> result = producer.send(new ProducerRecord<>(topics, msg));
            return String.valueOf(result.get().offset());
        } catch (Exception ex) {
            throw ex;
        }
    }


    /**
     * @param topics
     * @param table
     * @param fields
     * @param data
     * @return
     * @throws ExecutionException
     * @throws InterruptedException
     */
    public static String evaluate(String topics, String table, String fields, String... data) throws ExecutionException, InterruptedException {
        try {
            if (StringUtils.isBlank(topics)) throw new InterruptedException("topic 不能为空");
            if (StringUtils.isBlank(fields)) throw new InterruptedException("fields 不能为空");
            if (data == null || data.length == 0) throw new InterruptedException("字段值 不能为空");
            String[] fieldArray = fields.split(",");
            if (fieldArray.length != data.length) throw new InterruptedException("字段名称和字段值个数必须相等");
            String msg = "";
            JSONObject jsonObject = new JSONObject();
            for (int i = 0; i < fieldArray.length; i++) {
                jsonObject.put(fieldArray[i], data[i]);
            }
            if (StringUtils.isNotBlank(table)) {
                JSONArray jsonArray = new JSONArray();
                JSONObject result = new JSONObject();
                jsonArray.add(jsonObject);
                result.put(table, jsonArray);
                msg = JSON.toJSONString(result, SerializerFeature.WriteMapNullValue);
            } else {
                msg = JSON.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue);
            }
            Future<RecordMetadata> result = producer.send(new ProducerRecord<>(topics, msg));
            return String.valueOf(result.get().offset());
        } catch (Exception ex) {
            throw ex;
        }
    }


//    public static void main(String[] args) throws ExecutionException, InterruptedException {
//        System.out.println(evaluate("aaa","","f1,f2","a","b"));
//    }
}
