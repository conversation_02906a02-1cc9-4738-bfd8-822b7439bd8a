package com.qcc.udf.casesearch_v3.entity.output;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.LinkedList;
import java.util.List;

/**
 * @Auther: z<PERSON><PERSON><PERSON>
 * @Date: 2020/11/11 17:54
 * @Description:案件查询处罚类出餐基类
 */
@Data
public class BasePenaltyOutEntity {
    @JSONField(name = "Id")
    private String id = "";
    //处罚决定文书号
    @JSONField(name = "DocNo")
    private String docNo="";
    //处罚单位
    @JSONField(name = "PunishOffice")
    private String punishOffice="";
    //处罚是由
    @JSONField(name = "PunishReason")
    private String punishReason="";
    //处罚结果
    @JSONField(name = "PunishResult")
    private String punishResult="";
    //行政相对人
    @JSONField(name = "NameAndKeyNo")
    private List<NameAndKeyNoEntity> nameAndKeyNo = new LinkedList<>();
    //处罚日期
    @JSONField(name = "PunishDate")
    private Long punishDate = 0L;

    @JSONField(name = "IsValid")
    private Integer isValid = 0;

}
