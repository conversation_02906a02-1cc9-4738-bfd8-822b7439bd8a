package com.qcc.udf.casesearch_v3.test;

import com.google.common.base.Strings;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;
@Data
public class DebtInfo {
    private String zqrKeyNo;
    private String zqrName;

    private String zwrKeyNo;
    private String zwrName;

    private BigDecimal amount;
    private int isValid;
    private String amountType;
    private long timeStamp;
    private String wId;

    private String anNo;

    public String getZqrKeyNo() {
        return zqrKeyNo;
    }

    public void setZqrKeyNo(String zqrKeyNo) {
        this.zqrKeyNo = zqrKeyNo;
    }

    public String getZqrName() {
        return zqrName;
    }

    public void setZqrName(String zqrName) {
        this.zqrName = zqrName;
    }

    public String getZwrKeyNo() {
        return zwrKeyNo;
    }

    public void setZwrKeyNo(String zwrKeyNo) {
        this.zwrKeyNo = zwrKeyNo;
    }

    public String getZwrName() {
        return zwrName;
    }

    public void setZwrName(String zwrName) {
        this.zwrName = zwrName;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public int getIsValid() {
        return isValid;
    }

    public void setIsValid(int isValid) {
        this.isValid = isValid;
    }

    public String getAmountType() {
        return amountType;
    }

    public void setAmountType(String amountType) {
        this.amountType = amountType;
    }

    public long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(long timeStamp) {
        this.timeStamp = timeStamp;
    }

    public String getWId() {
        return wId;
    }

    public void setWId(String wId) {
        this.wId = wId;
    }

    public String getAnNo() {
        return anNo;
    }

    public void setAnNo(String anNo) {
        this.anNo = anNo;
    }

    public String getMark(){
        if(Strings.isNullOrEmpty(zwrKeyNo)){
            return zqrKeyNo+"_"+ CommonV3Util.full2Half(zwrName);
        }else{
            return zqrKeyNo+"_"+CommonV3Util.full2Half(zwrKeyNo);
        }
    }

    public  boolean sameZqr(String keyNo,String name){
        if(Strings.isNullOrEmpty(keyNo)){
            return Objects.equals(CommonV3Util.full2Half(name),CommonV3Util.full2Half(this.zqrName));
        }else{
            return Objects.equals(CommonV3Util.full2Half(keyNo),CommonV3Util.full2Half(this.zqrKeyNo));
        }
    }

    public  boolean sameZwr(String keyNo,String name){
        if(Strings.isNullOrEmpty(keyNo)){
            return Objects.equals(CommonV3Util.full2Half(name),CommonV3Util.full2Half(this.zwrName));
        }else{
            return Objects.equals(CommonV3Util.full2Half(keyNo),CommonV3Util.full2Half(this.zwrKeyNo));
        }
    }
}
