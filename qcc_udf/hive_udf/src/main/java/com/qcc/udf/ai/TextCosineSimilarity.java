package com.qcc.udf.ai;

import info.debatty.java.stringsimilarity.Cosine;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 简单的文本相似度对比，基于字符/字符组，无需分词
 */
public class TextCosineSimilarity extends UDF {

    final static Cosine cosine = new Cosine(2);
    final static Pattern removePattern = Pattern.compile("[《》？；：“”\\{\\}【】\\|﹃﹄﹁﹂『』「」、〈 〉，。/‘’—！@#￥%…&*（）\\\\·｜!:\"',\\.<>@#\\$%\\^&\\*\\(\\)_+=\\? \r\n\t-]");
    /**
     * 计算编辑距离
     * @param text1
     * @param text2
     * @return
     */
    public double evaluate(String text1, String text2) {

        // Pre-compute the profile of strings
        Map<String, Integer> profile1 = cosine.getProfile(preprocess(text1));
        Map<String, Integer> profile2 = cosine.getProfile(preprocess(text2));

        // Prints 0.516185
        return cosine.similarity(profile1, profile2);
    }

    /**
     * 预处理 去掉一些 文本
     * @param text
     * @return
     */
    public String preprocess(String text) {
        // 去除标点符号等空格，再转成小写字符
        Matcher m = removePattern.matcher(text);
        return m.replaceAll("").toLowerCase();
    }

//    public static void main(String[] args) {
//        String s = "住建部@修订个办法修出了太多的尴尬!";
//        String s1 = "住建部修订个办法";
//        System.out.println(new TextCosineSimilarity().eval(s, s1));
//    }

}
