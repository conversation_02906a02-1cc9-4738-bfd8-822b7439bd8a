package com.qcc.udf;


import org.apache.hadoop.hive.ql.exec.UDF;

public class halfToFull extends UDF {


    public static String evaluate(String halfStr) {

        /**
         * 半角转全角
         *
         * @param halfStr
         *            半角字符
         * @return 返回全角字符
         */
        if (halfStr == null || halfStr.trim().length() == 0) {
            return halfStr;
        }

            char[] c = halfStr.toCharArray();
            for (int i = 0; i < c.length; i++) {
                if (c[i] == 32) {
                    c[i] = (char) 12288;
                } else if (c[i] < 127) {
                    c[i] = (char) (c[i] + 65248);
                }
            }
            return new String(c);
        }


    }





