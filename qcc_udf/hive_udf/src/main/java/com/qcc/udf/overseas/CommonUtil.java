package com.qcc.udf.overseas;

import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormatter;

public class CommonUtil {

    /**
     * 根据时间格式转置为时间戳信息
     * @param dateInput
     * @param dateTimeFormatter
     * @return
     */
    public static String getTimestampFromDateField(String dateInput, DateTimeFormatter dateTimeFormatter) {
        String result = dateInput;
        try {
            result = String.valueOf(DateTime.parse(dateInput.trim(), dateTimeFormatter).getMillis());
        } catch (Exception e) {

        }
        return result;
    }

    public static Long getTimeStampLongFromDateField(String dateInput, DateTimeFormatter dateTimeFormatter) {
        String dateStr = getTimestampFromDateField(dateInput, dateTimeFormatter);
        try {
            return Long.parseLong(dateStr) / 1000;
        } catch (Exception e) {

        }
        return null;
    }
}
