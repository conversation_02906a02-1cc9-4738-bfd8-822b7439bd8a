package com.qcc.udf.risk_analysis;

public enum WeiduEnum {
    ALL(0, "全维度"),
    PCCZ(1, "破产重整"),
    XG(2, "限高"),
    SX(3, "失信"),
    ZX(4, "被执行"),
    ZB(5, "终本"),
    CPWS(6, "裁判文书"),
    LIAN(7, "立案"),
    KTGG(8, "开庭公告"),
    FYGG(9, "法院公告"),
    SDGG(10, "送达公告"),
    XJPG(11, "询价评估"),
    SFPM(12, "司法拍卖"),
    GQDJ(13, "股权冻结"),
    ZXBA(14, "注销备案"),
    JYZX(15, "简易注销"),
    JYYC(16, "经营异常"),
    GQCZ(17, "股权出质"),
    DCDY(18, "动产抵押"),
    TDDY(19, "土地抵押"),
    DWDB(20, "对外担保"),
    GSCG(21, "公示催告"),
    YZWF(22, "严重违法"),
    XZCF(23, "行政处罚"),
    HBCF(24, "环保处罚"),
    SSWF(25, "税收违法"),
    QSGG(26, "欠税公告"),
    WGCL(27, "违规处理"),
    CCJC(28, "抽查检查"),
    SQTJ(29, "诉前调解"),
    WZRJ(30, "未准入境"),
    SPAQ(31, "食品安全"),
    XZCJ(32, "限制出境"),
    SSJCC(33, "双随机抽查"),
    HMD(34, "黑名单"),
    CPZH(35, "产品召回"),
    ZSCQCZ(36, "知识产权出质");



    WeiduEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Integer code;
    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
