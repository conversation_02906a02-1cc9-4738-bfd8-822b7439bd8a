package com.qcc.udf.lawer;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date ：Created in 2021/03/03 13:34
 * @description ：获取城市和区域
 */
public class GetCityAndArea extends UDF {

    public static final String provinceKey = "省";
    public static final String cityKey = "市";
    public static final String areaKey = "区";
    public static final Pattern p = Pattern.compile("[A-Za-z0-9](区)");


    /**
     * Desc:获取城市和区域
     * @param type 1:城市 2：区域
     * */
    public static String evaluate(String param, int type) {
        String result = "";
        if (StringUtils.isEmpty(param)) {
            return result;
        }
        String[] areaStr = param.split("/");
        if (type == 1) {
            //获取市
            if (areaStr.length > 1) {
                result = areaStr[1];
            }

        } else if (type == 2) {
            //获取区
            if (areaStr.length > 2) {
                result = areaStr[2];
            }
        }

        return result;
    }

    public static void main(String[] args) {
        String param = "";
        System.out.println(evaluate(param, 1));
        System.out.println(evaluate(param, 2));
    }
}
