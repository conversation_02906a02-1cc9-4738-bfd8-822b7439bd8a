package com.qcc.udf.casesearch_v3.entity.output;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.time.LocalDate;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

@Data
public class PatentListEntity extends BaseCaseOutEntity{
    @JSONField(name = "Id")
    private String id;
    @JSONField(name = "PublicationDate")
    private Long publicationDate;
    @JSONField(name = "ApplicationDate")
    private Long applicationDate;
    @JSONField(name = "PublicationNumber")
    private String publicationNumber;
    @JSONField(name = "ApplicationNumber")
    private String applicationNumber;
    @JSONField(name = "InventorList")
    private String inventorList;
    @JSONField(name = "AssigneeList")
    private String assigneeList;
    @JSONField(name = "KindCode")
    private String kindCode;
    @JSONField(name = "Title")
    private String title;
    @JSONField(name = "Agency")
    private String agency;

    @JSONField(name = "Images")
    private String images;
    @JSONField(name = "LegalStatus")
    private String legalStatus;

    @JSONField(name = "Type")
    private Integer type = 4;
    @JSONField(name = "CompanyKeywords")
    private String companyKeywords;

    @JSONField(name = "NameAndKeyNo")
    private List<NameAndKeyNoEntity> nameAndKeyNo;
    @JSONField(name = "IsValid")
    private Integer isValid = 1;

    /**
     * 法律状态两级分类（用英文逗号间隔）
     */
    @JSONField(name = "Status")
    private String status;

    /**
     * 是否使用标志
     */
    @JSONField(name = "IsUse")
    private Integer isUse;

    @Override
    public boolean equals(Object o) {
        if (o == null ) return false;
        if (this == o) return true;
        PatentListEntity bean = (PatentListEntity) o;
        return Objects.equals(id,bean.id);
    }

    @Override
    public int hashCode() {
        int result = 1;
        result = 31 * result + id.hashCode();
        result = 31 * result + publicationNumber.hashCode();
        result = 31 * result + applicationNumber.hashCode();
        return result;
    }
}
