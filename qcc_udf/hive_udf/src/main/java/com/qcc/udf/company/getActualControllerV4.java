package com.qcc.udf.company;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.qcc.udf.company.controller.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class getActualControllerV4 extends UDF {
    /**
     * 获取实际控制人或者控制权最大的
     *
     * @param keyNo        公司keyno
     * @param companyName  公司名称
     * @param partners     公司股东
     * @param multipleOper 法人
     * @param employees    主要人员
     * @param isIpo        是否为上市公司
     * @param level        层级
     * @return
     */
    public String evaluate(String keyNo, String companyName, String partners, String multipleOper, String employees, Integer isIpo, Integer level) {
        CompanyActualFinal result = new CompanyActualFinal();

        BigDecimal minPercent;
        if (Pattern.matches(".*股份.*公司.*", companyName) || isIpo == 1) {
            minPercent = new BigDecimal("0.3");
        } else {
            minPercent = new BigDecimal("0.1");
        }

        //实际控制人
        CompanyActualController actualController = getComapnyActualController(keyNo, partners, multipleOper, employees, minPercent);
        if (StringUtils.isNotEmpty(actualController.getPkeyno()) || StringUtils.isNotEmpty(actualController.getPcompanyname())) {
            result.setActualControl(actualController);
        }

        List<CompanyPartner> groupList = new ArrayList<>();
        //最大控制权
        CompanyActualController maxControlPower = getComapnyMaxControlPower(keyNo, partners, employees, multipleOper, groupList, level);
        if (StringUtils.isNotEmpty(maxControlPower.getPkeyno()) || StringUtils.isNotEmpty(maxControlPower.getPcompanyname())) {
            result.setControlPower(maxControlPower);
        }

        result.setGroupList(groupList);

        return JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect);
    }

    /**
     * 获取实际控制人
     *
     * @param keyNo
     * @param partners
     * @param multipleOper
     * @param employees
     * @param minPercent
     * @return
     */
    public CompanyActualController getComapnyActualController(String keyNo, String partners, String multipleOper, String employees, BigDecimal minPercent) {
        CompanyActualController result = new CompanyActualController();

        if (StringUtils.isNotEmpty(keyNo) && StringUtils.isNotEmpty(partners)) {
            List<CompanyPartner> partnerList = JSONObject.parseArray(partners.replace("\\", "\\\\"), CompanyPartner.class);
            for (CompanyPartner partner : partnerList) {
                if (StringUtils.isNotEmpty(partner.control) && !"{}".equals(partner.control)) {
                    CompanyActualFinal companyControl = JSONObject.parseObject(partner.control, CompanyActualFinal.class);
                    if (StringUtils.isNotEmpty(multipleOper) && !"{}".equals(multipleOper) && companyControl.getActualControl() != null) {
                        CompanyMultipleOper companyMultipleOper = JSONObject.parseObject(multipleOper, CompanyMultipleOper.class);
                        if (companyMultipleOper != null && companyMultipleOper.getOperlist() != null && companyMultipleOper.getOperlist().size() > 0) {
                            if (companyMultipleOper.getOperlist().stream().anyMatch(s -> (StringUtils.isNotEmpty(s.getKeyNo()) && StringUtils.isNotEmpty(companyControl.getActualControl().getPkeyno()) && companyControl.getActualControl().getPkeyno().equals(s.getKeyNo()))
                                    || (StringUtils.isEmpty(s.getKeyNo()) && StringUtils.isEmpty(companyControl.getActualControl().getPkeyno()) && full2Half(s.getName()).equals(full2Half(companyControl.getActualControl().getPcompanyname()))))) {
                                partner.setIsoper(1);
                            } else {
                                partner.setIsoper(0);
                            }
                        }
                    }

                    if (companyControl.getActualControl() != null
                            && (!StringUtils.isEmpty(companyControl.getActualControl().getPkeyno()) || !StringUtils.isEmpty(companyControl.getActualControl().getPcompanyname()))) {
                        partner.setActualController(companyControl.getActualControl());
                    } else {
                        CompanyActualController actualController = new CompanyActualController();
                        actualController.setPkeyno("");
                        actualController.setPcompanyname("");
                        actualController.setIsgp(0);
                        actualController.setIsoper(0);
                        partner.setActualController(actualController);
                    }
                } else if (partner.haspartner == 0) {
                    CompanyActualController actualController = new CompanyActualController();
                    actualController.setPkeyno(partner.getPkeyno() == null ? "" : partner.getPkeyno());
                    actualController.setPcompanyname(partner.getPcompanyname());
                    actualController.setStockpercent(partner.getStockpercent());
                    actualController.setKeynopath(partner.getKeynopath());
                    actualController.setIsoper(partner.getIsoper());
                    actualController.setIsgp(partner.getIsgp());
//                    actualController.setGppath(partner.getGppath());
                    actualController.setHasimage(partner.isHasimage());
                    actualController.setFlag(1);
                    partner.setActualController(actualController);
                } else {
                    CompanyActualController actualController = new CompanyActualController();
                    actualController.setPkeyno("");
                    actualController.setPcompanyname("");
                    actualController.setIsgp(0);
                    actualController.setIsoper(0);
                    partner.setActualController(actualController);
                }

                //获取股东实际控制人的职位
                if (partner.getIsoper() == 1) {
                    partner.setJob("法定代表人");
                    partner.setJobLevel(10);
                } else {
                    partner.setJob("");
                    partner.setJobLevel(0);
                }
                if (StringUtils.isNotEmpty(employees) && !"[]".equals(employees) && partner.getActualController() != null) {
                    List<CompanyEmployee> employeeList = JSONObject.parseArray(employees, CompanyEmployee.class);
                    if (employeeList != null && employeeList.size() > 0) {
                        CompanyEmployee employee = employeeList.stream().filter(s -> (StringUtils.isNotEmpty(s.getKeyNo()) && StringUtils.isNotEmpty(partner.getActualController().getPkeyno()) && partner.getActualController().getPkeyno().equals(s.getKeyNo()))
                                || (StringUtils.isEmpty(s.getKeyNo()) && StringUtils.isEmpty(partner.getActualController().getPkeyno()) && full2Half(s.getName()).equals(full2Half(partner.getActualController().getPcompanyname())))).findFirst().orElse(null);
                        if (employee != null) {
                            partner.setJob(employee.getJob());
                            partner.setJobLevel(employee.getJobLevel());
                        }
                    }
                }

                if (StringUtils.isNotEmpty(partner.getActualController().getPkeyno())) {
                    partner.setGroupKey(partner.getActualController().getPkeyno());
                } else if (StringUtils.isNotEmpty(partner.getActualController().getPcompanyname())) {
                    partner.setGroupKey(full2Half(partner.getActualController().getPcompanyname()));
                } else {
                    partner.setGroupKey("");
                }
            }

            //实际控制人聚合
            List<CompanyPartner> groupPartnerList = getGroupPartnerList(partnerList);

            //获取实际控制人
            CompanyPartner finalActual = getActualController(groupPartnerList, minPercent);
            result = getFinalActualController(keyNo, finalActual, partnerList);
        }

        return result;
    }

    /**
     * 获取最大控制权
     *
     * @param keyNo
     * @param partners
     * @param employees
     * @param multipleOper
     * @param groupList
     * @param level
     * @return
     */
    public CompanyActualController getComapnyMaxControlPower(String keyNo, String partners, String employees, String multipleOper, List<CompanyPartner> groupList, Integer level) {
        CompanyActualController result = new CompanyActualController();

        if (StringUtils.isNotEmpty(keyNo) && StringUtils.isNotEmpty(partners)) {
            List<CompanyPartner> partnerList = JSONObject.parseArray(partners.replace("\\", "\\\\"), CompanyPartner.class);
            for (CompanyPartner partner : partnerList) {
                if (StringUtils.isNotEmpty(partner.control) && !"{}".equals(partner.control)) {
                    CompanyActualFinal companyControl = JSONObject.parseObject(partner.control, CompanyActualFinal.class);
                    if (StringUtils.isNotEmpty(multipleOper) && !"{}".equals(multipleOper) && companyControl.getControlPower() != null) {
                        CompanyMultipleOper companyMultipleOper = JSONObject.parseObject(multipleOper, CompanyMultipleOper.class);
                        if (companyMultipleOper != null && companyMultipleOper.getOperlist() != null && companyMultipleOper.getOperlist().size() > 0) {
                            if (companyMultipleOper.getOperlist().stream().anyMatch(s -> (StringUtils.isNotEmpty(s.getKeyNo()) && StringUtils.isNotEmpty(companyControl.getControlPower().getPkeyno()) && companyControl.getControlPower().getPkeyno().equals(s.getKeyNo()))
                                    || (StringUtils.isEmpty(s.getKeyNo()) && StringUtils.isEmpty(companyControl.getControlPower().getPkeyno()) && full2Half(s.getName()).equals(full2Half(companyControl.getControlPower().getPcompanyname()))))) {
                                partner.setIsoper(1);
                            } else {
                                partner.setIsoper(0);
                            }
                        }
                    }

                    if (companyControl.getControlPower() != null) {
                        partner.setActualController(companyControl.getControlPower());
                    } else {
                        CompanyActualController actualController = new CompanyActualController();
                        actualController.setPkeyno("");
                        actualController.setPcompanyname("");
                        actualController.setIsgp(0);
                        actualController.setIsoper(0);
                        partner.setActualController(actualController);
                    }
                } else if (partner.haspartner == 0) {
                    CompanyActualController actualController = new CompanyActualController();
                    actualController.setPkeyno(partner.getPkeyno() == null ? "" : partner.getPkeyno());
                    actualController.setPcompanyname(partner.getPcompanyname());
                    actualController.setStockpercent(partner.getStockpercent());
                    actualController.setKeynopath(partner.getKeynopath());
                    actualController.setIsoper(partner.getIsoper());
                    actualController.setIsgp(partner.getIsgp());
//                    actualController.setGppath(partner.getGppath());
                    actualController.setHasimage(partner.isHasimage());
                    actualController.setFlag(1);
                    partner.setActualController(actualController);
                } else {
                    CompanyActualController actualController = new CompanyActualController();
                    actualController.setPkeyno("");
                    actualController.setPcompanyname("");
                    actualController.setIsgp(0);
                    actualController.setIsoper(0);
                    partner.setActualController(actualController);
                }

                //获取股东实际控制人的职位
                if (partner.getIsoper() == 1) {
                    partner.setJob("法定代表人");
                    partner.setJobLevel(10);
                } else {
                    partner.setJob("");
                    partner.setJobLevel(0);
                }
                if (StringUtils.isNotEmpty(employees) && !"[]".equals(employees) && partner.getActualController() != null) {
                    List<CompanyEmployee> employeeList = JSONObject.parseArray(employees, CompanyEmployee.class);
                    if (employeeList != null && employeeList.size() > 0) {
                        CompanyEmployee employee = employeeList.stream().filter(s -> (StringUtils.isNotEmpty(s.getKeyNo()) && StringUtils.isNotEmpty(partner.getActualController().getPkeyno()) && partner.getActualController().getPkeyno().equals(s.getKeyNo()))
                                || (StringUtils.isEmpty(s.getKeyNo()) && StringUtils.isEmpty(partner.getActualController().getPkeyno()) && full2Half(s.getName()).equals(full2Half(partner.getActualController().getPcompanyname())))).findFirst().orElse(null);
                        if (employee != null) {
                            partner.setJob(employee.getJob());
                            partner.setJobLevel(employee.getJobLevel());
                        }
                    }
                }

                if (StringUtils.isNotEmpty(partner.getActualController().getPkeyno())) {
                    partner.setGroupKey(partner.getActualController().getPkeyno());
                } else if (StringUtils.isNotEmpty(partner.getActualController().getPcompanyname())) {
                    partner.setGroupKey(full2Half(partner.getActualController().getPcompanyname()));
                } else {
                    partner.setGroupKey("");
                }
            }

            //实际控制人聚合
            List<CompanyPartner> groupPartnerList = getGroupPartnerList(partnerList);

            //获取最大控制权
            result = getMaxControlPower(keyNo, groupPartnerList, partnerList, level);

            if (CollectionUtils.isNotEmpty(groupPartnerList)) {
                groupList.clear();
                groupList.addAll(groupPartnerList);
            }

            //groupList处理，合伙企业执行事务人的控制权是100%
            if (CollectionUtils.isNotEmpty(groupList) && groupList.size() > 1) {
                if (groupList.get(0).getIsgp() == 1 && groupList.get(1).getIsgp() != 1) {
                    groupList.get(0).setStockpercent(new BigDecimal("1"));
                    groupList.removeIf(s -> s.getIsgp() != 1);
                } else if (groupList.get(0).getIsgp() == 1 && groupList.get(1).getIsgp() == 1) {
                    groupList.clear();
                }
            }

            for (CompanyPartner item : groupList) {
                String groupKey = item.getGroupKey();
                if (StringUtils.isNotEmpty(groupKey)) {
                    String pCompanyName = partnerList.stream().filter(s -> s.getGroupKey().equals(groupKey))
                            .sorted(Comparator.comparing(CompanyPartner::getPcompanyname).reversed())
                            .map(s -> s.getActualController().getPcompanyname()).findFirst().orElse("");
                    item.setPcompanyname(pCompanyName);
                    if (groupKey.equals(full2Half(pCompanyName))) {
                        item.setGroupKey(pCompanyName);
                    }
                }
            }
        }

        return result;
    }

    /**
     * 根据股东实际控制人聚合
     *
     * @param partnerList
     * @return
     */
    private List<CompanyPartner> getGroupPartnerList(List<CompanyPartner> partnerList) {
        List<CompanyPartner> groupPartnerList = new ArrayList<>();

        // 根据GroupKey聚合，实际控制人keyno不为空但是名称为空应该没有实际控制人
        Map<String, List<CompanyPartner>> groupList = partnerList.stream().collect(Collectors.groupingBy(s -> {
            if (StringUtils.isEmpty(s.getActualController().getPcompanyname())
                    || "无实际控制人".equals(s.getActualController().getPcompanyname())
                    || (StringUtils.isNotEmpty(s.getGroupKey()) && Pattern.matches("^[0-9a-f]{32}$", s.getGroupKey()))) {
//                    || (s.getHaspartner() == 1 && StringUtils.isNotEmpty(s.getGroupKey()) && s.getGroupKey().equals(s.getPkeyno()) && Pattern.matches("^[0-9a-f]{32}$", s.getGroupKey()))) {
                return "";
            }
            return s.getGroupKey();
        }));

        groupList.forEach((key, list) -> {
            CompanyPartner item = new CompanyPartner();
            item.setGroupKey(key);
            if (Pattern.matches("^[0-9a-z]{32}$", key)) {
                item.setPkeyno(key);
                list.sort(Comparator.comparingInt(CompanyPartner::getIsgp).thenComparing(CompanyPartner::getStockpercent).thenComparing(CompanyPartner::getPcompanyname).reversed());
                item.setPcompanyname(list.get(0).getActualController().getPcompanyname());
            } else {
                item.setPkeyno("");
                item.setPcompanyname(key);
            }

            BigDecimal stockPercent = new BigDecimal("0");
            for (CompanyPartner s : list) {
                stockPercent = stockPercent.add(s.getStockpercent());
                if (s.getHaspartner() == 1 && StringUtils.isNotEmpty(key)) {
                    String[] actuakPaths = s.getActualController().getKeynopath().split(",");
                    for (int i = 0; i < actuakPaths.length; i++) {
                        String m = s.getKeynopath() + "-" + actuakPaths[i];
                        if (!actuakPaths[i].contains(s.getKeynopath()) && !Arrays.asList(actuakPaths).contains(m)) {
                            actuakPaths[i] = m;
                        }
                    }

                    s.setKeynopath(String.join(",", actuakPaths));
                }
            }

            if (StringUtils.isNotEmpty(key)) {
                String keyNoPaths = list.stream().filter(s -> StringUtils.isNotEmpty(s.getKeynopath()))
                        .map(s -> s.getKeynopath())
                        .distinct().collect(Collectors.joining(","));
                if (StringUtils.isNotEmpty(keyNoPaths)) {
                    keyNoPaths = Arrays.asList(keyNoPaths.split(",")).stream().distinct().sorted(Comparator.comparing(s -> s)).limit(100).collect(Collectors.joining(","));
                    item.setKeynopath(keyNoPaths);
                }
            }

            item.setStockpercent(stockPercent);
            item.setIsoper(list.stream().mapToInt(s -> s.getIsoper()).max().getAsInt());
            item.setIsgp(list.stream().mapToInt(s -> s.getIsgp()).max().getAsInt());
            item.setHasimage(list.stream().map(s -> s.getActualController().getHasimage()).findFirst().orElse(false));
            item.setHaspartner(list.stream().mapToInt(s -> s.getHaspartner()).max().getAsInt());
            item.setDataextend(list.stream().filter(s -> s.getActualController() != null && s.getActualController().getDataextend() != null).map(s -> s.getActualController().getDataextend()).findFirst().orElse(null));
            item.setJob(list.stream().filter(s -> StringUtils.isNotEmpty(s.getJob())).map(s -> s.getJob()).findFirst().orElse(""));
            item.setJobLevel(list.stream().mapToInt(s -> s.getJobLevel()).max().getAsInt());

            groupPartnerList.add(item);
        });

        return groupPartnerList;
    }

    /**
     * 获取实际控制人
     *
     * @param groupPartnerList
     * @param minPercent
     * @return
     */
    private CompanyPartner getActualController(List<CompanyPartner> groupPartnerList, BigDecimal minPercent) {
        CompanyPartner finalActual = null;

        if (groupPartnerList == null || groupPartnerList.size() == 0) {
            return finalActual;
        }

        // 只有一个股东
        if (groupPartnerList.size() == 1) {
            if (groupPartnerList.get(0).getIsgp() == 1 || groupPartnerList.get(0).getStockpercent().compareTo(minPercent) >= 0) {
                finalActual = groupPartnerList.get(0);
            }
        } else {
            // 根据isgp和股比倒序
            groupPartnerList.sort(Comparator.comparingInt(CompanyPartner::getIsgp).thenComparing(CompanyPartner::getStockpercent).thenComparing(CompanyPartner::getJobLevel).reversed());

            CompanyPartner firstActual = groupPartnerList.get(0);
            CompanyPartner secondActual = groupPartnerList.get(1);

            //多gp没有实际控制人
            if (firstActual.getIsgp() == 1 && secondActual.getIsgp() == 1) {
                return finalActual;
            }

            // gp优先，否则是控制权最大的
            if (firstActual.getIsgp() == 1 || firstActual.getStockpercent().compareTo(new BigDecimal("0.5")) > 0) {
                finalActual = firstActual;
            } else if (StringUtils.isNotEmpty(firstActual.getPcompanyname()) && firstActual.getStockpercent().compareTo(minPercent) >= 0) {
                if (firstActual.getStockpercent().compareTo(secondActual.getStockpercent().add(new BigDecimal("0.1"))) >= 0) {
                    finalActual = firstActual;
                } else if (firstActual.getStockpercent().compareTo(secondActual.getStockpercent()) > 0) {
                    if (firstActual.getJobLevel() > 0) {
                        finalActual = firstActual;
                    }
                } else {
                    List<CompanyPartner> matchList = groupPartnerList.stream().filter(s -> s.getStockpercent().compareTo(firstActual.getStockpercent()) == 0).collect(Collectors.toList());
                    matchList.sort(Comparator.comparingInt(CompanyPartner::getJobLevel).reversed());
                    if (matchList.get(0).getJobLevel().compareTo(matchList.get(1).getJobLevel()) > 0) {
                        finalActual = matchList.get(0);
                    }
                }

                //没有实际控制人的分组
                CompanyPartner noActualGroup = groupPartnerList.stream().filter(s -> StringUtils.isEmpty(s.getGroupKey())).findFirst().orElse(null);
                if (finalActual != null && noActualGroup != null && groupPartnerList.size() > 2) {
                    //如果控制权第一>控制权第二+没有控制权
                    List<CompanyPartner> hasActualGroupList = groupPartnerList.stream().filter(s -> StringUtils.isNotEmpty(s.getGroupKey())).collect(Collectors.toList());
                    hasActualGroupList.sort(Comparator.comparing(CompanyPartner::getStockpercent).reversed());
                    if (firstActual.getStockpercent().compareTo(hasActualGroupList.get(1).getStockpercent().add(noActualGroup.getStockpercent())) < 0) {
                        finalActual = null;
                    } else if (firstActual.getJobLevel() == 0
                            && firstActual.getStockpercent().compareTo(hasActualGroupList.get(1).getStockpercent().add(noActualGroup.getStockpercent())) == 0) {
                        finalActual = null;
                    }
                }
            }
        }

        return finalActual;
    }

    /**
     * 拼接实际控制人信息
     *
     * @param finalActual
     * @param partnerList
     * @return
     */
    private CompanyActualController getFinalActualController(String keyNo, CompanyPartner finalActual, List<CompanyPartner> partnerList) {
        CompanyActualController finalActualController = new CompanyActualController();

        if (finalActual == null || StringUtils.isEmpty(finalActual.getPcompanyname())) {
            //如果股东都有实际控制人，但是当前公司没有，实际控制人pkeyno给当前公司keyno
            if (!partnerList.stream().anyMatch(s -> StringUtils.isEmpty(s.getGroupKey()))) {
//                finalActualController.setGppath("");
                finalActualController.setHasimage(false);
                finalActualController.setIsgp(0);
                finalActualController.setIsoper(0);
                finalActualController.setKeynopath("");
                finalActualController.setPkeyno(keyNo);
                finalActualController.setPcompanyname("无实际控制人");
                finalActualController.setStockpercent(new BigDecimal("0"));
                finalActualController.setFlag(1);
            }
            return finalActualController;
        }

        if (!partnerList.stream().anyMatch(s -> StringUtils.isEmpty(s.getGroupKey()) || s.getActualController() == null || s.getActualController().getFlag() == null || s.getActualController().getFlag() == 0)) {
            finalActualController.setFlag(1);
        } else {
            finalActualController.setFlag(0);
        }

        //如果股东有实际控制人拼接Keynopath和Gppath，计算股比
        List<CompanyPartner> list = partnerList.stream()
                .filter(s -> s.getGroupKey().equals(finalActual.getGroupKey()))
                .sorted(Comparator.comparingInt(CompanyPartner::getIsgp).thenComparing(CompanyPartner::getStockpercent).thenComparing(CompanyPartner::getPcompanyname).reversed())
                .collect(Collectors.toList());

        finalActualController.setPkeyno(finalActual.getPkeyno());
        finalActualController.setPcompanyname(list.get(0).getActualController().getPcompanyname());
        finalActualController.setIsoper(finalActual.getIsoper());
        finalActualController.setIsgp(finalActual.getIsgp());
        finalActualController.setHasimage(finalActual.isHasimage());
        finalActualController.setDataextend(finalActual.getDataextend());

        BigDecimal stockPercent = new BigDecimal("0");
        for (CompanyPartner s : list) {
            stockPercent = stockPercent.add(s.getStockpercent());
//            if (s.getHaspartner() == 1) {
//                String[] actuakPaths = s.getActualController().getKeynopath().split(",");
//                for (int i = 0; i < actuakPaths.length; i++) {
//                    String item = s.getKeynopath() + "-" + actuakPaths[i];
//                    if (!actuakPaths[i].contains(s.getKeynopath()) && !Arrays.asList(actuakPaths).contains(item)) {
//                        actuakPaths[i] = item;
//                    }
//                }
//
//                s.setKeynopath(String.join(",", actuakPaths));
//            }
        }
        finalActualController.setStockpercent(stockPercent);

        //如果大股东股比>50%，取大股东的实际控制人路径作为主路径
        String keyNoPaths;
        List<CompanyPartner> gpList = list.stream().filter(s -> s.getIsgp() == 1).collect(Collectors.toList());
        if (gpList != null && gpList.size() >= 1) {
            keyNoPaths = gpList.stream().map(s -> s.getKeynopath()).distinct().collect(Collectors.joining(","));
        } else {
            keyNoPaths = list.stream().map(s -> s.getKeynopath()).distinct().collect(Collectors.joining(","));
        }

        if (StringUtils.isNotEmpty(keyNoPaths)) {
            keyNoPaths = Arrays.asList(keyNoPaths.split(",")).stream().distinct().sorted(Comparator.comparingInt(s -> s.length())).limit(100).collect(Collectors.joining(","));
        }

        finalActualController.setKeynopath(keyNoPaths);
        return finalActualController;
    }

    /**
     * 获取控制权最大
     *
     * @param keyNo
     * @param groupPartnerList
     * @param partnerList
     * @param level
     * @return
     */
    private CompanyActualController getMaxControlPower(String keyNo, List<CompanyPartner> groupPartnerList, List<CompanyPartner> partnerList, Integer level) {
        CompanyPartner finalActual = null;
        if (groupPartnerList != null && groupPartnerList.size() > 0) {
            // 根据isgp和股比倒序
            groupPartnerList.sort(Comparator.comparingInt(CompanyPartner::getIsgp).thenComparing(CompanyPartner::getStockpercent).thenComparing(CompanyPartner::getJobLevel).reversed());

            CompanyPartner firstActual = groupPartnerList.get(0);
            if (groupPartnerList.size() == 1) {
                finalActual = firstActual;
                if (finalActual.getIsgp() == 1) {
                    finalActual.setStockpercent(new BigDecimal("1"));
                }
            } else {
                CompanyPartner secondActual = groupPartnerList.get(1);
                //多gp没有最大控制权
                if (firstActual.getIsgp() == 1 && secondActual.getIsgp() == 1) {
                    finalActual = null;
                } else if (StringUtils.isNotEmpty(firstActual.getPcompanyname())) {
                    if (firstActual.getIsgp() == 1) {
                        finalActual = firstActual;
                        finalActual.setStockpercent(new BigDecimal("1"));
                    } else if (firstActual.getStockpercent().compareTo(new BigDecimal("0.5")) > 0) {
                        finalActual = firstActual;
                    } else if (firstActual.getStockpercent().compareTo(secondActual.getStockpercent()) >= 0) {
                        if (firstActual.getStockpercent().compareTo(secondActual.getStockpercent()) > 0) {
                            finalActual = firstActual;
                        } else {
                            List<CompanyPartner> matchList = groupPartnerList.stream().filter(s -> s.getStockpercent().compareTo(firstActual.getStockpercent()) == 0).collect(Collectors.toList());
                            matchList.sort(Comparator.comparingInt(CompanyPartner::getJobLevel).reversed());
                            if (matchList.get(0).getJobLevel().compareTo(matchList.get(1).getJobLevel()) > 0) {
                                finalActual = matchList.get(0);
                            }
                        }

                        //没有实际控制人的分组
                        CompanyPartner noActualGroup = groupPartnerList.stream().filter(s -> StringUtils.isEmpty(s.getGroupKey())).findFirst().orElse(null);
                        if (finalActual != null && noActualGroup != null && groupPartnerList.size() > 2) {
                            //如果控制权第一>控制权第二+没有控制权
                            List<CompanyPartner> hasActualGroupList = groupPartnerList.stream().filter(s -> StringUtils.isNotEmpty(s.getGroupKey())).collect(Collectors.toList());
                            hasActualGroupList.sort(Comparator.comparing(CompanyPartner::getStockpercent).reversed());
                            if (level <= 10 && firstActual.getStockpercent().compareTo(hasActualGroupList.get(1).getStockpercent().add(noActualGroup.getStockpercent())) < 0) {
                                finalActual = null;
                            } else if (firstActual.getJobLevel() == 0
                                    && firstActual.getStockpercent().compareTo(hasActualGroupList.get(1).getStockpercent().add(noActualGroup.getStockpercent())) == 0) {
                                finalActual = null;
                            }
                        }
                    }
                }
            }
        }

        if (finalActual == null || StringUtils.isEmpty(finalActual.getPcompanyname())) {
            //如果股东都有最大控制权，但是当前公司没有，最大控制权pkeyno给当前公司keyno，下一层无需再计算
            if (!partnerList.stream().anyMatch(s -> StringUtils.isEmpty(s.getGroupKey()))) {
                finalActual = new CompanyPartner();
//                finalActual.setGppath("");
                finalActual.setHasimage(false);
                finalActual.setIsgp(0);
                finalActual.setIsoper(0);
                finalActual.setKeynopath("");
                finalActual.setPkeyno(keyNo);
                finalActual.setPcompanyname("无实际控制人");
                finalActual.setStockpercent(new BigDecimal("0"));
            }
        }

        CompanyActualController result = new CompanyActualController();
        if (finalActual != null) {
            String groupKey = finalActual.getGroupKey();
            String pCompanyName = finalActual.getPcompanyname();
            if (StringUtils.isNotEmpty(groupKey)) {
                pCompanyName = partnerList.stream().filter(s -> s.getGroupKey().equals(groupKey))
                        .sorted(Comparator.comparing(CompanyPartner::getPcompanyname).reversed())
                        .map(s -> s.getActualController().getPcompanyname()).findFirst().orElse("");
            }

            result.setPkeyno(finalActual.getPkeyno());
            result.setPcompanyname(pCompanyName);
            result.setHasimage(finalActual.isHasimage());
            result.setStockpercent(finalActual.getStockpercent());
            result.setIsgp(finalActual.getIsgp());
            result.setIsoper(finalActual.getIsoper());
//            result.setGppath(finalActual.getGppath());
            result.setKeynopath(finalActual.getKeynopath());
            result.setDataextend(finalActual.getDataextend());

            if (!partnerList.stream().anyMatch(s -> StringUtils.isEmpty(s.getGroupKey()) || s.getActualController() == null || s.getActualController().getFlag() == null || s.getActualController().getFlag() == 0)) {
                result.setFlag(1);
            } else {
                result.setFlag(0);
            }
        }

        return result;
    }

    /**
     * 全角转半角
     *
     * @param input 输入
     * @return 半角文本
     */
    private String full2Half(String input) {
        if (StringUtils.isEmpty(input)) {
            return "";
        }

        char c[] = input.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == '\u3000') {
                c[i] = ' ';
            } else if (c[i] > '\uFF00' && c[i] < '\uFF5F') {
                c[i] = (char) (c[i] - 65248);
            }
        }
        return new String(c);
    }

    public static void main(String[] args) {
        getActualControllerV4 model = new getActualControllerV4();
        String result = "";
        result = model.evaluate("f2ea16f441809988a02a83debac6e211", "苏州慧芳项目管理合伙企业（有限合伙）", "[{\"pkeyno\":\"pr16787603dbcef44add58b1fcc46cce\",\"pcompanyname\":\"徐本仲\",\"stockpercent\":0,\"org\":2,\"hasimage\":false,\"shouldcapi\":\"\",\"keynopath\":\"f2ea16f441809988a02a83debac6e211\",\"isoper\":1,\"isgp\":1,\"gppath\":\"1\",\"haspartner\":0,\"control\":{}}]", "{\"OperType\":2,\"OperList\":[{\"KeyNo\":\"pr16787603dbcef44add58b1fcc46cce\",\"Org\":2,\"HasImage\":false,\"CompanyCount\":2,\"Name\":\"徐本仲\"}]}", "[]", 0, 1);
    }
}
