package com.qcc.udf.overseas;

import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

/**
 * ---------------------------------------------------------------------------------------------------------
 * create temporary function getHandledCompanyNameV2 as 'com.qcc.udf.overseas.GetHandledCompanyNameV2UDF' using jar 'hdfs:///data/hive/udf/qcc_udf.jar';
 * ---------------------------------------------------------------------------------------------------------
 * select getHandledCompanyNameV2 (entName);
 */
public class GetHandledCompanyNameV2UDF extends UDF {
    private final static Map<String, String> CompanyAbbrMap;
    static {
        CompanyAbbrMap = new HashMap<>();
        CompanyAbbrMap.put("BESLOTENVENNOOTSHAPMETBEPERKTEAANSPRAK-ELIJKHED", "BV");
        CompanyAbbrMap.put("GESELLSCHAFTMITBESCHRANKTERHAFTUNG", "GMBH");
        CompanyAbbrMap.put("SOCIEDADANONIMADERESPONSABILIDADLIMITADA", "SARL");
        CompanyAbbrMap.put("SOCIETEARESPONSABILITELIMITE", "SARL");
        CompanyAbbrMap.put("SOCIEDADANONIMADECAPITALVARIABLE", "SADECV");
        CompanyAbbrMap.put("SOCIEDADDERESPOSABILIDADLIMITADA", "SRL");
        CompanyAbbrMap.put("SOCIETÀARESPONSABILITÀLIMITATA", "SRL");
        CompanyAbbrMap.put("SPOLOČNOSŤSRUČENÍMOBMEDZENYM", "SRO");
        CompanyAbbrMap.put("LIMITEDLIABILITYCOMPANY", "LLC");
        CompanyAbbrMap.put("OPENJOINTSTOCKCOMPANY", "OJSC");
        CompanyAbbrMap.put("NAAMLOZEVENNOOTSCHAP", "NV");
        CompanyAbbrMap.put("PUBLICLIMITEDCOMPANY", "PLC");
        CompanyAbbrMap.put("PRIVATELIMITEDCOMPANY", "PLC");
        CompanyAbbrMap.put("LIFELONGLEARNINGPLAN", "LLP");
        CompanyAbbrMap.put("SOCIETAPERAZIONI", "SPA");
        CompanyAbbrMap.put("SOCIEDADANONIMA", "SA");
        CompanyAbbrMap.put("SOCIETAANONIMA", "SA");
        CompanyAbbrMap.put("SOCIETEANONYM", "SA");
        CompanyAbbrMap.put("THEBRITISHVIRGINISLANDS", "BVI");
        CompanyAbbrMap.put("BRITISHVIRGINISLANDS", "BVI");
        CompanyAbbrMap.put("FREEZONEESTABLISHMENT", "FZE");
        CompanyAbbrMap.put("FREEZONECOMPAGNIE", "FZC");
        CompanyAbbrMap.put("JOINTSTOCKCOMPANY", "JSC");
        CompanyAbbrMap.put("PERSEROANTERBATAS", "PT");
        CompanyAbbrMap.put("KABUSHIKIKAISHA", "KK");
        CompanyAbbrMap.put("LIMITEDPARTNERSHIP", "LP");
        CompanyAbbrMap.put("AKTIENGESELLSCHAFT", "AG");
        CompanyAbbrMap.put("COMPANYLIMITED", "COLTD");
        CompanyAbbrMap.put("CORPORATION", "CORP");
        CompanyAbbrMap.put("ESTABLISHMENT", "EST");
        CompanyAbbrMap.put("YUGENKAISHA", "YK");
        CompanyAbbrMap.put("AKTIESELSKAB", "A/S");
        CompanyAbbrMap.put("AKSJESELSKAP", "A/S");
        CompanyAbbrMap.put("INCORPORATED", "INC");
        CompanyAbbrMap.put("PROPRIETARY", "PTY");
        CompanyAbbrMap.put("MANUFACTORY", "MFY");
        CompanyAbbrMap.put("LIMITED", "LTD");
        CompanyAbbrMap.put("PRIVATE", "PTE");
        CompanyAbbrMap.put("ALTIEBOLAG", "AB");
        CompanyAbbrMap.put("BANGLADESH", "BD");
        CompanyAbbrMap.put("COMPANY", "CO");
        CompanyAbbrMap.put("HOLDING", "HLD");
        CompanyAbbrMap.put("(BVI)", "BVI");
        CompanyAbbrMap.put("FZCO", "FZC");
        CompanyAbbrMap.put("COLT", "COLTD");
        CompanyAbbrMap.put("TBK", "PT");
        CompanyAbbrMap.put("PVT", "PTE");
    }

    public String evaluate(String companyName) {
        /**
         * 公司名称处理转换逻辑：
         * 1 公司名做大写转换/剔除部分符号/去掉空格/半角处理
         * 2 公司尾部做简称的同义转换
         */
        try {
            if (StringUtils.isNotBlank(companyName)) {
                companyName = StringUtils.upperCase(companyName).replaceAll("\\.|,|\"|'|!|#|:", "")
                        .replace(" ", "").replace("（", "(").replace("）", ")");

                List<String> searchNameList = new ArrayList<>();
                for (Map.Entry<String, String> abbrEntry : CompanyAbbrMap.entrySet()) {
                    if (companyName.endsWith(abbrEntry.getKey())) {
                        String searchName = companyName.substring(0, (companyName.length()-abbrEntry.getKey().length())) + abbrEntry.getValue();
                        searchNameList.add(searchName);
                    }
                }

                // 公司名后缀在映射表中存在时，选取处理后公司名列表中长度最小的一个作为返回值
                if (searchNameList.size() > 0) {
                    companyName = searchNameList.stream().sorted((o1, o2) -> o1.length() - o2.length())
                            .findFirst().get();
                }
            }
        } catch (Exception ignored) {
        }
        return companyName;
    }

    public static void main(String[] args) {
        String companyName = "AAA THE BRITISH VIRGIN ISLANDS";
        String companyNameConnection = new GetHandledCompanyNameV2UDF().evaluate(companyName);
        System.out.println(companyNameConnection);
    }
}
