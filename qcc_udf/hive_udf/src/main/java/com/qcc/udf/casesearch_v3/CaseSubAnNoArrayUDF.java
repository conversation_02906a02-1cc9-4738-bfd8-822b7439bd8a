package com.qcc.udf.casesearch_v3;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import org.apache.commons.collections.CollectionUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021年04月21日 14:37
 */
public class CaseSubAnNoArrayUDF extends UDF {
    public static String evaluate(String anNoArray) {
        if(Strings.isNullOrEmpty(anNoArray)){
            return "";
        }
       Set<String> anNoSet = Arrays.asList(anNoArray.split(",")).stream()
               .filter(data->!Strings.isNullOrEmpty(data)).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(anNoSet) || anNoSet.size() == 1){
            return "";
        }
        List<String> list = anNoSet.stream().sorted().collect(Collectors.toList());
        String caseNo = list.get(0);
        String beforeCaseNo = list.subList(1,list.size()).stream().collect(Collectors.joining(","));
        Map<String,String> result = new HashMap<>();
        result.put("caseNo",caseNo);
        result.put("beforeCaseNo",beforeCaseNo);

        return JSON.toJSONString(result);
    }

    public static void main(String[] args) {
        System.out.println(evaluate("（2018）豫0326破260号,（2018）豫0326破申370号,（2019）豫0326破360号,（2018）豫0326破1号,（2018）豫0326破申260号,（2018）豫0326破申106号,（2018）豫0326破申360号,（2018）豫0326民破1号"));
    }
}
