package com.qcc.udf.casesearch_v3.entity.input;

import com.alibaba.fastjson.JSON;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.casesearch_v3.enums.CaseCategoryEnum;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import com.google.common.base.Strings;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther: zhangqiang
 * @Date: 2020/11/11 17:54
 * @Description:环保处罚
 */
@Data
public class HBCFEntity  extends BaseCaseEntity {

    /**
     * sxid : fceab03a23ce984f86d8febcfb5e2a272
     * id : fc9bab5094caa7ffcc5ad3fac8ff67a8
     * companykeywords : 上海基安汽车厢体制造厂。c5dbb3afc9821f9914228175aff93943
     * caseno : 第2120180044号
     * punishdate : 2018-01-22 00:00:00.0
     * illegaltype :
     * punishgov : 上海市奉贤区生态环境局
     * punishreason : 发现你单位于奉贤区青村镇浦星公路6995号从事汽车厢体加工生产项目，未向环保部门报批环境影响评价文件，配套环保设施未建成，主体工程于2017年8月即投入正式生产。
     * isvalid : 1
     * createdate : 2020-03-10 20:50:39.0
     * updatedate : 2020-11-02 06:51:44.0
     * courtyear : 2018
     * risklevel : 3
     * riskdate : 1516550400
     * companyname : null
     * company_c : 上海基安汽车厢体制造厂
     * implementation :
     * nameandkeyno : [{"KeyNo":"c5dbb3afc9821f9914228175aff93943","Org":0,"Name":"上海基安汽车厢体制造厂"}]
     * punishmentresult : 以下行政处罚：罚款人民币贰拾万元整。
     * province : SH
     */

    private String sxid;
    private String id;
    private String companynames;
    private String caseno;
    private long punishdate;
    private String illegaltype;
    private String punishgov;
    private String punishreason;
    private int isvalid;
    private String createdate;
    private String updatedate;
    private int courtyear;
    private int risklevel;
    private long riskdate;
    private Object companyname;
    private String company_c;
    private String implementation;
    private List<NameAndKeyNoEntity> nameandkeynoEntityList;
    private String punishmentresult;
    private String province;

    private String nameandkeyno;

    public static List<HBCFEntity> convert(List<String> jsonList) {
        List<HBCFEntity> list = new ArrayList<>();
        HBCFEntity entity = null;
        if(CollectionUtils.isEmpty(jsonList)){
            return list;
        }
        for (String json : jsonList) {
            if(Strings.isNullOrEmpty(json)){
                continue;
            }
            entity = JSON.parseObject(json, HBCFEntity.class);
            if(entity == null  || Strings.isNullOrEmpty(entity.getId())){
                continue;
            }

            String str = entity.getNameandkeyno();

            if (!Strings.isNullOrEmpty(str)) {
                entity.setNameandkeynoEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
            }

            //公共字段赋值
            entity.setBaseCaseNo(entity.getCaseno());
            entity.setBaseCaseCategoryEnum(CaseCategoryEnum.HBCF);
            if(!Strings.isNullOrEmpty(entity.getCompanynames())){
                entity.setBaseSearchWordSet(Arrays.stream(entity.getCompanynames().split(","))
                        .collect(Collectors.toSet()));
            }


            list.add(entity);
        }
        return list;
    }
}
