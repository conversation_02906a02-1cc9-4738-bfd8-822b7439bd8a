package com.qcc.udf.risk_graph;


import org.apache.commons.lang3.StringUtils;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2021/3/5
 */

public enum RiskCategoryEnum {
    PCCZ(1,"破产重整","申请",RiskWdEnum.PCCZ),
    SQXG(2,"限制高消费","申请被",RiskWdEnum.XG),
    GLXG(3,"限制高消费","使#{keyno}被关联",RiskWdEnum.XG),
    TAXG(4,"限制高消费","同案被",RiskWdEnum.XG),
    TASX(5,"失信被执行人","同案",RiskWdEnum.SX),
    SQZX(6,"被执行","申请成为",RiskWdEnum.ZX),
    QSMS(7,"民事起诉","",RiskWdEnum.SFAJ),
    QSXZ(8,"行政起诉","",RiskWdEnum.SFAJ),
    SFPM(9,"司法拍卖","",RiskWdEnum.SFPM),
    XJPG(10,"询价评估","",RiskWdEnum.XJPG),
    GQDJ(11,"股权冻结","",RiskWdEnum.GQDJ),
    YZWF(12,"严重违法","列入",RiskWdEnum.YZWF),
    JYYC(13,"经营异常","列入",RiskWdEnum.JYYC),
    XZCF(14,"行政处罚","",RiskWdEnum.XZCF),
    HBCF(15,"环保处罚","",RiskWdEnum.HBCF),
    SSWF(16,"税收违法","",RiskWdEnum.SSWF),
    QSGG(17,"欠税公告","",RiskWdEnum.QSGG),
    WGCL(18,"违规处理","",RiskWdEnum.WGCL),
    CCJC(19,"抽查检查","",RiskWdEnum.CCJC),
    GQCZ(20,"出质股权","向#{keyno}",RiskWdEnum.GQCZ),
    DCDY(21,"抵押动产","向#{keyno}",RiskWdEnum.DCDY),
    TDDY(22,"抵押土地","向#{keyno}",RiskWdEnum.TDDY),
    DWDB(23,"担保","请#{keyno}做",RiskWdEnum.DWDB),
    GSCG(24,"公示催示","申请#{keyno}持有的",RiskWdEnum.GSCG),
    JYXX(25,"交易信息","",RiskWdEnum.JYXX),
    TZXX(26,"投资信息","",RiskWdEnum.TZXX),
    RZXX(27,"任职信息","",RiskWdEnum.RZXX),
    ;


    private int type;
    private String desc;
    private String relation;
    private RiskWdEnum  riskWd;

    RiskCategoryEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    RiskCategoryEnum(int type, String desc, String relation,RiskWdEnum  riskWd) {
        this.type = type;
        this.desc = desc;
        this.relation = relation;
        this.riskWd = riskWd;
    }

    public String getDesc() {
        return desc;
    }

    public String getRelation() {
        return relation;
    }

    public RiskWdEnum getRiskWd() {
        return riskWd;
    }

    private static final Map<String, RiskCategoryEnum> lookup = new HashMap<String, RiskCategoryEnum>();

    static {
        for (RiskCategoryEnum e : EnumSet.allOf(RiskCategoryEnum.class)) {
            lookup.put(e.toString(), e);
        }
    }

    public static RiskCategoryEnum find(String name) {
        if (StringUtils.isBlank(name)){
            return null;
        }

        RiskCategoryEnum data = lookup.get(name.toUpperCase());
        if (name == null) {
            return null;
        }
        return data;
    }

    public static void main(String[] args) {
        String name ="gsc1";
        System.out.println(find(name));

    }
}
