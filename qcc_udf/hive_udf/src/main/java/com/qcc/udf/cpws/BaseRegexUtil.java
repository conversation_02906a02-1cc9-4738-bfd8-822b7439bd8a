package com.qcc.udf.cpws;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.TextUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class BaseRegexUtil {
    //身份证正则
    public static final String ID_CARD_REGEX = "[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]";
    public static final Pattern ID_CARD_NUMBER_PATTERN = Pattern.compile(ID_CARD_REGEX);
    public static final String SPLIT_REGEX = "[,，。;：:、.《》]";

    private final static String REGEXP_HOME_ADDRESS = "(通讯地址|家庭地址|家庭住址|户籍信息地|户籍地|住所地|住址|地址|现住)(.*[?<=市|县|区|室|号|村])(?!.*[法院|检察院])";
    private final static String REGEXP_BIRTH_ALL_DAY = "(\\d{2,4}年|[零一二三四五六七八九十\\d]{4}年)(\\d{0,2}月?|[一二三四五六七八九十]{0,2}月?)(\\d{0,2}|[零一二三四五六七八九十\\d]{0,3})";
    public static final Pattern REGEXP_BIRTH_ALL_DAY_PATTERN = Pattern.compile(REGEXP_BIRTH_ALL_DAY);

    /**
     * 隐藏文本中身份证号(默认隐藏月日)
     *
     * @param content 文本
     * @return String  身份证号已脱敏的文本
     */
    public static String handlerIdCard(String content) {
        if (StringUtils.isBlank(content)) {
            return content;
        }
        //脱敏身份证号
        content = handlerIdCard(content, 10, 4);
        //脱敏出生年月
        content = birthEncrypt(content);
        //脱敏住址信息
        content = addressEncrypt(content);
        return content;
    }

    /**
     * 隐藏文本中身份证号(根据指定位数隐藏)
     *
     * @param content 文本
     * @param front   身份证号需要显示前几位
     * @param end     身份证号需要显示末几位
     * @return String  身份证号已脱敏的文本
     */
    public static String handlerIdCard(String content, int front, int end) {
        Map<String, String> cardMap = getIdcardList(content, front, end);
        if (cardMap.isEmpty()) {
            return content;
        }
        for (Map.Entry<String, String> entry : cardMap.entrySet()) {
            content = content.replaceAll(entry.getKey(), entry.getValue());
        }
        return content;
    }

    /**
     * 从文本中提取身份证号map集合
     *
     * @param content 文本
     * @return Map<String, String>  key=身份证号，value=隐藏月日的身份证号
     */
    private static Map<String, String> getIdcardList(String content, int front, int end) {
        Map<String, String> cardMap = new HashMap<>(8);
        Matcher m = ID_CARD_NUMBER_PATTERN.matcher(content);
        while (m.find()) {
            String id = m.group();
            String idMask = idMask(id, front, end);
            cardMap.put(id, StringUtils.isBlank(idMask) ? id : idMask);
        }
        return cardMap;
    }

    /**
     * 用户身份证号码的打码隐藏加星号加*
     * <p>18位和非18位身份证处理均可成功处理</p>
     * <p>参数异常直接返回null</p>
     *
     * @param idCardNum 身份证号码
     * @param front     需要显示前几位
     * @param end       需要显示末几位
     * @return 处理完成的身份证
     */
    public static String idMask(String idCardNum, int front, int end) {
        //身份证不能为空
        if (TextUtils.isEmpty(idCardNum)) {
            return null;
        }
        //需要截取的长度不能大于身份证号长度
        if ((front + end) > idCardNum.length()) {
            return null;
        }
        //需要截取的不能小于0
        if (front < 0 || end < 0) {
            return null;
        }
        //计算*的数量
        int asteriskCount = idCardNum.length() - (front + end);
        StringBuffer asteriskStr = new StringBuffer();
        for (int i = 0; i < asteriskCount; i++) {
            asteriskStr.append("*");
        }
        String regex = "(\\w{" + String.valueOf(front) + "})(\\w+)(\\w{" + String.valueOf(end) + "})";
        return idCardNum.replaceAll(regex, "$1" + asteriskStr + "$3");
    }

    /**
     * 隐藏文本中地址信息
     *
     * @param content 文本
     * @return String  地址已脱敏的文本
     */
    private static String addressEncrypt(String content) {
        if (StringUtils.isBlank(content)) {
            return content;
        }
        String[] contentArr = content.split(SPLIT_REGEX);
        for (String str : contentArr) {
            if (StringUtils.isBlank(str)) {
                continue;
            }
            String regexpContent = str.replaceAll(REGEXP_HOME_ADDRESS, "$1" + "****");
            content = content.replace(str, regexpContent);
        }
        return content;
    }

    /**
     * 隐藏文本中的出生年月
     *
     * @param content 文本
     * @return String  出生年月已脱敏的文本
     */
    private static String birthEncrypt(String content) {
        if (Pattern.compile(REGEXP_BIRTH_ALL_DAY).matcher(content).find()) {
            content = replaceAll("生于", content, "");
            content = replaceAll("", content, "日出生");
            content = replaceAll("", content, "号出生");
            content = replaceAll("", content, "日生(?!产)");
            content = replaceAll("", content, "号生");
            content = replaceAll("", content, "出生");
            content = replaceAll("", content, "生(?!产)");
            content = replaceAll("出生日期", content, "");
        }
        return content;
    }

    private static String replaceAll(String prefix, String content, String suffix) {
        Matcher m = REGEXP_BIRTH_ALL_DAY_PATTERN.matcher(content);
        while (m.find()){
            String group = m.group();
            String replacement = "****年";
            if (group.contains("月")){
                replacement = "****年**月";
            }
            if (suffix.contains("日")||suffix.contains("号")){
                replacement = "****年**月**";
            }
            content = content.replaceAll(prefix + REGEXP_BIRTH_ALL_DAY + suffix, prefix + replacement + suffix);
        }
        return content;
    }


    /**
     * 删除文本中的证件号字段
     * @param content
     * @return
     */
    public static String replaceRegexColumn(String content) {
        if(StringUtils.isBlank(content)){
            return content;
        }
        // 身份证
        content = content.replace("(","（").replace(")","）");
        Matcher m = ID_CARD_NUMBER_PATTERN.matcher(content);
        while (m.find()) {
            String id = m.group();
            content = content.replaceAll(id, "");
            content=content
                    .replace("居民身份证号码","")
                    .replace("公民身份号码为","")
                    .replace("公民身份号码","")
                    .replace("身份证号码","")
                    .replace("居民身份证","")
                    .replace("身份证编号","")
                    .replace("证件号码","")
                    .replace("身份证号","")
                    .replace("身份证","")
                    .replaceAll("[:：]","")
                    .replace("（）","");

        }
        // 手机号
        Pattern pattern = Pattern.compile("(?<!\\d)(?:(?:1[3456789]\\d{9}))(?!\\d)");
        Matcher matcher = pattern.matcher(content);
        while (matcher.find()) {
            String phoneNum = matcher.group();
            content=content.replace(phoneNum,"");
        }
        //地址
        String[] contentArr = content.split(SPLIT_REGEX);
        for (String str : contentArr) {
            if (StringUtils.isBlank(str)) {
                continue;
            }
            String regexpContent = str.replaceAll("(通讯地址|家庭地址|家庭住址|住所地|住址|住)(.*[?<=市|县|区]).*", "$1" + "$2" + "****");
            content = content.replace(str, regexpContent);
        }
        //出生日期
        content = birthEncrypt(content);

        return content;
    }




    public static void main(String[] args) {
         String regex = "(.*a|b|c)\\b.{6}(.*(a|b|c))";
         Pattern compile = Pattern.compile(regex);
         Matcher matcher = compile.matcher("住长a市b城区高塘岭镇雷锋路社区10组317号，身份证号码：4301221a9c74");
        while (matcher.find()){
            System.out.println(matcher.group(0));
        }

        String content = "6,朱勇，男，汉族，1974年12月20日出生，住长沙市望城区高塘岭镇雷锋路社区10组317号，身份证号码：4301221974****0638被执行人拒不履行已经发生法律效力的（2012）长中民一终字第2297号民事判决书确定的义务";
            System.out.println(replaceRegexColumn(content));


        String regexs = "\\d+(?=号)|\\d+(?=幢)|\\d+(?=室)|\\d+(?=区)|\\d+(?=房)|\\d+(?=层)";
        String result = content.replaceAll(regexs, "**");
        System.out.println(result);

    }
}
