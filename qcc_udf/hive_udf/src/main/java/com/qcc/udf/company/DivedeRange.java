package com.qcc.udf.company;

public class DivedeRange {
    private int industryCode;
    private String companyType;
    private int rangeUp;
    private int rangeDown;

    public DivedeRange(int industryCode, String companyType, int rangeUp, int rangeDown) {
        this.industryCode = industryCode;
        this.companyType = companyType;
        this.rangeUp = rangeUp;
        this.rangeDown = rangeDown;
    }

    public int getIndustryCode() {
        return industryCode;
    }

    public void setIndustryCode(int industryCode) {
        this.industryCode = industryCode;
    }

    public String getCompanyType() {
        return companyType;
    }

    public void setCompanyType(String companyType) {
        this.companyType = companyType;
    }

    public int getRangeUp() {
        return rangeUp;
    }

    public void setRangeUp(int rangeUp) {
        this.rangeUp = rangeUp;
    }

    public int getRangeDown() {
        return rangeDown;
    }

    public void setRangeDown(int rangeDown) {
        this.rangeDown = rangeDown;
    }
}
