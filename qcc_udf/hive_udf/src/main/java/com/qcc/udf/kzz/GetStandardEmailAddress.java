package com.qcc.udf.kzz;

import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.stream.Collectors;

public class GetStandardEmailAddress extends UDF {
//    public static void main(String[] args) {
//        String testMsg = "项目部邮箱:ceidea@<EMAIL>,人力部邮箱:<EMAIL> 郵箱:Cathy@ jufubag.comceidea@<EMAIL>";
//        String result = evaluate(testMsg);
//        System.out.printf(result);
//    }

    public static String evaluate(String multiSource) {
        try {
            String source = StringUtils.isNotBlank(multiSource) ? multiSource : "";
            source = CommonV3Util.full2Half(source);
            source = source.replace((char) 12288, ' ').replaceAll("[\\s\\?#%\\[\\]\\{\\}【】\\(\\)\\+:=]+", "");
            source = source.toLowerCase();
            String emailRegex = "[A-Za-z0-9_\\.]+@[A-Za-z0-9_\\-\\.]+(\\.(com|cn|net|org|cc|co|shop|link|lv|live))";

            List<String> step1List = new ArrayList<>();
            if (RegexHelper.isFind(source, emailRegex)) {
                step1List = RegexHelper.getGlobalRegex(emailRegex, source);
            }

            List<String> resultList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(step1List)) {
                for (String item : step1List) {
                    item = item.replaceAll("\\.{2,}", ".");
                    item = item.replaceAll("\\-{2,}", "-");
                    item = item.replaceAll("\\_{2,}", "_");
                    String[] strArr = item.split("@");
                    if (strArr.length != 2) {
                        continue;
                    }
                    String address = strArr[0];
                    String domain = strArr[1];
                    address = address.replaceAll("(^(\\.|_){1,})|((\\.|_){1,}$)", "");
                    domain = domain.replaceAll("^(\\.|-|_){1,}", "");
                    if (address.length() <= 2 || !domain.contains(".")) {
                        continue;
                    }
                    resultList.add(address + "@" + domain);
                }
            }
            return StringUtils.join(resultList.stream().distinct().collect(Collectors.toList()), ",");
        } catch (Exception e) {
            return "";
        }
    }
}