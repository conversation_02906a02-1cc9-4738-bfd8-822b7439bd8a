package com.qcc.udf.risk_graph;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @desc 风险数据详情
 * @date 2021/3/4
 */
@Data
public class RiskDataInfo {
    /**
     * 数据主键
     */
    @JSONField(name = "Id")
    private String id;

    /**
     * 风险维度名称
     */
    @JSONField(name = "WdName")
    private String wdName;

    /**
     * 风险关系名
     */
    @JSONField(name = "RtName")
    private String rtName;

    /**
     * 风险维度描述
     */
    @JSONField(name = "WdDesc")
    private String wdDesc;

    /**
     * 风险维度ids
     */
    @JSONField(name = "Ids")
    private String ids;

    /**
     * 风险维度: WGCL,CCJC,DWDB. 无id的数据放details
     */
    @JSONField(name = "Details")
    private String details;

    /**
     * 风险维度cnt
     */
    @JSONField(name = "WdCount")
    private int wdCount;

    /**
     * 指向标识
     */
    @JSONField(name = "ForwardFlag")
    private int forwardFlag;

    /**
     * 金额1
     */
    @JSONField(name = "Amt1")
    private String amt1;

    /**
     * 金额2
     */
    @JSONField(name = "Amt2")
    private String amt2;

    @JSONField(name = "TradeAmount")
    private String tradeAmount;
}
