package com.qcc.udf.casesearch_v3;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

/**
 * <AUTHOR>
 */
public class mergeZxAndZb extends UDF {
    public static String evaluate(String id, String param) {
        JSONArray result = new JSONArray();

        if (StringUtils.isNotEmpty(param)) {
            // 获取被执行/终本的所有数据
            Map<String, JSONArray> zxMap = new LinkedHashMap<>();
            Map<String, JSONArray> zbMap = new LinkedHashMap<>();
            Map<String, JSONArray> sxMap = new LinkedHashMap<>();
            Map<String, JSONArray> xgMap = new LinkedHashMap<>();
            JSONArray array = JSONObject.parseObject(param).getJSONArray("InfoList");
            if (array != null && !array.isEmpty()) {
                Iterator<Object> it = array.iterator();
                while (it.hasNext()) {
                    JSONObject json = (JSONObject) it.next();
                    getInfoFromArray(json.getJSONArray("ZxList"), zxMap, 1);
                    getInfoFromArray(json.getJSONArray("ZbList"), zbMap, 2);
                    getInfoFromArray(json.getJSONArray("SxList"), sxMap, 3);
                    getInfoFromArray(json.getJSONArray("XgList"), xgMap, 4);
                }
            }
            //
            if (sxMap.size() > 0) {
                Set<String> keyNoSet = sxMap.keySet();
                for (String str : keyNoSet) {
                    if (sxMap.get(str).size() > 1) {
                        result.add(mergeResult(sxMap, id, str, 1));
                    }
                }
            }
            if (zxMap.size() > 0) {
                Set<String> keyNoSet = zxMap.keySet();
                for (String str : keyNoSet) {
                    if (zxMap.get(str).size() > 1) {
                        result.add(mergeResult(zxMap, id, str, 2));
                    }
                }
            }
            if (zbMap.size() > 0) {
                Set<String> keyNoSet = zbMap.keySet();
                for (String str : keyNoSet) {
                    if (zbMap.get(str).size() > 1) {
                        result.add(mergeResult(zbMap, id, str, 3));
                    }
                }
            }
            if (xgMap.size() > 0) {
                Set<String> keyNoSet = xgMap.keySet();
                for (String str : keyNoSet) {
                    if (xgMap.get(str).size() > 1) {
                        result.add(mergeResult(xgMap, id, str, 4));
                    }
                }
            }

        }

        return result.toString();
    }

    public static JSONObject mergeResult(Map<String, JSONArray> wbMap, String id, String str, int wdType) {
        JSONArray infoArray = wbMap.get(str);
        Iterator<Object> myIt = infoArray.iterator();
        Set<String> idSet = new LinkedHashSet<>();
        Long time = 0L;
        String newestId = "";
        while (myIt.hasNext()) {
            JSONObject json = (JSONObject) myIt.next();
            idSet.add(json.getString("Id"));
            if (json.getLong("SortDate").compareTo(time) >= 0) {
                time = json.getLong("SortDate");
                newestId = json.getString("Id");
            }
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("Id", id);
        jsonObject.put("KeyNo", str.substring(0, 32));
        jsonObject.put("AllIds", String.join(",", idSet));
        idSet.remove(newestId);
        jsonObject.put("DelIds", String.join(",", idSet));
        jsonObject.put("NewestId", newestId);
        jsonObject.put("IsValid", Integer.parseInt(str.substring(32, 33)));
        jsonObject.put("WdType", wdType);
        return jsonObject;
    }

    public static void getInfoFromArray(JSONArray array, Map<String, JSONArray> infoMap, int type) {
        if (array != null && !array.isEmpty()) {
            Iterator<Object> it = array.iterator();
            while (it.hasNext()) {
                JSONObject json = (JSONObject) it.next();
                int isValid = json.getInteger("IsValid");

                JSONArray jsonArray = json.getJSONArray("NameAndKeyNo");
                if (type == 4){
                    jsonArray = new JSONArray();
                    JSONArray array1 = json.getJSONArray("XglNameAndKeyNo");
                    JSONArray array2 = json.getJSONArray("GlNameAndKeyNo");
                    if (array1 != null && array1.size() > 0){
                        Iterator<Object> subIt = array1.iterator();
                        while (subIt.hasNext()) {
                            JSONObject nameJson = (JSONObject) subIt.next();
                            jsonArray.add(nameJson);
                        }
                    }
                    if (array2 != null && array2.size() > 0){
                        Iterator<Object> subIt = array2.iterator();
                        while (subIt.hasNext()) {
                            JSONObject nameJson = (JSONObject) subIt.next();
                            jsonArray.add(nameJson);
                        }
                    }
                }

                if (jsonArray != null && jsonArray.size() > 0) {
                    Iterator<Object> subIt = jsonArray.iterator();
                    while (subIt.hasNext()) {
                        JSONObject nameJson = (JSONObject) subIt.next();
                        String keyNo = nameJson.getString("KeyNo") == null ? "" : nameJson.getString("KeyNo");

                        if (StringUtils.isNotEmpty(keyNo)) {
                            JSONArray infoArr = new JSONArray();
                            if (infoMap.containsKey(keyNo.concat(String.valueOf(isValid)))) {
                                infoArr = infoMap.get(keyNo.concat(String.valueOf(isValid)));
                            }
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("Id", json.getString("Id"));
                            jsonObject.put("IsValid", isValid);
                            if (type == 1) {
                                jsonObject.put("SortDate", json.getLong("LianDate"));
                            } else if (type == 2) {
                                jsonObject.put("SortDate", json.getLong("JudgeDate"));
                            } else if (type == 3) {
                                jsonObject.put("SortDate", json.getLong("PublishDate"));
                            } else if (type == 4) {
                                jsonObject.put("SortDate", json.getLong("PublishDate"));
                            }

                            infoArr.add(jsonObject);
                            infoMap.put(keyNo.concat(String.valueOf(isValid)), infoArr);
                        }
                    }
                }
            }
        }
    }

    public static void main(String[] args) {
        System.out.println(evaluate("id123", "{\"_id\" : \"86688d8cfcea2e88d1aeb8a80bd5508d\",\"InfoList\" : [ {\"TrialRound\" : \"民事一审\",\"Anno\" : \"（2018）豫1729民初2129号\",\"CaseReason\" : \"借款合同纠纷\",\"Prosecutor\" : [],\"Defendant\" : [],\"Procuratorate\" : \"\",\"Court\" : \"河南省驻马店市新蔡县人民法院\",\"ExecuteNo\" : \"\",\"SxList\" : [],\"ZxList\" : [],\"XgList\" : [],\"CaseList\" : [ {\"Id\" : \"7e9b14806c9eee44cf0f6fb6cd44f403\",\"DocType\" : \"调解日期\",\"JudgeDate\" : NumberLong(1527696000),\"IsValid\" : 1,\"ResultType\" : \"调解结果\",\"Result\" : \"以调解方式结案\",\"ResultClean\" : \"以调解方式结案\",\"Amt\" : \"\",\"CaseType\" : \"民事调解书\",\"ShieldCaseFlag\" : 1}],\"LianList\" : [],\"FyggList\" : [],\"KtggList\" : [],\"SdggList\" : [],\"PcczList\" : [],\"GqdjList\" : [],\"XjpgList\" : [],\"ZbList\" : [],\"HbcfList\" : [],\"CfgsList\" : [],\"CfxyList\" : [],\"CfdfList\" : [],\"SfpmList\" : [],\"SqtjList\" : [],\"XzcjList\" : [],\"XdpgjgList\" : [],\"XsggList\" : [],\"LatestTimestamp\" : NumberLong(1527696000)}, {\"TrialRound\" : \"首次执行\",\"Anno\" : \"（2018）豫1729执1024号\",\"CaseReason\" : \"借款合同纠纷案件执行\",\"Prosecutor\" : [],\"Defendant\" : [ {\"Role\" : \"被执行人\",\"Name\" : \"袁金花\",\"KeyNo\" : \"pbbec57d6989d53b60ecd8f27b7b71bf\",\"Org\" : 2,\"JR\" : \"\"}, {\"Role\" : \"被执行人\",\"Name\" : \"赵芳\",\"KeyNo\" : \"\",\"Org\" : -2,\"JR\" : \"\"}, {\"Role\" : \"被执行人\",\"Name\" : \"闫泽亭\",\"KeyNo\" : \"pe046ca3e162ad21673207a3a5400f99\",\"Org\" : 2,\"JR\" : \"\"}, {\"Role\" : \"被执行人\",\"Name\" : \"马文堂\",\"KeyNo\" : \"\",\"Org\" : -2,\"JR\" : \"\"}],\"Procuratorate\" : \"\",\"Court\" : \"河南省驻马店市新蔡县人民法院\",\"ExecuteNo\" : \"2018豫1729民初2129号\",\"SxList\" : [ {\"Id\" : \"3378d56bd3f837caf0cbea347c98b7c62\",\"NameAndKeyNo\" : [ {\"Name\" : \"闫泽亭\",\"KeyNo\" : \"pe046ca3e162ad21673207a3a5400f99\",\"Org\" : 2}],\"PublishDate\" : NumberLong(1534089600),\"IsValid\" : 0,\"ExecuteStatus\" : \"全部未履行\",\"ActionType\" : \"有履行能力而拒不履行生效法律文书确定义务\"}, {\"Id\" : \"8c42ae59f4119cb5ef1625af681b85a62\",\"NameAndKeyNo\" : [ {\"Name\" : \"赵芳\",\"KeyNo\" : \"\",\"Org\" : -2}],\"PublishDate\" : NumberLong(1534089600),\"IsValid\" : 0,\"ExecuteStatus\" : \"全部未履行\",\"ActionType\" : \"有履行能力而拒不履行生效法律文书确定义务\"}, {\"Id\" : \"93f126d223a092e62e51bf1fe903d13b2\",\"NameAndKeyNo\" : [ {\"Name\" : \"马文堂\",\"KeyNo\" : \"\",\"Org\" : -2}],\"PublishDate\" : NumberLong(1534089600),\"IsValid\" : 0,\"ExecuteStatus\" : \"全部未履行\",\"ActionType\" : \"有履行能力而拒不履行生效法律文书确定义务\"}, {\"Id\" : \"f7763d13130610b45d7d5bcb78f743e32\",\"NameAndKeyNo\" : [ {\"Name\" : \"袁金花\",\"KeyNo\" : \"pbbec57d6989d53b60ecd8f27b7b71bf\",\"Org\" : 2}],\"PublishDate\" : NumberLong(1534089600),\"IsValid\" : 0,\"ExecuteStatus\" : \"全部未履行\",\"ActionType\" : \"有履行能力而拒不履行生效法律文书确定义务\"}],\"ZxList\" : [ {\"Id\" : \"8c42ae59f4119cb5ef1625af681b85a61\",\"NameAndKeyNo\" : [ {\"Name\" : \"赵芳\",\"KeyNo\" : \"\",\"Org\" : -2}],\"LianDate\" : NumberLong(1531929600),\"IsValid\" : 0,\"Biaodi\" : \"3935885\",\"SqrNameAndKeyNo\" : [ {\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0}]}, {\"Id\" : \"93f126d223a092e62e51bf1fe903d13b1\",\"NameAndKeyNo\" : [ {\"Name\" : \"马文堂\",\"KeyNo\" : \"\",\"Org\" : -2}],\"LianDate\" : NumberLong(1531929600),\"IsValid\" : 0,\"Biaodi\" : \"3935885\",\"SqrNameAndKeyNo\" : [ {\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0}]}, {\"Id\" : \"f7763d13130610b45d7d5bcb78f743e31\",\"NameAndKeyNo\" : [ {\"Name\" : \"袁金花\",\"KeyNo\" : \"pbbec57d6989d53b60ecd8f27b7b71bf\",\"Org\" : 2}],\"LianDate\" : NumberLong(1531929600),\"IsValid\" : 0,\"Biaodi\" : \"3935885\",\"SqrNameAndKeyNo\" : [ {\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0}]}],\"XgList\" : [ {\"Id\" : \"01fb8ff42f70910d818e7072506d6552\",\"NameAndKeyNo\" : [ {\"Name\" : \"袁金花\",\"KeyNo\" : \"pbbec57d6989d53b60ecd8f27b7b71bf\",\"Org\" : 2}],\"CompanyInfo\" : [ {\"Name\" : \"袁金花\",\"KeyNo\" : \"pbbec57d6989d53b60ecd8f27b7b71bf\",\"Org\" : 2}],\"PublishDate\" : NumberLong(1542556800),\"IsValid\" : 0,\"XglNameAndKeyNo\" : [ {\"Name\" : \"袁金花\",\"KeyNo\" : \"pbbec57d6989d53b60ecd8f27b7b71bf\",\"Org\" : 2}],\"GlNameAndKeyNo\" : [],\"SqrNameAndKeyNo\" : [ {\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0}],\"ContentFlag\" : 1}, {\"Id\" : \"05a016c6cf6e76f6969b15138014eda6\",\"NameAndKeyNo\" : [ {\"Name\" : \"马文堂\",\"KeyNo\" : \"\",\"Org\" : -2}],\"CompanyInfo\" : [ {\"Name\" : \"马文堂\",\"KeyNo\" : \"\",\"Org\" : -2}],\"PublishDate\" : NumberLong(1542556800),\"IsValid\" : 0,\"XglNameAndKeyNo\" : [ {\"Name\" : \"马文堂\",\"KeyNo\" : \"\",\"Org\" : -2}],\"GlNameAndKeyNo\" : [],\"SqrNameAndKeyNo\" : [ {\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0}],\"ContentFlag\" : 1}, {\"Id\" : \"9890d39f797c94579a28b1104a733454\",\"NameAndKeyNo\" : [ {\"Name\" : \"赵芳\",\"KeyNo\" : \"\",\"Org\" : -2}],\"CompanyInfo\" : [ {\"Name\" : \"赵芳\",\"KeyNo\" : \"\",\"Org\" : -2}],\"PublishDate\" : NumberLong(1542556800),\"IsValid\" : 0,\"XglNameAndKeyNo\" : [ {\"Name\" : \"赵芳\",\"KeyNo\" : \"\",\"Org\" : -2}],\"GlNameAndKeyNo\" : [],\"SqrNameAndKeyNo\" : [ {\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0}],\"ContentFlag\" : 1}, {\"Id\" : \"cbb3bb6122ca7720ddc7d2a98b63fcc6\",\"NameAndKeyNo\" : [ {\"Name\" : \"闫泽亭\",\"KeyNo\" : \"pe046ca3e162ad21673207a3a5400f99\",\"Org\" : 2}],\"CompanyInfo\" : [ {\"Name\" : \"闫泽亭\",\"KeyNo\" : \"pe046ca3e162ad21673207a3a5400f99\",\"Org\" : 2}],\"PublishDate\" : NumberLong(1542556800),\"IsValid\" : 0,\"XglNameAndKeyNo\" : [ {\"Name\" : \"闫泽亭\",\"KeyNo\" : \"pe046ca3e162ad21673207a3a5400f99\",\"Org\" : 2}],\"GlNameAndKeyNo\" : [],\"SqrNameAndKeyNo\" : [ {\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0}],\"ContentFlag\" : 1}],\"CaseList\" : [ {\"Id\" : \"c8b5d38a158b8538a0c61dd93c4017df\",\"DocType\" : \"决定日期\",\"JudgeDate\" : NumberLong(1534089600),\"IsValid\" : 1,\"ResultType\" : \"决定结果\",\"Result\" : \"将闫泽亭纳入失信被执行人名单。\",\"ResultClean\" : \"将闫泽亭纳入失信被执行人名单。\",\"Amt\" : \"\",\"CaseType\" : \"执行决定书\",\"ShieldCaseFlag\" : 0}],\"LianList\" : [],\"FyggList\" : [],\"KtggList\" : [],\"SdggList\" : [],\"PcczList\" : [],\"GqdjList\" : [],\"XjpgList\" : [],\"ZbList\" : [ {\"Id\" : \"3378d56bd3f837caf0cbea347c98b7c6\",\"JudgeDate\" : NumberLong(1544198400),\"IsValid\" : 0,\"NameAndKeyNo\" : [ {\"Name\" : \"闫泽亭\",\"KeyNo\" : \"pe046ca3e162ad21673207a3a5400f99\",\"Org\" : 2}],\"ExecuteObject\" : \"3935885\",\"FailureAct\" : \"3195885\"}, {\"Id\" : \"8c42ae59f4119cb5ef1625af681b85a6\",\"JudgeDate\" : NumberLong(1544198400),\"IsValid\" : 0,\"NameAndKeyNo\" : [ {\"Name\" : \"赵芳\",\"KeyNo\" : \"\",\"Org\" : -2}],\"ExecuteObject\" : \"3935885\",\"FailureAct\" : \"3195885\"}, {\"Id\" : \"93f126d223a092e62e51bf1fe903d13b\",\"JudgeDate\" : NumberLong(1544198400),\"IsValid\" : 1,\"NameAndKeyNo\" : [ {\"Name\" : \"马文堂\",\"KeyNo\" : \"\",\"Org\" : -2}],\"ExecuteObject\" : \"3935885\",\"FailureAct\" : \"3195885\"}, {\"Id\" : \"f7763d13130610b45d7d5bcb78f743e3\",\"JudgeDate\" : NumberLong(1544198400),\"IsValid\" : 1,\"NameAndKeyNo\" : [ {\"Name\" : \"袁金花\",\"KeyNo\" : \"pbbec57d6989d53b60ecd8f27b7b71bf\",\"Org\" : 2}],\"ExecuteObject\" : \"3935885\",\"FailureAct\" : \"3195885\"}],\"HbcfList\" : [],\"CfgsList\" : [],\"CfxyList\" : [],\"CfdfList\" : [],\"SfpmList\" : [],\"SqtjList\" : [],\"XzcjList\" : [],\"XdpgjgList\" : [],\"XsggList\" : [],\"LatestTimestamp\" : NumberLong(1544198400)}, {\"TrialRound\" : \"恢复执行\",\"Anno\" : \"（2020）豫1729执恢34号\",\"CaseReason\" : \"借款合同纠纷案件执行\",\"Prosecutor\" : [ {\"Role\" : \"申请人\",\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0,\"JR\" : \"\",\"LawFirmList\" : [ {\"P\" : \"\",\"N\" : \"\",\"O\" : -2,\"LY\" : [ {\"P\" : \"高峰\",\"R\" : \"委托代理人\",\"N\" : \"\"}]}]}],\"Defendant\" : [ {\"Role\" : \"被执行人\",\"Name\" : \"***\",\"KeyNo\" : \"\",\"Org\" : -2,\"LR\" : \"21\",\"JR\" : \"28\"}, {\"Role\" : \"被执行人\",\"Name\" : \"袁金花\",\"KeyNo\" : \"pbbec57d6989d53b60ecd8f27b7b71bf\",\"Org\" : 2,\"LR\" : \"21\",\"JR\" : \"28\"}, {\"Role\" : \"被执行人\",\"Name\" : \"闫泽亭\",\"KeyNo\" : \"pe046ca3e162ad21673207a3a5400f99\",\"Org\" : 2,\"LR\" : \"21\",\"JR\" : \"28\"}, {\"Role\" : \"被执行人\",\"Name\" : \"马文堂\",\"KeyNo\" : \"\",\"Org\" : -2,\"LR\" : \"21\",\"JR\" : \"28\"}],\"Procuratorate\" : \"\",\"Court\" : \"河南省驻马店市新蔡县人民法院\",\"ExecuteNo\" : \"（2018）豫1729民初2129号\",\"SxList\" : [ {\"Id\" : \"2b14b71f3aa63e8d6c1edbeebe4e2da52\",\"NameAndKeyNo\" : [ {\"Name\" : \"马文堂\",\"KeyNo\" : \"\",\"Org\" : -2}],\"PublishDate\" : NumberLong(1613923200),\"IsValid\" : 1,\"ExecuteStatus\" : \"全部未履行\",\"ActionType\" : \"有履行能力而拒不履行生效法律文书确定义务\"}, {\"Id\" : \"8743ca63fe73ccdedd930857c3cb8eaa2\",\"NameAndKeyNo\" : [ {\"Name\" : \"赵芳\",\"KeyNo\" : \"\",\"Org\" : -2}],\"PublishDate\" : NumberLong(1613923200),\"IsValid\" : 1,\"ExecuteStatus\" : \"全部未履行\",\"ActionType\" : \"有履行能力而拒不履行生效法律文书确定义务\"}, {\"Id\" : \"8fe139567001c883ab30db0b920cccdb2\",\"NameAndKeyNo\" : [ {\"Name\" : \"闫泽亭\",\"KeyNo\" : \"pe046ca3e162ad21673207a3a5400f99\",\"Org\" : 2}],\"PublishDate\" : NumberLong(1613923200),\"IsValid\" : 0,\"ExecuteStatus\" : \"全部未履行\",\"ActionType\" : \"有履行能力而拒不履行生效法律文书确定义务\"}, {\"Id\" : \"c1d59b7bd41d1a47023089326fb718252\",\"NameAndKeyNo\" : [ {\"Name\" : \"袁金花\",\"KeyNo\" : \"pbbec57d6989d53b60ecd8f27b7b71bf\",\"Org\" : 2}],\"PublishDate\" : NumberLong(1613923200),\"IsValid\" : 1,\"ExecuteStatus\" : \"全部未履行\",\"ActionType\" : \"有履行能力而拒不履行生效法律文书确定义务\"}],\"ZxList\" : [ {\"Id\" : \"2b14b71f3aa63e8d6c1edbeebe4e2da51\",\"NameAndKeyNo\" : [ {\"Name\" : \"马文堂\",\"KeyNo\" : \"\",\"Org\" : -2}],\"LianDate\" : NumberLong(1586880000),\"IsValid\" : 0,\"Biaodi\" : \"3935885\",\"SqrNameAndKeyNo\" : [ {\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0}]}, {\"Id\" : \"8743ca63fe73ccdedd930857c3cb8eaa1\",\"NameAndKeyNo\" : [ {\"Name\" : \"赵芳\",\"KeyNo\" : \"\",\"Org\" : -2}],\"LianDate\" : NumberLong(1586880000),\"IsValid\" : 0,\"Biaodi\" : \"3935885\",\"SqrNameAndKeyNo\" : [ {\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0}]}, {\"Id\" : \"8fe139567001c883ab30db0b920cccdb1\",\"NameAndKeyNo\" : [ {\"Name\" : \"闫泽亭\",\"KeyNo\" : \"pe046ca3e162ad21673207a3a5400f99\",\"Org\" : 2}],\"LianDate\" : NumberLong(1586880000),\"IsValid\" : 0,\"Biaodi\" : \"3935885\",\"SqrNameAndKeyNo\" : [ {\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0}]}, {\"Id\" : \"c1d59b7bd41d1a47023089326fb718251\",\"NameAndKeyNo\" : [ {\"Name\" : \"袁金花\",\"KeyNo\" : \"pbbec57d6989d53b60ecd8f27b7b71bf\",\"Org\" : 2}],\"LianDate\" : NumberLong(1586880000),\"IsValid\" : 0,\"Biaodi\" : \"3935885\",\"SqrNameAndKeyNo\" : [ {\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0}]}],\"XgList\" : [ {\"Id\" : \"15214ea595eb2f2b5375b598c73b3abf\",\"NameAndKeyNo\" : [ {\"Name\" : \"马文堂\",\"KeyNo\" : \"\",\"Org\" : -2}],\"CompanyInfo\" : [ {\"Name\" : \"马文堂\",\"KeyNo\" : \"\",\"Org\" : -2}],\"PublishDate\" : NumberLong(1610294400),\"IsValid\" : 1,\"XglNameAndKeyNo\" : [ {\"Name\" : \"马文堂\",\"KeyNo\" : \"\",\"Org\" : -2}],\"GlNameAndKeyNo\" : [],\"SqrNameAndKeyNo\" : [ {\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0}],\"ContentFlag\" : 1}, {\"Id\" : \"25ef847ab6ed89a599ff7cc5f42055dd\",\"NameAndKeyNo\" : [ {\"Name\" : \"袁金花\",\"KeyNo\" : \"pbbec57d6989d53b60ecd8f27b7b71bf\",\"Org\" : 2}],\"CompanyInfo\" : [ {\"Name\" : \"袁金花\",\"KeyNo\" : \"pbbec57d6989d53b60ecd8f27b7b71bf\",\"Org\" : 2}],\"PublishDate\" : NumberLong(1610294400),\"IsValid\" : 1,\"XglNameAndKeyNo\" : [ {\"Name\" : \"袁金花\",\"KeyNo\" : \"pbbec57d6989d53b60ecd8f27b7b71bf\",\"Org\" : 2}],\"GlNameAndKeyNo\" : [],\"SqrNameAndKeyNo\" : [ {\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0}],\"ContentFlag\" : 1}, {\"Id\" : \"45246549e14a640fcbce0effd51e2e44\",\"NameAndKeyNo\" : [ {\"Name\" : \"赵芳\",\"KeyNo\" : \"\",\"Org\" : -2}],\"CompanyInfo\" : [ {\"Name\" : \"赵芳\",\"KeyNo\" : \"\",\"Org\" : -2}],\"PublishDate\" : NumberLong(1610294400),\"IsValid\" : 1,\"XglNameAndKeyNo\" : [ {\"Name\" : \"赵芳\",\"KeyNo\" : \"\",\"Org\" : -2}],\"GlNameAndKeyNo\" : [],\"SqrNameAndKeyNo\" : [ {\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0}],\"ContentFlag\" : 1}, {\"Id\" : \"9b0c1b94585de9cb33c7559c08ee3ab0\",\"NameAndKeyNo\" : [ {\"Name\" : \"闫泽亭\",\"KeyNo\" : \"pe046ca3e162ad21673207a3a5400f99\",\"Org\" : 2}],\"CompanyInfo\" : [ {\"Name\" : \"闫泽亭\",\"KeyNo\" : \"pe046ca3e162ad21673207a3a5400f99\",\"Org\" : 2}],\"PublishDate\" : NumberLong(1610294400),\"IsValid\" : 0,\"XglNameAndKeyNo\" : [ {\"Name\" : \"闫泽亭\",\"KeyNo\" : \"pe046ca3e162ad21673207a3a5400f99\",\"Org\" : 2}],\"GlNameAndKeyNo\" : [],\"SqrNameAndKeyNo\" : [ {\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0}],\"ContentFlag\" : 1}],\"CaseList\" : [ {\"Id\" : \"6c9b3c3d026bdcd73d349c8346790946\",\"DocType\" : \"裁定日期\",\"JudgeDate\" : NumberLong(1614009600),\"IsValid\" : 1,\"ResultType\" : \"裁定结果\",\"Result\" : \"终结本次执行程序。\",\"ResultClean\" : \"终结本次执行程序。\",\"Amt\" : \"\",\"CaseType\" : \"执行裁定书\",\"ShieldCaseFlag\" : 0}],\"LianList\" : [ {\"Id\" : \"6c9b3c3d026bdcd73d349c8346790946\",\"LianDate\" : NumberLong(1586880000),\"IsValid\" : 1}],\"FyggList\" : [],\"KtggList\" : [],\"SdggList\" : [],\"PcczList\" : [],\"GqdjList\" : [],\"XjpgList\" : [],\"ZbList\" : [],\"HbcfList\" : [],\"CfgsList\" : [],\"CfxyList\" : [],\"CfdfList\" : [],\"SfpmList\" : [],\"SqtjList\" : [],\"XzcjList\" : [],\"XdpgjgList\" : [],\"XsggList\" : [],\"LatestTimestamp\" : NumberLong(1614009600)}, {\"TrialRound\" : \"恢复执行\",\"Anno\" : \"（2022）豫1729执恢46号\",\"CaseReason\" : \"\",\"Prosecutor\" : [],\"Defendant\" : [ {\"Role\" : \"被执行人\",\"Name\" : \"袁金花\",\"KeyNo\" : \"pbbec57d6989d53b60ecd8f27b7b71bf\",\"Org\" : 2,\"JR\" : \"\"}, {\"Role\" : \"被执行人\",\"Name\" : \"赵芳\",\"KeyNo\" : \"\",\"Org\" : -2,\"JR\" : \"\"}, {\"Role\" : \"被执行人\",\"Name\" : \"闫泽亭\",\"KeyNo\" : \"pe046ca3e162ad21673207a3a5400f99\",\"Org\" : 2,\"JR\" : \"\"}, {\"Role\" : \"被执行人\",\"Name\" : \"马文堂\",\"KeyNo\" : \"\",\"Org\" : -2,\"JR\" : \"\"}],\"Procuratorate\" : \"\",\"Court\" : \"新蔡县人民法院\",\"ExecuteNo\" : \"（2018）豫1729民初2129号\",\"SxList\" : [ {\"Id\" : \"2ca11897b929d1e6928e7d1ebff8979b2\",\"NameAndKeyNo\" : [ {\"Name\" : \"袁金花\",\"KeyNo\" : \"pbbec57d6989d53b60ecd8f27b7b71bf\",\"Org\" : 2}],\"PublishDate\" : NumberLong(1667923200),\"IsValid\" : 1,\"ExecuteStatus\" : \"全部未履行\",\"ActionType\" : \"有履行能力而拒不履行生效法律文书确定义务\"}, {\"Id\" : \"954ec6607772082cc118167081257d642\",\"NameAndKeyNo\" : [ {\"Name\" : \"闫泽亭\",\"KeyNo\" : \"pe046ca3e162ad21673207a3a5400f99\",\"Org\" : 2}],\"PublishDate\" : NumberLong(1667923200),\"IsValid\" : 1,\"ExecuteStatus\" : \"全部未履行\",\"ActionType\" : \"有履行能力而拒不履行生效法律文书确定义务\"}, {\"Id\" : \"9b82228ba1097415a0d113e4965879062\",\"NameAndKeyNo\" : [ {\"Name\" : \"马文堂\",\"KeyNo\" : \"\",\"Org\" : -2}],\"PublishDate\" : NumberLong(1667923200),\"IsValid\" : 1,\"ExecuteStatus\" : \"全部未履行\",\"ActionType\" : \"有履行能力而拒不履行生效法律文书确定义务\"}, {\"Id\" : \"c65f641b23c0e28a87f4f68f6be901df2\",\"NameAndKeyNo\" : [ {\"Name\" : \"赵芳\",\"KeyNo\" : \"\",\"Org\" : -2}],\"PublishDate\" : NumberLong(1667923200),\"IsValid\" : 1,\"ExecuteStatus\" : \"全部未履行\",\"ActionType\" : \"有履行能力而拒不履行生效法律文书确定义务\"}],\"ZxList\" : [ {\"Id\" : \"2ca11897b929d1e6928e7d1ebff8979b1\",\"NameAndKeyNo\" : [ {\"Name\" : \"袁金花\",\"KeyNo\" : \"pbbec57d6989d53b60ecd8f27b7b71bf\",\"Org\" : 2}],\"LianDate\" : NumberLong(1645718400),\"IsValid\" : 0,\"Biaodi\" : \"3935885\",\"SqrNameAndKeyNo\" : [ {\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0}]}, {\"Id\" : \"954ec6607772082cc118167081257d641\",\"NameAndKeyNo\" : [ {\"Name\" : \"闫泽亭\",\"KeyNo\" : \"pe046ca3e162ad21673207a3a5400f99\",\"Org\" : 2}],\"LianDate\" : NumberLong(1645718400),\"IsValid\" : 0,\"Biaodi\" : \"3935885\",\"SqrNameAndKeyNo\" : [ {\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0}]}, {\"Id\" : \"9b82228ba1097415a0d113e4965879061\",\"NameAndKeyNo\" : [ {\"Name\" : \"马文堂\",\"KeyNo\" : \"\",\"Org\" : -2}],\"LianDate\" : NumberLong(1645718400),\"IsValid\" : 0,\"Biaodi\" : \"3935885\",\"SqrNameAndKeyNo\" : [ {\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0}]}, {\"Id\" : \"c65f641b23c0e28a87f4f68f6be901df1\",\"NameAndKeyNo\" : [ {\"Name\" : \"赵芳\",\"KeyNo\" : \"\",\"Org\" : -2}],\"LianDate\" : NumberLong(1645718400),\"IsValid\" : 0,\"Biaodi\" : \"3935885\",\"SqrNameAndKeyNo\" : [ {\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0}]}],\"XgList\" : [ {\"Id\" : \"5df0a908b7eaa03762eb16ba90a0e37b\",\"NameAndKeyNo\" : [ {\"Name\" : \"闫泽亭\",\"KeyNo\" : \"pe046ca3e162ad21673207a3a5400f99\",\"Org\" : 2}],\"CompanyInfo\" : [ {\"Name\" : \"闫泽亭\",\"KeyNo\" : \"pe046ca3e162ad21673207a3a5400f99\",\"Org\" : 2}],\"PublishDate\" : NumberLong(1667923200),\"IsValid\" : 1,\"XglNameAndKeyNo\" : [ {\"Name\" : \"闫泽亭\",\"KeyNo\" : \"pe046ca3e162ad21673207a3a5400f99\",\"Org\" : 2}],\"GlNameAndKeyNo\" : [],\"SqrNameAndKeyNo\" : [ {\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0}],\"ContentFlag\" : 1}, {\"Id\" : \"7ce864270476e07e328513d4e5289ebe\",\"NameAndKeyNo\" : [ {\"Name\" : \"袁金花\",\"KeyNo\" : \"pbbec57d6989d53b60ecd8f27b7b71bf\",\"Org\" : 2}],\"CompanyInfo\" : [ {\"Name\" : \"袁金花\",\"KeyNo\" : \"pbbec57d6989d53b60ecd8f27b7b71bf\",\"Org\" : 2}],\"PublishDate\" : NumberLong(1667923200),\"IsValid\" : 1,\"XglNameAndKeyNo\" : [ {\"Name\" : \"袁金花\",\"KeyNo\" : \"pbbec57d6989d53b60ecd8f27b7b71bf\",\"Org\" : 2}],\"GlNameAndKeyNo\" : [],\"SqrNameAndKeyNo\" : [ {\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0}],\"ContentFlag\" : 1}, {\"Id\" : \"8d9eacb239ded19ecb4c1a72d3213c99\",\"NameAndKeyNo\" : [ {\"Name\" : \"赵芳\",\"KeyNo\" : \"\",\"Org\" : -2}],\"CompanyInfo\" : [ {\"Name\" : \"赵芳\",\"KeyNo\" : \"\",\"Org\" : -2}],\"PublishDate\" : NumberLong(1667923200),\"IsValid\" : 1,\"XglNameAndKeyNo\" : [ {\"Name\" : \"赵芳\",\"KeyNo\" : \"\",\"Org\" : -2}],\"GlNameAndKeyNo\" : [],\"SqrNameAndKeyNo\" : [ {\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0}],\"ContentFlag\" : 1}, {\"Id\" : \"e25c075f6bc5acdc2a95c0d5b4e59dbc\",\"NameAndKeyNo\" : [ {\"Name\" : \"马文堂\",\"KeyNo\" : \"\",\"Org\" : -2}],\"CompanyInfo\" : [ {\"Name\" : \"马文堂\",\"KeyNo\" : \"\",\"Org\" : -2}],\"PublishDate\" : NumberLong(1667923200),\"IsValid\" : 1,\"XglNameAndKeyNo\" : [ {\"Name\" : \"马文堂\",\"KeyNo\" : \"\",\"Org\" : -2}],\"GlNameAndKeyNo\" : [],\"SqrNameAndKeyNo\" : [ {\"Name\" : \"河南新蔡农村商业银行股份有限公司\",\"KeyNo\" : \"311acad1dfcae855d6a1c73cef1d2c66\",\"Org\" : 0}],\"ContentFlag\" : 1}],\"CaseList\" : [],\"LianList\" : [],\"FyggList\" : [],\"KtggList\" : [],\"SdggList\" : [],\"PcczList\" : [],\"GqdjList\" : [],\"XjpgList\" : [],\"ZbList\" : [],\"HbcfList\" : [],\"CfgsList\" : [],\"CfxyList\" : [],\"CfdfList\" : [],\"SfpmList\" : [ {\"Id\" : \"5a799a3eeb8721378c290186418162e3\",\"IsValid\" : 1,\"OwnerKeyNoArray\" : [],\"LianDate\" : NumberLong(1649952000),\"YiWu\" : \"3666120\",\"AmountUnit\" : \"元\",\"EvaluationPrice\" : \"3666120\",\"Name\" : \"袁金花、马文堂名下位于新蔡县古吕镇新华街周潢路南段路东不动产\",\"BiaoDi\" : \"袁金花、马文堂名下位于新蔡县古吕镇新华街周潢路南段路东不动产\"}, {\"Id\" : \"a1fcdda5304530b973fbbe3e494ccb17\",\"IsValid\" : 1,\"OwnerKeyNoArray\" : [],\"LianDate\" : NumberLong(1651852800),\"YiWu\" : \"3299508\",\"AmountUnit\" : \"元\",\"EvaluationPrice\" : \"3666120\",\"Name\" : \"袁金花、马文堂名下位于新蔡县古吕镇新华街周潢路南段路东不动产\",\"BiaoDi\" : \"袁金花、马文堂名下位于新蔡县古吕镇新华街周潢路南段路东不动产\"}, {\"Id\" : \"d41b9791ed46f960ed5fc6b70346838f\",\"IsValid\" : 1,\"OwnerKeyNoArray\" : [],\"LianDate\" : NumberLong(1656172800),\"YiWu\" : \"\",\"AmountUnit\" : \"元\",\"EvaluationPrice\" : \"3666120\",\"Name\" : \"变卖袁金花、马文堂名下位于新蔡县古吕镇新华街周潢路南段路东不动产\",\"BiaoDi\" : \"变卖袁金花、马文堂名下位于新蔡县古吕镇新华街周潢路南段路东不动产\"}, {\"Id\" : \"dd2852dbd5d346c4be081f665c1bcce3\",\"IsValid\" : 1,\"OwnerKeyNoArray\" : [],\"LianDate\" : NumberLong(1656172800),\"YiWu\" : \"3299508\",\"AmountUnit\" : \"元\",\"EvaluationPrice\" : \"3666120\",\"Name\" : \"袁金花、马文堂名下位于新蔡县古吕镇新华街周潢路南段路东不动产\",\"BiaoDi\" : \"袁金花、马文堂名下位于新蔡县古吕镇新华街周潢路南段路东不动产\"}],\"SqtjList\" : [],\"XzcjList\" : [],\"XdpgjgList\" : [],\"XsggList\" : [],\"LatestTimestamp\" : NumberLong(1667923200)}]}"));
    }
}
