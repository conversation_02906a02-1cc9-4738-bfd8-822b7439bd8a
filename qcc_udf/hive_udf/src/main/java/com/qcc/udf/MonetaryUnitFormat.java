package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * @Auther: lvdf
 * @Date: 2019/7/11 11:46
 * @Description:规范工商信息中的注册资本和实缴资本单位
 */
public class MonetaryUnitFormat extends UDF {
    public static String evaluate(String Str) {
        String mat = "韩元,阿富汗尼,列克,阿尔及利亚第纳尔,安道尔比塞塔,阿根廷比索,澳大利亚元,先令,巴哈马元,巴林第纳尔,塔卡,亚美尼亚达姆,巴巴多斯元,比利时法郎,百慕大元,努尔特鲁姆,玻利瓦诺,普拉,伯利兹元,所罗门群岛元,文莱元,列弗,缅元,布隆迪法郎,瑞尔,加元,佛得角埃斯库多,开曼群岛元,斯里兰卡卢比,智利比索,人民币,哥伦比亚比索,科摩罗法郎,哥斯达黎加科郎,克罗地亚库纳,古巴比索,塞浦路斯镑,捷克克朗,丹麦克朗,多米尼加比索,萨尔瓦多科郎,埃塞俄比亚比尔,纳克法,克罗姆,福克兰群岛镑,斐济元,德国马克,马克,法国法郎,吉布提法郎,达拉西,塞地,直布罗陀镑,德拉克马,格查尔,几内亚法郎,圭亚那元,古德,伦皮拉,港元,福林,冰岛克朗,印度卢比,卢比,伊朗里亚尔,伊拉克第纳尔,爱尔兰镑,新谢客尔,意大利里拉,牙买加元,日元,坚戈,约旦第纳尔,肯尼亚先令,北朝鲜圆,圆,科威特第纳尔,索姆,基普,黎巴嫩镑,罗提,拉脱维亚拉特,利比里亚元,利比亚第纳尔,立陶宛,卢森堡法郎,澳门元,马尔加什法郎,克瓦查,马来西亚林吉特,卢菲亚,马尔他里拉,乌吉亚,毛里求斯卢比,墨西哥比索,图格里克,摩尔瓦多列伊,摩洛哥迪拉姆,麦梯卡尔,阿曼里亚尔,纳米比亚元,尼泊尔卢比,荷兰盾,荷属安的列斯盾,阿鲁巴盾,瓦图,新西兰元,金科多巴,奈拉,挪威克朗,巴基斯坦卢比,巴波亚,基那,瓜拉尼,索尔,非律宾比索,葡萄牙埃斯库多,几内亚比绍比索,东帝汶埃斯库多,卡塔尔里亚尔,列伊,俄罗斯卢布,卢旺达法郎,圣赫勒拿磅,多布拉,沙特里亚尔,塞舌尔卢比,利昂,新加坡元,斯洛伐克克朗,盾,托拉尔,索马里先令,兰特,津巴布韦元,西班牙比塞塔,苏丹第纳尔,苏里南盾,里兰吉尼,瑞典克朗,瑞士法郎,叙利亚镑,铢,邦加,特立尼达和多巴哥元,UAE迪拉姆,突尼斯第纳尔,土耳其里拉,马纳特,乌干达先令,第纳尔,俄罗斯卢布,埃及镑,英镑,坦桑尼亚先令,美元,乌拉圭比索,乌兹别克斯坦苏姆,塔拉,也门里亚尔,南斯拉夫第纳尔,克瓦查,新台币,CFA法郎BEAC,东加勒比元,CFA法郎BCEAO,CFP法郎,黄金,银,铂白金,钯,索莫尼,宽扎,白俄罗斯卢布,保加利亚列弗,刚果法郎,可自由兑换标记,欧元,墨西哥发展单位,格里夫纳,Mvdol(玻利维亚),兹罗提,巴西瑞尔,发展单位,UIC法郎,黄金法郎,阿塞拜疆马纳特,新扎伊尔";
        if (Str == null || "".equals(Str)) {
            return "";
        }
        if(Str.replaceAll("[ ]","").matches("[0-9.]+")){
            return "人民币";
        }else {
            String newStr = Str.replaceAll("[\\x00-\\xff,.]", "");
            String[] arr = mat.split(",");
            if ("万".equals(newStr) || "万元".equals(newStr) || "元".equals(newStr) || "万万元".equals(newStr)) {
                return "人民币";
            } else if (newStr.contains("新台湾元")) {
                return "新台币";
            } else if (newStr.contains("港")) {
                return "港元";
            } else {
                for (int i = 0; i < arr.length; i++) {
                    if (newStr.contains(arr[i])) {
                        return arr[i];
                    }
                }
            }
        }
        if(Str.replaceAll("[\\x00-\\xff,.]", "").length() != 0){
            return "人民币";
        }
        return  "";
    }
}
