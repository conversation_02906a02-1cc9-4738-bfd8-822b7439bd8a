package com.qcc.udf.risk_graph;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 风险关系列表
 * @date 2021/3/4
 */

@Data
public class RiskRelationListEntity {
    /**
     * 数据主键
     */
    @JSONField(name = "Id")
    private String id;

    /**
     * 风险数据
     */
    @JSONField(name = "RiskData")
    private List<RiskDataInfo> riskData;

    /**
     * 统计信息-破产重组
     */
    @JSONField(name = "PcczCnt")
    private int pcczCnt;

    /**
     * 统计信息-同案限高
     */
    @JSONField(name = "TaxgCnt")
    private int taxgCnt;

    /**
     * 统计信息-申请限高
     */
    @JSONField(name = "SqxgCnt")
    private int sqxgCnt;

    /**
     * 统计信息-同案失信
     */
    @JSONField(name = "TasxCnt")
    private int tasxCnt;

    /**
     * 统计信息-出质股权
     */
    @JSONField(name = "GqczCnt")
    private int gqczCnt;

    /**
     * 统计信息-动产抵押
     */
    @JSONField(name = "DcdyCnt")
    private int dcdyCnt;

    /**
     * 统计信息-抵押土地
     */
    @JSONField(name = "TddyCnt")
    private int tddyCnt;

    /**
     * 统计信息-公示催告
     */
    @JSONField(name = "GscgCnt")
    private int gscgCnt;

    /**
     * 统计信息- 对外担保
     */
    @JSONField(name = "DwdbCnt")
    private int dwdbCnt;

    /**
     * 统计信息-民事起诉
     */
    @JSONField(name = "QsmsCnt")
    private int qsmsCnt;

    /**
     * 统计信息-行政起诉
     */
    @JSONField(name = "QsxzCnt")
    private int qsxzCnt;

    /**
     * 统计信息-经营异常
     */
    @JSONField(name = "JyycCnt")
    private int jyycCnt;

    /**
     * 统计信息-询价评估
     */
    @JSONField(name = "XjpgCnt")
    private int xjpgCnt;

    /**
     * 统计信息-欠税公告
     */
    @JSONField(name = "QsggCnt")
    private int qsggCnt;

    /**
     * 统计信息-违规处理
     */
    @JSONField(name = "WgclCnt")
    private int wgclCnt;

    /**
     * 统计信息-抽查检查
     */
    @JSONField(name = "CcjcCnt")
    private int ccjcCnt;

    /**
     * 统计信息-司法拍卖
     */
    @JSONField(name = "SfpmCnt")
    private int sfpmCnt;

    /**
     * 统计信息-股权冻结
     */
    @JSONField(name = "GqdjCnt")
    private int gqdjCnt;

    /**
     * 统计信息-环保处罚
     */
    @JSONField(name = "HbcfCnt")
    private int hbcfCnt;

    /**
     * 统计信息-税收违法
     */
    @JSONField(name = "SswfCnt")
    private int sswfCnt;

    /**
     * 统计信息-行政处罚
     */
    @JSONField(name = "XzcfCnt")
    private int xzcfCnt;


    public void cleanCount(String name, int count) {
        RiskCategoryEnum riskCategoryEnum = RiskCategoryEnum.find(name);
        switch (riskCategoryEnum) {
            case PCCZ:
                this.pcczCnt = count;
                break;
            case SQXG:
                this.sqxgCnt = count;
                break;
//            case GLXG:
            case TAXG:
                this.taxgCnt = count;
                break;
            case TASX:
                this.tasxCnt = count;
                break;
//            case SQZX:
            case QSMS:
                this.qsmsCnt = count;
                break;
            case QSXZ:
                this.qsxzCnt = count;
                break;
            case SFPM:
                this.sfpmCnt = count;
                break;
            case XJPG:
                this.xjpgCnt = count;
                break;
            case GQDJ:
                this.gqdjCnt = count;
                break;
//            case YZWF:

            case JYYC:
                this.jyycCnt = count;
                break;
            case XZCF:
                this.xzcfCnt = count;
                break;
            case HBCF:
                this.hbcfCnt = count;
                break;
            case SSWF:
                this.sswfCnt = count;
                break;
            case QSGG:
                this.qsggCnt = count;
                break;
            case WGCL:
                this.wgclCnt = count;
                break;
            case CCJC:
                this.ccjcCnt = count;
                break;
            case GQCZ:
                this.gqczCnt = count;
                break;
            case DCDY:
                this.dcdyCnt = count;
                break;
            case TDDY:
                this.tddyCnt = count;
                break;
            case DWDB:
                this.dwdbCnt = count;
                break;
            case GSCG:
                this.gscgCnt = count;
                break;
        }


    }
}
