package com.qcc.udf.casesearch_v3;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

/**
 * <AUTHOR>
 */
public class getWdid extends UDF {
    public static String evaluate(String param) {
        List<String> idSet = new LinkedList<>();
        if (StringUtils.isNotEmpty(param)){
            JSONArray array = JSONObject.parseObject(param).getJSONArray("InfoList");
            if (array != null && !array.isEmpty()){
                Iterator<Object> it = array.iterator();
                while (it.hasNext()){
                    JSONObject json = (JSONObject) it.next();
                    getInfoFromArray(json.getJSONArray("SxList"), idSet);
                    getInfoFromArray(json.getJSONArray("ZxList"), idSet);
                    getInfoFromArray(json.getJSONArray("XgList"), idSet);
                    getInfoFromArray(json.getJSONArray("CaseList"), idSet);
                    getInfoFromArray(json.getJSONArray("LianList"), idSet);
                    getInfoFromArray(json.getJSONArray("FyggList"), idSet);
                    getInfoFromArray(json.getJSONArray("KtggList"), idSet);
                    getInfoFromArray(json.getJSONArray("SdggList"), idSet);
                    getInfoFromArray(json.getJSONArray("PcczList"), idSet);
                    getInfoFromArray(json.getJSONArray("GqdjList"), idSet);
                    getInfoFromArray(json.getJSONArray("XjpgList"), idSet);
                    getInfoFromArray(json.getJSONArray("ZbList"), idSet);
                }
            }
        }
        Collections.sort(idSet);

        return String.join(",", idSet);
    }

    public static void getInfoFromArray(JSONArray array, List<String> idSet){
        if (array != null && !array.isEmpty() && array.size() > 0){
            Iterator<Object> it = array.iterator();
            while (it.hasNext()){
                JSONObject json = (JSONObject) it.next();
                idSet.add(json.getString("Id"));
            }
        }
    }

    public static void main(String[] args) {
        System.out.println(evaluate("{\"KtggCnt\":0,\"LastestDateType\":\"首次执行|案件终本日期\",\"XjpgCnt\":0,\"ZxCnt\":1,\"CfgsCnt\":0,\"LastestDate\":1575475200,\"EarliestDate\":1574092800,\"Source\":\"OT\",\"AnnoCnt\":1,\"EarliestDateType\":\"首次执行|被执行人立案日期\",\"XzcfCnt\":0,\"CompanyKeywords\":\"e906fad4aa670e80be90a9fdf2ea6f71,p5e18fa86afd996489dccd8841977aa6,世纪爱心(北京)科技发展有限公司,世纪爱心(北京)科技孵化有限公司,世纪爱心（北京）科技发展有限公司,刘杰,周耿宁\",\"AnNoList\":\"（2019）京0101执11345号\",\"GqdjCnt\":0,\"XgCnt\":1,\"Tags\":\"2,3,4,6\",\"FyggCnt\":0,\"ZbCnt\":1,\"LatestTrialRound\":\"首次执行\",\"CfdfCnt\":0,\"CaseName\":\"周耿宁与世纪爱心（北京）科技发展有限公司人事争议案件执行的案件\",\"CfxyCnt\":0,\"SxCnt\":0,\"Province\":\"BJ\",\"GroupId\":\"82d51d643a3af286db3f6f0cc2da7409\",\"LianCnt\":0,\"CaseCnt\":2,\"HbcfCnt\":0,\"PcczCnt\":0,\"Type\":1,\"CaseType\":\"执行案件\",\"ProcuratorateList\":\"\",\"CaseRole\":\"[{\\\"N\\\":\\\"\\\",\\\"O\\\":-2,\\\"P\\\":\\\"周耿宁\\\",\\\"R\\\":\\\"申请执行人\\\"},{\\\"N\\\":\\\"e906fad4aa670e80be90a9fdf2ea6f71\\\",\\\"O\\\":0,\\\"P\\\":\\\"世纪爱心（北京）科技发展有限公司\\\",\\\"R\\\":\\\"被执行人\\\"}]\",\"CaseReason\":\"人事争议案件执行\",\"CourtList\":\"北京市东城区人民法院\",\"Id\":\"d3285571f5430ddcff363ff12df532c9\",\"InfoList\":[{\"Defendant\":[{\"KeyNo\":\"e906fad4aa670e80be90a9fdf2ea6f71\",\"Role\":\"被执行人\",\"Org\":0,\"Name\":\"世纪爱心（北京）科技发展有限公司\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"北京市东城区人民法院\",\"LatestTimestamp\":1575475200,\"ZxList\":[{\"timeStamp\":1574092800,\"LianDate\":1574092800,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"周耿宁\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"e906fad4aa670e80be90a9fdf2ea6f71\",\"Org\":0,\"Name\":\"世纪爱心（北京）科技发展有限公司\"}],\"timeType\":\"被执行人立案日期\",\"Id\":\"4b3f4ccb075a44899a88082e042404331\",\"Biaodi\":\"126566\",\"IsValid\":0}],\"XzcffList\":[],\"CfdfList\":[],\"HbcfList\":[],\"CaseList\":[{\"timeStamp\":1575475200,\"JudgeDate\":1575475200,\"Amt\":\"\",\"timeType\":\"裁定日期\",\"Id\":\"55d14ab9b2bf3dc18d1a83c695b755d30\",\"ResultType\":\"裁定结果\",\"DocType\":\"执行裁定日期\",\"IsValid\":1,\"Result\":\"终结北京市东城区劳动人事争议仲裁委员会作出的京东劳人仲字[2019]第3082号裁决书的本次执行程序。\"},{\"timeStamp\":1575475200,\"JudgeDate\":1575475200,\"Amt\":\"\",\"timeType\":\"裁定日期\",\"Id\":\"ae0e3599c06b783af7c259ed180299b10\",\"ResultType\":\"裁定结果\",\"DocType\":\"执行裁定日期\",\"IsValid\":1,\"Result\":\"终结北京市东城区劳动人事争议仲裁委员会作出的京东劳人仲字[2019]第3082号裁决书的本次执行程序。\"}],\"TrialRound\":\"首次执行\",\"Prosecutor\":[{\"KeyNo\":\"\",\"Role\":\"申请执行人\",\"Org\":-2,\"Name\":\"周耿宁\"}],\"ZbList\":[{\"timeStamp\":1575475200,\"FailureAct\":\"126566\",\"ExecuteObject\":\"126566\",\"JudgeDate\":1575475200,\"NameAndKeyNo\":[{\"KeyNo\":\"e906fad4aa670e80be90a9fdf2ea6f71\",\"Org\":0,\"Name\":\"世纪爱心（北京）科技发展有限公司\"}],\"timeType\":\"案件终本日期\",\"Id\":\"4b3f4ccb075a44899a88082e04240433\",\"IsValid\":1}],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2019）京0101执11345号\",\"CaseType\":\"执行案件\",\"LianList\":[],\"XgList\":[{\"timeStamp\":1575302400,\"PublishDate\":1575302400,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"周耿宁\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"e906fad4aa670e80be90a9fdf2ea6f71\",\"Org\":0,\"Name\":\"世纪爱心（北京）科技发展有限公司\"}],\"timeType\":\"限制高消费发布日期\",\"XglNameAndKeyNo\":[{\"KeyNo\":\"e906fad4aa670e80be90a9fdf2ea6f71\",\"Org\":0,\"Name\":\"世纪爱心（北京）科技发展有限公司\"}],\"Id\":\"392e7aa4f5311f65759f7f2a1a5c8340\",\"CompanyInfo\":[{\"KeyNo\":\"e906fad4aa670e80be90a9fdf2ea6f71\",\"Org\":0,\"Name\":\"世纪爱心（北京）科技发展有限公司\"}],\"GlNameAndKeyNo\":[{\"KeyNo\":\"p5e18fa86afd996489dccd8841977aa6\",\"Org\":2,\"Name\":\"刘杰\"}],\"IsValid\":1}],\"CaseReason\":\"人事争议案件执行\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]}],\"SdggCnt\":0} "));
    }
}
