package com.qcc.udf.tag.keywordEnum;

import com.qcc.udf.tag.tagEnum.LaborDisputeEnum;

/**
 * 劳动争议-关键词
 */
public enum LDKeywordMatchEnum {
    //LD02
    LDGX(".*劳动关系.*", LaborDisputeEnum.LD02),
    LDHT(".*劳动合同.*", LaborDisputeEnum.LD02),
    LDF(".*劳动法.*", LaborDisputeEnum.LD02),
    HT(".*合同.*", LaborDisputeEnum.LD02),
    WY(".*违约.*", LaborDisputeEnum.LD02),
    BC(".*补偿.*", LaborDisputeEnum.LD02),

    //LD04
    GSPC(".*工伤.*", LaborDisputeEnum.LD04),

    //LD05
    TG(".*童工.*", LaborDisputeEnum.LD05),

    //LD06
    TQ(".*拖欠.*", LaborDisputeEnum.LD06),
    WFF(".*未发放.*", LaborDisputeEnum.LD06),
    WZF(".*未支付.*", LaborDisputeEnum.LD06),
    LDBZ_TQ(".*劳动保障.*拖欠.*", LaborDisputeEnum.LD06),
    W_FF(".*未.*发放.*", LaborDisputeEnum.LD06),
    W_ZF(".*未.*支付.*", LaborDisputeEnum.LD06),
    TQGZ_YZWF("{\"value\":\"拖欠工资社保税费\",\"key\":\"6\"}", LaborDisputeEnum.LD06),

    //LD07
    SB(".*社保.*", LaborDisputeEnum.LD07),
    BX(".*保险.*", LaborDisputeEnum.LD07),

    //LD08

    //LD99
    DEFAULT("其他", LaborDisputeEnum.LD99),
    ;


    private String keyword;
    private LaborDisputeEnum laborDisputeEnum;

    LDKeywordMatchEnum(String keyword, LaborDisputeEnum laborDisputeEnum) {
        this.keyword = keyword;
        this.laborDisputeEnum = laborDisputeEnum;
    }

    public String getKeyword() {
        return keyword;
    }

    public LaborDisputeEnum getLaborDisputeEnum() {
        return laborDisputeEnum;
    }

    public LaborDisputeEnum getTagEnum() {
        return getLaborDisputeEnum();
    }
}
