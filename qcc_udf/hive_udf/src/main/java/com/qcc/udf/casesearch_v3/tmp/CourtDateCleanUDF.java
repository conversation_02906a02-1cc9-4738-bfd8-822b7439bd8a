package com.qcc.udf.casesearch_v3.tmp;

import com.qcc.udf.enums.PunishGovEnum;
import com.qcc.udf.tax.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;
import parquet.Strings;

import java.util.Arrays;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Auther: zhanqgiang
 * @Date: 2020/11/19 10:00
 * @Description: 案号简单清洗
 */
public class CourtDateCleanUDF extends UDF {
    public static final String date_SPLIT_REGEX = "定于|本院.?于|本案.?于|开庭时间.?为?";
    private final static String REGEXP_BIRTH_ALL_DAY = "(\\d{2,4}年|[零一二三四五六七八九十\\d]{4}年)(\\d{0,2}月?|[一二三四五六七八九十]{0,2}月?)(\\d{0,2}|[零一二三四五六七八九十\\d]{0,3})日?(上午|下午)?(\\d{0,2})时?(\\d{0,2})分?";
    public static final Pattern REGEXP_BIRTH_ALL_DAY_PATTERN = Pattern.compile(REGEXP_BIRTH_ALL_DAY);

    public static String evaluate(String content) {
        return extractDate(content);
    }


    /**
     * 从文本中提取出时间，并转换为LocalDateTime格式  yyyy年mm月dd日格式
     *
     * @param content
     * @return
     */
    public static String extractDate(String content) {
        if (StringUtils.isBlank(content)){
            return "";
        }
        String extractContent = "";
        String[] split = content.split(date_SPLIT_REGEX);
        if (split.length > 1) {
            extractContent = split[1];
        }
        Matcher m = REGEXP_BIRTH_ALL_DAY_PATTERN.matcher(extractContent);
        String publicDate = "";
        if (m.find()) {
            publicDate = m.group();
            publicDate = publicDate.replace("上午", "")
                    .replace("下午", "");
        }

        if (StringUtils.isNotBlank(publicDate)){
            publicDate = CommonDateCleanUtil.cleanDate(publicDate, DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS);
        }
        return publicDate;
    }


    public static void main(String[] args) {
        String content = "东莞市金美克能源有限公司、深圳市超境界新能源有限公司：本院受理原告深圳市华美兴泰科技股份有限公司诉被告深圳市伏特云商有限公司，你们（2019）粤0309民初1291号产品责任纠纷一案，原告诉讼请求为：1、请求法院判令三被告赔偿原告损失共计1336900.19元；2、请求法院判令三被告对上述损失承担连带赔偿责任；3、本案诉讼费用由三被告承担。本案标的额为1336900.19元。因被告你们下落不明，现依法向你们公告送达起诉状副本、证据副本、应诉通知书、开庭传票、合议庭组成人员通知书等。自公告之日起经过60日，即视为送达。提出答辩状和举证的期限为公告期满后的15日内，并定于2019年6月24日下午14时30分在观澜法庭第二审判庭开庭审理，逾期将依法缺席裁判。";
//        String content = "李蒙建，男，1971年1月13日出生，汉族、住浙江省绍兴市柯桥区稽东镇高阳村谢家湾161号。本院在审理原告刘乾红诉被告李蒙建买卖合同纠纷一案，因你下落不明，不能直接送达有关诉讼法律文书，现依照《中华人民共和国民事诉讼法》的有关规定向你公告送达起诉状副本、应诉通知书、举证通知书、合议庭组成人员通知书、开庭传票、民事诉讼须知、权利义务告知书、风险提示书、上网告知书、廉政监督卡、民事裁定书。本院于2019年9月23日10时00分在本院第十四审判庭公开开庭进行审理。自公告发出之日起经过60日，即视为送达。提出答辩状的期限为公告送达期满后的15日。举证期限为公告期满后的30日内。逾期本院则将依法判决。";
        content = "唐**：原告锐拓（杭州）互联网金融信息服务有限公司诉你民间借贷纠纷一案，因你下落不明，现依法向你公告送达起诉状副本、应诉通知书、举证通知书、风险提示书、廉政监督卡、民事诉讼须知、外网查询告知书、告知合议庭组成人员通知书及开庭传票。自本公告发出之日起经过60日即视为送达。提出答辩状的期限为公告送达期满后的15日内。举证期限为公告送达后的30日内。本案定于2017年9月21日9时00分在淳安县人民法院第三法庭公开开庭审理。逾期本院将依法缺席判决。";
        content = "刘讳：本院于2016年7月14日所受理的李龙全诉你买卖合同纠纷一案，因你下落不明，本院用其他方式无法向你送达，现依法向你公告送达起诉状副本（原告诉请法院判令解除原、被告双方之间的买卖合同；被告返还原告已支付的货款2951095元，并向原告支付资金占用利息212437.84元，按照人民银行同期同类基准贷款利率从2015年2月11日起暂计至起诉之日，实际支付至货款本金及利息付清之日止；本案诉讼费、保全费由被告承担）、应诉通知书、举证通知书、开庭传票、民事裁定书。自本公告发出之日起经过60日即视为送达。提出答辩状的期限和举证期限分别为公告送达期满后的15日和30日内。并定于举证期满后的第3日上午10时（遇节假日顺延）在本院西航港法庭公开审理此案，逾期将依法缺席判决。 ";
        content = "广东省东莞市第二人民法院 公告 (2020)粤1972民初11328号之一 东莞市虎门罗才枢服装店(统一社会信用代码为92441900MA52XHDA99): 本院受理原告邓智燕诉你与文晓云、罗才枢与买卖合同纠纷一案,因你下落不明,无法直接向你送达诉讼文书,依照《中华人民共和国民事诉讼法》第九十二条的规定,向你公告送达当事人本人出庭告知书、诉讼风险提示书、追加被告、变更诉讼请求申请书、参加诉讼通知书、应诉通知书、廉政与作风评价卡、审限告知书、诉讼权利义务告知书、上诉须知、举证通知书、举证须知、当事人确认送达地址及方式告知(附诉讼文书送达地址、送达方式确认书)、开庭传票(2020.12.24)、民事裁定书(转普)、合议庭成员告知书、民事起诉状及证据副本,原告提交了微信聊天记录、交易单等。原告向本院提出诉讼请求:1.判令三被告立即归还原告剩余货款4850元;2.判令三被告承担本案诉讼费。自本公告发出之日起,经六十日,即视为送达。提出答辩状的期限为公告送达期满后的十五日。举证的期限为公告送达期满后的十五日。开庭时间为2020年12月24日9时30分,地点为东莞市第二人民法院虎门人民法庭。无正当理由逾期未提供证据,视为放弃举证权利,承担举证不能的法律后果。无正当理由拒不到庭参加诉讼,本院将依法判决。 特此公告。 二○二○年十月九日";
        content = "广 州 市 海 珠 区 人 民 法 院 公  告 (2021)粤0105民初28649号 陈卫平:   本院受理原告魏月金诉被告陈卫平(住广东省广州市海珠区****之一前座301房,公民身份号码4401121966****0027)、第三人魏寿庆赡养纠纷一案,原告的诉讼请求如下:1、请求判令被告承担原告的生活费即每月给付原告生活费2000元,按月支付,于每月5日之前支付当月生活费;2、请求判令被告承担应承担而未承担从2012年1月始至2021年10月期间的原告的生活费合计162000元(每月生活费用按1500元计付);3、请求判令被告承担原告在判决生效之后病时必要的全部医药费、护理费;4、本案诉讼费由被告承担。  因你下落不明,现依法向你公告送达起诉书副本、应诉通知书、举证须知、证据交换通知书、证据材料、廉政监督卡、开庭传票等。限你自公告之日起三十日内来本院民一庭领取上述文件,逾期即视为送达。提出答辩状的期限为公告期满后的十五日内,举证期限为答辩期满后的七日内。本案将于2022年3月14日10时45分在本院第16法庭适用一审简易程序公开开庭审理,逾期不到庭则依法缺席审理,并依法在大洋网公告送达民事判决书。 二〇二二年一月十一日 联系人:刘伊莎、麦静华电话:83005455";
        content = "广东省深圳前海合作区人民法院 公 告 深圳市一健科技开发有限公司: 关于本院受理原告鑫盛源京港(北京)投资控股有限公司诉被告深圳市一健科技开发有限公司、陈银彬借款合同纠纷一案,案号为(2019)粤0391民初1968号。因你方下落不明，通过其他方式无法送达，依照《中华人民共和国民事诉讼法》第九十二条的规定，向你方公告送达诉讼权利义务告知书、诉讼风险告知书、送达地址确认书、调解温馨提示、起诉状副本、证据材料、举证通知书、应诉通知书及传票等。自本公告发出之日起，经过六十日，深圳市一健科技开发有限公司即视为送达，提出答辩状的期限为公告期满后的十五日内，举证期限为公告期满后的三十日内。开庭时间定于2019年10月18日14时00分，开庭地点为本院第五审判庭，请准时到庭，逾期则依法裁判。 特此公告 二〇一九年七月九日";
        System.out.println(extractDate(content));
    }

}
