package com.qcc.udf.cpws.casesearch_v2;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.cpws.ExtractCaseTrialRoundUDF;
import com.qcc.udf.cpws.ExtractCaseTypeUDF;
import com.qcc.udf.cpws.ExtractCourtNameFromCaseNoUDF;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

public class GetDetailInfoZXV2UDF extends UDF {

    public String evaluate(List<String> sourceSxList, List<String> sourceZxList, List<String> sourceXgList,
                           List<String> sourceCaseList, List<String> sourcePcczList, List<String> sourceZbList,
                           List<String> sourceXjpgList, List<String> sourceGqdjList, List<String> sourceSdggList,
                           List<String> sourceFyggList, List<String> sourceKtggList, List<String> sourceLianList,
                           List<String> sourceHbcfList, List<String> sourceCfgsList, List<String> sourceCfxyList, List<String> sourceCfdfList) {
        //排序
        Collections.sort(sourceSxList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceZxList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceXgList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceCaseList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourcePcczList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceZbList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceXjpgList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceGqdjList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceSdggList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceFyggList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceKtggList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceLianList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceHbcfList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceCfgsList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceCfxyList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });
        Collections.sort(sourceCfdfList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.length()-o2.length();
            }
        });

        LawSuitV2Entity inputLawSuitEntity = new LawSuitV2Entity();
        inputLawSuitEntity.setSxList(sourceSxList);
        inputLawSuitEntity.setZxList(sourceZxList);
        inputLawSuitEntity.setXgList(sourceXgList);
        inputLawSuitEntity.setCaseList(sourceCaseList);
        inputLawSuitEntity.setPcczList(sourcePcczList);
        inputLawSuitEntity.setZbList(sourceZbList);
        inputLawSuitEntity.setXjpgList(sourceXjpgList);
        inputLawSuitEntity.setGqdjList(sourceGqdjList);
        inputLawSuitEntity.setSdggList(sourceSdggList);
        inputLawSuitEntity.setFyggList(sourceFyggList);
        inputLawSuitEntity.setKtggList(sourceKtggList);
        inputLawSuitEntity.setLianList(sourceLianList);
        inputLawSuitEntity.setHbcfList(sourceHbcfList);
        inputLawSuitEntity.setCfgsList(sourceCfgsList);
        inputLawSuitEntity.setCfxyList(sourceCfxyList);
        inputLawSuitEntity.setCfdfList(sourceCfdfList);
        List<LawSuitV2Entity> lawSuitEntityList = CommonV2Util.getLawSuitEntityList(inputLawSuitEntity, "zx");

        JSONArray detailInfoArray = new JSONArray();
        for (LawSuitV2Entity lawSuitEntity : lawSuitEntityList) {
            List<String> sxList = lawSuitEntity.getSxList();
            List<String> zxList = lawSuitEntity.getZxList();
            List<String> xgList = lawSuitEntity.getXgList();
            List<String> caseList = lawSuitEntity.getCaseList();
            List<String> pcczList = lawSuitEntity.getPcczList();
            List<String> zbList = lawSuitEntity.getZbList();
            List<String> xjpgList = lawSuitEntity.getXjpgList();
            List<String> gqdjList = lawSuitEntity.getGqdjList();
            List<String> sdggList = lawSuitEntity.getSdggList();
            List<String> fyggList = lawSuitEntity.getFyggList();
            List<String> ktggList = lawSuitEntity.getKtggList();
            List<String> lianList = lawSuitEntity.getLianList();
            List<String> hbcfList = lawSuitEntity.getHbcfList();
            List<String> cfgsList = lawSuitEntity.getCfgsList();
            List<String> cfxyList = lawSuitEntity.getCfxyList();
            List<String> cfdfList = lawSuitEntity.getCfdfList();
            Set<String> provinceCodeSet = CommonV2Util.collectProvinceCode(sxList, zxList, xgList, caseList,
                    pcczList, zbList, xjpgList, gqdjList, sdggList, fyggList, ktggList, lianList, hbcfList,
                    cfgsList, cfxyList, cfdfList);
            for (String provinceCode : provinceCodeSet) {
                // provinceCode为空字符传则跳过
                if (provinceCode.equals("")) {
                    continue;
                }

                JSONObject result = new JSONObject();
                try {
                    /**
                     * 各审理程序列表对应字段
                     */
                    // 审理程序总数集合
                    Set<String> caseNoSet = new LinkedHashSet<>();
                    // 搜索关键字集合
                    Set<String> searchWordSet = new TreeSet<>();
                    // 所有时间节点的map集合（key-> 时间戳; value->表示当前的节点状态，审判程序 + "维度时间节点描述文案"(被执行人/失信人/限制高消费)发布日期 或 判决日期 或 裁定日期等）
                    Map<Long, String> trialRoundDateNodeMap = new HashMap<>();

                    // 案号分组
                    Map<String, JSONObject> anNoMap = new LinkedHashMap<>();
                    Set<String> sxIdSet = new LinkedHashSet<>();
                    Set<String> zxIdSet = new LinkedHashSet<>();
                    Set<String> xgIdSet = new LinkedHashSet<>();
                    Set<String> caseIdSet = new LinkedHashSet<>();
                    Set<String> pcczIdSet = new LinkedHashSet<>();
                    Set<String> zbIdSet = new LinkedHashSet<>();
                    Set<String> xjpgIdSet = new LinkedHashSet<>();
                    Set<String> gqdjIdSet = new LinkedHashSet<>();
                    Set<String> sdggIdSet = new LinkedHashSet<>();
                    Set<String> fyggIdSet = new LinkedHashSet<>();
                    Set<String> ktggIdSet = new LinkedHashSet<>();
                    Set<String> lianIdSet = new LinkedHashSet<>();
                    Set<String> hbcfIdSet = new LinkedHashSet<>();
                    Set<String> cfgsIdSet = new LinkedHashSet<>();
                    Set<String> cfxyIdSet = new LinkedHashSet<>();
                    Set<String> cfdfIdSet = new LinkedHashSet<>();

                    dataClean(provinceCode, sxList, 1, anNoMap, caseNoSet, sxIdSet, searchWordSet, trialRoundDateNodeMap);
                    dataClean(provinceCode, zxList, 2, anNoMap, caseNoSet, zxIdSet, searchWordSet, trialRoundDateNodeMap);
                    dataClean(provinceCode, xgList, 3, anNoMap, caseNoSet, xgIdSet, searchWordSet, trialRoundDateNodeMap);
                    dataClean(provinceCode, caseList, 4, anNoMap, caseNoSet, caseIdSet, searchWordSet, trialRoundDateNodeMap);
                    dataClean(provinceCode, pcczList, 5, anNoMap, caseNoSet, pcczIdSet, searchWordSet, trialRoundDateNodeMap);
                    dataClean(provinceCode, zbList, 6, anNoMap, caseNoSet, zbIdSet, searchWordSet, trialRoundDateNodeMap);
                    dataClean(provinceCode, xjpgList, 7, anNoMap, caseNoSet, xjpgIdSet, searchWordSet, trialRoundDateNodeMap);
                    dataClean(provinceCode, gqdjList, 8, anNoMap, caseNoSet, gqdjIdSet, searchWordSet, trialRoundDateNodeMap);
                    dataClean(provinceCode, sdggList, 9, anNoMap, caseNoSet, sdggIdSet, searchWordSet, trialRoundDateNodeMap);
                    dataClean(provinceCode, fyggList, 10, anNoMap, caseNoSet, fyggIdSet, searchWordSet, trialRoundDateNodeMap);
                    dataClean(provinceCode, ktggList, 11, anNoMap, caseNoSet, ktggIdSet, searchWordSet, trialRoundDateNodeMap);
                    dataClean(provinceCode, lianList, 12, anNoMap, caseNoSet, lianIdSet, searchWordSet, trialRoundDateNodeMap);
                    dataClean(provinceCode, hbcfList, 13, anNoMap, caseNoSet, hbcfIdSet, searchWordSet, trialRoundDateNodeMap);
                    dataClean(provinceCode, cfgsList, 14, anNoMap, caseNoSet, cfgsIdSet, searchWordSet, trialRoundDateNodeMap);
                    dataClean(provinceCode, cfxyList, 15, anNoMap, caseNoSet, cfxyIdSet, searchWordSet, trialRoundDateNodeMap);
                    dataClean(provinceCode, cfdfList, 16, anNoMap, caseNoSet, cfdfIdSet, searchWordSet, trialRoundDateNodeMap);

                    // 按照案号显示数据
                    JSONArray array = new JSONArray();
                    for (String str : caseNoSet) {
                        /**
                         * 兼容ms案件中的infoList项
                         */
                        JSONObject jsonObj = anNoMap.get(str);
                        if (jsonObj != null) {
                            jsonObj = CommonV2Util.addExternalFieldToJsonStruct(jsonObj);
                            Long latestTimestamp = CommonV2Util.getLatestTimestampFromInfoListItem(jsonObj);
                            jsonObj.put("LatestTimestamp", latestTimestamp);
                            array.add(jsonObj);
                        }
                    }
                    result.put("InfoList", array);

                    /**
                     * 3.案件统计字段
                     */
                    // 最新审理程序
                    result.put("LatestTrialRound", CommonV2Util.getLatestTrialRoundFromInfoList(array));
                    // 审理程序总数
                    result.put("AnnoCnt", caseNoSet.size());
                    // 关联裁判文书数
                    result.put("CaseCnt",  caseIdSet.size());
                    // 关联被执行数
                    result.put("ZxCnt", zxIdSet.size());
                    // 关联失信数
                    result.put("SxCnt", sxIdSet.size());
                    // 关联限高数
                    result.put("XgCnt", xgIdSet.size());
                    // 关联破产重整公告数
                    result.put("PcczCnt", pcczIdSet.size());
                    // 关联终本案件数
                    result.put("ZbCnt", zbIdSet.size());
                    // 关联询价评估数
                    result.put("XjpgCnt", xjpgIdSet.size());
                    // 关联股权冻结数
                    result.put("GqdjCnt", gqdjIdSet.size());
                    // 关联送达公告数
                    result.put("SdggCnt", sdggIdSet.size());
                    // 关联法院公告数
                    result.put("FyggCnt", fyggIdSet.size());
                    // 关联开庭公告数
                    result.put("KtggCnt", ktggIdSet.size());
                    // 关联立案信息数
                    result.put("LianCnt", lianIdSet.size());
                    // 关联环保处罚数
                    result.put("HbcfCnt", hbcfIdSet.size());
                    // 关联行政处罚（工商）
                    result.put("CfgsCnt", cfgsIdSet.size());
                    // 关联行政处罚（信用中国）
                    result.put("CfxyCnt", cfxyIdSet.size());
                    // 关联行政处罚（地方）
                    result.put("CfdfCnt", cfdfIdSet.size());

                    /**
                     * 案件基础字段
                     */
                    // 分组法院信息
                    result.put("GroupCourt", lawSuitEntity.getCourt());
                    // 所在省份编码
                    result.put("Province", provinceCode);
                    // 案件名称
                    result.put("CaseName", CommonV2Util.getCaseNameFromInfoList(array, "zx"));
                    // 案件类型
                    result.put("CaseType", "执行案件");
                    // 关联的公司或个人信息
                    result.put("CompanyKeywords", CommonV2Util.getCompanyKeywordsFromSearchWordSet(searchWordSet));
                    // 相关案号
                    result.put("AnNoList", CommonV2Util.getKeywordsFromInfoList(array, "AnNo"));
                    // 列表中的案由
                    result.put("CaseReason", CommonV2Util.getCaseReasonFromInfoList(array));
                    // 列表中的案件身份
                    result.put("CaseRole", CommonV2Util.getCaseRoleFromInfoList(array));
                    // 相关法院
                    result.put("CourtList", CommonV2Util.getKeywordsFromInfoList(array, "Court"));
                    // 相关检察院
                    result.put("ProcuratorateList", CommonV2Util.getKeywordsFromInfoList(array, "Procuratorate"));

                    Map<Long, String> sortedDateNodeMap = new LinkedHashMap<>();
                    trialRoundDateNodeMap.entrySet().stream()
                            .sorted(Map.Entry.comparingByKey())
                            .forEachOrdered(e -> sortedDateNodeMap.put(e.getKey(), e.getValue()));

                    result.put("EarliestDate", -1L);
                    result.put("EarliestDateType", "");
                    result.put("LastestDate", -1);
                    result.put("LastestDateType", "");

                    Long earliestDate = -1L;
                    String earliestDateType = "";
                    Long lastestDate = -1L;
                    String lastestDateType = "";
                    boolean flag = true;
                    for (Map.Entry<Long, String> sortedDateNodeEntry : sortedDateNodeMap.entrySet()) {
                        if (flag) {
                            earliestDate = sortedDateNodeEntry.getKey();
                            earliestDateType = sortedDateNodeEntry.getValue();
                            flag = false;
                        }
                        lastestDate = sortedDateNodeEntry.getKey();
                        lastestDateType = sortedDateNodeEntry.getValue();
                    }
                    result.put("EarliestDate", earliestDate);
                    result.put("EarliestDateType", CommonV2Util.getDataTypeWithoutTrialRound(earliestDateType));
                    if (sortedDateNodeMap.size() > 1) {
                        result.put("LastestDate", lastestDate);
                        result.put("LastestDateType", CommonV2Util.getDataTypeWithoutTrialRound(lastestDateType));
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                detailInfoArray.add(result);
            }
        }

        JSONArray resDetailInfoJSONArray = new JSONArray();
        Iterator iterator = detailInfoArray.iterator();
        while (iterator.hasNext()) {
            try {
                JSONObject jsonObject = (JSONObject) iterator.next();
                if (jsonObject.getLong("AnnoCnt") > 0) {
                    resDetailInfoJSONArray.add(jsonObject);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return resDetailInfoJSONArray.toJSONString();
    }

    /**
     * @param provinceCode
     * @param infoList
     * @param type 1失信 2被执行 3限高 4裁判文书 5破产重整 6终本案件 7询价评估 8股权冻结 9送达公告 10法院公告 11开庭公告 12立案信息
     *             13环保处罚 14行政处罚（工商） 15行政处罚（信用中国） 16行政处罚（地方）
     * @param anNoMap
     * @param caseNoSet
     * @param idSet
     * @param searchWordSet
     * @param trialRoundDateNodeMap
     */
    public static void dataClean(String provinceCode, List<String> infoList, int type, Map<String, JSONObject> anNoMap,
                                 Set<String> caseNoSet, Set<String> idSet, Set<String> searchWordSet, Map<Long, String> trialRoundDateNodeMap){
        // 编辑数据
        if (infoList != null && infoList.size() > 0){
            Iterator it = infoList.iterator();
            while(it.hasNext()){
                JSONObject json = CommonV2Util.getJsonObject(it.next());
                if (json == null || json.isEmpty()){
                    continue;
                }
                // 过滤各维度的涉诉历史记录
                /*if (json.containsKey("isvalid") && json.getInteger("isvalid") != 1) {
                    continue;
                }*/
                // 过滤掉非指定省份编码的记录（特殊情况：当某个维度没有"province"字段时，参与相同caseNo下所有省份的匹配中）
                if (json.containsKey("province") && !json.getString("province").equals(provinceCode)) {
                    continue;
                }

                // 获取案号
                String anNo = "";
                if (type == 1 || type == 2 || type == 5 || type == 6 || type == 9 || type == 11 || type == 12){
                    anNo = CommonV2Util.full2Half(json.getString("anno"));
                } else if(type == 3 || type == 4 || type == 7){
                    anNo = CommonV2Util.full2Half(json.getString("caseno"));
                } else if (type == 8) {
                    anNo = CommonV2Util.full2Half(json.getString("executionnoticenum"));
                } else if (type == 10) {
                    // 法院公告会存在多个caseno，做过滤处理
                    Set<String> annoSet = Arrays.stream(CommonV2Util.full2Half(json.getString("anno")).split(","))
                            .filter(StringUtils::isNotBlank)
                            .filter(e -> new ExtractCaseTypeUDF().evaluate(e).equals("执行类案件"))
                            .collect(Collectors.toSet());
                    anNo = StringUtils.join(annoSet, ",");
                } else if (type == 13 || type == 14 || type == 15 || type == 16) {
                    if (anNoMap != null && anNoMap.size() > 0) {
                        Optional<String> anNoOption = anNoMap.keySet().stream().filter(StringUtils::isNotBlank).findFirst();
                        anNo = anNoOption.get();
                    }
                }

                // 过滤掉案号没有对应到执行案件类型的记录（该规则不适用于环保处罚和行政处罚维度）
                if (type <= 12) {
                    if (!new ExtractCaseTypeUDF().evaluate(anNo).equals("执行类案件")) {
                        continue;
                    }
                }

                anNo = anNo.split("之")[0];
                Set<String> anNoSet = Arrays.stream(anNo.split(",")).collect(Collectors.toSet());
                caseNoSet.add(String.join(",",anNoSet));

                // 部分字段的汇总逻辑
                try {
                    List<String> companyNameList = new ArrayList<>();
                    if (type == 3) {
                        companyNameList.add(json.getString("personname"));
                        companyNameList.add(json.getString("personid"));
                        companyNameList.add(json.getString("keyno"));
                        companyNameList.add(json.getString("companyname"));
                    } else if (type == 6) {
                        companyNameList.add(json.getString("name"));
                        companyNameList.add(json.getString("keyno"));
                    } else if (type == 8) {
                        companyNameList.add(json.getString("companyname"));
                        companyNameList.add(json.getString("keyno"));
                    } else if (type == 10) {
                        JSONArray jsonArray = JSONArray.parseArray(json.getString("nameandkeyno"));
                        for (int i = 0; i < jsonArray.size(); i++) {
                            JSONObject jsonObject = jsonArray.getJSONObject(i);
                            companyNameList.add(jsonObject.getString("Name"));
                            companyNameList.add(jsonObject.getString("KeyNo"));
                        }
                    } else if (type == 12) {
                        companyNameList = Arrays.stream(json.getString("companykeywords").split(","))
                                .collect(Collectors.toList());
                    } else {
                        companyNameList = Arrays.stream(json.getString("companynames").split(","))
                                .collect(Collectors.toList());
                    }

                    // 汇总关联公司或个人信息
                    for (String companyName : companyNameList) {
                        searchWordSet.add(companyName);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

                // 判断该案号是否已经存在
                JSONObject jsonObject = new JSONObject();
                JSONArray itemArray = new JSONArray();
                JSONObject itemJson = new JSONObject();

                // 不存在，则创建新的对象
                if (!anNoMap.keySet().contains(anNo)){
                    // 列表数据
                    itemJson = editItemJson(json, type, trialRoundDateNodeMap, anNo);
                }else{
                    // 存在则获取原有列表，进行数据补充
                    jsonObject = anNoMap.get(anNo);
                    // 列表数据
                    if (type == 1) {
                        itemArray = jsonObject.getJSONArray("SxList");
                    } else if (type == 2) {
                        itemArray = jsonObject.getJSONArray("ZxList");
                    } else if (type == 3) {
                        itemArray = jsonObject.getJSONArray("XgList");
                    } else if (type == 4) {
                        itemArray = jsonObject.getJSONArray("CaseList");
                    } else if (type == 5) {
                        itemArray = jsonObject.getJSONArray("PcczList");
                    } else if (type == 6) {
                        itemArray = jsonObject.getJSONArray("ZbList");
                    } else if (type == 7) {
                        itemArray = jsonObject.getJSONArray("XjpgList");
                    } else if (type == 8) {
                        itemArray = jsonObject.getJSONArray("GqdjList");
                    } else if (type == 9) {
                        itemArray = jsonObject.getJSONArray("SdggList");
                    } else if (type == 10) {
                        itemArray = jsonObject.getJSONArray("FyggList");
                    } else if (type == 11) {
                        itemArray = jsonObject.getJSONArray("KtggList");
                    } else if (type == 12) {
                        itemArray = jsonObject.getJSONArray("LianList");
                    } else if (type == 13) {
                        itemArray = jsonObject.getJSONArray("HbcfList");
                    } else if (type == 14) {
                        itemArray = jsonObject.getJSONArray("CfgsList");
                    } else if (type == 15) {
                        itemArray = jsonObject.getJSONArray("CfxyList");
                    } else if (type == 16) {
                        itemArray = jsonObject.getJSONArray("CfdfList");
                    }
                    if (!idSet.contains(json.getString("id"))){
                        itemJson = editItemJson(json, type, trialRoundDateNodeMap, anNo);
                    }
                }
                idSet.add(json.getString("id"));
                itemArray = itemArray == null ? new JSONArray() : itemArray;
                if (itemJson.size() > 0) {
                    itemArray.add(itemJson);
                }
                if (type == 1) {
                    jsonObject.put("SxList", itemArray);
                } else if (type == 2) {
                    jsonObject.put("ZxList", itemArray);
                } else if (type == 3) {
                    jsonObject.put("XgList", itemArray);
                } else if (type == 4) {
                    jsonObject.put("CaseList", itemArray);
                } else if (type == 5) {
                    jsonObject.put("PcczList", itemArray);
                } else if (type == 6) {
                    jsonObject.put("ZbList", itemArray);
                } else if (type == 7) {
                    jsonObject.put("XjpgList", itemArray);
                } else if (type == 8) {
                    jsonObject.put("GqdjList", itemArray);
                } else if (type == 9) {
                    jsonObject.put("SdggList", itemArray);
                } else if (type == 10) {
                    jsonObject.put("FyggList", itemArray);
                } else if (type == 11) {
                    jsonObject.put("KtggList", itemArray);
                } else if (type == 12) {
                    jsonObject.put("LianList", itemArray);
                } else if (type == 13) {
                    jsonObject.put("HbcfList", itemArray);
                } else if (type == 14) {
                    jsonObject.put("CfgsList", itemArray);
                } else if (type == 15) {
                    jsonObject.put("CfxyList", itemArray);
                } else if (type == 16) {
                    jsonObject.put("CfdfList", itemArray);
                }

                jsonObject.put("AnNo", anNo);
                jsonObject.put("TrialRound", new ExtractCaseTrialRoundUDF().evaluate(anNo));
                if (!jsonObject.containsKey("CaseReason")) {
                    jsonObject.put("CaseReason", "");
                }
                if (!jsonObject.containsKey("Prosecutor")) {
                    jsonObject.put("Prosecutor", new JSONArray());
                }
                if (!jsonObject.containsKey("Defendant")) {
                    jsonObject.put("Defendant", new JSONArray());
                }
                if (!jsonObject.containsKey("Court")) {
                    jsonObject.put("Court", new ExtractCourtNameFromCaseNoUDF().evaluate(anNo));
                }
                if (!jsonObject.containsKey("Procuratorate")) {
                    jsonObject.put("Procuratorate", "");
                }
                if (!jsonObject.containsKey("ExecuteNo")) {      // 执行依据文书号
                    jsonObject.put("ExecuteNo", "");
                }

                if (type == 4) {    // 从裁判文书信息中提取案由 / 当事人（双方）/ 执行法院
                    String caseReason = json.getString("casereason");
                    if (StringUtils.isNotBlank(caseReason)) {
                        jsonObject.put("CaseReason", caseReason);
                    }

                    JSONArray prosecutorJsonArray = CommonV2Util.getLitigantJSONArray(json.getString("prosecutor"), json.getString("caserole"));
                    if (prosecutorJsonArray != null && prosecutorJsonArray.size() > 0) {
                        jsonObject.put("Prosecutor", prosecutorJsonArray);
                    }

                    JSONArray defendantJsonArray = CommonV2Util.getLitigantJSONArray(json.getString("defendant"), json.getString("caserole"));
                    if (defendantJsonArray != null && defendantJsonArray.size() > 0) {
                        jsonObject.put("Defendant", defendantJsonArray);
                    }

                    String court = json.getString("court");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }

                    JSONObject protestorganJsonObj = JSONObject.parseObject(json.getString("protestorgan"));
                    if (protestorganJsonObj != null && protestorganJsonObj.containsKey("name")) {
                        jsonObject.put("Procuratorate", protestorganJsonObj.getString("name"));
                    }
                } else if (type == 1 || type == 2) {    // 从失信/被执行中提取并汇总当事人（被执行一方）
                    JSONArray defendantJsonArray = jsonObject.getJSONArray("Defendant");
                    if (defendantJsonArray == null) {
                        defendantJsonArray = new JSONArray();
                    }

                    JSONArray nameAndKeyNoArray = JSONArray.parseArray(json.getString("nameandkeyno"));
                    Iterator iterator = nameAndKeyNoArray.iterator();
                    while (iterator.hasNext()) {
                        JSONObject nameAndKeyNo = (JSONObject) iterator.next();

                        JSONObject caseRoleJsonObj = new JSONObject();
                        caseRoleJsonObj.put("Name", nameAndKeyNo.getString("Name"));
                        caseRoleJsonObj.put("KeyNo", nameAndKeyNo.getString("KeyNo"));
                        caseRoleJsonObj.put("Org", nameAndKeyNo.getInteger("Org"));
                        caseRoleJsonObj.put("Role", "被执行人");
                        if (!defendantJsonArray.toJSONString().contains(caseRoleJsonObj.getString("Name"))) {
                            defendantJsonArray.add(caseRoleJsonObj);
                        }
                    }
                    jsonObject.put("Defendant", defendantJsonArray);

                    // 法院提取
                    String court = json.getString("executegov");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }
                    // 执行依据文书号
                    String executeNo = json.getString("executeno");
                    if (StringUtils.isNotBlank(executeNo)) {
                        jsonObject.put("ExecuteNo", executeNo);
                    }
                } else if (type == 3) { // 限高中提取并汇总当事人（被执行一方）
                    JSONArray defendantJsonArray = jsonObject.getJSONArray("Defendant");
                    if (defendantJsonArray == null) {
                        defendantJsonArray = new JSONArray();
                    }

                    String personName = json.getString("personname");
                    String personid = json.getString("personid");
                    if (StringUtils.isNotBlank(personName)) {
                        JSONObject personJson = new JSONObject();
                        personJson.put("Name", personName);
                        personJson.put("KeyNo", StringUtils.isNotBlank(personid) ? personid : "");
                        personJson.put("Org", CommonV2Util.getOrgByKeyNo(personJson.getString("KeyNo"), personJson.getString("Name")));
                        personJson.put("Role", "被执行人");
                        if (!defendantJsonArray.toJSONString().contains(personName)) {
                            defendantJsonArray.add(personJson);
                        }
                    }

                    String court = json.getString("court");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }

                    String companyName = json.getString("companyname");
                    String companyKeyNo = json.getString("keyno");
                    if (StringUtils.isNotBlank(companyName)) {
                        JSONObject companyJson = new JSONObject();
                        companyJson.put("Name", companyName);
                        companyJson.put("KeyNo", StringUtils.isNotBlank(companyKeyNo) ? companyKeyNo : "");
                        companyJson.put("Org", CommonV2Util.getOrgByKeyNo(companyJson.getString("KeyNo"), companyJson.getString("Name")));
                        companyJson.put("Role", "被执行人");
                        if (!defendantJsonArray.toJSONString().contains(companyName)) {
                            defendantJsonArray.add(companyJson);
                        }
                    }
                    jsonObject.put("Defendant", defendantJsonArray);
                } else if (type == 5) {     // 破产重整
                    jsonObject.put("TrialRound", "破产");

                    String court = json.getString("courtname");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }

                    JSONArray applicantJsonArray = CommonV2Util.getLitigantJSONArrayFromPccz(
                            json.getString("applicantnameandkeyno"), "申请人");
                    if (jsonObject.getJSONArray("Prosecutor") == null || jsonObject.getJSONArray("Prosecutor").size() == 0) {
                        jsonObject.put("Prosecutor", applicantJsonArray);
                    }

                    JSONArray respondentJsonArray = CommonV2Util.getLitigantJSONArrayFromPccz(
                            json.getString("respondentnameandkeyno"), "被申请人");
                    if (jsonObject.getJSONArray("Defendant") == null || jsonObject.getJSONArray("Defendant").size() == 0) {
                        jsonObject.put("Defendant", respondentJsonArray);
                    }
                } else if (type == 6) { // 终本案件
                    JSONArray defendantJsonArray = jsonObject.getJSONArray("Defendant");
                    if (defendantJsonArray == null || defendantJsonArray.size() == 0) { // 当被告字段没有值时，考虑将终本的对应数据放入
                        defendantJsonArray = new JSONArray();

                        String name = CommonV2Util.getStringOrEmptyString(json.getString("name"));
                        String keyNo = CommonV2Util.getStringOrEmptyString(json.getString("keyno"));
                        if (StringUtils.isNotBlank(name) || StringUtils.isNotBlank(keyNo)) {
                            JSONObject defendantJson = new JSONObject();
                            defendantJson.put("Name", name);
                            defendantJson.put("KeyNo", keyNo);
                            defendantJson.put("Org", CommonV2Util.getOrgByKeyNo(keyNo, name));
                            defendantJson.put("Role", "被执行人");
                            if (!defendantJsonArray.toJSONString().contains(name)) {
                                defendantJsonArray.add(defendantJson);
                            }
                        }
                    }
                    jsonObject.put("Defendant", defendantJsonArray);

                    String court = json.getString("court");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }
                } else if (type == 7) { // 询价评估
                    JSONArray defendantJsonArray = new JSONArray();
                    JSONArray nameAndKeyNoArray = json.getJSONArray("nameandkeyno");
                    for (int i = 0; i < nameAndKeyNoArray.size(); i++) {
                        JSONObject nameAndKeyNo = nameAndKeyNoArray.getJSONObject(i);

                        JSONObject defendantJson = new JSONObject();
                        defendantJson.put("Name", nameAndKeyNo.getString("Name"));
                        defendantJson.put("KeyNo", nameAndKeyNo.getString("KeyNo"));
                        defendantJson.put("Org", nameAndKeyNo.getInteger("Org"));
                        defendantJson.put("Role", "当事人");
                        if (!defendantJsonArray.toJSONString().contains(defendantJson.getString("Name"))) {
                            defendantJsonArray.add(defendantJson);
                        }
                    }

                    if (jsonObject.getJSONArray("Defendant") == null || jsonObject.getJSONArray("Defendant").size() == 0) {
                        jsonObject.put("Defendant", defendantJsonArray);
                    }

                    String court = json.getString("courtname");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }
                } else if (type == 8) { // 股权冻结
                    JSONArray defendantJsonArray = new JSONArray();
                    JSONObject nameAndKeyNo = json.getJSONObject("relatedcompanyinfo");
                    if (nameAndKeyNo != null) {
                        JSONObject defendantJson = new JSONObject();
                        defendantJson.put("Name", nameAndKeyNo.getString("Name"));
                        defendantJson.put("KeyNo", nameAndKeyNo.getString("KeyNo"));
                        defendantJson.put("Org", nameAndKeyNo.getInteger("Org"));
                        defendantJson.put("Role", "当事人");
                        if (!defendantJsonArray.toJSONString().contains(defendantJson.getString("Name"))) {
                            defendantJsonArray.add(defendantJson);
                        }
                    }

                    if (jsonObject.getJSONArray("Defendant") == null || jsonObject.getJSONArray("Defendant").size() == 0) {
                        jsonObject.put("Defendant", defendantJsonArray);
                    }

                    String court = json.getString("enforcementcourt");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }
                } else if (type == 9) { // 送达公告
                    String court = json.getString("courtname");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }
                    String caseRole = CommonV2Util.getCaseRoleFromLianOrKtggList(Arrays.asList(json.toJSONString()), 1);
                    JSONArray caseRoleJsonArray = JSONArray.parseArray(caseRole);
                    if (caseRoleJsonArray != null && caseRoleJsonArray.size() > 0) {
                        JSONArray prosecutorArray = new JSONArray();
                        JSONArray defendantArray = new JSONArray();
                        for (int i = 0; i < caseRoleJsonArray.size(); i++) {
                            JSONObject caseRoleJson = caseRoleJsonArray.getJSONObject(i);

                            JSONObject jsonObj = new JSONObject();
                            jsonObj.put("Name", caseRoleJson.getString("P"));
                            jsonObj.put("KeyNo", caseRoleJson.getString("N"));
                            jsonObj.put("Role", caseRoleJson.getString("R"));
                            jsonObj.put("Org", caseRoleJson.getInteger("O"));

                            if (caseRoleJson.getString("R").equals("原告")) {
                                prosecutorArray.add(jsonObj);
                            } else if (caseRoleJson.getString("R").equals("被告")) {
                                defendantArray.add(jsonObj);
                            }
                        }
                        jsonObject.put("Prosecutor", prosecutorArray);
                        jsonObject.put("Defendant", defendantArray);
                    } else {
                        jsonObject.put("Prosecutor", new JSONArray());
                        jsonObject.put("Defendant", new JSONArray());
                    }
                } else if (type == 10) {    // 法院公告
                    String court = json.getString("court");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }
                    String caseRole = CommonV2Util.getCaseRoleFromLianOrKtggList(Arrays.asList(json.toJSONString()), 1);
                    JSONArray caseRoleJsonArray = JSONArray.parseArray(caseRole);
                    if (caseRoleJsonArray != null && caseRoleJsonArray.size() > 0) {
                        JSONArray prosecutorArray = new JSONArray();
                        JSONArray defendantArray = new JSONArray();
                        for (int i = 0; i < caseRoleJsonArray.size(); i++) {
                            JSONObject caseRoleJson = caseRoleJsonArray.getJSONObject(i);

                            JSONObject jsonObj = new JSONObject();
                            jsonObj.put("Name", caseRoleJson.getString("P"));
                            jsonObj.put("KeyNo", caseRoleJson.getString("N"));
                            jsonObj.put("Role", caseRoleJson.getString("R"));
                            jsonObj.put("Org", caseRoleJson.getInteger("O"));

                            if (caseRoleJson.getString("R").equals("原告")) {
                                prosecutorArray.add(jsonObj);
                            } else if (caseRoleJson.getString("R").equals("被告")) {
                                defendantArray.add(jsonObj);
                            }
                        }
                        jsonObject.put("Prosecutor", prosecutorArray);
                        jsonObject.put("Defendant", defendantArray);
                    } else {
                        jsonObject.put("Prosecutor", new JSONArray());
                        jsonObject.put("Defendant", new JSONArray());
                    }
                } else if (type == 11 || type == 12) {    // 开庭公告 or 立案信息
                    int selectType = (type == 11 ? 2 : 1);
                    String caseRole = CommonV2Util.getCaseRoleFromLianOrKtggList(Arrays.asList(json.toJSONString()), selectType);
                    JSONArray caseRoleJsonArray = JSONArray.parseArray(caseRole);
                    if (caseRoleJsonArray != null && caseRoleJsonArray.size() > 0) {
                        JSONArray prosecutorArray = new JSONArray();
                        JSONArray defendantArray = new JSONArray();
                        for (int i = 0; i < caseRoleJsonArray.size(); i++) {
                            JSONObject caseRoleJson = caseRoleJsonArray.getJSONObject(i);

                            JSONObject jsonObj = new JSONObject();
                            jsonObj.put("Name", caseRoleJson.getString("P"));
                            jsonObj.put("KeyNo", caseRoleJson.getString("N"));
                            jsonObj.put("Role", caseRoleJson.getString("R"));
                            jsonObj.put("Org", caseRoleJson.getInteger("O"));

                            if (caseRoleJson.getString("R").equals("原告")) {
                                prosecutorArray.add(jsonObj);
                            } else if (caseRoleJson.getString("R").equals("被告")) {
                                defendantArray.add(jsonObj);
                            }
                        }
                        jsonObject.put("Prosecutor", prosecutorArray);
                        jsonObject.put("Defendant", defendantArray);
                    }

                    // 提取法院公告中的案由信息
                    if (type == 11) {
                        String caseReason = json.getString("casereason");
                        if (StringUtils.isNotBlank(caseReason)) {
                            jsonObject.put("CaseReason", caseReason);
                        }
                    }

                    // 法院提取
                    String court = json.getString("executegov");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }
                }
                anNoMap.put(anNo, jsonObject);
            }
        }
    }

    public static JSONObject editItemJson(JSONObject jsonObject, int type, Map<Long, String> dateNodeMap, String anNo){
        JSONObject result = new JSONObject();

        String trialRound = new ExtractCaseTrialRoundUDF().evaluate(anNo);
        // 编辑字段
        result.put("Id", jsonObject.getString("id"));
        result.put("IsValid", jsonObject.getInteger("isvalid"));
        if (type == 1){
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = CommonV2Util.parseDateToTimeStamp(jsonObject.getString("publicdate"));
            result.put("PublishDate", publishDate);
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "失信人发布日期");
            }
            result.put("ExecuteStatus", jsonObject.getString("executestatus") == null ? "" : jsonObject.getString("executestatus"));
            result.put("ActionType", jsonObject.getString("actiontype") == null ? "" : jsonObject.getString("actiontype"));
        } else if (type == 2){
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long lianDate = CommonV2Util.parseDateToTimeStamp(jsonObject.getString("liandate"));
            result.put("LianDate", lianDate);
            if (lianDate != -1) {
                dateNodeMap.put(lianDate, trialRound + "|" + "被执行人立案日期");
            }
            result.put("Biaodi", jsonObject.getString("biaodi") == null ? "" : jsonObject.getString("biaodi"));
            JSONArray tmpArr = new JSONArray();
            if (StringUtils.isNotEmpty(jsonObject.getString("sqrinfo"))){
                try{
                    tmpArr = JSONArray.parseArray(jsonObject.getString("sqrinfo"));
                }catch (Exception e){

                }
            }
            result.put("SqrNameAndKeyNo", tmpArr);
        } else if (type == 3){
            JSONObject json = new JSONObject();
            json.put("Name", jsonObject.getString("personname"));
            json.put("KeyNo", jsonObject.getString("personid"));
            json.put("Org", CommonV2Util.getOrgByKeyNo(jsonObject.getString("personid"), jsonObject.getString("personname")));
            result.put("NameAndKeyNo", json);

            JSONObject companyJson = new JSONObject();
            String companyKeyNo = jsonObject.getString("keyno");
            String companyName = jsonObject.getString("companyname");
            companyJson.put("Name", StringUtils.isNotBlank(companyName) ? companyName : "");
            companyJson.put("KeyNo", StringUtils.isNotBlank(companyKeyNo) ? companyKeyNo : "");
            companyJson.put("Org", CommonV2Util.getOrgByKeyNo(companyJson.getString("KeyNo"), companyJson.getString("Name")));
            result.put("CompanyInfo", companyJson);

            // 限消令对象
            JSONObject xglNameAndKeyNoJson = new JSONObject();
            try {
                xglNameAndKeyNoJson.put("Name", CommonV2Util.getStringOrEmptyString(jsonObject.getString("xglname")));
                xglNameAndKeyNoJson.put("KeyNo", CommonV2Util.getStringOrEmptyString(jsonObject.getString("xglkeyno")));
                xglNameAndKeyNoJson.put("Org", CommonV2Util.getOrgByKeyNo(
                        xglNameAndKeyNoJson.getString("KeyNo"), xglNameAndKeyNoJson.getString("Name")));
            } catch (Exception ex) {
            }
            result.put("XglNameAndKeyNo", new JSONArray(Arrays.asList(xglNameAndKeyNoJson)));

            // 关联对象
            JSONObject glNameAndKeyNoJson = new JSONObject();
            try {
                glNameAndKeyNoJson.put("Name", CommonV2Util.getStringOrEmptyString(jsonObject.getString("glname")));
                glNameAndKeyNoJson.put("KeyNo", CommonV2Util.getStringOrEmptyString(jsonObject.getString("glkeyno")));
                glNameAndKeyNoJson.put("Org", CommonV2Util.getOrgByKeyNo(
                        glNameAndKeyNoJson.getString("KeyNo"), glNameAndKeyNoJson.getString("Name")));
            } catch (Exception ex) {
            }
            result.put("GlNameAndKeyNo", new JSONArray(Arrays.asList(glNameAndKeyNoJson)));

            result.put("Id", jsonObject.getString("id"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = jsonObject.getLong("publishdate") != null ? jsonObject.getLong("publishdate") : -1L;
            result.put("PublishDate", publishDate);
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "限制高消费发布日期");
            }
            // 申请人
            JSONArray tmpArr = new JSONArray();
            JSONObject sqrNameAndKeyNoJson = new JSONObject();
            try {
                if (StringUtils.isNotEmpty(jsonObject.getString("sqrname"))){
                    sqrNameAndKeyNoJson.put("Name", CommonV2Util.getStringOrEmptyString(jsonObject.getString("sqrname")));
                    sqrNameAndKeyNoJson.put("KeyNo", CommonV2Util.getStringOrEmptyString(jsonObject.getString("sqrkeyno")));
                    sqrNameAndKeyNoJson.put("Org", CommonV2Util.getOrgByKeyNo(
                            sqrNameAndKeyNoJson.getString("KeyNo"), sqrNameAndKeyNoJson.getString("Name")));

                    tmpArr.add(sqrNameAndKeyNoJson);
                }
            } catch (Exception ex) {
            }
            result.put("SqrNameAndKeyNo", tmpArr);
        } else if (type == 4){
            result.put("Id", jsonObject.getString("id"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long judgeDate = CommonV2Util.parseDateToTimeStamp(jsonObject.getString("judgedate"));
            result.put("JudgeDate", judgeDate);

            String docType = jsonObject.getString("doctype");
            if (StringUtils.isNotBlank(docType)) {
                if (docType.equals("ver")) {
                    result.put("DocType", "执行判决日期");
                    dateNodeMap.put(judgeDate, trialRound + "|" + "判决日期");
                    result.put("ResultType", "判决结果");
                } else {
                    result.put("DocType", "执行裁定日期");
                    dateNodeMap.put(judgeDate, trialRound + "|" + "裁定日期");
                    result.put("ResultType", "裁定结果");
                }
                result.put("Result", jsonObject.getString("judgeresult") == null ? "" : jsonObject.getString("judgeresult"));
            }
            result.put("Amt", jsonObject.getString("amountinvolved") == null ? "" : jsonObject.getString("amountinvolved"));
        } else if (type == 5) {
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("applicantnameandkeyno"));

            String category = "破产";
            String caseType = jsonObject.getString("casetype").trim();
            if (StringUtils.isNotBlank(caseType) && !caseType.equals("案件")) {
                category = caseType.replace("案件", "");
            }
            result.put("Category", category);
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = jsonObject.getLong("riskdate");
            result.put("PublishDate", publishDate != null ? publishDate : -1L);
            if (publishDate != null) {
                dateNodeMap.put(publishDate, "破产" + "|" + category + "公开日期");
            }
        } else if (type == 6) {
            result.put("Id", jsonObject.getString("id"));

            JSONArray nameAndKeyNoJsonArray = new JSONArray();
            JSONObject nameAndKeyNoJson = new JSONObject();
            nameAndKeyNoJson.put("Name", CommonV2Util.getStringOrEmptyString(jsonObject.getString("name")));
            nameAndKeyNoJson.put("KeyNo", CommonV2Util.getStringOrEmptyString(jsonObject.getString("keyno")));
            nameAndKeyNoJson.put("Org", CommonV2Util.getOrgByKeyNo(
                    nameAndKeyNoJson.getString("KeyNo"), nameAndKeyNoJson.getString("Name")));
            nameAndKeyNoJsonArray.add(nameAndKeyNoJson);
            result.put("NameAndKeyNo", nameAndKeyNoJsonArray);
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long judgeDate = (jsonObject.getLong("enddate") != null ? jsonObject.getLong("enddate") : -1L);
            result.put("JudgeDate", judgeDate);
            if (judgeDate != -1) {
                dateNodeMap.put(judgeDate, trialRound + "|" + "案件终本日期");
            }
            result.put("ExecuteObject", jsonObject.getString("executeobject") == null ? "" : jsonObject.getString("executeobject"));
            result.put("FailureAct", jsonObject.getString("failureact") == null ? "" : jsonObject.getString("failureact"));
        } else if (type == 7) {
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("Category", jsonObject.getString("confirmreferencemode"));
            result.put("Target", jsonObject.getString("target"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));
            // 标的物所有人
            JSONArray targetNameAndKeyNo = new JSONArray();
            try {
                targetNameAndKeyNo = jsonObject.getJSONArray("targetcompany");
            } catch (Exception ex) {
            }
            result.put("TargetNameAndKeyNo", targetNameAndKeyNo);

            Long publishDate = CommonV2Util.parseDateToTimeStamp(jsonObject.getString("publicdate"));
            result.put("PublishDate", publishDate);
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "询价评估公示日期");
            }

            String xjResult = "";
            JSONArray priceArray = JSONArray.parseArray(jsonObject.getString("evaluationunit"));
            if (priceArray != null && !priceArray.isEmpty() && priceArray.size() > 0){
                Double priceMin = null;
                Double priceMax = null;
                Iterator<Object> it = priceArray.iterator();
                int i = 0;
                while (it.hasNext()){
                    JSONObject json = (JSONObject) it.next();
                    if (i == 0){
                        try{
                            priceMin = json.getDouble("EvaluationPrice");
                            priceMax = json.getDouble("EvaluationPrice");
                        }catch (Exception e){
                            priceMin = null;
                            priceMax = null;
                        }
                    }else{
                        try{
                            if(priceMin > json.getDouble("EvaluationPrice")){
                                priceMin = json.getDouble("EvaluationPrice");
                            }
                            if(priceMax < json.getDouble("EvaluationPrice")){
                                priceMax = json.getDouble("EvaluationPrice");
                            }
                        }catch (Exception e){
                            priceMin = null;
                            priceMax = null;
                        }
                    }
                    i++;
                }

                if (priceMin != null && priceMax != null){
                    DecimalFormat df = new DecimalFormat("0.00");
                    if (priceMin.doubleValue() == priceMax.doubleValue()){
                        xjResult = df.format(priceMax);
                    }else{
                        xjResult = df.format(priceMin).concat("~").concat(df.format(priceMax));
                    }
                }
            }
            result.put("Result", xjResult);
        } else if (type == 8) {
            result.put("Id", jsonObject.getString("no"));   // 股权冻结没有该字段，置空

            JSONArray nameAndKeyNoJsonArray = new JSONArray();
            nameAndKeyNoJsonArray.add(jsonObject.getJSONObject("relatedcompanyinfo"));
            result.put("NameAndKeyNo", nameAndKeyNoJsonArray);

            String category = jsonObject.getString("statuesdetail") != null ?
                    jsonObject.getString("statuesdetail") : "冻结";
            result.put("Category", "股权" + category);
            result.put("IsValid", 1);

            Long publishDate = -1L;
            try {
                JSONObject detailJson = jsonObject.getJSONObject("equityfreezedetail");
                if (detailJson == null) {
                    detailJson = jsonObject.getJSONObject("equityunfreezedetail");
                }

                if (detailJson != null && detailJson.containsKey("PublicDate")) {
                    String publicDate = detailJson.getString("PublicDate");
                    if (StringUtils.isNotBlank(publicDate)){
                        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                        publishDate = format.parse(detailJson.getString("PublicDate")).getTime() / 1000;
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            result.put("PublishDate", publishDate);
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "股权" + category + "公示日期");
            }
            // 被执行人
            JSONArray tmpArr = new JSONArray();
            JSONObject zhixingJson = new JSONObject();
            try {
                if (StringUtils.isNotEmpty(jsonObject.getString("executedby"))){
                    zhixingJson.put("Name", CommonV2Util.getStringOrEmptyString(jsonObject.getString("executedby")));
                    zhixingJson.put("KeyNo", CommonV2Util.getStringOrEmptyString(jsonObject.getString("executedkeyno")));
                    zhixingJson.put("Org", CommonV2Util.getOrgByKeyNo(
                            zhixingJson.getString("KeyNo"), zhixingJson.getString("Name")));
                    tmpArr.add(zhixingJson);
                }
            } catch (Exception ex) {
            }
            result.put("ZxNameAndKeyNo", tmpArr);
            result.put("EquityAmount", jsonObject.getString("equityamount") == null ? "" : jsonObject.getString("equityamount"));
        } else if (type == 9) {
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = jsonObject.getLong("publishdate") != null ?
                    jsonObject.getLong("publishdate") : -1L;
            result.put("PublishDate", publishDate);
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "送达公告发布日期");
            }
        } else if (type == 10) {
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("Category", jsonObject.getString("category"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = CommonV2Util.parseDateToTimeStamp(jsonObject.getString("publishdate"));
            result.put("PublishDate", publishDate);
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "法院公告刊登日期");
            }
        } else if (type == 11) {
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long openDate = CommonV2Util.parseDateToTimeStamp(jsonObject.getString("liandate"));
            result.put("OpenDate", openDate);
            if (openDate != -1) {
                dateNodeMap.put(openDate, trialRound + "|" + "开庭时间");
            }
        } else if (type == 12) {
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = CommonV2Util.parseDateToTimeStamp(jsonObject.getString("punishdate"));
            result.put("LianDate", publishDate);
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "立案日期");
            }
        } else if (type == 13) {
            result.put("Id", jsonObject.getString("id"));

            JSONArray nameAndKeyNoJsonArray = new JSONArray();
            try {
                JSONArray jsonArray = jsonObject.getJSONArray("nameandkeyno");
                if (jsonArray != null && jsonArray.size() > 0) {
                    nameAndKeyNoJsonArray = jsonArray;
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            result.put("NameAndKeyNo", nameAndKeyNoJsonArray);
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = jsonObject.getLong("riskdate");
            result.put("PublishDate", (publishDate != null ? publishDate : -1L));
        } else if (type == 14) {
            result.put("Id", jsonObject.getString("no"));
            JSONArray nameAndKeyNoJsonArray = new JSONArray();
            try {
                 JSONObject nameAndKeyNo = new JSONObject();
                 String name = CommonV2Util.getStringOrEmptyString(jsonObject.getString("companyname"));
                 String keyNo = CommonV2Util.getStringOrEmptyString(jsonObject.getString("keyno"));
                 nameAndKeyNo.put("Name", name);
                 nameAndKeyNo.put("KeyNo" , keyNo);
                 nameAndKeyNo.put("Org", CommonV2Util.getOrgByKeyNo(keyNo, name));
                 nameAndKeyNoJsonArray.add(nameAndKeyNo);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            result.put("NameAndKeyNo", nameAndKeyNoJsonArray);
            result.put("IsValid", 1);

            Long publishDate = jsonObject.getLong("penaltydate");
            result.put("PublishDate", (publishDate != null ? publishDate : -1L));
        } else if (type == 15) {
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = CommonV2Util.parseDateToTimeStamp(jsonObject.getString("liandate"));
            result.put("PublishDate", publishDate);
        } else if (type == 16) {
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = jsonObject.getLong("judgedate");
            result.put("PublishDate", (publishDate != null ? publishDate : -1L));
        }
        return result;
    }
    public static void editItemJsonConn(JSONObject jsonObject, int type, Map<Long, String> dateNodeMap, String anNo){
        String trialRound = new ExtractCaseTrialRoundUDF().evaluate(anNo);
        // 编辑字段
        if (type == 1){
            Long publishDate = jsonObject.getLong("PublishDate");
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "失信人发布日期");
            }
        } else if (type == 2){
            Long lianDate = jsonObject.getLong("LianDate");
            if (lianDate != -1) {
                dateNodeMap.put(lianDate, trialRound + "|" + "被执行人立案日期");
            }
        } else if (type == 3){
            Long publishDate = jsonObject.getLong("PublishDate");
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "限制高消费发布日期");
            }
        } else if (type == 4){
            Long judgeDate = jsonObject.getLong("JudgeDate");

            String docType = jsonObject.getString("DocType");
            if (StringUtils.isNotBlank(docType)) {
                if (docType.contains("判决日期")) {
                    dateNodeMap.put(judgeDate, trialRound + "|" + "判决日期");
                } else {
                    dateNodeMap.put(judgeDate, trialRound + "|" + "裁定日期");
                }
            }
        } else if (type == 5) {
            String category = jsonObject.getString("Category");
            Long publishDate = jsonObject.getLong("PublishDate");
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, "破产" + "|" + category + "公开日期");
            }
        } else if (type == 6) {
            Long judgeDate = jsonObject.getLong("JudgeDate");
            if (judgeDate != -1) {
                dateNodeMap.put(judgeDate, trialRound + "|" + "案件终本日期");
            }
        } else if (type == 7) {
            Long publishDate = jsonObject.getLong("PublishDate");
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "询价评估公示日期");
            }
        } else if (type == 8) {
            Long publishDate = jsonObject.getLong("PublishDate");
            String category = jsonObject.getString("Category");
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "股权" + category + "公示日期");
            }
        } else if (type == 9) {
            Long publishDate = jsonObject.getLong("PublishDate");
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "送达公告发布日期");
            }
        } else if (type == 10) {
            Long publishDate = jsonObject.getLong("PublishDate");
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "法院公告刊登日期");
            }
        } else if (type == 11) {
            Long openDate = jsonObject.getLong("OpenDate");
            if (openDate != -1) {
                dateNodeMap.put(openDate, trialRound + "|" + "开庭时间");
            }
        } else if (type == 12) {
            Long publishDate = jsonObject.getLong("LianDate");
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "立案日期");
            }
        }
    }

//    /**
//     * 提取案件角色信息
//     */
//    private static String getCaseRoleInfo(List<String> caseList, JSONArray infoList, String provinceCode) {
//        /**
//         * 提取规则
//         * 1，如果有裁判文书，取裁判文书中的当事人信息（剔除非执行类的案件） - caserole
//         * 2，如果裁判文书缺失，取infoList中第一个item中的被告信息作为caserole
//         */
//        String caseRole = CommonV2Util.getCaseRoleFromCaseList(caseList, provinceCode);
//        if (caseRole.equals(new JSONArray().toJSONString())) {
//            try {
//                List<JSONObject> jsonObjectList = infoList.stream()
//                        .map(e -> {
//                            try {
//                                return (JSONObject) e;
//                            } catch (Exception ex) {
//                                return null;
//                            }
//                        })
//                        .filter(e -> e != null && e.containsKey("LatestTimestamp"))
//                        .sorted(Comparator.comparingLong(e -> e.getLong("LatestTimestamp")))
//                        .collect(Collectors.toList());
//                if (jsonObjectList != null && jsonObjectList.size() > 0) {
//                    JSONObject jsonObject = jsonObjectList.get(0);
//                    JSONArray defendantJsonArray = jsonObject.getJSONArray("Defendant");
//                    if (defendantJsonArray != null && defendantJsonArray.size() > 0) {
//                        Iterator iterator = defendantJsonArray.iterator();
//
//                        JSONArray caseRoleJsonArray = new JSONArray();
//                        while (iterator.hasNext()) {
//                            JSONObject defendantJson = (JSONObject) iterator.next();
//                            JSONObject caseRoleJson = new JSONObject();
//                            caseRoleJson.put("P", (StringUtils.isNotBlank(defendantJson.getString("Name")) ? defendantJson.getString("Name") : ""));
//                            caseRoleJson.put("R", (StringUtils.isNotBlank(defendantJson.getString("Role")) ? defendantJson.getString("Role") : ""));
//                            caseRoleJson.put("N", (StringUtils.isNotBlank(defendantJson.getString("KeyNo")) ? defendantJson.getString("KeyNo") : ""));
//                            caseRoleJson.put("O", (defendantJson.getInteger("Org") != null ? defendantJson.getInteger("Org") : -1));
//                            caseRoleJsonArray.add(caseRoleJson);
//                        }
//                        caseRole = caseRoleJsonArray.toJSONString();
//                    }
//                }
//            } catch (Exception ex) {
//                ex.printStackTrace();
//            }
//        }
//        return caseRole;
//    }


    public static void main(String[] args) {
        List<String> sxList = new ArrayList();
        String sxStr1 ="{\"id\":\"fffffd010d22da150b80018134ccc9f42\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"p88f3f5d8ee930faedaec32d9e352e46\\\",\\\"Org\\\":2,\\\"Name\\\":\\\"吴蕙君\\\"}]\",\"liandate\":\"2020-08-06 00:00:00.0\",\"anno\":\"（2016）吉0723执987号\",\"executegov\":\"东莞市第一人民法院\",\"publicdate\":\"2020-09-16 00:00:00.0\",\"isvalid\":\"1\",\"companynames\":\"吴蕙君,p88f3f5d8ee930faedaec32d9e352e46\",\"province\":\"GD\",\"executeno\":\"（2019）粤1971民初23219号民事判决书\",\"executestatus\":\"全部未履行\",\"actiontype\":\"违反财产报告制度\"}";
//        sxList.add(sxStr1);
        List<String> zxList = new ArrayList();
        String zxStr1 = "" +
                "{\"id\":\"dbd8428199c260102e860141741151e81\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"吉林省宏利房地产开发有限公司乾安第一\\\"}]\",\"liandate\":\"2016-09-29 00:00:00.0\",\"anno\":\"（2016）吉0723执987号\",\"executegov\":\"乾安县人民法院\",\"isvalid\":\"0\",\"companynames\":\"吉林省宏利房地产开发有限公司乾安第一\",\"province\":\"JL\",\"sqrinfo\":\"\",\"biaodi\":\"500000\"}";
//        String  zxStr2 ="{\"id\":\"0a3b426cfe32fafe47852a95205b3ee31\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"paafb18e89b430232d8fac6b4a9d7cdd\\\",\\\"Org\\\":2,\\\"Name\\\":\\\"朱晓庆\\\"}]\",\"liandate\":\"2019-02-01 00:00:00.0\",\"anno\":\"（2019）沪0117执880号\",\"executegov\":\"上海市松江区人民法院\",\"isvalid\":\"0\",\"companynames\":\"朱晓庆,paafb18e89b430232d8fac6b4a9d7cdd\",\"province\":\"SH\"}";
//        String zxStr3 ="{\"id\":\"0a3b426cfe32fafe47852a95205b3ee31\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"paafb18e89b430232d8fac6b4a9d7cdd\\\",\\\"Org\\\":2,\\\"Name\\\":\\\"朱晓庆\\\"}]\",\"liandate\":\"2019-02-01 00:00:00.0\",\"anno\":\"（2019）沪0117执880号\",\"executegov\":\"上海市松江区人民法院\",\"isvalid\":\"0\",\"companynames\":\"朱晓庆,paafb18e89b430232d8fac6b4a9d7cdd\",\"province\":\"SH\"}";
//        zxList.add(zxStr1);
//        zxList.add(zxStr2);
//        zxList.add(zxStr3);
        List<String> xgList = new ArrayList();
        String xgStr1 = "{\"id\":\"5501ab65758417e862a19caec4937bea\",\"personid\":\"pd1727e8b22fa77afae3f01a3a6cff78\",\"personname\":\"吴显忠\",\"caseno\":\"（2016）吉0723执987号\",\"judgedate\":1475078400,\"publishdate\":1543852800,\"isvalid\":1,\"keyno\":\"12d6dbdb87107811a62173d591d63824\",\"companyname\":\"吉林省宏利房地产开发有限公司乾安第一分公司\",\"province\":\"JL\",\"court\":\"乾安县人民法院\",\"xglkeyno\":\"12d6dbdb87107811a62173d591d63824\",\"xglname\":\"吉林省宏利房地产开发有限公司乾安第一分公司\",\"glkeyno\":\"pd1727e8b22fa77afae3f01a3a6cff78\",\"glname\":\"吴显忠\",\"sqrkeyno\":\"\",\"sqrname\":\"陈桂香\"}";
//        xgList.add(xgStr1);

        List<String> caseList = new ArrayList();
        String caseStr1 = "{\"id\":\"b125eb2ca605ea8d922c73d107893d820\",\"defendant\":\"吉林省宏利房地产开发有限公司乾安第一分公司,12d6dbdb87107811a62173d591d63824\",\"prosecutor\":\"陈桂香\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"陈桂香\\\"},{\\\"KeyNo\\\":\\\"12d6dbdb87107811a62173d591d63824\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"吉林省宏利房地产开发有限公司乾安第一分公司\\\"}]\",\"caseno\":\"（2016）吉0723执987号\",\"submitdate\":\"2017-01-03T00:00:00+08:00\",\"judgedate\":\"2016-12-22T00:00:00+08:00\",\"courtdate\":\"2017-01-03T00:00:00+08:00\",\"casereason\":\"民事案件执行\",\"isvalid\":\"1\",\"trialround\":\"首次执行\",\"court\":\"乾安县人民法院\",\"companynames\":\"12d6dbdb87107811a62173d591d63824,吉林省宏利房地产开发有限公司乾安第一分公司,陈桂香\",\"caserole\":\"[{\\\"P\\\":\\\"陈桂香\\\",\\\"R\\\":\\\"申请执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"吉林省宏利房地产开发有限公司乾安第一分公司\\\",\\\"R\\\":\\\"被执行人\\\",\\\"N\\\":\\\"12d6dbdb87107811a62173d591d63824\\\",\\\"O\\\":0}]\",\"doctype\":\"adj\",\"protestorgan\":\"\",\"province\":\"JL\",\"amountinvolved\":\"\",\"judgeresult\":\"终结本次执行程序。\"} ";
        String caseStr2 = "{\"id\":\"5ff9cbae46355df8b66390cee09530140\",\"defendant\":\"吉林省宏利房地产开发有限公司乾安第一分公司,12d6dbdb87107811a62173d591d63824\",\"prosecutor\":\"陈桂香\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"陈桂香\\\"},{\\\"KeyNo\\\":\\\"12d6dbdb87107811a62173d591d63824\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"吉林省宏利房地产开发有限公司乾安第一分公司\\\"}]\",\"caseno\":\"（2016）吉0723执987号\",\"submitdate\":\"2017-04-13T08:00:00+08:00\",\"judgedate\":\"2016-12-29T08:00:00+08:00\",\"courtdate\":\"2017-04-13T08:00:00+08:00\",\"casereason\":\"民事案件执行\",\"isvalid\":\"1\",\"trialround\":\"首次执行\",\"court\":\"乾安县人民法院\",\"companynames\":\"12d6dbdb87107811a62173d591d63824,吉林省宏利房地产开发有限公司乾安第一分公司,陈桂香\",\"caserole\":\"[{\\\"P\\\":\\\"陈桂香\\\",\\\"R\\\":\\\"申请执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"吉林省宏利房地产开发有限公司乾安第一分公司\\\",\\\"R\\\":\\\"被执行人\\\",\\\"N\\\":\\\"12d6dbdb87107811a62173d591d63824\\\",\\\"O\\\":0}]\",\"doctype\":\"adj\",\"protestorgan\":\"\",\"province\":\"JL\",\"amountinvolved\":\"\",\"judgeresult\":\"终结本次执行程序。\"} ";
//        caseList.add(caseStr1);
//        caseList.add(caseStr2);

        caseList.add("{\"id\":\"f61921a07783d07282dfd407cbd311720\",\"defendant\":\"\",\"prosecutor\":\"古蔺宇明酿造有限公司破产管理人\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"古蔺宇明酿造有限公司破产管理人\\\"}]\",\"caseno\":\"（2017）川0525破2号之二\",\"submitdate\":\"2017-12-28T08:00:00+08:00\",\"judgedate\":\"2017-11-14T08:00:00+08:00\",\"courtdate\":\"2017-12-28T08:00:00+08:00\",\"casereason\":\"申请破产清算\",\"isvalid\":\"1\",\"trialround\":\"\",\"court\":\"古蔺县人民法院\",\"companynames\":\"古蔺宇明酿造有限公司破产管理人\",\"caserole\":\"[{\\\"P\\\":\\\"古蔺宇明酿造有限公司破产管理人\\\",\\\"R\\\":\\\"申请人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-1}]\",\"doctype\":\"adj\",\"protestorgan\":\"\",\"province\":\"SC\",\"amountinvolved\":\"\",\"judgeresult\":\"宣告古蔺宇明酿造有限公司破产；  终结古蔺宇明酿造有限公司破产程序。\"}");
        caseList.add("{\"id\":\"09ba41b5d49069c59955f6f5e82dba500\",\"defendant\":\"\",\"prosecutor\":\"\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"e2f364854931f7f35f9804af6ef8d8fd\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"古蔺宇明酿造有限公司\\\"}]\",\"caseno\":\"（2017）川0525破2号\",\"submitdate\":\"2017-12-20T00:00:00+08:00\",\"judgedate\":\"2017-07-12T00:00:00+08:00\",\"courtdate\":\"2017-12-20T00:00:00+08:00\",\"casereason\":\"其他民事\",\"isvalid\":\"1\",\"trialround\":\"\",\"court\":\"古蔺县人民法院\",\"companynames\":\"古蔺宇明酿造有限公司,e2f364854931f7f35f9804af6ef8d8fd,古蔺郎酒酿造有限公司\",\"caserole\":\"[]\",\"doctype\":\"not\",\"protestorgan\":\"\",\"province\":\"SC\",\"amountinvolved\":\"\",\"judgeresult\":\"\"}");
        caseList.add("{\"id\":\"d9abfa502830cda5bfac2bd5c6bd64b60\",\"defendant\":\"\",\"prosecutor\":\"\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"e2f364854931f7f35f9804af6ef8d8fd\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"古蔺宇明酿造有限公司\\\"}]\",\"caseno\":\"（2017）川0525破2号\",\"submitdate\":\"2017-12-20T00:00:00+08:00\",\"judgedate\":\"2017-12-06T00:00:00+08:00\",\"courtdate\":\"2017-12-20T00:00:00+08:00\",\"casereason\":\"其他民事\",\"isvalid\":\"1\",\"trialround\":\"\",\"court\":\"古蔺县人民法院\",\"companynames\":\"古蔺宇明酿造有限公司,e2f364854931f7f35f9804af6ef8d8fd,古蔺郎酒酿造有限公司\",\"caserole\":\"[]\",\"doctype\":\"dec\",\"protestorgan\":\"\",\"province\":\"SC\",\"amountinvolved\":\"\",\"judgeresult\":\"\"}");

        List<String> pcczList = new ArrayList<>();      // 破产重整
        pcczList.add("{\"id\":\"ffed2893021c275bba8472ca1ff829e9\",\"companynames\":\"古蔺宇明酿造有限公司,e2f364854931f7f35f9804af6ef8d8fd,古蔺郎酒酿造有限公司\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"e2f364854931f7f35f9804af6ef8d8fd\\\",\\\"Name\\\":\\\"古蔺宇明酿造有限公司\\\",\\\"Org\\\":0}]\",\"company_c\":\"古蔺宇明酿造有限公司\",\"anno\":\"（2017）川0525破2号\",\"casetype\":\"破产案件\",\"courtname\":\"古蔺县人民法院\",\"respondent\":\"古蔺宇明酿造有限公司\",\"respondentnameandkeyno\":\"[{\\\"KeyNo\\\":\\\"e2f364854931f7f35f9804af6ef8d8fd\\\",\\\"Name\\\":\\\"古蔺宇明酿造有限公司\\\",\\\"Org\\\":0}]\",\"applicant\":\"古蔺宇明酿造有限公司\",\"applicantnameandkeyno\":\"[{\\\"KeyNo\\\":\\\"e2f364854931f7f35f9804af6ef8d8fd\\\",\\\"Name\\\":\\\"古蔺宇明酿造有限公司\\\",\\\"Org\\\":0}]\",\"riskdate\":1499616000,\"groupyear\":2017,\"managementorganization\":\"四川大山律师事务所\",\"responsibleperson\":\"陈祥庐\",\"province\":\"SC\",\"isvalid\":0,\"createdate\":\"2019-10-25 21:22:17.0\",\"updatedate\":\"2020-11-16 18:46:35.0\"}");

        List<String> zbList = new ArrayList<>();        // 终本案件
        String zbStr1 = "{\"id\":\"981320dcec23e3f02a63afe4af13cb3a\",\"caseid\":\"981320dcec23e3f02a63afe4af13cb3a\",\"name\":\"吉林省宏利房地产开发有限公司乾安第一分公司\",\"sex\":\"\",\"cerno\":\"55978549-4\",\"searchcerno\":\"5 5 9 7 8 5 4 9 4\",\"court\":\"乾安县人民法院\",\"enddate\":1483027200,\"keyno\":\"12d6dbdb87107811a62173d591d63824\",\"isvalid\":1,\"updatedate\":\"2020-10-01 08:11:26.0\",\"anno\":\"（2016）吉0723执987号\",\"executeobject\":\"500000\",\"failureact\":\"0\",\"judgedate\":1475078400,\"province\":\"JL\",\"year\":2016,\"executeobjectunit\":\"元\",\"failureactunit\":\"元\"}";
//        zbList.add(zbStr1);

        List<String> xjpgList = new ArrayList<>();      // 询价评估
//        xjpgList.add("{\"id\":\"ffffa03a04d1fcaa3c1770bcb38ba9a6\",\"caseno\":\"（2016）吉0723执987号\",\"party\":null,\"partykeyno\":null,\"target\":\"广东省佛山市南海区里水镇里水大道南109号长信御景峰豪园9栋1704房\",\"courtname\":\"佛山市南海区人民法院\",\"confirmreferencemode\":\"定向询价\",\"companynames\":\"陈港集\",\"isdetail\":\"1\",\"publicdate\":\"2020-10-12 00:00:00.0\",\"createdate\":\"2020-10-14 01:09:32.0\",\"updatedate\":\"2020-11-13 09:35:05.0\",\"isvalid\":\"1\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"p17e06d349ad7e757aae781f25bbe81f\\\",\\\"Org\\\":2,\\\"Name\\\":\\\"陈港集\\\"}]\",\"company_c\":\"\",\"evaluationunit\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Name\\\":\\\"\\\",\\\"EvaluationPrice\\\":\\\"1492662.86\\\"}]\",\"province\":\"GD\",\"year\":\"2020\",\"targetcompany\":\"[{\\\"KeyNo\\\":\\\"p17e06d349ad7e757aae781f25bbe81f\\\",\\\"Org\\\":2,\\\"Name\\\":\\\"陈港集\\\"}]\",\"othercompany\":\"[]\",\"targets\":\"[{\\\"Target\\\":\\\"广东省佛山市南海区里水镇里水大道南109号长信御景峰豪园9栋1704房\\\"}]\"}");

        List<String> gqdjList = new ArrayList<>();      // 股权冻结
//        gqdjList.add("{\"keyno\":\"ffffefc9be5d327e5c49099f341912db\",\"companyname\":\"遂宁建欣鸿源农牧业开发有限公司\",\"status\":\"股权冻结|失效\",\"id\":\"ea3555929bba1acae7f66883c25b5da5\",\"no\":\"ea3555929bba1acae7f66883c25b5da5\",\"enforcementcourt\":\"四川省遂宁市安居区人民法院\",\"statuesdetail\":\"失效\",\"equityunfreezedetail\":null,\"org\":\"2\",\"judicialpartnerschangedetail\":null,\"equityamount\":\"30万元人民币\",\"source\":\"0\",\"executedkeyno\":\"pe1bddf67760f8d96417335ab1a12bb5\",\"executionnoticenum\":\"（2016）吉0723执987号之四\",\"equityfreezedetail\":\"{\\\"CompanyName\\\":\\\"白建红\\\",\\\"FreezeTerm\\\":\\\"729天\\\",\\\"ExecutionDocNum\\\":null,\\\"ExecutionVerdictNum\\\":\\\"（2015）安居执字第354号之四\\\",\\\"ExecutedPersonDocNum\\\":\\\"\\\",\\\"FreezeStartDate\\\":\\\"2018-01-11\\\",\\\"ExecutionMatters\\\":\\\"公示冻结股权、其他投资权益\\\",\\\"FreezeEndDate\\\":\\\"2020-01-10\\\",\\\"PublicDate\\\":\\\"2018-01-11\\\",\\\"ExecutedPersonDocType\\\":\\\"中华人民共和国居民身份证\\\"}\",\"relatedcompanyinfo\":\"{\\\"KeyNo\\\":\\\"ffffefc9be5d327e5c49099f341912db\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"遂宁建欣鸿源农牧业开发有限公司\\\"}\",\"statuestype\":\"股权冻结\",\"executedby\":\"白建红\"}");

        List<String> sdggList = new ArrayList<>();      // 送达公告
//        sdggList.add("{\"id\":\"fffff0d3d29de3907ff5ef1cbd9b853e_21\",\"anno\":\"（2016）吉0723执987号\",\"courtname\":\"湖州市吴兴区人民法院\",\"publishdate\":1488297600,\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"王艳华\\\"},{\\\"KeyNo\\\":\\\"1fc7ee008587fa465e8393e77e578464\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"中银消费金融有限公司\\\"}]\",\"isvalid\":1,\"companynames\":\"王艳华,中银消费金融有限公司,1fc7ee008587fa465e8393e77e578464\",\"province\":\"ZJ\",\"casereason\":\"借款合同纠纷\",\"prosecutorlistos\":\"[{\\\"KeyNo\\\":\\\"1fc7ee008587fa465e8393e77e578464\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"中银消费金融有限公司\\\"}]\",\"defendantlistos\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"王艳华\\\"}]\"} ");

        List<String> fyggList = new ArrayList<>();      // 法院公告
//        fyggList.add("{\"id\":\"6002601963\",\"anno\":\"（2016）吉0723执987号,（2016）吉0723执987号\",\"category\":\"执行文书\",\"court\":\"哈尔滨铁路运输中级法院\",\"publishdate\":\"2020-03-24T00:00:00+08:00\",\"isvalid\":1,\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Name\\\":\\\"公告\\\",\\\"Org\\\":-1},{\\\"KeyNo\\\":\\\"pe62971c140f25f69dc5e9945477fab4\\\",\\\"Name\\\":\\\"邱金贝\\\",\\\"Org\\\":2},{\\\"KeyNo\\\":\\\"p1fc353c6746cad3adb19c462efc3d9e\\\",\\\"Name\\\":\\\"杨金忠\\\",\\\"Org\\\":2},{\\\"KeyNo\\\":\\\"babff2f7855f965f543d3726c77e02da\\\",\\\"Name\\\":\\\"哈尔滨东大林业技术装备有限公司\\\",\\\"Org\\\":0}]\",\"province\":\"HLJ\",\"casereason\":\"买卖合同纠纷\",\"prosecutorlistos\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Name\\\":\\\"公告\\\",\\\"Org\\\":-1},{\\\"KeyNo\\\":\\\"pe62971c140f25f69dc5e9945477fab4\\\",\\\"Name\\\":\\\"邱金贝\\\",\\\"Org\\\":2},{\\\"KeyNo\\\":\\\"p1fc353c6746cad3adb19c462efc3d9e\\\",\\\"Name\\\":\\\"杨金忠\\\",\\\"Org\\\":2}]\",\"defendantlistos\":\"[{\\\"KeyNo\\\":\\\"babff2f7855f965f543d3726c77e02da\\\",\\\"Name\\\":\\\"哈尔滨东大林业技术装备有限公司\\\",\\\"Org\\\":0}]\"}");

        List<String> ktggList = new ArrayList<>();      // 开庭公告
        String kt1 = "{\"id\":\"4eece1c571a63639b3f9e48d7102e2525\",\"liandate\":\"2020-08-28T15:30:00+08:00\",\"casereason\":\"\",\"executegov\":\"深圳市南山区人民法院\",\"anno\":\"（2016）吉0723执987号\",\"province\":\"GD\",\"companynames\":\"115774a26b65708f81feaed0cd837fe4,东莞易施宝建筑材料有限公司,a598716f16e293de3b3b1e4d3473e2b9,深圳市中建南方建设集团有限公司\",\"isvalid\":\"1\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"115774a26b65708f81feaed0cd837fe4\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"东莞易施宝建筑材料有限公司\\\"},{\\\"KeyNo\\\":\\\"a598716f16e293de3b3b1e4d3473e2b9\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"深圳市中建南方建设集团有限公司\\\"}]\",\"prosecutorlistos\":\"[]\",\"defendantlistos\":\"[]\"} ";
//        String kt2 = "{\"id\":\"490ac033b2e31e68bdc17c3477fd22305\",\"liandate\":\"2020-08-28T15:34:00+08:00\",\"casereason\":\"买卖合同纠纷\",\"executegov\":\"深圳市南山区人民法院\",\"anno\":\"（2020）粤0305民初15795号\",\"province\":\"GD\",\"companynames\":\"115774a26b65708f81feaed0cd837fe4,东莞易施宝建筑材料有限公司,a598716f16e293de3b3b1e4d3473e2b9,深圳市中建南方建设集团有限公司\",\"isvalid\":\"1\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"115774a26b65708f81feaed0cd837fe4\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"东莞易施宝建筑材料有限公司\\\"},{\\\"KeyNo\\\":\\\"a598716f16e293de3b3b1e4d3473e2b9\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"深圳市中建南方建设集团有限公司\\\"}]\",\"prosecutorlistos\":\"[{\\\"keyno\\\":\\\"115774a26b65708f81feaed0cd837fe4\\\",\\\"Org\\\":0,\\\"name\\\":\\\"东莞易施宝建筑材料有限公司\\\"}]\",\"defendantlistos\":\"[{\\\"keyno\\\":\\\"a598716f16e293de3b3b1e4d3473e2b9\\\",\\\"Org\\\":0,\\\"name\\\":\\\"深圳市中建南方建设集团有限公司\\\"}]\"} ";

//        ktggList.add(kt2);
//        ktggList.add(kt1);


        List<String> lianList = new ArrayList<>();      // 立案信息
        String lianStr ="{\"id\":\"027f69ada99cc845089deac2686ba597\",\"companykeywords\":\"王荣华,,深圳市源美龙智能科技有限公司,26cb4aa57e3de538084105b6af2aeef8\",\"anno\":\"（2016）民初9287号\",\"punishdate\":\"2018-10-17T00:00:00+08:00\",\"isvalid\":1,\"prosecutorlistos\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"王荣华\\\"}]\",\"defendantlistos\":\"[{\\\"KeyNo\\\":\\\"26cb4aa57e3de538084105b6af2aeef8\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"深圳市源美龙智能科技有限公司\\\"}]\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"王荣华\\\"},{\\\"KeyNo\\\":\\\"26cb4aa57e3de538084105b6af2aeef8\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"深圳市源美龙智能科技有限公司\\\"}]\",\"province\":\"GD\"} ";
//        lianList.add(lianStr);

        List<String> hbcfList = new ArrayList<>();      // 环保处罚
//        hbcfList.add("{\"sxid\":\"fceab03a23ce984f86d8febcfb5e2a272\",\"id\":\"fc9bab5094caa7ffcc5ad3fac8ff67a8\",\"companykeywords\":\"上海基安汽车厢体制造厂。c5dbb3afc9821f9914228175aff93943\",\"caseno\":\"（2016）吉0723执987号\",\"punishdate\":\"2018-01-22 00:00:00.0\",\"illegaltype\":\"\",\"punishgov\":\"上海市奉贤区生态环境局\",\"punishreason\":\"发现你单位于奉贤区青村镇浦星公路6995号从事汽车厢体加工生产项目，未向环保部门报批环境影响评价文件，配套环保设施未建成，主体工程于2017年8月即投入正式生产。\",\"isvalid\":1,\"createdate\":\"2020-03-10 20:50:39.0\",\"updatedate\":\"2020-11-02 06:51:44.0\",\"courtyear\":2018,\"risklevel\":3,\"riskdate\":1516550400,\"companyname\":null,\"company_c\":\"上海基安汽车厢体制造厂\",\"implementation\":\"\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"c5dbb3afc9821f9914228175aff93943\\\",\\\"Org\\\":0,\\\"Name\\\":\\\"上海基安汽车厢体制造厂\\\"}]\",\"punishmentresult\":\"以下行政处罚：罚款人民币贰拾万元整。\",\"province\":\"SH\"}");


        List<String> xzcfList = new ArrayList<>();      // 行政处罚

        // 环保处罚

        List<String> cfgsList = new ArrayList<>();      // 行政处罚（工商）

        List<String> cfxyList = new ArrayList<>();      // 行政处罚（信用中国）

        List<String> cfdfList = new ArrayList<>();      // 行政处罚（地方）

        String output = new GetDetailInfoZXV2UDF().evaluate(sxList, zxList, xgList, caseList
                , pcczList, zbList, xjpgList, gqdjList, sdggList, fyggList, ktggList, lianList, hbcfList
                , cfgsList, cfxyList, cfdfList);
        System.out.println(output);

    }
}
