package com.qcc.udf.trade_mark;

import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentLengthException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDTF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;

import java.util.ArrayList;

public class TmEditStringUDTF extends GenericUDTF {
    @Override
    public void close() throws HiveException {
        // TODO Auto-generated method stub

    }

    @Override
    public void process(Object[] args) throws HiveException {
        for (int i = 0; i < args.length; i++) {
            if (args[i] == null || "".equals(args[i])) {
                args[i] = "";
            } else {
                args[i] = args[i].toString().replaceAll(" ", "").replaceAll("\\n", "").trim();
            }
        }
        forward(args);
    }


    @Override
    public StructObjectInspector initialize(ObjectInspector[] args)
            throws UDFArgumentException {
        if (args.length == 0) {
            throw new UDFArgumentLengthException(
                    "Courtnotice_filterUDTF takes only Postive arguments");
        }
        ArrayList<String> fieldNames = new ArrayList<String>();
        ArrayList<ObjectInspector> fieldOIs = new ArrayList<ObjectInspector>();
        String[] outschema
                = new String[args.length];
        for (int i = 0; i < outschema.length; i++) {
            fieldNames.add("output" + i);
            fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        }
        return ObjectInspectorFactory.getStandardStructObjectInspector(
                fieldNames, fieldOIs);
    }

    public static void main(String[] args) throws HiveException {


    }
}
