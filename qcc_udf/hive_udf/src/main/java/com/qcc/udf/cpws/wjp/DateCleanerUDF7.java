package com.qcc.udf.cpws.wjp;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.qcc.udf.tax.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class DateCleanerUDF7 extends UDF {

    private static final Pattern DATE_DOT_PATTERN = Pattern.compile("^(\\d{4})\\.(0?[1-9]|1[0-2])\\.(0?[1-9]|[12][0-9]|3[01])[-—].*$");
    private static final Pattern P = Pattern.compile("^(?=(?:.*年){2,}|(?:.*月){2,}|(?:.*日){2,}).*[,，].*$");
    // 匹配各种常见格式：
//  2024年4月24日
//  2024-04-24
//  2024.3.17上午9.00
//  2017-05-03 09:30
//  ……
    private static final String TIME_REGEX =
            "\\d{4}"                       // 年份
                    + "(?:年|[-\\.\\/])"                // “年” 或 “-” 或 “.”
                    + "\\s*\\d{1,2}"                 // 月
                    + "(?:月|[-\\.\\/])"                // “月” 或 “-” 或 “.”
                    + "\\s*\\d{1,2}"                 // 日
                    + "(?:日)?"
                    + "(?:"
                    + "[\\sT]*"                 // 可选空格或“T”
                    + "(?:上午|下午)?"          // 可选中文上午/下午
                    + "\\s*\\d{1,2}"           // 时
                    + "(?::|[\\.:])"           // “:” 或 “.”
                    + "\\d{1,2}"               // 分
                    + "(?::\\d{1,2})?"         // 可选秒
                    + ")?";
    private static final String regex = "(" +
            // 1. 2022-10-18,2022-10-18
            "\\d{4}-\\d{2}-\\d{2},\\d{4}-\\d{2}-\\d{2}" +
            "|" +
            // 2. 2017年5月31日至6月2日,2017-05-31 10:00
            "\\d{4}年\\d{1,2}月\\d{1,2}日[至到]\\d{1,2}月\\d{1,2}日,\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}" +
            "|" +
            // 3. 2024年4月24日,2024-04-24 09:30
            "\\d{4}年\\d{1,2}月\\d{1,2}日,\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}" +
            "|" +
            // 4. 2017年3月13日—2017年3月28日,2017.3.17上午9.00
            "\\d{4}年\\d{1,2}月\\d{1,2}日[—-]\\d{4}年\\d{1,2}月\\d{1,2}日,\\d{4}\\.\\d{1,2}\\.\\d{1,2}[上下]午\\d{1,2}[.:]\\d{2}" +
            "|" +
            // 5. 2017年05月02日至2017年05月31日,2017-05-03 09:30–10:00
            "\\d{4}年\\d{2}月\\d{2}日[至到]\\d{4}年\\d{2}月\\d{2}日,\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}[–-]\\d{2}:\\d{2}" +
            "|" +
            "\\d{4}/\\d{1,2}/\\d{1,2}" +
            ")";
    private static final String regex2 =
            "(\\d{4}年\\d{1,2}月\\d{1,2}日(至|到|—)\\d{4}年\\d{1,2}月\\d{1,2}日)" +  // 2017年05月02日至2017年05月31日
                    "|(\\d{4}年\\d{1,2}月\\d{1,2}日(至|到|—)\\d{1,2}月\\d{1,2}日)" +       // 2017年5月31日至6月2日
                    "|(\\d{4}[-./年]\\d{1,2}[-./月]\\d{1,2}日?)" +                        // 2024年4月24日 或 2017-05-03
                    "|(\\d{4}[-./]\\d{1,2}[-./]\\d{1,2}\\s*[上下]午?\\s*\\d{1,2}[.:]\\d{2})" + // 2017.3.17上午9.00
                    "|(\\d{4}[-./]\\d{1,2}[-./]\\d{1,2}\\s*\\d{1,2}:\\d{2}(–|-|—)\\d{1,2}:\\d{2})" +
                    "|(\\d{4}[-./]\\d{1,2}[-./]\\d{1,2}\\s*\\d{1,2}:\\d{2})" +
                    "|(\\d{4}[-./]\\d{1,2}[-./]\\d{1,2})";               // 简单时间格式
    ;               // 简单时间格式
    ; // 2017-05-03 09:30–10:00

    public static String evaluate(String mainDate, String secondDate, String nlpContent, String releaseDate, String dataSource, String caseNo) {
        String ktggTime = CommonDateCleanUtil.cleanDate(secondDate, DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS);
//        String ktggTime = CommonDateCleanUtil.procesTime(secondDate == null ? "" : secondDate);
        if (StringUtils.isBlank(ktggTime)) {
            secondDate = (askTime(secondDate, caseNo, releaseDate));
        } else {
            secondDate = (ktggTime);
        }


        String mainDatev2 = mainDate;
        //FX-35101  嘗試處理一下開庭的時間,提取時分秒
        //https://doc.greatld.com/pages/viewpage.action?pageId=55901412

        if (StringUtils.isNotBlank(mainDate) && StringUtils.isNotBlank(nlpContent) && nlpContent.contains("法院") && nlpContent.contains("开庭")) {
            mainDate = Arrays.stream(mainDate.split(",")).filter(value -> !value.contains("日-") && !value.contains("日——")).collect(Collectors.joining(","));
        } else if (StringUtils.isNotBlank(nlpContent) && nlpContent.contains("一周开庭")) {
            mainDate = Arrays.stream(mainDate.split(",")).filter(item -> StringUtils.isNotBlank(item) && !DATE_DOT_PATTERN.matcher(item).matches()).collect(Collectors.joining(","));
        }
        if (StringUtils.isNotBlank(mainDate) && mainDate.contains("日至") && mainDate.contains(",")) {
            mainDate = Arrays.stream(mainDate.split(",")).filter(item -> StringUtils.isNotBlank(item) && !item.contains("日至")).collect(Collectors.joining(","));
        }

        if (StringUtils.isNotEmpty(mainDate)) {
            mainDate = mainDate.replace("\"", "");
        }
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(mainDate);
        if (matcher.find()) {
            if (mainDate.contains(",")) {
                List<String> dateList = Arrays.stream(mainDate.split(",")).filter(x -> Pattern.compile(regex2).matcher(x.replace("\"", "")
                        .replace("\\","")).matches()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(dateList)) {
                    mainDate = dateList.get(dateList.size() - 1);
                } else {
                    mainDate = mainDatev2;

                }
            }
            mainDate = mainDate.replaceAll("[\"'\\s]+$", "");
            Pattern p = Pattern.compile(".*(" + TIME_REGEX + ")$");
            Matcher m = p.matcher(mainDate);
            while (m.find()) {
                mainDate = m.group();
            }
        }

        mainDate = SessionTimeUdf.evaluate(mainDate);


        String resultDate = mainDate;
        if (Strings.isNullOrEmpty(resultDate)) {
            resultDate = secondDate;
        }
        resultDate = CommonDateCleanUtil.cleanDate(resultDate, DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS);
        if (StringUtils.isBlank(resultDate)) {
            resultDate = CommonDateCleanUtil.cleanDate(secondDate, DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS);
        }


        if (StringUtils.isEmpty(resultDate)) {
            releaseDate = CommonDateCleanUtil.cleanDate(releaseDate, DateUtil.DATE_FORMAT_YYYYMMDD);
            resultDate = cleanDateByPublishDate(mainDatev2, releaseDate, dataSource, DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS, nlpContent, "");
        }


        return resultDate;
    }

    private static String askTime(String trialDate, String caseNo, String publishDate) {
        return GetCaseYearUtil.evaluate(caseNo, trialDate, publishDate);
    }

    private static final List<String> PUBLISH_DATE_DATA_SOURCE = Lists.newArrayList(
            "d_spider_fd229ddad87545a0961f21b021df11df",
            "d_spider_fca4d8d2b5e64027b635680d2ad2d76f",
            "d_spider_b479aedbfc084ca8a8dc7766911d5874",
            "d_spider_56b57be552954689b48fff5394df467f",
            "d_spider_acc5cbde4861418dbce608fd08c9ce1b",
            "d_spider_7bfa5e8b712c4503864890948da57200",
            "d_spider_410c7830858c447390bed45079a03714",
            "d_spider_623227faa72b49d0aca34f07dbbc1f7c",
            "d_spider_82b39d22b8844ddfa6f9ed513d60e20a",
            "d_spider_46c7c4b30bc144fda4165a0503643923",
            "d_spider_46c7c4b30bc144fda4165a0503643923",
            "d_spider_6fd456e0819b4112b0f2f1b227ebbe2e",
            "d_spider_81f76b7be7034856a66c6b89e114994e"
    );
    private static final List<String> TODAY_PUBLISH_DATA_SOURCE = Lists.newArrayList(
            "d_spider_5149b43b18404b46b9f98f2c29a55603"
    );
    private static final Pattern DATE_PATTERN = Pattern.compile("(\\d{4})年\\d{1,2}月\\d{1,2}日[-—]+");

    private static String cleanDateByPublishDate(String mainDate, String publishDate, String dataSource, String timeFormat, String nlpContent, String caseNo) {
        Date date = null;
        try {
            if (PUBLISH_DATE_DATA_SOURCE.contains(dataSource)) {
                if (StringUtils.isNotBlank(publishDate) && (date = DateUtil.parseStrToDate(publishDate, DateUtil.DATE_FORMAT_YYYYMMDD)) != null) {
                    Integer year = DateUtil.getYear(date);
                    return CommonDateCleanUtil.cleanDate(year + "年" + mainDate, timeFormat);
                }
            } else if (TODAY_PUBLISH_DATA_SOURCE.contains(dataSource)) {
                if (mainDate.contains("今日") && StringUtils.isNotBlank(nlpContent) && nlpContent.length() > 10) {
                    mainDate = " " + mainDate.replace("今日", "") + ":00";
                    return CommonDateCleanUtil.cleanDate(nlpContent.substring(0, 10) + mainDate, timeFormat);
                }
            } else if (nlpContent.contains("法院") && nlpContent.contains("开庭")) {
                Matcher matcher = DATE_PATTERN.matcher(nlpContent);
                if (matcher.find()) {
                    String year = matcher.group(1);
                    return CommonDateCleanUtil.cleanDate(year + "年" + mainDate, timeFormat);
                }
            } else if (nlpContent.contains("一周开庭")) {
                Matcher matcher = DATE_DOT_PATTERN.matcher(nlpContent);
                if (matcher.find()) {
                    String year = matcher.group(1);
                    return CommonDateCleanUtil.cleanDate(year + "年" + mainDate, timeFormat);
                }
            }
        } catch (Exception e) {
        }
        return null;
    }

    public static void main(String[] args) {

        String[] testCases = {
                "2019年8月26—30号,2019-08-26 10:00--10:29,星期:一"
        };

        for (String testCase : testCases) {
            System.out.println("测试字符串: " + testCase);


        System.out.println(
                evaluate(testCase
                        , "", "2017年3月13日—2017年3月28日开庭公告 原　告 : 周少娜、周文斌 被　告 ：刘自安、刘欣轲 案　由 ：股权转让 承办庭 : 民一庭 审判员 ：左斌、李艺 地　点 ：五楼 时　间 ：2017.3.17上午9.00。"
                        , "2017-03-14 16:41:56", "d_spider_4a2e88872d4147e99365642a33c0afdf", ""));
        }
    }
}
