package com.qcc.udf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.hadoop.hive.ql.exec.UDF;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.AutoRetryHttpClient;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Random;

public class GetGeocoderAddressUDF extends UDF {

    public static  final  String geoURL = "https://restapi.amap.com/v3/geocode/regeo?parameters";
    public static double x_pi = 3.14159265358979324 * 3000.0 / 180.0;
    public static final String key = "fd55c5053a2cdc9228970d03e4bd31ce";

    public static  String evaluate(String lon, String lat) throws URISyntaxException, IOException, InterruptedException {
        //QPS限制 5S内随机休眠
        Random random = new Random();
        Thread.sleep((long) random.nextInt(5) * 1000);
        double[] doubles = bd09_To_Gcj02(Double.parseDouble(lon), Double.parseDouble(lat));
        AutoRetryHttpClient httpclient = new AutoRetryHttpClient();
        URIBuilder url = new URIBuilder(geoURL);
        url.addParameter("key",key);
        url.addParameter("location",String.valueOf(doubles[0])+","+String.valueOf(doubles[1]));
        HttpGet httpGet = new HttpGet(url.build());
        HttpResponse httpResponse = httpclient.execute(httpGet);
        if (httpResponse.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
            JSONObject json = JSON.parseObject(EntityUtils.toString(httpResponse.getEntity()));
            if (json.getIntValue("status") == 1){
                JSONObject addressComponent = json.getJSONObject("regeocode").getJSONObject("addressComponent");
                return  addressComponent.getString("country")+"-"+addressComponent.getString("province")
                        +(addressComponent.getString("city").contains("[") ? "":"-"+addressComponent.getString("city"))
                        + "-" + addressComponent.getString("district");
            }else {
                throw new IOException("error info : " + "高德逆地理编码api请求错误！ 错误码 :" +
                        json.getString("infocode") + "\t" + json.getString("info"));
            }
        }else {
            throw new IOException("高德逆地理编码api请求失败" + httpResponse.getStatusLine().getReasonPhrase());
        }
    }


    public static double[] bd09_To_Gcj02(double lon, double lat) {
        double x = lon - 0.0065, y = lat - 0.006;
        double z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi);
        double theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi);
        double tempLon = z * Math.cos(theta);
        double tempLat = z * Math.sin(theta);
        double[] gps = {tempLon,tempLat};
        return gps;
    }

    //高德转百度
    public static double [] gcj02tobd09(double lng,double lat){
        double z = Math.sqrt(lng * lng + lat * lat)+ 0.00002 * Math.sin(lat * x_pi);
        double theta = Math.atan2(lat,lng)+ 0.000003 * Math.cos(lng * x_pi);
        double bd_lng = z * Math.cos(theta)+ 0.0065 ;
        double bd_lat = z * Math.sin(theta)+ 0.006 ;
        return new double [] {bd_lng,bd_lat};
    }

    public static void main(String[] args) throws IOException, URISyntaxException, InterruptedException {
        System.out.println(GetGeocoderAddressUDF.evaluate("116.481488","39.990464"));
    }

}
