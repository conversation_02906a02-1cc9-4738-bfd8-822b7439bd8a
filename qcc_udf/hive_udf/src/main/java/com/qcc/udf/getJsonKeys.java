package com.qcc.udf;


import org.apache.hadoop.hive.ql.exec.UDF;
import org.json.JSONObject;

import java.util.Iterator;


public class getJsonKeys extends UDF {

    public static String evaluate(String jsonStr) throws org.json.JSONException {
        if (jsonStr.length() == 0) {
            return "";
        }

        JSONObject obj = new JSONObject(jsonStr);
        Iterator it = obj.keys();
        String result = "";
        while (it.hasNext()) {

            result += "," + it.next().toString();

        }
        return result.length() == 0 ? result : result.substring(1);

    }

   

}
