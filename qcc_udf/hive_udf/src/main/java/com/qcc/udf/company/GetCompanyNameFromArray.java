package com.qcc.udf.company;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2020/12/31 13:43
 * @description ：获取公司信息中某个关键字
 */
public class GetCompanyNameFromArray extends UDF {

    public String evaluate(String input, String keyWord) {
        List<String> result = new ArrayList<>();
        if (StringUtils.isNotEmpty(input)) {
            JSONArray jsonArray = JSON.parseArray(input);
            for(int i=0; i< jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String value = jsonObject.getOrDefault(keyWord, "").toString();
                if (StringUtils.isNotEmpty(value)) {
                    result.add(value);
                }
            }
         }
        if (CollectionUtils.isEmpty(result)) {
            return "";
        } else {
            return result.stream().collect(Collectors.joining(","));
        }
    }

    public static void main(String[] args) {
        String test = "[{\"KeyNo\":\"6851f86ca2ba938b40196af870b58584\",\"Org\":0,\"Name\":\"浙江齐力科技有限公司\"},{\"KeyNo\":\"\",\"Org\":-1,\"Name\":\"雷现容\"},{\"KeyNo\":\"\",\"Org\":-1,\"Name\":\"孔晓燕\"},{\"KeyNo\":\"\",\"Org\":-1,\"Name\":\"孔令武\"},{\"KeyNo\":\"f54f925aa66ecfe5d7bba179553aa5a8\",\"Org\":0,\"Name\":\"浙江中权电器有限公司\"},{\"KeyNo\":\"\",\"Org\":-1,\"Name\":\"孔祥榜\"}]";
        System.out.println(new GetCompanyNameFromArray().evaluate(test, "Name"));
    }
}
