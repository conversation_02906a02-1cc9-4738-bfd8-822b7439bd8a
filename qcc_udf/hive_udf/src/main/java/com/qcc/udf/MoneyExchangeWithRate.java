package com.qcc.udf;

import com.alibaba.fastjson.JSON;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by nixb on 2018/8/29.
 */
public class MoneyExchangeWithRate {
    public String Name;

    public String Code;

    public float Rate;

    public String KeyWords;

    public String getName() {
        return Name;
    }

    public void setName(String name) {
        Name = name;
    }

    public String getCode() {
        return Code;
    }

    public void setCode(String code) {
        Code = code;
    }

    public float getRate() {
        return Rate;
    }

    public void setRate(float rate) {
        Rate = rate;
    }

    public String getKeyWords() {
        return KeyWords;
    }

    public void setKeyWords(String keyWords) {
        KeyWords = keyWords;
    }

    public List<String> keywordsDecode() {
        if (null != this.getKeyWords() && !"".equals(this.getKeyWords())) {
            return JSON.parseArray(this.getKeyWords(), String.class);
        }
        return new ArrayList<>();
    }


}
