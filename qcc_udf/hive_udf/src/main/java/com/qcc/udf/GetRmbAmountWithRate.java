package com.qcc.udf;

import com.alibaba.fastjson.JSONObject;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.List;

/**
 * Created by nixb on 2018/8/29.
 */
public class GetRmbAmountWithRate extends UDF {

    public String evaluate(String registCapi) {
        String result = "";
        try {
            if (registCapi != null && registCapi != "") {

                String str = "[{\"name\":\"瑞士法郎\",\"rate\":\"7.5237\",\"keywords\":[\"瑞士\", \"CHF\"],\"code\":\"CHF\"},{\"name\":\"韩国圆\",\"rate\":\"0.0053\",\"keywords\":[\"韩\", \"KRW\"],\"code\":\"KRW\"},{\"name\":\"日元\",\"rate\":\"0.0517\",\"keywords\":[\"日元\", \"日本\", \"JPY\"],\"code\":\"JPY\"},{\"name\":\"菲律宾比索\",\"rate\":\"0.1369\",\"keywords\":[\"比索\", \"菲律宾\", \"PHP\"],\"code\":\"PHP\"},{\"name\":\"印度卢比\",\"rate\":\"0.0977\",\"keywords\":[\"卢比\", \"印度\", \"INR\"],\"code\":\"INR\"},{\"name\":\"俄罗斯卢布\",\"rate\":\"0.1022\",\"keywords\":[\"俄罗斯\", \"卢布\"],\"code\":\"SUR\"},{\"name\":\"瑞典克朗\",\"rate\":\"0.6635\",\"keywords\":[\"瑞典\", \"SEK\"],\"code\":\"SEK\"},{\"name\":\"美元\",\"rate\":\"6.8717\",\"keywords\":[\"美元\", \"USD\"],\"code\":\"USD\"},{\"name\":\"丹麦克朗\",\"rate\":\"1.0061\",\"keywords\":[\"丹麦\", \"DKK\"],\"code\":\"DKK\"},{\"name\":\"新加坡元\",\"rate\":\"5.176\",\"keywords\":[\"新币\", \"新加坡\", \"SGD\"],\"code\":\"SGD\"},{\"name\":\"泰国铢\",\"rate\":\"0.2013\",\"keywords\":[\"泰\", \"THB\"],\"code\":\"THB\"},{\"name\":\"人民币\",\"rate\":\"1\",\"keywords\":[\"人民币\", \"CNY\"],\"code\":\"CNY\"},{\"name\":\"挪威克朗\",\"rate\":\"0.6612\",\"keywords\":[\"挪威\", \"NOK\"],\"code\":\"NOK\"},{\"name\":\"加拿大元\",\"rate\":\"5.0824\",\"keywords\":[\"加元\", \"加拿大\", \"CAD\"],\"code\":\"CAD\"},{\"name\":\"港元\",\"rate\":\"0.8754\",\"keywords\":[\"港\", \"HKD\"],\"code\":\"HKD\"},{\"name\":\"澳大利亚元\",\"rate\":\"4.6131\",\"keywords\":[\"澳元\", \"澳大利亚\", \"AUD\"],\"code\":\"AUD\"},{\"name\":\"英镑\",\"rate\":\"8.5127\",\"keywords\":[\"英镑\", \"GBP\"],\"code\":\"GBP\"},{\"name\":\"新西兰元\",\"rate\":\"4.3069\",\"keywords\":[\"新西兰\", \"NZD\"],\"code\":\"NZD\"},{\"name\":\"新台币\",\"rate\":\"0.1963\",\"keywords\":[\"台币\", \"台湾\", \"TWD\"],\"code\":\"TWD\"},{\"name\":\"缅甸元\",\"rate\":\"0.005\",\"keywords\":[\"缅甸\", \"BUK\"],\"code\":\"BUK\"},{\"name\":\"欧元\",\"rate\":\"7.4945\",\"keywords\":[\"欧元\", \"EUR\"],\"code\":\"EUR\"},{\"name\":\"马来币\",\"rate\":\"1.5562\",\"keywords\":[\"马来\", \"马币\"],\"code\":\"MYR\"},{\"name\":\"匈牙利福林\",\"rate\":\"0.0197\",\"keywords\":[],\"code\":\"HUF\"},{\"name\":\"沙特里亚尔\",\"rate\":\"1.8308\",\"keywords\":[],\"code\":\"SAR\"},{\"name\":\"土耳其新里拉\",\"rate\":\"0.3584\",\"keywords\":[],\"code\":\"TRY\"},{\"name\":\"南非兰特\",\"rate\":\"0.3856\",\"keywords\":[],\"code\":\"ZAR\"},{\"name\":\"波兰兹罗提\",\"rate\":\"1.6033\",\"keywords\":[],\"code\":\"PLN\"},{\"name\":\"阿联酋迪拉姆\",\"rate\":\"1.8713\",\"keywords\":[],\"code\":\"AED\"},{\"name\":\"墨西哥比索\",\"rate\":\"0.3798\",\"keywords\":[],\"code\":\"MXN\"},{\"name\":\"俄罗斯卢布\",\"rate\":\"0.0889\",\"keywords\":[],\"code\":\"RUB\"}]";

                List<MoneyExchangeWithRate> moneyExchangeList = JSONObject.parseArray(str, MoneyExchangeWithRate.class);
                if (moneyExchangeList != null && moneyExchangeList.size() > 0) {
                    boolean flag = false;
                    for (MoneyExchangeWithRate moneyExchange : moneyExchangeList) {
                        for (String keyWord : moneyExchange.keywordsDecode()) {
                            if (registCapi.contains(keyWord)) {
                                result = moneyExchange.getCode();
                                flag = true;
                                break;
                            }
                        }
                        if (flag) {
                            break;
                        }
                    }
                }


            }
        } catch (Exception e) {
        }
        return result;
    }
}
