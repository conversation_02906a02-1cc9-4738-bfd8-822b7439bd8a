package com.qcc.udf;

import com.qcc.udf.cpws.CommonUtil;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.LinkedHashSet;
import java.util.Set;

public class columnCompare extends UDF {

    public static String evaluate(String companynames, String pro, String def)  throws Exception{
        companynames = companynames == null ? "" : companynames;
        pro = pro == null ? "" : pro;
        def = def == null ? "" : def;

        Set<String> nameSet = new LinkedHashSet<>();
        String[] a1 = companynames.split(",");
        for (String str : a1){
            if (CommonUtil.isKeyword(str)){
                nameSet.add(str);
            }
        }

        Set<String> nameSet2 = new LinkedHashSet<>();
        String[] a2 = pro.split(",");
        for (String str : a2){
            if (CommonUtil.isKeyword(str)){
                nameSet2.add(str);
            }
        }
        String[] a3 = def.split(",");
        for (String str : a3){
            if (CommonUtil.isKeyword(str)){
                nameSet2.add(str);
            }
        }

        boolean flag = false;
        for (String str : nameSet2){
            if (!nameSet.contains(str)){
                flag = true;
                break;
            }
        }

        if (flag){
            return "true";
        }else{
            return "false";
        }
    }
}
