package com.qcc.udf.casesearch_v3.role;

import com.alibaba.fastjson.annotation.JSONField;
import com.qcc.udf.casesearch_v3.entity.input.CPWSLawyerFirmGroupInfo;

import java.beans.ConstructorProperties;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2021/8/30
 */
public class NameKeyNoEntity {
    @JSONField(
            name = "Name"
    )
    private String Name;
    @J<PERSON>NField(name = "ShowName")
    private String ShowName;
    @JSONField(
            name = "KeyNo"
    )
    private String KeyNo;
    @JSONField(
            name = "Org"
    )
    private Integer Org;
    @J<PERSON>NField(
            name = "T"
    )
    private String T;

    @JSONField(name = "Role")
    private String Role;
    @JSONField(name="Source")
    private Integer Source;
    @JSONField(name = "RoleTag")
    private Integer RoleTag;
    @JSONField(name = "RoleType")
    private Integer RoleType;
    @J<PERSON>NField(name = "RoleTypeDesc")
    private String RoleTypeDesc;

    @JSO<PERSON>ield(name = "LawFirmList")
    private List<CPWSLawyerFirmGroupInfo> LawFirmList;

    public static NameKeyNoEntity.NameKeyDtoBuilder builder() {
        return new NameKeyNoEntity.NameKeyDtoBuilder();
    }

    @Override
    public String toString() {
        return "NameKeyDto(Name=" + this.getName() + ", KeyNo=" + this.getKeyNo() + ", Org=" + this.getOrg() + ", T=" + this.getT() + ", LawFirmList=" + this.getLawFirmList() +
                ", Role=" + this.getRole() +", Source=" + this.getSource() + ", RoleTag=" + this.getRoleTag() + ", RoleType=" + this.getRoleType() + ", RoleTypeDesc=" + this.getRoleTypeDesc() +
                ")";

    }

    @ConstructorProperties({"Name", "keyNo", "org", "t", "lawFirmList", "role", "roleTag", "roleType", "roleTypeDesc"})
    public NameKeyNoEntity(String Name, String showName, String keyNo, Integer org, String t, List<CPWSLawyerFirmGroupInfo> lawFirmList, String role, Integer source, Integer roleTag, Integer roleType, String roleTypeDesc) {
        this.Name = Name;
        this.ShowName = showName;
        this.KeyNo = keyNo;
        this.Org = org;
        this.T = t;
        this.LawFirmList = lawFirmList;
        this.Role = role;
        this.Source = source;
        this.RoleTag = roleTag;
        this.RoleType = roleType;
        this.RoleTypeDesc = roleTypeDesc;
    }

    public NameKeyNoEntity() {
    }

    public String getName() {
        return this.Name;
    }

    public String getKeyNo() {
        return this.KeyNo;
    }

    public Integer getOrg() {
        return this.Org;
    }

    public String getT() {
        return this.T;
    }

    public void setName(String Name) {
        this.Name = Name;
    }

    public void setKeyNo(String keyNo) {
        this.KeyNo = keyNo;
    }

    public void setOrg(Integer org) {
        this.Org = org;
    }

    public void setT(String t) {
        this.T = t;
    }

    public String getRole() {
        return Role;
    }

    public void setRole(String role) {
        Role = role;
    }

    public Integer getSource() {
        return Source;
    }

    public void setSource(Integer source) {
        Source = source;
    }

    public Integer getRoleTag() {
        return RoleTag;
    }

    public void setRoleTag(Integer roleTag) {
        RoleTag = roleTag;
    }

    public Integer getRoleType() {
        return RoleType;
    }

    public void setRoleType(Integer roleType) {
        RoleType = roleType;
    }

    public String getRoleTypeDesc() {
        return RoleTypeDesc;
    }

    public void setRoleTypeDesc(String roleTypeDesc) {
        RoleTypeDesc = roleTypeDesc;
    }

    public List<CPWSLawyerFirmGroupInfo> getLawFirmList() {
        return LawFirmList;
    }

    public void setLawFirmList(List<CPWSLawyerFirmGroupInfo> lawFirmList) {
        LawFirmList = lawFirmList;
    }

    public String getShowName() {
        return ShowName;
    }

    public void setShowName(String showName) {
        ShowName = showName;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof NameKeyNoEntity)) {
            return false;
        } else {
            NameKeyNoEntity other = (NameKeyNoEntity) o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label59:
                {
                    Object this$Name = this.getName();
                    Object other$Name = other.getName();
                    if (this$Name == null) {
                        if (other$Name == null) {
                            break label59;
                        }
                    } else if (this$Name.equals(other$Name)) {
                        break label59;
                    }

                    return false;
                }

                Object this$keyNo = this.getKeyNo();
                Object other$keyNo = other.getKeyNo();
                if (this$keyNo == null) {
                    if (other$keyNo != null) {
                        return false;
                    }
                } else if (!this$keyNo.equals(other$keyNo)) {
                    return false;
                }

                Object this$org = this.getOrg();
                Object other$org = other.getOrg();
                if (this$org == null) {
                    if (other$org != null) {
                        return false;
                    }
                } else if (!this$org.equals(other$org)) {
                    return false;
                }

                Object this$t = this.getT();
                Object other$t = other.getT();
                if (this$t == null) {
                    if (other$t != null) {
                        return false;
                    }
                } else if (!this$t.equals(other$t)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof NameKeyNoEntity;
    }

    public int hashCode() {
        int result = 1;
        Object $Name = this.getName();
        result = result * 59 + ($Name == null ? 43 : $Name.hashCode());
        Object $keyNo = this.getKeyNo();
        result = result * 59 + ($keyNo == null ? 43 : $keyNo.hashCode());
        Object $org = this.getOrg();
        result = result * 59 + ($org == null ? 43 : $org.hashCode());
        Object $t = this.getT();
        result = result * 59 + ($t == null ? 43 : $t.hashCode());
        return result;
    }

    public static class NameKeyDtoBuilder {
        private String Name;
        private String ShowName;
        private String KeyNo;
        private Integer Org;
        private String T;
        private String Role;
        private Integer Source;
        private Integer RoleTag;
        private Integer RoleType;
        private String RoleTypeDesc;
        private List<CPWSLawyerFirmGroupInfo> LawFirmList;

        NameKeyDtoBuilder() {
        }

        public NameKeyNoEntity.NameKeyDtoBuilder Name(String Name) {
            this.Name = Name;
            return this;
        }

        public NameKeyNoEntity.NameKeyDtoBuilder ShowName(String ShowName) {
            this.ShowName = ShowName;
            return this;
        }

        public NameKeyNoEntity.NameKeyDtoBuilder keyNo(String keyNo) {
            this.KeyNo = keyNo;
            return this;
        }

        public NameKeyNoEntity.NameKeyDtoBuilder org(Integer org) {
            this.Org = org;
            return this;
        }

        public NameKeyNoEntity.NameKeyDtoBuilder t(String t) {
            this.T = t;
            return this;
        }

        public NameKeyNoEntity.NameKeyDtoBuilder Role(String Role) {
            this.Role = Role;
            return this;
        }

        public NameKeyNoEntity.NameKeyDtoBuilder Source(Integer Source) {
            this.Source = Source;
            return this;
        }

        public NameKeyNoEntity.NameKeyDtoBuilder RoleTag(Integer RoleTag) {
            this.RoleTag = RoleTag;
            return this;
        }

        public NameKeyNoEntity.NameKeyDtoBuilder RoleType(Integer RoleType) {
            this.RoleType = RoleType;
            return this;
        }

        public NameKeyNoEntity.NameKeyDtoBuilder RoleTypeDesc(String RoleTypeDesc) {
            this.RoleTypeDesc = RoleTypeDesc;
            return this;
        }

        public NameKeyNoEntity.NameKeyDtoBuilder LawFirmList(List<CPWSLawyerFirmGroupInfo> LawFirmList) {
            this.LawFirmList = LawFirmList;
            return this;
        }

        public NameKeyNoEntity build() {
            return new NameKeyNoEntity(this.Name, this.ShowName, this.KeyNo, this.Org, this.T, this.LawFirmList, this.Role,this.Source, this.RoleTag, this.RoleType, this.RoleTypeDesc);
        }

        @Override
        public String toString() {
            return "NameKeyDto.NameKeyDtoBuilder(Name=" + this.Name + "ShowName=" + this.ShowName + ", KeyNo=" + this.KeyNo + ", Org=" + this.Org + ", T=" + this.T + ", LawFirmList=" + this.LawFirmList +
                    ", Role=" + this.Role +", Source=" + this.Source + ", RoleTag=" + this.RoleTag + ", RoleType=" + this.RoleType + ", RoleTypeDesc=" + this.RoleTypeDesc
                    + ")";
        }
    }
}
