package com.qcc.udf.casesearch_v3.entity.output;

import com.qcc.udf.casesearch_v3.entity.input.CPWSLawyerFirmGroupInfo;
import com.qcc.udf.casesearch_v3.entity.input.CPWSLawyerGroupInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NameAndKeyNoEntity  extends BaseCaseOutEntity{
    @JSONField(name = "Name")
    private String name = "";
    @JSONField(name = "KeyNo")
    private String keyNo = "";
    @JSONField(name = "Org")
    private Integer org = 0;
    @J<PERSON>NField(name = "Role")
    private String role;
    /**
     * 判决结果
     */
    @JSONField(name = "LR")
    private String lawsuitResult;

    @JSONField(name = "LawFirmList")
    private List<CPWSLawyerFirmGroupInfo> LawFirmList;

    /**
     * 判决结果v2
     */
    @J<PERSON><PERSON>ield(name = "JR")
    private String lawsuitResultV2;
}
