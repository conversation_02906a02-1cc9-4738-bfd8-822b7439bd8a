package com.qcc.udf.cpws;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.stream.Collectors;

public class GetDetailInfoZXUDF extends UDF {

    public String evaluate(List<String> sourceSxList, List<String> sourceZxList, List<String> sourceXgList, List<String> sourceCaseList) {
        LawSuitEntity inputLawSuitEntity = new LawSuitEntity();
        inputLawSuitEntity.setSxList(sourceSxList);
        inputLawSuitEntity.setZxList(sourceZxList);
        inputLawSuitEntity.setXgList(sourceXgList);
        inputLawSuitEntity.setCaseList(sourceCaseList);
        List<LawSuitEntity> lawSuitEntityList = CommonUtil.getLawSuitEntityList(inputLawSuitEntity, "zx");

        JSONArray detailInfoArray = new JSONArray();
        for (LawSuitEntity lawSuitEntity : lawSuitEntityList) {
            List<String> sxList = lawSuitEntity.getSxList();
            List<String> zxList = lawSuitEntity.getZxList();
            List<String> xgList = lawSuitEntity.getXgList();
            List<String> caseList = lawSuitEntity.getCaseList();

            Set<String> provinceCodeSet = CommonUtil.collectProvinceCode(sxList, zxList, xgList, caseList);
            for (String provinceCode : provinceCodeSet) {
                JSONObject result = new JSONObject();
                try {
                    /**
                     * 各审理程序列表对应字段
                     */
                    // 审理程序总数集合
                    Set<String> caseNoSet = new LinkedHashSet<>();
                    // 搜索关键字集合
                    Set<String> searchWordSet = new HashSet<>();
                    // 所有时间节点的map集合（key-> 时间戳; value->表示当前的节点状态，审判程序 + (被执行人/失信人/限制高消费)发布日期 或 判决日期 或 裁定日期）
                    Map<Long, String> trialRoundDateNodeMap = new HashMap<>();

                    // 案号分组
                    Map<String, JSONObject> anNoMap = new LinkedHashMap<>();
                    Set<String> sxIdSet = new LinkedHashSet<>();
                    Set<String> zxIdSet = new LinkedHashSet<>();
                    Set<String> xgIdSet = new LinkedHashSet<>();
                    Set<String> caseIdSet = new LinkedHashSet<>();

                    dataClean(provinceCode, sxList, 1, anNoMap, caseNoSet, sxIdSet, searchWordSet, trialRoundDateNodeMap);
                    dataClean(provinceCode, zxList, 2, anNoMap, caseNoSet, zxIdSet, searchWordSet, trialRoundDateNodeMap);
                    dataClean(provinceCode, xgList, 3, anNoMap, caseNoSet, xgIdSet, searchWordSet, trialRoundDateNodeMap);
                    dataClean(provinceCode, caseList, 4, anNoMap, caseNoSet, caseIdSet, searchWordSet, trialRoundDateNodeMap);

                    // 按照案号显示数据
                    JSONArray array = new JSONArray();
                    for (String str : caseNoSet) {
                        /**
                         * 兼容ms案件中的infoList项
                         */
                        JSONObject jsonObj = anNoMap.get(str);
                        if (jsonObj != null) {
                            jsonObj = CommonUtil.addExternalFieldToJsonStruct(jsonObj);
                            Long latestTimestamp = CommonUtil.getLatestTimestampFromInfoListItem(jsonObj);
                            jsonObj.put("LatestTimestamp", latestTimestamp);
                            array.add(jsonObj);
                        }
                    }
                    result.put("InfoList", array);

                    /**
                     * 3.案件统计字段
                     */
                    // 最新审理程序
                    result.put("LatestTrialRound", CommonUtil.getLatestTrialRoundFromInfoList(array));
                    // 审理程序总数
                    result.put("AnnoCnt", caseNoSet.size());
                    // 关联裁判文书数 / 有效裁判文书数
                    result.put("CaseCnt",  caseIdSet.size());
//                    result.put("CaseCnt",  CommonUtil.calculateListSize(caseList, provinceCode, "case", "执行类案件"));
                    // 关联被执行数 / 有效被执行数
                    result.put("ZxCnt", zxIdSet.size());
//                    result.put("ZxCnt", CommonUtil.calculateListSize(zxList, provinceCode, "zx", "执行类案件"));
                    // 关联失信数 / 有效失信数
                    result.put("SxCnt", sxIdSet.size());
//                    result.put("SxCnt", CommonUtil.calculateListSize(sxList, provinceCode, "sx", "执行类案件"));
                    // 关联限高数 / 有效限高数
                    result.put("XgCnt", xgIdSet.size());
//                    result.put("XgCnt", CommonUtil.calculateListSize(xgList, provinceCode, "xg", "执行类案件"));

                    // 兼容字段处理
                    // 关联立案信息数 / 有效立案信息数
                    result.put("LianCnt", 0);
                    // 关联开庭公告数 / 有效开庭公告数
                    result.put("KtggCnt", 0);
                    // 关联送达公告数 / 有效送达公告数
                    result.put("SdggCnt", 0);
                    // 关联法院公告数 / 有效法院公告数
                    result.put("FyggCnt", 0);

                    /**
                     * 案件基础字段
                     */
                    // 分组法院信息
                    result.put("GroupCourt", lawSuitEntity.getCourt());
                    // 所在省份编码
                    result.put("Province", provinceCode);
                    // 案件名称
                    result.put("CaseName", CommonUtil.getCaseNameFromInfoList(array, "zx"));
                    // 案件类型
                    result.put("CaseType", "执行案件");
                    // 关联的公司或个人信息
                    result.put("CompanyKeywords", CommonUtil.getCompanyKeywordsFromSearchWordSet(searchWordSet));
                    // 相关案号
                    result.put("AnNoList", CommonUtil.getKeywordsFromInfoList(array, "AnNo"));
                    // 列表中的案由
                    result.put("CaseReason", CommonUtil.getCaseReasonFromInfoList(array));
                    // 列表中的案件身份
                    result.put("CaseRole", getCaseRoleInfo(caseList, array, provinceCode));
                    // 相关法院
                    result.put("CourtList", CommonUtil.getKeywordsFromInfoList(array, "Court"));
                    // 相关检察院
                    result.put("ProcuratorateList", CommonUtil.getKeywordsFromInfoList(array, "Procuratorate"));

                    Map<Long, String> sortedDateNodeMap = new LinkedHashMap<>();
                    trialRoundDateNodeMap.entrySet().stream()
                            .sorted(Map.Entry.comparingByKey())
                            .forEachOrdered(e -> sortedDateNodeMap.put(e.getKey(), e.getValue()));

                    result.put("EarliestDate", -1L);
                    result.put("EarliestDateType", "");
                    result.put("LastestDate", -1);
                    result.put("LastestDateType", "");

                    Long earliestDate = -1L;
                    String earliestDateType = "";
                    Long lastestDate = -1L;
                    String lastestDateType = "";
                    boolean flag = true;
                    for (Map.Entry<Long, String> sortedDateNodeEntry : sortedDateNodeMap.entrySet()) {
                        if (flag) {
                            earliestDate = sortedDateNodeEntry.getKey();
                            earliestDateType = sortedDateNodeEntry.getValue();
                            flag = false;
                        }
                        lastestDate = sortedDateNodeEntry.getKey();
                        lastestDateType = sortedDateNodeEntry.getValue();
                    }
                    result.put("EarliestDate", earliestDate);
                    result.put("EarliestDateType", CommonUtil.getDataTypeWithoutTrialRound(earliestDateType));
                    if (sortedDateNodeMap.size() > 1) {
                        result.put("LastestDate", lastestDate);
                        result.put("LastestDateType", CommonUtil.getDataTypeWithoutTrialRound(lastestDateType));
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                detailInfoArray.add(result);
            }
        }

        JSONArray resDetailInfoJSONArray = new JSONArray();
        Iterator iterator = detailInfoArray.iterator();
        while (iterator.hasNext()) {
            try {
                JSONObject jsonObject = (JSONObject) iterator.next();
                if (jsonObject.getLong("AnnoCnt") > 0) {
                    resDetailInfoJSONArray.add(jsonObject);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return resDetailInfoJSONArray.toJSONString();
    }

    public static void dataClean(String provinceCode, List<String> infoList, int type, Map<String, JSONObject> anNoMap,
                                 Set<String> caseNoSet, Set<String> idSet, Set<String> searchWordSet, Map<Long, String> trialRoundDateNodeMap){
        // 编辑数据
        if (infoList != null && infoList.size() > 0){
            Iterator it = infoList.iterator();
            while(it.hasNext()){
                JSONObject json = CommonUtil.getJsonObject(it.next());
                if (json == null || json.isEmpty()){
                    continue;
                }
                // 过滤各维度的涉诉历史记录
                if (json.getInteger("isvalid") != 1) {
                    continue;
                }
                // 过滤掉非指定省份编码的记录
                if (!json.getString("province").equals(provinceCode)) {
                    continue;
                }

                // 获取案号
                String anNo = "";
                if (type == 1 || type == 2){
                    anNo = CommonUtil.full2Half(json.getString("anno"));
                }else if(type == 3 || type == 4){
                    anNo = CommonUtil.full2Half(json.getString("caseno"));
                }
                // 过滤掉案号没有对应到执行案件类型的记录
                if (!new ExtractCaseTypeUDF().evaluate(anNo).equals("执行类案件")) {
                    continue;
                }

                caseNoSet.add(anNo);
                // 部分字段的汇总逻辑
                try {
                    List<String> companyNameList = new ArrayList<>();
                    if (type == 3) {
                        companyNameList.add(json.getString("personname"));
                        companyNameList.add(json.getString("personid"));
                        companyNameList.add(json.getString("keyno"));
                        companyNameList.add(json.getString("companyname"));
                    } else {
                        companyNameList = Arrays.stream(json.getString("companynames").split(","))
                                .collect(Collectors.toList());
                    }

                    // 汇总关联公司或个人信息
                    for (String companyName : companyNameList) {
                        searchWordSet.add(companyName);
                    }
                } catch (Exception ex) {
                }

                // 判断该案号是否已经存在
                JSONObject jsonObject = new JSONObject();
                JSONArray itemArray = new JSONArray();
                JSONObject itemJson = new JSONObject();

                // 不存在，则创建新的对象
                if (!anNoMap.keySet().contains(anNo)){
                    // 列表数据
                    itemJson = editItemJson(json, type, trialRoundDateNodeMap, anNo);
                }else{
                    // 存在则获取原有列表，进行数据补充
                    jsonObject = anNoMap.get(anNo);
                    // 列表数据
                    if (type == 1){
                        itemArray = jsonObject.getJSONArray("SxList");
                    }else if (type == 2){
                        itemArray = jsonObject.getJSONArray("ZxList");
                    }else if (type == 3){
                        itemArray = jsonObject.getJSONArray("XgList");
                    }else if (type == 4){
                        itemArray = jsonObject.getJSONArray("CaseList");
                    }
                    if (!idSet.contains(json.getString("id"))){
                        itemJson = editItemJson(json, type, trialRoundDateNodeMap, anNo);
                    }
                }
                idSet.add(json.getString("id"));
                itemArray = itemArray == null ? new JSONArray() : itemArray;
                itemArray.add(itemJson);
                if (type == 1){
                    jsonObject.put("SxList", itemArray);
                }else if (type == 2){
                    jsonObject.put("ZxList", itemArray);
                }else if (type == 3){
                    jsonObject.put("XgList", itemArray);
                }else if (type == 4){
                    jsonObject.put("CaseList", itemArray);
                }
                jsonObject.put("AnNo", anNo);
                jsonObject.put("TrialRound", new ExtractCaseTrialRoundUDF().evaluate(anNo));
                jsonObject.put("CaseReason", "");
                jsonObject.put("Prosecutor", new JSONArray());
                jsonObject.put("Defendant", new JSONArray());
                jsonObject.put("Court", "");
                jsonObject.put("Procuratorate", "");

                if (type == 4) {    // 从裁判文书信息中提取案由 / 当事人（双方）/ 执行法院
                    String caseReason = json.getString("casereason");
                    if (StringUtils.isNotBlank(caseReason)) {
                        jsonObject.put("CaseReason", caseReason);
                    }

                    JSONArray prosecutorJsonArray = CommonUtil.getLitigantJSONArray(json.getString("prosecutor"), json.getString("caserole"));
                    if (prosecutorJsonArray != null && prosecutorJsonArray.size() > 0) {
                        jsonObject.put("Prosecutor", prosecutorJsonArray);
                    }

                    JSONArray defendantJsonArray = CommonUtil.getLitigantJSONArray(json.getString("defendant"), json.getString("caserole"));
                    if (defendantJsonArray != null && defendantJsonArray.size() > 0) {
                        jsonObject.put("Defendant", defendantJsonArray);
                    }

                    String court = json.getString("court");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }

                    JSONObject protestorganJsonObj = JSONObject.parseObject(json.getString("protestorgan"));
                    if (protestorganJsonObj != null && protestorganJsonObj.containsKey("name")) {
                        jsonObject.put("Procuratorate", protestorganJsonObj.getString("name"));
                    }
                } else if (type == 1 || type == 2) {    // 从失信/被执行中提取并汇总当事人（被执行一方）
                    JSONArray defendantJsonArray = jsonObject.getJSONArray("Defendant");
                    if (defendantJsonArray == null) {
                        defendantJsonArray = new JSONArray();
                    }

                    JSONArray nameAndKeyNoArray = JSONArray.parseArray(json.getString("nameandkeyno"));
                    Iterator iterator = nameAndKeyNoArray.iterator();
                    while (iterator.hasNext()) {
                        JSONObject nameAndKeyNo = (JSONObject) iterator.next();

                        JSONObject caseRoleJsonObj = new JSONObject();
                        caseRoleJsonObj.put("Name", nameAndKeyNo.getString("Name"));
                        caseRoleJsonObj.put("KeyNo", nameAndKeyNo.getString("KeyNo"));
                        caseRoleJsonObj.put("Org", nameAndKeyNo.getInteger("Org"));
                        caseRoleJsonObj.put("Role", "被执行人");
                        defendantJsonArray.add(caseRoleJsonObj);
                    }
                    jsonObject.put("Defendant", defendantJsonArray);

                    // 法院提取
                    String court = json.getString("executegov");
                    if (StringUtils.isNotBlank(court)) {
                        jsonObject.put("Court", court);
                    }
                } else if (type == 3) { // 限高中提取并汇总当事人（被执行一方）
                    JSONArray defendantJsonArray = jsonObject.getJSONArray("Defendant");
                    if (defendantJsonArray == null) {
                        defendantJsonArray = new JSONArray();
                    }

                    String personName = json.getString("personname");
                    String personid = json.getString("personid");
                    if (StringUtils.isNotBlank(personName)) {
                        JSONObject personJson = new JSONObject();
                        personJson.put("Name", personName);
                        personJson.put("KeyNo", StringUtils.isNotBlank(personid) ? personid : "");
                        personJson.put("Org", CommonUtil.getOrgByKeyNo(personJson.getString("KeyNo"), personJson.getString("Name")));
                        personJson.put("Role", "被执行人");
                        defendantJsonArray.add(personJson);
                    }

                    String companyName = json.getString("companyname");
                    String companyKeyNo = json.getString("keyno");
                    if (StringUtils.isNotBlank(companyName)) {
                        JSONObject companyJson = new JSONObject();
                        companyJson.put("Name", companyName);
                        companyJson.put("KeyNo", StringUtils.isNotBlank(companyKeyNo) ? companyKeyNo : "");
                        companyJson.put("Org", CommonUtil.getOrgByKeyNo(companyJson.getString("KeyNo"), companyJson.getString("Name")));
                        companyJson.put("Role", "被执行人");
                        defendantJsonArray.add(companyJson);
                    }
                    jsonObject.put("Defendant", defendantJsonArray);
                }
                anNoMap.put(anNo, jsonObject);
            }
        }
    }

    public static JSONObject editItemJson(JSONObject jsonObject, int type, Map<Long, String> dateNodeMap, String anNo){
        JSONObject result = new JSONObject();

        String trialRound = new ExtractCaseTrialRoundUDF().evaluate(anNo);
        // 编辑字段
        result.put("Id", jsonObject.getString("id"));
        result.put("IsValid", jsonObject.getInteger("isvalid"));
        if (type == 1){
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = CommonUtil.parseDateToTimeStamp(jsonObject.getString("publicdate"));
            result.put("PublishDate", publishDate);
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "失信人发布日期");
            }
        } else if (type == 2){
            result.put("Id", jsonObject.getString("id"));
            result.put("NameAndKeyNo", jsonObject.getJSONArray("nameandkeyno"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long lianDate = CommonUtil.parseDateToTimeStamp(jsonObject.getString("liandate"));
            result.put("LianDate", lianDate);
            if (lianDate != -1) {
                dateNodeMap.put(lianDate, trialRound + "|" + "被执行人立案日期");
            }
        } else if (type == 3){
            JSONObject json = new JSONObject();
            json.put("Name", jsonObject.getString("personname"));
            json.put("KeyNo", jsonObject.getString("personid"));
            json.put("Org", CommonUtil.getOrgByKeyNo(jsonObject.getString("personid"), jsonObject.getString("personname")));
            result.put("NameAndKeyNo", json);

            JSONObject companyJson = new JSONObject();
            String companyKeyNo = jsonObject.getString("keyno");
            String companyName = jsonObject.getString("companyname");
            companyJson.put("Name", StringUtils.isNotBlank(companyName) ? companyName : "");
            companyJson.put("KeyNo", StringUtils.isNotBlank(companyKeyNo) ? companyKeyNo : "");
            companyJson.put("Org", CommonUtil.getOrgByKeyNo(companyJson.getString("KeyNo"), companyJson.getString("Name")));
            result.put("CompanyInfo", companyJson);

            result.put("Id", jsonObject.getString("id"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long publishDate = jsonObject.getLong("publishdate") != null ? jsonObject.getLong("publishdate") : -1L;
            result.put("PublishDate", publishDate);
            if (publishDate != -1) {
                dateNodeMap.put(publishDate, trialRound + "|" + "限制高消费发布日期");
            }
        } else if (type == 4){
            result.put("Id", jsonObject.getString("id"));
            result.put("IsValid", jsonObject.getInteger("isvalid"));

            Long judgeDate = CommonUtil.parseDateToTimeStamp(jsonObject.getString("judgedate"));
            result.put("JudgeDate", judgeDate);

            String docType = jsonObject.getString("doctype");
            if (StringUtils.isNotBlank(docType)) {
                if (docType.equals("ver")) {
                    result.put("DocType", "执行判决日期");
                    dateNodeMap.put(judgeDate, trialRound + "|" + "判决日期");
                } else {
                    result.put("DocType", "执行裁定日期");
                    dateNodeMap.put(judgeDate, trialRound + "|" + "裁定日期");
                }
            }
        }
        return result;
    }

    /**
     * 提取案件角色信息
     */
    private static String getCaseRoleInfo(List<String> caseList, JSONArray infoList, String provinceCode) {
        /**
         * 提取规则
         * 1，如果有裁判文书，取裁判文书中的当事人信息 - caserole
         * 2，如果裁判文书缺失，取infoList中第一个item中的被告信息作为caserole
         */
        String caseRole = CommonUtil.getCaseRoleFromCaseList(caseList, provinceCode);
        if (caseRole.equals(new JSONArray().toJSONString())) {
            try {
                List<JSONObject> jsonObjectList = infoList.stream()
                        .map(e -> {
                            try {
                                return (JSONObject) e;
                            } catch (Exception ex) {
                                return null;
                            }
                        })
                        .filter(e -> e != null && e.containsKey("LatestTimestamp"))
                        .sorted(Comparator.comparingLong(e -> e.getLong("LatestTimestamp")))
                        .collect(Collectors.toList());
                if (jsonObjectList != null && jsonObjectList.size() > 0) {
                    JSONObject jsonObject = jsonObjectList.get(0);
                    JSONArray defendantJsonArray = jsonObject.getJSONArray("Defendant");
                    if (defendantJsonArray != null && defendantJsonArray.size() > 0) {
                        Iterator iterator = defendantJsonArray.iterator();

                        JSONArray caseRoleJsonArray = new JSONArray();
                        while (iterator.hasNext()) {
                            JSONObject defendantJson = (JSONObject) iterator.next();
                            JSONObject caseRoleJson = new JSONObject();
                            caseRoleJson.put("P", (StringUtils.isNotBlank(defendantJson.getString("Name")) ? defendantJson.getString("Name") : ""));
                            caseRoleJson.put("R", (StringUtils.isNotBlank(defendantJson.getString("Role")) ? defendantJson.getString("Role") : ""));
                            caseRoleJson.put("N", (StringUtils.isNotBlank(defendantJson.getString("KeyNo")) ? defendantJson.getString("KeyNo") : ""));
                            caseRoleJson.put("O", (defendantJson.getInteger("Org") != null ? defendantJson.getInteger("Org") : -1));
                            caseRoleJsonArray.add(caseRoleJson);
                        }
                        caseRole = caseRoleJsonArray.toJSONString();
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return caseRole;
    }


    public static void main(String[] args) {
        List<String> sxList = new ArrayList();
        sxList.add("{\"id\":\"22de063321d684dff8b0321b7d85acc72\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"余良\\\"}]\",\"liandate\":\"2017-01-10 00:00:00.0\",\"anno\":\"（2017）川1722执92号\",\"executegov\":\"宣汉县人民法院\",\"publicdate\":\"2017-01-22 00:00:00.0\",\"isvalid\":\"0\",\"companynames\":\"余良\",\"province\":\"SC\"}");
        sxList.add("{\"id\":\"e8be507aa77fed99b4267972d08cd5c62\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"姚烈斌\\\"}]\",\"liandate\":\"2017-01-10 00:00:00.0\",\"anno\":\"（2017）川1722执92号\",\"executegov\":\"宣汉县人民法院\",\"publicdate\":\"2017-01-22 00:00:00.0\",\"isvalid\":\"0\",\"companynames\":\"姚烈斌\",\"province\":\"SC\"}");

        List<String> zxList = new ArrayList();
        zxList.add("{\"id\":\"22de063321d684dff8b0321b7d85acc71\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"余良\\\"}]\",\"liandate\":\"2017-01-10 00:00:00.0\",\"anno\":\"（2017）川1722执92号\",\"executegov\":\"宣汉县人民法院\",\"isvalid\":\"0\",\"companynames\":\"余良\",\"province\":\"SC\"}");
        zxList.add("{\"id\":\"e8be507aa77fed99b4267972d08cd5c61\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"姚烈斌\\\"}]\",\"liandate\":\"2017-01-10 00:00:00.0\",\"anno\":\"（2017）川1722执92号\",\"executegov\":\"宣汉县人民法院\",\"isvalid\":\"0\",\"companynames\":\"姚烈斌\",\"province\":\"SC\"}");

        List<String> xgList = new ArrayList();

        List<String> caseList = new ArrayList();
        caseList.add("{\"id\":\"08a707289538e27bc16bb231d2e0b4dd0\",\"defendant\":\"姚烈斌\",\"prosecutor\":\"罗兆明\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"罗兆明\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"姚烈斌\\\"}]\",\"caseno\":\"（2018）川1722执异16号\",\"submitdate\":\"2018-11-27T00:00:00+08:00\",\"judgedate\":\"2018-08-07T00:00:00+08:00\",\"courtdate\":\"2018-11-27T00:00:00+08:00\",\"casereason\":\"合同纠纷\",\"isvalid\":\"1\",\"trialround\":\"执行异议\",\"court\":\"宣汉县人民法院\",\"companynames\":\"姚烈斌,罗兆明,余良\",\"caserole\":\"[{\\\"P\\\":\\\"罗兆明\\\",\\\"R\\\":\\\"申请执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"姚烈斌\\\",\\\"R\\\":\\\"被执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2}]\",\"doctype\":\"adj\",\"protestorgan\":\"\",\"province\":\"SC\"}");
        caseList.add("{\"id\":\"ddb43e6104013ed2cc67b9a3eb8f5eff0\",\"defendant\":\"姚烈斌,余良,p39662b6d9a469ebc8b4cd89ca7d1955\",\"prosecutor\":\"罗兆明\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"罗兆明\\\"},{\\\"KeyNo\\\":\\\"p39662b6d9a469ebc8b4cd89ca7d1955\\\",\\\"Org\\\":2,\\\"Name\\\":\\\"姚烈斌\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"余良\\\"}]\",\"caseno\":\"（2017）川1722执92号\",\"submitdate\":\"2017-04-17T08:00:00+08:00\",\"judgedate\":\"2017-04-05T08:00:00+08:00\",\"courtdate\":\"2017-04-17T08:00:00+08:00\",\"casereason\":\"民间借贷纠纷\",\"isvalid\":\"1\",\"trialround\":\"首次执行\",\"court\":\"宣汉县人民法院\",\"companynames\":\"姚烈斌,罗兆明,余良,p39662b6d9a469ebc8b4cd89ca7d1955\",\"caserole\":\"[{\\\"P\\\":\\\"罗兆明\\\",\\\"R\\\":\\\"申请执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"姚烈斌\\\",\\\"R\\\":\\\"被执行人\\\",\\\"N\\\":\\\"p39662b6d9a469ebc8b4cd89ca7d1955\\\",\\\"O\\\":2},{\\\"P\\\":\\\"余良\\\",\\\"R\\\":\\\"被执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2}]\",\"doctype\":\"adj\",\"protestorgan\":\"\",\"province\":\"SC\"}");
        caseList.add("{\"id\":\"9728765a70fc8c93c49d567075a431290\",\"defendant\":\"姚烈斌\",\"prosecutor\":\"罗兆明,余良\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"余良\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"罗兆明\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"姚烈斌\\\"}]\",\"caseno\":\"（2018）川17执复48号\",\"submitdate\":\"2018-12-25T00:00:00+08:00\",\"judgedate\":\"2018-09-29T00:00:00+08:00\",\"courtdate\":\"2018-12-25T00:00:00+08:00\",\"casereason\":\"民间借贷纠纷\",\"isvalid\":\"1\",\"trialround\":\"执行复议\",\"court\":\"四川省达州市中级人民法院\",\"companynames\":\"姚烈斌,罗兆明,余良\",\"caserole\":\"[{\\\"P\\\":\\\"余良\\\",\\\"R\\\":\\\"复议申请人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"罗兆明\\\",\\\"R\\\":\\\"申请执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"姚烈斌\\\",\\\"R\\\":\\\"被执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2}]\",\"doctype\":\"adj\",\"protestorgan\":\"\",\"province\":\"SC\"} ");
        caseList.add("{\"id\":\"e268ec86d327f7d8e3ecc46f447e2cf90\",\"defendant\":\"姚烈斌\",\"prosecutor\":\"罗兆明\",\"nameandkeyno\":\"[{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"罗兆明\\\"},{\\\"KeyNo\\\":\\\"\\\",\\\"Org\\\":-1,\\\"Name\\\":\\\"姚烈斌\\\"}]\",\"caseno\":\"（2018）川1722执异38号\",\"submitdate\":\"2019-03-28T00:00:00+08:00\",\"judgedate\":\"2018-11-30T00:00:00+08:00\",\"courtdate\":\"2019-03-28T00:00:00+08:00\",\"casereason\":\"借款合同纠纷\",\"isvalid\":\"1\",\"trialround\":\"执行异议\",\"court\":\"宣汉县人民法院\",\"companynames\":\"姚烈斌,罗兆明\",\"caserole\":\"[{\\\"P\\\":\\\"罗兆明\\\",\\\"R\\\":\\\"申请执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"姚烈斌\\\",\\\"R\\\":\\\"被执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2}]\",\"doctype\":\"adj\",\"protestorgan\":\"\",\"province\":\"SC\"}");

        String output = new GetDetailInfoZXUDF().evaluate(sxList, zxList, xgList, caseList);
        System.out.println(output);

    }
}
