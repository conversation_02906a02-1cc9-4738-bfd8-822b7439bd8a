package com.qcc.udf.company;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023-01-11 18:49
 */
public class getSortedMinNum extends UDF {

    public String evaluate(String value) {
        if (StringUtils.isEmpty(value)) {
            return "";
        }

        return Arrays.stream(value.split(",")).map(s -> Integer.valueOf(s)).sorted().map(s -> s.toString()).findFirst().orElse("");
    }

    public static void main(String[] args) {
        getSortedMinNum model = new getSortedMinNum();
        String result = model.evaluate("11,20,2,3,5,6");
    }
}
