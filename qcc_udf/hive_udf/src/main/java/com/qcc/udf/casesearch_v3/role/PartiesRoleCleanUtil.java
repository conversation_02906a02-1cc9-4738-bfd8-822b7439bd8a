package com.qcc.udf.casesearch_v3.role;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Strings;
import com.qcc.udf.cpws.CommonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 开庭/立案 当事人身份清洗
 * @desc
 * @date 2021/1/11
 */
public class PartiesRoleCleanUtil {

    private static String Filter_char = "=|[A-Z]+|[a-z]+|\\[|\\]|【|】|<|>|\\.|\\{|\\}";
    private static Pattern LAST_CHINESE_PATTERN = Pattern.compile("[\u4e00-\u9fa5](?=[^\u4e00-\u9fa5]*?$)");
    private static String Filter_part_regex = "书记员|\\?|审判员|人民陪审员|目录|null|法官助理|未指定|其他|公诉|审判长|异常数据（仅一个 |检察员|无|承办人|执行员|院长| 原案|广检|合议庭成员|陪审员|封面|复核人|公益诉讼人|出庭检察员|潮南检察|检察院|法官";


    /**
     * 根据爬虫parties和基础表当事人，清洗当事人身份
     */
    public static String cleanPartiesRoleAndRoleTag(PartiesRoleEntity item) {
        String keynoArray = item.getKeynoArray();
        if (StringUtils.isBlank(keynoArray)) {
            return keynoArray;
        }

        List<NameKeyNoEntity> keynoList = Collections.EMPTY_LIST;
        try {
            keynoList = JSON.parseArray(item.getKeynoArray(), NameKeyNoEntity.class);
        } catch (Exception e) {
        }
        if (CollectionUtils.isEmpty(keynoList)) {
            return keynoArray;
        }

        //爬虫当事人预清洗
        String newItem = preClean(item.getSpiderParties());
        //清洗爬虫当事人
        Map<String, String> partRoleMap = cleanSpiderParties(newItem);

        //获取所有当事人的roleTag
        List<NameKeyNoEntity> partRoles = keynoList;
        Map<String, Integer> partiesRoleTagMap = item.getPartiesRoleTagMap();
        //根据案号获取审理进程,通过审理进程取得对应的身份枚举
        CaseNoTrialRoundEnum caseNoTrialRoundEnum = CaseNoTrialRoundEnum.getCaseNoTrialRoundEnum(item.getCaseNo());
        partRoles = keynoList.stream().filter(e -> e != null).filter(e -> !filterTestName(e.getName()))
                .map(e -> {
                    Integer roleTag = getRoleTag(partiesRoleTagMap, e.getName());
                    String role = "";
                    Integer source = -1;
                    //1.从爬虫当事人获取身份
                    if (!partRoleMap.isEmpty()) {
                        role = getNameToRole(partRoleMap, e.getName());
                        //校验role
                        role = CheckPartyRoleUtil.checkRole(role);
                        if (StringUtils.isNotBlank(role)){
                            source = 0;
                        }
                    }

                    //2.从审理进程获取身份
                    if (StringUtils.isBlank(role)) {
                        role = getTrialRoundToRole(caseNoTrialRoundEnum, roleTag);
                        if (StringUtils.isNotBlank(role)){
                            source = 1;
                        }
                    }
                    e.setRole(role);
                    e.setSource(source);
                    e.setRoleTag(roleTag);
                    e.setRoleType(PartyRoleCodeEnum.findCode(role));
                    return e;
                }).collect(Collectors.toList());

        return JSON.toJSONString(partRoles, SerializerFeature.DisableCircularReferenceDetect);

    }

    /**
     * 爬虫当事人预清洗
     */
    private static String preClean(String spiderParties) {
        String newItem = CommonUtil.full2Half(spiderParties);
        newItem = newItem.replaceAll("\\(", "（");
        newItem = newItem.replaceAll("\\)", "）");
        newItem = newItem.replaceAll(Filter_part_regex, "");
        newItem = newItem.replaceAll(Filter_char, "");
        newItem = newItem.replaceAll("\t|\"|'|诉讼地位|公诉方|B&amp;|&amp;", "");

        newItem = newItem.replaceAll("  |  ", " ");

        newItem = newItem.replaceAll("::|:\\.", ":");
        newItem = newItem.replace(".,", "###FD###");

        newItem = newItem.replaceAll("/|\n", ";");
        newItem = newItem.replaceAll("【|】", "");
        newItem = replaceBlank(newItem);
        if (newItem.contains("上诉人:") && newItem.contains("被上诉人:")) {
            newItem = newItem.replace("被上诉人:", ";被上&诉人:");
            newItem = newItem.replace("上诉人:", ";上诉人:");
            newItem = newItem.replace(";被上&诉人:", ";被上诉人:");
        }

        if (newItem.contains("原告:")) {
            newItem = newItem.replace("原告:", ";原告:");
        }
        if (newItem.contains("被告:")) {
            newItem = newItem.replace("被告:", ";被告:");
        }
        if (newItem.contains("被告人:")) {
            newItem = newItem.replace("被告人:", ";被告:");
        }
        if (newItem.contains("第三人:")) {
            newItem = newItem.replace("第三人:", ";第三人:");
        }
        return newItem;
    }


    private static Map<String, String> cleanSpiderParties(String newItem) {
        Map<String, String> partRoleMap = new HashMap<>();
        //1.尝试使用分号进行切割
        cleanParty(newItem, partRoleMap, ";");
        //2.尝试使用逗号进行切割
        if (!newItem.contains(";") && newItem.contains(",")) {
            cleanParty(newItem, partRoleMap, ",");
        }
        return partRoleMap;
    }

    private static void cleanParty(String newItem, Map<String, String> partRoleMap, String s) {
        String arr[] = newItem.split(s);
        for (String data : arr) {
            data = charCheckChinese(data);
            if (Strings.isNullOrEmpty(data)) {
                continue;
            }
            if (data.contains(":") && data.split(":").length > 1) {
                String left = data.split(":")[0];
                String right = data.split(":")[1];
                partRoleMap.put(right, left);
            }
        }
    }

    /**
     * 是否含有汉字
     *
     * @param value
     * @return
     */
    public static String charCheckChinese(String value) {
        if (!LAST_CHINESE_PATTERN.matcher(value).find()) {
            return StringUtils.EMPTY;
        }
        return value;
    }

    /**
     * 判断名称是否是:测试等
     */
    private static boolean filterTestName(String name) {
        boolean result = false;
        if (name.length() <= 5 && name.contains("测试")) {
            return true;
        }

        if (name.contains("测试演练") || name.contains("测试者") || name.contains("测试0") ||
                name.contains("测试1") || name.contains("测试2") || name.contains("（测试）") || name.contains("测试3")) {
            return true;
        }
        return result;
    }


    /**
     * 根据名称获取roleTag
     */
    private static Integer getRoleTag(Map<String, Integer> partiesRoleTagMap, String name) {
        Integer roleTag = partiesRoleTagMap.get(name);
        if (roleTag == null) {
            roleTag = 3;
        }
        return roleTag;
    }


    private static String cleanSpiderPartyRole(Map<String, String> partRoleMap, CaseNoTrialRoundEnum caseNoTrialRoundEnum, String name, Integer roleTag) {
        String role = "";
        //1.从爬虫当事人获取身份
        if (!partRoleMap.isEmpty()) {
            role = getNameToRole(partRoleMap, name);
        }

        //2.从审理进程获取身份
        if (StringUtils.isBlank(role)) {
            role = getTrialRoundToRole(caseNoTrialRoundEnum, roleTag);
        }
        return role;
    }

    private static String getNameToRole(Map<String, String> partRoleMap, String name) {
        String role = "";
        for (Map.Entry<String, String> entry : partRoleMap.entrySet()) {
            if (entry.getKey().contains(name)) {
                role = entry.getValue();
                break;
            }
        }

        if (StringUtils.isNotBlank(role)) {
            role = cleanSpiderPartyRole(role);
        }

        return role;
    }

    private static String cleanSpiderPartyRole(String role) {
        if (StringUtils.isBlank(role)) {
            return role;
        }
        if (role.contains("第三人")) {
            role = role.replace("第三人", "###");
        }
        role = role.replaceAll("\\d+|  |  | ", "");
        role = role.replaceAll(Filter_char, "");
        //处理空白字符
        role = replaceBlank(role);
        if (role.length() == 1) {
            role = "";
        }
        return role.replaceAll("###", "第三人");
    }

    protected static String replaceBlank(String str) {
        String dest = null;
        if (str == null) {
            return dest;
        } else {
            Pattern p = Pattern.compile("\\s*|\t|\r|\n");
            Matcher m = p.matcher(str);
            dest = m.replaceAll("");
            return dest;
        }
    }

    /**
     * 获取roleName
     */
    private static String getTrialRoundToRole(CaseNoTrialRoundEnum caseNoTrialRoundEnum, Integer roleTag) {
        String role = "";
        if (caseNoTrialRoundEnum != null) {
            role = caseNoTrialRoundEnum.findRole(roleTag);
        }
        return role;
    }

}
