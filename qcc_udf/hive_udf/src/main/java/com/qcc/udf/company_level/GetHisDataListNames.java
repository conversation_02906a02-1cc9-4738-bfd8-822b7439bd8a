package com.qcc.udf.company_level;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.hadoop.hive.ql.exec.UDF;


public class GetHisDataListNames extends UDF {

    /**
     * 获取曾用名
     *
     * @param hisNamesStr
     * @return
     * @throws Exception
     */
    public static String evaluate(String hisNamesStr) throws Exception {
        String result = "";
        try{


        if (hisNamesStr == null || hisNamesStr.isEmpty()) return "";
        JSONArray dataList = JSONArray.parseArray(hisNamesStr);
        String hisNames = "";
        for (Object o : dataList) {
            if (o == null) continue;
            JSONObject obj = JSONObject.parseObject(o.toString());
            if (obj.getString("DataName").equals("曾用名")) {
                hisNames = obj.getString("Children");
                break;
            }
        }
        if (hisNames == null || hisNames.isEmpty()) return result;
        JSONArray hisNameArray = JSONArray.parseArray(hisNames);
        for (Object o : hisNameArray) {
            if (o == null) continue;
            JSONObject obj = JSONObject.parseObject(o.toString());
            String hisName = obj.getString("DataName");
            if (hisName != null && !hisName.isEmpty()) result += hisName + ",";
        }
        if (result.length() > 1) result = result.substring(0, result.length() - 1);
        }catch (Exception ex){
            return result;
        }
        return result;
    }
}
