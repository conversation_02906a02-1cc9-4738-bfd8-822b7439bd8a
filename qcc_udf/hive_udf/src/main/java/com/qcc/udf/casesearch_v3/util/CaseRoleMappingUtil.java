package com.qcc.udf.casesearch_v3.util;

public class CaseRoleMappingUtil {
    /**
     * 获取审理流程简称
     * @param trialRound
     * @return
     */
    public static String getShortTrialRound(String trialRound){
        if(trialRound.contains("一审")){
            return "一审";
        }
        if(trialRound.contains("二审")){
            return "二审";
        }
        if(trialRound.contains("管辖上诉")){
            return "管辖上诉";
        }
        if(trialRound.contains("申请再审审查")){
            return "申请再审审查";
        }
        if(trialRound.contains("刑事审判监督")){
            return "审判监督";
        }
        if(trialRound.contains("管辖")){
            return "管辖";
        }
        if(trialRound.contains("依职权再审审查")){
            return "依职权再审审查";
        }
        if(trialRound.contains("再审")){
            return "再审";
        }
        if(trialRound.contains("刑事复核")){
            return "复核";
        }
        if(trialRound.contains("民事特别程序监督")){
            return "特别程序监督";
        }
        if(trialRound.contains("抗诉再审审查")){
            return "抗诉再审审查";
        }
        return trialRound;
    }

    public static void main(String[] args) {
        String roles = "执行复议, 被判刑人移管, 其他刑事, 其他民事, 支付令监督, 行政赔偿一审, 其他赔偿, 行政赔偿二审, 行政赔偿管辖, 申请支付令审查, 其他司法救助, 催告, 赔偿委员会审理赔偿, 民事抗诉再审审查, 涉诉信访司法救助, 行政再审, 民事申请再审审查, 行政管辖上诉, 民事再审, 引渡, 非诉行政行为申请执行审查, 刑事司法救助, 民事司法救助, 刑事管辖, 司法赔偿监督上级法院赔偿委员会重审, 行政赔偿申请再审审查, 停止执行死刑, 行政一审, 刑事审判监督, 行为保全复议, 司法制裁审查, 行政二审, 认可与执行审查其他, 诉前调解, 民事特别程序监督, 行政赔偿依职权再审审查, 执行异议, 行政赔偿抗诉再审审查, 行为保全, 证据保全, 行政赔偿管辖上诉, 首次执行, 送达文书, 刑事一审, 行政抗诉再审审查, 行政管辖, 财产保全执行, 刑事二审, 其他行政, 司法制裁复议, 司法赔偿监督审查, 认可与执行审查复议, 其他执行, 承认与执行申请审查, 恢复执行, 行政司法救助, 调查取证, 执行协调, 申请没收违法所得, 强制清算, 民事管辖上诉, 行政依职权再审审查, 刑事复核, 刑罚与执行变更, 法院作为赔偿义务机关自赔, 财产保全, 民事一审, 行政赔偿再审, 罪赃移交, 强制医疗, 认可与执行申请审查, 司法赔偿监督本院赔偿委员会重审, 第三人撤销之诉, 执行司法救助, 民事依职权再审审查, 民事管辖, 特别程序, 破产, 行政申请再审审查, 国家赔偿司法救助, 执行监督, 民事二审, 非诉行政行为申请执行审查复议";
        for (String s : roles.split(",")) {
            String sm = getShortTrialRound(s);
            if(!sm.equals(s)){
                System.out.println(sm);
            }
        }
    }
}
