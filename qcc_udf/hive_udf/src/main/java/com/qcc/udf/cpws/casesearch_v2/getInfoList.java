package com.qcc.udf.cpws.casesearch_v2;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.court_notice.anUtils.Util;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

/**
 * @Auther: liulh
 * @Date: 2020/6/11 17:54
 * @Description:
 */
public class getInfoList extends UDF {
    public static String evaluate(String id, String info) {
        JSONArray result = new JSONArray();

        // 传入参数为空，则返回空json
        if (StringUtils.isEmpty(info)){
            return result.toString();
        }

        JSONArray infoList = JSONObject.parseObject(info).getJSONArray("InfoList");
        if (infoList != null && infoList.size() > 0){
            Iterator<Object> it = infoList.iterator();

            while (it.hasNext()){
                JSONObject jsonObject = (JSONObject)it.next();

                String party = "";
                JSONArray defendantArr = jsonObject.getJSONArray("Defendant");
                if (defendantArr != null && defendantArr.size() > 0){
                    Iterator<Object> itTmp = defendantArr.iterator();
                    while (itTmp.hasNext()){
                        JSONObject json = (JSONObject)itTmp.next();
                        if(StringUtils.isNotEmpty(json.getString("Name"))){
                            party = party.concat(",").concat(json.getString("Name"));
                        }
                        if(StringUtils.isNotEmpty(json.getString("KeyNo"))){
                            party = party.concat(",").concat(json.getString("KeyNo"));
                        }
                    }
                }
                JSONArray prosecutorArr = jsonObject.getJSONArray("Prosecutor");
                if (prosecutorArr != null && prosecutorArr.size() > 0){
                    Iterator<Object> itTmp = prosecutorArr.iterator();
                    while (itTmp.hasNext()){
                        JSONObject json = (JSONObject)itTmp.next();
                        if(StringUtils.isNotEmpty(json.getString("Name"))){
                            party = party.concat(",").concat(json.getString("Name"));
                        }
                        if(StringUtils.isNotEmpty(json.getString("KeyNo"))){
                            party = party.concat(",").concat(json.getString("KeyNo"));
                        }
                    }
                }
                party = party.length() > 0 ? party.substring(1) : party;

                JSONObject sub = new JSONObject();
                sub.put("Id", id);
                sub.put("Anno", Util.full2Half(jsonObject.getString("AnNo")));
                sub.put("Party", party);
                sub.put("Province", JSONObject.parseObject(info).getString("Province"));

                result.add(sub);
            }
        }
        return result.toString();
    }

    public static void main(String[] args) {
        System.out.println(evaluate("id001", "{\"ZxCnt\":0,\"XjpgCnt\":0,\"KtggCnt\":0,\"LastestDateType\":\"执行监督|法院公告刊登日期\",\"CfgsCnt\":0,\"LastestDate\":1584979200,\"EarliestDate\":1563206400,\"AnnoCnt\":3,\"EarliestDateType\":\"执行监督|裁定日期\",\"CompanyKeywords\":\"babff2f7855f965f543d3726c77e02da,p1fc353c6746cad3adb19c462efc3d9e,pe62971c140f25f69dc5e9945477fab4,公告,刘影,哈尔滨东大林业技术装备有限公司,杨金忠,王艳,邱金贝\",\"AnNoList\":\"(2019)黑执监121号,(2019)黑71执514号,(2019)黑71执604号,(2019)黑执监121号\",\"GqdjCnt\":0,\"GroupCourt\":\"哈尔滨铁路运输中级法院\",\"XgCnt\":1,\"FyggCnt\":1,\"ZbCnt\":1,\"LatestTrialRound\":\"执行监督\",\"CfdfCnt\":0,\"CfxyCnt\":0,\"CaseName\":\"\",\"SxCnt\":0,\"Province\":\"HLJ\",\"LianCnt\":0,\"CaseCnt\":2,\"PcczCnt\":0,\"HbcfCnt\":0,\"CaseType\":\"执行案件\",\"ProcuratorateList\":\"\",\"CaseRole\":\"[]\",\"CaseReason\":\"其它类型纠纷\",\"CourtList\":\"黑龙江省高级人民法院,哈尔滨铁路运输中级法院\",\"InfoList\":[{\"Defendant\":[{\"Role\":\"被执行人\",\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"刘影\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"哈尔滨铁路运输中级法院\",\"ZxList\":[],\"LatestTimestamp\":1575820800,\"HbcfList\":[],\"CfdfList\":[],\"TrialRound\":\"首次执行\",\"CaseList\":[{\"JudgeDate\":1574956800,\"Id\":\"8465e121694cee8cb85ba664334d60830\",\"DocType\":\"执行裁定日期\",\"IsValid\":1}],\"Prosecutor\":[{\"Role\":\"申请执行人\",\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"王艳\"}],\"ZbList\":[{\"JudgeDate\":1575820800,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"刘影\"}],\"Id\":\"12ad356289a029c771154fbaaf6a3394\",\"IsValid\":1}],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"(2019)黑71执514号\",\"XgList\":[{\"PublishDate\":1566921600,\"NameAndKeyNo\":{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"刘影\"},\"XglNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"刘影\"}],\"Id\":\"acd9801625c1ecd67fb7bd3400dc60ff\",\"CompanyInfo\":{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"\"},\"GlNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"\"}],\"IsValid\":1}],\"LianList\":[],\"CaseReason\":\"追偿权纠纷\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]},{\"Defendant\":[],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"黑龙江省高级人民法院\",\"ZxList\":[],\"LatestTimestamp\":1563206400,\"HbcfList\":[],\"CfdfList\":[],\"CaseList\":[{\"JudgeDate\":1563206400,\"Id\":\"493faef528cbe3da7a20f0d5d9d90fd70\",\"DocType\":\"执行裁定日期\",\"IsValid\":1}],\"TrialRound\":\"执行监督\",\"Prosecutor\":[],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"(2019)黑执监121号\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"其它类型纠纷\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]},{\"Defendant\":[{\"KeyNo\":\"babff2f7855f965f543d3726c77e02da\",\"Role\":\"被告\",\"Org\":0,\"Name\":\"哈尔滨东大林业技术装备有限公司\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"哈尔滨铁路运输中级法院\",\"ZxList\":[],\"LatestTimestamp\":1584979200,\"HbcfList\":[],\"CfdfList\":[],\"TrialRound\":\"执行监督\",\"CaseList\":[],\"Prosecutor\":[{\"KeyNo\":\"\",\"Role\":\"原告\",\"Org\":-1,\"Name\":\"公告\"},{\"KeyNo\":\"pe62971c140f25f69dc5e9945477fab4\",\"Role\":\"原告\",\"Org\":2,\"Name\":\"邱金贝\"},{\"KeyNo\":\"p1fc353c6746cad3adb19c462efc3d9e\",\"Role\":\"原告\",\"Org\":2,\"Name\":\"杨金忠\"}],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[{\"PublishDate\":1584979200,\"Category\":\"执行文书\",\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-1,\"Name\":\"公告\"},{\"KeyNo\":\"pe62971c140f25f69dc5e9945477fab4\",\"Org\":2,\"Name\":\"邱金贝\"},{\"KeyNo\":\"p1fc353c6746cad3adb19c462efc3d9e\",\"Org\":2,\"Name\":\"杨金忠\"},{\"KeyNo\":\"babff2f7855f965f543d3726c77e02da\",\"Org\":0,\"Name\":\"哈尔滨东大林业技术装备有限公司\"}],\"Id\":\"6002601963\",\"IsValid\":1}],\"XjpgList\":[],\"AnNo\":\"(2019)黑71执604号,(2019)黑执监121号\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]}],\"SdggCnt\":0}"));
    }

}
