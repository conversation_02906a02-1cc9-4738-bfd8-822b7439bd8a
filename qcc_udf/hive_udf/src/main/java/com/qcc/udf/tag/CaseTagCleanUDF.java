package com.qcc.udf.tag;

import com.alibaba.fastjson.JSON;
import com.qcc.udf.tag.dimtype.DimensionEnum;
import com.qcc.udf.tag.dimtype.LDDimtypeKeywordEnum;
import com.qcc.udf.tag.dimtype.SWDimtypeKeywordEnum;
import com.qcc.udf.tag.dimtype.ZLDimtypeKeywordEnum;
import com.qcc.udf.tag.keywordEnum.SWKeywordMatchEnum;
import com.qcc.udf.tag.tagEnum.TaxRiskEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Auther: zhanqgiang
 * @Date: 2020/11/19 10:00
 * @Description: 案号简单清洗
 */
public class CaseTagCleanUDF extends UDF {
    public static final String NULL_ARRAY_STRING = "[]";
    public static final String CASE_RESON_REGEX = "^(A030601|A030603|A030602|A030604|A0306|A0917|A030609|A0916|A030605|A030613|A0918|A030608|A030611|A030610|A030606)$";
    public static final Pattern CASE_RESON_REGEX_PATTERN = Pattern.compile(CASE_RESON_REGEX);

    public static String evaluate(String caseReson) {
        if (StringUtils.isBlank(caseReson)){
            return NULL_ARRAY_STRING;
        }

        TagEntity tagEntity = null;
        Matcher matcher = CASE_RESON_REGEX_PATTERN.matcher(caseReson);
        if (matcher.find()){
            tagEntity = TaxRiskEnum.SW10.getTagEntity();
        }

        if (tagEntity == null){
            return NULL_ARRAY_STRING;
        }

        return JSON.toJSONString(tagEntity);
    }


    public static void main(String[] args) {
        System.out.println(evaluate("A030615"));
    }

}
