package com.qcc.udf.court_notice.anCleanMethods;

import com.qcc.udf.court_notice.anUtils.Util;

import java.util.HashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CleanCourtPartyFromEs {
    private static String getDefendantAndProsecutor(String party) {
        /**
         * 先把当事人提取出来，带原告被告前缀名称
         */

        if (Util.isEmpty(party)) {
            return null;
        }
        party = party.replaceAll("抗诉", "");
        //原告前缀全部置空
        String pro = "(上诉人|申诉人|原告：|原告人|申请人|申请再审人)";//这里替换掉例如申述人，下面的被申述人就变成了被
        String pro_1 = "原告";
        //被告前缀
        String def = "(被告：|被告人|被申请人|被申请再审人)";
        String def_1 = "被告|被";
        party = party.replaceAll(pro, "*").replaceAll(pro_1, "*");
        party = party.replaceAll(def, "~").replaceAll(def_1, "~").replace("~*", "~").replaceAll("(与|、)~", "诉~");//上诉人武汉市中联房地产开发有限公司与被上诉人武汉海龙实业集团有限公司、被上诉人武汉海润房地产开发有限公司这种带两个被上述人的也可以处理
        return party;
    }

    //得到被告
    public static String getDefendant(String party) {
        party = getDefendantAndProsecutor(party);
        if (Util.isEmpty(party)) {
            return null;
        }
        //先用符号分隔被告、原告
        if (party.contains("诉")) {
            if (!party.contains("*")) {
                party = "*" + party;
            }
            if (!party.contains("~")) {
                party = party.replace("诉", "诉~");
            }
        }
        if (party.contains("诉")) {
            party = party.replace("诉", "&");
        } else if (party.contains("与")) {
            party = party.replace("与", "&");
        }
        if (party.contains("&")) {//被告、原告都有
            Set<String> list = new HashSet<String>();
            for (String str : party.split("&")) {
                if (str.contains("~")) {//被告表标识
                    str = str.substring(str.indexOf("~"), str.length());
                    str = str.replaceAll("(~|等| )", "").replaceAll("和|及|与", ",");
                    for (String singleName : str.split(",|、|，")) {
                        //单独处理公司名称不规范的
                        if (singleName.contains("*")) {//含有被告
                            break;
                        }
                        //单独处理公司名称不规范的
                        if (singleName.contains("公司") && singleName.endsWith("分行")) {
                            singleName = singleName.substring(0, singleName.indexOf("分行") + 2);
                        }
                        if (singleName.contains("公司") && singleName.endsWith("支行")) {
                            singleName = singleName.substring(0, singleName.indexOf("支行") + 2);
                        }
                        if (singleName.contains("公司") && !singleName.endsWith("公司") && !singleName.endsWith("分行") && !singleName.endsWith("支行")) {
                            singleName = singleName.substring(0, singleName.indexOf("公司") + 2);
                        }
                        if(singleName.contains("flag_")&&singleName.contains("_flag")){
                            Matcher m=Pattern.compile("(flag_[0-9]_flag)").matcher(singleName);
                            if(m.find()) {
                                singleName = m.group(1);
                            }
                        }
                        list.add(singleName);
                    }
                }
            }
            party = list.toString().replaceAll("\\[|\\]| ", "");
            if (Util.isEmpty(party)) {
                return null;
            }
            return party;
        } else {//被告、原告只有一个的情况
            //含有被告
            if (party.contains("~")) {
                Set<String> list = new HashSet<String>();
                party = party.substring(party.indexOf("~"), party.length());
                party = party.replaceAll("(~|等| )", "").replaceAll("和|及|与", ",");
                for (String singleName : party.split(",|、|，")) {
                    //防止混入原告数据
                    if (singleName.contains("*")) {
                        break;
                    }
                    //单独处理公司名称不规范的
                    if (singleName.contains("公司") && singleName.endsWith("分行")) {
                        singleName = singleName.substring(0, singleName.indexOf("分行") + 2);
                    }
                    if (singleName.contains("公司") && singleName.endsWith("支行")) {
                        singleName = singleName.substring(0, singleName.indexOf("支行") + 2);
                    }
                    if (singleName.contains("公司") && !singleName.endsWith("公司") && !singleName.endsWith("分行") && !singleName.endsWith("支行")) {
                        singleName = singleName.substring(0, singleName.indexOf("公司") + 2);
                    }
                    if(singleName.contains("flag_")&&singleName.contains("_flag")){
                        Matcher m=Pattern.compile("(flag_[0-9]_flag)").matcher(singleName);
                        if(m.find()) {
                            singleName = m.group(1);
                        }
                    }
                    list.add(singleName);
                }
                party = list.toString().replaceAll("\\[|\\]| ", "");
                if (Util.isEmpty(party)) {
                    return null;
                }
                return party;
            } else {
                //只有原告，因为这里只返回被告，所以原告不考虑
                return null;
            }
        }
    }

    //得到原告
    public static String getProsecutor(String party) {
        party = getDefendantAndProsecutor(party);

        if (Util.isEmpty(party)) {
            return null;
        }
        //先用符号分隔被告、原告
        //王七顺诉王彩涛的情况，包含诉，却没有被告原告
        if (party.contains("诉")) {
            if (!party.contains("*")) {
                party = "*" + party;
            }
            if (!party.contains("~")) {
                party = party.replace("诉", "诉~");
            }
        }
        if (party.contains("诉")) {
            party = party.replace("诉", "&");
        } else if (party.contains("与")) {
            party = party.replace("与", "&");
        }
        if (party.contains("&")) {//被告、原告都有
            for (String str : party.split("&")) {
                if (str.contains("*")) {//被告表标识
                    str = str.substring(str.indexOf("*"), str.length());
                    str = str.replaceAll("(\\*|等| )", "").replaceAll("和|及|与", ",");
                    Set<String> list = new HashSet<String>();
                    for (String singleName : str.split(",|、|，")) {
                        //单独处理公司名称不规范的
                        if (singleName.contains("~")) {//防止混入被告
                            break;
                        }
                        //单独处理公司名称不规范的
                        if (singleName.contains("公司") && singleName.endsWith("分行")) {
                            singleName = singleName.substring(0, singleName.indexOf("分行") + 2);
                        }
                        if (singleName.contains("公司") && singleName.endsWith("支行")) {
                            singleName = singleName.substring(0, singleName.indexOf("支行") + 2);
                        }
                        if (singleName.contains("公司") && !singleName.endsWith("公司") && !singleName.endsWith("分行") && !singleName.endsWith("支行")) {
                            singleName = singleName.substring(0, singleName.indexOf("公司") + 2);
                        }
                        if(singleName.contains("flag_")&&singleName.contains("_flag")){
                            Matcher m=Pattern.compile("(flag_[0-9]_flag)").matcher(singleName);
                            if(m.find()) {
                                singleName = m.group(1);
                            }
                        }
                        list.add(singleName);
                    }
                    str = list.toString().replaceAll("\\[|\\]| ", "");
                    return str;
                }
            }
            return null;
        } else {//被告、原告只有一个的情况
            //含有原告
            if (party.contains("*")) {
                Set<String> list = new HashSet<String>();
                party = party.substring(party.indexOf("*"), party.length());
                party = party.replaceAll("(\\*|等| )", "").replaceAll("和|及|与", ",");
                for (String singleName : party.split(",|、|，")) {
                    if (singleName.contains("~")) {//防止混入被告
                        break;
                    }
                    //单独处理公司名称不规范的
                    if (singleName.contains("公司") && singleName.endsWith("分行")) {
                        singleName = singleName.substring(0, singleName.indexOf("分行") + 2);
                    }
                    if (singleName.contains("公司") && singleName.endsWith("支行")) {
                        singleName = singleName.substring(0, singleName.indexOf("支行") + 2);
                    }
                    if (singleName.contains("公司") && !singleName.endsWith("公司") && !singleName.endsWith("分行") && !singleName.endsWith("支行")) {
                        singleName = singleName.substring(0, singleName.indexOf("公司") + 2);
                    }
                    if(singleName.contains("flag_")&&singleName.contains("_flag")){
                        Matcher m=Pattern.compile("(flag_[0-9]_flag)").matcher(singleName);
                        if(m.find()) {
                            singleName = m.group(1);
                        }
                    }
                    list.add(singleName);
                }
                party = list.toString().replaceAll("\\[|\\]| ", "");
                return party;
            } else {
                //只有原告，因为这里只返回被告，所以原告不考虑
                return null;
            }
        }

    }
}
