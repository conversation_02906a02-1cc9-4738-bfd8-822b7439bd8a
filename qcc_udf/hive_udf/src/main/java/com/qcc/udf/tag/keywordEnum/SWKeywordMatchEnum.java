package com.qcc.udf.tag.keywordEnum;

import com.qcc.udf.tag.tagEnum.TaxRiskEnum;

/**
 * 税务风险-关键词
 */
public enum SWKeywordMatchEnum {
    //SW01
    XKFP(".*\"value\":\"虚开发票\".*", TaxRiskEnum.SW01),
    XK_FP(".*虚开.*发票.*", TaxRiskEnum.SW01),

    //SW02
    QS(".*欠税.*", TaxRiskEnum.SW02),

    //SW04
    TS_TS_KS(".*(逃避|偷税|逃税|走逃|失联).*", TaxRiskEnum.SW04),

    //SW05
    FF_QD_JXFP(".*非法.*取得.*进项发票.*", TaxRiskEnum.SW05),

    //SW06
    WAGD(".*未按.*规定.*", TaxRiskEnum.SW06),
    WFGD(".*违反.*规定.*", TaxRiskEnum.SW06),

    //SW07
    YQ_WAQ(".*(逾期|未按期).*", TaxRiskEnum.SW07),

    //SW08
    SJ(".*少缴.*", TaxRiskEnum.SW08),
    BJ(".*补缴.*", TaxRiskEnum.SW08),
    LJ(".*漏缴.*", TaxRiskEnum.SW08),

    //SW09
    SW09_YQ(".*延期.*", TaxRiskEnum.SW09),

    //SW10
    SSXSAJ("^(A030601|A030603|A030602|A030604|A0306|A0917|A030609|A0916|A030605|A030613|A0918|A030608|A030611|A030610|A030606)$", TaxRiskEnum.SW10),

    //ZL99
    DEFAULT("其他", TaxRiskEnum.SW99),
    ;


    private String keyword;
    private TaxRiskEnum taxRiskEnum;

    SWKeywordMatchEnum(String keyword, TaxRiskEnum taxRiskEnum) {
        this.keyword = keyword;
        this.taxRiskEnum = taxRiskEnum;
    }

    public String getKeyword() {
        return keyword;
    }

    public TaxRiskEnum getTaxRiskEnum() {
        return taxRiskEnum;
    }

    public TaxRiskEnum getTagEnum() {
        return getTaxRiskEnum();
    }
}
