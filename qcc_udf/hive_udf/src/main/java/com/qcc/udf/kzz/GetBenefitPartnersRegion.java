package com.qcc.udf.kzz;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * 获取最终受益人地区
 * <AUTHOR>
 * @date 2021/9/17
 */
public class GetBenefitPartnersRegion extends UDF{
//
//    public static void main(String[] args){
//        String content ="张昇";
////       String region =  evaluate(content);
//       System.out.println(region);
//    }

    public static String evaluate(String stockJson){
        String region="";
        try{
            if(StringUtils.isNotBlank(stockJson)){
                JSONObject stockObj = JSONObject.parseObject(stockJson);
                if(null!=stockObj && stockObj.containsKey("Paths")){
                    JSONArray array = stockObj.getJSONArray("Paths");
                    if(!array.isEmpty()){
                        for (Object arr : array) {
                            JSONArray subArray = (JSONArray)arr;
                            if(!subArray.isEmpty()){
                                for (Object subObj : subArray) {
                                    JSONObject partner = (JSONObject)subObj;
                                    if(partner.containsKey("Tags")){
                                        JSONArray tagArray = partner.getJSONArray("Tags");
                                        for (Object tagObj : tagArray) {
                                            JSONObject tag= ( JSONObject)tagObj;
                                            int type = tag.getIntValue("Type");
                                            String regionName = tag.getString("Name");
                                            if(402==type){
                                                region=regionName;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }catch (Exception e){

        }
        return region;
    }
}
