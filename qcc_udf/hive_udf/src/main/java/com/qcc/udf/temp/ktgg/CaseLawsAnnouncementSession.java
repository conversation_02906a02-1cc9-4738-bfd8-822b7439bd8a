package com.qcc.udf.temp.ktgg;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * 开庭公告基础表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CaseLawsAnnouncementSession {
    /**
     * 主键
     */
    @JSONField(name = "id")
    private String id;

    /**
     * 数据状态
     */
    @JSONField(name = "data_status")
    private Integer dataStatus;

    /**
     * 新增时间
     */
    @JSONField(name = "create_date")
    private Date createDate;

    /**
     * 更新时间
     */
    @JSONField(name = "update_date")
    private Date updateDate;

    /**
     * 唯一id
     */
    @JSONField(name = "unique_id")
    private String uniqueId;

    /**
     * ai返回结果
     */
    @JSONField(name = "ai_result")
    private String aiResult;

    /**
     * 数据源链接
     */
    @JSONField(name = "source_url")
    private String sourceUrl;

    /**
     * 案号
     */
    @JSONField(name = "case_no")
    private String caseNo;

    /**
     * 发布日期
     */
    @JSONField(name = "publish_date")
    private String publishDate;

    /**
     * 开庭时间
     */
    @JSONField(name = "session_time")
    private Date sessionTime;

    /**
     * 开庭日期
     */
    @JSONField(name = "session_date")
    private String sessionDate;

    /**
     * 排期时间
     */
    @JSONField(name = "schedule_date")
    private String scheduleDate;

    /**
     * 年份
     */
    @JSONField(name = "case_year")
    private Integer caseYear;

    /**
     * 案由
     */
    @JSONField(name = "casereason")
    private String casereason;

    /**
     * 案由code
     */
    @JSONField(name = "related_casereason")
    private String relatedCasereason;

    /**
     * 法院
     */
    @JSONField(name = "court")
    private String court;

    /**
     * 法院code
     */
    @JSONField(name = "related_court")
    private String relatedCourt;

    /**
     * 省份
     */
    @JSONField(name = "court_province")
    private String courtProvince;

    /**
     * 法院介绍
     */
    @JSONField(name = "court_introduce")
    private String courtIntroduce;

    /**
     * 法庭
     */
    @JSONField(name = "court_room")
    private String courtRoom;

    /**
     * 承办部门
     */
    @JSONField(name = "undertake_dept")
    private String undertakeDept;

    /**
     * 承办法官
     */
    @JSONField(name = "pers_judge_undertake")
    private String persJudgeUndertake;

    /**
     * 审判长
     */
    @JSONField(name = "pers_judge_preside")
    private String persJudgePreside;

    /**
     * 所有审判人员
     */
    @JSONField(name = "pers_judges")
    private String persJudges;

    /**
     * 审判员
     */
    @JSONField(name = "pers_judge")
    private String persJudge;

    /**
     * 陪审员
     */
    @JSONField(name = "pers_juror")
    private String persJuror;

    /**
     * 书记员
     */
    @JSONField(name = "pers_clerk")
    private String persClerk;

    /**
     * 原告方kn数组
     */
    @JSONField(name = "pltf_keyno_array")
    private String pltfKeynoArray;

    /**
     * 被告方kn数组
     */
    @JSONField(name = "defd_keyno_array")
    private String defdKeynoArray;

    /**
     * 第三人kn数组
     */
    @JSONField(name = "tdpt_keyno_array")
    private String tdptKeynoArray;

    /**
     * 当事人kn数组
     */
    @JSONField(name = "keyno_array")
    private String keynoArray;

    /**
     * 公告内容
     */
    @JSONField(name = "content")
    private String content;

    /**
     * 完整公告内容
     */
    @JSONField(name = "full_content")
    private String fullContent;

    /**
     * 庭审视频url
     */
    @JSONField(name = "video_url")
    private String videoUrl;

    /**
     * 有无视频标识
     */
    @JSONField(name = "video_flag")
    private Integer videoFlag;

    /**
     * 溯源字段
     */
    @JSONField(name = "spider_id")
    private String spiderId;

    /**
     * ai调用是否成功
     */
    @JSONField(name = "if_ai")
    private Integer ifAi;

    /**
     * 数据来源
     */
    @JSONField(name = "source_name")
    private String sourceName;

    /**
     * 新流程迁移前的id
     */
    @JSONField(name = "old_id")
    private String oldId;
    /**
     * 未知身份集合
     */
    @JSONField(name = "other_keyno_array")
    private String otherKeynoArray;

    /**
     * 标准化案号
     */
    @JSONField(name = "case_no_standard")
    private String caseNoStandard;

    /**
     * 当事人身份kn数组
     */
    @JSONField(name = "party_keyno_array")
    private String partyKeynoArray;

    /**
     * 和公司关联的keywords
     */
    @JSONField(name = "comp_rel_keywords")
    private String compRelKeywords;

    private int count;

    private static final long serialVersionUID = 1L;
}