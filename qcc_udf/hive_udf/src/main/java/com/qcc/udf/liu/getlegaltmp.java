package com.qcc.udf.liu;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.cpws.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.Set;

public class getlegaltmp extends UDF {

    public static String evaluate(String info)  throws Exception{
        JSONArray array = new JSONArray();

        if (StringUtils.isNotEmpty(info)){
            try {
                JSONArray array1 = JSONArray.parseArray(info);
                Iterator<Object> it = array1.iterator();
                while (it.hasNext()){
                    JSONObject json = (JSONObject)it.next();
                    String law = json.getString("法规名称");
                    JSONArray items = json.getJSONArray("Items");
                    if (items != null){
                        Iterator<Object> it1 = items.iterator();
                        while (it1.hasNext()){
                            JSONObject artJson = (JSONObject)it1.next();
                            String art = artJson.getString("法条名称");

                            JSONObject object = new JSONObject();
                            object.put("Law", law);
                            object.put("Article", art);

                            array.add(object);
                        }
                    }
                }
            }catch (Exception e){

            }
        }

        return array.toString();
    }

    public static void main(String[] args) {
        try {
            evaluate("[{\"法规名称\":\"中华人民共和国民事诉讼法\",\"Items\":[{\"法条内容\":\"第一百条系统尚未收录或引用有误&amp;#xA;\",\"法条名称\":\"第一百条\"},{\"法条内容\":\"第一百零二条系统尚未收录或引用有误&amp;#xA;\",\"法条名称\":\"第一百零二条\"},{\"法条内容\":\"第一百零三条系统尚未收录或引用有误&amp;#xA;\",\"法条名称\":\"第一百零三条\"},{\"法条内容\":\"第一百零五条系统尚未收录或引用有误&amp;#xA;\",\"法条名称\":\"第一百零五条\"},{\"法条内容\":\"第一百零八条系统尚未收录或引用有误&amp;#xA;\",\"法条名称\":\"第一百零八条\"}]}]");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
