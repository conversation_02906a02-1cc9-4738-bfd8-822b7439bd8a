package com.qcc.udf.kzz;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 提取招投标项目编号,去除结尾带-的括号内的内容
 * <AUTHOR>
 * @date 2022/4/13
 */
public class GetTenderProjectNo extends UDF {
    private static final Pattern PATTERN =Pattern.compile("(?<=(（|\\())(.*?)(?=(\\)|）))");
    public static String evaluate(String content) {
        String result=content;
        try{
            if(StringUtils.isNotBlank(content)){
                Matcher matcher = PATTERN.matcher(content);
                String str="";
                while(matcher.find()){
                    str = matcher.group(2);
                }
                if(str.contains("-")){
                    result = result.replaceAll(str,"").replace("()","").replace("（）","");
                }
            }
        }catch (Exception e){

        }
        return result;
    }
//    public static void main(String[] args){
////        String content = "YLZC2018-G2-60931-GXYQ(YQG20183007-BB)";
//        String content = "9-12)(7-81)(7-53)";
//        String tels =  evaluate(content);
//        System.out.println(tels);
//    }
}
