package com.qcc.udf.competition;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2021/07/21 21:55
 * @description ：
 */
public class GetSameCertificationIds extends UDF {

    public String evaluate(String originIdsItem, String originIdsOther) {

        String result = "";
        if (StringUtils.isEmpty(originIdsItem) || StringUtils.isEmpty(originIdsOther)) {
            return result;
        }

        String[] itemStr = originIdsItem.split(",");
        Map<String, String> itemMap = new HashMap<>();
        for(String item : itemStr) {
            if (StringUtils.isEmpty(item)) {
                continue;
            }
            String[] splitOther = item.split("=");
            if (splitOther.length > 1) {
                itemMap.put(splitOther[0], splitOther[1]);
            }
        }

        String[] otherStr = originIdsOther.split(",");
        List<String> otherList = Arrays.asList(otherStr);

        Set<String> ids = new HashSet<>();
        for(String other: otherList) {
            if (StringUtils.isEmpty(other)) {
                continue;
            }
            String[] splitItem = other.split("=");
            if (splitItem.length > 1 && itemMap.containsKey(splitItem[0])) {
                ids.add(splitItem[1]);
            }
        }
        if (CollectionUtils.isNotEmpty(ids)) {
            result = ids.stream().collect(Collectors.joining(","));
        }
        return result;
    }

    public static void main(String[] args) {
        GetSameCertificationIds getSameCertificationKeys = new GetSameCertificationIds();
        String one = "1=666,2=777,3=888,4=999";
        String two = "8=111,2=222,4=333,7=444";
        System.out.println(getSameCertificationKeys.evaluate(one, two));
    }

}
