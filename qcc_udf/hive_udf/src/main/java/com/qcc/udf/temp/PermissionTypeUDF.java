package com.qcc.udf.temp;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

/**
 *  根据案号，清洗出detailnfo信息
 * @Auther: wuql
 * @Date: 2020/11/17 10:00
 * @Description:
 */
public class PermissionTypeUDF extends UDF {

    public int evaluate(String typeName)  {
        if (StringUtils.isBlank(typeName)){
            return  PermissionTypeEnum.TYPE_0.gettype();
        }

        PermissionTypeEnum permissionTypeEnum = PermissionTypeEnum.find(typeName);
        if (permissionTypeEnum==null){
            return PermissionTypeEnum.TYPE_UNKNOWN.gettype();
        }

        return permissionTypeEnum.gettype();
    }

    public static void main(String[] args) {
        String typeName ="普通";
        PermissionTypeUDF dateHandlerUDF = new PermissionTypeUDF();
        int evaluate = dateHandlerUDF.evaluate(typeName);
        System.out.println(evaluate);
    }
}
