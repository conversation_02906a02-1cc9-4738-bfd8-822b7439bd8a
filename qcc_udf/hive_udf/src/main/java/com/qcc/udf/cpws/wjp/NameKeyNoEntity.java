package com.qcc.udf.cpws.wjp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2021/8/30
 */
@Data
@Builder
@AllArgsConstructor
public class NameKeyNoEntity {
    @JSONField(
            name = "Name"
    )
    private String Name;
    @JSONField(name = "ShowName")
    private String ShowName;
    @JSONField(
            name = "KeyNo"
    )
    private String KeyNo;
    @JSONField(
            name = "Org"
    )
    private Integer Org;
    @J<PERSON><PERSON>ield(
            name = "T"
    )
    private String T;

    @JSONField(
            name = "J"
    )
    private String J;

    @JSONField(name = "Role")
    private String Role;
    @JSONField(name="Source")
    private Integer Source;
    @JSONField(name = "RoleTag")
    private Integer RoleTag;
    @JSONField(name = "RoleType")
    private Integer RoleType;
    @JSONField(name = "RoleTypeDesc")
    private String RoleTypeDesc;

    @JSONField(name = "LawFirmList")
    private List<CPWSLawyerFirmGroupInfo> LawFirmList;

    @JSONField(name = "SupNameAndKeyNo")
    private SupNameAndKeyNo SupNameAndKeyNo;
}
