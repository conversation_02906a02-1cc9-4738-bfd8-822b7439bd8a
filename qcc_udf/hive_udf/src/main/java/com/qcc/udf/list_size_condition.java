package com.qcc.udf;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.TypeAdapter;
import com.google.gson.internal.LinkedTreeMap;
import com.google.gson.reflect.TypeToken;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class list_size_condition extends UDF{
	public String evaluate(String s,String c){
		ArrayList<Map<String, Object>> js2 = new ArrayList<Map<String, Object>>();
		GsonBuilder builder = new GsonBuilder();  
		builder.registerTypeAdapter(new TypeToken<ArrayList<Map<String, Object>>>() {}.getType(), new MapTypeAdapter());
		Gson gson = builder.create();
		try{
			js2 = gson.fromJson(s, new TypeToken<ArrayList<Map<String, Object>>>() {}.getType());
			int allnum = js2.size();
			int num = 0;
			for(int i=0;i<allnum;i++){
				Map<String,Object> tmp = js2.get(i);
				if(tmp.get(c)!=null && tmp.get(c).toString().length()>4){
					num++;
				}
			}
			return (allnum-num)+"";
		}catch(Exception e){
			return 0+"";
		}
	}
	
	public static class MapTypeAdapter extends TypeAdapter<Object> {

		@Override
		public Object read(JsonReader in) throws IOException {
			JsonToken token = in.peek();
			switch (token) {
			case BEGIN_ARRAY:
				List<Object> list = new ArrayList<Object>();
				in.beginArray();
				while (in.hasNext()) {
					list.add(read(in));
				}
				in.endArray();
				return list;

			case BEGIN_OBJECT:
				Map<String, Object> map = new LinkedTreeMap<String, Object>();
				in.beginObject();
				while (in.hasNext()) {
					map.put(in.nextName(), read(in));
				}
				in.endObject();
				return map;

			case STRING:
				return in.nextString();

			case NUMBER:

				double dbNum = in.nextDouble();

				if (dbNum > Long.MAX_VALUE) {
					return dbNum;
				}

				long lngNum = (long) dbNum;
				if (dbNum == lngNum) {
					return lngNum;
				} else {
					return dbNum;
				}

			case BOOLEAN:
				return in.nextBoolean();

			case NULL:
				in.nextNull();
				return null;

			default:
				throw new IllegalStateException();
			}
		}
		@Override
		public void write(JsonWriter out, Object value) throws IOException {
		}
	}	
	

}