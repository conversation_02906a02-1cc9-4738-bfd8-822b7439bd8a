package com.qcc.udf.overseas;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashSet;
import java.util.Set;

/**
 * 业务UDF（海外企业）替换无意义的字符串信息为空字符串
 * ---------------------------------------------------------------------------------------------------------
 * add jar hdfs://ldh/data/hive/udf/qcc_udf.jar;
 * create temporary function FilterInValidMsg as 'com.qcc.udf.overseas.FilterInValidMsgUDF';
 * ---------------------------------------------------------------------------------------------------------
 * select FilterInValidMsg ('UNKNOWN, NA 00000');
 * 结果: ''
 */
public class FilterInValidMsgUDF extends UDF {
    private final static Set<String> filterWordList;

    static {
        filterWordList = new HashSet<>();
        try {
            InputStream is = FilterInValidMsgUDF.class.getResourceAsStream("/invalid_msg_list.txt");
            BufferedReader br = new BufferedReader(new InputStreamReader(is));
            String line;
            while ((line = br.readLine()) != null) {
                if (!line.startsWith("#")) {
                    filterWordList.add(line.trim());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String evaluate(String input) {
        return filterWordList.contains(input.trim()) ? "" : input.trim();
    }
}
