package com.qcc.udf.cpws;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class BeforeCaseNoDiffUDF extends UDF {
    public static   Pattern regex = Pattern.compile("另案|合并执行|还有其他债务");

    public static String evaluate(String input,String beforeCaseno)    {
        List<String> diffCaseno=new ArrayList<>();
        if(StringUtils.isNotEmpty(input)&&StringUtils.isNotEmpty(beforeCaseno)){
            String[] beforeList = beforeCaseno.split(",");
            String[] list = input.split("[,，。？\\?.]");

            for (String caseno : beforeList) {
            for (String s : list) {
                Matcher matcher = regex.matcher(s);
                if(s.contains(caseno)&&matcher.find()){
                    diffCaseno.add(caseno);
                    caseno="";
                }
            }
            if(StringUtils.isNotEmpty(caseno)){
                int i1=input.indexOf("另案");
                int i2=input.indexOf("合并执行");
                int i3=input.indexOf("还有其他债务");
                int ii= input.indexOf(caseno);
              if(regex.matcher(input).find()&&ii!=-1&&(i1<ii||i2<ii||i3<ii)){
                  diffCaseno.add(caseno);
              }
            }
            }
        }
        return String.join(",",diffCaseno);

    }

    public static void main(String[] args) {
        String a="本院另案（2022）浙1082执4120号已扣押进行处置，本案参与分配。截至2022年10月13日，本案执行标的已执行到位0元。暂未发现被执行人有其他可供执行的财产。还有（2022）苏A001号并案审理。";
        String b=" （2022）苏123456号";
        System.out.println(evaluate(a,b));
    }
}
