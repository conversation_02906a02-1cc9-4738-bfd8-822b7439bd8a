package com.qcc.udf.cpws;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class CpwsAmountUDFV5 extends UDF {

    public static final Pattern ADDRESS_PATTERN =
            Pattern.compile("((?:住所地|户籍地|现住)[^0-9]+)");
    public static final Pattern BIRTHDAY_PATTERN =
            Pattern.compile("((19|20)[0-9]{2}[年|\\-|/]((0[1-9])|(1[0-2]))[月|\\-|/](0[1-9]|[1-2][0-9]|3[0-1])[日]?)");
    public static final Pattern ID_CARD_PATTERN =
            Pattern.compile("([1-9]\\d{5}(?:18|19|20)\\d{2}(?:0[1-9]|1[0-2])(?:0[1-9]|[1-2][0-9]|3[0-1])\\d{3}[\\dxX])");

    public  static String evaluate(String textString) {
        Matcher addressMatcher = ADDRESS_PATTERN.matcher(textString);
        Matcher birthdayMatcher = BIRTHDAY_PATTERN.matcher(textString);
        Matcher idCardMatcher = ID_CARD_PATTERN.matcher(textString);

        String address = "";
        String birthday = "";
        String idCard = "";

        if (addressMatcher.find()) {
            address = addressMatcher.group(0);
        }

//        if (birthdayMatcher.find()) {
            birthday = shieldBirthInfoV3(textString);
//        }

        if (idCardMatcher.find()) {
            idCard = idCardMatcher.group(0);
        }

        if (StringUtils.isNotBlank(address) || StringUtils.isNotBlank(birthday) || StringUtils.isNotBlank(idCard)) {
            return "true";
        }
        return "false";
    }
    private final static String REGEXP_BIRTH_ALL_DAY = "(\\d{2,4}年|[零一二三四五六七八九十\\d]{4}年)(\\d{1,2}月|[一二三四五六七八九十]{1,2}月)(\\d{1,2}|[零一二三四五六七八九十\\d]{1,3})";
    private final static String REGEXP_BIRTH_WITHOUT_DAY = "(\\d{2,4}年|[零一二三四五六七八九十\\d]{4}年)(\\d{1,2}月|[一二三四五六七八九十]{1,2}月)";

    private static String shieldBirthInfoV3(String segment) {
        String day = "";

        if (Pattern.compile(REGEXP_BIRTH_ALL_DAY).matcher(segment).find()) {
            Matcher m1 = Pattern.compile("生于" + REGEXP_BIRTH_ALL_DAY).matcher(segment);
            Matcher m2 = Pattern.compile(REGEXP_BIRTH_ALL_DAY + "日出生").matcher(segment);
            Matcher m3 = Pattern.compile(REGEXP_BIRTH_ALL_DAY + "号出生").matcher(segment);
            Matcher m4 = Pattern.compile(REGEXP_BIRTH_ALL_DAY + "日生").matcher(segment);
            Matcher m5 = Pattern.compile(REGEXP_BIRTH_ALL_DAY + "号生").matcher(segment);
            Matcher m6 = Pattern.compile("出生日期" + REGEXP_BIRTH_ALL_DAY).matcher(segment);
            if (m1.find()) {
                day = (m1.group());
            } else if (m2.find()) {
                day = (m2.group());
            } else if (m3.find()) {
                day = (m3.group());
            } else if (m4.find()) {
                day = (m4.group());
            } else if (m5.find()) {
                day = (m5.group());
            } else if (m6.find()) {
                day = (segment);
            }
        } else if (Pattern.compile(REGEXP_BIRTH_WITHOUT_DAY).matcher(segment).find()) {
            Matcher m1 = Pattern.compile("生于" + REGEXP_BIRTH_WITHOUT_DAY).matcher(segment);
            Matcher m2 = Pattern.compile(REGEXP_BIRTH_WITHOUT_DAY + "出生").matcher(segment);
            Matcher m3 = Pattern.compile(REGEXP_BIRTH_WITHOUT_DAY + "出于").matcher(segment);
            Matcher m4 = Pattern.compile(REGEXP_BIRTH_WITHOUT_DAY + "生").matcher(segment);
            Matcher m5 = Pattern.compile("出生日期" + REGEXP_BIRTH_WITHOUT_DAY).matcher(segment);

            if (m1.find()) {
                day = (m1.group());
            } else if (m2.find()) {
                day = (m2.group());
            } else if (m3.find()) {
                day = (m3.group());
            } else if (m4.find()) {
                day = (m4.group());
            } else if (m5.find()) {
                day = (m5.group());
            }
        }


        return day;
    }

    // The entry point of the UDF
    public static void main(String[] args) {
        String textString = "{\"Input\":\"所有当事人:原告:杭州轰隆隆网络科技有限公司住所地。\",\"Result\":{\"Caseno\":\"\",\"Casereason\":\"\",\"Court\":\"\",\"CourtRoom\":\"\",\"Defendant\":\"\",\"IfAi\":1,\"Plaintiff\":\"杭州轰隆隆网络科技有限公司\",\"Preside\":\"\",\"PubltishDate\":\"\",\"ScheduleDate\":\"\",\"SessionTime\":\"\"}}";
        String result = evaluate(textString);
        System.out.println(result);
    }
}
