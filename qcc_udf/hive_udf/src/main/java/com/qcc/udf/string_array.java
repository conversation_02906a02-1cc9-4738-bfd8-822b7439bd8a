package com.qcc.udf;

import com.google.gson.*;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;

public class string_array extends UDF{
	public ArrayList<String> evaluate(String str){
		ArrayList<String> result = new ArrayList<String>();
		if(str!=null){
			Gson gson = new GsonBuilder().create();
			JsonArray resulttmp = gson.fromJson(str, JsonArray.class);
			for(JsonElement value:resulttmp){
				if (value != null && !(value instanceof JsonNull)) {
					result.add(value.toString());
				}
			}
			return result;
		}else{
			return result;
		}
	}
	
	public ArrayList<String> evaluate(String str,String key){
		ArrayList<String> result = new ArrayList<String>();
		if(str!=null){
			Gson gson = new GsonBuilder().create();
			JsonArray resulttmp = gson.fromJson(str, JsonArray.class);
			for(String k:key.split("\\.")){
				JsonArray tmpresult = new JsonArray();
				for(JsonElement value:resulttmp){
					if (value != null && !(value instanceof JsonNull)) {
						JsonArray tmp = value.getAsJsonObject().get(k).getAsJsonArray();
						for(JsonElement tmpvalue:tmp){
							if (tmpvalue != null && !(tmpvalue instanceof JsonNull)) {
								tmpresult.add(tmpvalue);
							}
						}
					}
				}
				resulttmp = tmpresult;
			}
			for(JsonElement value:resulttmp){
				result.add(value.toString());
			}
			return result;
		}else{
			return result;
		}
	}

}