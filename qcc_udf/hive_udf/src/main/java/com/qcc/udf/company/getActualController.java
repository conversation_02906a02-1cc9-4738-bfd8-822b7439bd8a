package com.qcc.udf.company;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.company.controller.CompanyActualController;
import com.qcc.udf.company.controller.CompanyPartner;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Auther: nixb
 * @Date: 2020/9/24 17:31
 * @Description:
 */
public class getActualController extends UDF {

    public String evaluate(String keyNo, String partners, String operKey, String operName) {
        String result = "{}";

        if (StringUtils.isNotEmpty(keyNo) && StringUtils.isNotEmpty(partners)) {
            List<CompanyPartner> partnerList = JSONObject.parseArray(partners.replace("\\", "\\\\"), CompanyPartner.class);
            for (CompanyPartner partner : partnerList) {
                if (StringUtils.isNotEmpty(partner.control) && !"{}".equals(partner.control)) {
                    CompanyActualController actualController = JSONObject.parseObject(partner.control, CompanyActualController.class);
                    if ((StringUtils.isNotEmpty(operKey) && StringUtils.isNotEmpty(actualController.getPkeyno()) && actualController.getPkeyno().equals(operKey))
                            || (StringUtils.isEmpty(operKey) && StringUtils.isEmpty(actualController.getPkeyno()) && full2Half(operName).equals(full2Half(actualController.getPcompanyname())))) {
                        partner.setIsoper(1);
                    } else {
                        partner.setIsoper(0);
                    }
                    partner.setActualController(actualController);
                } else if (partner.haspartner == 0) {
                    CompanyActualController actualController = new CompanyActualController();
                    actualController.setPkeyno(partner.getPkeyno() == null ? "" : partner.getPkeyno());
                    actualController.setPcompanyname(partner.getPcompanyname());
                    actualController.setStockpercent(partner.getStockpercent());
                    actualController.setKeynopath(partner.getKeynopath());
                    actualController.setIsoper(partner.getIsoper());
                    actualController.setIsgp(partner.getIsgp());
                    actualController.setGppath(partner.getGppath());
                    actualController.setHasimage(partner.isHasimage());
                    partner.setActualController(actualController);
                } else {
                    CompanyActualController actualController = new CompanyActualController();
                    actualController.setPkeyno("");
                    actualController.setPcompanyname("");
                    actualController.setIsgp(0);
                    actualController.setIsoper(0);
                    partner.setActualController(actualController);
                }

                if (StringUtils.isNotEmpty(partner.getActualController().getPkeyno())) {
                    partner.setGroupKey(partner.getActualController().getPkeyno());
                } else if (StringUtils.isNotEmpty(partner.getActualController().getPcompanyname())) {
                    partner.setGroupKey(full2Half(partner.getActualController().getPcompanyname()));
                } else {
                    partner.setGroupKey("");
                }
            }

            //实际控制人聚合
            List<CompanyPartner> groupPartnerList = getGroupPartnerList(partnerList);

            //获取实际控制人
            CompanyPartner finalActual = getActualController(groupPartnerList, partnerList);
            result = getFinalActualController(keyNo, finalActual, partnerList);
        }

        return result;
    }

    /**
     * 根据股东实际控制人聚合
     *
     * @param partnerList
     * @return
     */
    private List<CompanyPartner> getGroupPartnerList(List<CompanyPartner> partnerList) {
        List<CompanyPartner> groupPartnerList = new ArrayList<>();

        // 根据GroupKey聚合
        Map<String, List<CompanyPartner>> groupList = partnerList.stream().collect(Collectors.groupingBy(CompanyPartner::getGroupKey));
        groupList.forEach((key, list) -> {
            CompanyPartner item = new CompanyPartner();
            item.setGroupKey(key);
            if (Pattern.matches("^[0-9a-z]{32}$", key)) {
                item.setPkeyno(key);
                list.sort(Comparator.comparingInt(CompanyPartner::getIsgp).thenComparing(CompanyPartner::getStockpercent).thenComparingInt(CompanyPartner::getIsoper).reversed());
                item.setPcompanyname(list.get(0).getActualController().getPcompanyname());
            } else {
                item.setPkeyno("");
                item.setPcompanyname(key);
            }

            BigDecimal stockPercent = new BigDecimal("0");
            for (CompanyPartner s : list) {
                stockPercent = stockPercent.add(s.getStockpercent());
            }
            item.setStockpercent(stockPercent);

            item.setIsoper(list.stream().mapToInt(s -> s.getIsoper()).max().getAsInt());
            item.setIsgp(list.stream().mapToInt(s -> s.getIsgp()).max().getAsInt());
            item.setHasimage(list.stream().map(s -> s.getActualController().getHasimage()).findFirst().orElse(false));
            item.setHaspartner(list.stream().mapToInt(s -> s.getHaspartner()).max().getAsInt());
            groupPartnerList.add(item);
        });

        return groupPartnerList;
    }

    /**
     * 获取实际控制人
     *
     * @param groupPartnerList
     * @param partnerList
     * @return
     */
    private CompanyPartner getActualController(List<CompanyPartner> groupPartnerList, List<CompanyPartner> partnerList) {
        CompanyPartner finalActual = null;

        if (groupPartnerList == null || groupPartnerList.size() == 0) {
            return finalActual;
        }

        // 只有一个股东
        if (groupPartnerList.size() == 1) {
            finalActual = groupPartnerList.get(0);
        } else {
            // 根据isgp和股比倒序
            groupPartnerList.sort(Comparator.comparingInt(CompanyPartner::getIsgp).thenComparing(CompanyPartner::getStockpercent).thenComparingInt(CompanyPartner::getIsoper).reversed());

            CompanyPartner firstActual = groupPartnerList.get(0);
            CompanyPartner secondActual = groupPartnerList.get(1);

            // gp优先，否则是控制权最大的
            if (firstActual.getIsgp() == 1 || firstActual.getStockpercent().compareTo(new BigDecimal("0.5")) > 0) {
                finalActual = firstActual;
            } else {
                if (firstActual.getStockpercent().compareTo(secondActual.getStockpercent()) == 0) {
                    if (!partnerList.stream().anyMatch(s -> StringUtils.isEmpty(s.getGroupKey()))) {
                        if (firstActual.getIsoper() == 1) {
                            finalActual = firstActual;
                        } else if (secondActual.getIsoper() == 1) {
                            finalActual = secondActual;
                        }
                    }
                } else {
                    //没有实际控制人的分组
                    CompanyPartner noActualGroup = groupPartnerList.stream().filter(s -> StringUtils.isEmpty(s.getGroupKey())).findFirst().orElse(null);
                    if (noActualGroup == null || groupPartnerList.size() == 2) {
                        finalActual = firstActual;
                    } else if (StringUtils.isNotEmpty(firstActual.getGroupKey())) {
                        //如果控制权第一>控制权第二+没有控制权
                        List<CompanyPartner> hasActualGroupList = groupPartnerList.stream().filter(s -> StringUtils.isNotEmpty(s.getGroupKey())).collect(Collectors.toList());
                        hasActualGroupList.sort(Comparator.comparing(CompanyPartner::getStockpercent).reversed());
                        if (firstActual.getStockpercent().compareTo(hasActualGroupList.get(1).getStockpercent().add(noActualGroup.getStockpercent())) > 0) {
                            finalActual = firstActual;
                        }
                    }
                }
            }

            // 如果控制权最大的没有实际控制人，第二控制权的是该公司大股东
//            if (finalActual != null && finalActual.getIsgp() == 0 && StringUtils.isEmpty(finalActual.getGroupKey())
//                    && !partnerList.stream().anyMatch(s -> s.getHasActual() == 0)) {
//                partnerList.sort(Comparator.comparing(CompanyPartner::getStockpercent).reversed());
//                CompanyPartner firstPartner = partnerList.get(0);
//                CompanyPartner secondPartner = partnerList.get(1);
//
//                if (!firstPartner.getStockpercent().equals(secondPartner.getStockpercent())
//                        && (secondActual.getGroupKey().equals(firstPartner.getActualController().getPkeyno())
//                        || secondActual.getGroupKey().equals(firstPartner.getActualController().getPcompanyname()))) {
//                    finalActual = secondActual;
//                }
//            }
        }

        return finalActual;
    }

    /**
     * 拼接实际控制人信息
     *
     * @param finalActual
     * @param partnerList
     * @return
     */
    private String getFinalActualController(String keyNo, CompanyPartner finalActual, List<CompanyPartner> partnerList) {
        CompanyActualController finalActualController = new CompanyActualController();

        if (finalActual == null || StringUtils.isEmpty(finalActual.getGroupKey())) {
            //如果股东都有实际控制人，但是当前公司没有，实际控制人pkeyno给当前公司keyno
            if (!partnerList.stream().anyMatch(s -> StringUtils.isEmpty(s.getGroupKey()))) {
                finalActualController.setGppath("");
                finalActualController.setHasimage(false);
                finalActualController.setIsgp(0);
                finalActualController.setIsoper(0);
                finalActualController.setKeynopath("");
                finalActualController.setPkeyno(keyNo);
                finalActualController.setPcompanyname("");
                finalActualController.setStockpercent(new BigDecimal("0"));
                return JSON.toJSONString(finalActualController);
            }
            return "{}";
        }

        //如果股东有实际控制人拼接Keynopath和Gppath，计算股比
        List<CompanyPartner> list = partnerList.stream()
                .filter(s -> s.getGroupKey().equals(finalActual.getGroupKey()))
                .sorted(Comparator.comparingInt(CompanyPartner::getIsgp).thenComparing(CompanyPartner::getStockpercent).thenComparingDouble(CompanyPartner::getIsoper).thenComparing(CompanyPartner::getPcompanyname).reversed())
                .collect(Collectors.toList());

        finalActualController.setPkeyno(finalActual.getPkeyno());
        finalActualController.setPcompanyname(list.get(0).getActualController().getPcompanyname());
        finalActualController.setIsoper(finalActual.getIsoper());
        finalActualController.setIsgp(finalActual.getIsgp());
        finalActualController.setHasimage(finalActual.isHasimage());

        BigDecimal stockPercent = new BigDecimal("0");
        for (CompanyPartner s : list) {
            stockPercent = stockPercent.add(s.getStockpercent());
            if (s.getHaspartner() == 1) {
                s.setKeynopath(s.getKeynopath() + "-" + s.getActualController().getKeynopath().replace(",", "," + s.getKeynopath() + "-"));
                s.setGppath(s.getGppath() + s.getActualController().getGppath().replace(",", "," + s.getGppath()));
            }
        }
        finalActualController.setStockpercent(stockPercent);

        //如果大股东股比>50%，取大股东的实际控制人路径作为主路径
        String keyNoPaths;
        String gpPaths;
        if (list.get(0).getIsgp() == 1 || list.get(0).getStockpercent().compareTo(new BigDecimal("0.5")) > 0) {
            keyNoPaths = list.get(0).getKeynopath();
            gpPaths = list.get(0).getGppath();
        } else {
            keyNoPaths = list.stream().map(s -> s.getKeynopath()).collect(Collectors.joining(","));
            gpPaths = list.stream().map(s -> s.getGppath()).collect(Collectors.joining(","));
        }
        finalActualController.setKeynopath(keyNoPaths);
        finalActualController.setGppath(gpPaths);

        if (finalActualController != null) {
            return JSON.toJSONString(finalActualController);
        } else {
            return "{}";
        }
    }

    /**
     * 全角转半角
     *
     * @param input 输入
     * @return 半角文本
     */
    private String full2Half(String input) {
        char c[] = input.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == '\u3000') {
                c[i] = ' ';
            } else if (c[i] > '\uFF00' && c[i] < '\uFF5F') {
                c[i] = (char) (c[i] - 65248);
            }
        }
        return new String(c);
    }

    public static void main(String[] args) {
        getActualController model = new getActualController();
        String result = model.evaluate("0001275769892e10bcac3c0747566108", "[{\"pkeyno\":\"c2ed2812b5b937ea924a72b6b4b21ac0\",\"pcompanyname\":\"欧开实业集团有限公司\",\"stockpercent\":1,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"10\",\"keynopath\":\"0001275769892e10bcac3c0747566108\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"0\",\"hasimage\":0,\"isgp\":0,\"isoper\":1,\"keynopath\":\"c2ed2812b5b937ea924a72b6b4b21ac0\",\"pcompanyname\":\"李秀左\",\"pkeyno\":\"p8c1aefacab79119b3079ae44549d802\",\"stockpercent\":0.6}}]", "", "");
        result = model.evaluate("ccc55a3ddfca2e93ac23437db757c7d6", "[{\"pkeyno\":\"3f95eeae93de5a0aae913d8f28fcad37\",\"pcompanyname\":\"广州市晓南物业管理有限责任公司\",\"stockpercent\":0.5,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"25\",\"keynopath\":\"ccc55a3ddfca2e93ac23437db757c7d6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{}},{\"pkeyno\":\"g6f86fa90301b8c2aef5805294966dbd\",\"pcompanyname\":\"广州市海珠区瑞宝街瑞宝第二经济合作社\",\"stockpercent\":0.5,\"org\":4,\"hasimage\":false,\"shouldcapi\":\"25\",\"keynopath\":\"ccc55a3ddfca2e93ac23437db757c7d6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":0,\"control\":{}}]", "pr002afe5901fd69d8fc719cc64999f2", "李顺洪");
        result = model.evaluate("f20a5b575c5a932da8e347898ecd6d54", "[{\"pkeyno\":\"\",\"pcompanyname\":\"南阳市黄石庵林场（河南伏牛山国家级自然保护区黄石庵管理局）\",\"stockpercent\":0.8,\"org\":-1,\"hasimage\":false,\"shouldcapi\":\"40\",\"keynopath\":\"f20a5b575c5a932da8e347898ecd6d54\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":0,\"control\":{}},{\"pkeyno\":\"p6550a621f9be11e4d52d123752633ff\",\"pcompanyname\":\"马合军\",\"stockpercent\":0.2,\"org\":2,\"hasimage\":false,\"shouldcapi\":\"10\",\"keynopath\":\"f20a5b575c5a932da8e347898ecd6d54\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":0,\"control\":{}}]\t", "p991d68310e4f9db1619316d88439011", "衡建宏");
        result = model.evaluate("291f4f1a6c59eb113a7179da26facb51", "[{\"pkeyno\":\"p09ae04ec7f8813a0886efc467feb2c0\",\"pcompanyname\":\"范积绵\",\"stockpercent\":0.04,\"org\":2,\"hasimage\":false,\"shouldcapi\":\"4\",\"keynopath\":\"291f4f1a6c59eb113a7179da26facb51\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":0,\"control\":{}},{\"pkeyno\":\"27a2e6147964c1605d186ca601bc9ad5\",\"pcompanyname\":\"青岛福源餐具制品有限公司\",\"stockpercent\":0.01,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"1\",\"keynopath\":\"291f4f1a6c59eb113a7179da26facb51\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{}},{\"pkeyno\":\"p952e2fa18fa3cd00f1605c02be54e70\",\"pcompanyname\":\"赵希锋\",\"stockpercent\":0.25,\"org\":2,\"hasimage\":false,\"shouldcapi\":\"25\",\"keynopath\":\"291f4f1a6c59eb113a7179da26facb51\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":0,\"control\":{}},{\"pkeyno\":\"pedc51f819c014aef39b5ff46759def3\",\"pcompanyname\":\"薛永杰\",\"stockpercent\":0.04,\"org\":2,\"hasimage\":false,\"shouldcapi\":\"4\",\"keynopath\":\"291f4f1a6c59eb113a7179da26facb51\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":0,\"control\":{}},{\"pkeyno\":\"pdc8721c224a23d7f6178d32e64d491e\",\"pcompanyname\":\"薛兰英\",\"stockpercent\":0.04,\"org\":2,\"hasimage\":false,\"shouldcapi\":\"4\",\"keynopath\":\"291f4f1a6c59eb113a7179da26facb51\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":0,\"control\":{}},{\"pkeyno\":\"ee0b6449378a9b6fb306195772d5d370\",\"pcompanyname\":\"青岛经济技术开发区环宇物业管理有限公司\",\"stockpercent\":0.25,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"25\",\"keynopath\":\"291f4f1a6c59eb113a7179da26facb51\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"00\",\"hasimage\":false,\"isgp\":0,\"isoper\":0,\"keynopath\":\"ee0b6449378a9b6fb306195772d5d370-7cdc25f599539e62120b43fced6f327a\",\"pcompanyname\":\"焦志福\",\"pkeyno\":\"p14cc70c79de818a7cd01eb65227b392\",\"stockpercent\":1}},{\"pkeyno\":\"p47016b4f1639dfd578405e433b445fb\",\"pcompanyname\":\"苗在文\",\"stockpercent\":0.04,\"org\":2,\"hasimage\":false,\"shouldcapi\":\"4\",\"keynopath\":\"291f4f1a6c59eb113a7179da26facb51\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":0,\"control\":{}},{\"pkeyno\":\"pac27a47600f790078e5f65eff7e2307\",\"pcompanyname\":\"刘洪新\",\"stockpercent\":0.25,\"org\":2,\"hasimage\":false,\"shouldcapi\":\"25\",\"keynopath\":\"291f4f1a6c59eb113a7179da26facb51\",\"isoper\":1,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":0,\"control\":{}},{\"pkeyno\":\"p10837cb21d73157d73f2ccb8e260167\",\"pcompanyname\":\"焦玉玲\",\"stockpercent\":0.04,\"org\":2,\"hasimage\":false,\"shouldcapi\":\"4\",\"keynopath\":\"291f4f1a6c59eb113a7179da26facb51\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":0,\"control\":{}},{\"pkeyno\":\"p79c84e19b8b73efc619886e2286beea\",\"pcompanyname\":\"耿光梅\",\"stockpercent\":0.04,\"org\":2,\"hasimage\":false,\"shouldcapi\":\"4\",\"keynopath\":\"291f4f1a6c59eb113a7179da26facb51\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":0,\"control\":{}}]\t", "pac27a47600f790078e5f65eff7e2307", "刘洪新");
        result = model.evaluate("34cdc2712479132cd95552f074fdacdf", "[{\"pkeyno\":\"f2709e0c929d615b8d812473e5260f27\",\"pcompanyname\":\"南京英缪赛生物科技有限公司\",\"stockpercent\":0.25,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"25\",\"keynopath\":\"34cdc2712479132cd95552f074fdacdf\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{}},{\"pkeyno\":\"p23e3c29903bb07af9845a21ad0bfa1b\",\"pcompanyname\":\"潘坚\",\"stockpercent\":0.25,\"org\":2,\"hasimage\":false,\"shouldcapi\":\"25\",\"keynopath\":\"34cdc2712479132cd95552f074fdacdf\",\"isoper\":1,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":0,\"control\":{}},{\"pkeyno\":\"3a346cdcfbd7c251395ba28507bcf17d\",\"pcompanyname\":\"阿法隆（南京）游艇有限公司\",\"stockpercent\":0.25,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"25\",\"keynopath\":\"34cdc2712479132cd95552f074fdacdf\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"0\",\"hasimage\":false,\"isgp\":0,\"isoper\":0,\"keynopath\":\"3a346cdcfbd7c251395ba28507bcf17d\",\"pcompanyname\":\"Innoco B.V.\",\"pkeyno\":\"z0135c87bb671afee6858911c82d0581\",\"stockpercent\":1}},{\"pkeyno\":\"d512e2162464d7a7f89bd7fa0eeec9e9\",\"pcompanyname\":\"南京才润创业投资有限公司\",\"stockpercent\":0.25,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"25\",\"keynopath\":\"34cdc2712479132cd95552f074fdacdf\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"0\",\"hasimage\":false,\"isgp\":0,\"isoper\":1,\"keynopath\":\"d512e2162464d7a7f89bd7fa0eeec9e9\",\"pcompanyname\":\"潘秀兵\",\"pkeyno\":\"p66763c4eb2a6b6734ab8dd419d5ff5e\",\"stockpercent\":0.6}}]", "p23e3c29903bb07af9845a21ad0bfa1b", "潘坚");
        result = model.evaluate("00b6ba5abb53b32d450d91018a9427dc", "[{\"pkeyno\":\"c49792ca672f0f9971cdf0f2a3dcfa7e\",\"pcompanyname\":\"中农润民农业科技有限公司\",\"stockpercent\":1,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"5000\",\"keynopath\":\"00b6ba5abb53b32d450d91018a9427dc\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"00;01\",\"hasimage\":false,\"isgp\":0,\"isoper\":0,\"keynopath\":\"c49792ca672f0f9971cdf0f2a3dcfa7e-c8a081e3188814f7fc9180b06da1834e;c49792ca672f0f9971cdf0f2a3dcfa7e-718f9b72a4e9833c3843bf91138b083f\",\"pcompanyname\":\"彭俊\",\"pkeyno\":\"pc0ee78d4af41056abc5aaf390c5c36a\",\"stockpercent\":1.0}}]", "prd83a0f820424d1cbd35fa742840c50", "万里亮");
        result = model.evaluate("00328b3f19c95edf52b5f20d99351798", "[{\"pkeyno\":\"904c859bcdfd8725b5be2b7b85cfa8da\",\"pcompanyname\":\"上海金山第二工业区投资有限公司\",\"stockpercent\":1,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"3728\",\"keynopath\":\"00328b3f19c95edf52b5f20d99351798\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"000;000;00\",\"hasimage\":false,\"isgp\":0,\"isoper\":0,\"keynopath\":\"904c859bcdfd8725b5be2b7b85cfa8da-00820594387775c560ccd3c4829eb935-2bce5f12a1c869075a474ac44e44d1e3;00820594387775c560ccd3c4829eb935-701b4edfc13c6e6aaeaf854dc4568a90-2bce5f12a1c869075a474ac44e44d1e3;904c859bcdfd8725b5be2b7b85cfa8da-2bce5f12a1c869075a474ac44e44d1e3\",\"pcompanyname\":\"上海市金山区金山卫镇经济管理事务中心\",\"pkeyno\":\"g40921e6461645f32658c2a8df74c196\",\"stockpercent\":0.666667}}]", "pb00f137aa72bc766510a0b00e5dd982", "黄俊杰");
        result = model.evaluate("005cd09a33d53ae495ec72836edf7deb", "[{\"pkeyno\":\"e3ea55a512f7b4c42f1f90549097e6ef\",\"pcompanyname\":\"饶阳县博睿文化传媒有限公司\",\"stockpercent\":0.5,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"250\",\"keynopath\":\"005cd09a33d53ae495ec72836edf7deb\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"0\",\"hasimage\":false,\"isgp\":0,\"isoper\":1,\"keynopath\":\"e3ea55a512f7b4c42f1f90549097e6ef\",\"pcompanyname\":\"李群辉\",\"pkeyno\":\"p3bb7a4d5553f8bfd37e8ef4207a99ed\",\"stockpercent\":1.0}},{\"pkeyno\":\"prc406a1a9261bf0244dbdf4cd006aa5\",\"pcompanyname\":\"李岱辉\",\"stockpercent\":0.5,\"org\":2,\"hasimage\":false,\"shouldcapi\":\"250\",\"keynopath\":\"005cd09a33d53ae495ec72836edf7deb\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":0,\"control\":{}}]", "p3bb7a4d5553f8bfd37e8ef4207a99ed", "李群辉");
        result = model.evaluate("2e6c498caee06e7d94c0dad66de0e8e6", "[{\"pkeyno\":\"6a462f6323cf81369d7f2d84c12e6c3a\",\"pcompanyname\":\"杭州圆景股权投资合伙企业（有限合伙）\",\"stockpercent\":0.022216,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"56.6538\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"110\",\"hasimage\":false,\"isgp\":1,\"isoper\":0,\"keynopath\":\"6a462f6323cf81369d7f2d84c12e6c3a-0333d535f23b3d281f25a32d3ff3da8c-59ac2549c5db029512fce432a26f9da6\",\"pcompanyname\":\"郭庆杭\",\"pkeyno\":\"pf039c5565b53906503e4836c8a02944\",\"stockpercent\":0.139059}},{\"pkeyno\":\"p0307597b2af5708d22c5545a63a0423\",\"pcompanyname\":\"高始兴\",\"stockpercent\":0.121248,\"org\":2,\"hasimage\":true,\"shouldcapi\":\"309.195\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":1,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":0,\"control\":{}},{\"pkeyno\":\"b86d2d6cc193c3efa21ed60fe9078700\",\"pcompanyname\":\"广东横琴新凤祥光明投资合伙企业（有限合伙）\",\"stockpercent\":0.045953,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"117.184\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"10,0\",\"hasimage\":false,\"isgp\":1,\"isoper\":0,\"keynopath\":\"b86d2d6cc193c3efa21ed60fe9078700-ca866f1794905787c190e912e2e5c9d1,b86d2d6cc193c3efa21ed60fe9078700\",\"pcompanyname\":\"刘志明\",\"pkeyno\":\"p520deea46bddafcd61e4179605abcfa\",\"stockpercent\":0.505}},{\"pkeyno\":\"e31c9fb0c2531cec2d0530853806f047\",\"pcompanyname\":\"南京国调国信智芯股权投资合伙企业（有限合伙）\",\"stockpercent\":0.03246,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"82.7762\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"110\",\"hasimage\":false,\"isgp\":1,\"isoper\":0,\"keynopath\":\"e31c9fb0c2531cec2d0530853806f047-827b0c786f34e5ef60ef6aa445fbc619-08e4bf96f5a25783efa37b08ad33e83d\",\"pcompanyname\":\"王雨晴\",\"pkeyno\":\"pc48d714d097eda3e154056e09e9de0e\",\"stockpercent\":0.014397}},{\"pkeyno\":\"813671b05d047d4b6506c501e62df889\",\"pcompanyname\":\"珠海横琴境成聚成创业投资基金（有限合伙）\",\"stockpercent\":0.004893,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"12.4773\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"110,10\",\"hasimage\":false,\"isgp\":1,\"isoper\":0,\"keynopath\":\"813671b05d047d4b6506c501e62df889-33f96bed1c03c0bd3301a963b2780847-89d638af0530c7cc9a9f3da5c4d4842c,813671b05d047d4b6506c501e62df889-33f96bed1c03c0bd3301a963b2780847\",\"pcompanyname\":\"丛远兵\",\"pkeyno\":\"p4810884736489c5496ea6ae800823dc\",\"stockpercent\":0.011407}},{\"pkeyno\":\"d1921cbbabd77dece233fd64618fafc2\",\"pcompanyname\":\"武汉斐翔汽车电子产业投资合伙企业（有限合伙）\",\"stockpercent\":0.009739,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"24.835\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"000\",\"hasimage\":false,\"isgp\":0,\"isoper\":0,\"keynopath\":\"d1921cbbabd77dece233fd64618fafc2-bb8f36844d4476b2fb63a0deac37945f-2eee6a65d3982222be3552243d405dc2\",\"pcompanyname\":\"武汉东湖新技术开发区管理委员会\",\"pkeyno\":\"g2799c0b9bcf33085260ed2f899f819d\",\"stockpercent\":0.666667}},{\"pkeyno\":\"1d1b2e6e53ccc831430bee4feecaad67\",\"pcompanyname\":\"苏州联想之星天使投资中心（有限合伙）\",\"stockpercent\":0.065364,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"166.6857\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{}},{\"pkeyno\":\"854981beb205ad84cac03f13893e0a2a\",\"pcompanyname\":\"中新苏州工业园区创业投资有限公司\",\"stockpercent\":0.013552,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"34.558\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"000,000\",\"hasimage\":true,\"isgp\":0,\"isoper\":0,\"keynopath\":\"854981beb205ad84cac03f13893e0a2a-763493d6c4bb9872288f921feb9e4eb6-f3a2b2a1c4bf04b4510480c1126b71ff,854981beb205ad84cac03f13893e0a2a-763493d6c4bb9872288f921feb9e4eb6-99dfb7e99fceb21ec1d530a4a29a4b95\",\"pcompanyname\":\"苏州工业园区管理委员会\",\"pkeyno\":\"gc64d7b823f2069db1b68249e3306cc1\",\"stockpercent\":1}},{\"pkeyno\":\"e524e0fb0bd9c88ab4ae979bbbb9531a\",\"pcompanyname\":\"高邮红土创业投资基金（有限合伙）\",\"stockpercent\":0.005421,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"13.8232\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{}},{\"pkeyno\":\"88e0dedd40d45447c23686d3559eb97b\",\"pcompanyname\":\"宁波梅山保税港区睿薪投资管理合伙企业（有限合伙）\",\"stockpercent\":0.008402,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"21.426\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"10\",\"hasimage\":false,\"isgp\":1,\"isoper\":0,\"keynopath\":\"88e0dedd40d45447c23686d3559eb97b-06bdb46de2f295c4fae2f83b74c795ae\",\"pcompanyname\":\"朱世杰\",\"pkeyno\":\"p334a0a75309b2a2312c6976890b75d7\",\"stockpercent\":0.009434}},{\"pkeyno\":\"7854038dd69e5f3c146fa0521006247d\",\"pcompanyname\":\"嘉兴会凌拾贰号投资合伙企业（有限合伙）\",\"stockpercent\":0.023002,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"58.6576\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"10\",\"hasimage\":false,\"isgp\":1,\"isoper\":0,\"keynopath\":\"7854038dd69e5f3c146fa0521006247d-35f097f251aa2bb7d105082171dd815d\",\"pcompanyname\":\"张凤林\",\"pkeyno\":\"p85d7b7e31169e2f9d4daa78339c3d1b\",\"stockpercent\":0.011646}},{\"pkeyno\":\"5fc94ec5fd6a1e9a48ba9acfbf22f69e\",\"pcompanyname\":\"联发博动科技（北京）有限公司\",\"stockpercent\":0.013552,\"org\":0,\"hasimage\":true,\"shouldcapi\":\"34.558\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"0\",\"hasimage\":false,\"isgp\":0,\"isoper\":0,\"keynopath\":\"5fc94ec5fd6a1e9a48ba9acfbf22f69e\",\"pcompanyname\":\"联发科中国有限公司\",\"pkeyno\":\"h549d417af9deec8849fed37530f08d0\",\"stockpercent\":1}},{\"pkeyno\":\"79acc0f2f2f7af08e3fa68db986de120\",\"pcompanyname\":\"嘉兴五信之琪股权投资合伙企业（有限合伙）\",\"stockpercent\":0.027269,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"69.5398\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"10,101,010\",\"hasimage\":false,\"isgp\":1,\"isoper\":0,\"keynopath\":\"79acc0f2f2f7af08e3fa68db986de120-72c45eb85eb34f7b5c1ea9e6483b4883,79acc0f2f2f7af08e3fa68db986de120-72c45eb85eb34f7b5c1ea9e6483b4883-fd4a29fc10d4aa4f6eaee9e9535e13de,79acc0f2f2f7af08e3fa68db986de120-a1ca5f2bbdd98f4ea2c8f8e03a041cac-bc52dceb4ec01b896e7d0c9074d59c09\",\"pcompanyname\":\"马飞\",\"pkeyno\":\"pe1a10d5694977ef94fd82a482588f75\",\"stockpercent\":0.088236}},{\"pkeyno\":\"88e5ddbcc357709a65352bc8a0913355\",\"pcompanyname\":\"苏州境成高锦股权投资企业（有限合伙）\",\"stockpercent\":0.004893,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"12.4773\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"110,10\",\"hasimage\":false,\"isgp\":1,\"isoper\":0,\"keynopath\":\"88e5ddbcc357709a65352bc8a0913355-25e54239d2b8789621b1efa68886c352-89d638af0530c7cc9a9f3da5c4d4842c,88e5ddbcc357709a65352bc8a0913355-25e54239d2b8789621b1efa68886c352\",\"pcompanyname\":\"丛远兵\",\"pkeyno\":\"p4810884736489c5496ea6ae800823dc\",\"stockpercent\":0.010228}},{\"pkeyno\":\"c70a55cb048c8e4db7bca357a2c113e0\",\"pcompanyname\":\"阿里巴巴（中国）网络技术有限公司\",\"stockpercent\":0.160071,\"org\":0,\"hasimage\":true,\"shouldcapi\":\"408.1971\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"000,000\",\"hasimage\":false,\"isgp\":0,\"isoper\":0,\"keynopath\":\"c70a55cb048c8e4db7bca357a2c113e0-fa3d9de6fd3ccf354b5ae34c38eb7587-he0f4179237a7ece83af003ca061839f,c70a55cb048c8e4db7bca357a2c113e0-561b327281de903b19bb458772446bfe-he0f4179237a7ece83af003ca061839f\",\"pcompanyname\":\"TAOBAO HOLDING LIMITED\",\"pkeyno\":\"gc784a3dbd71c911728967997bdb0aa5\",\"stockpercent\":0.860587}},{\"pkeyno\":\"0251a7210e08c3fd18da7e61f6392033\",\"pcompanyname\":\"启迪创新（天津）股权投资基金合伙企业（有限合伙）\",\"stockpercent\":0.070977,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"180.9974\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"00\",\"hasimage\":false,\"isgp\":0,\"isoper\":0,\"keynopath\":\"0251a7210e08c3fd18da7e61f6392033-029ca32c236c177ffefe2962f35fb539\",\"pcompanyname\":\"清华大学教育基金会\",\"pkeyno\":\"j8dddb390f2b4ef22be89b26f16b8a4d\",\"stockpercent\":0.091149}},{\"pkeyno\":\"b670f8a30c42ae082d9d523e29a72aa6\",\"pcompanyname\":\"西藏达孜积慧聚焦投资合伙企业（有限合伙）\",\"stockpercent\":0.149171,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"380.4009\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"1\",\"hasimage\":true,\"isgp\":1,\"isoper\":1,\"keynopath\":\"b670f8a30c42ae082d9d523e29a72aa6\",\"pcompanyname\":\"高始兴\",\"pkeyno\":\"p0307597b2af5708d22c5545a63a0423\",\"stockpercent\":0.8}},{\"pkeyno\":\"744289b7441071f5841b30a8251724b1\",\"pcompanyname\":\"鸿富创新（杭州）有限公司\",\"stockpercent\":0.013552,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"34.558\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"000\",\"hasimage\":false,\"isgp\":0,\"isoper\":0,\"keynopath\":\"744289b7441071f5841b30a8251724b1-92b395e5c73827445cb91786e3f96496-h4ac318c5eb8d36594649a0386f15970\",\"pcompanyname\":\"BEST BEHAVIOUR HOLDINGS LIMITED\",\"pkeyno\":\"g68f7338594942653610739963d57893\",\"stockpercent\":1}},{\"pkeyno\":\"158c86458842cdcbe9460796d93e39d8\",\"pcompanyname\":\"苏州明善汇德投资企业（有限合伙）\",\"stockpercent\":0.002435,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"6.2093\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"10\",\"hasimage\":false,\"isgp\":1,\"isoper\":0,\"keynopath\":\"158c86458842cdcbe9460796d93e39d8-6925eb5cfbe223ab207f40bbb3599db1\",\"pcompanyname\":\"张小冬\",\"pkeyno\":\"p9e9fc77645b45d7e0f5176e4b111073\",\"stockpercent\":0.009808}},{\"pkeyno\":\"p241ec08590549b0df6ecc41ca059aab\",\"pcompanyname\":\"俞凯\",\"stockpercent\":0.094223,\"org\":2,\"hasimage\":true,\"shouldcapi\":\"240.2791\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":0,\"control\":{}},{\"pkeyno\":\"9092b79677d1d0faf9b30776eece220c\",\"pcompanyname\":\"苏州工业园区元禾秉胜股权投资基金合伙企业（有限合伙）\",\"stockpercent\":0.013552,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"34.558\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"0\",\"hasimage\":true,\"isgp\":0,\"isoper\":0,\"keynopath\":\"9092b79677d1d0faf9b30776eece220c\",\"pcompanyname\":\"全国社会保障基金理事会\",\"pkeyno\":\"g599bafb4da74aa069cc9b67a39992e0\",\"stockpercent\":0.325521}},{\"pkeyno\":\"f37bbffa58c81ff76ac86aefa056e8be\",\"pcompanyname\":\"深圳市创新投资集团有限公司\",\"stockpercent\":0.013552,\"org\":0,\"hasimage\":true,\"shouldcapi\":\"34.558\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"0,00,00\",\"hasimage\":true,\"isgp\":0,\"isoper\":0,\"keynopath\":\"f37bbffa58c81ff76ac86aefa056e8be,f37bbffa58c81ff76ac86aefa056e8be-ccbceae9799e3e7896d69139229148d6,f37bbffa58c81ff76ac86aefa056e8be-9b5cb0034186e0370459a6b4ee351d80\",\"pcompanyname\":\"深圳市人民政府国有资产监督管理委员会\",\"pkeyno\":\"g4f96c6d9038c738efd78b1d2a554455\",\"stockpercent\":0.433221}},{\"pkeyno\":\"142dc26dbf28db68ac8143802106ad2c\",\"pcompanyname\":\"江苏疌泉红土智能创业投资基金（有限合伙）\",\"stockpercent\":0.008131,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"20.7348\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"00,000,000\",\"hasimage\":true,\"isgp\":0,\"isoper\":0,\"keynopath\":\"142dc26dbf28db68ac8143802106ad2c-f37bbffa58c81ff76ac86aefa056e8be,142dc26dbf28db68ac8143802106ad2c-f37bbffa58c81ff76ac86aefa056e8be-ccbceae9799e3e7896d69139229148d6,142dc26dbf28db68ac8143802106ad2c-f37bbffa58c81ff76ac86aefa056e8be-9b5cb0034186e0370459a6b4ee351d80\",\"pcompanyname\":\"深圳市人民政府国有资产监督管理委员会\",\"pkeyno\":\"g4f96c6d9038c738efd78b1d2a554455\",\"stockpercent\":0.346154}},{\"pkeyno\":\"pc15faedc204124add008e1a269efe55\",\"pcompanyname\":\"林远东\",\"stockpercent\":0.032624,\"org\":2,\"hasimage\":true,\"shouldcapi\":\"83.1949\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":0,\"control\":{}},{\"pkeyno\":\"2db47d388106a7d7aa2a9021b8ad279e\",\"pcompanyname\":\"潍坊北汽新功能转换创业投资基金合伙企业（有限合伙）\",\"stockpercent\":0.009739,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"24.835\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{}},{\"pkeyno\":\"58334c033d54d7e45a3dd398b2c057f9\",\"pcompanyname\":\"苏州康力君卓股权投资中心（有限合伙）\",\"stockpercent\":0.012174,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"31.0439\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"00\",\"hasimage\":true,\"isgp\":0,\"isoper\":0,\"keynopath\":\"58334c033d54d7e45a3dd398b2c057f9-f0e74c529176e189a6ab9f8c645709b5\",\"pcompanyname\":\"王友林\",\"pkeyno\":\"p4386c7e3bb6ce6373076fcfc5b28a9a\",\"stockpercent\":0.877193}},{\"pkeyno\":\"9878805a9f6d138c4d063ae4b23e66ad\",\"pcompanyname\":\"北京华创策联创业投资中心（有限合伙）\",\"stockpercent\":0.00279,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"7.1147\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"10\",\"hasimage\":true,\"isgp\":1,\"isoper\":0,\"keynopath\":\"9878805a9f6d138c4d063ae4b23e66ad-44407b2fed91edecc1f7c506a03e63c4\",\"pcompanyname\":\"罗茁\",\"pkeyno\":\"p7ba177f090f86ee84ac5d5b4c4e00cd\",\"stockpercent\":0.01}},{\"pkeyno\":\"e98e33c8f4a7a6e69a7722f8a327a94a\",\"pcompanyname\":\"金石智娱股权投资（杭州）合伙企业（有限合伙）\",\"stockpercent\":0.007304,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"18.6257\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"000\",\"hasimage\":false,\"isgp\":0,\"isoper\":0,\"keynopath\":\"e98e33c8f4a7a6e69a7722f8a327a94a-3b218bbfe901a8b37a7213dfa469489f-1c28dd6a67b48f116b02432d47c04d62\",\"pcompanyname\":\"广东省人民政府国有资产监督管理委员会\",\"pkeyno\":\"g21879a1699064d04fe956b10ffd3e19\",\"stockpercent\":0.364697}},{\"pkeyno\":\"51b0d8dd0996a9ac88389177e409b566\",\"pcompanyname\":\"苏州境成聚成创业投资企业（有限合伙）\",\"stockpercent\":0.002446,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"6.2386\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"110,10,0\",\"hasimage\":false,\"isgp\":1,\"isoper\":0,\"keynopath\":\"51b0d8dd0996a9ac88389177e409b566-25e54239d2b8789621b1efa68886c352-89d638af0530c7cc9a9f3da5c4d4842c,51b0d8dd0996a9ac88389177e409b566-25e54239d2b8789621b1efa68886c352,51b0d8dd0996a9ac88389177e409b566\",\"pcompanyname\":\"丛远兵\",\"pkeyno\":\"p4810884736489c5496ea6ae800823dc\",\"stockpercent\":0.053572}},{\"pkeyno\":\"e4f80dcfc9df97ab369a8cd9f55a355f\",\"pcompanyname\":\"聚安（上海）投资有限公司\",\"stockpercent\":0.009296,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"23.7068\",\"keynopath\":\"2e6c498caee06e7d94c0dad66de0e8e6\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"0\",\"hasimage\":false,\"isgp\":0,\"isoper\":0,\"keynopath\":\"e4f80dcfc9df97ab369a8cd9f55a355f\",\"pcompanyname\":\"胡浏鹤\",\"pkeyno\":\"p041a9027cf3abf9f4906dded2a1fda8\",\"stockpercent\":0.5}}]\t", "p0307597b2af5708d22c5545a63a0423", "高始兴");
        result = model.evaluate("b46b029cdb99248761c2e0494f59c630", "[{\"pkeyno\":\"ce15f3ad5a969ab4bdbb8d68eb815162\",\"pcompanyname\":\"广州捷越置业有限公司\",\"stockpercent\":0.12,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"6000\",\"keynopath\":\"b46b029cdb99248761c2e0494f59c630\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"0\",\"hasimage\":false,\"isgp\":0,\"isoper\":0,\"keynopath\":\"ce15f3ad5a969ab4bdbb8d68eb815162\",\"pcompanyname\":\"陈衡\",\"pkeyno\":\"p2bc9b7de6b479e077713135e10a15cd\",\"stockpercent\":1}},{\"pkeyno\":\"e3a56019144118941c38a76d8780e748\",\"pcompanyname\":\"广州福晟投资有限公司\",\"stockpercent\":0.88,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"44000\",\"keynopath\":\"b46b029cdb99248761c2e0494f59c630\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{}}]", "pr0a4ba3ab0f8637f4dbb31aa6c00956", "黄俊南");
        result = model.evaluate("05f60c6362d8d1fc0f7f36ae5c3a78c4", "[{\"pkeyno\":\"dcdc82a495632e3c588c3399f41da6d1\",\"pcompanyname\":\"融信（福建）投资集团有限公司\",\"stockpercent\":0.5,\"org\":0,\"hasimage\":true,\"shouldcapi\":\"50000\",\"keynopath\":\"05f60c6362d8d1fc0f7f36ae5c3a78c4\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"0000\",\"hasimage\":false,\"isgp\":0,\"isoper\":0,\"keynopath\":\"dcdc82a495632e3c588c3399f41da6d1-305ca1e43af7eb0e5187f45e805a33cd-1eb8cbaba2355f6e7c5d467bd12549c3-39d2e3575fbcbf9d3e4173b4c2747414\",\"pcompanyname\":\"融泰有限公司\",\"pkeyno\":\"hfea7ed99f10ef1f61c28924854c20a3\",\"stockpercent\":1}},{\"pkeyno\":\"ef9e5893893b34722551bdb2ebc55993\",\"pcompanyname\":\"融汇（福建）集团有限公司\",\"stockpercent\":0.09,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"9000\",\"keynopath\":\"05f60c6362d8d1fc0f7f36ae5c3a78c4\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"000\",\"hasimage\":false,\"isgp\":0,\"isoper\":0,\"keynopath\":\"ef9e5893893b34722551bdb2ebc55993-2c24c398274c6b42eb0b762ee90df252-hef726d85902b3178ecf189e899182bc\",\"pcompanyname\":\"黃祖仕\",\"pkeyno\":\"pr664f8063bacbad190bf75ee4d457f8\",\"stockpercent\":1}},{\"pkeyno\":\"92731e7b4d134f50a476c1096423cf1b\",\"pcompanyname\":\"重庆融汇投资有限公司\",\"stockpercent\":0.41,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"41000\",\"keynopath\":\"05f60c6362d8d1fc0f7f36ae5c3a78c4\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{}}]", "pr01f2b1bccce649d66412d041c2472d", "郎辉");
        result = model.evaluate("fc4d52e49d0583723958573e4ada8f62", "[{\"pkeyno\":\"fa24a757bd1537e9489443a3355acd9d\",\"pcompanyname\":\"四川聚信发展股权投资基金管理有限公司\",\"stockpercent\":0.3,\"org\":0,\"hasimage\":true,\"shouldcapi\":\"300\",\"keynopath\":\"fc4d52e49d0583723958573e4ada8f62\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"\",\"hasimage\":false,\"isgp\":0,\"isoper\":0,\"keynopath\":\"\",\"pcompanyname\":\"\",\"pkeyno\":\"fa24a757bd1537e9489443a3355acd9d\",\"stockpercent\":0}},{\"pkeyno\":\"8b8ed50f982a908da7fd089b6ce4b9d6\",\"pcompanyname\":\"厦门国际信托有限公司\",\"stockpercent\":0.4,\"org\":0,\"hasimage\":true,\"shouldcapi\":\"400\",\"keynopath\":\"fc4d52e49d0583723958573e4ada8f62\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"0000,000\",\"hasimage\":true,\"isgp\":0,\"isoper\":0,\"keynopath\":\"8b8ed50f982a908da7fd089b6ce4b9d6-55b8a29d845a3c891b45a1cca9ff700c-4dbaa5473821d3e452eda90b7dd51088-2604a65557c5c1b21b985731172a8d97,8b8ed50f982a908da7fd089b6ce4b9d6-55b8a29d845a3c891b45a1cca9ff700c-2604a65557c5c1b21b985731172a8d97\",\"pcompanyname\":\"厦门市财政局\",\"pkeyno\":\"g96d22559ad43907cad24601ecc66c86\",\"stockpercent\":0.8}},{\"pkeyno\":\"8d44836b737eb7ffd2f6ab9539d1d9f9\",\"pcompanyname\":\"成都聚智汇诚资产管理有限公司\",\"stockpercent\":0.3,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"300\",\"keynopath\":\"fc4d52e49d0583723958573e4ada8f62\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"0\",\"hasimage\":false,\"isgp\":0,\"isoper\":1,\"keynopath\":\"8d44836b737eb7ffd2f6ab9539d1d9f9\",\"pcompanyname\":\"杨杰涵\",\"pkeyno\":\"pr0eeebab218bf6c00c594abfc40e327\",\"stockpercent\":0.95}}]", "p8eb5ffbb264cf3273bfb7661f2f7867", "刘斌");
        result = model.evaluate("fa24a757bd1537e9489443a3355acd9d", "[{\"pkeyno\":\"c61f125355ac19c1fe712d94c8f10c75\",\"pcompanyname\":\"中信聚信（北京）资本管理有限公司\",\"stockpercent\":0.49,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"2450\",\"keynopath\":\"fa24a757bd1537e9489443a3355acd9d\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"0000\",\"hasimage\":false,\"isgp\":0,\"isoper\":0,\"keynopath\":\"c61f125355ac19c1fe712d94c8f10c75-6010e65e0cf7637506a13cb8e809a1fe-c838f43376ba7f0424ea155e5a3e467d-h23978d9855cf279d37911d5a289980d\",\"pcompanyname\":\"中信盛星有限公司\",\"pkeyno\":\"ge8cb438f459ed862e62ab58bec56073\",\"stockpercent\":1}},{\"pkeyno\":\"1cf7fbd6a80a7010c3849ec6f3956a43\",\"pcompanyname\":\"成都聚智投资管理中心（有限合伙）\",\"stockpercent\":0.02,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"100\",\"keynopath\":\"fa24a757bd1537e9489443a3355acd9d\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"10\",\"hasimage\":false,\"isgp\":1,\"isoper\":0,\"keynopath\":\"1cf7fbd6a80a7010c3849ec6f3956a43-8d44836b737eb7ffd2f6ab9539d1d9f9\",\"pcompanyname\":\"杨杰涵\",\"pkeyno\":\"pr0eeebab218bf6c00c594abfc40e327\",\"stockpercent\":0.7}},{\"pkeyno\":\"fb44b736ed45616511b54b712caf22d0\",\"pcompanyname\":\"四川发展股权投资基金管理有限公司\",\"stockpercent\":0.49,\"org\":0,\"hasimage\":false,\"shouldcapi\":\"2450\",\"keynopath\":\"fa24a757bd1537e9489443a3355acd9d\",\"isoper\":0,\"isgp\":0,\"gppath\":\"0\",\"haspartner\":1,\"control\":{\"gppath\":\"00\",\"hasimage\":true,\"isgp\":0,\"isoper\":0,\"keynopath\":\"fb44b736ed45616511b54b712caf22d0-719ca433a2658f7d50021e4a9e85a38e\",\"pcompanyname\":\"四川省人民政府\",\"pkeyno\":\"gd677edd232c8aeef94bbe0b5e884bcf\",\"stockpercent\":1}}]", "pr42c92d86b366630b7e51ec4fa27240", "席代金");
    }
}

