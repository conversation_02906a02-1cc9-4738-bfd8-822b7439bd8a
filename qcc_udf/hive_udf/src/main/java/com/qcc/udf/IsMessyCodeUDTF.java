package com.qcc.udf;

import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.model.MessyCodeModelEntity;
import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentTypeException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDTF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.PrimitiveObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;
import org.datanucleus.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 获取多个字段的乱码数量
 *
 * <AUTHOR>
 */
public class IsMessyCodeUDTF extends GenericUDTF {
    /**
     * 列信息
     *
     * @param argOIs
     * @return
     * @throws UDFArgumentException
     */
    @Override
    public StructObjectInspector initialize(ObjectInspector[] argOIs) throws UDFArgumentException {
        if (argOIs.length < 3) {
            throw new UDFArgumentException("IsMessyCodeUDTF takes at least one argument.");
        }
        for (int i = 0; i < argOIs.length; i++) {
            if (argOIs[i].getCategory() != ObjectInspector.Category.PRIMITIVE
                    && ((PrimitiveObjectInspector) argOIs[i]).getPrimitiveCategory() != PrimitiveObjectInspector.PrimitiveCategory.STRING) {
                throw new UDFArgumentTypeException(i, "IsMessyCodeUDTF takes a string as a parameter.");
            }
        }

        ArrayList<String> fieldNames = new ArrayList<String>();
        ArrayList<ObjectInspector> fieldOIs = new ArrayList<ObjectInspector>();
        //固定列
        fieldNames.add("keyno");
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        //动态列
        for (int i = 0; i < argOIs.length - 2; i++) {
            int num = i + 1;
            fieldNames.add(String.format("column%s", num));
            fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        }

        return ObjectInspectorFactory.getStandardStructObjectInspector(fieldNames, fieldOIs);
    }

    /**
     * 数据处理
     *
     * @param objects
     * @throws HiveException
     */
    @Override
    public void process(Object[] objects) throws HiveException {
        List<String> messyCodeInfoList = new ArrayList<>();
        try {
            // 固定字段
            messyCodeInfoList.add(objects[0].toString());
            //特殊符号
            String code = objects[1].toString();
            //动态列
            MessyCodeModelEntity model = new MessyCodeModelEntity();
            for (int i = 2; i < objects.length; i++) {
                model = new MessyCodeModelEntity();
                model.setContent(objects[i].toString());
                model.setNum(getMessyCodeNum(objects[i].toString(), code));
                messyCodeInfoList.add(JSONObject.toJSONString(model));
            }
            forward(messyCodeInfoList);
        } catch (Exception ex) {
        }
    }

    @Override
    public void close() throws HiveException {

    }

    /**
     * 获取乱码数量
     *
     * @param str  原文
     * @param code 特殊符号
     * @return 乱码个数
     */
    public static Integer getMessyCodeNum(String str, String code) {
        if (StringUtils.isEmpty(str)) {
            return 0;
        }

        Pattern p = Pattern.compile("\\s*\\t*\\r*\\n*");
        Matcher m = p.matcher(str);
        String after = m.replaceAll("").replaceAll("[a-zA-z]", "")
                .replaceAll("[\\d]", "")
                .replaceAll("[\\$|<|>|+|=|/|×|±|÷|﹣|／|≈||≠|∽|≌|≤|≥|─|°|№|○|^|*|@|~|`|∧|∕|﹤|﹥|﹦|━]", "")
                .replaceAll("[①|②|③|④|⑤|⑥|⑦|⑧|⑨|⑩|⑪|⑫|⑬|⑭|⑮|⑯|⑰|⑱|⑲|⑳]", "")
                .replaceAll("[⑴|⑵|⑶|⑷|⑸|⑹|⑺|⑻|⑼|⑽|⑾|⑿|⒀|⒁|⒂|⒃|⒄|⒅|⒆|⒇]", "")
                .replaceAll("[Ⅰ|Ⅱ|Ⅲ|Ⅳ|Ⅴ|Ⅵ|Ⅶ|Ⅷ|Ⅸ|Ⅹ|Ⅺ|Ⅻ|ⅰ|ⅱ|ⅲ|ⅳ|ⅴ|ⅵ|ⅶ|ⅷ|ⅸ|ⅹ]", "")
                .replaceAll("[㈠|㈡|㈢|㈣|㈤|㈥|㈦|㈧|㈨|㈩]", "")
                .replaceAll("[⒈|⒉|⒊|⒋|⒌|⒍|⒎|⒏|⒐|⒑]", "")
                .replaceAll("[㎡|㎥|‰|℃|㎏]", "")
                .replaceAll("[®|❤|★| |™|®]", "")
                .replaceAll("[\\p{Cs}]", "");
        // 额外的过滤字符
        if (StringUtils.notEmpty(code)) {
            code = code.replace(",", "|");
            after = after.replaceAll(String.format("[%s]", code), "");
        }

        String temp = after.replaceAll("\\p{P}", "");
        char[] ch = temp.trim().toCharArray();
        Integer count = 0;
        for (char c : ch) {
            if (!Character.isLetterOrDigit(c)) {
                if (!isChinese(c)) {
                    count += 1;
                }
            }
        }

        return count;
    }

    /**
     * 判断是否为中文
     *
     * @param c
     * @return
     */
    private static boolean isChinese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        return ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
                || ub == Character.UnicodeBlock.GENERAL_PUNCTUATION
                || ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION
                || ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS;
    }
}



