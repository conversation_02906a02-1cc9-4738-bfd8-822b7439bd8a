package com.qcc.udf.cpws.casesearch_v2;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: liulh
 * @Date: 2020/6/11 17:54
 * @Description:
 */
public class mergeCaseSearch extends UDF {
    public static String evaluate(String zxInfo, String msInfo) {
        JSONObject jsonObject = new JSONObject();

        // 传入参数为空，则返回空json
        if (StringUtils.isEmpty(zxInfo) && StringUtils.isEmpty(msInfo)){
            return jsonObject.toString();
        }
        // 不为空，则数据合并
        if (!StringUtils.isEmpty(zxInfo) && StringUtils.isEmpty(msInfo)){
            jsonObject = JSONObject.parseObject(zxInfo);
        }else if (!StringUtils.isEmpty(msInfo) && StringUtils.isEmpty(zxInfo)){
            jsonObject = JSONObject.parseObject(msInfo);
        }else{
            // 数据合并
            JSONObject zxJson = JSONObject.parseObject(zxInfo);
            JSONObject msJson = JSONObject.parseObject(msInfo);

            // infoList信息及各个cnt信息直接合并或者相加
            JSONArray infoListZx = zxJson.getJSONArray("InfoList");
            JSONArray infoListMs = msJson.getJSONArray("InfoList");

            jsonObject.put("InfoList", infoListMs);
            jsonObject.put("KtggCnt", zxJson.getIntValue("KtggCnt") + msJson.getIntValue("KtggCnt"));
            jsonObject.put("ZxCnt", zxJson.getIntValue("ZxCnt") + msJson.getIntValue("ZxCnt"));
            jsonObject.put("XjpgCnt", zxJson.getIntValue("XjpgCnt") + msJson.getIntValue("XjpgCnt"));
            jsonObject.put("CfgsCnt", zxJson.getIntValue("CfgsCnt") + msJson.getIntValue("CfgsCnt"));
            jsonObject.put("AnnoCnt", zxJson.getIntValue("AnnoCnt") + msJson.getIntValue("AnnoCnt"));
            jsonObject.put("GqdjCnt", zxJson.getIntValue("GqdjCnt") + msJson.getIntValue("GqdjCnt"));
            jsonObject.put("XgCnt", zxJson.getIntValue("XgCnt") + msJson.getIntValue("XgCnt"));
            jsonObject.put("FyggCnt", zxJson.getIntValue("FyggCnt") + msJson.getIntValue("FyggCnt"));
            jsonObject.put("ZbCnt", zxJson.getIntValue("ZbCnt") + msJson.getIntValue("ZbCnt"));
            jsonObject.put("CfdfCnt", zxJson.getIntValue("CfdfCnt") + msJson.getIntValue("CfdfCnt"));
            jsonObject.put("CfxyCnt", zxJson.getIntValue("CfxyCnt") + msJson.getIntValue("CfxyCnt"));
            jsonObject.put("SxCnt", zxJson.getIntValue("SxCnt") + msJson.getIntValue("SxCnt"));
            jsonObject.put("LianCnt", zxJson.getIntValue("LianCnt") + msJson.getIntValue("LianCnt"));
            jsonObject.put("CaseCnt", zxJson.getIntValue("CaseCnt") + msJson.getIntValue("CaseCnt"));
            jsonObject.put("PcczCnt", zxJson.getIntValue("PcczCnt") + msJson.getIntValue("PcczCnt"));
            jsonObject.put("HbcfCnt", zxJson.getIntValue("HbcfCnt") + msJson.getIntValue("HbcfCnt"));
            jsonObject.put("SdggCnt", zxJson.getIntValue("SdggCnt") + msJson.getIntValue("SdggCnt"));

            // CompanyKeywords
            jsonObject.put("CompanyKeywords", mergeItem(zxJson.getString("CompanyKeywords"), msJson.getString("CompanyKeywords")));
            // AnNoList
            jsonObject.put("AnNoList", mergeItem(zxJson.getString("AnNoList"), msJson.getString("AnNoList")));
            // GroupCourt
            jsonObject.put("GroupCourt", mergeItem(zxJson.getString("GroupCourt"), msJson.getString("GroupCourt")));
            // ProcuratorateList
            jsonObject.put("ProcuratorateList", mergeItem(zxJson.getString("ProcuratorateList"), msJson.getString("ProcuratorateList")));
            // CourtList
            jsonObject.put("CourtList", mergeItem(zxJson.getString("CourtList"), msJson.getString("CourtList")));
            // CaseType
            jsonObject.put("CaseType", mergeItem(zxJson.getString("CaseType"), msJson.getString("CaseType")));
            // Province
            jsonObject.put("Province", mergeItem(zxJson.getString("Province"), msJson.getString("Province")));
            // CaseName
            jsonObject.put("CaseName", msJson.getString("CaseName"));
            // CaseRole
            jsonObject.put("CaseRole", msJson.getString("CaseRole"));
            // CaseReason
            jsonObject.put("CaseReason", msJson.getString("CaseReason"));

            sortList(jsonObject, infoListZx, infoListMs);

            compareAllRound(jsonObject);

        }

        return jsonObject.toString();
    }

    public static String mergeItem(String param1, String param2){
        String result = "";
        String[] companykeywordsZx = param1.split(",");
        String[] companykeywordsMs = param2.split(",");
        Set<String> keywordSet = new LinkedHashSet<>();
        for (String str : companykeywordsZx){
            keywordSet.add(str.replace(" ", "").replace("　", ""));
        }
        for (String str : companykeywordsMs){
            keywordSet.add(str.replace(" ", "").replace("　", ""));
        }
        for (String str : keywordSet){
            result = result.concat(",").concat(str);
        }
        result = result.length() > 0 ? result.substring(1) : result;

        return result;
    }

    public static void compareAllRound(JSONObject jsonObject){
        JSONArray infoList = jsonObject.getJSONArray("InfoList");
        String latestTrialRound = "";
        String lastestDateType = "";
        String earliestDateType = "";
        long lastestDate = 0;
        long earliestDate = 0;

        latestTrialRound = CommonV2Util.getLatestTrialRoundFromInfoList(infoList);

        Map<Long, String> trialRoundDateNodeMap = new HashMap<>();
        Iterator<Object> it = infoList.iterator();
        while (it.hasNext()){
            JSONObject json = (JSONObject) it.next();
            String anNo = json.getString("AnNo");
            getInfoDate(json.getJSONArray("SxList"), 1, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("ZxList"), 2, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("XgList"), 3, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("CaseList"), 4, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("PcczList"), 5, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("ZbList"), 6, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("XjpgList"), 7, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("GqdjList"), 8, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("SdggList"), 9, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("FyggList"), 10, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("KtggList"), 11, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("LianList"), 12, anNo, trialRoundDateNodeMap);
        }

        Map<Long, String> sortedDateNodeMap = new LinkedHashMap<>();
        trialRoundDateNodeMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEachOrdered(e -> sortedDateNodeMap.put(e.getKey(), e.getValue()));

        boolean flag = true;
        for (Map.Entry<Long, String> sortedDateNodeEntry : sortedDateNodeMap.entrySet()) {
            if (flag) {
                earliestDate = sortedDateNodeEntry.getKey();
                earliestDateType = sortedDateNodeEntry.getValue();
                flag = false;
            }
            lastestDate = sortedDateNodeEntry.getKey();
            lastestDateType = sortedDateNodeEntry.getValue();
        }

        jsonObject.put("EarliestDate", earliestDate);
        jsonObject.put("EarliestDateType", CommonV2Util.getDataTypeWithoutTrialRound(earliestDateType));
        if (sortedDateNodeMap.size() > 1) {
            jsonObject.put("LastestDateType", CommonV2Util.getDataTypeWithoutTrialRound(lastestDateType));
            jsonObject.put("LastestDate", lastestDate);
        }
        jsonObject.put("LatestTrialRound", latestTrialRound);
    }

    public static void getInfoDate(JSONArray array, int type, String anNo, Map<Long, String> trialRoundDateNodeMap){
        if (array != null && !array.isEmpty()){
            Iterator<Object> itWd = array.iterator();
            while (itWd.hasNext()){
                JSONObject itemJson = (JSONObject)itWd.next();
                GetDetailInfoZXV2UDF.editItemJsonConn(itemJson, type, trialRoundDateNodeMap, anNo);
            }
        }
    }

    public static void sortList(JSONObject jsonObject, JSONArray infoListZx, JSONArray infoListMs){
        // 获取时间
        List<JSONObject> infoList = new LinkedList<>();
        getAllListInfo(infoListMs, 1, infoList);
        getAllListInfo(infoListZx, 2, infoList);

        // 排序
        Collections.sort(infoList, (a1, a2)->{
            int sort1 = a1.getLong("sort1").compareTo(a2.getLong("sort1"));
            if (sort1 == 0){
                return a1.getInteger("sort2").compareTo(a2.getInteger("sort2"));
            }
            return sort1;
        });

        JSONArray newArray = new JSONArray();
        for (JSONObject jsonObject1 : infoList){
            jsonObject1.remove("sort1");
            jsonObject1.remove("sort2");
            newArray.add(jsonObject1);
        }

        jsonObject.put("InfoList", newArray);
    }

    public static void getAllListInfo(JSONArray array, int type, List<JSONObject> infoList){
        Iterator<Object> it = array.iterator();
        while (it.hasNext()){
            Map<Long, String> trialRoundDateNodeMap = new HashMap<>();
            JSONObject json = (JSONObject) it.next();
            String anNo = json.getString("AnNo");
            getInfoDate(json.getJSONArray("SxList"), 1, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("ZxList"), 2, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("XgList"), 3, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("CaseList"), 4, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("PcczList"), 5, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("ZbList"), 6, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("XjpgList"), 7, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("GqdjList"), 8, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("SdggList"), 9, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("FyggList"), 10, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("KtggList"), 11, anNo, trialRoundDateNodeMap);
            getInfoDate(json.getJSONArray("LianList"), 12, anNo, trialRoundDateNodeMap);

            Set<Long> timeSet = trialRoundDateNodeMap.keySet();
            long firstTime = 0;
            int idx = 0;
            for (Long item : timeSet){
                if (idx == 0){
                    firstTime = item.longValue();
                }else{
                    if (item.longValue() < firstTime){
                        firstTime = item.longValue();
                    }
                }
                idx++;
            }
            json.put("sort1", firstTime);
            json.put("sort2", type);

            infoList.add(json);
        }
    }

    public static void main(String[] args) {
        System.out.println(evaluate("{\"KtggCnt\":0,\"ZxCnt\":0,\"XjpgCnt\":0,\"LastestDateType\":\"民事二审|判决日期\",\"CfgsCnt\":0,\"LastestDate\":1523808000,\"EarliestDate\":1511193600,\"AnnoCnt\":2,\"EarliestDateType\":\"民事一审|判决日期\",\"CompanyKeywords\":\"1e3de703634a01fc7a2d439672eb44be,李忠平,黄平县粮食购销有限责任公司\",\"AnNoList\":\"（2017）黔2622民初979号,（2018）黔26民终174号\",\"GqdjCnt\":0,\"GroupCourt\":\"贵州省黔东南苗族侗族自治州中级人民法院\",\"XgCnt\":0,\"FyggCnt\":0,\"ZbCnt\":0,\"LatestTrialRound\":\"民事二审\",\"CfdfCnt\":0,\"CfxyCnt\":0,\"CaseName\":\"黄平县粮食购销有限责任公司与李忠平排除妨碍纠纷\",\"SxCnt\":0,\"Province\":\"GZ\",\"LianCnt\":0,\"CaseCnt\":2,\"PcczCnt\":0,\"HbcfCnt\":0,\"CaseType\":\"民事案件\",\"ProcuratorateList\":\"\",\"CaseRole\":\"[{\\\"P\\\":\\\"黄平县粮食购销有限责任公司\\\",\\\"R\\\":\\\"原告\\\",\\\"N\\\":\\\"1e3de703634a01fc7a2d439672eb44be\\\",\\\"O\\\":0},{\\\"P\\\":\\\"贵州福万律师事务所\\\",\\\"R\\\":\\\"代理律师事务所\\\",\\\"N\\\":\\\"wa2429e0b2cefd5dbf9a6e2670970b33\\\",\\\"O\\\":4},{\\\"P\\\":\\\"李忠平\\\",\\\"R\\\":\\\"被告\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2}]\",\"CaseReason\":\"排除妨碍纠纷\",\"CourtList\":\"黄平县人民法院,贵州省黔东南苗族侗族自治州中级人民法院\",\"InfoList\":[{\"Defendant\":[{\"Role\":\"被告\",\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"李忠平\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"黄平县人民法院\",\"ZxList\":[],\"LatestTimestamp\":1511193600,\"HbcfList\":[],\"CfdfList\":[],\"CaseList\":[{\"JudgeDate\":1511193600,\"Id\":\"19d355477a3879689ac4d2ef4881e4200\",\"DocType\":\"民事判决日期\",\"IsValid\":1}],\"TrialRound\":\"民事一审\",\"Prosecutor\":[{\"Role\":\"原告\",\"KeyNo\":\"1e3de703634a01fc7a2d439672eb44be\",\"Org\":0,\"Name\":\"黄平县粮食购销有限责任公司\"}],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2017）黔2622民初979号\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"排除妨碍纠纷\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]},{\"Defendant\":[{\"Role\":\"被上诉人（原审原告）\",\"KeyNo\":\"1e3de703634a01fc7a2d439672eb44be\",\"Org\":0,\"Name\":\"黄平县粮食购销有限责任公司\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"贵州省黔东南苗族侗族自治州中级人民法院\",\"ZxList\":[],\"LatestTimestamp\":1523808000,\"HbcfList\":[],\"CfdfList\":[],\"CaseList\":[{\"JudgeDate\":1523808000,\"Id\":\"52e7d87062778588f9df98a1545e3bd90\",\"DocType\":\"民事判决日期\",\"IsValid\":1}],\"TrialRound\":\"民事二审\",\"Prosecutor\":[{\"Role\":\"上诉人（原审被告）\",\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"李忠平\"}],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2018）黔26民终174号\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"排除妨碍纠纷\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]}],\"SdggCnt\":0} ",
                "{\"KtggCnt\":0,\"ZxCnt\":0,\"XjpgCnt\":0,\"LastestDateType\":\"民事二审|判决日期\",\"CfgsCnt\":0,\"LastestDate\":1523808000,\"EarliestDate\":1511193600,\"AnnoCnt\":2,\"EarliestDateType\":\"民事一审|判决日期\",\"CompanyKeywords\":\"1e3de703634a01fc7a2d439672eb44be,李忠平,黄平县粮食购销有限责任公司\",\"AnNoList\":\"（2017）黔2622民初979号,（2018）黔26民终174号\",\"GqdjCnt\":0,\"GroupCourt\":\"贵州省黔东南苗族侗族自治州中级人民法院\",\"XgCnt\":0,\"FyggCnt\":0,\"ZbCnt\":0,\"LatestTrialRound\":\"民事二审\",\"CfdfCnt\":0,\"CfxyCnt\":0,\"CaseName\":\"黄平县粮食购销有限责任公司与李忠平排除妨碍纠纷\",\"SxCnt\":0,\"Province\":\"GZ\",\"LianCnt\":0,\"CaseCnt\":2,\"PcczCnt\":0,\"HbcfCnt\":0,\"CaseType\":\"民事案件\",\"ProcuratorateList\":\"\",\"CaseRole\":\"[{\\\"P\\\":\\\"黄平县粮食购销有限责任公司\\\",\\\"R\\\":\\\"原告\\\",\\\"N\\\":\\\"1e3de703634a01fc7a2d439672eb44be\\\",\\\"O\\\":0},{\\\"P\\\":\\\"贵州福万律师事务所\\\",\\\"R\\\":\\\"代理律师事务所\\\",\\\"N\\\":\\\"wa2429e0b2cefd5dbf9a6e2670970b33\\\",\\\"O\\\":4},{\\\"P\\\":\\\"李忠平\\\",\\\"R\\\":\\\"被告\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2}]\",\"CaseReason\":\"排除妨碍纠纷\",\"CourtList\":\"黄平县人民法院,贵州省黔东南苗族侗族自治州中级人民法院\",\"InfoList\":[{\"Defendant\":[{\"Role\":\"被告\",\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"李忠平\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"黄平县人民法院\",\"ZxList\":[],\"LatestTimestamp\":1511193600,\"HbcfList\":[],\"CfdfList\":[],\"CaseList\":[{\"JudgeDate\":1511193600,\"Id\":\"19d355477a3879689ac4d2ef4881e4200\",\"DocType\":\"民事判决日期\",\"IsValid\":1}],\"TrialRound\":\"民事一审\",\"Prosecutor\":[{\"Role\":\"原告\",\"KeyNo\":\"1e3de703634a01fc7a2d439672eb44be\",\"Org\":0,\"Name\":\"黄平县粮食购销有限责任公司\"}],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2017）黔2622民初979号\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"排除妨碍纠纷\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]},{\"Defendant\":[{\"Role\":\"被上诉人（原审原告）\",\"KeyNo\":\"1e3de703634a01fc7a2d439672eb44be\",\"Org\":0,\"Name\":\"黄平县粮食购销有限责任公司\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"贵州省黔东南苗族侗族自治州中级人民法院\",\"ZxList\":[],\"LatestTimestamp\":1523808000,\"HbcfList\":[],\"CfdfList\":[],\"CaseList\":[{\"JudgeDate\":1523808000,\"Id\":\"52e7d87062778588f9df98a1545e3bd90\",\"DocType\":\"民事判决日期\",\"IsValid\":1}],\"TrialRound\":\"民事二审\",\"Prosecutor\":[{\"Role\":\"上诉人（原审被告）\",\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"李忠平\"}],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2018）黔26民终174号\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"排除妨碍纠纷\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]}],\"SdggCnt\":0} "));
    }

}
