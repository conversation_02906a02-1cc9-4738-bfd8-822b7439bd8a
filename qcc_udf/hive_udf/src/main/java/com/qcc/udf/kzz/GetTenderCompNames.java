package com.qcc.udf.kzz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 招投标解析公司名称
 * <AUTHOR>
 * @date 2022/4/1
 */
public class GetTenderCompNames  extends UDF {
    public static String evaluate(String content) {
        List<String> result = new ArrayList<>();
        try{
            if(StringUtils.isNotBlank(content) && StringUtils.length(content) > 5){
                JSONArray jsonArray = JSON.parseArray(content);
                if(!jsonArray.isEmpty()){
                    for (Object obj : jsonArray) {
                        JSONObject json = (JSONObject)obj;
                        String name = json.getString("Name");
                        if(StringUtils.isNotBlank(name)){
                            result.add(name);
                        }
                    }
                }
            }
        }catch (Exception e){

        }
        return result.stream().collect(Collectors.joining(","));
    }
//    public static void main(String[] args){
//        String content ="[{\"KeyNo\":\"g563d9a500ac2234744d664ed0fdc267\",\"Name\":\"广西壮族自治区桂林冶金疗养院\",\"Org\":4}]\n" +
//                "\n";
//        String tels =  evaluate(content);
//        System.out.println(tels);
//    }
}
