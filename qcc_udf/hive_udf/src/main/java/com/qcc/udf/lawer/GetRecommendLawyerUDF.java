package com.qcc.udf.lawer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import lombok.Data;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;
import java.util.*;

/**
 * 获取推荐律师维度信息
 *
 * <AUTHOR>
 * @date 2021年10月08日 14:47
 */
public class GetRecommendLawyerUDF extends UDF {
    public static String evaluate(String item) {
        //named_struct('id',id,'lawername',lawername,'courtname',courtname,'case_reason_all',case_reason_all,'casecnt',casecnt,'casereasoncnt',casereasoncnt,'courtcnt',courtcnt)) as detail  from

        JSONObject json = JSON.parseObject(item);
        String id = json.getString("id");
        String lawername = json.getString("lawername");
        String courtname = json.getString("courtname");
        String case_reason_all = json.getString("case_reason_all");
        Integer casecnt = json.getInteger("casecnt");
        String casereasoncnt = json.getString("casereasoncnt");
        String courtcnt = json.getString("courtcnt");
        String casetypecnt = json.getString("casetypecnt");


        //计算案由匹配度
        List<TmpRecommendCnt> caseReasonJsonList = JSON.parseArray(casereasoncnt, TmpRecommendCnt.class);
        int matchCaseReasonCount = calCaseReasonRate(case_reason_all, caseReasonJsonList);
        BigDecimal caseReasonRate = new BigDecimal(matchCaseReasonCount).divide(new BigDecimal(casecnt), 2
                , BigDecimal.ROUND_HALF_UP);
        //count≥100即满分，200以下按照0-100的比例进行算分（如160对应80分），若低于10分则取10分
        int caseReasonScore = calScore(matchCaseReasonCount, 100, 10);

        //执行案件办案数量
        List<TmpRecommendCnt> casetypecntJsonList = JSON.parseArray(casetypecnt, TmpRecommendCnt.class);
        if(casetypecntJsonList == null) casetypecntJsonList = new ArrayList<>();
        int zxCount = casetypecntJsonList.stream().filter(x -> x.getName().contains("6")).mapToInt(TmpRecommendCnt::getCnt).sum();
        BigDecimal zxRate = new BigDecimal(zxCount).divide(new BigDecimal(casecnt), 2, BigDecimal.ROUND_HALF_UP);
        //count≥200即满分，200以下按照0-200的比例进行算分（如160对应80分），若低于10分则取10分
        int zxScore = calScore(zxCount, 200, 10);

        //法院匹配数量
        List<TmpRecommendCnt> courtJsonList = JSON.parseArray(courtcnt, TmpRecommendCnt.class);
        int matchCourtCount = calCaseReasonRate(courtname, courtJsonList);
        BigDecimal courtRate = new BigDecimal(matchCourtCount).divide(new BigDecimal(casecnt), 2
                , BigDecimal.ROUND_HALF_UP);
        //count≥100即满分，200以下按照0-100的比例进行算分（如160对应80分），若低于10分则取10分
        int courtScore = calScore(matchCourtCount, 100, 10);

        //count≥500即满分，500以下按照0-500的比例进行算分（如400对应80分），若低于10分则取10分
        int caseScore = calScore(casecnt, 500, 10);


        Map<String, Object> lawInfo = new HashMap<>();
        lawInfo.put("id", id);
        lawInfo.put("lawername", lawername);
        lawInfo.put("courtname", courtname);
        lawInfo.put("caseReasonAll", case_reason_all);
        lawInfo.put("caseReasonCount", matchCaseReasonCount);
        lawInfo.put("caseReasonRate", caseReasonRate);
        lawInfo.put("caseReasonScore", caseReasonScore);
        lawInfo.put("caseCnt", casecnt);
        lawInfo.put("caseScore", caseScore);
        lawInfo.put("zxCount", zxCount);
        lawInfo.put("zxRate", zxRate);
        lawInfo.put("zxScore", zxScore);
        lawInfo.put("courtCount", matchCourtCount);
        lawInfo.put("courtRate", courtRate);
        lawInfo.put("courtScore", courtScore);
        return JSON.toJSONString(lawInfo);
    }

    static int calScore(int currentCount, int maxCount, int defaultScore) {
        int resultScore;
        if (currentCount >= 200) {
            resultScore = 100;
        } else {
            BigDecimal score = new BigDecimal(currentCount).divide(new BigDecimal(maxCount), 2
                    , BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
            resultScore = score.intValue();
        }
        if (resultScore < defaultScore) {
            resultScore = defaultScore;
        }

        return resultScore;
    }

    static int calCaseReasonRate(String allCaseReason, List<TmpRecommendCnt> reasonList) {
        Set<String> allReasonSet = Sets.newHashSet(allCaseReason.split(","));
        int matchCount = 0;
        for (TmpRecommendCnt reason : reasonList) {
            if (allReasonSet.contains(reason.getName())) {
                matchCount = matchCount + reason.getCnt();
            }
        }
        return matchCount;
    }

    public static void main(String[] args) {

        String json = "{\"id\":\"70d322ff51e4efdbf74949821f7f8579\",\"lawername\":\"安进启\",\"courtname\":\"宁夏回族自治区中卫市沙坡头区人民法院\",\"case_reason_all\":\"盗窃罪\",\"casecnt\":\"95\",\"casereasoncnt\":\"[{\\\"name\\\":\\\"劳动争议\\\",\\\"cnt\\\":5,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"合同纠纷\\\",\\\"cnt\\\":4,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"股权转让纠纷\\\",\\\"cnt\\\":4,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"房屋买卖合同纠纷\\\",\\\"cnt\\\":4,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"建筑设备租赁合同纠纷\\\",\\\"cnt\\\":3,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"排除妨害纠纷\\\",\\\"cnt\\\":3,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"执行异议之诉\\\",\\\"cnt\\\":3,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"建设工程施工合同纠纷\\\",\\\"cnt\\\":3,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"抢劫罪\\\",\\\"cnt\\\":2,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"运输合同纠纷\\\",\\\"cnt\\\":2,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"机动车交通事故责任纠纷\\\",\\\"cnt\\\":2,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"盗窃罪\\\",\\\"cnt\\\":2,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"物业服务合同纠纷\\\",\\\"cnt\\\":2,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"提供劳务者受害责任纠纷\\\",\\\"cnt\\\":2,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"故意杀人罪\\\",\\\"cnt\\\":2,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"买卖合同纠纷\\\",\\\"cnt\\\":14,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"民间借贷纠纷\\\",\\\"cnt\\\":12,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"损害公司利益责任纠纷\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"其他（公安）\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"房屋租赁合同纠纷\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"诈骗罪\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"法定继承纠纷\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"申请执行人执行异议之诉\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"储蓄存款合同纠纷\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"农村房屋买卖合同纠纷\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"租赁合同纠纷\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"合伙协议纠纷\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"担保物权确认纠纷\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"非法拘禁罪\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"单位行贿罪\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"追偿权纠纷\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"物权确认纠纷\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"生命权、健康权、身体权纠纷\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"物权纠纷\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"土地承包经营权纠纷\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"强迫卖淫罪\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"受贿罪\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"寻衅滋事罪\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"同居关系析产纠纷\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"虚开增值税专用发票、用于骗取出口退税、抵扣税款发票罪\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"其他行政行为\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"返还原物纠纷\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"走私、贩卖、运输、制造毒品罪\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"}]\",\"courtcnt\":\"[{\\\"name\\\":\\\"宁夏回族自治区银川市永宁县人民法院\\\",\\\"cnt\\\":100,\\\"casesearchids\\\":\\\"\\\",\\\"code\\\":\\\"640121\\\"},{\\\"name\\\":\\\"宁夏回族自治区高级人民法院\\\",\\\"cnt\\\":6,\\\"casesearchids\\\":\\\"\\\",\\\"code\\\":\\\"640000\\\"},{\\\"name\\\":\\\"宁夏回族自治区银川市中级人民法院\\\",\\\"cnt\\\":38,\\\"casesearchids\\\":\\\"\\\",\\\"code\\\":\\\"640100\\\"},{\\\"name\\\":\\\"宁夏回族自治区银川市兴庆区人民法院\\\",\\\"cnt\\\":30,\\\"casesearchids\\\":\\\"\\\",\\\"code\\\":\\\"640104\\\"},{\\\"name\\\":\\\"宁夏回族自治区银川市西夏区人民法院\\\",\\\"cnt\\\":21,\\\"casesearchids\\\":\\\"\\\",\\\"code\\\":\\\"640105\\\"},{\\\"name\\\":\\\"宁夏回族自治区中卫市中级人民法院\\\",\\\"cnt\\\":2,\\\"casesearchids\\\":\\\"\\\",\\\"code\\\":\\\"640500\\\"},{\\\"name\\\":\\\"宁夏回族自治区中卫市沙坡头区人民法院\\\",\\\"cnt\\\":80,\\\"casesearchids\\\":\\\"\\\",\\\"code\\\":\\\"640502\\\"},{\\\"name\\\":\\\"宁夏回族自治区银川市贺兰县人民法院\\\",\\\"cnt\\\":2,\\\"casesearchids\\\":\\\"\\\",\\\"code\\\":\\\"640122\\\"},{\\\"name\\\":\\\"山东省菏泽市单县人民法院\\\",\\\"cnt\\\":2,\\\"casesearchids\\\":\\\"\\\",\\\"code\\\":\\\"371722\\\"},{\\\"name\\\":\\\"宁夏回族自治区银川市灵武市人民法院\\\",\\\"cnt\\\":2,\\\"casesearchids\\\":\\\"\\\",\\\"code\\\":\\\"640181\\\"},{\\\"name\\\":\\\"宁夏回族自治区吴忠市青铜峡市人民法院\\\",\\\"cnt\\\":2,\\\"casesearchids\\\":\\\"\\\",\\\"code\\\":\\\"640381\\\"},{\\\"name\\\":\\\"宁夏回族自治区银川市金凤区人民法院\\\",\\\"cnt\\\":17,\\\"casesearchids\\\":\\\"\\\",\\\"code\\\":\\\"640106\\\"},{\\\"name\\\":\\\"宁夏回族自治区吴忠市中级人民法院\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\",\\\"code\\\":\\\"640300\\\"},{\\\"name\\\":\\\"宁夏回族自治区石嘴山市中级人民法院\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\",\\\"code\\\":\\\"640200\\\"},{\\\"name\\\":\\\"辽宁省大连市中级人民法院\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\",\\\"code\\\":\\\"210200\\\"},{\\\"name\\\":\\\"甘肃省兰州市中级人民法院\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\",\\\"code\\\":\\\"620100\\\"},{\\\"name\\\":\\\"甘肃省兰州市城关区人民法院\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\",\\\"code\\\":\\\"620102\\\"},{\\\"name\\\":\\\"辽宁省大连市金州区人民法院\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\",\\\"code\\\":\\\"210213\\\"},{\\\"name\\\":\\\"宁夏回族自治区石嘴山市平罗县人民法院\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\",\\\"code\\\":\\\"640221\\\"},{\\\"name\\\":\\\"河南省郑州市新密市人民法院\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\",\\\"code\\\":\\\"410183\\\"},{\\\"name\\\":\\\"宁夏回族自治区固原市中级人民法院\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\",\\\"code\\\":\\\"640400\\\"}]\",\"casetypecnt\":\"[{\\\"name\\\":\\\"2_6\\\",\\\"cnt\\\":7,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"2\\\",\\\"cnt\\\":7,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"1\\\",\\\"cnt\\\":41,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"1_6\\\",\\\"cnt\\\":36,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"3\\\",\\\"cnt\\\":2,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"1_4\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"},{\\\"name\\\":\\\"1_4_6\\\",\\\"cnt\\\":1,\\\"casesearchids\\\":\\\"\\\"}]\"}";

        System.out.println(evaluate(json));

//        System.out.println(list);

    }
}

@Data
class TmpRecommendCnt {
    private String name;
    private int cnt;
}
