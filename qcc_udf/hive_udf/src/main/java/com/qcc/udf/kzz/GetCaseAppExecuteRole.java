package com.qcc.udf.kzz;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 提取司法案件申请人,被执行人
 * <AUTHOR>
 * @date 2022/4/19
 */
public class GetCaseAppExecuteRole extends UDF {
    /**
     *
     * @param caseRoleSearch
     * @param type 申请执行人,被执行人
     * @return
     */
    public static String evaluate(String caseRoleSearch, String type) {
        String result = "";
        try {
            if (StringUtils.isNotBlank(caseRoleSearch)) {
                JSONArray array = JSONArray.parseArray(caseRoleSearch);
                List<String> keyNos =new ArrayList<>();
                if (!array.isEmpty()) {
                    for (Object obj : array) {
                        JSONObject jsonObject = (JSONObject) obj;
                        //公司keyno
                        String keyNo = jsonObject.getString("N");
                        //角色
                        String role = jsonObject.getString("R");
                        if(StringUtils.isNotBlank(role) && type.equals(role)){
                            if(StringUtils.isNotBlank(keyNo) && !keyNo.startsWith("p")){
                                keyNos.add(keyNo);
                            }
                        }
                    }
                }
                if(CollectionUtils.isNotEmpty(keyNos)){
                    result = keyNos.stream().collect(Collectors.joining(","));
                }
            }

        } catch (Exception e) {

        }
        return result;
    }
//    public static void main(String[] args) {
//        String content = "[\n" +
//                "  {\n" +
//                "    \"P\": \"中国工商银行股份有限公司灵山县支行\",\n" +
//                "    \"R\": \"申请执行人\",\n" +
//                "    \"D\": \"首次执行申请执行人\",\n" +
//                "    \"RL\": [\n" +
//                "      {\n" +
//                "        \"R\": \"申请执行人\",\n" +
//                "        \"T\": \"首次执行\"\n" +
//                "      }\n" +
//                "    ],\n" +
//                "    \"N\": \"49fa6f1a80d8d5840f2b0073f4d4fbc3\",\n" +
//                "    \"O\": 0\n" +
//                "  },\n" +
//                "  {\n" +
//                "    \"P\": \"刘家燕\",\n" +
//                "    \"R\": \"被执行人\",\n" +
//                "    \"D\": \"首次执行被执行人\",\n" +
//                "    \"RL\": [\n" +
//                "      {\n" +
//                "        \"R\": \"被执行人\",\n" +
//                "        \"T\": \"首次执行\"\n" +
//                "      }\n" +
//                "    ],\n" +
//                "    \"N\": \"32523\",\n" +
//                "    \"O\": -2\n" +
//                "  }\n" +
//                "]";
//        String region = evaluate(content,"被执行人");
//        System.out.println(region);
//    }
}
