package com.qcc.udf.trade_mark;

import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDF;

public class TmEncoderUDF extends UDF{
    @Description(name="yyc_encoder",
            value = "_FUNC_(long id)" +
                    "to generate a new id consists of letters")
    private static int EncryptKey = 54367;

    public  String evaluate(long id ) {
        String str=String.valueOf(id * EncryptKey);
        String htext = "";
        for (int i = 0; i < str.length(); i++) {
            htext = htext + (char) (str.charAt(i) + 30 - 1 * 2);
        }
        return htext;
    }
}
