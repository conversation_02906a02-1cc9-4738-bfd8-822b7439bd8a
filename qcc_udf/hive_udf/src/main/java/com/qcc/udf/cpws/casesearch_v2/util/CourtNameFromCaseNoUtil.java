package com.qcc.udf.cpws.casesearch_v2.util;

import cn.hutool.core.util.ReUtil;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.LinkedHashMap;
import java.util.Map;

public class CourtNameFromCaseNoUtil {
    private final static Map<String, String> caseNoCourtNameMap;
    static {
        caseNoCourtNameMap = new LinkedHashMap<>();
        try {
            try (InputStream is = CourtNameFromCaseNoUtil.class.getResourceAsStream("/casenoToCourtNameMap.csv")) {
                BufferedReader br = new BufferedReader(new InputStreamReader(is));
                String line;
                while ((line = br.readLine()) != null) {
                    String[] splits = line.split("\t");
                    if (splits != null && splits.length == 2) {
                        String caseNoChar = splits[0].trim();
                        String courtName = splits[1].trim();
                        caseNoCourtNameMap.put(caseNoChar, courtName);
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public String evaluate(String caseNo) {
        String courtName = StringUtils.EMPTY;
        try {
            if (StringUtils.isNotBlank(caseNo)) {

                caseNo = SbcToDbc.convertSBCToDBC(caseNo);
                String rule ="[\\u4e00-\\u9fa5]\\d{0,4}";

                String caseNoChar = ReUtil.get(rule, caseNo, 0);
                courtName= caseNoCourtNameMap.get(caseNoChar);
            }
        } catch (Exception ex) {
        }
        return courtName;
    }

    public static void main(String[] args) {
        System.out.println(new CourtNameFromCaseNoUtil().evaluate("（2020）浙0521执24号"));
    }
}
