package com.qcc.udf.cpws.wjp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021年08月10日 19:31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CPWSLawyerFirmGroupInfo {
    @JSONField(name = "P")
    private String P;
    @JSONField(name = "N")
    private String N;
    @JSONField(name = "O")
    private int Org;

    @JSONField(name = "LY")
    private List<CPWSLawyerGroupInfo> LY;
}
