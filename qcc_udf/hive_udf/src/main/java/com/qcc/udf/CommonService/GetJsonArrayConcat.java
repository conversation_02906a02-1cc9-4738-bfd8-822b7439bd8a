package com.qcc.udf.CommonService;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;

public class GetJsonArrayConcat extends UDF {

//    public static void main(String[] args) throws Exception {
//        System.out.println(evaluate("[{\"ChangeDate\":{\"$numberLong\":\"1375113600\"},\"Name\":\"北京小米科技有限责任公司\"},{\"ChangeDate\":{\"$numberLong\":\"1375113600\"},\"Name\":\"北京小米科技有限责任公司1\"}]", "Name",",","北京小米科技有限责任公司"));
//    }

    /**
     * 获取json数据组的key值
     *
     * @param jsonArrayStr json字符串
     * @param jsonKey      json的key
     * @return 拼接后的key值
     * @throws Exception
     */
    public static String evaluate(String jsonArrayStr, String jsonKey) throws Exception {
        return evaluate(jsonArrayStr, jsonKey, "", "");
    }

    /**
     * 获取json数据组的key值
     *
     * @param jsonArrayStr json字符串
     * @param jsonKey      json的key
     * @param separation   分隔符
     * @return 拼接后的key值
     * @throws Exception
     */
    public static String evaluate(String jsonArrayStr, String jsonKey, String separation) throws Exception {
        return evaluate(jsonArrayStr, jsonKey, separation, "");
    }

    /**
     * 获取json数据组的key值
     *
     * @param jsonArrayStr json字符串
     * @param jsonKey      json的key
     * @param separation   分隔符
     * @param condition    筛选条件
     * @return 拼接后的key值
     * @throws Exception
     */
    public static String evaluate(String jsonArrayStr, String jsonKey, String separation, String condition) throws Exception {
        if (StringUtils.isBlank(jsonArrayStr)) {
            return null;
        }
        ArrayList<String> result = new ArrayList<>();
        JSONArray jsonArray = JSONArray.parseArray(jsonArrayStr);
        jsonArray.forEach(a -> {
            if (a != null) {
                JSONObject tObj = JSONObject.parseObject(a.toString());
                String tKeyNo = tObj.getString(jsonKey);

                if (StringUtils.isNotBlank(tKeyNo)) {
                    if (StringUtils.isBlank(condition) || tKeyNo.equals(condition))//条件不为空时，value值必须和条件相同
                        result.add(tKeyNo);
                }
            }
        });
        if (StringUtils.isBlank(separation)) separation = ",";
        return String.join(separation, result);
    }
}
