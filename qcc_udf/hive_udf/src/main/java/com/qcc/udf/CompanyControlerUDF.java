
package com.qcc.udf;
import com.google.gson.*;
import org.apache.hadoop.hive.ql.exec.UDF;
import org.apache.hadoop.io.Text;


public class CompanyControlerUDF extends UDF {

    public static Text evaluate(Text param) {
        String org = "";
        String keyno = "";
        String name = "";
        try
        {
            if (param != null){
                String s = param.toString();
                if (!"".equals(s)){
                    Gson gson = new Gson();
                    JsonObject jsonObject = gson.fromJson(s, JsonObject.class);
                    if (jsonObject.has("Names")) {
                        JsonArray namelist = jsonObject.getAsJsonArray("Names");
                        for (JsonElement item : namelist) {
                            JsonObject i = item.getAsJsonObject();
                            if (i.has("KeyNo")) keyno = i.get("KeyNo").getAsString();
                            if (i.has("Name")) name = i.get("Name").getAsString();
                            if (i.has("Paths")) {
                                JsonArray paths = i.getAsJsonArray("Paths");
                                for (JsonElement path:paths) {
                                    JsonArray onepath = path.getAsJsonArray();
                                    for (JsonElement a:onepath){
                                        JsonObject ttt = a.getAsJsonObject();
                                        if (ttt.has("KeyNo")) {
                                            String pathkeyno = ttt.get("KeyNo").getAsString();
                                            if (keyno.equals(pathkeyno)){
                                                org = ttt.get("Org").getAsString();
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }catch(JsonSyntaxException e1)
        {
            System.out.println("not a json");
        }
        JsonObject json = new JsonObject();
        json.addProperty("keyno", keyno);
        json.addProperty("name", name);
        json.addProperty("org", org);
        return new Text(json.toString());
    }

  /*  public static void main(String[] args) throws Exception {
        String s = "{\"KeyNo\":\"917720a16d08bdde171681983391aed0\",\"CompanyName\":\"恒大地产集团有限公司\",\"Names\":[{\"KeyNo\":\"od80b2eecedba3237ad3e9d79317510f\",\"Name\":\"安基(BVI)有限公司\",\"Percent\":\"\",\"PercentTotal\":\"63.46%\",\"Level\":\"3\",\"Paths\":[[{\"KeyNo\":\"f93c6a23cf326e025b16d58b7ce67fe6\",\"Name\":\"广州市凯隆置业有限公司\",\"Org\":\"0\",\"HasImage\":false,\"Percent\":\"63.46%\",\"PercentTotal\":\"63.46%\",\"Level\":1},{\"KeyNo\":\"3ff6d0134f2749f0261ac063c6fc41d9\",\"Name\":\"广州市超丰置业有限公司\",\"Org\":\"0\",\"HasImage\":false,\"Percent\":\"100%\",\"PercentTotal\":\"63.46%\",\"Level\":\"2\"},{\"KeyNo\":\"od80b2eecedba3237ad3e9d79317510f\",\"Name\":\"安基(BVI)有限公司\",\"Org\":\"8\",\"HasImage\":false,\"Percent\":\"100%\",\"PercentTotal\":\"63.46%\",\"Level\":\"3\"}]]}]}";
        Text tt = new Text(s);
        Text text = CompanyControlerUDF.evaluate(tt);
        System.out.println(text);
    }*/
}