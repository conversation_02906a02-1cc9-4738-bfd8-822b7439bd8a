package com.qcc.udf.casesearch_v3;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Strings;
import com.qcc.udf.casesearch_v3.entity.output.LawSuitV3OutputEntity;
import org.apache.commons.collections.CollectionUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class CaseV3InfoIdUDF extends UDF {
    public static String evaluate(String info){
        if(Strings.isNullOrEmpty(info)){
            return "";
        }
        Set<String> idSet = new HashSet<>();
        try {
            List<LawSuitV3OutputEntity> list = JSONArray.parseArray(info,LawSuitV3OutputEntity.class);
            if(CollectionUtils.isNotEmpty(list)){
                for (LawSuitV3OutputEntity item : list) {
                  if(!Strings.isNullOrEmpty(item.getId())){
                      idSet.add(item.getId());
                  }
                }
            }
        }catch (Exception e){

        }

        return idSet.stream().sorted().collect(Collectors.joining(","));
    }

    public static void main(String[] args) {
        System.out.println(evaluate("[{\"AmtInfo\":{},\"AnNoList\":\"（2012）行他字第17号\",\"AnnoCnt\":1,\"CaseCnt\":1,\"CaseName\":\"其他行政的案件\",\"CaseReason\":\"其他行政\",\"CaseRole\":\"[]\",\"CaseType\":\"行政案件\",\"CfdfCnt\":0,\"CfgsCnt\":0,\"CfxyCnt\":0,\"CompanyKeywords\":\"\",\"CourtList\":\"最高人民法院\",\"EarliestDate\":1375027200,\"EarliestDateType\":\"其他行政|答复日期\",\"FyggCnt\":0,\"GqdjCnt\":0,\"GroupId\":\"cfec8b2c50231db15ead28303f6aab6d\",\"HbcfCnt\":0,\"Id\":\"3f634f2149e550d6434188b94eb56433\",\"InfoList\":[{\"AnNo\":\"（2012）行他字第17号\",\"CaseList\":[{\"Amt\":\"\",\"CaseType\":\"行政答复\",\"DocType\":\"答复日期\",\"Id\":\"698ce0751045e586aac9f4ad9a4cf10d0\",\"IsValid\":1,\"JudgeDate\":1375027200,\"Result\":\"\",\"ResultType\":\"答复内容\"}],\"CaseReason\":\"其他行政\",\"CaseType\":\"行政案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"最高人民法院\",\"Defendant\":[],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1375027200,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"其他行政\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]}],\"KtggCnt\":0,\"LastestDate\":1375027200,\"LastestDateType\":\"其他行政|答复日期\",\"LatestTrialRound\":\"其他行政\",\"LianCnt\":0,\"PcczCnt\":0,\"ProcuratorateList\":\"\",\"Province\":\"BJ\",\"SdggCnt\":0,\"Source\":\"OT\",\"SxCnt\":0,\"Tags\":\"4\",\"Type\":1,\"XgCnt\":0,\"XjpgCnt\":0,\"XzcfCnt\":0,\"ZbCnt\":0,\"ZxCnt\":0}]"));
    }
}
