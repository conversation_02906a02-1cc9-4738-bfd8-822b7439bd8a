package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class EnterpriseTypeJudy extends UDF {

    /**
     * 验证企业类型
     * @param econKind:
     * @return
     * @throws Exception
     */
    public static String evaluate(String econKind) throws Exception {
        econKind = econKind == null ? "" : econKind;
        String enterpriseType = econKind;

        //正则表达式
        String regEx = ".*[非]+.*[独资]";
        Pattern pattern = Pattern.compile(regEx);
        Matcher matcher = pattern.matcher(enterpriseType);

        //判断逻辑
        if ((econKind.contains("个体") || econKind.contains("个人") || econKind.contains("家庭"))
                && !econKind.contains("企业")) {
            enterpriseType = "个体工商户";
        } else if (enterpriseType.contains("有限责任公司")) {
            enterpriseType = "有限责任公司";
        } else if (enterpriseType.contains("股份有限公司")) {
            enterpriseType = "股份有限公司";
        } else if (enterpriseType.contains("国有") && !enterpriseType.contains("非国有")) {
            enterpriseType = "国企";
        } else if (enterpriseType.contains("中外合作") || enterpriseType.contains("中外合资") || enterpriseType.contains("外国") || enterpriseType.contains("外商")) {
            enterpriseType = "外商投资企业";
        } else if ((enterpriseType.contains("独资") && !matcher.matches()) || enterpriseType.contains("一人有限责任公司")) {
            enterpriseType = "独资企业";
        } else if (enterpriseType.contains("有限合伙") || enterpriseType.contains("普通合伙") || enterpriseType.contains("合伙企业")) {
            enterpriseType = "合伙制企业";
        }

        return enterpriseType;
    }

/*    public static void main(String[] args){
        String econKind = "股份合作制";
        try {
            String rst = evaluate(econKind);
            System.out.println(rst);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }*/

}
