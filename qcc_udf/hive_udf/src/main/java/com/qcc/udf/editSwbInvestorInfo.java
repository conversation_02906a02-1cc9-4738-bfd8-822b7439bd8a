package com.qcc.udf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Iterator;

/**
 * @Auther: wb
 * @Date: 2019/3/8 11:48
 * @Description:
 */
public class editSwbInvestorInfo extends UDF {
    public static String evaluate(String param) {
        if (param == null || param.isEmpty()) {
            return "";
        }
        try {
            JSONObject result = new JSONObject();

            JSONArray array = JSONArray.parseArray(param);
            if (array == null || array.isEmpty() || array.size() == 0) {
                return "";
            }
            JSONArray array1 = new JSONArray();
            JSONArray array2 = new JSONArray();
            Iterator<Object> it = array.iterator();
            while (it.hasNext()) {
                JSONObject json = (JSONObject) it.next();
                JSONObject item = new JSONObject();

                item.put("country_name", (json.getString("country") == null ? "" : json.getString("country")));
                item.put("name_cn", (json.getString("investor_name") == null ? "" : json.getString("investor_name")));
                item.put("name_en", "");
                if (json.getString("country") == null || "".equals(json.getString("country")) || "中国".equals(json.getString("country"))) {
                    array1.add(item);
                } else {
                    array2.add(item);
                }
            }
            result.put("report2_1", array1);
            result.put("report2_2", array2);
            return result.toString();
        } catch (Exception ex) {
            return "";
        }
    }

}
