package com.qcc.udf.court_std;

import com.google.common.base.Strings;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2021年05月18日 9:58
 */
public class convertCourtAno extends UDF {
    public static String evaluate(String courtName, String anNo) {
        if (!Strings.isNullOrEmpty(courtName)) {
            String name = full2Half(courtName).replaceAll(" | ", "").replace("(", "（").replace(")", "）");
            name = name.replaceAll("(（\\d{4}）\\S*号(之\\\\S{1,2})?)", "");
            name = name.replaceAll("(\\S*年\\S*月\\S*日)", "");
            name = name.replaceAll("[0-9a-zA-Z]", "");
            name = name.replaceAll("\\[[\\u4e00-\\u9fa5]+\\]", "");
            name = name.replaceAll("\\p{Punct}", "");
            name = name.replaceAll("[\\u00AD|\\u3002|\\uff1f|\\uff01|\\uff0c|\\u3001|\\uff1b|\\uff1a|\\u201c|\\u201d|\\u2018|\\u2019|\\uff08|\\uff09|\\u300a|\\u300b|\\u3008|\\u3009|\\u3010|\\u3011|\\u300e|\\u300f|\\u300c|\\u300d|\\ufe43|\\ufe44|\\u3014|\\u3015|\\u2026|\\u2014|\\uff5e|\\ufe4f|\\uffe5]", "");
            name = name.replaceAll("[pP+~$`^=|<>～｀＄＾＋＝｜＜＞￥×,（）\\·]", "");
            name = name.replaceAll("(法院\\S*)", "法院");
            name = name.replaceAll("(人民$)", "人民法院");
            name = name.replaceAll("(法$)", "法院");
            name = name.replaceAll("(人法院)", "人民法院");
            return name;
        } else {
            if (!Strings.isNullOrEmpty(anNo)) {
                Pattern p = Pattern.compile("([京皖鲁赣闽甘冀青吉粤沪琼最内苏津晋渝黑藏陕黔云辽鄂宁浙川桂新京兵湘豫]+\\d*)");
                Matcher m = p.matcher(anNo);
                if (m.find()) {
                    return m.group();
                }
            }

            return "";
        }
    }

    public static String full2Half(String input) {
        char[] c = input.toCharArray();

        for(int i = 0; i < c.length; ++i) {
            if (c[i] == 12288) {
                c[i] = ' ';
            } else if (c[i] > '\uff00' && c[i] < '｟') {
                c[i] -= 'ﻠ';
            }
        }

        return new String(c);
    }

    public static void main(String[] args) {
        System.out.println(evaluate("", "(2017)渝0153民初605号"));
        System.out.println(evaluate(".陕西榆林市靖边县法院", ""));
    }
}

