package com.qcc.udf.cpws.wjp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.cpws.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class getcpwsCompareOtherUDF extends UDF {

    public static String evaluate(String jsonStr, String companyNames) {
        try {


            boolean hasCommon = false;
            if (StringUtils.isNotEmpty(jsonStr)
                    && !jsonStr.equals("[]")
                    && (!isContainsohter(jsonStr)
                    || jsonStr.contains("SupNameAndKeyNo"))) {


                if (StringUtils.isNotEmpty(companyNames)
                        && !companyNames.matches(".*[某×xX*ｘＸ✘✖ΧχⅹХх].*")) {
                    // 裁判文书当事人
                    List<String> cpwsNames = new ArrayList<>();
                    List<JSONObject> list = JSON.parseArray(jsonStr, JSONObject.class);
                    for (JSONObject object : list) {
                        cpwsNames.add(CommonUtil.full2Half(object.getString("Name")));
                        if (StringUtils.isNotEmpty(object.getString("KeyNo"))) {
                            cpwsNames.add(object.getString("KeyNo"));
                        }
                        if (object.containsKey("SupNameAndKeyNo")) {
                            cpwsNames.add(CommonUtil.full2Half(object.getJSONObject("SupNameAndKeyNo").getString("Name")));
                            if (StringUtils.isNotEmpty(object.getJSONObject("SupNameAndKeyNo").getString("KeyNo"))) {
                                cpwsNames.add(object.getJSONObject("SupNameAndKeyNo").getString("KeyNo"));
                            }
                        }
                    }

                    // 判断是否存在交集
                    List<String> beforeNames = Arrays.stream(companyNames.split(",")).collect(Collectors.toList());
                    hasCommon = cpwsNames.stream().anyMatch(beforeNames::contains);
                    if (hasCommon) {
                        return "存在交集";
                    }
                } else {
                    return "风险维度包含脱敏信息";
                }
            } else {
                return "裁判文书包含脱敏信息";
            }
            return "不存在交集";
        } catch (Exception e) {
            return "error";
        }
    }

    public static boolean isContainsohter(String keynoarray) {
        if (StringUtils.isNotEmpty(keynoarray)) {
            List<JSONObject> list = JSON.parseArray(keynoarray, JSONObject.class);
            for (JSONObject jsonObject : list) {
                if (StringUtils.isNotEmpty(jsonObject.getString("Name")) && jsonObject.getString("Name").matches(".*[某×xX*ｘＸ✘✖ΧχⅹХх].*")) {
                    return true;
                }
            }
        }
        return false;

    }

    public static void main(String[] args) {
        System.out.println(evaluate("[{\"KeyNo\":\"eebdaf3b3e570bccc8ee46cef218bf27\",\"Org\":0,\"ShowName\":\"上海活网数码营销有限公司\",\"Name\":\"上海活网数码营销有限公司\"},{\"KeyNo\":\"\",\"Org\":-1,\"ShowName\":\"朱**\",\"Name\":\"朱s123\"}]",

                "上海活网数码营销有限公司,王大勇"

        ));
    }
}
