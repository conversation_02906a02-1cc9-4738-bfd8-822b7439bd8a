/*
 *  Copyright 2021-2031 基础数据
 */
package com.qcc.udf.cpws;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @website https://www.qcc.com
 * @description /
 * @date 2023-03-01
 **/
@Data
public class RiskJudgementCase implements Serializable {

    private String id;

    private Date createDate;


    private Integer dataStatus;

    /**
     * 案号
     */
    private String caseNo;

    /**
     * 标题
     */
    private String title;

    /**
     * 法院
     */
    private String court;

    /**
     * 法院(标准化)
     */
    private String courtStandard;

    /**
     * 法院code组
     */
    private String courtCode;

    /**
     * 发布日期
     */
    private LocalDateTime submitDate;

    /**
     * 判决日期
     */
    private LocalDateTime judgeDate;

    /**
     * 案件类型
     */
    private String caseType;

    /**
     * 案件类型code
     */
    private String caseTypeCode;

    /**
     * 原始案由
     */
    private String caseReason;

    /**
     * 标准化案由
     */
    private String caseReasonStandard;

    /**
     * 案由code
     */
    private String caseReasonCode;

    /**
     * 所有当事人数组
     */
    private String keynoArray;

    /**
     * 被告数组
     */
    private String defdKeynoArray;

    /**
     * 原告数组
     */
    private String pltfKeynoArray;

    /**
     * 第三人数组
     */
    private String tdptKeynoArray;

    /**
     * 当事人数组（不确定身份的当事人）
     */
    private String otherKeynoArray;

    /**
     * 关联角色数组（案外人）
     */
    private String relatedKeynoArray;

    /**
     * 当事人身份数组
     */
    private String caseroleArray;

    /**
     * 上层案号
     */
    private String beforeCaseNo;

    /**
     * 关联案号
     */
    private String relatedCaseNo;

    /**
     * 胜败诉结果
     */
    private String lawsuitResult;

    /**
     * 文书类型
     */
    private String docType;

    /**
     * 地区
     */
    private String area;

    /**
     * 地区code
     */
    private String areaCode;

    /**
     * 爬虫keyno
     */
    private String spiderId;

    /**
     * 数据来源链接
     */
    private String sourceUrl;

    /**
     * 标准化案号
     */
    private String caseNoStandard;

    /**
     * 上级标准化案号
     */
    private String beforeCaseNoStandard;

    /**
     * AI当事人结果
     */
    private String caseroleAiResult;

    /**
     * 法律依据
     */
    private String legalBase;

    /**
     * 空内容标签
     */
    private Integer shieldFlag;

    /**
     * 涉案总金额
     */
    private String amountInvolved;

    /**
     * 爬虫表当事人
     */
    private String originParties;

    /**
     * 裁判文书来源标识
     */
    private String sourceTag;

    /**
     * 提取的开庭时间
     */
    private String courtDate;

    /**
     * 提取的立案时间
     */
    private String lianDate;

    /**
     * 提取的商标信息
     */
    private String trademarkInfo;

    /**
     * 提取的专利详情
     */
    private String patentsDetail;

    /**
     * 提取的商标详情
     */
    private String trademarkDetail;

    /**
     * 法律事实
     */
    private String legalFact;

    /**
     * 审理进程
     */
    private String trialRound;

    /**
     * 关联标签
     */
    private String relatedTag;

    /**
     * 审理经过
     */
    private String trialProcess;

    /**
     * 判决结果
     */
    private String judgeResult;

    /**
     * 业务主键
     */
    private String uniqueId;

    /**
     * 被告赔付金额
     */
    private String defendantPayTotal;

    /**
     * 当事人信息
     */
    private String litigantInfo;

    /**
     * 专利提取信息
     */
    private String patentsInfo;

    /**
     * 原告诉求
     */
    private String plaintiffRequest;

    /**
     * 文书正文中提取到的文书类型
     */
    private String lawPaperType;

    /**
     * 从当事人段落中提取到的涉案单位信息
     */
    private String caseRoleLawJudgeParty;

    /**
     * 新案外人
     */
    private String outsider;

    /**
     * 新案外人公司
     */
    private String outsiderC;

    /**
     * ipokeywords
     */
    private String compRelKeywords;

    /**
     * ipo其他当事人
     */
    private String compRelKeywordsOther;

    /**
     * 关联主体
     */
    private String subjectRelKeywords;

    /**
     * ipokeywords_v2
     */
    private String compRelKeywordsV2;

    /**
     * ipo其他当事人_v2
     */
    private String compRelKeywordsOtherV2;

    /**
     * 当事人金额信息
     */
    private String partyAmount;

    /**
     * 建筑企业案件
     */
    private Integer isBuildCase;
    /**
     * 业务主键
     */
    private String uniqueIdV2;
    /**
     * 标准化案号
     */
    private String caseNoStandardV2;

}