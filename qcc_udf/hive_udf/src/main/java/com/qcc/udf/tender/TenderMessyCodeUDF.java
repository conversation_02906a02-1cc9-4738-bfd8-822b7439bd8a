package com.qcc.udf.tender;

import org.apache.commons.io.IOUtils;
import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;

public class TenderMessyCodeUDF extends GenericUDF {

    private static final int commonCounterLine = 2000;
    private static final Map<String, Long> tenderCharStatisticMap;
    private static Long commonCounter;

    static {
        InputStream inputStream = null;
        InputStreamReader inputStreamReader = null;
        BufferedReader reader = null;
        try {
            inputStream = TenderMessyCodeUDF.class.getResourceAsStream("/tender_char_statistics.txt");
            inputStreamReader = new InputStreamReader(inputStream, "UTF-8");
            reader = new BufferedReader(inputStreamReader);
            Map<String, Long> tempMap = new HashMap<>();
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                String[] items = line.split("\t");
                if (items.length >= 2) {
                    tempMap.put(items[0], Long.parseLong(items[1]));
                    if (commonCounter == null && tempMap.size() >= commonCounterLine) {
                        commonCounter = Long.parseLong(items[1]);
                    }
                }
            }
            tenderCharStatisticMap = tempMap;

        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            IOUtils.closeQuietly(reader);
            IOUtils.closeQuietly(inputStreamReader);
            IOUtils.closeQuietly(inputStream);
        }
    }


    @Override
    public ObjectInspector initialize(ObjectInspector[] objectInspectors) throws UDFArgumentException {
        return PrimitiveObjectInspectorFactory.javaStringObjectInspector;
    }

    @Override
    public Object evaluate(DeferredObject[] deferredObjects) throws HiveException {
        String inputStr = deferredObjects[0].get().toString();
        inputStr = inputStr.replaceAll("[ \r\n\t]", "");
        int totalCount = inputStr.length();
        int generalCount = 0;
        String tempChar = null;
        for (int i = 0; i < inputStr.length(); i++) {
            tempChar = inputStr.substring(i, i+1);
            Long tenderStatisticsCount = tenderCharStatisticMap.get(tempChar);
            if (tenderStatisticsCount != null && tenderStatisticsCount >= commonCounter) {
                // 常规字
                generalCount ++;
            }
        }
        double generalPercent = ((double) generalCount / (double) totalCount);
        return generalPercent;
    }

    @Override
    public String getDisplayString(String[] strings) {
        return "";
    }


    public static void main(String[] args) throws Exception {
        DeferredObject[] deferredObjects = new DeferredObject[]{
                new DeferredJavaObject("大大大所多多大所")
        };

        Object a = new TenderMessyCodeUDF().evaluate(deferredObjects);
        System.out.println(a);
    }

}
