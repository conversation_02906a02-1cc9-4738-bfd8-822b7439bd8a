package com.qcc.udf.temp;

import cn.hutool.core.util.ReUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Pattern;

/**
 * 清洗doc no
 */
public class CleanDocNoTempV2 extends UDF {

    public String evaluate(String s) {
        if (StringUtils.isEmpty(s)) {
            return "";
        }
        return ReUtil.replaceAll(s, Pattern.compile("[^\\u4e00-\\u9fa5\\d]"), "");
    }

    public static void main(String[] args) {
        CleanDocNoTempV2 d = new CleanDocNoTempV2();
        System.out.println(d.evaluate("昆建罚字(2021)/&#^@***第270号"));
    }
}
