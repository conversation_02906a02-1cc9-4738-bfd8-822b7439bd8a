package com.qcc.udf.ip_region;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDF;

import org.lionsoul.ip2region.xdb.Searcher;
import shaded.org.apache.http.HttpEntity;
import shaded.org.apache.http.client.methods.HttpGet;
import shaded.org.apache.http.entity.StringEntity;
import shaded.org.apache.http.impl.client.CloseableHttpClient;
import shaded.org.apache.http.impl.client.HttpClients;
import shaded.org.apache.http.message.BasicHeader;
import shaded.org.apache.http.util.EntityUtils;


import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/***
 *
 * <AUTHOR>
 */
@Description(name = "ipregion",
        value = "_FUNC_(a) - Returns Province-City "
)
public class GetIpRegionUDF extends UDF {

    private static List<String> list = Arrays.asList("北京市", "重庆市", "天津市", "上海市");

    private static List<String> otherList = Arrays.asList("香港", "澳门", "台湾");

    private static JSONObject standardProvinceCity = null;

    private static InputStream resourceAsStream = null;

    private static final String ipv4Rex = "(?:(?:2[0-4][0-9]\\.)|(?:25[0-5]\\.)|(?:1[0-9][0-9]\\.)|(?:[1-9][0-9]\\.)|(?:[0-9]\\.)){3}(?:(?:2[0-4][0-9])|(?:25[0-5])|(?:1[0-9][0-9])|(?:[1-9][0-9])|(?:[0-9]))";

    private static final String ipv6Rex = "(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))";

    static private final String token = "1y82gco1000cokjmcaxeiv3d00xu35dn";


    public static String getCityInfo(String ip) throws IOException {
        try {
            StringBuffer stringBuffer = new StringBuffer();
            InputStream is = GetIpRegionUDF.class.getResourceAsStream("/standard_provinc_city.txt");
            BufferedReader br = new BufferedReader(new InputStreamReader(is));
            String line;
            while ((line = br.readLine()) != null) {
                stringBuffer.append(line);
            }
            is.close();
            br.close();
            standardProvinceCity = JSONObject.parseObject(stringBuffer.toString());

        } catch (Exception e) {
            e.printStackTrace();
        }

        String result = null;
        Pattern r4 = Pattern.compile(ipv4Rex);
        Pattern r6 = Pattern.compile(ipv6Rex);
        Matcher m4 = r4.matcher(ip);
        Matcher m6 = r6.matcher(ip);
        while (m4.find()) {
            ip = m4.group(0);
            result = getIpv4Info(ip);
        }

        while (m6.find()) {
            ip = m6.group(0);
            result = getIpv6Info(ip);
        }

        return result;
    }

    public static String getIpv4Info(String ip) throws IOException {
        {
            try {
                resourceAsStream = GetIpRegionUDF.class.getClassLoader().getResourceAsStream("ip2region.xdb");
            } catch (Exception e) {
                e.printStackTrace();
            }

            byte[] cBuff;
            String result = null;
            try {
                cBuff = Searcher.loadContentFromFile(null, resourceAsStream);
                // 2、使用上述的 cBuff 创建一个完全基于内存的查询对象。
                Searcher searcher;
                searcher = Searcher.newWithBuffer(cBuff);


                Searcher.checkIP(ip);

                // 获取解析后的数据  格式：国家|大区|省|市|运营商
                String region = searcher.search(ip);
                String replace = region.replace("|", ",");
                String[] splits = replace.split(",");
                System.out.println(region);
                if (splits.length == Integer.parseInt("5")) {
                    String country = "0".equals(splits[0]) ? "non" : splits[0];
                    AtomicReference<String> province = new AtomicReference<String>();
                    AtomicReference<String> city = new AtomicReference<String>();
                    province.set("non");
                    city.set("non");
                    String operator = "0".equals(splits[4]) ? "non" : splits[4].contains("内网") ? "non" : splits[4];
                    standardProvinceCity.keySet().forEach(k -> {
                        if (k.contains(replaceProvince(splits[2]))) {
                            //省
                            province.set(k);
                            JSONArray jsonArray = standardProvinceCity.getJSONArray(k);
                            jsonArray.forEach(t -> {
                                if (((String) t).contains(replaceCity(k,splits[3]))) {
                                    city.set(t.toString());
                                }
                            });
                        }
                    });
                    result = country + "-" + province.get() + "-" + city.get() + "-" + operator;
                }


            } catch (Exception e) {
                System.out.println(ip);
                e.printStackTrace();
            }finally {
                resourceAsStream.close();
            }
            return result;
        }

    }

    public static String  getIpv6Info(String ip) {
        String result = null;
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet();

            httpGet.setHeader("Cookie", "token=" + token);
            httpGet.setURI(URI.create("http://*********:8499/api/ip/info?ip=" + ip));
            httpGet.setHeader(new BasicHeader("Accept", "text/plain;charset=utf-8"));

            String responseBody = httpClient.execute(httpGet, httpResponse -> {

                int status = httpResponse.getStatusLine().getStatusCode();
                if (status < HttpStatus.SC_OK || status >= HttpStatus.SC_MULTIPLE_CHOICES) {
                    // ... handle unsuccessful request
                    throw new RuntimeException("http请求 error:" + httpResponse.getStatusLine().getReasonPhrase());
                }
                HttpEntity entity = httpResponse.getEntity();
                return entity != null ? EntityUtils.toString(entity, "UTF-8") : null;
            });
            // ... do something with response
            JSONObject info = JSONObject.parseObject(responseBody);
            System.out.println(info);
            if (info.getLong("code") == 0) {
                JSONObject location = info.getJSONObject("data").getJSONObject("location_info");
                String country = location.getString("country");
                AtomicReference<String> province = new AtomicReference<String>();
                AtomicReference<String> city = new AtomicReference<String>();
                province.set("non");
                city.set("non");
                String operator = location.getString("owner").replaceAll("中国","");
                standardProvinceCity.keySet().forEach(k -> {
                    if (replaceProvince(location.getString("province")).contains(k)) {
                        //省
                        province.set(k);
                        JSONArray jsonArray = standardProvinceCity.getJSONArray(k);
                        jsonArray.forEach(t -> {
                            if (replaceCity(k, location.getString("city")).contains(t.toString())) {
                                city.set((String) t);
                            }
                        });
                    }
                });
                result = country + "-" + province.get() + "-" + city.get() + "-" + operator;
            } else {
                throw new Exception("http请求 error: 接口code出错 code值" + info.getLong("code"));
            }
            httpClient.close();
            return result;

        } catch (Exception e) {
            // ... handle IO exception
            e.printStackTrace();
        }

        return result;
    }

    private static String replaceCity(String province , String city) {
        if (province.contains("香港")) {
            city = "香港特别行政区";
            return city;
        } else if (province.contains("澳门")) {
            city = "澳门特别行政区";
            return city;
        } else
        if ("0".equals(city)) {
            city = "non";
            return city;
        } else if (city.contains("内网")) {
            city = "non";
            return city;
        } else if (city.endsWith("地区")) {
            city = city.replaceAll("地区", "");
            return city;
        } else if (city.endsWith("市") || city.endsWith("县")) {
            city = city.substring(0, city.length() - 1);
            return city;
        } /*else {
            switch (city) {
                case "延边":
                    city = "延边朝鲜族自治州";
                    break;
                case "恩施":
                    city = "恩施土家族苗族自治州";
                    break;
                case "湘西":
                    city = "湘西土家族苗族自治州";
                    break;
                case "凉山":
                    city = "凉山彝族自治州";
                    break;
                case "甘孜":
                    city = "甘孜藏族自治州";
                    break;
                case "阿坝":
                    city = "阿坝藏族羌族自治州";
                    break;
                case "黔南":
                    city = "黔南布依族苗族自治州";
                    break;
                case "黔东南":
                    city = "黔东南苗族侗族自治州";
                    break;
                case "黔西南":
                    city = "黔西南布依族苗族自治州";
                    break;
                case "迪庆":
                    city = "迪庆藏族自治州";
                    break;
                case "怒江":
                    city = "怒江傈僳族自治州";
                    break;
                case "德宏":
                    city = "德宏傣族景颇族自治州";
                    break;
                case "大理":
                    city = "大理白族自治州";
                    break;
                case "西双版纳":
                    city = "西双版纳傣族自治州";
                    break;
                case "文山":
                    city = "文山壮族苗族自治州";
                    break;
                case "红河":
                    city = "红河哈尼族彝族自治州";
                    break;
                case "楚雄":
                    city = "楚雄彝族自治州";
                    break;
                case "甘南":
                    city = "甘南藏族自治州";
                    break;
                case "临夏":
                    city = "临夏回族自治州";
                    break;
                case "海西":
                    city = "海西蒙古族藏族自治州";
                    break;
                case "玉树":
                    city = "玉树藏族自治州";
                    break;
                case "果洛":
                    city = "果洛藏族自治州";
                    break;
                case "海南":
                    city = "海南藏族自治州";
                    break;
                case "黄南":
                    city = "黄南藏族自治州";
                    break;
                case "海北":
                    city = "海北藏族自治州";
                    break;
                case "伊犁":
                    city = "伊犁哈萨克自治州";
                    break;
                case "克孜勒苏":
                    city = "克孜勒苏柯尔克孜自治州";
                    break;
                case "巴音郭楞":
                    city = "巴音郭楞蒙古自治州";
                    break;
                case "博尔塔拉":
                    city = "博尔塔拉蒙古自治州";
                    break;
                case "昌吉":
                    city = "昌吉回族自治州";
                    break;
                default:
                    return city;
            }*/
        return city;
    }

    public static String replaceProvince(String provience) {
        if ("0".equals(provience)) {
            provience = "non";
            return provience;
        } else if (provience.contains("内网")) {
            provience = "non";
            return provience;
        } else if ("澳门".equals(provience)) {
            provience = "中国澳门";
            return provience;
        } else if ("香港".equals(provience)) {
            provience = "中国香港";
            return provience;
        } else if (provience.contains("台湾")) {
            provience = "中国台湾";
            return provience;
        } else if (provience.endsWith("省") || provience.endsWith("市")) {
            provience = provience.substring(0, provience.length() - 1);
            return provience;
        }
        return provience;
    }

    /**
     * 静态方法，传入ip地址，返回ip地址所在城市或地区
     *
     * @param ip IP地址，例：************
     * @return 返回IP地址所在城市或地区，例：北京市
     */
    public static String evaluate(String ip) throws IOException {
        return getCityInfo(ip);
    }

    public static void main(String[] ars) throws IOException, URISyntaxException {
        System.out.println(evaluate("***************"));
    }

}
