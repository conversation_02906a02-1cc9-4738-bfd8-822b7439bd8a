package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * @Auther: liulh
 * @Date: 2019/1/17 14:38
 * @Description:
 */
public class getLastNDateTime extends UDF {

    public String evaluate(int param) throws Exception {
        String result = "";
        Calendar c = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        c.setTime(sdf.parse(sdf.format(new Date())));
        for (int i = 1; i <= param; i++) {
            c.add(Calendar.DATE, -1);
            long time = c.getTimeInMillis() / 1000;
                result = result + "," + time;
        }
        return result.substring(1);
    }

}
