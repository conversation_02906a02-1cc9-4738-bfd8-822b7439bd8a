package com.qcc.udf;

import com.google.gson.Gson;
import com.google.gson.TypeAdapter;
import com.google.gson.internal.LinkedTreeMap;
import com.google.gson.reflect.TypeToken;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDFArgumentTypeException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.parse.SemanticException;
import org.apache.hadoop.hive.ql.udf.generic.AbstractGenericUDAFResolver;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDAFEvaluator;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.PrimitiveObjectInspector;
import org.apache.hadoop.hive.serde2.typeinfo.TypeInfo;
import org.apache.hadoop.hive.serde2.typeinfo.TypeInfoUtils;
import org.apache.hadoop.io.Text;

import java.io.IOException;
import java.util.*;


@Description(name = "yyc_case_relation", value = "_FUNC_(expr) - return case_inner_relationships")
public class Case_inner_RelationUDAF extends AbstractGenericUDAFResolver {
    static final Log LOG = LogFactory.getLog(Case_inner_RelationUDAF.class);

    @Override
    public GenericUDAFEvaluator getEvaluator(TypeInfo[] parameters) throws SemanticException {
        if (parameters.length != 9) {
            throw new UDFArgumentTypeException(parameters.length - 1,
                    "Exactly 9 argument is expected.");
        }
        for (int i = 0; i < parameters.length; i++) {
            ObjectInspector oi = TypeInfoUtils.getStandardJavaObjectInspectorFromTypeInfo(parameters[i]);

            if (oi.getCategory() != ObjectInspector.Category.PRIMITIVE) {
                throw new UDFArgumentTypeException(0,
                        "Argument must be PRIMITIVE, but "
                                + oi.getCategory().name()
                                + " was passed.");
            }

            PrimitiveObjectInspector inputOI = (PrimitiveObjectInspector) oi;

            if (inputOI.getPrimitiveCategory() != PrimitiveObjectInspector.PrimitiveCategory.STRING
                    && inputOI.getPrimitiveCategory() != PrimitiveObjectInspector.PrimitiveCategory.LONG
                    && inputOI.getPrimitiveCategory() != PrimitiveObjectInspector.PrimitiveCategory.INT) {
                throw new UDFArgumentTypeException(0,
                        "Argument must be String,Long or Int, but "
                                + inputOI.getPrimitiveCategory().name()
                                + " was passed.");

            }
        }
        return new Case_inner_RelationUDAFEvaluator();
    }

    public static class Case_inner_RelationUDAFEvaluator extends GenericUDAFEvaluator {
        PrimitiveObjectInspector inputOI;
        ObjectInspector outputOI;
        ObjectInspector middleInputOI;
        int stage;
        Map<String, String> beforeMap = new HashMap<>();
        Map<String, String> afterMap = new HashMap<>();
        Map<String, String> companyinfo = new HashMap<>();
        Map<String, String> idMap = new HashMap<>();

        @Override
        public ObjectInspector init(Mode m, ObjectInspector[] parameters)
                throws HiveException {

            assert (parameters.length == 9);
            super.init(m, parameters);

            //map阶段读取sql列，输入为String基础数据格式
            if (m == Mode.PARTIAL1 || m == Mode.COMPLETE) {
                inputOI = (PrimitiveObjectInspector) parameters[0];
            } else {
                //其余阶段，输入为Integer基础数据格式
                middleInputOI = parameters[0];
            }

            // 指定各个阶段输出数据格式都为Integer类型
            if (m == Mode.FINAL) {
                stage = 1;
                outputOI = ObjectInspectorFactory.getReflectionObjectInspector(String.class,
                        ObjectInspectorFactory.ObjectInspectorOptions.JAVA);
            } else {
                stage = 0;
                outputOI = ObjectInspectorFactory.getReflectionObjectInspector(Text.class,
                        ObjectInspectorFactory.ObjectInspectorOptions.JAVA);
            }
            return outputOI;

        }

        static class InputInfos {
            String id;
            String judgedate;
            String beforecaseno;
            String trialround;
            String casename;
            String caserole;
            String casetype;
            String court;
        }

        static class InfosStore implements AggregationBuffer {
            Boolean empty = true;
            String strresult = "";
            Text result = new Text("");
        }

        //允许保留中间值
        @Override
        public AggregationBuffer getNewAggregationBuffer() throws HiveException {
            InfosStore result = new InfosStore();
            reset(result);
            return result;
        }

        @Override
        public void reset(AggregationBuffer agg) throws HiveException {
            InfosStore myagg = (InfosStore) agg;
            myagg.empty = false;
            String strresult = "";
            myagg.result = new Text("");
        }

        private boolean warned = false;

        @Override
        public void iterate(AggregationBuffer agg, Object[] parameters)
                throws HiveException {
            assert (parameters.length == 9);
            InfosStore myagg = (InfosStore) agg;
            InputInfos a = new InputInfos();
            a.id = parameters[0] == null ? "" : parameters[0].toString();
            a.judgedate = parameters[1] == null ? "0" : parameters[1].toString();
            String caseno = parameters[2] == null ? "" : parameters[2].toString();
            if ("".equals(caseno)) {
                return;
            }
            a.beforecaseno = parameters[3] == null ? "" : parameters[3].toString();
            a.trialround = parameters[4] == null ? "" : parameters[4].toString();
            a.casename = parameters[5] == null ? "" : parameters[5].toString();
            a.caserole = parameters[6] == null || "".equals(parameters[6]) ? "[]" : parameters[6].toString();
            a.casetype = parameters[7] == null ? "" : parameters[7].toString();
            a.court = parameters[8] == null ? "" : parameters[8].toString();
            Gson gson = new Gson();
            Map<String, InputInfos> b = new HashMap<>();
            b.put(caseno, a);
            myagg.empty = false;
            myagg.result = new Text(myagg.result.toString() + gson.toJson(b) + "_split_");
            LOG.error("iterate " + myagg.result);
        }

        @Override
        public Object terminatePartial(AggregationBuffer agg) throws HiveException {
            InfosStore myagg = (InfosStore) agg;
            Gson gson = new Gson();
            LOG.error("terminatePartial " + gson.toJson(myagg.result));
            return myagg.result;

        }

        @Override
        public void merge(AggregationBuffer agg, Object partial)
                throws HiveException {
            InfosStore myagg = (InfosStore) agg;
            if (partial != null && partial.toString().contains("_split_")) {
                myagg.result = new Text(myagg.result + partial.toString());
            }
        }

        @Override
        public Object terminate(AggregationBuffer agg) throws HiveException {
            InfosStore myagg = (InfosStore) agg;

            Gson gson = new Gson();
            LOG.error("terminate--------------完整:" + myagg.result);
            Map<String, InputInfos> partialMap = new HashMap<>();
            Map<String, Object> tmp = new HashMap<>();
            for (String a : myagg.result.toString().split("_split_")) {
                LOG.error("terminate--------------split1:" + a);
                if (!"".equals(a)) {
                    LOG.error("terminate--------------split2:" + a);
                    tmp.clear();
                    tmp = (Map<String, Object>) gson.fromJson(a, new TypeToken<Map<String, Object>>() {
                    }.getType());
                    Iterator<String> iter = tmp.keySet().iterator();
                    while (iter.hasNext()) {
                        String key = iter.next();
                        LOG.error("terminate--------------caseno:" + key);
                        InputInfos tmp2 = new InputInfos();
                        Map<String, Object> tmp3 = (Map<String, Object>) tmp.get(key);
                        tmp2.id = tmp3.get("id").toString();
                        LOG.error("terminate--------------id:" + tmp2.id);
                        tmp2.judgedate = tmp3.get("judgedate").toString();
                        tmp2.beforecaseno = tmp3.get("beforecaseno").toString();
                        tmp2.trialround = tmp3.get("trialround").toString();
                        tmp2.casename = tmp3.get("casename").toString();
                        tmp2.caserole = tmp3.get("caserole").toString();
                        tmp2.casetype = tmp3.get("casetype").toString();
                        tmp2.court = tmp3.get("court").toString();
                        partialMap.put(key, tmp2);
                    }

                }
            }
            //LOG.error("merge-----stage: "+stage+"---------partial:"+gson.toJson(((InfosStore)partial).InputInfosMap));
            beforeMap.clear();
            afterMap.clear();
            companyinfo.clear();
            idMap.clear();
            //1.存储信息
            Iterator<String> iter1 = partialMap.keySet().iterator();
            while (iter1.hasNext()) {
                String caseno = iter1.next();
                String id = partialMap.get(caseno).id;
                Long judgedate = Long.parseLong(partialMap.get(caseno).judgedate);
                String beforecaseno = partialMap.get(caseno).beforecaseno;
                String trialround = partialMap.get(caseno).trialround;
                String casename = partialMap.get(caseno).casename;
                String caserole = partialMap.get(caseno).caserole;
                String casetype = partialMap.get(caseno).casetype;
                String court = partialMap.get(caseno).court;
                idMap.put(caseno, id);
                companyinfo.put(caseno,
                        "\"judgedate\":\"" + judgedate + "\",\"casename\":\"" + casename + "\",\"caseno\":\"" + caseno + "\",\"casetype\":\"" + casetype + "\",\"court\":\"" + court + "\",\"caserole\":" + caserole);

                if (!"".equals(beforecaseno)) {
                    if (!caseno.equals(beforecaseno)) {
                        //前置节点
                        beforeMap.put(caseno, beforecaseno);
                        //后继节点
                        if (!afterMap.containsKey(beforecaseno)) {
                            afterMap.put(beforecaseno, caseno);
                        } else {
                            //有两个案号的前置节点关联到当前案号，跳过
                            return null;
                        }
                    }
                }
            }
            ArrayList<Map<Integer, String>> resultlist = new ArrayList<>();
            //2.遍历
            Iterator<String> iter2 = partialMap.keySet().iterator();
            int relationedFlag = 0;
            while (iter2.hasNext()) {
                String caseno0 = iter2.next();
                String caseno = caseno0;
                LOG.error("caseno is " + caseno);
                String buffer1 = caseno;
                String buffer2;
                String buffer3 = partialMap.get(caseno).id;
                if (companyinfo.containsKey(caseno)) {
                    buffer2 = companyinfo.get(caseno);
                } else {
                    buffer2 = "\"judgedate\":\"0\",\"casename\":\"\",\"caseno\":\"\",\"casetype\":\"\",\"court\":\"\",\"caserole\":[]";
                }
                int count = 1;
                int flag = 0;
                while (beforeMap.containsKey(caseno) && count <= 5) {
                    if (!buffer1.contains(beforeMap.get(caseno))) {
                        String beforecaseno = beforeMap.get(caseno);

                        if (companyinfo.containsKey(beforecaseno)) {
                            buffer1 = beforecaseno + "," + buffer1;
                            buffer2 = companyinfo.get(beforecaseno) + "$$$" + buffer2;
                            buffer3 = idMap.get(beforecaseno) + "," + buffer3;
                        } else {
                            //buffer2="\"id\":\"\",\"judgedate\":\"\",\"trialround\":\"\""+"_"+buffer2;
                            break;
                        }
                        caseno = beforecaseno;
                    } else {
                        flag = 1;
                        break;
                    }
                    count++;
                }

                caseno = caseno0;
                while (afterMap.containsKey(caseno) && count <= 9) {
                    if (!buffer1.contains(afterMap.get(caseno))) {
                        String afterno = afterMap.get(caseno);

                        if (companyinfo.containsKey(afterno)) {
                            buffer1 = buffer1 + "," + afterno;
                            buffer2 = buffer2 + "$$$" + companyinfo.get(afterno);
                            buffer3 = buffer3 + "," + idMap.get(afterno);
                        } else {
                            //buffer2=buffer2+"_"+"\"id\":\"\",\"judgedate\":\"\",\"trialround\":\"\"";
                            break;
                        }
                        caseno = afterno;
                    } else {
                        flag = 1;
                        break;
                    }
                    count++;

                }
                String[] a = buffer1.split(",");
                String[] b = buffer2.split("\\$\\$\\$");
                String[] c = buffer3.split(",");

                if (flag == 0) {
                    for (int i = 0; i < count; i++) {

                        b[i] = "{" + b[i] + "}";

                    }
                } else {
                    return null;
                }
                Map<Integer, String> result = new HashMap<>();
                if (idMap.get(caseno0) != null && count > 1) {
                    for (int i = 0; i < 6; i++) {
                        if (i < count) {
                            result.put(0 + i, c[i]);
                        } else {
                            result.put(0 + i, null);
                        }

                    }
                    for (int i = 0; i < 6; i++) {
                        if (i < count) {
                            result.put(6 + i, b[i]);
                        } else {
                            result.put(6 + i, null);
                        }
                    }
                    //result.setBigint("rounds",new Long(count));

                }
                result.put(12, String.valueOf(count));
                if (count > 1) {
                    relationedFlag = 1;
                }
                resultlist.add(result);
            }

            myagg.strresult = gson.toJson(resultlist);
            LOG.error(myagg.strresult);
            if (relationedFlag == 1) {
                return myagg.strresult;
            } else {
                return null;
            }
        }


    }

    static class MapTypeAdapter extends TypeAdapter<Object> {

        @Override
        public Object read(JsonReader in) throws IOException {
            JsonToken token = in.peek();
            switch (token) {
                case BEGIN_ARRAY:
                    List<Object> list = new ArrayList<Object>();
                    in.beginArray();
                    while (in.hasNext()) {
                        list.add(read(in));
                    }
                    in.endArray();
                    return list;

                case BEGIN_OBJECT:
                    Map<String, Object> map = new LinkedTreeMap<String, Object>();
                    in.beginObject();
                    while (in.hasNext()) {
                        map.put(in.nextName(), read(in));
                    }
                    in.endObject();
                    return map;

                case STRING:
                    return in.nextString();

                case NUMBER:

                    double dbNum = in.nextDouble();

                    if (dbNum > Long.MAX_VALUE) {
                        return dbNum;
                    }

                    long lngNum = (long) dbNum;
                    if (dbNum == lngNum) {
                        return lngNum;
                    } else {
                        return dbNum;
                    }

                case BOOLEAN:
                    return in.nextBoolean();

                case NULL:
                    in.nextNull();
                    return null;

                default:
                    throw new IllegalStateException();
            }
        }

        @Override
        public void write(JsonWriter out, Object value) throws IOException {
        }
    }

    public static void main(String[] args) {
        System.out.println("abba".substring(1, 4 - 1));
    }
}

