package com.qcc.udf.liu;

import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.cpws.CommonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.text.SimpleDateFormat;
import java.util.*;

public class getDcrzInfo4 extends UDF {

    public static String evaluate(List<String> infoList)  throws Exception{
        JSONObject result = new JSONObject();
        String priority = "3";
        String dataStatus = "1";

        if(CollectionUtils.isNotEmpty(infoList)){
            long maxDate = 0L;
            long maxRegDate = 0L;
            Set<Long> dateSet = new LinkedHashSet<>();
            JSONObject maxInfo = null;
            JSONObject maxRegInfo = null;
            for (String str : infoList){
                JSONObject json = JSONObject.parseObject(str);
                String createDate = json.getString("createdate");
                if (CommonUtil.parseDateToTimeStamp(createDate) > maxDate){
                    maxDate = CommonUtil.parseDateToTimeStamp(createDate);
                    maxInfo = json;
                }
                String regDate = json.getString("regdate");
                if (CommonUtil.parseDateToTimeStamp(regDate) > maxRegDate){
                    maxRegDate = CommonUtil.parseDateToTimeStamp(regDate);
                    maxRegInfo = json;
                }
                dateSet.add(CommonUtil.parseDateToTimeStamp(json.getString("regdate")));
            }

            if (StringUtils.isEmpty(maxInfo.getString("businesstype")) && StringUtils.isEmpty(maxInfo.getString("regdate")) && "1".equals(maxInfo.getString("datastatus"))){
                priority = "3";
                dataStatus = maxInfo.getString("datastatus");
            }else{
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(sdf.parse(sdf.format(new Date())));
                long today = calendar.getTimeInMillis() / 1000;
                        //long today = System.currentTimeMillis()/1000;
                // 最近一周
                Set<Long> dateSet7 = new LinkedHashSet<>();
                Set<Long> dateSet7_30 = new LinkedHashSet<>();
                Set<Long> dateSet30_180 = new LinkedHashSet<>();
                Set<Long> dateSet180 = new LinkedHashSet<>();
                for (Long date : dateSet){
                    if ((today - date) <= (7*86400)){
                        dateSet7.add(date);
                    }
                    if ((today - date) > (7*86400) && (today - date) <= (30*86400)){
                        dateSet7_30.add(date);
                    }
                    if ((today - date) > (30*86400) && (today - date) <= (180*86400)){
                        dateSet30_180.add(date);
                    }
                    if ((today - date) > (180*86400)){
                        dateSet180.add(date);
                    }
                }

                // t-7=<regdate（最新一次请求），且近1个月有多笔，优先级为1，t-7=<regdate（进一个月只有本周有融资记录），或近1个月有多笔（进7天无融资记录）， 优先级为2 ，否则进行下一步
                if (dateSet7.size() > 0 && dateSet7_30.size() > 0){
                    priority = "1";
                }
                if (dateSet7.size() > 0 && dateSet7_30.size() == 0){
                    priority = "2";
                }
                /*if (dateSet7.size() == 0 && dateSet7_30.size() > 0){
                    priority = "2";
                }*/
                if (priority.equals("3")){
                    if (dateSet7_30.size() > 0 && dateSet30_180.size() > 0){
                        priority = "1";
                    }
                    if (dateSet7_30.size() > 0 && dateSet30_180.size() == 0){
                        priority = "2";
                    }
                    if (dateSet7_30.size() == 0 && dateSet30_180.size() > 0){
                        priority = "2";
                    }
                    if (priority.equals("3")){
                        if (dateSet30_180.size() > 0){
                            priority = "2";
                        }
                    }
                }

                dataStatus = maxInfo.getString("datastatus");
            }
        }

        result.put("Priority", priority);
        result.put("DataStatus", dataStatus);


        return result.toString();
    }


    public static void main(String[] args) {
        try {

            List<String> infoList = new LinkedList<>();
            infoList.add("{\"id\":\"0371ed890b4a4fb79e47ad48071e1c95\",\"createdate\":\"2022-11-08 14:44:28.0\",\"regdate\":\"2022-10-24 12:11:27\",\"businesstype\":\"生产设备、原材料、半成品、产品抵押\",\"datastatus\":\"1\",\"regno\":\"19862100002458967885\"}");
            infoList.add("{\"id\":\"6d34adb43e9546038e8ae6973e7541b7\",\"createdate\":\"2022-11-08 14:44:28.0\",\"regdate\":\"2022-10-23 09:24:27\",\"businesstype\":\"其他动产和权利担保\",\"datastatus\":\"1\",\"regno\":\"19854652002457998648\"}");
            infoList.add("{\"id\":\"8795550e88e8441ba297af1e04c2d94b\",\"createdate\":\"2022-11-08 14:44:28.0\",\"regdate\":\"2022-10-23 09:21:18\",\"businesstype\":\"其他动产和权利担保\",\"datastatus\":\"1\",\"regno\":\"19854652002457997897\"}");
            infoList.add("{\"id\":\"4709dbd6e5e44f8b96ad391bdf042500\",\"createdate\":\"2022-11-08 14:44:28.0\",\"regdate\":\"2022-10-24 12:09:11\",\"businesstype\":\"生产设备、原材料、半成品、产品抵押\",\"datastatus\":\"1\",\"regno\":\"19862100002458963048\"}");
            infoList.add("{\"id\":\"35dfe578656c4e0ca036263ed426bda6\",\"createdate\":\"2022-11-08 14:44:27.0\",\"regdate\":\"2022-10-17 15:33:14\",\"businesstype\":\"生产设备、原材料、半成品、产品抵押\",\"datastatus\":\"1\",\"regno\":\"19750178002443864648\"}");
            infoList.add("{\"id\":\"bec016a72bd04ddab1adf6bef479cd05\",\"createdate\":\"2022-11-08 14:44:28.0\",\"regdate\":\"2022-10-23 15:42:16\",\"businesstype\":\"生产设备、原材料、半成品、产品抵押\",\"datastatus\":\"1\",\"regno\":\"19855321002458073224\"}");
            infoList.add("{\"id\":\"9d8e8fd51ea14a18bc84537ab4aa44e8\",\"createdate\":\"2022-11-08 14:44:27.0\",\"regdate\":\"2022-10-23 15:36:58\",\"businesstype\":\"生产设备、原材料、半成品、产品抵押\",\"datastatus\":\"1\",\"regno\":\"19855321002458071612\"}");
            infoList.add("{\"id\":\"a1fbfe15eee44e3ca72b33a15064093a\",\"createdate\":\"2022-11-08 14:44:27.0\",\"regdate\":\"2022-10-17 15:29:38\",\"businesstype\":\"生产设备、原材料、半成品、产品抵押\",\"datastatus\":\"1\",\"regno\":\"19750178002443835299\"}");
            System.out.println(evaluate(infoList));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
