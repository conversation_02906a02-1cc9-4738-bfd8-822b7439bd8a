package com.qcc.udf.risk_graph;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.court_notice.anUtils.MD5Util;
import com.qcc.udf.court_notice.anUtils.Util;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Auther: liulh
 * @Date: 2020/6/2 20:33
 * @Description:
 */
public class getCaseSearchRole extends UDF {
    public static String  evaluate(String id,String type, String roleArray) {
        JSONArray result = new JSONArray();

        if (StringUtils.isEmpty(roleArray)){
            return "";
        }

        Set<JSONObject> roleSetP = new LinkedHashSet<>();
        Set<JSONObject> roleSetD = new LinkedHashSet<>();
        JSONArray caseRoleJsonArray = JSONArray.parseArray(roleArray);
        Iterator<Object> it = caseRoleJsonArray.iterator();
        while(it.hasNext()){
            JSONObject jsonObject = (JSONObject)it.next();
            if (StringUtils.isNotEmpty(jsonObject.getString("P"))){
                String role = getCaseRoleCode(Util.full2Half(jsonObject.getString("R")));
                if (role.contains("P")){
                    roleSetP.add(jsonObject);
                }
                if (role.contains("D")){
                    roleSetD.add(jsonObject);
                }
            }
        }

        if (roleSetP.size() > 0 && roleSetD.size() > 0){
            for (JSONObject str : roleSetP){
                for (JSONObject sub : roleSetD){
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("Id", id);
                    jsonObject.put("Typ", type.contains("民事") ? "ms" : "xz");
                    jsonObject.put("Pname", str.getString("P"));
                    jsonObject.put("PkeyNo", str.getString("N"));
                    String pid = jsonObject.getString("PkeyNo");
                    if (StringUtils.isEmpty(pid)){
                        if (jsonObject.getString("Pname").length() < 6){
                            pid = "rp".concat(MD5Util.ecodeByMD5(Util.full2Half(id.concat(jsonObject.getString("Pname")))).substring(2));
                        }else {
                            pid = "rp".concat(MD5Util.ecodeByMD5(Util.full2Half(id.concat(jsonObject.getString("Pname")))).substring(2));
                        }
                    }
                    jsonObject.put("PkeyNoId", pid);
                    jsonObject.put("Dname", sub.getString("P"));
                    jsonObject.put("DkeyNo", sub.getString("N"));
                    String did = jsonObject.getString("DkeyNo");
                    if (StringUtils.isEmpty(did)){
                        if (jsonObject.getString("Dname").length() < 6){
                            did = "rp".concat(MD5Util.ecodeByMD5(Util.full2Half(id.concat(jsonObject.getString("Dname")))).substring(2));
                        }else {
                            did = "rp".concat(MD5Util.ecodeByMD5(Util.full2Half(id.concat(jsonObject.getString("Dname")))).substring(2));
                        }
                    }
                    jsonObject.put("DkeyNoId", did);

                    result.add(jsonObject);
                }
            }
        }

        return result.isEmpty() || result.size() == 0 ? "" : result.toString();
    }

    public static String getCaseRoleCode(String caseRole){
        String result = "";
        if (StringUtils.isEmpty(caseRole)){
            return "";
        }

        Pattern p1 = Pattern.compile("(被执行人)|(被告)|(被申请人)|(被申请执行人)|(原审被告)|(被上诉人\\(原审被告\\))|(上诉人\\(原审被告\\))|(被告\\(反诉原告\\))|(被告人)|(上诉人\\(一审被告\\))|" +
                "(被上诉人\\(一审被告\\))|(被上诉人)|(上诉人\\(原审被告反诉原告\\))|(被告二)|(被告一)|(原告\\(被告\\))|(被申请人\\(一审被告二审被上诉人\\))|(被申请人\\(原审被告\\))|(再审申请人\\(一审被告二审上诉人\\))|" +
                "(再审申请人\\(原审被告\\))|(被申请人\\(仲裁被申请人\\))|(被申请人\\(原被执行人\\))|(再审被申请人)|(上诉人\\(原审被告原审原告\\))");
        Matcher m1 = p1.matcher(caseRole);
        if (m1.matches()) {
            result = "D";
        }

        Pattern p2 = Pattern.compile("(申请执行人)|(原告)|(申请人)|(被上诉人\\(原审原告\\))|(复议申请人)|(上诉人\\(原审原告\\))|(原告\\(反诉被告\\))|(上诉人)|(被上诉人\\(一审原告\\))|(上诉人\\(一审原告\\))|(被上诉人\\(原审原告反诉被告\\))|" +
                "(原审原告)|(再审申请人)|(被告\\(原告\\))|(被申请人\\(原审原告\\))|(附带民事诉讼原告人)|(复议申请人\\(原申请执行人\\))|(再审申请人\\(一审原告二审上诉人\\))|(再审申请人\\(原审原告\\))|(申请再审人\\(一审原告二审上诉人\\))|" +
                "(二审上诉人)|(原告人)|(附带民事诉讼原告)|(上诉人\\(原审原告原审被告\\))|(起诉人)|(申请人\\(仲裁申请人\\))|(赔偿请求人)");
        Matcher m2 = p2.matcher(caseRole);
        if (m2.matches()) {
            result = "P";
        }

        return result;
    }

    public static void main(String[] args) {
        System.out.println(evaluate("id1", "民事案件","[{\"P\":\"辽中县农村信用合作联社\",\"R\":\"当事人\",\"N\":\"7750964ed8fb5b1d743af99be277bb4b\",\"O\":0},{\"P\":\"刘红\",\"R\":\"被告\",\"N\":\"\",\"O\":-2},{\"P\":\"王福军\",\"R\":\"被告\",\"N\":\"\",\"O\":-2}]"));
        //System.out.println("123".substring(2));
    }
}
