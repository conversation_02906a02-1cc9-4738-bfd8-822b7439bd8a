package com.qcc.udf.overseas;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 业务UDF（海外企业）格式化大段文本信息的字段样式，去掉连续空格或换行符，仅保留一个空格符
 * ---------------------------------------------------------------------------------------------------------
 * add jar hdfs://ldh/data/hive/udf/qcc_udf.jar;
 * create temporary function FormatAddressField as 'com.qcc.udf.overseas.FormatAddressFieldUDF';
 * ---------------------------------------------------------------------------------------------------------
 * select FormatAddressField ('68350 EAST PALM CANYON DRIVE       CATHEDRAL CITY CA 92234');
 * 结果: '68350 EAST PALM CANYON DRIVE CATHEDRAL CITY CA 92234'
 */
public class FormatAddressFieldUDF extends UDF {

    public String evaluate(String input) {
        String result = input;
        if (StringUtils.isNotBlank(input)) {
            List<String> itemList = Arrays.asList(input.split(" |\r\n|\r|\n")).stream()
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            result = StringUtils.join(itemList, " ");
        }
        return result;
    }

    public static void main(String[] args) {

        List<String> addresses = Arrays.asList(
                "4083 SOUTH 4425 WEST     WEST VALLEY CITY, UT 84120"
        );
        for (String address : addresses) {
            System.out.println(address + "\t" + new FormatAddressFieldUDF().evaluate(address));
        }
    }
}
