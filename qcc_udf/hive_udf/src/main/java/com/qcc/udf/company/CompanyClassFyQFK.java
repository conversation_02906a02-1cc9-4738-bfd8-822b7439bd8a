package com.qcc.udf.company;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.TypeAdapter;
import com.google.gson.internal.LinkedTreeMap;
import com.google.gson.reflect.TypeToken;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class CompanyClassFyQFK extends UDF {
    public String evaluate(String classifytwo, String classifythree, Double y, int x, Double z) {
        String[] title = {"大型企业", "中型企业", "小型企业", "微型企业", "待定"};
        if (classifytwo == null || "53,66,67,68,69,83,84,91,92,93,94,95,96,97".contains(classifytwo)) {
            return "待定";
        }
        if (classifythree != null && !"".equals(classifythree) && "704".contains(classifythree)) {
            return "待定";
        }
        if ("70".equals(classifytwo) || "59".equals(classifytwo)) {
            classifytwo = (null != classifythree) ? classifythree : classifytwo + "1";
        }
        if (classifytwo == null) {
            return "待定";
        } else {
            int index = compute(classifytwo, y, Double.valueOf(x), z);
            if (index == -1) {
                return "出错";
            } else if (index == 0) {
                return "待定";
            } else {
                return title[index - 1];
            }
        }
    }

    Map<String, Map<String, ArrayList<Long>>> jsondata = getjson();

    /**
     * 根据行业获取对应等级的人员数量要求
     *
     * @param classifytwo
     * @param classifythree
     * @param x
     * @return
     */
    public String getCompanyTypeByClassifyTwoAndEmployee(String classifytwo, String classifythree, Double x) {
        String[] title = {"大型企业", "中型企业", "小型企业", "微型企业", "待定"};
        if (classifytwo == null || "53,66,67,68,69,83,84,91,92,93,94,95,96,97".contains(classifytwo)) {
            return "待定";
        }
        if (classifythree != null && !"".equals(classifythree) && "704".contains(classifythree)) {
            return "待定";
        }
        if ("70".equals(classifytwo) || "59".equals(classifytwo)) {
            classifytwo = (null != classifythree) ? classifythree : classifytwo + "1";
        }
        if (classifytwo == null) {
            return "待定";
        } else {
            int numx = 0;
            if (jsondata.containsKey(classifytwo)) {
                Map<String, ArrayList<Long>> jsontmp = jsondata.get(classifytwo);

                if (jsontmp.containsKey("x")) {
                    numx = compute_value(jsontmp, x, "x");
                }
            } else {
                Map<String, ArrayList<Long>> jsontmp = jsondata.get("200");

                if (jsontmp.containsKey("x")) {
                    numx = compute_value(jsontmp, x, "x");
                }
            }
            if (numx == -1) {
                return "出错";
            } else if (numx == 0) {
                return "待定";
            } else {
                return title[numx - 1];
            }
        }

    }


    public boolean getCompanyNeedEmployee(String classifytwo, String classifythree) {
        if (classifytwo == null || "53,66,67,68,69,83,84,91,92,93,94,95,96,97".contains(classifytwo)) {
            return false;
        }
        if (classifythree != null && !"".equals(classifythree) && "704".contains(classifythree)) {
            return false;
        }
        if ("70".equals(classifytwo) || "59".equals(classifytwo)) {
            classifytwo = (null != classifythree) ? classifythree : classifytwo + "1";
        }

        if (jsondata.containsKey(classifytwo)) {
            Map<String, ArrayList<Long>> jsontmp = jsondata.get(classifytwo);

            return jsontmp.containsKey("x");
        }
        return true;
    }

    public String getCompanyTypeWithTotal(String classifytwo, String classifythree, Double y) {
        String[] title = {"大型企业", "中型企业", "小型企业", "微型企业", "待定"};
        if (classifytwo == null || "53,66,67,68,69,83,84,91,92,93,94,95,96,97".contains(classifytwo)) {
            return "待定";
        }
        if (classifythree != null && !"".equals(classifythree) && "704".contains(classifythree)) {
            return "待定";
        }
        if ("70".equals(classifytwo) || "59".equals(classifytwo)) {
            classifytwo = (null != classifythree) ? classifythree : classifytwo + "1";
        }
        if (classifytwo == null) {
            return "待定";
        } else {
            int numz = 0;
            if (jsondata.containsKey(classifytwo)) {
                Map<String, ArrayList<Long>> jsontmp = jsondata.get(classifytwo);

                if (jsontmp.containsKey("z")) {
                    numz = compute_value(jsontmp, y, "z");
                }

            } else {
                Map<String, ArrayList<Long>> jsontmp = jsondata.get("200");

                if (jsontmp.containsKey("z")) {
                    numz = compute_value(jsontmp, y, "z");
                }

            }
            if (numz == -1) {
                return "出错";
            } else if (numz == 0) {
                return "待定";
            } else {
                return title[numz - 1];
            }
        }
    }

    public String getCompanyTypeWithRevenueNum(String classifytwo, String classifythree, Double y) {
        String[] title = {"大型企业", "中型企业", "小型企业", "微型企业", "待定"};
        if (classifytwo == null || "53,66,67,68,69,83,84,91,92,93,94,95,96,97".contains(classifytwo)) {
            return "待定";
        }
        if (classifythree != null && !"".equals(classifythree) && "704".contains(classifythree)) {
            return "待定";
        }
        if ("70".equals(classifytwo) || "59".equals(classifytwo)) {
            classifytwo = (null != classifythree) ? classifythree : classifytwo + "1";
        }
        if (classifytwo == null) {
            return "待定";
        } else {
            int numy = 0;
            if (jsondata.containsKey(classifytwo)) {
                Map<String, ArrayList<Long>> jsontmp = jsondata.get(classifytwo);

                if (jsontmp.containsKey("y")) {
                    numy = compute_value(jsontmp, y, "y");
                }

            } else {
                Map<String, ArrayList<Long>> jsontmp = jsondata.get("200");

                if (jsontmp.containsKey("y")) {
                    numy = compute_value(jsontmp, y, "y");
                }

            }
            if (numy == -1) {
                return "出错";
            } else if (numy == 0) {
                return "待定";
            } else {
                return title[numy - 1];
            }
        }
    }

    /**
     * 根据行业获取对应等级的人员数量要求
     *
     * @param classifytwo
     * @param classifythree
     * @return
     */
    public String getCompanyTypeByClassifyTwoWithOutEmployee(String classifytwo, String classifythree, Double y, Double z) {
        String[] title = {"大型企业", "中型企业", "小型企业", "微型企业", "待定"};
        if (classifytwo == null || "53,66,67,68,69,83,84,91,92,93,94,95,96,97".contains(classifytwo)) {
            return "待定";
        }
        if (classifythree != null && !"".equals(classifythree) && "704".contains(classifythree)) {
            return "待定";
        }
        if ("70".equals(classifytwo) || "59".equals(classifytwo)) {
            classifytwo = (null != classifythree) ? classifythree : classifytwo + "1";
        }
        if (classifytwo == null) {
            return "待定";
        } else {
            int numy = 0;
            int numz = 0;
            if (jsondata.containsKey(classifytwo)) {
                Map<String, ArrayList<Long>> jsontmp = jsondata.get(classifytwo);

                if (jsontmp.containsKey("y")) {
                    numy = compute_value(jsontmp, y, "y");
                }
                if (jsontmp.containsKey("z")) {
                    numz = compute_value(jsontmp, z, "z");
                }
            } else {
                Map<String, ArrayList<Long>> jsontmp = jsondata.get("200");

                if (jsontmp.containsKey("y")) {
                    numy = compute_value(jsontmp, y, "y");
                }
                if (jsontmp.containsKey("z")) {
                    numz = compute_value(jsontmp, z, "z");
                }
            }
            int numresult = getIndexWithOutEmployee(numy, numz);

            if (numresult == -1) {
                return "出错";
            } else if (numresult == 0) {
                return "待定";
            } else {
                return title[numresult - 1];
            }
        }

    }

    private int getIndexWithOutEmployee(int numy, int numz) {

        if (numy > 0 || numz > 0) {
            // 如果是一个条件，则直接返回
            if (numy == 0) {
                return numz;
            }
            if (numz == 0) {
                return numy;
            }

            // 否则要两个条件，需考虑滑档，之前null和0不考虑滑档，现在必定滑档
            return compute_level(numy, numz);


        }
        return 0;
    }

    public List<String> getIndustryConf(String classifytwo, String classifythree) {
        if (classifytwo == null || "53,66,67,68,69,83,84,91,92,93,94,95,96,97".contains(classifytwo)) {
            return new ArrayList<>();
        }

        if (classifythree != null && !"".equals(classifythree) && "704".contains(classifythree)) {
            return new ArrayList<>();
        }
        if ("70".equals(classifytwo) || "59".equals(classifytwo)) {
            classifytwo = (null != classifythree) ? classifythree : classifytwo + "1";
        }

        if (jsondata.containsKey(classifytwo)) {
            Map<String, ArrayList<Long>> jsontmp = jsondata.get(classifytwo);

            return new ArrayList<>(jsontmp.keySet());
        }

        return Collections.singletonList("x");

    }

    public int compute(String classifytwo, Double y, Double x, Double z) {
        int result = 0;
        int numx = 0;
        int numy = 0;
        int numz = 0;
        if (jsondata.containsKey(classifytwo)) {
            Map<String, ArrayList<Long>> jsontmp = jsondata.get(classifytwo);
            if (jsontmp.containsKey("x")) {
                numx = compute_value(jsontmp, x, "x");
            }
            if (jsontmp.containsKey("y")) {
                numy = compute_value(jsontmp, y, "y");
            }
            if (jsontmp.containsKey("z")) {
                numz = compute_value(jsontmp, z, "z");
            }
        } else {
            Map<String, ArrayList<Long>> jsontmp = jsondata.get("200");
            if (jsontmp.containsKey("x")) {
                numx = compute_value(jsontmp, x, "x");
            }
            if (jsontmp.containsKey("y")) {
                numy = compute_value(jsontmp, y, "y");
            }
            if (jsontmp.containsKey("z")) {
                numz = compute_value(jsontmp, z, "z");
            }
        }
        if (numx > 0 || numy > 0 || numz > 0) {
            // 如果是一个条件，则直接返回
            if (numx == 0 && numy == 0) {
                return numz;
            }
            if (numx == 0 && numz == 0) {
                return numy;
            }
            if (numy == 0 && numz == 0) {
                return numx;
            }
            // 否则要两个条件，需考虑滑档，之前null和0不考虑滑档，现在必定滑档
            if (numx == 0) {
                return compute_level(numy, numz);

            }
            if (numy == 0) {
                return compute_level(numx, numz);

            }
            if (numz == 0) {
                return compute_level(numy, numx);

            }
            return -1;
        }
        return result;
    }

    public int compute_level(int a, int b) {
        // 如果同时满足，则返回；如果一条不满足，必定滑档，且最低是待定
        return a == b ? a : Math.min(Math.min(a, b) + 1, 4);
    }

    public int compute_value(Map<String, ArrayList<Long>> jsontmp, Double value, String type) {
        int num = 0;
        if (value == null || value == 0) {
            return 5;
        }
        ArrayList<Long> listint = jsontmp.get(type);
        for (int i = 0, length = listint.size(); i < length; i++) {
            long tmp = listint.get(i);
            if (value >= tmp) {
                break;
            }
            num++;
        }
        num++;
        return num;
    }

    public static Map<String, Map<String, ArrayList<Long>>> getjson() {
        String jsonstring = "{\"01\":{\"y\":[20000,500,50]},\"02\":{\"y\":[20000,500,50]},\"03\":{\"y\":[20000,500,50]},\"04\":{\"y\":[20000,500,50]},\"05\":{\"y\":[20000,500,50]},\"06\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"07\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"08\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"09\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"10\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"11\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"12\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"13\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"14\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"15\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"16\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"17\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"18\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"19\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"20\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"21\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"22\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"23\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"24\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"25\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"26\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"27\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"28\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"29\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"30\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"31\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"32\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"33\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"34\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"35\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"36\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"37\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"38\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"39\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"40\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"41\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"42\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"43\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"44\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"45\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"46\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"47\":{\"y\":[80000,6000,300],\"z\":[80000,5000,300]},\"48\":{\"y\":[80000,6000,300],\"z\":[80000,5000,300]},\"49\":{\"y\":[80000,6000,300],\"z\":[80000,5000,300]},\"50\":{\"y\":[80000,6000,300],\"z\":[80000,5000,300]},\"51\":{\"x\":[200,20,5],\"y\":[40000,5000,1000]},\"52\":{\"x\":[300,50,10],\"y\":[20000,500,100]},\"54\":{\"x\":[1000,300,20],\"y\":[30000,3000,200]},\"55\":{\"x\":[1000,300,20],\"y\":[30000,3000,200]},\"56\":{\"x\":[1000,300,20],\"y\":[30000,3000,200]},\"57\":{\"x\":[1000,300,20],\"y\":[30000,3000,200]},\"58\":{\"x\":[1000,300,20],\"y\":[30000,3000,200]},\"591\":{\"x\":[1000,300,20],\"y\":[30000,3000,200]},\"592\":{\"x\":[200,100,20],\"y\":[30000,1000,100]},\"593\":{\"x\":[200,100,20],\"y\":[30000,1000,100]},\"594\":{\"x\":[200,100,20],\"y\":[30000,1000,100]},\"595\":{\"x\":[200,100,20],\"y\":[30000,1000,100]},\"596\":{\"x\":[200,100,20],\"y\":[30000,1000,100]},\"599\":{\"x\":[200,100,20],\"y\":[30000,1000,100]},\"60\":{\"x\":[1000,300,20],\"y\":[30000,2000,100]},\"61\":{\"x\":[300,100,10],\"y\":[10000,2000,100]},\"62\":{\"x\":[300,100,10],\"y\":[10000,2000,100]},\"63\":{\"x\":[2000,100,10],\"y\":[100000,1000,100]},\"64\":{\"x\":[2000,100,10],\"y\":[100000,1000,100]},\"65\":{\"x\":[300,100,10],\"y\":[10000,1000,50]},\"701\":{\"y\":[200000,1000,100],\"z\":[10000,5000,2000]},\"702\":{\"x\":[1000,300,100],\"y\":[5000,1000,500]},\"71\":{\"x\":[300,100,10],\"z\":[120000,8000,100]},\"72\":{\"x\":[300,100,10],\"z\":[120000,8000,100]},\"200\":{\"x\":[300,100,10]}}";
        GsonBuilder builder = new GsonBuilder();
        builder.registerTypeAdapter(new TypeToken<Map<String, Map<String, ArrayList<Long>>>>() {
        }.getType(), new MapTypeAdapter());
        Gson gson = builder.create();
        Map<String, Map<String, ArrayList<Long>>> result = gson.fromJson(jsonstring,
                new TypeToken<Map<String, Map<String, ArrayList<Long>>>>() {
                }.getType());
        return result;
    }

    public static class MapTypeAdapter extends TypeAdapter<Object> {
        @Override
        public Object read(JsonReader in) throws IOException {
            JsonToken token = in.peek();
            switch (token) {
                case BEGIN_ARRAY:
                    List<Object> list = new ArrayList<Object>();
                    in.beginArray();
                    while (in.hasNext()) {
                        list.add(read(in));
                    }
                    in.endArray();
                    return list;

                case BEGIN_OBJECT:
                    Map<String, Object> map = new LinkedTreeMap<String, Object>();
                    in.beginObject();
                    while (in.hasNext()) {
                        map.put(in.nextName(), read(in));
                    }
                    in.endObject();
                    return map;

                case STRING:
                    return in.nextString();

                case NUMBER:

                    double dbNum = in.nextDouble();

                    if (dbNum > Long.MAX_VALUE) {
                        return dbNum;
                    }

                    long lngNum = (long) dbNum;
                    if (dbNum == lngNum) {
                        return lngNum;
                    } else {
                        return dbNum;
                    }

                case BOOLEAN:
                    return in.nextBoolean();

                case NULL:
                    in.nextNull();
                    return null;

                default:
                    throw new IllegalStateException();
            }
        }

        @Override
        public void write(JsonWriter out, Object value) throws IOException {
        }
    }
}
