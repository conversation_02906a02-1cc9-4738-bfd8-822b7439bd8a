package com.qcc.udf.company;

import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDTF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


class ResponseMode {
    private int LRCount;

    private int PubCount;

    private int CIECount;

    private int BNTCount;

    private int AnnualReportCount;

    private int SoftCRCount;

    private int NewsCount;

    private int CertificateCount;

    private int FinancingCount;

    private int TaxCount;

    private int PatentCount;

    private int CompanyInfoCount;

    private int FinanceCount;

    private int AdministrativePenaltyCount;

    private int RecruitCount;

    private List<CommonData> CommonList;

    private int JudgementCount;

    private int CerCategoryCount;

    private int LPCount;

    private int LMCount;

    private int HCCount;

    private int LDCount;

    private int InvesterCount;

    private int CourtSessionAnnouncementCount;

    private int WorkCRCount;

    private int PSCount;

    private int CTACount;

    private int ProductCount;

    private int ENPCount;

    private int JudicialSaleCount;

    private String _id;

    private int SAICCount;

    private int PefundCount;

    private int WebCount;

    private int PSMCount;

    private int FINCount;

    private int ZhiXingCount;

    private int TenderCount;

    private int ShiXinCount;

    private int TaxOwnCount;

    private int WechatCount;

    private long UploadTime;

    private int AdministrativeLicenseCount;

    private int ResearchReportCount;

    private int TiCount;

    private int XJCount;

    private int FinancingTaxCount;

    private int TeamCount;

    public void setLRCount(int LRCount) {
        this.LRCount = LRCount;
    }

    public int getLRCount() {
        return this.LRCount;
    }

    public void setPubCount(int PubCount) {
        this.PubCount = PubCount;
    }

    public int getPubCount() {
        return this.PubCount;
    }

    public void setCIECount(int CIECount) {
        this.CIECount = CIECount;
    }

    public int getCIECount() {
        return this.CIECount;
    }

    public void setBNTCount(int BNTCount) {
        this.BNTCount = BNTCount;
    }

    public int getBNTCount() {
        return this.BNTCount;
    }

    public void setAnnualReportCount(int AnnualReportCount) {
        this.AnnualReportCount = AnnualReportCount;
    }

    public int getAnnualReportCount() {
        return this.AnnualReportCount;
    }

    public void setSoftCRCount(int SoftCRCount) {
        this.SoftCRCount = SoftCRCount;
    }

    public int getSoftCRCount() {
        return this.SoftCRCount;
    }

    public void setNewsCount(int NewsCount) {
        this.NewsCount = NewsCount;
    }

    public int getNewsCount() {
        return this.NewsCount;
    }

    public void setCertificateCount(int CertificateCount) {
        this.CertificateCount = CertificateCount;
    }

    public int getCertificateCount() {
        return this.CertificateCount;
    }

    public void setFinancingCount(int FinancingCount) {
        this.FinancingCount = FinancingCount;
    }

    public int getFinancingCount() {
        return this.FinancingCount;
    }

    public void setTaxCount(int TaxCount) {
        this.TaxCount = TaxCount;
    }

    public int getTaxCount() {
        return this.TaxCount;
    }

    public void setPatentCount(int PatentCount) {
        this.PatentCount = PatentCount;
    }

    public int getPatentCount() {
        return this.PatentCount;
    }

    public void setCompanyInfoCount(int CompanyInfoCount) {
        this.CompanyInfoCount = CompanyInfoCount;
    }

    public int getCompanyInfoCount() {
        return this.CompanyInfoCount;
    }

    public void setFinanceCount(int FinanceCount) {
        this.FinanceCount = FinanceCount;
    }

    public int getFinanceCount() {
        return this.FinanceCount;
    }

    public void setAdministrativePenaltyCount(int AdministrativePenaltyCount) {
        this.AdministrativePenaltyCount = AdministrativePenaltyCount;
    }

    public int getAdministrativePenaltyCount() {
        return this.AdministrativePenaltyCount;
    }

    public void setRecruitCount(int RecruitCount) {
        this.RecruitCount = RecruitCount;
    }

    public int getRecruitCount() {
        return this.RecruitCount;
    }

    public void setCommonList(List<CommonData> CommonList) {
        this.CommonList = CommonList;
    }

    public List<CommonData> getCommonList() {
        return this.CommonList;
    }

    public void setJudgementCount(int JudgementCount) {
        this.JudgementCount = JudgementCount;
    }

    public int getJudgementCount() {
        return this.JudgementCount;
    }

    public void setCerCategoryCount(int CerCategoryCount) {
        this.CerCategoryCount = CerCategoryCount;
    }

    public int getCerCategoryCount() {
        return this.CerCategoryCount;
    }

    public void setLPCount(int LPCount) {
        this.LPCount = LPCount;
    }

    public int getLPCount() {
        return this.LPCount;
    }

    public void setLMCount(int LMCount) {
        this.LMCount = LMCount;
    }

    public int getLMCount() {
        return this.LMCount;
    }

    public void setHCCount(int HCCount) {
        this.HCCount = HCCount;
    }

    public int getHCCount() {
        return this.HCCount;
    }

    public void setLDCount(int LDCount) {
        this.LDCount = LDCount;
    }

    public int getLDCount() {
        return this.LDCount;
    }

    public void setInvesterCount(int InvesterCount) {
        this.InvesterCount = InvesterCount;
    }

    public int getInvesterCount() {
        return this.InvesterCount;
    }

    public void setCourtSessionAnnouncementCount(int CourtSessionAnnouncementCount) {
        this.CourtSessionAnnouncementCount = CourtSessionAnnouncementCount;
    }

    public int getCourtSessionAnnouncementCount() {
        return this.CourtSessionAnnouncementCount;
    }

    public void setWorkCRCount(int WorkCRCount) {
        this.WorkCRCount = WorkCRCount;
    }

    public int getWorkCRCount() {
        return this.WorkCRCount;
    }

    public void setPSCount(int PSCount) {
        this.PSCount = PSCount;
    }

    public int getPSCount() {
        return this.PSCount;
    }

    public void setCTACount(int CTACount) {
        this.CTACount = CTACount;
    }

    public int getCTACount() {
        return this.CTACount;
    }

    public void setProductCount(int ProductCount) {
        this.ProductCount = ProductCount;
    }

    public int getProductCount() {
        return this.ProductCount;
    }

    public void setENPCount(int ENPCount) {
        this.ENPCount = ENPCount;
    }

    public int getENPCount() {
        return this.ENPCount;
    }

    public void setJudicialSaleCount(int JudicialSaleCount) {
        this.JudicialSaleCount = JudicialSaleCount;
    }

    public int getJudicialSaleCount() {
        return this.JudicialSaleCount;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public String get_id() {
        return this._id;
    }

    public void setSAICCount(int SAICCount) {
        this.SAICCount = SAICCount;
    }

    public int getSAICCount() {
        return this.SAICCount;
    }

    public void setPefundCount(int PefundCount) {
        this.PefundCount = PefundCount;
    }

    public int getPefundCount() {
        return this.PefundCount;
    }

    public void setWebCount(int WebCount) {
        this.WebCount = WebCount;
    }

    public int getWebCount() {
        return this.WebCount;
    }

    public void setPSMCount(int PSMCount) {
        this.PSMCount = PSMCount;
    }

    public int getPSMCount() {
        return this.PSMCount;
    }

    public void setFINCount(int FINCount) {
        this.FINCount = FINCount;
    }

    public int getFINCount() {
        return this.FINCount;
    }

    public void setZhiXingCount(int ZhiXingCount) {
        this.ZhiXingCount = ZhiXingCount;
    }

    public int getZhiXingCount() {
        return this.ZhiXingCount;
    }

    public void setTenderCount(int TenderCount) {
        this.TenderCount = TenderCount;
    }

    public int getTenderCount() {
        return this.TenderCount;
    }

    public void setShiXinCount(int ShiXinCount) {
        this.ShiXinCount = ShiXinCount;
    }

    public int getShiXinCount() {
        return this.ShiXinCount;
    }

    public void setTaxOwnCount(int TaxOwnCount) {
        this.TaxOwnCount = TaxOwnCount;
    }

    public int getTaxOwnCount() {
        return this.TaxOwnCount;
    }

    public void setWechatCount(int WechatCount) {
        this.WechatCount = WechatCount;
    }

    public int getWechatCount() {
        return this.WechatCount;
    }

    public void setUploadTime(long uploadTime) {
        this.UploadTime = uploadTime;
    }

    public long getUploadTime() {
        return this.UploadTime;
    }

    public void setAdministrativeLicenseCount(int AdministrativeLicenseCount) {
        this.AdministrativeLicenseCount = AdministrativeLicenseCount;
    }

    public int getAdministrativeLicenseCount() {
        return this.AdministrativeLicenseCount;
    }

    public void setResearchReportCount(int ResearchReportCount) {
        this.ResearchReportCount = ResearchReportCount;
    }

    public int getResearchReportCount() {
        return this.ResearchReportCount;
    }

    public void setTiCount(int TiCount) {
        this.TiCount = TiCount;
    }

    public int getTiCount() {
        return this.TiCount;
    }

    public void setXJCount(int XJCount) {
        this.XJCount = XJCount;
    }

    public int getXJCount() {
        return this.XJCount;
    }

    public void setFinancingTaxCount(int FinancingTaxCount) {
        this.FinancingTaxCount = FinancingTaxCount;
    }

    public int getFinancingTaxCount() {
        return this.FinancingTaxCount;
    }

    public void setTeamCount(int TeamCount) {
        this.TeamCount = TeamCount;
    }

    public int getTeamCount() {
        return this.TeamCount;
    }

}

class CommonData {
    private String Value;

    private String Key;

    public void setValue(String Value) {
        this.Value = Value;
    }

    public String getValue() {
        return this.Value;
    }

    public void setKey(String Key) {
        this.Key = Key;
    }

    public String getKey() {
        return this.Key;
    }

}

public class CompanyCountParse extends GenericUDTF {
//    public static void main(String[] args) throws HiveException {
//        String str = "{\"LRCount\":0,\"PubCount\":0,\"CIECount\":0,\"BNTCount\":1,\"AnnualReportCount\":1,\"SoftCRCount\":0,\"NewsCount\":0,\"CertificateCount\":0,\"FinancingCount\":0,\"TaxCount\":0,\"PatentCount\":0,\"CompanyInfoCount\":0,\"Remark\":\"1901111832,AL\",\"FinanceCount\":0,\"AdministrativePenaltyCount\":0,\"RecruitCount\":0,\"CommonList\":[{\"Value\":0,\"Key\":1}],\"JudgementCount\":0,\"CerCategoryCount\":0,\"LPCount\":0,\"LMCount\":0,\"HCCount\":0,\"LDCount\":0,\"InvesterCount\":0,\"CourtSessionAnnouncementCount\":0,\"WorkCRCount\":0,\"PSCount\":0,\"CTACount\":0,\"ProductCount\":0,\"ENPCount\":0,\"JudicialSaleCount\":0,\"_id\":\"00035d267b2a8ec01c69072fe4dbeddf\",\"SAICCount\":0,\"PefundCount\":0,\"WebCount\":0,\"PSMCount\":0,\"FINCount\":0,\"ZhiXingCount\":0,\"TenderCount\":0,\"ShiXinCount\":0,\"TaxOwnCount\":0,\"WechatCount\":0,\"uploadtime\":1547202909137,\"AdministrativeLicenseCount\":1,\"ResearchReportCount\":0,\"TiCount\":0,\"IsIndividual\":false,\"XJCount\":0,\"FinancingTaxCount\":0,\"TeamCount\":0}";
//        new CompanyCountParse().process(new Object[]{str});
//    }

    @Override
    public void close() throws HiveException {
        // TODO Auto-generated method stub

    }

    @Override
    public void process(Object[] args) throws HiveException {
        String input = args[0].toString().replace("KeyNo","_id");
        ResponseMode jsonObject = JSON.parseObject(input, ResponseMode.class);
        Map<String, String> objMap = getObjInfo(jsonObject);
        String keyNo = jsonObject.get_id();
        long uploadTime = jsonObject.getUploadTime();
        for (String key : objMap.keySet()) {
            if (key == "CommonList" || key == "uploadTime" || key == "_id") {
                continue;
            } else {
                int dataCount = ParseToInt(objMap.get(key));
                if (dataCount > 0) {
                    String[] result = new String[4];
                    GetObj(result, keyNo, key, objMap.get(key), String.valueOf(uploadTime));
                    forward(result);
                    //System.out.println(result[0] + "," + result[1] + "," + result[2] + "," + result[3]);
                }
            }
        }
        List<CommonData> commonList = jsonObject.getCommonList();
        if (CollectionUtils.isNotEmpty(commonList)) {
            for (CommonData a : commonList) {
                if (ParseToInt(a.getValue()) > 0) {
                    String[] result = new String[4];
                    GetObj(result, keyNo, a.getKey(), a.getValue(), String.valueOf(uploadTime));
                    forward(result);
                    //System.out.println(result[0] + "," + result[1] + "," + result[2] + "," + result[3]);
                }
            }
        }

    }

    /**
     * 获取满足要求的key与value的字符串数组
     *
     * @param keyno
     * @param objkey
     * @param objvalue
     * @param uoloadtime
     * @return
     */
    private void GetObj(String[] result, String keyno, String objkey, String objvalue, String uoloadtime) {
        result[0] = keyno;
        result[1] = objkey;//属性名
        result[2] = objvalue;//属性值
        result[3] = uoloadtime;
    }

    @Override
    public StructObjectInspector initialize(ObjectInspector[] args) {
        ArrayList<String> fieldNames = new ArrayList<>();
        ArrayList<ObjectInspector> fieldOIs = new ArrayList<>();
        fieldNames.add("keyno");
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        fieldNames.add("objkey");
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        fieldNames.add("objvalue");
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        fieldNames.add("uoloadtime");
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        return ObjectInspectorFactory.getStandardStructObjectInspector(
                fieldNames, fieldOIs);
    }

    private int ParseToInt(String str) {
        try {
            return Integer.valueOf(str).intValue();
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 获取属性名及属性值的Map
     */
    private static Map<String, String> getObjInfo(Object o) {
        Map<String, String> result = new HashMap<>();
        Field[] fields = o.getClass().getDeclaredFields();
        for (int i = 0; i < fields.length; i++) {
            Field f = fields[i];
            f.setAccessible(true);
            String fieldName = f.getName();
            String fieldValue;
            try {
                fieldValue = String.valueOf(f.get(o));
                result.put(fieldName, fieldValue);
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }

        }
        return result;
    }

}