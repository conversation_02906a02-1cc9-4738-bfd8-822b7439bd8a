package com.qcc.udf;

import org.apache.commons.math3.distribution.NormalDistribution;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;

public class ZXNormalDistributionV1 extends UDF {
    public static String evaluate(String amount, String mean, String stdDev) {
        NormalDistribution  d = new NormalDistribution(StrToDouble(mean), StrToDouble(stdDev));
        return BigDecimal.valueOf(d.cumulativeProbability(StrToDouble(amount))).setScale(3,BigDecimal.ROUND_UP).toPlainString();
    }
    public static double StrToDouble(String v){
        try {
            return BigDecimal.valueOf(Double.parseDouble(v)).setScale(3,BigDecimal.ROUND_UP).doubleValue();
        }catch (Exception e){
            return 0;
        }
    }

    public static void main(String[] args) {
        System.out.println(evaluate("0","2273684.44","2273684"));

    }
}
