package com.qcc.udf;


import org.apache.hadoop.hive.ql.exec.UDF;


public class IsGreateNumberUDF extends UDF {
    public boolean evaluate(String str) {
        String[] numbers = new String[] { "1234", "2345", "3456", "4567", "5678","6789", "6543", "7654", "8765", "9876" };
        String[] chars = new String[] { "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G","J", "H", "I", "G", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z" };

        for (String item : numbers) {
            if(str.contains(item))
                return true;
        }

        for (String item : chars) {
            String temp = item + item + item + item;
            if(str.contains(temp))
                return true;
        }


        return false;
    }

  
   
}