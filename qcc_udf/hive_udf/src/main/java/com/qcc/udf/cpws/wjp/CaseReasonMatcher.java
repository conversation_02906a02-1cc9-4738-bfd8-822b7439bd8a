package com.qcc.udf.cpws.wjp;

import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

public class CaseReasonMatcher extends UDF {

    // 限定词枚举，如果案由前面有这些词则跳过匹配
    private static final Set<String> EXCLUSION_QUALIFIERS = new HashSet<>(Arrays.asList(
            "未申请",
            "不涉及",
            "非",
            "无",
            "排除",
            "除外",
            "不包括",
            "不含",
            "未涉及"
    ));

    //    private static Map<String, List<CaseReasonMapping>> groupedMappings = new HashMap<>();
    private static List<CaseReasonMapping> sorted = new ArrayList<>();

    static {
        InputStream is = null;
        Workbook workbook = null;

        try {
            // 使用FileInputStream直接读取文件
            String filePath = "src/main/resources/case_reason_20250716.xlsx";
            File file = new File(filePath);
            if (!file.exists()) {
                throw new IOException("无法找到配置文件: " + filePath);
            }
            is = new FileInputStream(file);

            // 使用Apache POI读取XLSX文件
            workbook = new XSSFWorkbook(is);
            Sheet sheet = workbook.getSheetAt(0); // 读取第一个工作表

            // 遍历所有行（跳过表头）
            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) {
                    continue;
                }

                // 读取三列数据：原始案由、标准案由、案由代码
                String originalReason = getCellValueAsString(row.getCell(0));
                String standardReason = getCellValueAsString(row.getCell(1));
                String reasonCode = getCellValueAsString(row.getCell(2));

                // 数据验证和过滤
                if (StringUtils.isNotEmpty(originalReason) && StringUtils.isNotEmpty(standardReason)) {
                    // 过滤特定的无效数据
                    if (originalReason.trim().equals("侵害商标权") && standardReason.trim().equals("商标权权属、侵权纠纷")) {
                        continue;
                    }
                    if (originalReason.trim().equals("纠纷\"")) {
                        continue;
                    }

                    sorted.add(new CaseReasonMapping(
                            originalReason.trim(),
                            standardReason.trim(),
                            reasonCode != null ? reasonCode.trim() : ""
                    ));
                }
            }

            workbook.close();

            // 按原始案由长度倒序排序（长的优先匹配）
            sorted.sort(Comparator.comparingInt((CaseReasonMapping m) -> m.originalReason.length()).reversed());

            System.out.println("成功加载案由配置数据，共 " + sorted.size() + " 条记录");

        } catch (IOException e) {
            System.err.println("读取案由配置文件失败: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("处理案由配置文件时发生异常: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 手动关闭资源
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    System.err.println("关闭workbook失败: " + e.getMessage());
                }
            }
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    System.err.println("关闭输入流失败: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 获取单元格的字符串值，处理不同的数据类型
     * @param cell 单元格对象
     * @return 单元格的字符串值
     */
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        return cell.getStringCellValue();

    }

    /**
     * 检查案由前是否有限定词，如果有则跳过匹配
     *
     * @param text           要检查的文本
     * @param originalReason 原始案由
     * @param index          案由在文本中的位置
     * @return true表示应该跳过匹配，false表示可以匹配
     */
    private boolean shouldSkipMatch(String text, String originalReason, int index) {
        if (text == null || index < 0) {
            return false;
        }

        // 获取案由前面的文本片段（最多检查前20个字符）
        int startIndex = Math.max(0, index - 20);
        String prefixText = text.substring(startIndex, index);

        // 检查是否包含限定词
        for (String qualifier : EXCLUSION_QUALIFIERS) {
            if (prefixText.contains(qualifier)) {
                return true; // 发现限定词，跳过匹配
            }
        }

        return false; // 没有发现限定词，可以匹配
    }

    // ✅ 优化逻辑：匹配所有案由，按照优先级和位置索引返回最靠前的案由
    // 匹配顺序：正文 > 标题 > 爬虫案由，同优先级内按出现位置最靠前排序
    public String evaluate(String casereason, String title, String contentText) {
        List<CaseReasonMapping> group = sorted;

        // 存储所有匹配结果，包含案由信息、优先级、位置索引
        List<MatchResult> allMatches = new ArrayList<>();

        // 优先级1：正文匹配
        if (contentText != null) {
            for (CaseReasonMapping m : group) {
                int index = contentText.indexOf(m.originalReason);
                if (index >= 0 && !shouldSkipMatch(contentText, m.originalReason, index)) {
                    allMatches.add(new MatchResult(m.standardReason, 1, index, m.originalReason));
                }
            }
        }

        // 优先级2：标题匹配
        if (title != null) {
            for (CaseReasonMapping m : group) {
                int index = title.indexOf(m.originalReason);
                if (index >= 0 && !shouldSkipMatch(title, m.originalReason, index)) {
                    allMatches.add(new MatchResult(m.standardReason, 2, index, m.originalReason));
                }
            }
        }

        // 优先级3：爬虫案由匹配
        if (casereason != null) {
            for (CaseReasonMapping m : group) {
                int index = casereason.indexOf(m.originalReason);
                if (index >= 0 && !shouldSkipMatch(casereason, m.originalReason, index)) {
                    allMatches.add(new MatchResult(m.standardReason, 3, index, m.originalReason));
                }
            }
        }

        // 如果没有匹配到任何案由，返回兜底案由
        if (allMatches.isEmpty()) {
            return "兜底案由";
        }

        // 优化后的排序逻辑：合同纠纷始终排在最后，其他类型按正常顺序排序
        allMatches.sort((a, b) -> {
            // 首先按优先级排序（数字越小优先级越高）
            int priorityCompare = Integer.compare(a.priority, b.priority);
            if (priorityCompare != 0) {
                return priorityCompare;
            }

            // 特殊处理：将'合同纠纷'排在最后
            boolean aIsContract = a.standardReason.equals("合同纠纷");
            boolean bIsContract = b.standardReason.equals("合同纠纷");

            if (aIsContract && !bIsContract) {
                return 1; // a是合同纠纷，排在后面
            }
            if (!aIsContract && bIsContract) {
                return -1; // b是合同纠纷，a排在前面
            }

            // 如果都不是合同纠纷，按案由长度排序（长度越长越优先）
            if (!aIsContract && !bIsContract) {
                int lengthCompare = Integer.compare(b.standardReason.length(), a.standardReason.length());
                if (lengthCompare != 0) {
                    return lengthCompare;
                }
            }

            // 长度相同时或都是合同纠纷时按位置索引排序（位置越靠前越优先）
            return Integer.compare(a.index, b.index);
        });

        // 返回最高优先级且符合排序规则的案由
        return allMatches.get(0).standardReason;
    }

    /**
     * 案由映射类
     */
    public static class CaseReasonMapping {
        public String originalReason;
        public String standardReason;
        public String reasonCode;

        public CaseReasonMapping(String originalReason, String standardReason, String reasonCode) {
            this.originalReason = originalReason;
            this.standardReason = standardReason;
            this.reasonCode = reasonCode;
        }
    }

    /**
     * 匹配结果类，用于存储匹配的案由信息、优先级和位置索引
     */
    public static class MatchResult {
        public String standardReason;    // 标准案由
        public int priority;             // 优先级：1=正文，2=标题，3=爬虫案由
        public int index;                // 在文本中的位置索引
        public String originalReason;    // 原始匹配的案由关键词

        public MatchResult(String standardReason, int priority, int index, String originalReason) {
            this.standardReason = standardReason;
            this.priority = priority;
            this.index = index;
            this.originalReason = originalReason;
        }

        @Override
        public String toString() {
            return String.format("MatchResult{standardReason='%s', priority=%d, index=%d, originalReason='%s'}",
                    standardReason, priority, index, originalReason);
        }
    }


    public static void main(String[] args) throws Exception {
        CaseReasonMatcher matcher = new CaseReasonMatcher();



        // 测试用例4：测试优先级
        String casereason4 = "[\"行纪合同纠纷\"]";  // 爬虫案由：优先级3
        String title4 = "青海安意房地产中介服务有限公司、常立娟中介合同纠纷民事一审民事裁定书";  // 标题：优先级2
        String content4 = "原告：青海安意房地产中介服务有限公司，住所：青海省西宁市城北区柴达木路351号16号楼1单元1011室。 法定代表人：雷菊林，该公司总经理。 被告：常立娟，女，****年**月**日出生，汉族，住**。 委托诉讼代理人：宋长银，男，****年**月**日出生，汉族，住青海省西宁市城北区。,原告青海安意房地产中介服务有限公司与被告常立娟中介合同纠纷一案，本院于2023年4月11日立案。原告青海安意房地产中介服务有限公司于2023年5月19日向本院提出撤诉申请。";  // 正文：优先级1
        String result4 = matcher.evaluate(casereason4, title4, content4);
        System.out.println("测试用例4 - 优先级测试（应返回劳动争议）：" + result4);

        System.out.println("=== 测试完成 ===");
    }

}