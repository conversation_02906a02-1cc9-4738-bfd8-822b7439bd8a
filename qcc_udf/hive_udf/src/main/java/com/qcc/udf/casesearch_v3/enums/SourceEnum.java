package com.qcc.udf.casesearch_v3.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum SourceEnum {
	GONG_SHANG_JU(1, "工商局"),
	XING_YONG_ZHONG_GUO(2, "信用中国"),
	SHUI_WU_JU(3, "税务局"),
	QI_TA(4, "其他");

	private Integer code;
	private String desc;


	SourceEnum(Integer code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public static String getSourceName(String source){
		for (SourceEnum value : SourceEnum.values()) {
			if(Objects.equals(source,value.getCode())){
				return value.getDesc();
			}
		}
		return source;
	}
}
