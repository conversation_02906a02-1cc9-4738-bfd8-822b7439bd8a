package com.qcc.udf.cpws.casesearch_v2;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.model.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2020/8/31
 */
public class CleanSingleDatailinfo extends UDF {
    public int  evaluate(String jsonStr){
        // 案件详情数据转换
        JSONObject json = JSONObject.parseObject(jsonStr);
        JSONArray array = json.getJSONArray("InfoList");
        List<CaseSearchMongoInfoListEntity> infoList = new LinkedList<>();
        if (array != null && !array.isEmpty() && array.size() > 0){
            infoList = JSONArray.parseArray(array.toString(), CaseSearchMongoInfoListEntity.class);
        }
        // 去除只有一条维度信息的数据
        int flag = checkIsSingleData(infoList);

        return flag;
    }

    public static int checkIsSingleData(List<CaseSearchMongoInfoListEntity> infoList){
        int result = 0;

        int validFlag = 0;
        int inValidFlag = 0;
        for (CaseSearchMongoInfoListEntity obj : infoList){

            if (CollectionUtils.isNotEmpty(obj.getSxList())){
                List<CaseSearchMongoSXListEntity> list = obj.getSxList();
                for (CaseSearchMongoSXListEntity item : list){
                    if (item.getIsValid() == 1){
                        validFlag++;
                    }else{
                        inValidFlag++;
                    }
                }
            }
            if (obj.getZxList() != null && obj.getZxList().size() > 0){
                List<CaseSearchMongoZXListEntity> list = obj.getZxList();
                for (CaseSearchMongoZXListEntity item : list){
                    if (item.getIsValid() == 1){
                        validFlag++;
                    }else{
                        inValidFlag++;
                    }
                }
            }
            if (obj.getXgList() != null && obj.getXgList().size() > 0){
                List<CaseSearchMongoXGListEntity> list = obj.getXgList();
                for (CaseSearchMongoXGListEntity item : list){
                    if (item.getIsValid() == 1){
                        validFlag++;
                    }else{
                        inValidFlag++;
                    }
                }
            }
            if (obj.getZbList() != null && obj.getZbList().size() > 0){
                List<CaseSearchMongoZbListEntity> list = obj.getZbList();
                for (CaseSearchMongoZbListEntity item : list){
                    if (item.getIsValid() == 1){
                        validFlag++;
                    }else{
                        inValidFlag++;
                    }
                }
            }
            if (obj.getKtggList() != null && obj.getKtggList().size() > 0){
                List<CaseSearchMongoKtggListEntity> list = obj.getKtggList();
                for (CaseSearchMongoKtggListEntity item : list){
                    if (item.getIsValid() == 1){
                        validFlag++;
                    }else{
                        inValidFlag++;
                    }
                }
            }
            if (obj.getFyggList() != null && obj.getFyggList().size() > 0){
                List<CaseSearchMongoFyggListEntity> list = obj.getFyggList();
                for (CaseSearchMongoFyggListEntity item : list){
                    if (item.getIsValid() == 1){
                        validFlag++;
                    }else{
                        inValidFlag++;
                    }
                }
            }
            if (obj.getSdggList() != null && obj.getSdggList().size() > 0){
                List<CaseSearchMongoSdggListEntity> list = obj.getSdggList();
                for (CaseSearchMongoSdggListEntity item : list){
                    if (item.getIsValid() == 1){
                        validFlag++;
                    }else{
                        inValidFlag++;
                    }
                }
            }
            if (obj.getLianList() != null && obj.getLianList().size() > 0){
                List<CaseSearchMongoLianListEntity> list = obj.getLianList();
                for (CaseSearchMongoLianListEntity item : list){
                    if (item.getIsValid() == 1){
                        validFlag++;
                    }else{
                        inValidFlag++;
                    }
                }
            }
            if (obj.getCaseList() != null && obj.getCaseList().size() > 0){
                List<CaseSearchMongoCaseListEntity> list = obj.getCaseList();
                for (CaseSearchMongoCaseListEntity item : list){
                    if (item.getIsValid() == 1){
                        validFlag++;
                    }else{
                        inValidFlag++;
                    }
                }
            }
            if (obj.getPcczList() != null && obj.getPcczList().size() > 0){
                List<CaseSearchMongoPcczListEntity> list = obj.getPcczList();
                for (CaseSearchMongoPcczListEntity item : list){
                    if (item.getIsValid() == 1){
                        validFlag++;
                    }else{
                        inValidFlag++;
                    }
                }
            }
            if (obj.getXjpgList() != null && obj.getXjpgList().size() > 0){
                List<CaseSearchMongoXjpgListEntity> list = obj.getXjpgList();
                for (CaseSearchMongoXjpgListEntity item : list){
                    if (item.getIsValid() == 1){
                        validFlag++;
                    }else{
                        inValidFlag++;
                    }
                }
            }
            if (obj.getGqdjList() != null && obj.getGqdjList().size() > 0){
                List<CaseSearchMongoGqdjListEntity> list = obj.getGqdjList();
                for (CaseSearchMongoGqdjListEntity item : list){
                    if (item.getIsValid() == 1){
                        validFlag++;
                    }else{
                        inValidFlag++;
                    }
                }
            }
            if (obj.getHbcfList() != null && obj.getHbcfList().size() > 0){
                List<CaseSearchMongoHbcfListEntity> list = obj.getHbcfList();
                for (CaseSearchMongoHbcfListEntity item : list){
                    if (item.getIsValid() == 1){
                        validFlag++;
                    }else{
                        inValidFlag++;
                    }
                }
            }
            if (obj.getCfgsList() != null && obj.getCfgsList().size() > 0){
                List<CaseSearchMongoCfgsListEntity> list = obj.getCfgsList();
                for (CaseSearchMongoCfgsListEntity item : list){
                    if (item.getIsValid() == 1){
                        validFlag++;
                    }else{
                        inValidFlag++;
                    }
                }
            }
            if (obj.getCfxyList() != null && obj.getCfxyList().size() > 0){
                List<CaseSearchMongoCfxyListEntity> list = obj.getCfxyList();
                for (CaseSearchMongoCfxyListEntity item : list){
                    if (item.getIsValid() == 1){
                        validFlag++;
                    }else{
                        inValidFlag++;
                    }
                }
            }
            if (obj.getCfdfList() != null && obj.getCfdfList().size() > 0){
                List<CaseSearchMongoCfdfListEntity> list = obj.getCfdfList();
                for (CaseSearchMongoCfdfListEntity item : list){
                    if (item.getIsValid() == 1){
                        validFlag++;
                    }else{
                        inValidFlag++;
                    }
                }
            }
        }

        if (validFlag == 0 && inValidFlag == 1){
            result = 1;
        }

        return result;
    }

    public static void main(String[] args) {
        String json ="{\"KtggCnt\":0,\"ZxCnt\":5,\"XjpgCnt\":0,\"LastestDateType\":\"首次执行|限制高消费发布日期\",\"CfgsCnt\":0,\"LastestDate\":1532275200,\"EarliestDate\":1495756800,\"AnnoCnt\":2,\"EarliestDateType\":\"民事一审|判决日期\",\"CompanyKeywords\":\"p5bc77b7ef938c4cb2a233afed9ac592,pd104a1586767ec134995b6d646a417b,何三英,李彦,邓建辉,gbe47569f8007fcddff1d6d2a748d09d,广州市从化区人民法院,李卫东,李桂松,郑敬房\",\"AnNoList\":\"（2017）粤0184民初1289号,（2017）粤0184执2597号\",\"GqdjCnt\":0,\"GroupCourt\":\"从化市人民法院\",\"XgCnt\":2,\"FyggCnt\":0,\"ZbCnt\":2,\"LatestTrialRound\":\"首次执行\",\"CfdfCnt\":0,\"CfxyCnt\":0,\"CaseName\":\"李彦与邓建辉、何三英借款合同纠纷\",\"SxCnt\":2,\"Province\":\"GD\",\"LianCnt\":0,\"CaseCnt\":2,\"PcczCnt\":0,\"HbcfCnt\":0,\"ProcuratorateList\":\"\",\"CaseType\":\"民事案件,执行案件\",\"CaseRole\":\"[{\\\"P\\\":\\\"李彦\\\",\\\"R\\\":\\\"原告\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2},{\\\"P\\\":\\\"邓建辉\\\",\\\"R\\\":\\\"被告\\\",\\\"N\\\":\\\"pd104a1586767ec134995b6d646a417b\\\",\\\"O\\\":2},{\\\"P\\\":\\\"何三英\\\",\\\"R\\\":\\\"被告\\\",\\\"N\\\":\\\"p5bc77b7ef938c4cb2a233afed9ac592\\\",\\\"O\\\":2}]\",\"CaseReason\":\"借款合同纠纷\",\"CourtList\":\"从化市人民法院,广州市从化区人民法院\",\"SdggCnt\":0,\"InfoList\":[{\"Defendant\":[{\"Role\":\"被告\",\"KeyNo\":\"pd104a1586767ec134995b6d646a417b\",\"Org\":2,\"Name\":\"邓建辉\"},{\"Role\":\"被告\",\"KeyNo\":\"p5bc77b7ef938c4cb2a233afed9ac592\",\"Org\":2,\"Name\":\"何三英\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"从化市人民法院\",\"ZxList\":[],\"LatestTimestamp\":1495756800,\"HbcfList\":[],\"CfdfList\":[],\"CaseList\":[{\"JudgeDate\":1495756800,\"Id\":\"761204491e500608acb83e3998f11c610\",\"DocType\":\"民事判决日期\",\"IsValid\":1}],\"TrialRound\":\"民事一审\",\"Prosecutor\":[{\"Role\":\"原告\",\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"李彦\"}],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2017）粤0184民初1289号\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"借款合同纠纷\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]},{\"Defendant\":[{\"Role\":\"被执行人\",\"KeyNo\":\"pd104a1586767ec134995b6d646a417b\",\"Org\":2,\"Name\":\"邓建辉\"},{\"Role\":\"被执行人\",\"KeyNo\":\"p5bc77b7ef938c4cb2a233afed9ac592\",\"Org\":2,\"Name\":\"何三英\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"广州市从化区人民法院\",\"ZxList\":[{\"LianDate\":1500480000,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-1,\"Name\":\"李桂松\"}],\"Id\":\"3b89bce301c4745ccc5c52b18c20442d1\",\"IsValid\":0},{\"LianDate\":1500480000,\"NameAndKeyNo\":[{\"KeyNo\":\"p5bc77b7ef938c4cb2a233afed9ac592\",\"Org\":2,\"Name\":\"何三英\"}],\"Id\":\"2681b69a564f812b33c2c0d03328c7271\",\"IsValid\":0},{\"LianDate\":1500480000,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-1,\"Name\":\"李卫东\"}],\"Id\":\"4009b467207d0573c5c046fdfd3693c61\",\"IsValid\":0},{\"LianDate\":1500480000,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-1,\"Name\":\"郑敬房\"}],\"Id\":\"3be530317626f2fe6fc2347339f754781\",\"IsValid\":0},{\"LianDate\":1500480000,\"NameAndKeyNo\":[{\"KeyNo\":\"pd104a1586767ec134995b6d646a417b\",\"Org\":2,\"Name\":\"邓建辉\"}],\"Id\":\"1cb4aab1e8aa0da84dd625e0fcee327d1\",\"IsValid\":0}],\"LatestTimestamp\":1532275200,\"HbcfList\":[],\"CfdfList\":[],\"TrialRound\":\"首次执行\",\"CaseList\":[{\"JudgeDate\":1510617600,\"Id\":\"2fa8a2734980e89e99e6b76a681788890\",\"DocType\":\"执行裁定日期\",\"IsValid\":1}],\"Prosecutor\":[],\"ZbList\":[{\"JudgeDate\":1511452800,\"NameAndKeyNo\":[{\"KeyNo\":\"p5bc77b7ef938c4cb2a233afed9ac592\",\"Org\":2,\"Name\":\"何三英\"}],\"Id\":\"94013cba35303c6850523c1bca68fd04\",\"IsValid\":1},{\"JudgeDate\":1511452800,\"NameAndKeyNo\":[{\"KeyNo\":\"pd104a1586767ec134995b6d646a417b\",\"Org\":2,\"Name\":\"邓建辉\"}],\"Id\":\"9a628ad8917eb72c9c550e8305c644fd\",\"IsValid\":1}],\"ExecuteNo\":\"（2017）粤0184民初1289号\",\"SxList\":[{\"PublishDate\":1509984000,\"NameAndKeyNo\":[{\"KeyNo\":\"p5bc77b7ef938c4cb2a233afed9ac592\",\"Org\":2,\"Name\":\"何三英\"}],\"Id\":\"2681b69a564f812b33c2c0d03328c7272\",\"IsValid\":0},{\"PublishDate\":1509984000,\"NameAndKeyNo\":[{\"KeyNo\":\"pd104a1586767ec134995b6d646a417b\",\"Org\":2,\"Name\":\"邓建辉\"}],\"Id\":\"1cb4aab1e8aa0da84dd625e0fcee327d2\",\"IsValid\":0}],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2017）粤0184执2597号\",\"XgList\":[{\"PublishDate\":1532275200,\"NameAndKeyNo\":{\"KeyNo\":\"p5bc77b7ef938c4cb2a233afed9ac592\",\"Org\":2,\"Name\":\"何三英\"},\"XglNameAndKeyNo\":[{\"KeyNo\":\"p5bc77b7ef938c4cb2a233afed9ac592\",\"Org\":2,\"Name\":\"何三英\"}],\"Id\":\"71098f1b1ae3aeaaa5dd5e235ad485a8\",\"CompanyInfo\":{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"\"},\"GlNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"\"}],\"IsValid\":1},{\"PublishDate\":1532275200,\"NameAndKeyNo\":{\"KeyNo\":\"pd104a1586767ec134995b6d646a417b\",\"Org\":2,\"Name\":\"邓建辉\"},\"XglNameAndKeyNo\":[{\"KeyNo\":\"pd104a1586767ec134995b6d646a417b\",\"Org\":2,\"Name\":\"邓建辉\"}],\"Id\":\"fc3081cad57361ca9125ccf8fb065044\",\"CompanyInfo\":{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"\"},\"GlNameAndKeyNo\":[{\"KeyNo\":\"\",\"Org\":-2,\"Name\":\"\"}],\"IsValid\":1}],\"LianList\":[],\"CaseReason\":\"服务合同纠纷\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]}]}";
        CleanSingleDatailinfo datailinfo = new CleanSingleDatailinfo();
        int evaluate = datailinfo.evaluate(json);
        System.out.println(evaluate);
    }
}
