package com.qcc.udf.person;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

/**
 * @Auther: liulh
 * @Date: 2020/6/11 17:54
 * @Description:
 */
public class getBirthDay extends UDF {
    public static String evaluate(String birth, String person) {
        JSONArray array = new JSONArray();
        if (StringUtils.isNotEmpty(birth) && StringUtils.isNotEmpty(person)){
            Map<String, String> birthMap = new LinkedHashMap<>();
            JSONArray birthArray = JSONArray.parseArray(birth);
            Iterator<Object> it = birthArray.iterator();
            while (it.hasNext()){
                JSONObject json = (JSONObject)it.next();
                String birthDay = json.getString("birthDay");
                if (StringUtils.isNotEmpty(birthDay)){
                    birthMap.put(json.getString("name"), birthDay.replace("-",""));
                }
            }

            JSONArray personArray = JSONArray.parseArray(person);
            Iterator<Object> itPerson = personArray.iterator();
            while (itPerson.hasNext()){
                JSONObject json = (JSONObject)itPerson.next();
                if (birthMap.containsKey(json.getString("Name"))){
                    json.put("BirthDay", birthMap.get(json.getString("Name")));

                    array.add(json);
                }
            }
        }


        return array.toString();
    }

    public static void main(String[] args) {
        String a = "[{\"address\":{},\"birthDay\":\"\",\"name\":\"张某\",\"relatedCompanyName\":\"\",\"role\":\"原告\"},{\"address\":{},\"birthDay\":\"\",\"name\":\"蔡某\",\"relatedCompanyName\":\"\",\"role\":\"被告\"},{\"address\":{},\"birthDay\":\"\",\"name\":\"茹某（蔡某\",\"relatedCompanyName\":\"\",\"role\":\"委托诉讼代理人\"},{\"address\":{},\"birthDay\":\"\",\"name\":\"武涛\",\"relatedCompanyName\":\"北京城建兴华地产有限公司\",\"role\":\"第三人-法定代表人\"},{\"address\":{},\"birthDay\":\"2021-07-16\",\"name\":\"李书国\",\"relatedCompanyName\":\"北京市土地整理储备中心顺义区分中心\",\"role\":\"第三人-法定代表人\"},{\"address\":{},\"birthDay\":\"\",\"name\":\"周靖慧\",\"relatedCompanyName\":\"北京市顺义区仁和镇人民政府\",\"role\":\"第三人-负责人\"},{\"address\":{},\"birthDay\":\"\",\"name\":\"张某诉称\",\"relatedCompanyName\":\"\",\"role\":\"原告\"},{\"address\":{},\"birthDay\":\"\",\"name\":\"蔡某\",\"relatedCompanyName\":\"\",\"role\":\"被告\"}] ";
        String b = "[{\"Name\":\"周靖慧\",\"Cerno\":\"pr6b1eb9da88b374edf20a9bd8bc9a21\"},{\"Name\":\"李书国\",\"Cerno\":\"pr1b6efe92ed3cbf5a2758ffbf73eaea\"},{\"Name\":\"武涛\",\"Cerno\":\"p593abd3bc57f2f53aefc8f65a903de1\"}]";
        System.out.println(evaluate(a,b));
    }

}
