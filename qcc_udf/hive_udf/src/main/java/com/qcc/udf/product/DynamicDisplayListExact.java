package com.qcc.udf.product;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class DynamicDisplayListExact extends UDF {
    public static String evaluate(String info) {
        List<RiskChangeListNews> resultList = new ArrayList<>();
        if (StringUtils.isEmpty(info)) {
            return JSON.toJSONString(resultList);
        }

        List<String> strings = JSON.parseArray(info, String.class);
        List<RiskChangeListNews> riskChangeListNews = strings.stream().map(v -> {
            return JSON.parseObject(v, RiskChangeListNews.class);
        }).collect(Collectors.toList());

        return JSON.toJSONString(riskChangeListNews);
    }
}
