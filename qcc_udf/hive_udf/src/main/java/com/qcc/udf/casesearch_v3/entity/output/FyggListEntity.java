package com.qcc.udf.casesearch_v3.entity.output;

import lombok.Data;
import com.alibaba.fastjson.annotation.JSONField;

import java.util.LinkedList;
import java.util.List;

@Data
public class FyggListEntity extends BaseCaseOutEntity {
    @JSONField(name = "Id")
    private String id = "";
    @JSONField(name = "PublishDate")
    private Long publishDate = 0L;
    @JSONField(name = "Category")
    private String category = "";
    @JSONField(name = "IsValid")
    private Integer isValid = 0;
    @JSONField(name = "NameAndKeyNo")
    private List<NameAndKeyNoEntity> nameAndKeyNo = new LinkedList<>();
    //受送达人
    @JSONField(name = "RecipientList")
    private List<NameAndKeyNoEntity> recipientList;
}
