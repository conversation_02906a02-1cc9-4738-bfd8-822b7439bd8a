package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * string转decimal 保留小数
 */
public class StringToDecimalUDF extends UDF {

    private static Pattern NUMBER_PATTERN = Pattern.compile("^(-?\\d+)(\\.\\d+)?$");

    public static String evaluate(String str , int num){
        Matcher isNum = NUMBER_PATTERN.matcher(str);
        if( isNum.matches() ){
            BigDecimal b = new BigDecimal(str);
            return b.setScale(num, RoundingMode.HALF_UP).toString();
        }else{
            return null;
        }
    }

    public static void main(String[] args) {
        System.out.println(evaluate("5", 2));
    }
}
