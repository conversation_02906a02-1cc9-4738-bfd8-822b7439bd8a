package com.qcc.udf.casesearch_v3.enums;

import lombok.Getter;

/**
 * @Auther: zhangqiang
 * @Date: 2020/12/14 14:55
 * @Description:裁判文书doc_type枚举
 */
@Getter
public enum  CaseDocTypeEnum {

    VERDICT("ver", "判决书", "判决日期", "判决结果"),
    ADJUDICATE("adj", "裁定书", "裁定日期", "裁定结果"),
    MEDIATION("med", "调解书", "调解日期", "调解结果"),
    DECISION("dec", "决定书", "决定日期", "决定结果"),
    NOTICE("not", "通知书", "通知日期", "通知结果"),
    REPLY("rep", "批复", "批复日期", "批复结果"),
    ANSWER("ans", "答复", "答复日期", "答复内容"),
    LETTER("let", "函", "发函日期", "发函内容"),
    MAKE("mak", "令", "发令日期", "发令内容"),
    OTHER("other", "其他", "裁判日期", "");


    CaseDocTypeEnum(String code,String type,String dateDesc,String resultDesc) {
        this.code = code;
        this.type = type;
        this.dateDesc = dateDesc;
        this.resultDesc = resultDesc;
    }
    private String code;
    private String type;
    private String dateDesc;
    private String resultDesc;

    public static CaseDocTypeEnum getCaseDocByCode(String code){
        for (CaseDocTypeEnum value : CaseDocTypeEnum.values()) {
            if(value.getCode().equals(code)){
                return value;
            }
        }
        return null;
    }

}
