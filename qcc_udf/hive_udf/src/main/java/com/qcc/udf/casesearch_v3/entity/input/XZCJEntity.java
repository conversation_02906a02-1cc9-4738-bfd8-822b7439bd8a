package com.qcc.udf.casesearch_v3.entity.input;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.casesearch_v3.enums.CaseCategoryEnum;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 限制出境UDF入参
 */
@Data
public class XZCJEntity extends BaseCaseEntity {
    /**
     * {
     *     "id":"0f4c3ce89dda9ccbd704f0a7d5e58899",
     *     "companynames":"包夏香",
     *     "anno":"（2020）浙1023执2461号",
     *     "provincecode":"ZJ",
     *     "courtname":"浙江省台州市天台县人民法院",
     *     "publishdate":-1,
     *     "isvalid":1,
     *     "registerdate":1595174400,
     *     "keynojsonperslimited":"[{"KeyNo":"","Name":"包夏香","Org":2}]",
     *     "keynojsonexecuted":"[{"KeyNo":"","Name":"包夏香","Org":2}]",
     *     "keynojsonapply":"",
     *     "executedamount":"50000.00"
     * }
     */

    private String id;
    private String companynames;
    private String anno;
    private String provincecode;
    private String courtname;
    /**
     * 发布日期
     */
    private long publishdate;
    private int isvalid;
    /**
     *立案时间
     */
    private long registerdate;
    /**
     *限制出境对象json
     */
    private String keynojsonperslimited;
    /**
     *被执行人json
     */
    private String keynojsonexecuted;
    /**
     *申请执行人json
     */
    private String keynojsonapply;
    /**
     *执行标的金额
     */
    private String executedamount;


    /**
     *限制出境对象json
     */
    private List<NameAndKeyNoEntity> keynojsonperslimitedEntityList;
    /**
     *被执行人json
     */
    private List<NameAndKeyNoEntity> keynojsonexecutedEntityList;
    /**
     *申请执行人json
     */
    private List<NameAndKeyNoEntity> keynojsonapplyEntityList;


    public static List<XZCJEntity> convert(List<String> jsonList) {
        List<XZCJEntity> list = new ArrayList<>();
        XZCJEntity entity = null;
        if(CollectionUtils.isEmpty(jsonList)){
            return list;
        }
        if(CollectionUtils.isEmpty(jsonList)){
            return list;
        }
        for (String json : jsonList) {
            if(Strings.isNullOrEmpty(json)){
                continue;
            }
            entity = JSON.parseObject(json, XZCJEntity.class);
            if(entity == null  || Strings.isNullOrEmpty(entity.getId())){
                continue;
            }

            List<NameAndKeyNoEntity> nameAndKeyNoEntityList = new ArrayList<>();

            String str = entity.getKeynojsonperslimited();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setKeynojsonperslimitedEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getKeynojsonperslimitedEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                }
                nameAndKeyNoEntityList.addAll(entity.getKeynojsonperslimitedEntityList());
            }
            str = entity.getKeynojsonexecuted();
            if (!Strings.isNullOrEmpty(str)) {
                entity.setKeynojsonexecutedEntityList(JSON.parseArray(str, NameAndKeyNoEntity.class));
                for (NameAndKeyNoEntity namekey : entity.getKeynojsonexecutedEntityList()) {
                    namekey.setOrg(CommonV3Util.getOrgByKeyNo(namekey.getKeyNo(),namekey.getName()));
                }
                nameAndKeyNoEntityList.addAll(entity.getKeynojsonexecutedEntityList());
            }

            //公共字段赋值
            entity.setBaseCaseNo(entity.getAnno());
            entity.setBaseCaseCategoryEnum(CaseCategoryEnum.XZCJ);
            entity.setBaseCourt(entity.getCourtname());
            if(!Strings.isNullOrEmpty(entity.getCompanynames())){
                entity.setBaseSearchWordSet(Arrays.stream(entity.getCompanynames().split(","))
                        .collect(Collectors.toSet()));
            }

            entity.setBaseProvinceCode(entity.getProvincecode());
            entity.setBaseNameKeyNoList(nameAndKeyNoEntityList);
            entity.setBaseId(entity.getBaseCaseCategoryEnum().getType()+"_"+entity.getId());

            String caseType= CommonV3Util.getCaseType(CommonV3Util.getCaseNo(entity.getBaseCaseNo()));
            //案件类型为空的数据直接过滤
            if(Strings.isNullOrEmpty(caseType)){
                continue;
            }
            list.add(entity);
        }
        return list;
    }
}
