package com.qcc.udf.cpws.wjp;

import cn.hutool.core.util.ReUtil;
import com.google.common.collect.Lists;
import com.qcc.udf.cpws.CommonUtil;
import com.qcc.udf.temp.MD5Util;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Desc:案号标准化
 */
public class CaseNoCleanUtil extends UDF {
    private static final List<String> CASE_TYPE_LIST = Lists.newArrayList("行辖", "行辖终", "行初", "行终", "行申", "行抗", "行审", "行审复", "行他", "赔辖", "赔辖终",
            "刑辖", "刑初", "刑终", "刑监", "刑申", "刑抗", "刑再", "刑没", "刑核", "刑医", "刑医解", "刑医复", "刑医监", "刑止", "刑止调", "刑止核", "刑更", "刑更监", "刑更备", "刑他",
            "民辖", "民辖终", "民辖监", "民初", "民终", "民监", "民申", "民抗", "民再", "民撤", "民特", "民特监", "民催", "民督", "民督监", "民破", "民算", "民他",
            "行赔初", "行赔终", "行赔监", "行赔申", "行赔抗", "行赔再", "法赔", "委赔", "委赔监", "委赔提", "委赔再", "赔他", "司救刑", "司救民", "司救行", "司救赔", "司救执", "司救访", "司救他",
            "认台", "认港", "认澳", "认复", "认他", "请台送", "请港送", "请澳送", "台请送", "港请送", "澳请送", "请台调", "请港调", "请澳调", "台请调", "港请调", "澳请调", "请移管", "助移管", "请移赃", "助移赃", "协外认", "协他", "协外送", "请外送", "协外调", "请外调", "请外移", "协外移", "请外引", "协外引",
            "司惩", "司惩复", "财保", "行保", "行保复", "证保", "执", "执恢", "执保", "执异", "执复", "执监", "执协", "执他", "民", "司救", "刑", "辖", "商", "破");

    public static String evaluate(String caseno) {
        Set<String> caseNoMd5List = getCommonHandler(caseno).stream().map(MD5Util::encode).collect(Collectors.toSet());
        return String.join(",", caseNoMd5List);
    }

    public static Set<String> getCommonHandler(String caseNo) {
        if (StringUtils.isEmpty(caseNo)) {
            return new HashSet<>();
        }
        caseNo = CommonUtil.full2Half(caseNo);
        caseNo = caseNo.replaceAll(" | |　", "");
        caseNo = caseNo.replaceAll("\\[|【|〔|﹝|<", "(");
        caseNo = caseNo.replaceAll("\\]|】|〕|﹞|>", ")");
        caseNo = caseNo.replaceAll("\r\n|\n", "");
        caseNo = caseNo.replaceAll("`|\\.|\\^|★", "");
        boolean dunHaoFlag = canSplitDunHao(caseNo);
        String split = ",";
        if (dunHaoFlag) {
            split = split + "|、";
        }
        String[] annos = caseNo.split(split);
        Set<String> annoSet = Arrays.stream(annos)
                .filter(anno -> StringUtils.isNotBlank(anno))
                .map(anno -> parseCaseNoStandard(anno))
                .map(anno -> numberHandle(anno))
                .map(anno -> fixErrPrefix(anno))
                .collect(Collectors.toSet());

        if (CollectionUtils.isNotEmpty(annoSet)) {
            return annoSet;
        } else {
            return new HashSet<>();
        }
    }

    static boolean canSplitDunHao(String caseNo) {
        String[] annos = caseNo.split("、");
        if (annos.length == 1) {
            return true;
        }
        for (String anno : annos) {
            if (anno.length() < 10 || StringUtils.isEmpty(caseTypeByCaseNo(anno))) {
                return false;
            }
        }
        return true;
    }

    private static int getAnnoYear(String anno) {
        String year = ReUtil.get("\\d{4}", anno, 0);
        if (StringUtils.isNotBlank(year) && StringUtils.isNumeric(year)) {
            return Integer.parseInt(year);
        }
        return 0;
    }

    private static String numberHandle(String annoRes) {
        if (annoRes.contains("之")) {
            annoRes = annoRes.substring(0, annoRes.lastIndexOf("之"));
        }
        String rule6 = ".*\\d+号?$";
        boolean match = ReUtil.isMatch(rule6, annoRes);
        if (!match) {
            return annoRes;
        }
        annoRes = new StringBuffer(annoRes).reverse().toString();
        String numberRes = ReUtil.get("\\d+", annoRes, 0);
        numberRes = numberRes.replaceAll("0+$", "");
        annoRes = numberRes + ReUtil.delPre("\\d+", annoRes);
        annoRes = new StringBuffer(annoRes).reverse().toString();
        return annoRes.endsWith("号") ? annoRes : annoRes + "号";
    }


    public static String fixErrPrefix(String anno) {
        int year = getAnnoYear(anno);
        //15年之前案号不处理
        if (year < 2016) {
            return anno;
        }
        if (anno.length() < 6) {
            return anno;
        }
        //(2021苏0585民初8267号
        String prefix = anno.substring(0, 6);
        boolean match = ReUtil.isMatch("\\(\\d{4}[\\u4e00-\\u9fa5]", prefix);
        String newAnno = "";
        if (match) {
            newAnno = anno.substring(0, 5) + ")" + anno.substring(5);
        }


        //2021)苏0585民初8267号
        prefix = anno.substring(0, 5);
        match = ReUtil.isMatch("\\d{4}\\)", prefix);
        if (match && "".equals(newAnno)) {
            newAnno = "(" + prefix + anno.substring(5);
        }

        //2021年苏0585民初8267号
        match = ReUtil.isMatch("\\d{4}\\年", prefix);
        if (match && "".equals(newAnno)) {
            newAnno = "(" + prefix.substring(0, 4) + ")" + anno.substring(5);
        }

        //2021苏0585民初8267号
        prefix = anno.substring(0, 4);
        match = ReUtil.isMatch("\\d{4}", prefix);
        if (match && "".equals(newAnno)) {
            newAnno = "(" + prefix + ")" + anno.substring(4);
        }
        if ("".equals(newAnno)) {
            newAnno = anno;
        }

        return newAnno;

    }

    /**
     * Desc:标准化案号清洗
     */
    public static String parseCaseNoStandard(String caseNo) {
        //左右括号缺失补充
        caseNo = addParentheses(caseNo);
        //去除非第一个左右括号
        caseNo = removeByRegex(removeByRegex(caseNo, "\\(", 1), "\\)", 1);
        //去除 年|副本|字第|？
        caseNo = caseNo.replaceAll("年|-副本号|副本号|-副本|副本|\\?", "");
        //号-书去除 示例：（2014）年朝执字第11491号强制执行裁定书
        String matchStr1 = parseRegex(caseNo, "号.{0,}书$", -1);
        if (StringUtils.isNotEmpty(matchStr1)) {
            caseNo = caseNo.replace(matchStr1, "号");
        }
        if (!caseNo.endsWith("号")) {
            caseNo = caseNo + "号";
        }
        //号-号处理 示例：（2020）辽0502执22号-4号
        String matchStr2 = parseRegex(caseNo, "[0-9]{1,}号-[0-9]{1,}号$|[0-9]{1,}号[0-9]{1,}号$", -1);
        if (StringUtils.isNotEmpty(matchStr2)) {
            String[] nums = matchStr2.replace("-", "").split("号");
            if (nums.length == 2 && nums[0].length() < 10 && nums[1].length() < 10) {
                int one = Integer.parseInt(nums[0]);
                int two = Integer.parseInt(nums[1]);
                if (one >= two) {
                    caseNo = caseNo.replaceAll("号-[0-9]{1,}号$|号[0-9]{1,}号$", "号");
                }
            }
        }
        return caseNo;
    }

    /**
     * Desc:去除中间括号
     */
    public static String removeByRegex(String input, String regexStr, int index) {
        Pattern pattern = Pattern.compile(regexStr);
        Matcher matcher = pattern.matcher(input);
        StringBuffer result = new StringBuffer();

        int count = 0;
        while (matcher.find()) {
            if (count >= index) {
                matcher.appendReplacement(result, "");
            }
            count++;
        }
        matcher.appendTail(result);

        return result.toString();
    }

    /**
     * Desc:补充左右括号
     */
    public static String addParentheses(String caseNo) {
        if (StringUtils.isNotEmpty(caseNo)) {
            String matchStr1 = parseRegex(caseNo, "^\\(20\\d{2}\\)|^\\(19\\d{2}\\)", -1);
            if (StringUtils.isEmpty(matchStr1)) {
                String matchStr2 = parseRegex(caseNo, "^20\\d{2}\\)|^19\\d{2}\\)", -1);
                if (StringUtils.isNotEmpty(matchStr2)) {
                    caseNo = "(" + caseNo;
                } else {
                    String matchStr3 = parseRegex(caseNo, "^\\(20\\d{2}|^\\(19\\d{2}", -1);
                    if (StringUtils.isNotEmpty(matchStr3)) {
                        caseNo = caseNo.replace(matchStr3, matchStr3 + ")");
                    } else {
                        String matchStr4 = parseRegex(caseNo, "^20\\d{2}|^19\\d{2}", -1);
                        if (StringUtils.isNotEmpty(matchStr4)) {
                            caseNo = caseNo.replace(matchStr4, "(" + matchStr4 + ")");
                        }
                    }
                }
            }
        }
        return caseNo;
    }

    /**
     * Desc:返回第index匹配到的数据，index小于0时，返回第一个
     *
     * @param str
     * @param regex
     * @param index
     * @return
     */
    public static String parseRegex(final String str, final String regex, final int index) {
        if (StringUtils.isEmpty(str)) {
            return "";
        }
        final Pattern p = Pattern.compile(regex);
        final Matcher m = p.matcher(str);
        if (index < 0) {
            return m.find() ? m.group() : null;
        }
        return m.find() ? m.group(index) : null;
    }

    public static String caseTypeByCaseNo(String caseNo) {
        try {
            if (StringUtils.isBlank(caseNo)) {
                return "";
            }

            for (String searchKey : CASE_TYPE_LIST) {
                if (caseNo.contains(searchKey)) {
                    return caseNo;
                }
            }

            // 民事案件（知识产权案件）
            if (caseNo.contains("知") && !caseNo.contains("行")) {
                return "民事案件";
            }
            // 行政案件
            if (caseNo.contains("行") && !caseNo.contains("赔") && !caseNo.contains("救") && !caseNo.contains("保")) {
                return "行政案件";
            }

            if (caseNo.contains("保") && !caseNo.contains("执")) {
                return "保全案件";
            }

            //清申 强清
            if (caseNo.contains("清申") || caseNo.contains("强清")) {
                return "民事案件";
            }
            //诉前调
            if (caseNo.contains("诉前调") && !caseNo.contains("刑")) {
                return "民事案件";
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return "";
    }

    public static void main(String[] args) {

        System.out.println(getCommonHandler("（2020）粤1322执3012号"));
        System.out.println(getCommonHandler("（2022）赣0123执1107号之一"));
        System.out.println(getCommonHandler("（2019）内0627执恢14号之六"));


    }
}
