package com.qcc.udf.temp;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * 案件类型
 */
public enum PermissionTypeEnum {
    TYPE_0(0,""),
    TYPE_1(1,"普通"),
    TYPE_11(1,"普通许可"),
    TYPE_2(2,"特许"),
    TYPE_3(3,"许可"),
    TYPE_4(4,"核准"),
    TYPE_5(5,"登记"),
    TYPE_6(6,"其他"),
    TYPE_61(6,"其他（备注注明）"),
    TYPE_UNKNOWN(-1,"未知"),

    ;


    private int type;
    private String name;

    PermissionTypeEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }


    public int gettype() {
        return type;
    }

    public String getName() {
        return name;
    }

    private static final Map<String, PermissionTypeEnum> lookup = new HashMap<String, PermissionTypeEnum>();

    static {
        for (PermissionTypeEnum e : EnumSet.allOf(PermissionTypeEnum.class)) {
            lookup.put(e.name, e);
        }
    }

    public static PermissionTypeEnum find(String name) {
        PermissionTypeEnum data = lookup.get(name);
        if (name == null) {
            return null;
        }
        return data;
    }
}
