package com.qcc.udf.cpws.wjp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.stream.Collectors;

public class CleanPartyFlagUDF extends UDF {
    public static void main(String[] args) {
        System.out.println(evaluate("[{\"Role\":\"原告\",\"KeyNo\":\"\",\"LawFirmList\":[],\"RoleTag\":0,\"Name\":\"王欢\"}, {\"Role\":\"被告\",\"KeyNo\":\"\",\"LawFirmList\":[],\"RoleTag\":1,\"Name\":\"李明丽\"}, {\"Role\":\"被告\",\"KeyNo\":\"\",\"LawFirmList\":[],\"RoleTag\":1,\"Name\":\"鲁旭\"}, {\"Role\":\"被告\",\"KeyNo\":\"2ee3d1888f1ea13c57b59f40744ce3dd\",\"LawFirmList\":[{\"LawFirm_KeyNo\":\"\",\"LawFirm_Name\":\"\",\"LawyerList\":[{\"Lawyer_Name\":\"王艳玲\",\"Lawyer_Role\":\"委托代理人\",\"Lawyer_KeyNo\":\"\"}]},{\"LawFirm_KeyNo\":\"\",\"LawFirm_Name\":\"\",\"LawyerList\":[{\"Lawyer_Name\":\"熊新生\",\"Lawyer_Role\":\"委托代理人\",\"Lawyer_KeyNo\":\"\"}]}],\"RoleTag\":1,\"Name\":\"信达财产保险股份有限公司唐山中心支公司\"}, {\"Role\":\"被告\",\"KeyNo\":\"\",\"Type\":1,\"SupName\":\"李某某\",\"SupShowName\":\"李**\",\"LawFirmList\":[],\"RoleTag\":1,\"Name\":\"李某某\"}]"
                , "原告王欢诉被告李明丽、鲁旭、信达财产保险股份有限公司唐山中心支公司、中国人民财产保险股份有限公司唐山市分公司财产损害赔偿纠纷一审民事判决书"
                , null
        ));
    }

    public static String evaluate(String caseRole, String title, String content) {
        if (StringUtils.isNotEmpty(caseRole)) {
            JSONArray array = JSON.parseArray(caseRole);
            Set<String> names = new HashSet<>();
            for (Object o : array) {
                JSONObject jsonObject = (JSONObject) o;
                names.add(jsonObject.getString("Name"));
            }
            names = names.stream().sorted(Comparator.comparing(String::length)).collect(Collectors.toCollection(LinkedHashSet::new));
            List<JSONObject> list = new ArrayList<>();
            for (String name : names) {
                JSONObject map = new JSONObject();
                int type = -1;
                if (StringUtils.isNotEmpty(title) && title.contains(name)) {
                    type = 0;
                }
                if (StringUtils.isNotEmpty(content) && content.contains(name)) {
                    if (type == 0) {
                        type = 2;
                    } else {
                        type = 1;
                    }
                }
                map.put("name", name);
                map.put("type", type);
                list.add(map);
            }
            for (JSONObject jsonObject : list) {
                String s = jsonObject.getString("name");
                int t = jsonObject.getInteger("type");
                if (s.length() < 5 && s.matches(".*[某×xX*ｘＸ✘✖ΧχⅹХх].*")) {
                    String s2[] = s.split("[某×xX*ｘＸ✘✖ΧχⅹХх]");

                    Set<String> likeName = new HashSet<>();
                    for (JSONObject jsonObject2 : list) {
                        String s1 = jsonObject2.getString("name");
                        int t1 = jsonObject2.getInteger("type");
                        if (!s1.equals(s) && t1 != t && t != 2 && t1 != 2) {
                            boolean isLike = true;
                            for (String s3 : s2) {
                                if (!s1.contains(s3)) {
                                    isLike = false;
                                }
                            }
                            if (isLike) {
                                likeName.add(s1);
                            }
                        }
                    }
                    if (CollectionUtils.isNotEmpty(likeName)) {
                        jsonObject.put("likeName", String.join(",", likeName));
                    }
                }

            }

            return list.toString();
        }
        return "";

    }


}
