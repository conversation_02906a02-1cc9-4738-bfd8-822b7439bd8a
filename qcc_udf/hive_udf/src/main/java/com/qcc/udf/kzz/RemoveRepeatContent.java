package com.qcc.udf.kzz;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 去除重复内容
 * <AUTHOR>
 * @date 2022/3/15
 */
public class RemoveRepeatContent  extends UDF {

    public static String evaluate(String content,String split) {

        if(StringUtils.isNotBlank(content)){
            Set<String> stringSet =new HashSet<>();
            String[] array = content.split(split);
            if(null!=array && array.length>0){
                for (String str : array) {
                    if(StringUtils.isNotBlank(str)){
                        stringSet.add(str);
                    }
                }
            }
            return stringSet.stream().collect(Collectors.joining(split));
        }else{
            return "";
        }
    }
//
//    public static void main(String[] args){
//        String content = "广东省,佛山市,顺德区,广东省,深圳市,龙岗区,广东省,东莞市,,广东省,深圳市,宝安区,广东省,江门市,鹤山市,广东省,佛山市,南海区,广东省,深圳市,光明区,广东省,中山市,,江西省,萍乡市,安源区,广东省,江门市,新会区,广东省,惠州市,惠阳区,广东省,江门市,蓬江区,广东省,江门市,江海区,广东省,广州市,番禺区,广东省,佛山市,三水区,广东省,广州市,黄埔区,上海市,上海市,松江区,广东省,广州市,从化区";
//        System.out.println(evaluate(content,","));
//    }
}
