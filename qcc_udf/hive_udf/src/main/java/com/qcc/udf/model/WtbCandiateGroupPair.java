package com.qcc.udf.model;

import com.alibaba.fastjson.annotation.JSONField;

public class WtbCandiateGroupPair {

    @JSONField(name = "Key1")
    private String key1;

    @JSONField(name = "Name1")
    private String name1;

    @JSONField(name = "Key2")
    private String key2;

    @JSO<PERSON>ield(name = "Name2")
    private String name2;


    public String getKey1() {
        return key1;
    }

    public void setKey1(String key1) {
        this.key1 = key1;
    }

    public String getName1() {
        return name1;
    }

    public void setName1(String name1) {
        this.name1 = name1;
    }

    public String getKey2() {
        return key2;
    }

    public void setKey2(String key2) {
        this.key2 = key2;
    }

    public String getName2() {
        return name2;
    }

    public void setName2(String name2) {
        this.name2 = name2;
    }
}