package com.qcc.udf.company;

import com.google.common.base.Objects;
import com.google.common.collect.ImmutableMap;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;


/**
 * <AUTHOR>
 * @date 2022-10-14 10:56
 */
public class getCompanyLevelDivide2 extends UDF {

    private static final String COMPANY_T_LARGE = "大型企业";
    private static final String COMPANY_T_MIDDLE = "中型企业";
    private static final String COMPANY_T_SMALL = "小型企业";
    private static final String COMPANY_T_MICRO = "微型企业";
    private static final String COMPANY_T_UNKNOW = "未知";

    private static final Map<String, String> COMPANY_T_DOWN_MAP = new ImmutableMap.Builder<String, String>()
            .put(COMPANY_T_LARGE, COMPANY_T_MIDDLE).put(COMPANY_T_MIDDLE, COMPANY_T_SMALL).put(COMPANY_T_SMALL, COMPANY_T_MICRO).put(COMPANY_T_MICRO, COMPANY_T_MICRO).build();

    Map<String, Map<String, ArrayList<Long>>> jsondata = getjson();
    private static final CompanyClassFyQFK classFyQFK = new CompanyClassFyQFK();

    /**
     * 获取企业划分
     *
     * @param companyType      根据财务数据计算的企业规模
     * @param registCapi       注册资本
     * @param isSm             是否为小微企业
     * @param isFin            是否为金融企业
     * @param hasFinancialData 是否为有公司财务数据
     * @param econKind         企业类型
     * @param startDate        成立日期
     * @param isZjtx           是否是专精特新中小企业、专精特新小巨人企业、科技型中小企业
     * @param employeeCount    是否有企业人数
     * @param tags             企业标签
     * @param baseFincial      是否是企业维度下的财务数据
     * @return
     */
    public String evaluate(String companyType, double registCapi, int isSm, int isFin, int hasFinancialData, String econKind, String startDate, int isZjtx, int employeeCount, String tags, int baseFincial, String companyTypeWithoutEmap, String industryCode, String industryThree, int employeeCountReal, int revenue_num, int total_assets_num) {
        //根据企业主页下的财务数据计算的结果直接使用
        //有财务数据，有员工数据，上市公司，根据官方标准计算的结果作为最终结果
        if (isFin == 1 && StringUtils.isNotEmpty(companyType)) {
            return companyType;
        }
        //金融企业不用其他逻辑
        List<String> array2 = Arrays.asList("IPO", "上市辅导标签", "融资标签", "有近三年的百强榜单", "公布的央企国企");
        List<String> array3 = Arrays.asList("专精特新中小企业", "专精特新小巨人企业", "科技型中小企业");

        if (hasFinancialData == 1 && baseFincial == 1 && employeeCount == 1 && tags.contains("上市公司") && StringUtils.isNotEmpty(companyType)) {
            return companyType;
        }
        //有财务数据,有员工数 非上市公司，
        if (hasFinancialData == 1 && baseFincial == 1 && employeeCount == 1 && StringUtils.isNotEmpty(companyType)) {
            //存在ipo相关标签
            return getCompanyType111(companyType, registCapi, econKind, tags, array2, isFin);
        }

        if (hasFinancialData == 1 && baseFincial == 1 && employeeCount == 0) {
            String companyTypeSecond = getCompanyType110(companyType, registCapi, isFin, econKind, tags, array2, array3, industryCode, industryThree, revenue_num, total_assets_num);
            if (StringUtils.isNotEmpty(companyTypeSecond)) {
                return companyTypeSecond;
            }
        }

        if (hasFinancialData == 1 && baseFincial == 0) {
            String companyTypeResult = getCompanyType10x(companyType, registCapi, isSm, isFin, econKind, employeeCount, tags, industryCode, industryThree, revenue_num, total_assets_num, array2, array3);
            if (StringUtils.isNotEmpty(companyTypeResult)) {
                return companyTypeResult;
            }
        }

        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if ((StringUtils.isEmpty(startDate)) || (StringUtils.isNotEmpty(startDate) && startDate.compareTo(df.format(LocalDateTime.now().minusYears(1))) < 0)) {
            return getCompanyTypeBeforeOneYear(registCapi, isSm, isFin, tags, industryCode, industryThree, employeeCountReal, array2, array3);

        }
        if (StringUtils.isNotEmpty(startDate) && startDate.compareTo(df.format(LocalDateTime.now().minusYears(1))) >= 0) {
            return getStartLessThanOneYear(companyType, registCapi, isSm, isFin, tags, array2, array3);
        }
        return COMPANY_T_UNKNOW;
    }

    private String getStartLessThanOneYear(String companyType, double registCapi, int isSm, int isFin, String tags, List<String> array2, List<String> array3) {
        if (array2.stream().anyMatch(tags::contains)) {
            if (COMPANY_T_LARGE.equals(companyType) || COMPANY_T_MIDDLE.equals(companyType)) {
                return COMPANY_T_MIDDLE;
            } else {
                return getCompanyTypeByRegistCapi(registCapi, isFin);
            }
        } else if (array3.stream().anyMatch(tags::contains)) {
            return getCompanyTypeByRegistCapi(registCapi, isFin);

        } else if (isSm == 1 || tags.contains("小微企业")) {
            String companyTSmall = getSmallMicroWithRegistCapi(registCapi, isFin);
            if (companyTSmall != null) {
                return companyTSmall;
            }
        }

        String companyTypeByRegi = getCompanyTypeByRegi(registCapi, 10000, 500, 100);

        if (Objects.equal(COMPANY_T_MICRO, companyTypeByRegi) || Objects.equal(COMPANY_T_SMALL, companyTypeByRegi)) {
            return companyTypeByRegi;
        }
        return COMPANY_T_UNKNOW;
    }

    private String getCompanyTypeBeforeOneYear(double registCapi, int isSm, int isFin, String tags, String industryCode, String industryThree, int employeeCountReal, List<String> array2, List<String> array3) {
        if (tags.contains("上市公司") || array2.stream().anyMatch(tags::contains)) {
            return COMPANY_T_MIDDLE;
        }
        if (array3.stream().anyMatch(tags::contains)) {
            //非金融业
            if (isFin == 0) {

                String evaluate = classFyQFK.getCompanyTypeByClassifyTwoAndEmployee(industryCode, industryThree, Double.parseDouble(String.valueOf(employeeCountReal)));
                if (COMPANY_T_LARGE.equals(evaluate) || COMPANY_T_MIDDLE.equals(evaluate)) {
                    return COMPANY_T_MIDDLE;
                }
                if (registCapi <= 500) {
                    return COMPANY_T_SMALL;
                }
                return COMPANY_T_MIDDLE;

            } else {
                if (registCapi <= 1000000) {
                    return COMPANY_T_SMALL;
                }
                return COMPANY_T_MIDDLE;

            }

        }
        if (isSm == 1 || tags.contains("小微企业")) {
            if (array3.stream().noneMatch(tags::contains)) {
                String companyTSmall = getSmallMicroWithRegistCapi(registCapi, isFin);
                if (companyTSmall != null) {
                    return companyTSmall;
                }
            }
        }

        if (isFin == 1) {
            return getCompanyTypeByRegi(registCapi, 10000000, 1000000, 100000);
        } else {
            String companyTypeByRegi = getCompanyTypeByRegi(registCapi, 10000, 500, 100);
            //根据注册资本判断，如果是小微直接使用
            if (COMPANY_T_SMALL.equals(companyTypeByRegi) || COMPANY_T_MICRO.equals(companyTypeByRegi)) {
                return companyTypeByRegi;
            } else {

                if (classFyQFK.getCompanyNeedEmployee(industryCode, industryThree) && employeeCountReal > 0) {
                    //根据行业取到对应行业的人数限制，如果满足，则以当前结果，否则降级
                    String companyTypeByClassifyTwoAndEmployee = classFyQFK.getCompanyTypeByClassifyTwoAndEmployee(industryCode, industryThree, Double.parseDouble(String.valueOf(employeeCountReal)));
                    if (Objects.equal(companyTypeByRegi, companyTypeByClassifyTwoAndEmployee)) {
                        return companyTypeByRegi;
                    }
                    return COMPANY_T_DOWN_MAP.getOrDefault(companyTypeByRegi, COMPANY_T_UNKNOW);

                }
                return companyTypeByRegi;
            }
        }
    }

    private String getSmallMicroWithRegistCapi(double registCapi, int isFin) {
        if (isFin == 0) {
            if (registCapi > 100) {
                return COMPANY_T_SMALL;
            } else if (registCapi <= 100) {
                return COMPANY_T_MICRO;
            }
        }
        if (registCapi > 100000) {
            return COMPANY_T_SMALL;
        } else if (registCapi <= 100000) {
            return COMPANY_T_MICRO;
        }
        return null;
    }

    private String getCompanyType10x(String companyType, double registCapi, int isSm, int isFin, String econKind, int employeeCount, String tags, String industryCode, String industryThree, int revenue_num, int total_assets_num, List<String> array2, List<String> array3) {
        if (employeeCount == 1) {
            return getCompanyType101(companyType, registCapi, isSm, isFin, econKind, tags, array2, array3);
        }
        if (isFin == 1) {
            return getCompanyTypeByRegi(registCapi, 10000000, 1000000, 100000);
        }

        if (isFin == 0) {
            //有财务数据，没有员工数，重新计算财务数据对应的公司规模，如果行业有人员要求，则降级
            CompanyTypeConf companyType2 = getCompanyTypeWithEmployeeZero(industryCode, industryThree, revenue_num, total_assets_num);
            if (StringUtils.isEmpty(companyType2.getCompanyType())) {
                return companyType2.getCompanyType();
            }
            companyType = companyType2.getCompanyType();

        }
        if (array2.stream().anyMatch(tags::contains) || array3.stream().anyMatch(tags::contains)) {
            //财务数据符合中大型企业，则处理成为“中型”；否则默认为“小型”

            if (COMPANY_T_LARGE.equals(companyType) || COMPANY_T_MIDDLE.equals(companyType)) {
                return COMPANY_T_MIDDLE;
            }
            return COMPANY_T_SMALL;
        }
        if (classFyQFK.getCompanyNeedEmployee(industryCode, industryThree)) {
            return COMPANY_T_DOWN_MAP.getOrDefault(companyType, COMPANY_T_UNKNOW);
        }
        return companyType;
    }

    private String getCompanyType101(String companyType, double registCapi, int isSm, int isFin, String econKind, String tags, List<String> array2, List<String> array3) {
        if (array2.stream().anyMatch(tags::contains)) {
            if (COMPANY_T_LARGE.equals(companyType) || COMPANY_T_MIDDLE.equals(companyType)) {
                return companyType;
            }
            if (COMPANY_T_SMALL.equals(companyType)) {
                return COMPANY_T_MIDDLE;
            }
            if (COMPANY_T_MICRO.equals(companyType)) {
                return COMPANY_T_SMALL;
            }
        }
        if (array3.stream().anyMatch(tags::contains)) {
            if (COMPANY_T_MICRO.equals(companyType) || COMPANY_T_SMALL.equals(companyType)) {
                return COMPANY_T_SMALL;
            } else {
                return COMPANY_T_MIDDLE;
            }

        } else {

            if (StringUtils.isNotEmpty(econKind) && Pattern.matches(".*(个人独资|自然人独资|合作社|专业合作|全民所有|集体所有|集体企业|集体经济).*", econKind) && COMPANY_T_LARGE.equals(companyType)) {
                return COMPANY_T_MIDDLE;
                //非金融企业判断为大型企业，需要满足注册资本
            }
            //有财务数据，有员工数，判断不是大型企业，如果有小微企业标签，则将companyType降级
            else if (isSm == 1 && !(COMPANY_T_MICRO.equals(companyType) || COMPANY_T_SMALL.equals(companyType))) {
                return COMPANY_T_DOWN_MAP.getOrDefault(companyType, COMPANY_T_UNKNOW);
            } else if (isFin == 0 && COMPANY_T_LARGE.equals(companyType)) {
                if (registCapi <= 10000) {
                    return COMPANY_T_MIDDLE;
                } else {
                    return COMPANY_T_LARGE;
                }
            }
            return companyType;

        }
    }

    private String getCompanyType110(String companyType, double registCapi, int isFin, String econKind, String tags, List<String> array2, List<String> array3, String industyCode, String industryCodeThree, int revenue_num, int total_assets_num) {
        if (isFin == 0) {
            //有财务数据，没有员工数，重新计算财务数据对应的公司规模，如果行业有人员要求，则降级
            CompanyTypeConf companyType2 = getCompanyTypeWithEmployeeZero(industyCode, industryCodeThree, revenue_num, total_assets_num);
            if (StringUtils.isEmpty(companyType2.getCompanyType())) {
                return companyType2.getCompanyType();
            }
            companyType = companyType2.getCompanyType();
        }

        if (array2.stream().anyMatch(tags::contains)) {
            if (StringUtils.isNotEmpty(econKind) && Pattern.matches(".*(个人独资|自然人独资|合作社|专业合作|全民所有|集体所有|集体企业|集体经济).*", econKind) && COMPANY_T_LARGE.equals(companyType)) {
                return COMPANY_T_MIDDLE;
            } else if ((COMPANY_T_LARGE.equals(companyType) && isFin == 0)) {
                if (registCapi <= 10000) {
                    return COMPANY_T_MIDDLE;
                }
            }
            return companyType;
        } else if (array3.stream().anyMatch(tags::contains)) {
            if (COMPANY_T_LARGE.equals(companyType) || COMPANY_T_MIDDLE.equals(companyType)) {
                return COMPANY_T_MIDDLE;
            }
            return COMPANY_T_SMALL;
        } else if (classFyQFK.getCompanyNeedEmployee(industyCode, industryCodeThree)) {
            return COMPANY_T_DOWN_MAP.getOrDefault(companyType, COMPANY_T_UNKNOW);
        }
        return companyType;
    }

    private CompanyTypeConf getCompanyTypeWithEmployeeZero(String industryCode, String industryCodeThree, int revenue_num, int total_assets_num) {
        String companyType2 = StringUtils.EMPTY;
        boolean allMatah = false;
        if (revenue_num > 0 && total_assets_num > 0) {
            allMatah = true;

            companyType2 = classFyQFK.getCompanyTypeByClassifyTwoWithOutEmployee(industryCode, industryCodeThree, Double.parseDouble(String.valueOf(revenue_num)), Double.parseDouble(String.valueOf(total_assets_num)));
        } else if (revenue_num > 0) {
            companyType2 = classFyQFK.getCompanyTypeWithRevenueNum(industryCode, industryCodeThree, Double.parseDouble(String.valueOf(revenue_num)));
        } else if (total_assets_num > 0) {
            companyType2 = classFyQFK.getCompanyTypeWithTotal(industryCode, industryCodeThree, Double.parseDouble(String.valueOf(total_assets_num)));
        }
        if (Objects.equal("出错", companyType2) || Objects.equal("待定", companyType2)) {
            companyType2 = StringUtils.EMPTY;
        }

        return new CompanyTypeConf(companyType2, allMatah, String.join("", classFyQFK.getIndustryConf(industryCode, industryCodeThree)));
    }

    private String getCompanyType111(String companyType, double registCapi, String econKind, String tags, List<String> array2, int isFin) {
        if (array2.stream().anyMatch(tags::contains)) {
            if (COMPANY_T_LARGE.equals(companyType) || COMPANY_T_MIDDLE.equals(companyType)) {
                return companyType;
            }
            if (COMPANY_T_SMALL.equals(companyType)) {
                return COMPANY_T_MIDDLE;
            }
            if (COMPANY_T_MICRO.equals(companyType)) {
                return COMPANY_T_SMALL;
            }

        }
        if (StringUtils.isNotEmpty(econKind) && Pattern.matches(".*(个人独资|自然人独资|合作社|专业合作|全民所有|集体所有|集体企业|集体经济).*", econKind) && COMPANY_T_LARGE.equals(companyType)) {
            return COMPANY_T_MIDDLE;
        } else if ((COMPANY_T_LARGE.equals(companyType) && isFin == 0)) {
            if (registCapi <= 10000) {
                return COMPANY_T_MIDDLE;
            }
        }
        return companyType;


    }

    private String getCompanyTypeByRegistCapi(double regist, int isFin) {

        if (isFin == 1) {
            if (regist <= 1000000) {
                return COMPANY_T_SMALL;
            }
            return COMPANY_T_MIDDLE;
        } else {
            if (regist <= 500) {
                return COMPANY_T_SMALL;
            }
            return COMPANY_T_MIDDLE;

        }
    }

    private String getCompanyTypeByRegi(double registCapi, int i, int b, int i2) {
        if (registCapi > i) {
            return "大型企业";
        } else if (registCapi > b) {
            return "中型企业";
        } else if (registCapi > i2) {
            return "小型企业";
        } else {
            return "微型企业";
        }
    }


    public static Map<String, Map<String, ArrayList<Long>>> getjson() {
        String jsonstring = "{\"01\":{\"y\":[20000,500,50]},\"02\":{\"y\":[20000,500,50]},\"03\":{\"y\":[20000,500,50]},\"04\":{\"y\":[20000,500,50]},\"05\":{\"y\":[20000,500,50]},\"06\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"07\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"08\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"09\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"10\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"11\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"12\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"13\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"14\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"15\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"16\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"17\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"18\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"19\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"20\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"21\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"22\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"23\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"24\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"25\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"26\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"27\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"28\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"29\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"30\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"31\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"32\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"33\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"34\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"35\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"36\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"37\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"38\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"39\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"40\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"41\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"42\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"43\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"44\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"45\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"46\":{\"x\":[1000,300,20],\"y\":[40000,2000,300]},\"47\":{\"y\":[80000,6000,300],\"z\":[80000,5000,300]},\"48\":{\"y\":[80000,6000,300],\"z\":[80000,5000,300]},\"49\":{\"y\":[80000,6000,300],\"z\":[80000,5000,300]},\"50\":{\"y\":[80000,6000,300],\"z\":[80000,5000,300]},\"51\":{\"x\":[200,20,5],\"y\":[40000,5000,1000]},\"52\":{\"x\":[300,50,10],\"y\":[20000,500,100]},\"54\":{\"x\":[1000,300,20],\"y\":[30000,3000,200]},\"55\":{\"x\":[1000,300,20],\"y\":[30000,3000,200]},\"56\":{\"x\":[1000,300,20],\"y\":[30000,3000,200]},\"57\":{\"x\":[1000,300,20],\"y\":[30000,3000,200]},\"58\":{\"x\":[1000,300,20],\"y\":[30000,3000,200]},\"591\":{\"x\":[1000,300,20],\"y\":[30000,3000,200]},\"592\":{\"x\":[200,100,20],\"y\":[30000,1000,100]},\"593\":{\"x\":[200,100,20],\"y\":[30000,1000,100]},\"594\":{\"x\":[200,100,20],\"y\":[30000,1000,100]},\"595\":{\"x\":[200,100,20],\"y\":[30000,1000,100]},\"596\":{\"x\":[200,100,20],\"y\":[30000,1000,100]},\"599\":{\"x\":[200,100,20],\"y\":[30000,1000,100]},\"60\":{\"x\":[1000,300,20],\"y\":[30000,2000,100]},\"61\":{\"x\":[300,100,10],\"y\":[10000,2000,100]},\"62\":{\"x\":[300,100,10],\"y\":[10000,2000,100]},\"63\":{\"x\":[2000,100,10],\"y\":[100000,1000,100]},\"64\":{\"x\":[2000,100,10],\"y\":[100000,1000,100]},\"65\":{\"x\":[300,100,10],\"y\":[10000,1000,50]},\"701\":{\"y\":[200000,1000,100],\"z\":[10000,5000,2000]},\"702\":{\"x\":[1000,300,100],\"y\":[5000,1000,500]},\"71\":{\"x\":[300,100,10],\"z\":[120000,8000,100]},\"72\":{\"x\":[300,100,10],\"z\":[120000,8000,100]},\"200\":{\"x\":[300,100,10]}}";
        GsonBuilder builder = new GsonBuilder();
        builder.registerTypeAdapter(new TypeToken<Map<String, Map<String, ArrayList<Long>>>>() {
        }.getType(), new CompanyClassFyQFK.MapTypeAdapter());
        Gson gson = builder.create();
        Map<String, Map<String, ArrayList<Long>>> result = gson.fromJson(jsonstring,
                new TypeToken<Map<String, Map<String, ArrayList<Long>>>>() {
                }.getType());
        return result;
    }

    public static void main(String[] args) {
        getCompanyLevelDivide2 getCompanyLevelDivide = new getCompanyLevelDivide2();
        String evaluate = getCompanyLevelDivide.evaluate("中型企业", 20400.00057220459, 1, 0, 1, "有限责任公司（外国法人独资）",
                "1995-05-05", 1, 0, "有近三年的百强榜单,小微企业", 0, "大型企业", "33", "331", 0, 65, 264);
        System.out.println(evaluate);

    }

    @Data
    public static class CompanyTypeConf {
        private String companyType;
        private Boolean allMatch;
        private String confStr;

        public CompanyTypeConf(String companyType, Boolean allMatch, String confStr) {
            this.companyType = companyType;
            this.allMatch = allMatch;
            this.confStr = confStr;
        }
    }
}
