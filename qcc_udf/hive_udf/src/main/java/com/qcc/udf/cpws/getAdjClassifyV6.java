package com.qcc.udf.cpws;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.LinkedHashSet;
import java.util.Set;

public class getAdjClassifyV6 extends UDF {
    public String evaluate(String caseName, String result, String caseNo) {
        String code = "";

        caseNo = caseNo == null ? "" : caseNo;
        caseName = caseName == null ? "" : caseName;
        result = result == null ? "" : CommonUtil.full2Half(result);

        Set<String> firstCodeSet = new LinkedHashSet<>();
        Set<String> codeSet = new LinkedHashSet<>();
        if (result.contains("冻结") || result.contains("查封") || result.contains("扣押") ||
                result.contains("提取") || result.contains("扣留") || result.contains("保全") || caseName.contains("财产保全")){
            codeSet.add("保全");
            firstCodeSet.add("保全裁定");
        }
        if (result.contains("解除") && (result.contains("冻结") || result.contains("查封") || result.contains("扣押") ||
                result.contains("提取") || result.contains("扣留") || result.contains("保全"))){
            codeSet.add("解除保全");
            firstCodeSet.add("保全裁定");
        }
        if (result.contains("驳回") && result.contains("起诉")){
            codeSet.add("驳回起诉");
            firstCodeSet.add("驳回裁定");
        }
        if (result.contains("驳回") && result.contains("上诉")){
            codeSet.add("驳回上诉");
            firstCodeSet.add("驳回裁定");
        }
        if (result.contains("驳回") && result.contains("的申请")){
            codeSet.add("驳回申请");
            firstCodeSet.add("驳回裁定");
        }
        if (result.contains("驳回") && result.contains("再审申请")){
            codeSet.add("驳回再审");
            firstCodeSet.add("驳回裁定");
        }
        if (result.contains("驳回") && result.contains("复议")){
            codeSet.add("驳回复议");
            firstCodeSet.add("驳回裁定");
        }
        if ((caseNo.contains("初") || caseNo.contains("受")) && result.contains("不予受理")){
            codeSet.add("不予受理");
            firstCodeSet.add("不予受理");
        }
        if ((caseNo.contains("监") || caseNo.contains("终") || caseNo.contains("再")) && ((result.contains("撤销") && result.contains("裁定")) || result.contains("本院再审"))){
            codeSet.add("撤销裁定,本院再审");
            firstCodeSet.add("撤销裁定");
        }
        if ((caseNo.contains("监") || caseNo.contains("终") || caseNo.contains("再")) && result.contains("撤销") && result.contains("指令") && (result.contains("重审") || result.contains("审理")) ){
            codeSet.add("撤销裁定,指令审理");
            firstCodeSet.add("撤销裁定");
        }
        if ((caseNo.contains("监") || caseNo.contains("终") || caseNo.contains("再")) && result.contains("撤销") && result.contains("判决")){
            codeSet.add("撤销原判");
            firstCodeSet.add("撤销裁定");
        }
        if ((caseNo.contains("监") || caseNo.contains("终") || caseNo.contains("再")) && result.contains("撤销") && result.contains("判决") && result.contains("驳回") && result.contains("起诉")){
            codeSet.add("撤销原判,驳回起诉");
            firstCodeSet.add("撤销裁定");
        }
        if ((caseNo.contains("监") || caseNo.contains("终") || caseNo.contains("再")) && result.contains("撤销") && result.contains("判决") && result.contains("发回") && result.contains("重审")){
            codeSet.add("撤销原判,发回重审");
            firstCodeSet.add("撤销裁定");
        }
        if ((caseNo.contains("监") || caseNo.contains("申") || caseNo.contains("再")) && result.contains("提审")){
            codeSet.add("提审");
            firstCodeSet.add("撤销裁定");
        }
        if (result.contains("撤销") && (result.contains("仲裁") || result.contains("裁决")) ){
            codeSet.add("撤销仲裁");
            firstCodeSet.add("撤销裁定");
        }
        if ((caseNo.contains("初") || caseNo.contains("管")) && (((result.contains("管辖权") && result.contains("异议")) || caseName.contains("管辖")))){
            codeSet.add("管辖权异议");
            firstCodeSet.add("管辖权裁定");
        }
        if (caseNo.contains("初") && (result.contains("处理") || result.contains("管辖") || result.contains("审理")) && result.contains("本案")){
            codeSet.add("移送管辖");
            firstCodeSet.add("管辖权裁定");
        }
        if ((caseNo.contains("初") || caseNo.contains("管")) && result.contains("驳回") && result.contains("管辖权") && result.contains("异议")){
            codeSet.add("驳回管辖权异议");
            firstCodeSet.add("管辖权裁定");
        }
        if (result.contains("中止诉讼") || result.contains("中止审理")){
            codeSet.add("中止诉讼");
            firstCodeSet.add("中止、恢复或终结诉讼");
        }
        if (result.contains("恢复") && (result.contains("审理") || result.contains("诉讼"))){
            codeSet.add("恢复诉讼");
            firstCodeSet.add("中止、恢复或终结诉讼");
        }
        if (result.contains("终结诉讼")){
            codeSet.add("终结诉讼");
            firstCodeSet.add("中止、恢复或终结诉讼");
        }
        if (result.contains("终结撤销")){
            codeSet.add("终结撤销");
            firstCodeSet.add("中止、恢复或终结诉讼");
        }
        if (result.contains("终结审查")){
            codeSet.add("终结审查");
            firstCodeSet.add("中止、恢复或终结诉讼");
        }
        if (result.contains("中止") && result.contains("执行")){
            codeSet.add("中止执行");
            firstCodeSet.add("中止或终结执行");
        }
        if (result.contains("终结") && result.contains("执行")){
            codeSet.add("终结执行");
            firstCodeSet.add("中止或终结执行");
        }
        if ((caseNo.contains("申") || caseNo.contains("提")) && result.contains("准许") && result.contains("撤回") && result.contains("再审申请")){
            codeSet.add("准许撤回再审申请");
            firstCodeSet.add("准许或不准许撤诉");
        }
        if ((caseNo.contains("初")  || caseNo.contains("终") || caseNo.contains("特") ) && result.contains("准") && ((result.contains("撤回") && (result.contains("起诉") || result.contains("上诉")))) || result.contains("撤诉")){
            codeSet.add("准许撤诉");
            firstCodeSet.add("准许或不准许撤诉");
        }
        if ((caseNo.contains("初")  || caseNo.contains("终") || caseNo.contains("特")) &&
                result.contains("本案") && (result.contains("按")  || result.contains("裁定") || result.contains("作")) &&
                ((result.contains("撤回") && (result.contains("起诉") || result.contains("上诉"))) || (result.contains("撤诉")) || (result.contains("自动放弃")) || (result.contains("自动撤回")))
                ){
            codeSet.add("按撤诉处理");
            firstCodeSet.add("准许或不准许撤诉");
        }
        if (result.contains("应为") || result.contains("补正") || result.contains("更正") || caseName.contains("笔误")){
            codeSet.add("补正");
            firstCodeSet.add("补正");
        }
        if (caseNo.contains("督") && result.contains("支付令")){
            codeSet.add("支付令");
            firstCodeSet.add("督促程序");
        }
        if (caseNo.contains("督") && result.contains("终结") && result.contains("督促程序")){
            codeSet.add("督促程序终结");
            firstCodeSet.add("督促程序");
        }
        if (caseNo.contains("保令")){
            codeSet.add("人身保护令");
            firstCodeSet.add("人身保护令");
        }
        if (caseNo.contains("破") && (result.contains("破产程序") || caseName.contains("破产") || caseName.contains("破产清算"))){
            codeSet.add("破产程序");
            firstCodeSet.add("破产程序");
        }
        if (caseNo.contains("催") && (((result.contains("终止") || result.contains("破产")) && result.contains("公示催告程序")) || caseName.contains("公示催告"))){
            codeSet.add("公示催告");
            firstCodeSet.add("公示催告");
        }
        if ((result.contains("并入") && (result.contains("案件审理") || result.contains("一并裁判") || result.contains("审理"))) || (result.contains("并案审理"))){
            codeSet.add("并案裁定");
            firstCodeSet.add("并案裁定");
        }
        if ((result.contains("转入") || result.contains("转为") || result.contains("转") || result.contains("适用")) && result.contains("普通程序")){
            codeSet.add("适用普通程序");
            firstCodeSet.add("普通程序与简易程序");
        }
        if ((result.contains("转入") || result.contains("转为") || result.contains("转") || result.contains("适用")) && result.contains("简易程序")){
            codeSet.add("适用简易程序");
            firstCodeSet.add("普通程序与简易程序");
        }
        if ((caseNo.contains("监") || caseNo.contains("申")) && result.contains("再审")){
            codeSet.add("再审裁定");
            firstCodeSet.add("再审裁定");
        }
        if (caseNo.contains("特") && (result.contains("拍卖") || result.contains("变卖")) && result.contains("所得") && result.contains("费")){
            codeSet.add("拍卖执行");
            firstCodeSet.add("其他");
        }
        if ((caseNo.contains("调") || caseNo.contains("特")) && result.contains("调解") && result.contains("有效")) {
            codeSet.add("调解裁定");
            firstCodeSet.add("其他");
        }

        if (firstCodeSet.size() > 0){
            Set<String> numCodeSet = new LinkedHashSet<>();
            for (String item : firstCodeSet){
                if ("保全裁定".equals(item)){
                    numCodeSet.add("01");
                }
                if ("驳回裁定".equals(item)){
                    numCodeSet.add("02");
                }
                if ("不予受理".equals(item)){
                    numCodeSet.add("03");
                }
                if ("撤销裁定".equals(item)){
                    numCodeSet.add("04");
                }
                if ("管辖权裁定".equals(item)){
                    numCodeSet.add("05");
                }
                if ("中止、恢复或终结诉讼".equals(item)){
                    numCodeSet.add("06");
                }
                if ("中止或终结执行".equals(item)){
                    numCodeSet.add("07");
                }
                if ("准许或不准许撤诉".equals(item)){
                    numCodeSet.add("08");
                }
                if ("补正".equals(item)){
                    numCodeSet.add("09");
                }
                if ("督促程序".equals(item)){
                    numCodeSet.add("10");
                }
                if ("人身保护令".equals(item)){
                    numCodeSet.add("11");
                }
                if ("破产程序".equals(item)){
                    numCodeSet.add("12");
                }
                if ("公示催告".equals(item)){
                    numCodeSet.add("13");
                }
                if ("并案裁定".equals(item)){
                    numCodeSet.add("14");
                }
                if ("普通程序与简易程序".equals(item)){
                    numCodeSet.add("15");
                }
                if ("再审裁定".equals(item)){
                    numCodeSet.add("16");
                }
                if ("其他".equals(item)){
                    numCodeSet.add("99");
                }
            }
            code = String.join(",", numCodeSet);
        }else{
            code = "99";
        }

        return code;
    }

    public static void main(String[] args) {
        System.out.println(new getAdjClassifyV6().evaluate("", "冻结", ""));
    }
}
