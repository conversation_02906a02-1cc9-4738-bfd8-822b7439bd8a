package com.qcc.udf.risk_graph;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 风险数据详情
 * @date 2021/3/4
 */
@Data
public class RiskDataInfo2 {
    /**
     * 数据主键
     */
    @JSONField(name = "Id")
    private String id;

    /**
     * 风险维度名称
     */
    @JSONField(name = "WdName")
    private String wdName;

    /**
     * 风险关系名
     */
    @JSONField(name = "SortDate")
    private Long sortDate;

    /**
     * 风险维度: WGCL,CCJC,DWDB. 无id的数据放details
     */
    @JSONField(name = "Details")
    private String details;

    @JSONField(name = "RtName")
    private String rtName;

}
