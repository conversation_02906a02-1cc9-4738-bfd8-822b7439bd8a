package com.qcc.udf.kzz;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 提取招投标标段
 * <AUTHOR>
 * @date 2022/4/20
 */
public class GetTenderSubBlock extends UDF {
    private static final Pattern PATTERN =Pattern.compile("(第)?(\\w+|十?[一|二|三|四|五|六|七|八|九])?(\\w、)*\\w*((\\w|\\-)*(\\w|十|、)*[一|二|三|四|五|六|七|八|九|I|II|III|IV|V|VI|VII|VIII|IX])?(标段)(\\w+|十?[一|二|三|四|五|六|七|八|九])?");

    /**
     *
     * @param title
     * @param type 1-提取标段,2-提取除标段外的标题内容
     * @return
     */
    public static String evaluate(String title,int type) {
        String result="";
        try{
            if(StringUtils.isNotBlank(title)){
                Matcher matcher = PATTERN.matcher(title);
                if(matcher.find()){
                    if(type ==1 ){
                        result = matcher.group();
                        if(result.startsWith("-")||result.startsWith("_")){
                            result = result.substring(1);
                        }
                        if("标段".equals(result)){
                            result="";
                        }
                    }else{
                        result = title.replace(matcher.group(),"");
                    }
                }else{
                    if(type ==1){
                        result = "";
                    }else{
                        result = title;
                    }
                }
            }
        }catch (Exception e){

        }
       return result;
    }
//    public static void main(String[] args){
//        String content ="玉屏县舞阳河重点中型灌区节水配套改造工程施工III标中标候选人公示";
//        String tels =  evaluate(content,1);
//        System.out.println(tels);
//    }
}
