package com.qcc.udf;

import org.apache.commons.lang.ArrayUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.io.UnsupportedEncodingException;

/**
 * 将数字拆分成1到参数的有序数组
 * <AUTHOR>
 *
 */
public class NumSplitUDF extends UDF {

    public static String evaluate(int num)  {
       int[] nums = new int[num];
        for (int i = 0; i < num ; i++) {
            nums[i] = i+1;
        }
        return ArrayUtils.toString(nums, ",");
    }

    /*public static void main(String[] args) throws UnsupportedEncodingException {
        System.out.println(evaluate(200));
    }*/

}
