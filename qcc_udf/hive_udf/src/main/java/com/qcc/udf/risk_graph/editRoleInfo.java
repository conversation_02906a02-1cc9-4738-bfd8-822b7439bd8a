package com.qcc.udf.risk_graph;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.Set;

/**
 * @Auther: liulh
 * @Date: 2020/6/11 17:54
 * @Description:
 */
public class editRoleInfo extends UDF {
    public static String evaluate(String startKeyNo, String endKeyNo, String info) throws Exception{
        Set<String> resultSet = new LinkedHashSet<>();
       if(StringUtils.isNotEmpty(info)){
           JSONArray itemArray = JSONArray.parseArray(info);

           Iterator<Object> it = itemArray.iterator();
           while (it.hasNext()){
               JSONObject jsonObject = (JSONObject)it.next();
               String wdName = jsonObject.getString("WdName");
               int forwardFlag = jsonObject.getIntValue("ForwardFlag");

               // 供应商/客户
               if ("JYXX".equals(wdName)){
                   String rtName = jsonObject.getString("RtName");

                   if ("GYS".equals(rtName)){
                       if (forwardFlag == 0){
                           if (StringUtils.isNotEmpty(startKeyNo)){
                               resultSet.add(startKeyNo.concat("_2"));
                           }
                           if (StringUtils.isNotEmpty(endKeyNo)){
                               resultSet.add(endKeyNo.concat("_1"));
                           }
                       }
                       if (forwardFlag == 1){
                           if (StringUtils.isNotEmpty(startKeyNo)){
                               resultSet.add(startKeyNo.concat("_1"));
                           }
                           if (StringUtils.isNotEmpty(endKeyNo)){
                               resultSet.add(endKeyNo.concat("_2"));
                           }
                       }
                   }
                   if ("KH".equals(rtName)){
                       if (forwardFlag == 0){
                           if (StringUtils.isNotEmpty(startKeyNo)){
                               resultSet.add(startKeyNo.concat("_1"));
                           }
                           if (StringUtils.isNotEmpty(endKeyNo)){
                               resultSet.add(endKeyNo.concat("_2"));
                           }
                       }
                       if (forwardFlag == 1){
                           if (StringUtils.isNotEmpty(startKeyNo)){
                               resultSet.add(startKeyNo.concat("_2"));
                           }
                           if (StringUtils.isNotEmpty(endKeyNo)){
                               resultSet.add(endKeyNo.concat("_1"));
                           }
                       }
                   }
               }
               // 投资信息
               if ("TZXX".equals(wdName)){
                   JSONArray infoArray = JSONArray.parseArray(jsonObject.getString("Details"));
                   JSONObject json = infoArray.getJSONObject(0);
                   if (forwardFlag == 0){
                       resultSet.add(json.getJSONObject("StartInfo").getString("KeyNo").concat("_3"));
                       resultSet.add(json.getJSONObject("EndInfo").getString("KeyNo").concat("_4"));
                   }
                   if (forwardFlag == 1){
                       resultSet.add(json.getJSONObject("StartInfo").getString("KeyNo").concat("_4"));
                       resultSet.add(json.getJSONObject("EndInfo").getString("KeyNo").concat("_3"));
                   }
               }
               // 任职信息
               if ("RZXX".equals(wdName)){
                   JSONArray infoArray = JSONArray.parseArray(jsonObject.getString("Details"));
                   JSONObject json = infoArray.getJSONObject(0);
                   if (startKeyNo != null && startKeyNo.startsWith("p")){
                       if (json.getInteger("IsMain") != null && json.getInteger("IsMain") == 1){
                           resultSet.add(startKeyNo.concat("_5"));
                       }
                       if (json.getInteger("IsMain") != null && json.getInteger("IsMain") == 2){
                           resultSet.add(startKeyNo.concat("_6"));
                       }
                       if (json.getInteger("IsMain") != null && json.getInteger("IsMain") == 3){
                           resultSet.add(startKeyNo.concat("_5"));
                           resultSet.add(startKeyNo.concat("_6"));
                       }
                   }
                   if (endKeyNo != null && endKeyNo.startsWith("p")){
                       if (json.getInteger("IsMain") != null && json.getInteger("IsMain") == 1){
                           resultSet.add(endKeyNo.concat("_5"));
                       }
                       if (json.getInteger("IsMain") != null && json.getInteger("IsMain") == 2){
                           resultSet.add(endKeyNo.concat("_6"));
                       }
                       if (json.getInteger("IsMain") != null && json.getInteger("IsMain") == 3){
                           resultSet.add(endKeyNo.concat("_5"));
                           resultSet.add(endKeyNo.concat("_6"));
                       }
                   }

                   if (startKeyNo != null && !startKeyNo.startsWith("p")){
                       if (json.getInteger("IsMain") != null && json.getInteger("IsMain") == 1){
                           resultSet.add(startKeyNo.concat("_7"));
                       }
                       if (json.getInteger("IsMain") != null && json.getInteger("IsMain") == 2){
                           resultSet.add(startKeyNo.concat("_8"));
                       }
                       if (json.getInteger("IsMain") != null && json.getInteger("IsMain") == 3){
                           resultSet.add(startKeyNo.concat("_7"));
                           resultSet.add(startKeyNo.concat("_8"));
                       }
                   }
                   if (endKeyNo != null && !endKeyNo.startsWith("p")){
                       if (json.getInteger("IsMain") != null && json.getInteger("IsMain") == 1){
                           resultSet.add(endKeyNo.concat("_7"));
                       }
                       if (json.getInteger("IsMain") != null && json.getInteger("IsMain") == 2){
                           resultSet.add(endKeyNo.concat("_8"));
                       }
                       if (json.getInteger("IsMain") != null && json.getInteger("IsMain") == 3){
                           resultSet.add(endKeyNo.concat("_7"));
                           resultSet.add(endKeyNo.concat("_8"));
                       }
                   }
               }
           }
       }

       String result = "";
       if (resultSet.size() > 0){
           result = String.join(",", resultSet);
       }

        return result;
    }
}
