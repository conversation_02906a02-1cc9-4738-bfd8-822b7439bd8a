package com.qcc.udf.tag;

import org.apache.commons.lang3.StringUtils;

/**
 * 标签枚举
 */
public enum TagEnum {
    LD("劳动争议"),
    SW("税务风险"),
    ZL("产品质量问题"),
    ;


    private String name;

    TagEnum(String name) {
        this.name = name;
    }

    public static TagEnum getTagEnum(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        TagEnum tagEnum = null;
        try {
            tagEnum = TagEnum.valueOf(code.toUpperCase());
        } catch (Exception e) {

        }

        return tagEnum;
    }

    public static void main(String[] args) {
        String code = "";
        System.out.println(getTagEnum(code));
    }
}
