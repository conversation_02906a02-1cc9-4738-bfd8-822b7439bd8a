package com.qcc.udf.cpws.casesearch_v2.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 裁判文书清洗UDF：从案号中提取出对应的案件类型
 * ---------------------------------------------------------------------------------------------------------
 * create temporary function extractCaseType as 'com.qcc.udf.cpws.ExtractCaseTypeUDF' using jar 'hdfs:///data/hive/udf/qcc_udf.jar';
 * ---------------------------------------------------------------------------------------------------------
 * select extractCaseType ('（2018）xxxx');
 * 结果: '民事案件'
 */
public class CaseTypeUtil {
    private final static Map<String, String> caseNoToTypeMap;
    static {
        caseNoToTypeMap = new LinkedHashMap<>();
        try {
            Map<String, String> map = new HashMap<>();
            try (InputStream is = CaseTypeUtil.class.getResourceAsStream("/casenoToTypeMap.csv")) {
                BufferedReader br = new BufferedReader(new InputStreamReader(is));
                String line;
                while ((line = br.readLine()) != null) {
                    String[] splits = line.split("\t");
                    if (splits != null && splits.length == 2) {
                        String caseType = splits[0].trim();
                        String caseNoChar = splits[1].trim();
                        map.put(caseNoChar, caseType);
                    }
                }
                br.close();
            }

            List<String> keyList = map.keySet().stream().sorted((e1, e2) -> e2.length()-e1.length()).collect(Collectors.toList());
            for (String key : keyList) {
                caseNoToTypeMap.put(key, map.get(key));
            }
        } catch (Exception ex) {
        }
    }

    public String evaluate(String caseNo) {
        try {
            if (StringUtils.isBlank(caseNo)) {
                return "";
            }

            // 执行案件
            if (caseNo.contains("执")) {
                return "执行类案件";
            }

            for (String searchKey : caseNoToTypeMap.keySet()) {
                if (caseNo.contains(searchKey)) {
                    return caseNoToTypeMap.get(searchKey);
                }
            }
            // 根据指定关键字的出现，扩大范围做案件类型匹配
            // 民事案件
            if (caseNo.contains("民") && !caseNo.contains("司救")) {
                return "民事案件";
            }
            // 刑事案件
            if (caseNo.contains("刑") && !caseNo.contains("司救")) {
                return "刑事案件";
            }
            // 管辖案件
            if (caseNo.contains("辖")) {
                return "管辖案件";
            }
            // 民事案件（商事案件）
            if (caseNo.contains("商")) {
                return "民事案件";
            }
            // 民事案件（商事案件）
            if (caseNo.contains("破")) {
                return "民事案件";
            }
            // 民事案件（知识产权案件）
            if (caseNo.contains("知") && !caseNo.contains("行")) {
                return "民事案件";
            }
            // 行政案件
            if (caseNo.contains("行") && !caseNo.contains("赔") && !caseNo.contains("救") && !caseNo.contains("保")) {
                return "行政案件";
            }

            if (caseNo.contains("保") && !caseNo.contains("执")) {
                return "保全案件";
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return "";
    }

    public static void main(String[] args) {
        String evaluate = new CaseTypeUtil().evaluate("（2018）川0922民初2195号");
        System.out.println(evaluate);
    }
}
