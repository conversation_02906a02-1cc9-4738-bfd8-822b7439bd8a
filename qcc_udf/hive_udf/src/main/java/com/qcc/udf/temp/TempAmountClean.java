package com.qcc.udf.temp;

import cn.hutool.core.lang.Validator;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 * @since 2022-02-22
 *
 *  行政处罚
 *  爬虫 和 AI  金额 之差
 *  阈值比较
 *  >100000000 return 1
 *  否则 return 0
 */
public class TempAmountClean extends UDF {

    public int evaluate(String s) {

        if (StringUtils.isEmpty(s)){
            return 0;
        }
        JSONObject js = JSON.parseObject(s);

        String a = js.getString("A");
        BigDecimal spAmount = getUniformAmount(a);
        BigDecimal aiAmount = js.getBigDecimal("B");
        if (spAmount!=null&&aiAmount!=null){
            if (spAmount.subtract(aiAmount).abs().compareTo(new BigDecimal(100000000)) >= 0) {
                return 1;
            }
        }
        return 0;
    }
    public static BigDecimal getUniformAmount(String sAmount) {
        if (StringUtils.isEmpty(sAmount)) {
            return null;
        }
        BigDecimal decimal = null;
        String amount = sAmount.trim().replace(",", "").replace("，", "");
        try {
            if (Validator.hasChinese(amount)) {
                if (amount.contains("万元") || amount.contains("万")) {
                    decimal = BigDecimal.valueOf(Double.parseDouble(amount.replace("万元", "").replace("万", "")) * 10000);
                } else if (amount.contains("元")) {
                    decimal = BigDecimal.valueOf(Double.parseDouble(amount.replace("元", "")));
                }
            } else {
                //默认万元
                    decimal = BigDecimal.valueOf(Double.parseDouble(sAmount)*10000);
            }
        }catch (Exception e){
            //异常转换返回null
        }
        return decimal;
    }

    public static void main(String[] args) {
        TempAmountClean d = new TempAmountClean();
        System.out.println(d.evaluate("{\"A\":10000000000000,\"B\":1}"));
    }
}
