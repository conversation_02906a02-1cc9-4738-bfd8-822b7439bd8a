package com.qcc.udf.CommonService;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import jodd.util.StringUtil;
import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDF;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

@Description(name = "HiveToSEO", value = "_FUNC_(String keyNo); - Return remain count" +
        "推送每日清洗出KeyNo到SEO服务   仅限专门任务使用")
public class HiveToSEO extends UDF {


    public static String doPostToSEO(String requestUrl, String parms) throws IOException {
        URL url = new URL(requestUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setDoOutput(true);
        connection.setDoInput(true);
        connection.setRequestMethod("POST");
        connection.setUseCaches(false);
        connection.setInstanceFollowRedirects(true);
        connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");

        connection.connect();

        DataOutputStream dataout = new DataOutputStream(connection.getOutputStream());
        dataout.writeBytes(parms);
        dataout.flush();
        dataout.close();
        try {
            BufferedReader bf = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            StringBuilder sb = new StringBuilder();
            while ((line = bf.readLine()) != null) {
                sb.append(line);
            }
            bf.close();
            connection.disconnect();
            return sb.toString();
        } catch (java.io.IOException e) {
            return e.getMessage();
        }
    }

    //type 等于1 是推送pc站点  0 是推送m站点
    public static String evaluate(String keyNo, Integer type) throws IOException, URISyntaxException {
        switch (type){
            case 0 :
            case 1 :
                return processFirm(keyNo,type);
            case 2 :
                return doPostToSEO(keyNo);
            case 3 :
            case 4 :
                return processProduct(keyNo,type);
            default:
                return "参数错误";
        }


    }

    private static String processProduct(String keyNo , int type) {
        StringBuilder str = new StringBuilder();
        try{
            if (type == 3){
                //String htmlKeyNo = "https://www.qcc.com/product/" + keyNo + ".html";
                str.append(HiveToSEO.doPostToSEO("http://data.zz.baidu.com/urls?site=https://www.qcc.com&token=aSFOBA8Io8O3tlmU", keyNo));
            }else{
                //String htmlKeyNo = "https://m.qcc.com/product/" + keyNo + ".html";
                str.append(HiveToSEO.doPostToSEO("http://data.zz.baidu.com/urls?site=https://m.qcc.com&token=CoZOiRzjxRrPilPX", keyNo));
            }
        }catch (Exception e){
            e.printStackTrace();
            str.append(e);
        }
        return str.toString();
    }

    private static String processFirm(String keyNo, int type) {
        try {
            if (keyNo == "" || StringUtil.isEmpty(keyNo)) {
                return "";
            }
            String jsonString = "";
            if (type == 0) {
                String htmlKeyNo = "https://www.qcc.com/firm/" + keyNo + ".html";
                jsonString = HiveToSEO.doPostToSEO("http://data.zz.baidu.com/urls?site=https://www.qcc.com&token=aSFOBA8Io8O3tlmU", htmlKeyNo);
            } else if(type == 1) {
                String htmlKeyNo = "https://m.qcc.com/firm/" + keyNo + ".html";
                jsonString = HiveToSEO.doPostToSEO("http://data.zz.baidu.com/urls?site=https://m.qcc.com&token=aSFOBA8Io8O3tlmU", htmlKeyNo);
            }
            return jsonString;


            /*String[] listKeyno = keyNo.split(",");
            Integer remainCount = 101;
            String retrunValue = "";
            String jsonString = "";
            for (String s : listKeyno) {
                if (remainCount <= 100) {
                    retrunValue = remainCount.toString();
                    break;
                }
                if (type == 0) {
                    String htmlKeyNo = "https://www.qcc.com/firm/" + s + ".html";
                    jsonString = HiveToSEO.doPostToSEO("http://data.zz.baidu.com/urls?site=https://www.qcc.com&token=aSFOBA8Io8O3tlmU", htmlKeyNo);
                } else if(type == 1) {
                    String htmlKeyNo = "https://m.qcc.com/firm/" + s + ".html";
                    jsonString = HiveToSEO.doPostToSEO("http://data.zz.baidu.com/urls?site=https://m.qcc.com&token=CoZOiRzjxRrPilPX", htmlKeyNo);
                }；

                if (jsonString.contains("Exception")) {
                    retrunValue = jsonString;
                    break;
                }
                JSONObject obj = JSONObject.parseObject(jsonString);
                remainCount = obj.getInteger("remain");
                retrunValue = remainCount.toString();

            }
            return retrunValue;*/
        } catch (Exception e) {
            e.printStackTrace();
            return e.toString();
        }
    }


    //百度api推送 按周来推送 每天推送2000条
    public static  String doPostToSEO(String keyNo)  {
        if (keyNo == "" || StringUtil.isEmpty(keyNo)) {
            return "";
        }
        StringBuilder str = new StringBuilder();
        Arrays.stream(keyNo.split(",")).forEach( k -> {
            str.append("/company-subpackages/detail/index?unique=").append(k).append(",");
        });
        String  paths = str.substring(0,str.length()-1);

        List result = new ArrayList();
        int firstIndex = paths.indexOf(",");
        int lastIndex = paths.lastIndexOf(",");
        int half = (lastIndex-firstIndex)/2;
        int halfIndex = paths.indexOf(",",half);
        String path1 = paths.substring(0, halfIndex);
        String path2 = paths.substring(halfIndex + 1, paths.length());
        Stream.of(path1,path2).forEach( k -> {
            try {
                HttpClient httpClient = new DefaultHttpClient();
                HttpPost httpPost = new HttpPost("http://mini-program-app-mp-api.sit.office.greatld.com/mp-baidu/plugin/submitSitemap");
                List<NameValuePair> nameValuePairs = new ArrayList<NameValuePair>();
                nameValuePairs.add(new BasicNameValuePair("type","0"));
                nameValuePairs.add(new BasicNameValuePair("paths",k));
                UrlEncodedFormEntity entity = new UrlEncodedFormEntity(nameValuePairs,"utf-8");

                httpPost.setEntity(entity);
                HttpResponse response = httpClient.execute(httpPost);
                if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                    JSONObject json = JSON.parseObject(EntityUtils.toString(response.getEntity()));
                    if (json.getBoolean("submitRs")){
                        result.add(json.toJSONString());
                    }else {
                        result.add(json.toJSONString());
                    }
                }else {
                    result.add("百度api请求失败" +response.getStatusLine().getReasonPhrase());
                }
                //避免api提交过快 sleep 3s
                Thread.sleep(3000);
            } catch (IOException | InterruptedException e) {
                e.printStackTrace();
            }
        });

        return result.toString();
    }

    public static void main(String[] args) throws IOException, URISyntaxException {
        System.out.println(evaluate("z0011ad1d95dbe6635b32376eb968a13", 1));
    }


}
