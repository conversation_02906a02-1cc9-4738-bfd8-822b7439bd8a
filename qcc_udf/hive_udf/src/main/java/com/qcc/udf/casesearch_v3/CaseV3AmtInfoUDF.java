package com.qcc.udf.casesearch_v3;

import com.alibaba.fastjson.JSON;
import com.qcc.udf.casesearch_v3.entity.output.AmtInfo;
import org.apache.hadoop.hive.ql.exec.UDF;
import parquet.Strings;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CaseV3AmtInfoUDF extends UDF {
    public static String evaluate(String amtInfo) {
        List<Map<String,Object>> list = new ArrayList<>();
        if (Strings.isNullOrEmpty(amtInfo)) {
            return "";
        }
        try{
            Map map =JSON.parseObject(amtInfo,Map.class);
            map.forEach((k,v)->{
                Map<String,Object> tmp = new HashMap<>();
                tmp.put("KeyNo",k);
                AmtInfo amtInfo1 = JSON.parseObject(JSON.toJSONString(v),AmtInfo.class);
                tmp.put("Amt",amtInfo1.getAmt());
                tmp.put("Type",amtInfo1.getType());
                tmp.put("IsValid",amtInfo1.getIsValid());
                list.add(tmp);
            });
        }catch (Exception e){

        }

        return JSON.toJSONString(list);
    }

    public static void main(String[] args) {
        System.out.println(evaluate("{\"1577cf228a7b47886799d5b791d7f5e8\":{\"Amt\":\"636793.00\",\"IsValid\":\"1\"},\"4e9584c78edfeac56a7128786b67b5aa\":{\"Amt\":\"636793.00\",\"Type\":\"案件金额\"},\"7348963d71fb3ff070f72961c4ce9f08\":{\"Amt\":\"636793.00\",\"Type\":\"案件金额\"},\"b866489ee5dcdfb71eb1249b976df937\":{\"Amt\":\"636793.00\",\"Type\":\"案件金额\"}}"));
    }

}
