package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.text.DecimalFormat;

/**
 * @Auther: liulh
 * @Date: 2020/6/1 17:54
 * @Description:
 */
public class chineseNumber2Int extends UDF {
    public static int evaluate(String chineseNumber) {
        int result = 0;
        // 存放一个单位的数字如：十万
        int temp = 1;
        // 判断是否有chArr
        int count = 0;
        char[] cnArr = new char[]{'一','二','三','四','五','六','七','八','九'};
        char[] chArr = new char[]{'十','百','千','万','亿'};
        for (int i = 0; i < chineseNumber.length(); i++) {
            // 判断是否是chArr
            boolean b = true;
            char c = chineseNumber.charAt(i);
            // 非单位，即数字
            for (int j = 0; j < cnArr.length; j++) {
                if (c == cnArr[j]) {
                    // 添加下一个单位之前，先把上一个单位值添加到结果中
                    if(0 != count){
                        result += temp;
                        temp = 1;
                        count = 0;
                    }
                    // 下标+1，就是对应的值
                    temp = j + 1;
                    b = false;
                    break;
                }
            }
            // 单位{'十','百','千','万','亿'}
            if(b){
                for (int j = 0; j < chArr.length; j++) {
                    if (c == chArr[j]) {
                        switch (j) {
                            case 0:
                                temp *= 10;
                                break;
                            case 1:
                                temp *= 100;
                                break;
                            case 2:
                                temp *= 1000;
                                break;
                            case 3:
                                temp *= 10000;
                                break;
                            case 4:
                                temp *= 100000000;
                                break;
                            default:
                                break;
                        }
                        count++;
                    }
                }
            }
            // 遍历到最后一个字符
            if (i == chineseNumber.length() - 1) {
                result += temp;
            }
        }
        return result;
    }

}
