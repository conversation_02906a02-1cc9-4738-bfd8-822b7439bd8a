package com.qcc.udf.kzz;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * 组合招聘地址
 * <AUTHOR>
 * @date 2022/5/12
 */
public class GetRecruitAddress extends UDF {
    public static String evaluate(String province, String city, String address) {
        String result = "";
        try{
            if(StringUtils.isNotBlank(address)) {
                if(address.startsWith(province)){
                    result = address.replace(province,"");
                    if(result.startsWith(city)){
                        result=province+result;
                    }else{
                        if(province.contains(city)){
                            result=province+result;
                        }else{
                            result=province+city+result;
                        }

                    }
                }else{
                    if(address.startsWith(city)){
                        result=province+address;
                    }else{
                        if(province.contains(city)){
                            result=province+address;
                        }else{
                            result=province+city+address;
                        }
                    }
                }
            }

        }catch (Exception e){

        }
        return result;
    }
//    public static void main(String[] args) {
//        String province = "北京";
//        String city = "北京";
//        String address = "北京朝阳区乐成中心A座3";
//        String region = evaluate(province,city,address);
//        System.out.println(region);
//    }
}
