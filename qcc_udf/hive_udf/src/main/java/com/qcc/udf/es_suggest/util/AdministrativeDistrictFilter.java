package com.qcc.udf.es_suggest.util;

import org.apache.commons.lang.StringUtils;
import org.nlpcn.commons.lang.dic.DicManager;
import org.nlpcn.commons.lang.tire.GetWord;
import org.nlpcn.commons.lang.tire.domain.Forest;
import org.nlpcn.commons.lang.tire.library.Library;

/**
 * Delete specify administrative district word.
 *
 * <AUTHOR> <PERSON>
 * @version 0.0.1 2020-04-13 16:25:47
 */
public class AdministrativeDistrictFilter {

    private static final int FIRST_POSITION = 0;
    private static Forest provinceForest;
    private static Forest cityForest;
    private static Forest countyForest;


    /**
     * Suppresses default construct, ensuring non-instantibility.
     */
    private AdministrativeDistrictFilter() {
    }

    static {

        try {
            provinceForest = Library.makeForest(
                    DicManager.class.getResourceAsStream("/dic/province.dic"));
            cityForest = Library.makeForest(
                    DicManager.class.getResourceAsStream("/dic/city.dic"));
            countyForest = Library.makeForest(
                    DicManager.class.getResourceAsStream("/dic/county.dic"));
        } catch (Exception e) {

        }

    }

    public static String deleteProvinceAtBeginning(String text) {
        return deleteFirstWord(provinceForest, text);
    }

    public static String deleteCityAtBeginning(String text) {
        return deleteFirstWord(cityForest, text);
    }

    public static String deleteCountyAtBeginning(String text) {
        return deleteFirstWord(countyForest, text);
    }

    private static String deleteFirstWord(Forest forest, String text) {
        if (StringUtils.isEmpty(text)) {
            return StringUtils.EMPTY;
        }

        GetWord word = forest.getWord(text);
        String firstMatchWord = word.getFrontWords();
        if (StringUtils.isEmpty(firstMatchWord) || word.offe != FIRST_POSITION) {
            return text;
        }
        return text.substring(firstMatchWord.length());
    }

    public static String smartDeleteAdministrativeDistrict(String text) {
        String result = deleteProvinceAtBeginning(text);
        result = deleteCityAtBeginning(result);
        result = deleteCountyAtBeginning(result);
        return result;
    }
}
