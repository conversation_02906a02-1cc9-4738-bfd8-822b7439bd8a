package com.qcc.udf;

import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.ip_region.GetIpRegionUDF;
import jodd.util.StringUtil;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;


public class Ip2Region extends UDF {

    public static String doPostGetIoInfo(String requestUrl, String parms) throws IOException {
        URL url = new URL(requestUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setDoOutput(true);
        connection.setDoInput(true);
        connection.setRequestMethod("POST");
        connection.setUseCaches(false);
        connection.setInstanceFollowRedirects(true);
        connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");

        connection.connect();

        DataOutputStream dataout = new DataOutputStream(connection.getOutputStream());
        dataout.writeBytes(parms);
        dataout.flush();
        dataout.close();
        try {
            BufferedReader bf = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            StringBuilder sb = new StringBuilder();
            while ((line = bf.readLine()) != null) {
                sb.append(line);
            }
            bf.close();
            connection.disconnect();
            return sb.toString();
        } catch (java.io.IOException e) {
            return e.toString();
        }
    }

    public static String evaluate(String IP) {
        try {
            if (IP == "" || StringUtil.isEmpty(IP)) {
                return "";
            }
            IP = "ip=" + IP;
            String jsonString = Ip2Region.doPostGetIoInfo("http://qcc-app-security-service.sit.office.qichacha.com/risk/getIpInfo", IP);
            JSONObject obj = JSONObject.parseObject(jsonString);
            return obj.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return e.toString();
        }
    }

    public static void main(String[] args) throws Exception {
        System.out.println(evaluate("***********"));
    }

}