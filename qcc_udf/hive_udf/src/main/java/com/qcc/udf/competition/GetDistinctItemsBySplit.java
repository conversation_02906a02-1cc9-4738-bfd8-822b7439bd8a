package com.qcc.udf.competition;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2021/07/21 21:55
 * @description ：
 */
public class GetDistinctItemsBySplit extends UDF {

    public String evaluate(String value, String splitItem) {

        String result = "";

        if (StringUtils.isEmpty(value) || StringUtils.isEmpty(splitItem)) {
            return result;
        }

        String[] itemStr = value.split(splitItem);
        Set<String> ids = new HashSet<>();
        for(String item: itemStr) {
            if (StringUtils.isEmpty(item)) {
                continue;
            }
            ids.add(item);
        }
        if (CollectionUtils.isNotEmpty(ids)) {
            result = ids.stream().collect(Collectors.joining(","));
        }
        return result;
    }

    public static void main(String[] args) {
        GetDistinctItemsBySplit getSameCertificationKeys = new GetDistinctItemsBySplit();
        String one = "1";
        String two = ",";
        System.out.println(getSameCertificationKeys.evaluate(one, two));
    }

}
