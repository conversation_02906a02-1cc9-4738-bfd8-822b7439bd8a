package com.qcc.udf.casesearch_v3;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.text.DateFormat;
import java.text.SimpleDateFormat;

public class CaseDateConvertUDF extends UDF {
    public static long evaluate(String inputDate) {
        Long timestamp = -1L;
        try {
            if (StringUtils.isNotBlank(inputDate)) {

                try {
                    if (inputDate.contains("T")) {
                        DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
                        timestamp = df.parse(inputDate).getTime() / 1000;
                    }
                } catch (Exception ex) {
                }

                try{
                    if (timestamp == -1L) {
                        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        timestamp = format.parse(inputDate).getTime() / 1000;
                    }
                } catch (Exception ex) {
                }

                try{
                    if (timestamp == -1L) {
                        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                        timestamp = format.parse(inputDate).getTime() / 1000;
                    }
                }catch (Exception e){}

                if (timestamp == -1L) {
                    SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
                    timestamp = format.parse(inputDate).getTime() / 1000;
                }
            }
        } catch (Exception e) {
        }
        return timestamp;
    }

    public static void main(String[] args) {
//        System.out.println(evaluate("2020-11-26 12:12:12"));
        System.out.println(evaluate("20201126"));
    }
}
