package com.qcc.udf.risk_analysis;

import com.alibaba.fastjson.JSON;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date ：Created in 2020/7/13 19:35
 * @description：风险分析，按照指定分隔符转为JSONArray
 */
public class convertJsonWithSplit extends UDF {
	/**
	 * yyyy-MM-dd HH:mm:ss
	 */
	public static final String YMD_DASH_WITH_TIME = "yyyy-MM-dd HH:mm:ss";
	private final static Pattern DATE_PATTERN = Pattern.compile("\\d{4}-\\d{0,2}-\\d{0,2}");

	static String[] ABC = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N"};

	/**
	 * 照指定分隔符转为JSONArray
	 *
	 * @param text  ["（2019）京0113民初2号#FGF#123456#FGF#abc","（2019）京0113民初2号#FGF#123456#FGF#abc"]
	 * @param split 分隔符名称（#FGF#）
	 * @return
	 */
	public static String evaluate(String text, String split) {
		List<String> list = JSON.parseArray(text, String.class);
		List<Map<String, Object>> resultList = new ArrayList<>();
		for (String item : list) {
			String[] array = item.split(split);
			Map<String, Object> map = new HashMap<>();

			for (int i = 0; i < array.length; i++) {
				String data = array[i];
				//标准年月日格式 转为时间戳
				if (isYMD(data)) {
					map.put(ABC[i], getTime(data));
				} else {
					map.put(ABC[i], convertNumber(data));
				}

			}
			resultList.add(map);
		}

		//按照日期排序（默认B参,时间倒排序）
		resultList.sort((a, b) -> {
			Object a1 = a.get("B");
			Object a2 = b.get("B");
			if (a1 != null && a2 != null) {
				if (a1 instanceof Long && a2 instanceof Long) {
					return ((Long) a2).compareTo((Long) a1);
				}
			}
			return 0;
		});

		return JSON.toJSONString(resultList);
	}

	/**
	 * 数字转为Long类型
	 *
	 * @param data
	 * @return
	 */
	private static Object convertNumber(String data) {
		//^\+?[1-9][0-9]*$
		boolean isNumber = Pattern.matches("^(-|)\\d+$", data);
		if (isNumber) {
			try {
				return Long.valueOf(data);
			} catch (Exception e) {
				return data;
			}
		}
		return data;
	}

	/**
	 * 判断是否是年月日类型
	 *
	 * @param data
	 * @return
	 */
	public static boolean isYMD(String data) {
		// 全部英文的情况下，返回英文括号
		Matcher m = DATE_PATTERN.matcher(data);
		if (m.find()) {
			return true;
		} else {
			return false;
		}
	}


	public static Long getTime(String date) {
		Date time = parse(date, YMD_DASH_WITH_TIME);
		if (time == null) {
			return 0L;
		}

		return time.getTime() < 0L ? 0L : time.getTime() / 1000L;
	}

	/**
	 * 转为日期
	 *
	 * @param date
	 * @param pattern
	 * @return
	 */
	public static Date parse(String date, String pattern) {
		if (date == null || date.isEmpty()) {
			return null;
		}
		DateFormat format = new SimpleDateFormat(pattern);
		try {
			return format.parse(date);
		} catch (ParseException e) {
			return null;
		}
	}

	public static void main(String[] args) {
		String text = "\t\n" +
				"[\"（2019）京0113民初2号#FGF#123456#FGF#abc\"]";
		System.out.println(evaluate(text, "#FGF#"));
	}

}
