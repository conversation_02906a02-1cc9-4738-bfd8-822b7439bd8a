package com.qcc.udf.property_clue;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.output.LawSuitV3OutputEntity;
import com.qcc.udf.casesearch_v3.entity.output.NameAndKeyNoEntity;
import com.qcc.udf.property_clue.entity.CaseTralRound;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Auther: wuql
 * @Date: 2021/1/18 17:33
 * @Description: 解析案件的所有审理程序
 */
public class TrialRoundInfo extends UDF {
    public static String evaluate(String id, String input) {
        if (StringUtils.isEmpty(id) || StringUtils.isEmpty(input)) {
            return "";
        }

        LawSuitV3OutputEntity caseInfo = JSON.parseObject(input, LawSuitV3OutputEntity.class);
        List<InfoListEntity> infoList = caseInfo.getInfoList();
        List<CaseTralRound> caseTralRounds = new ArrayList<>();
        for (InfoListEntity info : infoList) {
            String trialRound = info.getTrialRound();
            Long latestTimestamp = info.getLatestTimestamp();
            buildCaseTralRound(id, caseTralRounds, trialRound,latestTimestamp, info.getDefendant());
            buildCaseTralRound(id, caseTralRounds, trialRound,latestTimestamp, info.getProsecutor());
        }

        return JSON.toJSONString(caseTralRounds, SerializerFeature.DisableCircularReferenceDetect);
    }

    private static void buildCaseTralRound(String id, List<CaseTralRound> caseTralRounds, String trialRound,Long latestTimestamp, List<NameAndKeyNoEntity> nameAndKeynos) {
        for (NameAndKeyNoEntity keyno : nameAndKeynos) {
            String keyNo = keyno.getKeyNo();
            if (StringUtils.isBlank(keyNo)||keyNo.length()<32){
                continue;
            }
            CaseTralRound caseTralRound = new CaseTralRound();
            caseTralRound.setId(id);
            caseTralRound.setKeyno(keyNo);
            caseTralRound.setCompanyName(keyno.getName());
            caseTralRound.setRole(getCaseRoleCode(keyno.getRole()));
            caseTralRound.setTrialRound(trialRound);
            caseTralRound.setLatestTimestamp(latestTimestamp);
            caseTralRounds.add(caseTralRound);
        }
    }

    public static String getCaseRoleCode(String caseRole) {
        String result = "";
        if (StringUtils.isEmpty(caseRole)) {
            return "";
        }

        Pattern p1 = Pattern.compile("(被执行人)|(被告)|(被申请人)|(被申请执行人)|(原审被告)|(被上诉人\\(原审被告\\))|(上诉人\\(原审被告\\))|(被告\\(反诉原告\\))|(被告人)|(上诉人\\(一审被告\\))|" +
                "(被上诉人\\(一审被告\\))|(被上诉人)|(上诉人\\(原审被告反诉原告\\))|(被告二)|(被告一)|(原告\\(被告\\))|(被申请人\\(一审被告二审被上诉人\\))|(被申请人\\(原审被告\\))|(再审申请人\\(一审被告二审上诉人\\))|" +
                "(再审申请人\\(原审被告\\))|(被申请人\\(仲裁被申请人\\))|(被申请人\\(原被执行人\\))|(再审被申请人)|(上诉人\\(原审被告原审原告\\))");
        Matcher m1 = p1.matcher(caseRole);
        if (m1.matches()) {
            result = "D";
        }

        Pattern p2 = Pattern.compile("(申请执行人)|(原告)|(申请人)|(被上诉人\\(原审原告\\))|(复议申请人)|(上诉人\\(原审原告\\))|(原告\\(反诉被告\\))|(上诉人)|(被上诉人\\(一审原告\\))|(上诉人\\(一审原告\\))|(被上诉人\\(原审原告反诉被告\\))|" +
                "(原审原告)|(再审申请人)|(被告\\(原告\\))|(被申请人\\(原审原告\\))|(附带民事诉讼原告人)|(复议申请人\\(原申请执行人\\))|(再审申请人\\(一审原告二审上诉人\\))|(再审申请人\\(原审原告\\))|(申请再审人\\(一审原告二审上诉人\\))|" +
                "(二审上诉人)|(原告人)|(附带民事诉讼原告)|(上诉人\\(原审原告原审被告\\))|(起诉人)|(申请人\\(仲裁申请人\\))|(赔偿请求人)");
        Matcher m2 = p2.matcher(caseRole);
        if (m2.matches()) {
            result = "P";
        }

        return result;
    }

    public static void main(String[] args) {
        String id = "1";
        String info = "{\"KtggCnt\":0,\"LastestDateType\":\"首次执行|案件终本日期\",\"XjpgCnt\":0,\"ZxCnt\":2,\"CfgsCnt\":0,\"LastestDate\":1587830400,\"AmtInfo\":{\"p66c528e366d46f22499c844b2d9a811\":{\"Type\":\"未履行金额\",\"Amt\":\"808445\"},\"1c60b6090fcd648a54c69bb91cedd8d8\":{\"Type\":\"案件金额\",\"Amt\":\"808445.00\"},\"5029efb72f635dde3144a0d70454ed3b\":{\"Type\":\"未履行金额\",\"Amt\":\"808445\"}},\"EarliestDate\":1364342400,\"Source\":\"OT\",\"AnnoCnt\":2,\"EarliestDateType\":\"民事一审|判决日期\",\"XzcfCnt\":0,\"CompanyKeywords\":\"1c60b6090fcd648a54c69bb91cedd8d8,5029efb72f635dde3144a0d70454ed3b,p66c528e366d46f22499c844b2d9a811,义乌市派克制衣有限公司,武汉市大器龙服饰有限责任公司,汪忠\",\"AnNoList\":\"（2012）金义商初字第2795号,（2019）浙0782执14075号\",\"GqdjCnt\":0,\"XgCnt\":2,\"Tags\":\"1,2,3,4,6\",\"FyggCnt\":0,\"ZbCnt\":2,\"LatestTrialRound\":\"首次执行\",\"CfdfCnt\":0,\"CaseName\":\"义乌市派克制衣有限公司与武汉市大器龙服饰有限责任公司,汪忠买卖合同纠纷的案件\",\"CfxyCnt\":0,\"SxCnt\":2,\"Province\":\"ZJ\",\"GroupId\":\"fdd715e6a4db976da710f0213fdfd724\",\"LianCnt\":0,\"CaseCnt\":2,\"HbcfCnt\":0,\"PcczCnt\":0,\"Type\":1,\"CaseType\":\"执行案件,民事案件\",\"ProcuratorateList\":\"\",\"CaseRole\":\"[{\\\"D\\\":\\\"一审原告,首次执行申请执行人\\\",\\\"N\\\":\\\"1c60b6090fcd648a54c69bb91cedd8d8\\\",\\\"O\\\":0,\\\"P\\\":\\\"义乌市派克制衣有限公司\\\",\\\"R\\\":\\\"原告\\\"},{\\\"D\\\":\\\"一审被告,首次执行被执行人\\\",\\\"N\\\":\\\"5029efb72f635dde3144a0d70454ed3b\\\",\\\"O\\\":0,\\\"P\\\":\\\"武汉市大器龙服饰有限责任公司\\\",\\\"R\\\":\\\"被告\\\"},{\\\"D\\\":\\\"一审被告,首次执行被执行人\\\",\\\"N\\\":\\\"p66c528e366d46f22499c844b2d9a811\\\",\\\"O\\\":2,\\\"P\\\":\\\"汪忠\\\",\\\"R\\\":\\\"被告\\\"}]\",\"CaseReason\":\"买卖合同纠纷\",\"CourtList\":\"浙江省金华市义乌市人民法院\",\"Id\":\"c647cc00da8883318980b179498f67ed\",\"InfoList\":[{\"Defendant\":[{\"KeyNo\":\"5029efb72f635dde3144a0d70454ed3b\",\"Role\":\"被告\",\"Org\":0,\"Name\":\"武汉市大器龙服饰有限责任公司\"},{\"KeyNo\":\"p66c528e366d46f22499c844b2d9a811\",\"Role\":\"被告\",\"Org\":2,\"Name\":\"汪忠\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"浙江省金华市义乌市人民法院\",\"LatestTimestamp\":1364342400,\"ZxList\":[],\"XzcffList\":[],\"CfdfList\":[],\"HbcfList\":[],\"CaseList\":[{\"CaseType\":\"民事判决书\",\"JudgeDate\":1364342400,\"Amt\":\"808445.00\",\"Id\":\"d076df471967f4cc204c8e30b2b14f830\",\"ResultType\":\"判决结果\",\"DocType\":\"判决日期\",\"IsValid\":1,\"Result\":\"一、被告武汉市大器龙服饰有限责任公司于本判决生效后十日内支付原告义乌市派克制衣有限公司货款784898元及违约金23547元。\"}],\"TrialRound\":\"民事一审\",\"Prosecutor\":[{\"KeyNo\":\"1c60b6090fcd648a54c69bb91cedd8d8\",\"Role\":\"原告\",\"Org\":0,\"Name\":\"义乌市派克制衣有限公司\"}],\"ZbList\":[],\"ExecuteNo\":\"\",\"SxList\":[],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2012）金义商初字第2795号\",\"CaseType\":\"民事案件\",\"LianList\":[],\"XgList\":[],\"CaseReason\":\"买卖合同纠纷\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]},{\"Defendant\":[{\"KeyNo\":\"5029efb72f635dde3144a0d70454ed3b\",\"Role\":\"被执行人\",\"Org\":0,\"Name\":\"武汉市大器龙服饰有限责任公司\"},{\"KeyNo\":\"p66c528e366d46f22499c844b2d9a811\",\"Role\":\"被执行人\",\"Org\":2,\"Name\":\"汪忠\"}],\"CfgsList\":[],\"SdggList\":[],\"Court\":\"浙江省金华市义乌市人民法院\",\"LatestTimestamp\":1587830400,\"ZxList\":[{\"LianDate\":1575820800,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"1c60b6090fcd648a54c69bb91cedd8d8\",\"Org\":0,\"Name\":\"义乌市派克制衣有限公司\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"p66c528e366d46f22499c844b2d9a811\",\"Org\":2,\"Name\":\"汪忠\"}],\"Id\":\"118b34f9196815421324acfd897f5cee1\",\"Biaodi\":\"808445\",\"IsValid\":0},{\"LianDate\":1575820800,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"1c60b6090fcd648a54c69bb91cedd8d8\",\"Org\":0,\"Name\":\"义乌市派克制衣有限公司\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"5029efb72f635dde3144a0d70454ed3b\",\"Org\":0,\"Name\":\"武汉市大器龙服饰有限责任公司\"}],\"Id\":\"ec49437634408d803eec958e534bb3571\",\"Biaodi\":\"808445\",\"IsValid\":0}],\"XzcffList\":[],\"CfdfList\":[],\"HbcfList\":[],\"CaseList\":[{\"CaseType\":\"执行裁定书\",\"JudgeDate\":1587312000,\"Amt\":\"\",\"Id\":\"c10579d773ac7e6487b897d120e99a5d0\",\"ResultType\":\"裁定结果\",\"DocType\":\"裁定日期\",\"IsValid\":1,\"Result\":\"终结本院（2019）浙0782执14075号案件本次执行程序。\"}],\"TrialRound\":\"首次执行\",\"Prosecutor\":[{\"KeyNo\":\"1c60b6090fcd648a54c69bb91cedd8d8\",\"Role\":\"申请执行人\",\"Org\":0,\"Name\":\"义乌市派克制衣有限公司\"}],\"ZbList\":[{\"FailureAct\":\"808445\",\"ExecuteObject\":\"808445\",\"JudgeDate\":1587830400,\"NameAndKeyNo\":[{\"KeyNo\":\"p66c528e366d46f22499c844b2d9a811\",\"Org\":2,\"Name\":\"汪忠\"}],\"Id\":\"118b34f9196815421324acfd897f5cee\",\"IsValid\":1},{\"FailureAct\":\"808445\",\"ExecuteObject\":\"808445\",\"JudgeDate\":1587830400,\"NameAndKeyNo\":[{\"KeyNo\":\"5029efb72f635dde3144a0d70454ed3b\",\"Org\":0,\"Name\":\"武汉市大器龙服饰有限责任公司\"}],\"Id\":\"ec49437634408d803eec958e534bb357\",\"IsValid\":1}],\"ExecuteNo\":\"（2012）金义商初字第02795号\",\"SxList\":[{\"PublishDate\":1578931200,\"ActionType\":\"有履行能力而拒不履行生效法律文书确定义务\",\"NameAndKeyNo\":[{\"KeyNo\":\"p66c528e366d46f22499c844b2d9a811\",\"Org\":2,\"Name\":\"汪忠\"}],\"ExecuteStatus\":\"全部未履行\",\"Id\":\"118b34f9196815421324acfd897f5cee2\",\"IsValid\":1},{\"PublishDate\":1578931200,\"ActionType\":\"有履行能力而拒不履行生效法律文书确定义务\",\"NameAndKeyNo\":[{\"KeyNo\":\"5029efb72f635dde3144a0d70454ed3b\",\"Org\":0,\"Name\":\"武汉市大器龙服饰有限责任公司\"}],\"ExecuteStatus\":\"全部未履行\",\"Id\":\"ec49437634408d803eec958e534bb3572\",\"IsValid\":1}],\"Procuratorate\":\"\",\"FyggList\":[],\"XjpgList\":[],\"AnNo\":\"（2019）浙0782执14075号\",\"CaseType\":\"执行案件\",\"LianList\":[],\"XgList\":[{\"PublishDate\":1587312000,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"1c60b6090fcd648a54c69bb91cedd8d8\",\"Org\":0,\"Name\":\"义乌市派克制衣有限公司\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"p66c528e366d46f22499c844b2d9a811\",\"Org\":2,\"Name\":\"汪忠\"}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"p66c528e366d46f22499c844b2d9a811\",\"Org\":2,\"Name\":\"汪忠\"}],\"Id\":\"869778cb798699fceb8b8bffb1438666\",\"CompanyInfo\":[{\"KeyNo\":\"p66c528e366d46f22499c844b2d9a811\",\"Org\":2,\"Name\":\"汪忠\"}],\"GlNameAndKeyNo\":[],\"IsValid\":1},{\"PublishDate\":1587571200,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"1c60b6090fcd648a54c69bb91cedd8d8\",\"Org\":0,\"Name\":\"义乌市派克制衣有限公司\"}],\"NameAndKeyNo\":[{\"KeyNo\":\"5029efb72f635dde3144a0d70454ed3b\",\"Org\":0,\"Name\":\"武汉市大器龙服饰有限责任公司\"}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"5029efb72f635dde3144a0d70454ed3b\",\"Org\":0,\"Name\":\"武汉市大器龙服饰有限责任公司\"}],\"Id\":\"d38d869acc5604b5e08c607a051bddbe\",\"CompanyInfo\":[{\"KeyNo\":\"5029efb72f635dde3144a0d70454ed3b\",\"Org\":0,\"Name\":\"武汉市大器龙服饰有限责任公司\"}],\"GlNameAndKeyNo\":[{\"KeyNo\":\"p66c528e366d46f22499c844b2d9a811\",\"Org\":2,\"Name\":\"汪忠\"}],\"IsValid\":1}],\"CaseReason\":\"买卖合同纠纷案件执行\",\"KtggList\":[],\"PcczList\":[],\"GqdjList\":[],\"CfxyList\":[]}],\"SdggCnt\":0}";

        System.out.println(evaluate("1", info));

    }
}
