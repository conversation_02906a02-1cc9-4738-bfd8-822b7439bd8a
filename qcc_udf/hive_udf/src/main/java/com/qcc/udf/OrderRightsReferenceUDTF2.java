package com.qcc.udf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentLengthException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDTF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用于解析改版后的用户权益时间段
 */
public class OrderRightsReferenceUDTF2 extends GenericUDTF {


    static DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public StructObjectInspector initialize(StructObjectInspector argOIs) throws UDFArgumentException {
        if (argOIs.getAllStructFieldRefs().size() != 1) {
            throw new UDFArgumentLengthException("ExplodeRiskMap takes only one argument");
        }
        // 输出
        List<String> fieldNames = new ArrayList<String>(3);
        List<ObjectInspector> fieldOIs = new ArrayList<ObjectInspector>(3);
        fieldNames.add("order_code");
        fieldNames.add("rights_start_time");
        fieldNames.add("rights_end_time");
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
        return ObjectInspectorFactory.getStandardStructObjectInspector(fieldNames, fieldOIs);
    }

    @Override
    public void process(Object[] objects) throws HiveException {
        JSONObject jsonObject = JSONObject.parseObject(objects[0].toString());
        JSONArray orders = jsonObject.getJSONArray("orders");

        //判断老权益有没有过期  如过期则置位null  6.10订单权益改版
        LocalDateTime date = LocalDateTime.of(2021, 6, 10, 17, 44, 0);


        //判断是否是10.27改版之后的数据 改版之后数据不做处理
        List<Object> filterAfter = orders.stream().filter(temp -> ((JSONObject) temp).containsKey("rights_start_time")
                && !"NULL".equals(((JSONObject) temp).getString("rights_start_time"))).collect(Collectors.toList());
        if (filterAfter.size() == orders.size()){
            export(filterAfter);
            return;
        }
        orders.removeAll(filterAfter);


        ArrayList<LocalDateTime> vipEndTime = new ArrayList<LocalDateTime>();
        ArrayList<LocalDateTime> SvipStartTime = new ArrayList<LocalDateTime>();
        ArrayList<LocalDateTime> SvipEndTime = new ArrayList<LocalDateTime>();
        ArrayList<JSONObject> orderRight = new ArrayList<JSONObject>();


        //按照pay_time排序
        Map<Object, List<Object>> collect = orders.stream().sorted(Comparator.comparing(temp -> ((JSONObject) temp).getTimestamp("pay_time")))
                .collect(Collectors.groupingBy(temp -> ((JSONObject) temp).getString("goods_type")));

        //判断是否存在svip
        //优先筛选出svip订单  根据svip订单再确定vip订单的时间

        List<Object> SVIPList = collect.get("SVIP");
        List<Object> VIPList = collect.get("VIP");

        //svip 权益持续时间
        List<JSONObject> SVIPRightTimeList = new ArrayList<JSONObject>();


        if (SVIPList != null && SVIPList.size() > 0) {
            int i = 0;
            for (Object k : SVIPList) {
                JSONObject order = (JSONObject) k;
                JSONObject right = new JSONObject();
                String orderCode = order.getString("order_code");
                String payTime = order.getString("pay_time");
                String endTime = order.getString("end_time");
                Integer validPeriodMonths = order.getInteger("valid_period_months");
                Integer realGiveDays = order.getInteger("real_give_days");
                LocalDateTime payLocalDateTime = LocalDateTime.parse(payTime, dateTimeFormatter);
                LocalDateTime endLocalDateTime = LocalDateTime.parse(endTime, dateTimeFormatter);

                if (SvipEndTime.size() == 0) {
                    // 无svip权益  则下单时间则为svip开始时间
                    right.put("order_code", orderCode);
                    right.put("rights_start_time", payTime);
                    right.put("rights_end_time", endTime);
                    orderRight.add(right);
                    right.put("rights_start_dateTime", payLocalDateTime);
                    right.put("rights_end_dateTime", endLocalDateTime);
                    right.put("valid_period_months", validPeriodMonths);
                    right.put("real_give_days", realGiveDays);
                    SvipEndTime.add(endLocalDateTime);
                    SvipStartTime.add(payLocalDateTime);
                } else {
                    // 有svip权益
                    if (SvipEndTime.get(SvipEndTime.size() - 1).isBefore(payLocalDateTime)) {
                        //svip权益早于下单时间 说明已过期 则下单时间则为svip开始时间
                        right.put("order_code", orderCode);
                        right.put("rights_start_time", payTime);
                        right.put("rights_end_time", endTime);
                        orderRight.add(right);
                        right.put("rights_start_dateTime", payLocalDateTime);
                        right.put("rights_end_dateTime", endLocalDateTime);
                        right.put("valid_period_months", validPeriodMonths);
                        right.put("real_give_days", realGiveDays);
                        SvipStartTime.add(payLocalDateTime);
                        SvipEndTime.add(endLocalDateTime);
                    } else {
                        //svip权益晚于下单时间 说明未过期 则SVIP时间继续累加
                        LocalDateTime plus = SvipEndTime.get(SvipEndTime.size() - 1).plusMonths(validPeriodMonths).plusDays(realGiveDays);
                        right.put("order_code", orderCode);
                        right.put("rights_start_time", dateTimeFormatter.format(SvipEndTime.get(SvipEndTime.size() - 1)));
                        right.put("rights_end_time", dateTimeFormatter.format(plus));
                        orderRight.add(right);
                        right.put("rights_start_dateTime", SvipEndTime.get(SvipEndTime.size() - 1));
                        right.put("rights_end_dateTime", plus);
                        right.put("valid_period_months", validPeriodMonths);
                        right.put("real_give_days", realGiveDays);
                        SvipStartTime.add(SvipEndTime.get(SvipEndTime.size() - 1));
                        SvipEndTime.add(plus);
                    }
                }
                right.put("next", i );
                SVIPRightTimeList.add(right);
                i++;
            }
        }


        // vip  需要比较是否是6.10老权益  ，如果不是老权益 则需要一一比较svip权益
        // vip  6.10之前老权益 不处理  ，时间跨越6.10号 单独处理
        if (VIPList != null && VIPList.size() > 0){
            List<Object> vipListBefore = VIPList.stream().filter(temp -> ((JSONObject) temp).getTimestamp("pay_time").toLocalDateTime().isBefore(date)
                    && (((JSONObject) temp).getTimestamp("end_time").toLocalDateTime().isBefore(date)
                    || ((JSONObject) temp).getTimestamp("end_time").toLocalDateTime().isAfter(date))).collect(Collectors.toList());

            VIPList.removeAll(vipListBefore);

            if (vipListBefore != null && vipListBefore.size() > 0){
                LocalDateTime preVipEndTime = null;
                for (Object k : vipListBefore) {
                    JSONObject order = (JSONObject) k;
                    JSONObject right = new JSONObject();
                    String orderCode = order.getString("order_code");
                    String payTime = order.getString("pay_time");
                    String endTime = order.getString("end_time");
                    Integer validPeriodMonths = order.getInteger("valid_period_months");
                    Integer realGiveDays = order.getInteger("real_give_days");
                    LocalDateTime payLocalDateTime = LocalDateTime.parse(payTime, dateTimeFormatter);
                    LocalDateTime endLocalDateTime = LocalDateTime.parse(endTime, dateTimeFormatter);
                    LocalDateTime plus = null;
                    LocalDateTime rightsStartTime = null;
                    LocalDateTime rightsEndTime = null;

                    if (preVipEndTime == null) {
                        plus = endLocalDateTime;
                        rightsStartTime = payLocalDateTime;
                        rightsEndTime = plus;
                        right.put("order_code", orderCode);
                        right.put("rights_start_time", payTime);
                        right.put("rights_end_time", dateTimeFormatter.format(plus));
                    } else if (preVipEndTime.isBefore(date)){
                        if (preVipEndTime.isBefore(payLocalDateTime)) {
                            //vip权益过期
                            plus = endLocalDateTime;
                            rightsStartTime = payLocalDateTime;
                            rightsEndTime = plus;
                            right.put("order_code", orderCode);
                            right.put("rights_start_time", payTime);
                            right.put("rights_end_time", dateTimeFormatter.format(plus));
                        } else {
                            //上一单权益 覆盖
                            plus = preVipEndTime.plusMonths(validPeriodMonths).plusDays(realGiveDays);
                            rightsStartTime = preVipEndTime;
                            rightsEndTime = plus;
                            right.put("order_code", orderCode);
                            right.put("rights_start_time", dateTimeFormatter.format(preVipEndTime));
                            right.put("rights_end_time", dateTimeFormatter.format(plus));
                        }
                    }else{
                        VIPList.add(order);
                        continue;
                    }

                    if (rightsStartTime.isBefore(date) && rightsEndTime.isBefore(date)){
                        orderRight.add(right);
                        preVipEndTime = plus;
                    }else if (rightsStartTime.isBefore(date) && rightsEndTime.isAfter(date)){
                        //切割成两份   另一份add到viplist进行后续权益处理
                        right.put("rights_end_time", dateTimeFormatter.format(date));
                        orderRight.add(right);
                        preVipEndTime = plus;

                        JSONObject temp = new JSONObject();
                        temp.put("order_code", orderCode);
                        temp.put("pay_time",payTime);
                        temp.put("end_time",endTime);
                        temp.put("rights_start_time", date);
                        temp.put("rights_end_time", plus);
                        temp.put("valid_period_months", validPeriodMonths);
                        temp.put("real_give_days", realGiveDays);
                        temp.put("split_days", Duration.between(rightsStartTime,date).toDays());
                        vipEndTime.add(date);
                        VIPList.add(temp);
                    }else{
                        //权益完全在6.10号之后
                        VIPList.add(order);
                    }

                }
            }

            //重新排序
            List<Object> VIPNewList = VIPList.stream().sorted(Comparator.comparing(temp -> ((JSONObject) temp).getTimestamp("pay_time"))).collect(Collectors.toList());

            if (VIPNewList != null && VIPNewList.size() > 0) {
                LocalDateTime preVipEndTime = null;
                for (Object k : VIPNewList) {
                    JSONObject order = (JSONObject) k;
                    JSONObject right = new JSONObject();
                    String orderCode = order.getString("order_code");
                    String payTime = order.getString("pay_time");
                    String endTime = order.getString("end_time");
                    Integer validPeriodMonths = order.getInteger("valid_period_months");
                    Integer realGiveDays = order.getInteger("real_give_days");
                    Integer spliteDays = order.getInteger("split_days");
                    LocalDateTime payLocalDateTime = LocalDateTime.parse(payTime, dateTimeFormatter);
                    LocalDateTime endLocalDateTime = LocalDateTime.parse(endTime, dateTimeFormatter);

                    if (vipEndTime.size() > 0){
                        preVipEndTime = vipEndTime.get(vipEndTime.size() - 1);
                    }


                    if (vipEndTime.size() == 0 && SvipEndTime.size() == 0) {
                        //无vip svip权益 必定是第一单
                        right.put("order_code", orderCode);
                        right.put("rights_start_time", payTime);
                        right.put("rights_end_time", endTime);
                        orderRight.add(right);
                        vipEndTime.add(endLocalDateTime);

                    } else if (vipEndTime.size() > 0 && SvipEndTime.size() == 0) {
                        //有 vip权益 无svip权益
                        if (preVipEndTime.isBefore(payLocalDateTime)) {
                            //vip权益过期
                            right.put("order_code", orderCode);
                            right.put("rights_start_time", payTime);
                            right.put("rights_end_time", endTime);
                            orderRight.add(right);
                            vipEndTime.add(endLocalDateTime);
                        } else {
                            //上一单权益 覆盖
                            LocalDateTime plus = preVipEndTime.plusMonths(validPeriodMonths).plusDays(realGiveDays);
                            right.put("order_code", orderCode);
                            right.put("rights_start_time", dateTimeFormatter.format(preVipEndTime));
                            right.put("rights_end_time", dateTimeFormatter.format(plus));
                            orderRight.add(right);
                            vipEndTime.add(plus);
                        }

                    } else if (vipEndTime.size() == 0 && SvipEndTime.size() > 0) {
                        //无vip权益 svip权益
                        //svip分为 三种情况  为前中后
                        if (endLocalDateTime.isBefore(SvipStartTime.get(0))){
                            //vip在 svip之前
                            right.put("order_code", orderCode);
                            right.put("rights_start_time", payTime);
                            right.put("rights_end_time", endTime);
                            orderRight.add(right);
                            vipEndTime.add(endLocalDateTime);
                        }else  if (SvipEndTime.get(SvipEndTime.size() -1).isBefore(payLocalDateTime)){
                            //vip在svip之后
                            right.put("order_code", orderCode);
                            right.put("rights_start_time", payTime);
                            right.put("rights_end_time", endTime);
                            orderRight.add(right);
                            vipEndTime.add(endLocalDateTime);
                        }else {
                            //中 交集  确定previptime 时间   之前的时间段全部固定  循环处理  previp之后的时间
                            LocalDateTime endTemp = endLocalDateTime;
                            LocalDateTime startTemp = payLocalDateTime;
                            for (JSONObject temp : SVIPRightTimeList) {
                                LocalDateTime rights_start_dateTime = (LocalDateTime) temp.get("rights_start_dateTime");
                                LocalDateTime rights_end_dateTime = (LocalDateTime) temp.get("rights_end_dateTime");
                                Integer svipValidPeriodMonths = temp.getInteger("valid_period_months");
                                Integer svipRealGiveDays = temp.getInteger("real_give_days");
                                int next = temp.getInteger("next");
                                /* if (rights_start_dateTime.isAfter(preVipEndTime)){*/
                                //比较endTemp 和 preVipEndTime
                                if (preVipEndTime != null && preVipEndTime.isAfter(startTemp)){
                                    startTemp = preVipEndTime;
                                    endTemp = startTemp.plusMonths(validPeriodMonths).plusDays(realGiveDays);
                                }

                                if (endTemp.isBefore(rights_start_dateTime) ){
                                    //前
                                    right.put("order_code", orderCode);
                                    right.put("rights_start_time", dateTimeFormatter.format(startTemp));
                                    right.put("rights_end_time", dateTimeFormatter.format(endTemp));
                                    orderRight.add(right);
                                    vipEndTime.add(endTemp);
                                    break;
                                }else if(startTemp.isAfter(rights_end_dateTime)){
                                    //后
                                    right.put("order_code", orderCode);
                                    right.put("rights_start_time", dateTimeFormatter.format(startTemp));
                                    right.put("rights_end_time", dateTimeFormatter.format(endTemp));
                                    orderRight.add(right);
                                    vipEndTime.add(endTemp);
                                    break;
                                }else {
                                    // 交集
                                    if (startTemp.isBefore(rights_start_dateTime) && endTemp.isAfter(rights_start_dateTime)){
                                        //第一段
                                        right.put("order_code", orderCode);
                                        right.put("rights_start_time", dateTimeFormatter.format(startTemp));
                                        right.put("rights_end_time", dateTimeFormatter.format(rights_start_dateTime));
                                        orderRight.add(right);

                                        startTemp = rights_end_dateTime;
                                        //延迟权益
                                        endTemp = endTemp.plusMonths(svipValidPeriodMonths).plusDays(svipRealGiveDays);


                                    }else{
                                        // svip完全包含 vip
                                        startTemp = rights_end_dateTime;
                                        endTemp = startTemp.plusMonths(validPeriodMonths).plusDays(realGiveDays);
                                    }
                                    if ( next == SVIPRightTimeList.size() -1 ){
                                        if (spliteDays != null){
                                            endTemp = endTemp.minusDays(spliteDays);
                                        }
                                        JSONObject clone = (JSONObject) right.clone();
                                        clone.put("order_code", orderCode);
                                        clone.put("rights_start_time", dateTimeFormatter.format(startTemp));
                                        clone.put("rights_end_time", dateTimeFormatter.format(endTemp));
                                        orderRight.add(clone);
                                        vipEndTime.add(endTemp);
                                    }
                                }
                            }
                        }
                    } else {
                        //有vip 有svip权益  先消耗svip时间  再算vip时间  同样分为前中后 只是需要考虑之前未结束的 vip svip
                        //因为多笔不连续的svip订单没法判断是否过期  还是先前中后
                        LocalDateTime plus = preVipEndTime.plusMonths(validPeriodMonths).plusDays(realGiveDays);
                        if (endLocalDateTime.isBefore(SvipStartTime.get(0)) && plus.isBefore(SvipStartTime.get(0))){
                            //前
                            if (preVipEndTime.isBefore(payLocalDateTime)) {
                                //vip 权益过期
                                right.put("order_code", orderCode);
                                right.put("rights_start_time", payTime);
                                right.put("rights_end_time", endTime);
                                orderRight.add(right);
                                vipEndTime.add(endLocalDateTime);
                            } else {
                                right.put("order_code", orderCode);
                                right.put("rights_start_time", dateTimeFormatter.format(preVipEndTime));
                                right.put("rights_end_time", dateTimeFormatter.format(plus));
                                orderRight.add(right);
                                vipEndTime.add(plus);
                            }

                        } else if (SvipEndTime.get(SvipEndTime.size() - 1).isBefore(payLocalDateTime) || SvipEndTime.get(SvipEndTime.size() - 1).isBefore(preVipEndTime)) {
                            //后
                            if (preVipEndTime .isEqual(payLocalDateTime) ){
                                continue;
                            }else if (preVipEndTime.isBefore(payLocalDateTime)) {
                                //vip 权益过期
                                right.put("order_code", orderCode);
                                right.put("rights_start_time", payTime);
                                right.put("rights_end_time", endTime);
                                orderRight.add(right);
                                vipEndTime.add(endLocalDateTime);
                            } else {
                                right.put("order_code", orderCode);
                                right.put("rights_start_time", dateTimeFormatter.format(preVipEndTime));
                                right.put("rights_end_time", dateTimeFormatter.format(plus));
                                orderRight.add(right);
                                vipEndTime.add(plus);
                            }
                        } else {
                            //中 交集  确定previptime 时间   之前的时间段全部固定  循环处理  previp之后的时间
                            LocalDateTime endTemp = endLocalDateTime;
                            LocalDateTime startTemp = payLocalDateTime;
                            for (JSONObject temp : SVIPRightTimeList) {
                                LocalDateTime rights_start_dateTime = (LocalDateTime) temp.get("rights_start_dateTime");
                                LocalDateTime rights_end_dateTime = (LocalDateTime) temp.get("rights_end_dateTime");
                                Integer svipValidPeriodMonths = temp.getInteger("valid_period_months");
                                Integer svipRealGiveDays = temp.getInteger("real_give_days");
                                int next = temp.getInteger("next");

                                //比较endTemp 和 preVipEndTime
                                if (preVipEndTime != null && preVipEndTime.isAfter(startTemp)){
                                    startTemp = preVipEndTime;
                                    endTemp = startTemp.plusMonths(validPeriodMonths).plusDays(realGiveDays);
                                }

                                if (endTemp.isBefore(rights_start_dateTime) ){
                                    //前
                                    right.put("order_code", orderCode);
                                    right.put("rights_start_time", dateTimeFormatter.format(startTemp));
                                    right.put("rights_end_time", dateTimeFormatter.format(endTemp));
                                    orderRight.add(right);
                                    vipEndTime.add(endTemp);
                                    break;
                                }else if(startTemp.isAfter(rights_end_dateTime)){
                                    //后
                                    //判断是否和下一个无交集
                                    if (next == SVIPRightTimeList.size() -1){
                                        right.put("order_code", orderCode);
                                        right.put("rights_start_time", dateTimeFormatter.format(startTemp));
                                        right.put("rights_end_time", dateTimeFormatter.format(endTemp));
                                        orderRight.add(right);
                                        vipEndTime.add(endTemp);
                                        break;
                                    }else if (next < SVIPRightTimeList.size() -1  && endTemp.isBefore((LocalDateTime) SVIPRightTimeList.get(next+1).get("rights_start_dateTime"))){
                                        right.put("order_code", orderCode);
                                        right.put("rights_start_time", dateTimeFormatter.format(startTemp));
                                        right.put("rights_end_time", dateTimeFormatter.format(endTemp));
                                        orderRight.add(right);
                                        vipEndTime.add(endTemp);
                                        break;
                                    }else {
                                        continue;
                                    }
                                }else {
                                    // 交集
                                    if (startTemp.isBefore(rights_start_dateTime) && endTemp.isAfter(rights_start_dateTime)){
                                        //第一段
                                        right.put("order_code", orderCode);
                                        right.put("rights_start_time", dateTimeFormatter.format(startTemp));
                                        right.put("rights_end_time", dateTimeFormatter.format(rights_start_dateTime));
                                        orderRight.add(right);

                                        startTemp = rights_end_dateTime;
                                        //延迟权益
                                        endTemp = endTemp.plusMonths(svipValidPeriodMonths).plusDays(svipRealGiveDays);


                                    }else{
                                        // svip完全包含 vip
                                        startTemp = rights_end_dateTime;
                                        endTemp = startTemp.plusMonths(validPeriodMonths).plusDays(realGiveDays);
                                    }
                                    if ( next == SVIPRightTimeList.size() -1){
                                        if (spliteDays != null){
                                            endTemp = endTemp.minusDays(spliteDays);
                                        }
                                        JSONObject clone = (JSONObject) right.clone();
                                        clone.put("order_code", orderCode);
                                        clone.put("rights_start_time", dateTimeFormatter.format(startTemp));
                                        clone.put("rights_end_time", dateTimeFormatter.format(endTemp));
                                        orderRight.add(clone);
                                        vipEndTime.add(endTemp);
                                    }
                                }

                            }
                        }
                    }
                }
            }

        }

        List<Object> finalList = orderRight.stream().sorted(Comparator.comparing(temp -> temp.getTimestamp("rights_start_time"))).collect(Collectors.toList());
        //在加上新版本未处理的订单权益
        finalList.addAll(filterAfter);
        export(finalList);

    }

    public void export(List<Object> order){
        System.out.println(order);
        order.forEach(temp -> {
            JSONObject json = (JSONObject) temp;
            String[] strs = new String[3];
            strs[0] = json.getString("order_code");
            strs[1] = json.getString("rights_start_time");
            strs[2] = json.getString("rights_end_time");
            try {
                if (strs[0] != null){
                    forward(strs);
                }

            } catch (HiveException e) {
                e.printStackTrace();
            }
        });
    }

    @Override
    public void close() throws HiveException {

    }

    /**
     * 静态方法，用于解析改版后的用户权益时间段
     */
    public static void main(String[] args) throws HiveException {
        Object[] item = new Object[1];
        //item[0] = "{\"vip_end_time\":\"2022-01-01 00:00:00\",\"svip_end_time\":\"NULL\",\"orders\":[{\"order_code\":\"1623806573509072\",\"order_total\":1.0,\"goods_type\":\"VIP\",\"pay_time\":\"2021-07-01 00:00:00\",\"end_time\":\"2022-07-01 00:00:00\",\"valid_period_months\":12},{\"order_code\":\"1623805619110054\",\"order_total\":1.0,\"goods_type\":\"SVIP\",\"pay_time\":\"2021-09-01 00:00:00\",\"end_time\":\"2022-09-01 00:00:00\",\"valid_period_months\":12}]}";
        //item[0] = "{\"vip_end_time\":\"2022-01-01 00:00:00\",\"svip_end_time\":\"NULL\",\"orders\":[{\"order_code\":\"1623806573509072\",\"order_total\":1.0,\"goods_type\":\"VIP\",\"pay_time\":\"2021-07-01 00:00:00\",\"end_time\":\"2022-07-01 00:00:00\",\"valid_period_months\":12},{\"order_code\":\"1623805619110054\",\"order_total\":1.0,\"goods_type\":\"SVIP\",\"pay_time\":\"2022-02-01 00:00:00\",\"end_time\":\"2023-02-01 00:00:00\",\"valid_period_months\":12}]}";
        //item[0] = "{\"vip_end_time\":\"2022-01-01 00:00:00\",\"svip_end_time\":\"NULL\",\"orders\":[{\"order_code\":\"1623806573509072\",\"order_total\":1.0,\"goods_type\":\"SVIP\",\"pay_time\":\"2021-07-01 00:00:00\",\"end_time\":\"2022-07-01 00:00:00\",\"valid_period_months\":12,\"real_give_days\":0},{\"order_code\":\"1623805619110054\",\"order_total\":1.0,\"goods_type\":\"VIP\",\"pay_time\":\"2021-09-01 00:00:00\",\"end_time\":\"2022-09-01 00:00:00\",\"valid_period_months\":12,\"real_give_days\":0}]}";
        //item[0] = "{\"vip_end_time\":\"NULL\",\"svip_end_time\":\"2022-01-01 00:00:00\",\"orders\":[{\"order_code\":\"1623806573509072\",\"order_total\":1.0,\"goods_type\":\"SVIP\",\"pay_time\":\"2021-07-01 00:00:00\",\"end_time\":\"2022-07-01 00:00:00\",\"valid_period_months\":12,\"real_give_days\":0},{\"order_code\":\"1623805619110054\",\"order_total\":1.0,\"goods_type\":\"VIP\",\"pay_time\":\"2021-09-01 00:00:00\",\"end_time\":\"2022-09-01 00:00:00\",\"valid_period_months\":12,\"real_give_days\":0}]}";
        //item[0]= "{\"vip_end_time\":\"2021-11-20 16:56:17\",\"svip_end_time\":\"2021-11-20 16:56:17\",\"orders\":[{\"order_code\":\"1653620864754122\",\"order_total\":360,\"goods_type\":\"VIP\",\"pay_time\":\"2022-05-27 11:07:56\",\"end_time\":\"2023-07-26 11:07:56\",\"valid_period_months\":12,\"real_give_days\":60},{\"order_code\":\"1638159087571333\",\"order_total\":980,\"goods_type\":\"SVIP\",\"pay_time\":\"2021-11-29 12:11:37\",\"end_time\":\"2022-05-29 12:11:37\",\"valid_period_months\":6,\"real_give_days\":0},{\"order_code\":\"1657618152743958\",\"order_total\":1800,\"goods_type\":\"SVIP\",\"pay_time\":\"2022-07-12 17:29:21\",\"end_time\":\"2023-07-12 17:29:21\",\"valid_period_months\":12,\"real_give_days\":0}]}";
        //item[0] = "{\"vip_end_time\":\"NULL\",\"svip_end_time\":\"NULL\",\"orders\":[{\"order_code\":\"1717471557431814\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-01-27 14:56:15\",\"end_time\":\"2020-01-27 14:56:15\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"3440551470155478\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-01-20 11:27:51\",\"end_time\":\"2021-01-20 11:27:51\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6759515049560468\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-03-04 12:49:20\",\"end_time\":\"2020-03-04 12:49:20\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1101017414611941\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-02 09:21:50\",\"end_time\":\"2020-11-02 09:21:50\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5674194411175171\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-02 09:21:55\",\"end_time\":\"2020-11-02 09:21:55\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1425827501382439\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-10-11 15:04:23\",\"end_time\":\"2020-10-11 15:04:23\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6857211404862187\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-05 09:08:00\",\"end_time\":\"2019-11-05 09:08:00\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5106594546470441\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-14 21:02:53\",\"end_time\":\"2019-11-14 21:02:53\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1844684147841751\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-10-22 11:20:14\",\"end_time\":\"2020-10-22 11:20:14\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1785308015598460\",\"goods_type\":\"SVIP\",\"pay_time\":\"2018-09-19 14:58:08\",\"end_time\":\"2021-09-19 14:58:08\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"2551558880014412\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-12-19 15:50:50\",\"end_time\":\"2020-12-19 15:50:50\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5151402908921584\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-16 14:59:56\",\"end_time\":\"2020-09-16 14:59:56\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6548614513119148\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-27 14:05:16\",\"end_time\":\"2019-09-27 14:05:16\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8909190648489815\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-10-13 16:09:43\",\"end_time\":\"2020-10-13 16:09:43\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1191278813438732\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-19 11:22:16\",\"end_time\":\"2020-09-19 11:22:16\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8373193041644894\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-12-04 14:43:14\",\"end_time\":\"2020-12-04 14:43:14\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"7972640035627212\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-23 13:59:42\",\"end_time\":\"2019-11-23 13:59:42\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5066655556417051\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-03-25 17:22:47\",\"end_time\":\"2020-03-25 17:22:47\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6450369925210655\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-02-19 11:34:25\",\"end_time\":\"2021-02-19 11:34:25\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6635101921663175\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-24 12:06:56\",\"end_time\":\"2020-09-24 12:06:56\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"0776177867739217\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-24 12:03:13\",\"end_time\":\"2020-09-24 12:03:13\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"3108373933258271\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-10-12 09:12:12\",\"end_time\":\"2020-10-12 09:12:12\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5474356379107055\",\"goods_type\":\"SVIP\",\"pay_time\":\"2018-11-05 16:43:13\",\"end_time\":\"2018-12-05 16:43:13\",\"valid_period_months\":1,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1549593354741511\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-12-05 10:38:19\",\"end_time\":\"2020-12-05 10:38:19\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8844118554447531\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-09 15:44:41\",\"end_time\":\"2019-11-09 15:44:41\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6151347243182416\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-12-07 10:52:08\",\"end_time\":\"2019-12-07 10:52:08\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5111761518491114\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-09 14:26:59\",\"end_time\":\"2019-11-09 14:26:59\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8547153455363335\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-03-27 16:31:47\",\"end_time\":\"2020-03-27 16:31:47\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4565987108438003\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-28 10:08:15\",\"end_time\":\"2019-09-28 10:08:15\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1609623497394260\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-28 18:36:09\",\"end_time\":\"2019-09-28 18:36:09\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5831175551135972\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-05 11:57:37\",\"end_time\":\"2020-09-05 11:57:37\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6436954159064201\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-10-13 16:11:03\",\"end_time\":\"2020-10-13 16:11:03\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"7408029713995155\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-08-21 10:42:36\",\"end_time\":\"2019-08-21 10:42:36\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6863831785331833\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-01-24 16:31:21\",\"end_time\":\"2021-01-24 16:31:21\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6881255549538085\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-03-13 20:08:20\",\"end_time\":\"2021-03-13 20:08:20\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8340050910010100\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-27 11:35:10\",\"end_time\":\"2020-09-27 11:35:10\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5024589505168193\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-12-19 11:11:29\",\"end_time\":\"2019-12-19 11:11:29\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"9845918272843032\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-27 14:14:26\",\"end_time\":\"2019-09-27 14:14:26\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4781299215009285\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-23 14:01:22\",\"end_time\":\"2019-11-23 14:01:22\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"0928424662341564\",\"goods_type\":\"SVIP\",\"pay_time\":\"2018-11-14 17:16:35\",\"end_time\":\"2019-11-14 17:16:35\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5108046736881461\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-02-24 13:42:58\",\"end_time\":\"2021-02-24 13:42:58\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"3334142466174059\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-08-13 15:43:41\",\"end_time\":\"2019-08-13 15:43:41\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1094850798745417\",\"goods_type\":\"SVIP\",\"pay_time\":\"2018-11-12 17:20:52\",\"end_time\":\"2020-11-12 17:20:52\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"0215295211841357\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-12-04 20:19:34\",\"end_time\":\"2019-12-04 20:19:34\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5338385695083535\",\"goods_type\":\"SVIP\",\"pay_time\":\"2018-08-27 13:45:53\",\"end_time\":\"2020-08-27 13:45:53\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4451317474245639\",\"goods_type\":\"SVIP\",\"pay_time\":\"2018-09-11 13:44:02\",\"end_time\":\"2020-09-11 13:44:02\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4705056265227568\",\"goods_type\":\"SVIP\",\"pay_time\":\"2018-08-20 09:26:40\",\"end_time\":\"2020-08-20 09:26:40\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4213540555034228\",\"goods_type\":\"SVIP\",\"pay_time\":\"2018-10-23 13:09:14\",\"end_time\":\"2020-10-23 13:09:14\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5086546722396837\",\"goods_type\":\"SVIP\",\"pay_time\":\"2019-03-13 20:09:37\",\"end_time\":\"2021-03-13 20:09:37\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8271651284240512\",\"goods_type\":\"SVIP\",\"pay_time\":\"2019-01-23 09:51:45\",\"end_time\":\"2021-01-23 09:51:45\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4660379641695989\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-01-08 19:14:21\",\"end_time\":\"2021-01-08 19:14:21\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5361715532382219\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-03-12 13:28:28\",\"end_time\":\"2021-03-12 13:28:28\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1199820558026579\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-08-12 12:48:03\",\"end_time\":\"2021-08-12 12:48:03\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"3413423846936696\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-01-28 09:57:44\",\"end_time\":\"2020-01-28 09:57:44\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1613887703365699\",\"goods_type\":\"SVIP\",\"pay_time\":\"2018-09-03 13:51:30\",\"end_time\":\"2020-09-03 13:51:30\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1633230475069626\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-12-11 10:00:57\",\"end_time\":\"2019-12-11 10:00:57\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4644771565048587\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-05 16:44:16\",\"end_time\":\"2019-11-05 16:44:16\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1411366440984825\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-01-22 16:44:29\",\"end_time\":\"2020-01-22 16:44:29\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8240828800871039\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-10-14 20:28:03\",\"end_time\":\"2021-10-14 20:28:03\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"9240700822278181\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-10-14 20:27:01\",\"end_time\":\"2021-10-14 20:27:01\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"0355915385263137\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-10-12 15:52:05\",\"end_time\":\"2021-10-12 15:52:05\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4648214625511552\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-08 10:04:48\",\"end_time\":\"2019-11-08 10:04:48\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"9426571201301886\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-01-15 14:10:40\",\"end_time\":\"2021-01-15 14:10:40\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"7797744733385795\",\"goods_type\":\"SVIP\",\"pay_time\":\"2018-09-12 11:34:57\",\"end_time\":\"2020-09-12 11:34:57\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"0013070925833823\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-16 16:28:17\",\"end_time\":\"2019-09-16 16:28:17\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"3757577291748326\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-22 14:56:19\",\"end_time\":\"2020-11-22 14:56:19\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4218097680811618\",\"goods_type\":\"SVIP\",\"pay_time\":\"2018-11-20 11:32:37\",\"end_time\":\"2020-11-20 11:32:37\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"7349107559915315\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-01-24 11:29:39\",\"end_time\":\"2021-01-24 11:29:39\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8903632636490142\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-11-23 17:50:23\",\"end_time\":\"2020-11-23 17:50:23\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"7497445411211644\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-01-28 09:49:21\",\"end_time\":\"2021-01-28 09:49:21\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"8601015646614022\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-02-21 18:58:50\",\"end_time\":\"2021-02-21 18:58:50\",\"valid_period_months\":24,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1933543641316951\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-19 16:31:59\",\"end_time\":\"2019-09-19 16:31:59\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1572139635187964\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-01-14 16:57:23\",\"end_time\":\"2020-01-14 16:57:23\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"7746170876575995\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-12 14:38:35\",\"end_time\":\"2019-09-12 14:38:35\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"4099520553296951\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-01-15 16:08:25\",\"end_time\":\"2020-01-15 16:08:25\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1155991064389264\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-12-10 12:19:26\",\"end_time\":\"2019-12-10 12:19:26\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1494657537320621\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-10-24 13:53:53\",\"end_time\":\"2019-10-24 13:53:53\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"6752425431771085\",\"goods_type\":\"VIP\",\"pay_time\":\"2019-04-09 10:39:48\",\"end_time\":\"2022-04-09 10:39:48\",\"valid_period_months\":36,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"7540720474103840\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-09-21 13:40:40\",\"end_time\":\"2019-09-21 13:40:40\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"3197633951254758\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-12-21 15:06:19\",\"end_time\":\"2019-12-21 15:06:19\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"5411199993979595\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-10-19 09:59:57\",\"end_time\":\"2019-10-19 09:59:57\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1246459504595011\",\"goods_type\":\"VIP\",\"pay_time\":\"2018-12-05 14:27:09\",\"end_time\":\"2019-12-05 14:27:09\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"}]}";
        item[0] = "{\"vip_end_time\":\"NULL\",\"svip_end_time\":\"NULL\",\"orders\":[{\"order_code\":\"1664767233530212\",\"goods_type\":\"VIP\",\"pay_time\":\"2022-10-03 11:20:42\",\"end_time\":\"2023-12-02 11:20:42\",\"valid_period_months\":12,\"real_give_days\":60,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"},{\"order_code\":\"1632645087946558\",\"goods_type\":\"VIP\",\"pay_time\":\"2021-09-26 16:31:33\",\"end_time\":\"2022-09-26 16:31:33\",\"valid_period_months\":12,\"real_give_days\":0,\"rights_start_time\":\"NULL\",\"rights_end_time\":\"NULL\"}]}";
        OrderRightsReferenceUDTF2 udf = new OrderRightsReferenceUDTF2();
        udf.process(item);
    }
}
