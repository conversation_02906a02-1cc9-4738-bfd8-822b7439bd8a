package com.qcc.udf.casesearch_v3.tmp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.output.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2021年10月20日 10:38
 */
public class CaseV31019DataMaj extends UDF {
    public static String evaluate(String json) {
        List<LawSuitV3OutputEntity> outList = JSON.parseArray(json, LawSuitV3OutputEntity.class);
        if (outList != null) {
            for (LawSuitV3OutputEntity outputEntity : outList) {
                Map<String, AmtInfo> amtInfo = outputEntity.getAmtInfo();
                Map<String,AmtInfo> amt = new TreeMap<>();
                amtInfo.forEach((k,v)->{
                    amt.put(k,v);
                });
                outputEntity.setAmtInfo(amt);
                if (outputEntity.getInfoList() != null) {
                    for (InfoListEntity info : outputEntity.getInfoList()) {
                        if (info.getKtggList() != null) {
                            for (KtggListEntity ktgg : info.getKtggList()) {
                                if (ktgg.getNameAndKeyNo() != null) {
                                    for (NameAndKeyNoEntity nameKey : ktgg.getNameAndKeyNo()) {
                                        nameKey.setLawFirmList(null);
                                    }
                                }
                            }
                        }

                        if (info.getLianList() != null) {
                            for (LianListEntity la : info.getLianList()) {
                                if (la.getNameAndKeyNo() != null) {
                                    for (NameAndKeyNoEntity nameKey : la.getNameAndKeyNo()) {
                                        nameKey.setLawFirmList(null);
                                    }
                                }
                            }
                        }
                        if (CollectionUtils.isNotEmpty(info.getSxList())
                                || CollectionUtils.isNotEmpty(info.getZxList())
                                || CollectionUtils.isNotEmpty(info.getXgList())
                                || CollectionUtils.isNotEmpty(info.getCaseList())
                                || CollectionUtils.isNotEmpty(info.getLianList())
                                || CollectionUtils.isNotEmpty(info.getFyggList())
                                || CollectionUtils.isNotEmpty(info.getKtggList())
                                || CollectionUtils.isNotEmpty(info.getSdggList())
                                || CollectionUtils.isNotEmpty(info.getPcczList())
                                || CollectionUtils.isNotEmpty(info.getGqdjList())
                                || CollectionUtils.isNotEmpty(info.getXjpgList())
                                || CollectionUtils.isNotEmpty(info.getSqtjList())
                                || CollectionUtils.isNotEmpty(info.getZbList())
                        ) {
                            if (CollectionUtils.isEmpty(info.getSfpmList())) {
                                info.setSfpmList(null);
                            }
                            if (CollectionUtils.isEmpty(info.getSqtjList())) {
                                info.setSqtjList(null);
                            }
                            if (CollectionUtils.isEmpty(info.getXdpgjgList())) {
                                info.setXdpgjgList(null);
                            }
                            if (CollectionUtils.isEmpty(info.getXzcjList())) {
                                info.setXzcjList(null);
                            }
                        }


                    }
                }
            }
        }
        return JSON.toJSONString(outList, SerializerFeature.DisableCircularReferenceDetect);
//        JSONArray jsonArray = JSON.parseArray(json);
//        for(int i=0; i< jsonArray.size(); i++) {
//            JSONObject jsonObject = jsonArray.getJSONObject(i);
//            JSONArray InfoList = jsonObject.getJSONArray("InfoList");
//            if (InfoList != null) {
//                for (int j = 0; j < InfoList.size(); j++) {
//                    JSONObject info = InfoList.getJSONObject(i);
//                    JSONArray KtggList = info.getJSONArray("KtggList");
//                    if (KtggList != null) {
//                        for (int k = 0; k < InfoList.size(); k++) {
//                            JSONObject ktgg = KtggList.getJSONObject(i);
//                            JSONArray NameAndKeyNoList = ktgg.getJSONArray("NameAndKeyNo");
//                            if (NameAndKeyNoList != null) {
//                                for (int a = 0; a < InfoList.size(); a++) {
//                                    JSONObject NameKeyNo = NameAndKeyNoList.getJSONObject(i);
//                                    NameKeyNo.put("LawFirmList", null);
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//        }
//            return jsonArray.toJSONString();
    }

    public static void main(String[] args) {
        String json = "[{\"AmtInfo\":{\"647a557ce26905101e8b65ce96a9be68\":{\"Amt\":\"695970\",\"IsValid\":\"1\",\"Type\":\"执行标的\"},\"p7f7aa788be8721b00e9c8fcd07e8ad2\":{\"Amt\":\"695970\",\"IsValid\":\"1\",\"Type\":\"执行标的\"},\"p22dfe03b72c06dbb029659c81b360b4\":{\"Amt\":\"695970\",\"IsValid\":\"1\",\"Type\":\"执行标的\"},\"pb0852fab218f7fe965886cee11b4f90\":{\"Amt\":\"695970\",\"IsValid\":\"1\",\"Type\":\"执行标的\"}},\"AnNoList\":\"（2016）鲁0828执43号\",\"AnnoCnt\":1,\"CaseCnt\":0,\"CaseName\":\"唐洪彬,孙方迪,张太安等执行案件\",\"CaseReason\":\"\",\"CaseRole\":\"[{\\\"D\\\":\\\"首次执行被执行人\\\",\\\"N\\\":\\\"p22dfe03b72c06dbb029659c81b360b4\\\",\\\"O\\\":2,\\\"P\\\":\\\"唐洪彬\\\",\\\"R\\\":\\\"被执行人\\\",\\\"RL\\\":[{\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"首次执行\\\"}]},{\\\"D\\\":\\\"首次执行被执行人\\\",\\\"N\\\":\\\"p7f7aa788be8721b00e9c8fcd07e8ad2\\\",\\\"O\\\":2,\\\"P\\\":\\\"孙方迪\\\",\\\"R\\\":\\\"被执行人\\\",\\\"RL\\\":[{\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"首次执行\\\"}]},{\\\"D\\\":\\\"首次执行被执行人\\\",\\\"N\\\":\\\"pb0852fab218f7fe965886cee11b4f90\\\",\\\"O\\\":2,\\\"P\\\":\\\"张太安\\\",\\\"R\\\":\\\"被执行人\\\",\\\"RL\\\":[{\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"首次执行\\\"}]},{\\\"D\\\":\\\"首次执行被执行人\\\",\\\"N\\\":\\\"\\\",\\\"O\\\":-2,\\\"P\\\":\\\"杨晓平\\\",\\\"R\\\":\\\"被执行人\\\",\\\"RL\\\":[{\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"首次执行\\\"}]},{\\\"D\\\":\\\"首次执行被执行人\\\",\\\"N\\\":\\\"647a557ce26905101e8b65ce96a9be68\\\",\\\"O\\\":0,\\\"P\\\":\\\"金乡县诚和养猪专业合作社\\\",\\\"R\\\":\\\"被执行人\\\",\\\"RL\\\":[{\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"首次执行\\\"}]}]\",\"CaseType\":\"执行案件\",\"CfdfCnt\":0,\"CfgsCnt\":0,\"CfxyCnt\":0,\"CompanyKeywords\":\"647a557ce26905101e8b65ce96a9be68,p22dfe03b72c06dbb029659c81b360b4,p7f7aa788be8721b00e9c8fcd07e8ad2,pb0852fab218f7fe965886cee11b4f90,唐洪彬,孙方迪,张太安,杨晓平,金乡县诚和养猪专业合作社\",\"CourtList\":\"山东省济宁市金乡县人民法院\",\"EarliestDate\":1463673600,\"EarliestDateType\":\"首次执行|案件终本日期\",\"FyggCnt\":0,\"GqdjCnt\":0,\"GroupId\":\"003e650bd7391661db0d8ec7e25c74e1\",\"HbcfCnt\":0,\"Id\":\"ff1f030b2f63e7f1735ae58fbe86e4c2\",\"InfoList\":[{\"AnNo\":\"（2016）鲁0828执43号\",\"CaseList\":[],\"CaseReason\":\"\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"山东省济宁市金乡县人民法院\",\"Defendant\":[{\"KeyNo\":\"p22dfe03b72c06dbb029659c81b360b4\",\"Name\":\"唐洪彬\",\"Org\":2,\"Role\":\"被执行人\"},{\"KeyNo\":\"p7f7aa788be8721b00e9c8fcd07e8ad2\",\"Name\":\"孙方迪\",\"Org\":2,\"Role\":\"被执行人\"},{\"KeyNo\":\"pb0852fab218f7fe965886cee11b4f90\",\"Name\":\"张太安\",\"Org\":2,\"Role\":\"被执行人\"},{\"KeyNo\":\"\",\"Name\":\"杨晓平\",\"Org\":-2,\"Role\":\"被执行人\"},{\"KeyNo\":\"647a557ce26905101e8b65ce96a9be68\",\"Name\":\"金乡县诚和养猪专业合作社\",\"Org\":0,\"Role\":\"被执行人\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1463673600,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"首次执行\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[{\"ExecuteObject\":\"695970\",\"FailureAct\":\"0\",\"Id\":\"2bd74b3ba0ccfaad3845af6c58545dd7\",\"IsValid\":1,\"JudgeDate\":1463673600,\"NameAndKeyNo\":[{\"KeyNo\":\"pb0852fab218f7fe965886cee11b4f90\",\"Name\":\"张太安\",\"Org\":2}]},{\"ExecuteObject\":\"695970\",\"FailureAct\":\"0\",\"Id\":\"6c066306cb3dd3dc2bbf92900fdc8387\",\"IsValid\":1,\"JudgeDate\":1463673600,\"NameAndKeyNo\":[{\"KeyNo\":\"p7f7aa788be8721b00e9c8fcd07e8ad2\",\"Name\":\"孙方迪\",\"Org\":2}]},{\"ExecuteObject\":\"695970\",\"FailureAct\":\"0\",\"Id\":\"70e4e598d70afedcefd54e7408a90ce7\",\"IsValid\":1,\"JudgeDate\":1463673600,\"NameAndKeyNo\":[{\"KeyNo\":\"p22dfe03b72c06dbb029659c81b360b4\",\"Name\":\"唐洪彬\",\"Org\":2}]},{\"ExecuteObject\":\"695970\",\"FailureAct\":\"0\",\"Id\":\"e52b157c97e7244a25eeebbf08b7ab5e\",\"IsValid\":1,\"JudgeDate\":1463673600,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"杨晓平\",\"Org\":-2}]},{\"ExecuteObject\":\"695970\",\"FailureAct\":\"0\",\"Id\":\"e60dfd86d499fd37d192c70113d0825a\",\"IsValid\":1,\"JudgeDate\":1463673600,\"NameAndKeyNo\":[{\"KeyNo\":\"647a557ce26905101e8b65ce96a9be68\",\"Name\":\"金乡县诚和养猪专业合作社\",\"Org\":0}]}],\"ZxList\":[]}],\"KtggCnt\":0,\"LastestDate\":1463673600,\"LastestDateType\":\"首次执行|案件终本日期\",\"LatestTrialRound\":\"首次执行\",\"LianCnt\":0,\"PcczCnt\":0,\"ProcuratorateList\":\"\",\"Province\":\"SD\",\"SdggCnt\":0,\"Source\":\"OT\",\"SxCnt\":0,\"Tags\":\"6\",\"Type\":1,\"XgCnt\":0,\"XjpgCnt\":0,\"XzcfCnt\":0,\"ZbCnt\":5,\"ZxCnt\":0}] ";
        System.out.println(evaluate(json));
    }
}
