package com.qcc.udf.casesearch_v3;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.output.*;
import com.qcc.udf.casesearch_v3.enums.CaseCategoryEnum;
import com.qcc.udf.casesearch_v3.enums.CaseDocTypeEnum;
import com.qcc.udf.casesearch_v3.enums.LawResultEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021年10月18日 10:08
 */
public class GetCaseStatusLevelUDF extends UDF {
    public static String evaluate(List<LawSuitV3OutputEntity> detailList){
        List<Map<String,String>> caseStatusList = new ArrayList<>();
        for (LawSuitV3OutputEntity outputEntity : detailList) {
            if(CollectionUtils.isEmpty(outputEntity.getInfoList())){
                continue;
            }
            String id = outputEntity.getId();
            //按照时间到排的审理程序集合
            List<InfoListEntity> sortedInfoList = outputEntity.getInfoList().stream()
                    .sorted(Comparator.comparing(InfoListEntity::getLatestTimestamp,Comparator.reverseOrder())
                            .thenComparing(InfoListEntity::getTrialRound))
                    .collect(Collectors.toList());

            //最新的审理流程
            InfoListEntity latestTrialRound = sortedInfoList.get(0);
            String trialRoundName = latestTrialRound.getTrialRound();
            String anNo = latestTrialRound.getAnno();

            Set<String> allJudgeResultTags = new HashSet<>();
            for (NameAndKeyNoEntity nameKey : latestTrialRound.getProsecutor()) {
                if(!Strings.isNullOrEmpty(nameKey.getLawsuitResult())){
                    allJudgeResultTags.addAll(Arrays.asList(nameKey.getLawsuitResult().split(",")));
                }
            }
            for (NameAndKeyNoEntity nameKey : latestTrialRound.getDefendant()) {
                if(!Strings.isNullOrEmpty(nameKey.getLawsuitResult())){
                    allJudgeResultTags.addAll(Arrays.asList(nameKey.getLawsuitResult().split(",")));
                }
            }

            List<BaseCaseOutEntity> allOutList = GetCaseStatusUDF.buildAllOutput(latestTrialRound);

            if(CollectionUtils.isEmpty(allOutList)){
                continue;
            }

            allOutList.sort(Comparator.comparing(BaseCaseOutEntity::getTimeStamp,Comparator.reverseOrder())
                    .thenComparing(BaseCaseOutEntity::getTimeType));

            Map<String,String> outMap = new HashMap<>();
            outMap.put("Id",id);
            //最新的案件
            BaseCaseOutEntity latestCase = allOutList.get(0);
            CaseTypeMappingEntity caseInfo = GetCaseStatusUDF.convert(latestCase);
            CaseCategoryEnum category = caseInfo.getCaseType();

            //无优先级规则
            if(category == CaseCategoryEnum.KTGG
                    || category == CaseCategoryEnum.LA
                    || category == CaseCategoryEnum.SQTJ){
                outMap.put("CaseStatus",trialRoundName+"[结案状态-1]");
                outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                caseStatusList.add(outMap);
                continue;
            }
            //无优先级规则
            if(category == CaseCategoryEnum.FYGG){
                FyggListEntity fygg = (FyggListEntity)caseInfo.getCaseInfo();
                if(fygg.getCategory() != null && fygg.getCategory().contains("开庭传票")){
                    outMap.put("CaseStatus",trialRoundName+"[结案状态-1]");
                    outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                    caseStatusList.add(outMap);
                    continue;
                }
                if(fygg.getCategory() != null && (fygg.getCategory().contains("破产") || fygg.getCategory().contains("清算"))){
                    outMap.put("CaseStatus",trialRoundName+"破产受理中");
                    outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                    caseStatusList.add(outMap);
                    continue;
                }
            }

            //优先级1
            if(anNo.contains("财保")){
                if(category == CaseCategoryEnum.GQDJ){
                    outMap.put("CaseStatus","财产保全中");
                    outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                    caseStatusList.add(outMap);
                    continue;
                }
                if(category == CaseCategoryEnum.CPWS){
                    if(allJudgeResultTags.contains(LawResultEnum.CHA_FENG_ZAN_KOU_DONG_JIE.getCode())){
                        outMap.put("CaseStatus","财产保全中");
                        outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                        caseStatusList.add(outMap);
                        continue;
                    }
                }
            }
            //优先级1
            if(trialRoundName.contains("执行")){
                boolean flag = false;
                if(category == CaseCategoryEnum.GQDJ
                        ||category == CaseCategoryEnum.XJPG
                        || category == CaseCategoryEnum.SFPM){
                    flag = true;
                }
                if(category == CaseCategoryEnum.CPWS
                        && allJudgeResultTags.contains(LawResultEnum.CHA_FENG_ZAN_KOU_DONG_JIE.getCode())){
                    flag = true;
                }
                if(category == CaseCategoryEnum.FYGG){
                    FyggListEntity fygg = (FyggListEntity)caseInfo.getCaseInfo();
                    if(fygg.getCategory().contains("拍卖")){
                        flag = true;
                    }
                }
                if(flag){
                    outMap.put("CaseStatus",trialRoundName+"资产强制处置中");
                    outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                    caseStatusList.add(outMap);
                    continue;
                }
            }

            if(category == CaseCategoryEnum.CPWS) {
                //优先级2
                if (allJudgeResultTags.contains(LawResultEnum.JIE_CHU_CHA_FENG_ZAN_KOU_DONG_JIE.getCode())) {
                    outMap.put("CaseStatus", trialRoundName + "解除查冻扣");
                    outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                    caseStatusList.add(outMap);
                    continue;
                }
            }


            if(trialRoundName.contains("执行")){
                List<SXListEntity> sxList = GetCaseStatusUDF.genNoNullList(latestTrialRound.getSxList());
                List<ZXListEntity> zxList = GetCaseStatusUDF.genNoNullList(latestTrialRound.getZxList());
                List<XGListEntity> xgList = GetCaseStatusUDF.genNoNullList(latestTrialRound.getXgList());
                List<XZCJListEntity> xzcjList = GetCaseStatusUDF.genNoNullList(latestTrialRound.getXzcjList());
                List<ZbListEntity> zbList = GetCaseStatusUDF.genNoNullList(latestTrialRound.getXzcjList());

                long hisCount = sxList.stream().filter(x->Objects.equals(x.getIsValid(),0)).count()
                        + zxList.stream().filter(x->Objects.equals(x.getIsValid(),0)).count()
                        + xgList.stream().filter(x->Objects.equals(x.getIsValid(),0)).count()
                        + xzcjList.stream().filter(x->Objects.equals(x.getIsValid(),0)).count();

                long validCount = sxList.stream().filter(x->Objects.equals(x.getIsValid(),1)).count()
                        + zxList.stream().filter(x->Objects.equals(x.getIsValid(),1)).count()
                        + xgList.stream().filter(x->Objects.equals(x.getIsValid(),1)).count()
                        + xzcjList.stream().filter(x->Objects.equals(x.getIsValid(),1)).count();
                String secondLevelName = "";
                //无优先级规则
                if(validCount>0 && CollectionUtils.isEmpty(zbList)){
                    secondLevelName = "强制执行中";
                }
                if(category == CaseCategoryEnum.FYGG){
                    FyggListEntity fygg = (FyggListEntity)caseInfo.getCaseInfo();
                    if(fygg.getCategory().contains("执行文书")){
                        secondLevelName = "强制执行中";
                    }
                }
                //无优先级规则
                if(!Strings.isNullOrEmpty(secondLevelName)){
                    outMap.put("CaseStatus",trialRoundName+secondLevelName);
                    outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                    caseStatusList.add(outMap);
                    continue;
                }

                //无优先级规则
                //部分被告的失信/被执行人/限制高消费/限制出境数据变为历史，剩余部分出现终本案件
                if(hisCount>0 && validCount>0 && CollectionUtils.isNotEmpty(zbList)){
                    outMap.put("CaseStatus",trialRoundName+"终结执行,结案");
                    outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                    caseStatusList.add(outMap);
                    continue;
                }

                //优先级3
                if(validCount == 0 && hisCount>0){
                    secondLevelName = "执行完毕,结案";
                }
                //优先级3
                if(category == CaseCategoryEnum.CPWS) {
                    CaseListEntity cpws = (CaseListEntity) caseInfo.getCaseInfo();
                    boolean allEndFlag = true;
                    lp:for (NameAndKeyNoEntity defInfo : latestTrialRound.getDefendant()) {
                        if(!LawResultEnum.ZHI_XING_WAN_B.getCode().equals(defInfo.getLawsuitResult())){
                            allEndFlag = false;
                            break lp;
                        }
                    }
                    if(allEndFlag){
                        secondLevelName = "执行完毕,结案";
                    }
                }
                //优先级3
                if(!Strings.isNullOrEmpty(secondLevelName)){
                    outMap.put("CaseStatus",trialRoundName+secondLevelName);
                    outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                    caseStatusList.add(outMap);
                    continue;
                }
                //优先级4
                if(category == CaseCategoryEnum.CPWS) {
                    CaseListEntity cpws = (CaseListEntity) caseInfo.getCaseInfo();
                    if(cpws.getCaseType().contains(CaseDocTypeEnum.ADJUDICATE.getType())){
                        if(cpws.getResult().contains("不予执行")){
                            outMap.put("CaseStatus",trialRoundName+"不予执行,结案");
                            outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                            caseStatusList.add(outMap);
                            continue;
                        }
                    }
                }
            }

            //优先级5
            if(category == CaseCategoryEnum.CPWS){
                CaseListEntity cpws = (CaseListEntity) caseInfo.getCaseInfo();
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime judgeDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(cpws.getJudgeDate()*1000L), ZoneId.systemDefault());
                if(allJudgeResultTags.contains(LawResultEnum.ZHI_CHI.getCode())
                        ||allJudgeResultTags.contains(LawResultEnum.DUI_FANG_BEI_ZHI_CHI.getCode())
                        ||allJudgeResultTags.contains(LawResultEnum.BU_ZHI_CHI.getCode())
                        ||allJudgeResultTags.contains(LawResultEnum.DUI_FANG_BU_BEI_ZHI_CHI.getCode())
                        ||allJudgeResultTags.contains(LawResultEnum.BU_FEN_ZHI_CHI.getCode())
                        ||allJudgeResultTags.contains(LawResultEnum.DUI_FANG_BEI_BU_FEN_ZHI_CHI.getCode())
                        ||allJudgeResultTags.contains(LawResultEnum.BU_CHENG_DAN_ZE_RENG.getCode())
                        ||allJudgeResultTags.contains(LawResultEnum.WU.getCode())){
                    if(trialRoundName.contains("一审")){
                        String secondLevelName = "";
                        long days = Duration.between(judgeDate,now).toDays();
                        //判决书
                        if(cpws.getCaseType().contains(CaseDocTypeEnum.VERDICT.getType())){
                            if(days<=15){
                                secondLevelName = "已判决,可上诉[结案状态-2]";
                            }else{
                                secondLevelName = "已判决";
                            }
                        }
                        //裁定书
                        if(cpws.getCaseType().contains(CaseDocTypeEnum.ADJUDICATE.getType())){
                            secondLevelName = "已判决";
                        }

                        if(!Strings.isNullOrEmpty(secondLevelName)){
                            outMap.put("CaseStatus",trialRoundName+secondLevelName);
                            outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                            caseStatusList.add(outMap);
                            continue;
                        }
                    }

                    if(trialRoundName.contains("二审")||trialRoundName.contains("再审")){
                        String secondLevelName = "";
                        //判决书
                        if(cpws.getCaseType().contains(CaseDocTypeEnum.VERDICT.getType())){
                            secondLevelName = "已判决";
                        }
                        //裁定书
                        if(cpws.getCaseType().contains(CaseDocTypeEnum.ADJUDICATE.getType())){
                            secondLevelName = "已裁定";
                        }
                        if(!Strings.isNullOrEmpty(secondLevelName)){
                            outMap.put("CaseStatus",trialRoundName+secondLevelName);
                            outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                            caseStatusList.add(outMap);
                            continue;
                        }
                    }
                }
            }



            if(category == CaseCategoryEnum.CPWS){
                //优先级6
                if(allJudgeResultTags.contains(LawResultEnum.DA_CHENG_TIAO_JIE.getCode())){
                    outMap.put("CaseStatus",trialRoundName+"达成和解,已结案");
                    outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                    caseStatusList.add(outMap);
                    continue;
                }
                //优先级7
                if(allJudgeResultTags.contains(LawResultEnum.BO_HUI.getCode())){
                    if(trialRoundName.contains("一审")){
                        outMap.put("CaseStatus",trialRoundName+"驳回起诉");
                        outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                        caseStatusList.add(outMap);
                        continue;
                    } else if(trialRoundName.contains("二审") || trialRoundName.contains("再审")){
                        outMap.put("CaseStatus",trialRoundName+"驳回上诉");
                        outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                        caseStatusList.add(outMap);
                        continue;
                    }else{
                        outMap.put("CaseStatus",trialRoundName+"驳回");
                        outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                        caseStatusList.add(outMap);
                        continue;
                    }
                }
                //优先级8
                if(allJudgeResultTags.contains(LawResultEnum.CHE_SU.getCode())
                        || allJudgeResultTags.contains(LawResultEnum.SHI_WEI_CHE_SU.getCode())){
                    outMap.put("CaseStatus",trialRoundName+"撤诉,结案");
                    outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                    caseStatusList.add(outMap);
                    continue;
                }
            }


            //优先级9
            if(category == CaseCategoryEnum.CPWS && trialRoundName.contains("执行")) {
                if (allJudgeResultTags.contains(LawResultEnum.SU_SONG_ZHONG_ZHI.getCode())) {
                    outMap.put("CaseStatus", trialRoundName + "诉讼中止");
                    outputEntity.setCaseStatus(outMap.get("CaseStatus"));
                    caseStatusList.add(outMap);
                    continue;
                }
            }
        }

        System.out.println(JSON.toJSONString(caseStatusList));

        return "";
    }
}
