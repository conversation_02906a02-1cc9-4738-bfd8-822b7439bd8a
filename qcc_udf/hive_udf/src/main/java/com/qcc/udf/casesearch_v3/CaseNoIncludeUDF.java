package com.qcc.udf.casesearch_v3;

import com.google.common.collect.Sets;
import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import org.apache.hadoop.hive.ql.exec.UDF;
import parquet.Strings;

import java.util.Arrays;
import java.util.Set;

public class CaseNoIncludeUDF extends UDF {
    public static boolean evaluate(String caseNo1,String caseNo2) {
        if (Strings.isNullOrEmpty(caseNo1) || Strings.isNullOrEmpty(caseNo2)) {
            return false;
        }
        caseNo1 = CommonV3Util.full2Half(caseNo1);
        caseNo2 = CommonV3Util.full2Half(caseNo2);
        Set<String> caseNo1Set  = Sets.newHashSet(Arrays.asList(caseNo1.split(",")));
        Set<String> caseNo2Set  = Sets.newHashSet(Arrays.asList(caseNo2.split(",")));
        long cnt1 = caseNo1Set.stream().filter(data->caseNo2Set.contains(data)).count();
        long cnt2 = caseNo2Set.stream().filter(data->caseNo1Set.contains(data)).count();
        if(cnt1>0 || cnt2>0){
            return true;
        }

        return false;
    }


    public static void main(String[] args) {
        System.out.println(evaluate("(2007)昌 0107执 字12号之一","（2007)1昌 0107执 字12号之一"));
    }
}
