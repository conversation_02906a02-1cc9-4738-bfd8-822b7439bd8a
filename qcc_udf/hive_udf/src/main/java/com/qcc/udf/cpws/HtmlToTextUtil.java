package com.qcc.udf.cpws;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 清洗文本中HTML标签
 */
public class HtmlToTextUtil {

    public static String convert(String content) {
        Pattern p_script, p_style, p_html, p_html1;
        Matcher m_script, m_style, m_html, m_html1;

        // 定义script的正则表达式{或<script[^>]*?>[\\s\\S]*?<\\/script> }
        String regEx_script = "<[\\s]*?script[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?script[\\s]*?>";
        // 定义style的正则表达式{或<style[^>]*?>[\\s\\S]*?<\\/style> }
        String regEx_style = "<[\\s]*?style[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?style[\\s]*?>";
        String regEx_html = "<[^>]+>"; // 定义HTML标签的正则表达式
        String regEx_html1 = "<[^>]+";
        p_script = Pattern.compile(regEx_script, Pattern.CASE_INSENSITIVE);
        m_script = p_script.matcher(content);
        content = m_script.replaceAll(""); // 过滤script标签

        p_style = Pattern.compile(regEx_style, Pattern.CASE_INSENSITIVE);
        m_style = p_style.matcher(content);
        content = m_style.replaceAll(""); // 过滤style标签

        p_html = Pattern.compile(regEx_html, Pattern.CASE_INSENSITIVE);
        m_html = p_html.matcher(content);
        content = m_html.replaceAll(""); // 过滤html标签

        p_html1 = Pattern.compile(regEx_html1, Pattern.CASE_INSENSITIVE);
        m_html1 = p_html1.matcher(content);
        content = m_html1.replaceAll(""); // 过滤html标签

        return content;// 返回文本字符串
    }

    public static String convert2(String content) {
        Pattern p_script, p_style, p_html, p_html1;
        Matcher m_script, m_style, m_html, m_html1;

        // 定义script的正则表达式{或<script[^>]*?>[\\s\\S]*?<\\/script> }
        String regEx_script = "<[\\s]*?script[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?script[\\s]*?>";
        // 定义style的正则表达式{或<style[^>]*?>[\\s\\S]*?<\\/style> }
        String regEx_style = "<[\\s]*?style[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?style[\\s]*?>";
        String regEx_html = "<[^>]+>"; // 定义HTML标签的正则表达式
        String regEx_html1 = "<[^>]+";
        p_script = Pattern.compile(regEx_script, Pattern.CASE_INSENSITIVE);
        m_script = p_script.matcher(content);
        content = m_script.replaceAll("\r\n"); // 过滤script标签

        p_style = Pattern.compile(regEx_style, Pattern.CASE_INSENSITIVE);
        m_style = p_style.matcher(content);
        content = m_style.replaceAll("\r\n"); // 过滤style标签

        p_html = Pattern.compile(regEx_html, Pattern.CASE_INSENSITIVE);
        m_html = p_html.matcher(content);
        content = m_html.replaceAll("\r\n"); // 过滤html标签

        p_html1 = Pattern.compile(regEx_html1, Pattern.CASE_INSENSITIVE);
        m_html1 = p_html1.matcher(content);
        content = m_html1.replaceAll("\r\n"); // 过滤html标签

        return content;// 返回文本字符串
    }
}
