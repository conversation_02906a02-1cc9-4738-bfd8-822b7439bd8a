package com.qcc.udf.risk_analysis;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import groovy.json.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class getKeyNoCnt extends UDF {
    public static int evaluate(String param) {
        int cnt = 0;

        if (StringUtils.isNotEmpty(param)){
            JSONArray array = JSONArray.parseArray(param);
            if (array != null && !array.isEmpty()){
                Iterator<Object> it = array.iterator();
                while (it.hasNext()){
                    JSONObject json = (JSONObject) it.next();
                    if (json.containsKey("A")){
                        cnt = cnt + json.getInteger("A");
                    }
                }
            }
        }

        return cnt;
    }

    public static void main(String[] args) {
        System.out.println(evaluate("[{\"A\":2,\"Type\":7},{\"A\":3,\"Type\":23,\"B\":\"2000\"},{\"Type\":24},{\"A\":0,\"Type\":1},{\"A\":0,\"Type\":2},{\"A\":0,\"Type\":3},{\"A\":0,\"Type\":4},{\"A\":0,\"Type\":5},{\"A\":0,\"Type\":6},{\"A\":0,\"Type\":8},{\"A\":0,\"Type\":9},{\"A\":0,\"Type\":10},{\"A\":0,\"Type\":11},{\"A\":0,\"Type\":12},{\"A\":0,\"Type\":13},{\"A\":0,\"Type\":14},{\"A\":0,\"Type\":15},{\"A\":0,\"Type\":16},{\"A\":0,\"Type\":17},{\"A\":0,\"Type\":18},{\"A\":0,\"Type\":19},{\"A\":0,\"Type\":20},{\"A\":0,\"Type\":21},{\"A\":0,\"Type\":22},{\"A\":0,\"Type\":25},{\"A\":0,\"Type\":26},{\"A\":0,\"Type\":27},{\"A\":0,\"Type\":28}]"));
    }
}
