package com.qcc.udf;

import com.qcc.udf.casesearch_v3.util.CommonV3Util;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;
import parquet.Strings;

import java.util.Arrays;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Auther: zhanqgiang
 * @Date: 2020/11/19 10:00
 * @Description: 案号简单清洗
 */
public class CaseNoCleanUDF extends UDF {
    public static final String SPLIT_REGEX = "  |,";
    public static final String ANNO_REGEX = "^(（|\\()?(19|20)[0-9]{2}(）|\\))?[\\u4e00-\\u9fa50-9\\-\\,，、]{0,}号(之[\\u4e00-\\u9fa5]{0,})?$";

    public static String evaluate(String caseNo) {
        String anNo = caseNo;
        if (Strings.isNullOrEmpty(anNo)) {
            return "";
        }
        anNo = Arrays.stream(anNo.split(SPLIT_REGEX))
                .map(ano -> annoHandle(ano))
                .filter(str -> StringUtils.isNotBlank(str))
                .collect(Collectors.joining(","));
        anNo = anNo.replaceAll("\\(", "（").replaceAll("\\)", "）");
        if (anNo.length() > 500) {
            anNo = anNo.substring(0, 500);
        }

        return anNo;
    }

    private static String annoHandle(String caseNo) {
        if (com.google.common.base.Strings.isNullOrEmpty(caseNo)) {
            return "";
        }
        caseNo = CommonV3Util.full2Half(caseNo);
        caseNo = caseNo.replaceAll(" | |　", "");
        caseNo = caseNo.replaceAll("\\[|【|〔|﹝|<", "(");
        caseNo = caseNo.replaceAll("\\]|】|〕|﹞|>", ")");
        caseNo = caseNo.replaceAll("\r\n|\n", "");
        if (!caseNo.contains("号")) {
            caseNo = caseNo + "号";
        }
        if (!regexMatcher(caseNo, ANNO_REGEX)){
            return  "";
        }
        return caseNo;
    }

    /**
     * 正则判断
     *
     * @param content 文本
     * @param regex 正则
     * @return
     */
    public static boolean regexMatcher(String content,String regex) {
        Pattern p = Pattern.compile(regex);
        Matcher m = p.matcher(content);
        return m.find();
    }

    public static void main(String[] args) {
        System.out.println(evaluate("（2020）粤0391民初2400、2462、2465、4265-4267、4269-4273、4275、4277-4279、4281-4283号"));
    }

}
