package com.qcc.udf.lawer;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * <AUTHOR>
 * @date ：Created in 2021/03/03 13:34
 * @description ：获取省份名称
 */
public class getProvinceName extends UDF {

    public static String evaluate(String param) {
        String result = "";
        if (StringUtils.isEmpty(param)) {
            return result;
        }
        if (param.contains("河北")) {
            result = "河北";
        } else if (param.contains("山西")) {
            result = "山西";
        } else if (param.contains("辽宁")) {
            result = "辽宁";
        } else if (param.contains("吉林")) {
            result = "吉林";
        } else if (param.contains("黑龙江")) {
            result = "黑龙江";
        } else if (param.contains("江苏")) {
            result = "江苏";
        } else if (param.contains("浙江")) {
            result = "浙江";
        } else if (param.contains("安徽")) {
            result = "安徽";
        } else if (param.contains("福建")) {
            result = "福建";
        } else if (param.contains("江西")) {
            result = "江西";
        } else if (param.contains("山东")) {
            result = "山东";
        } else if (param.contains("台湾")) {
            result = "台湾";
        } else if (param.contains("河南")) {
            result = "河南";
        } else if (param.contains("湖北")) {
            result = "湖北";
        } else if (param.contains("湖南")) {
            result = "湖南";
        } else if (param.contains("广东")) {
            result = "广东";
        } else if (param.contains("海南")) {
            result = "海南";
        } else if (param.contains("四川")) {
            result = "四川";
        } else if (param.contains("贵州")) {
            result = "贵州";
        } else if (param.contains("云南")) {
            result = "云南";
        } else if (param.contains("陕西")) {
            result = "陕西";
        } else if (param.contains("甘肃")) {
            result = "甘肃";
        } else if (param.contains("青海")) {
            result = "青海";
        } else if (param.contains("北京")) {
            result = "北京";
        } else if (param.contains("天津")) {
            result = "天津";
        } else if (param.contains("上海")) {
            result = "上海";
        } else if (param.contains("重庆")) {
            result = "重庆";
        } else if (param.contains("内蒙古")) {
            result = "内蒙古";
        } else if (param.contains("广西")) {
            result = "广西";
        } else if (param.contains("宁夏")) {
            result = "宁夏";
        } else if (param.contains("西藏")) {
            result = "西藏";
        } else if (param.contains("新疆")) {
            result = "新疆";
        } else if (param.contains("香港")) {
            result = "香港";
        } else if (param.contains("澳门")) {
            result = "澳门";
        }
        return result;
    }

    public static void main(String[] args) {
        String param = "上海久光律师事务所";
        System.out.println(evaluate(param));
    }
}
