package com.qcc.udf.product;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class NewsTitleExact extends UDF {

    public static String evaluate(String info) {
        Set<String> set = new HashSet<>();
        if (StringUtils.isEmpty(info)) {
            return JSON.toJSONString(set);
        }
        List<DynamicInfo> infoList = JSON.parseArray(info, DynamicInfo.class);
        if (CollectionUtils.isNotEmpty(infoList)) {
            for (DynamicInfo inf : infoList) {
                if(StringUtils.isNotEmpty(inf.getChangeExtend())){
                    Extend extend = JSON.parseObject(inf.getChangeExtend(),Extend.class);
                    if (extend!= null && CollectionUtils.isNotEmpty(extend.getLinks())) {
                        for (Link link : extend.getLinks()) {
                            set.add(link.getTitle());
                        }
                    }
                }

            }
        }
        return JSON.toJSONString(set);
    }

    public static void main(String[] args) {
        String str="[{\"AfterContent\":\"\",\"BeforeContent\":\"\",\"Category\":62,\"ChangeDate\":1575513711,\"ChangeExtend\":\"{\\\"companyList\\\":[{\\\"alias\\\":\\\"邮储银行\\\",\\\"companyName\\\":\\\"中国邮政储蓄银行股份有限公司\\\",\\\"hasLogo\\\":1,\\\"keyNo\\\":\\\"cf08bcdc4aee73dcaed5dd8567d44f5e\\\"},{\\\"alias\\\":\\\"方正证券\\\",\\\"companyName\\\":\\\"方正证券股份有限公司\\\",\\\"hasLogo\\\":1,\\\"keyNo\\\":\\\"9217f84f1ee5444a1088da12745f7c21\\\"}],\\\"id\\\":\\\"665468c89d4a6d3591574c87cc6c2769\\\",\\\"isImportant\\\":0,\\\"keywords\\\":\\\"邮储银行,吴姚东,下设,管理部,投资\\\",\\\"links\\\":[{\\\"href\\\":\\\"\\\",\\\"images\\\":[],\\\"o_url\\\":\\\"https://www.cls.cn/depth/413954\\\",\\\"title\\\":\\\"全国第8家理财子公司开门纳客 中邮理财管理构架、组织构架、投资布局确定\\\",\\\"url\\\":\\\"https://share.qichacha.com/pro/app_11.0.4/news-template/index.html?id=7fecf4e19ce1278fdee09b60921710b7\\\"}],\\\"newTags\\\":[\\\"11001\\\"],\\\"newsId\\\":\\\"7fecf4e19ce1278fdee09b60921710b7\\\",\\\"senti\\\":\\\"positive\\\",\\\"sourceInfo\\\":{\\\"name\\\":\\\"财联社\\\"},\\\"tag\\\":[\\\"1200\\\",\\\"3300\\\"]}\",\"ChangeStatus\":1,\"CreateDate\":1575525065,\"DataType\":1,\"Extend1\":\"\",\"Extend2\":\"\",\"GroupMd5\":\"0c3ea350-b18c-4b8c-be73-927920f31e46\",\"Id\":\"665468c89d4a6d3591574c87cc6c2769\",\"ImportanceFlag\":0,\"IsRisk\":0,\"IsValid\":1,\"KeyNo\":\"9217f84f1ee5444a1088da12745f7c21\",\"Name\":\"方正证券股份有限公司\",\"ObjectId\":\"7fecf4e19ce1278fdee09b60921710b7\",\"UpdateDate\":1579443434}]";
            System.out.println(NewsTitleExact.evaluate(str));
    }

    @Data
    private static class DynamicInfo {
        /**
         * 变更扩展字段
         */
        @JSONField(name = "ChangeExtend")
        private String changeExtend;
    }

    @Data
    private static class Extend {
        private List<Link> links;
    }

    @Data
    private static class Link {
        private String title;
    }
}
