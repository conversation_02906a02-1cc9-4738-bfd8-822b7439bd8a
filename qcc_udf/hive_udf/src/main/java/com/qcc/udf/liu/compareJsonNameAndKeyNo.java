package com.qcc.udf.liu;

import com.alibaba.fastjson.JSONArray;
import com.qcc.udf.cpws.CommonUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.LinkedList;
import java.util.List;

public class compareJsonNameAndKeyNo extends UDF {

    public static String evaluate(String oldInfo, String newInfo)  throws Exception{
        List<entityInfoNameAndKeyNo> oldList = new LinkedList<>();
        if (StringUtils.isNotEmpty(oldInfo)){
            oldList = JSONArray.parseArray(oldInfo, entityInfoNameAndKeyNo.class);
        }

        List<entityInfoNameAndKeyNo> newList = new LinkedList<>();
        if (StringUtils.isNotEmpty(newInfo)){
            newList = JSONArray.parseArray(newInfo, entityInfoNameAndKeyNo.class);
        }

        if (oldList.size() != newList.size()){
            return "1";
        }else{
            List<String> oldSet = new LinkedList<>();
            for (entityInfoNameAndKeyNo item : oldList){
                String n = item.getKeyNo() == null ? "" : item.getKeyNo();
                String p = item.getOrg() == null ? "" : item.getOrg();
                String showName = item.getShowName() == null ? "" : item.getShowName();
                String r = item.getName() == null ? "" : item.getName();
                oldSet.add(CommonUtil.full2Half(n.concat(p).concat(showName).concat(r)));
            }

            List<String> newSet = new LinkedList<>();
            for (entityInfoNameAndKeyNo item : newList){
                String n = item.getKeyNo() == null ? "" : item.getKeyNo();
                String p = item.getOrg() == null ? "" : item.getOrg();
                String showName = item.getShowName() == null ? "" : item.getShowName();
                String r = item.getName() == null ? "" : item.getName();
                newSet.add(CommonUtil.full2Half(n.concat(p).concat(showName).concat(r)));
            }

            boolean flag = false;
            for (String old : oldSet){
                if (!newSet.contains(old)){
                    flag = true;
                }
            }
            for (String old : newSet){
                if (!oldSet.contains(old)){
                    flag = true;
                }
            }

            if (flag){
                return "1";
            }else{
                return "";
            }
        }
    }

    public static void main(String[] args) {
        try {
            System.out.println(evaluate("[{\"P\":\"福建莆田农村商业银行（股份）有限公司灵川支行\",\"R\":\"申请执行人\",\"ShowName\":\"福建莆田农村商业银行股份有限公司灵川支行\",\"N\":\"5a694ea1b8d7cb294242a819c8f7640e\",\"O\":0},{\"P\":\"余阿良\",\"R\":\"被执行人\",\"ShowName\":\"余**\",\"N\":\"\",\"O\":-2},{\"P\":\"黄清芳\",\"R\":\"被执行人\",\"ShowName\":\"黄**\",\"N\":\"\",\"O\":-2}]",
                    "[{\"P\":\"福建莆田农村商业银行(股份)有限公司灵川支行\",\"R\":\"申请执行人\",\"ShowName\":\"福建莆田农村商业银行股份有限公司灵川支行\",\"N\":\"5a694ea1b8d7cb294242a819c8f7640e\",\"O\":0},{\"R\":\"被执行人\",\"P\":\"余阿良\",\"ShowName\":\"余**\",\"N\":\"\",\"O\":-2},{\"P\":\"黄清芳\",\"R\":\"被执行人\",\"ShowName\":\"黄**\",\"N\":\"\",\"O\":-2}]"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

@Data
class entityInfoNameAndKeyNo{
    private String KeyNo;
    private String Org;
    private String ShowName;
    private String Name;
}
