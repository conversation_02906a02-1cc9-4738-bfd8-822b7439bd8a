package com.qcc.udf.cpws.casesearch_v2;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * @Auther: liulh
 * @Date: 2020/6/11 17:54
 * @Description:
 */
public class compareParty extends UDF {
    public static int evaluate(String obj1, String obj2) {
        int result = 0;

        if (StringUtils.isEmpty(obj1) || StringUtils.isEmpty(obj2)){
            return result;
        }
        String[] arr1 = obj1.split(",");
        String[] arr2 = obj2.split(",");

        for (String str : arr1){
            if(str.matches("\\w+")){
                continue;
            }
            for (String str2 : arr2){
                if (str.equals(str2)){
                    result ++;
                }
            }
        }


        return result;
    }

    public static void main(String[] args) {
        System.out.println(evaluate("李德林,张雷雨,付茂洪,王荣德,平阴县农村信用合作联社,4dd69fbdfe2b445374af56463547abef",
                "李国渊,邓大玲,李鑫山,李家吉,袁秀兰,平阴县农村信用合作联社,4dd69fbdfe2b445374af56463547abef"));
    }

}
