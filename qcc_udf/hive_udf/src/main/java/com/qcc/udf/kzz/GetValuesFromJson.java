package com.qcc.udf.kzz;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 从json中提取指定key的值
 * <AUTHOR>
 * @date 2022/4/26
 */
public class GetValuesFromJson extends UDF {

    public static String evaluate(String content, String key) {
        String result = "";
        try {
            if (StringUtils.isNotBlank(content) && StringUtils.isNotBlank(key)) {
                JSONArray array = JSONArray.parseArray(content);
                List<String> list = new ArrayList<>();
                if (!array.isEmpty()) {
                    for (Object obj : array) {
                        JSONObject jsonObject = (JSONObject) obj;
                        String value = jsonObject.getString(key);
                        if(StringUtils.isNotBlank(value)){
                            list.add(value);
                        }

                    }
                }
                if(CollectionUtils.isNotEmpty(list)){
                    result = list.stream().collect(Collectors.joining(","));
                }
            }

        } catch (Exception e) {

        }
        return result;
    }
}
