package com.qcc.udf.patent;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

/**
 * @Auther: daixuan
 * @Date: 2023/03/21
 * @Description: 此UDF函数
 */
public class HandleIpc extends UDF {
    public static String evaluate(String ipc) {
        StringBuilder notInRule = new StringBuilder("");
        StringBuilder regexpRule = new StringBuilder("");
        StringBuilder inRule = new StringBuilder("");
        StringBuilder notRegexpRule = new StringBuilder("");

        if (ipc.contains("(")) {
            String[] values = StringUtils.substringsBetween(ipc, "(", ")");

            for (String value : values) {
                String newValue = value.replace("不含", "");
                String[] li = newValue.split("、");
                for (String item : li) {
                    if (item.contains("*")) {
                        notRegexpRule.append(item.replace("*", "")).append("|");
                    } else {
                        notInRule.append(item).append(",");
                    }
                }
                ipc = ipc.replace(value, "");
            }
            System.out.println(ipc);
            ipc = ipc.replaceAll("[()]", "");
        }

        for (String item : ipc.split("、")) {
            if (item.contains("*")) {
                regexpRule.append(item.replace("*", "")).append("|");
            } else {
                inRule.append(item).append(",");
            }
        }


        StringBuilder result = new StringBuilder();
        result.append(handleStr(regexpRule)).append("&").
                append(handleStr(notRegexpRule)).append("&").
                append(handleStr(notInRule)).append("&").
                append(handleStr(inRule));
        return result.toString();
    }

    public static StringBuilder handleStr(StringBuilder string) {
        String newStr = string.toString();
        if (!newStr.equals("")) {
            return new StringBuilder(newStr.substring(0, newStr.length() - 1));
        } else {
            return string;
        }
    }


    public static void main(String[] args) {

        System.out.println(evaluate("G06F*"));
    }

}