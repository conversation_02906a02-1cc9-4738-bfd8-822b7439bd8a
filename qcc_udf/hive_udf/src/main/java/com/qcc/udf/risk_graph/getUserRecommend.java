package com.qcc.udf.risk_graph;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

/**
 * <AUTHOR>
 */
public class getUserRecommend extends UDF {
    public String evaluate(List<String> sourceList) {
        JSONObject result = new JSONObject();
        Set<String> typeSetCompany = new LinkedHashSet<>();
        Set<String> typeSetPerson = new LinkedHashSet<>();
        Set<String> typeSetOther = new LinkedHashSet<>();
        Set<String> idSet = new LinkedHashSet<>();
        Map<String, Integer> wdMap = new LinkedHashMap<>();
        for (String str : sourceList){
            JSONObject item = JSONObject.parseObject(str);
            String id = item.getString("id");
            String type = item.getString("type");
            String info = item.getString("info");
            if (!idSet.contains(id)){
                if ("1".equals(type)){
                    typeSetCompany.add(id);
                }else if ("3".equals(type)){
                    typeSetPerson.add(id);
                }else if ("2".equals(type)){
                    typeSetOther.add(id);
                }

                JSONArray infoArray = JSONArray.parseArray(info);
                Iterator<Object> it = infoArray.iterator();
                while(it.hasNext()){
                    JSONObject json = (JSONObject)it.next();
                    int cnt = 0;
                    if (wdMap.containsKey(json.getString("WdName"))){
                        cnt = wdMap.get(json.getString("WdName"));
                    }
                    cnt = cnt + json.getInteger("WdCount");
                    wdMap.put(json.getString("WdName"), cnt);
                }

                idSet.add(id);
            }
        }

        // 关系对象分类
        JSONObject sub1 = new JSONObject();
        sub1.put("CompanyCnt", typeSetCompany.size());
        sub1.put("PersonCnt", typeSetPerson.size());
        sub1.put("GovCnt", typeSetOther.size());

        // 关系类型
        JSONObject sub2 = new JSONObject();
        for (RiskWdEnum e : EnumSet.allOf(RiskWdEnum.class)) {
            int wdCnt = wdMap.get(e.name()) == null ? 0 : wdMap.get(e.name());
            if (wdCnt > 0){
                sub2.put(e.name(), wdCnt);
            }
        }

        result.put("A", sub1);
        result.put("B", sub2);

        return result.toString();
    }


    public static void main(String[] args) {
        getUserRecommend aa = new getUserRecommend();
        List<String> infoList = new LinkedList<>();
        infoList.add("{\"id\":\"3a973131d444f655c2a21cff10aa8e12\",\"type\":1,\"info\":\"[{\\\"Amt1\\\":\\\"0\\\",\\\"Amt2\\\":\\\"0\\\",\\\"Details\\\":\\\"\\\",\\\"ForwardFlag\\\":0,\\\"Ids\\\":\\\"d339df2a6ac71451f058668aa06bf388\\\",\\\"RtName\\\":\\\"QSMS\\\",\\\"WdCount\\\":1,\\\"WdDesc\\\":\\\"民事起诉\\\",\\\"WdName\\\":\\\"SFAJ\\\"}]\"}");
        infoList.add("{\"id\":\"f3472e748e70ad59ad256bfef8d58ce9\",\"type\":3,\"info\":\"[{\\\"Amt1\\\":\\\"0\\\",\\\"Amt2\\\":\\\"0\\\",\\\"Details\\\":\\\"\\\",\\\"ForwardFlag\\\":0,\\\"Ids\\\":\\\"3ae8c9f98560ea60b02a21db3400fdbd\\\",\\\"RtName\\\":\\\"GLXG\\\",\\\"WdCount\\\":1,\\\"WdDesc\\\":\\\"限制高消费\\\",\\\"WdName\\\":\\\"XG\\\"}]\"}");
        infoList.add("{\"id\":\"2f38c226303ff3a6660528fde51488df\",\"type\":1,\"info\":\"[{\\\"Amt1\\\":\\\"0\\\",\\\"Amt2\\\":\\\"0\\\",\\\"Details\\\":\\\"\\\",\\\"ForwardFlag\\\":0,\\\"Ids\\\":\\\"3ae8c9f98560ea60b02a21db3400fdbd\\\",\\\"RtName\\\":\\\"SQXG\\\",\\\"WdCount\\\":1,\\\"WdDesc\\\":\\\"限制高消费\\\",\\\"WdName\\\":\\\"XG\\\"},{\\\"Amt1\\\":\\\"0\\\",\\\"Amt2\\\":\\\"0\\\",\\\"Details\\\":\\\"\\\",\\\"ForwardFlag\\\":0,\\\"Ids\\\":\\\"efebcd5a98deb2f56860532290ee0482\\\",\\\"RtName\\\":\\\"QSMS\\\",\\\"WdCount\\\":1,\\\"WdDesc\\\":\\\"民事起诉\\\",\\\"WdName\\\":\\\"SFAJ\\\"}]\"}");
        infoList.add("{\"id\":\"e212fb63ba656dad04e047f78fec788d\",\"type\":3,\"info\":\"[{\\\"Amt1\\\":\\\"0\\\",\\\"Amt2\\\":\\\"0\\\",\\\"Details\\\":\\\"\\\",\\\"ForwardFlag\\\":0,\\\"Ids\\\":\\\"5497cc94945e2ecedf946a12dacc33a9,30d1ee9703d00a304a1b241987b35ba6\\\",\\\"RtName\\\":\\\"QSMS\\\",\\\"WdCount\\\":2,\\\"WdDesc\\\":\\\"民事起诉\\\",\\\"WdName\\\":\\\"SFAJ\\\"}]\"}");

        System.out.println(aa.evaluate(infoList));
    }

}