package com.qcc.udf.cpws.wjp;

import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TextFormatter extends UDF {
    // 定义关键字数组，兼容全半角
    private static final String[] KEYWORDS = {
            "基本案情[:|：]", "审理法院[:|：]", "开始时间[:|：]", "开庭地点[:|：]", "开庭日期（听证日期）[:|：]",
            "承办人[:|：]", "案件名称[:|：]", "案号[:|：]", "案由[:|：]"
    };

    // UDF 方法实现
    public static String addLineBreakBeforeKeywords(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        // 构造正则表达式，兼容全半角
        StringBuilder regexBuilder = new StringBuilder();
        for (String keyword : KEYWORDS) {
            // 添加关键字匹配规则
            if (regexBuilder.length() > 0) {
                regexBuilder.append("|");
            }
            regexBuilder.append(keyword);
        }
        // 增加通用句型匹配规则 (xxx:xxx; 或 xxx：xxx; 格式)
        regexBuilder.append("|\\b[^:;]+[:|：][^:;]+;|\\b[^:]+[:|：][^\n]+\b");

        String regex = "(" + regexBuilder.toString() + ")";

        // 使用正则表达式匹配并替换
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        // 使用 StringBuffer 构建替换后的字符串
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(result, "\n" + matcher.group(1));
        }
        matcher.appendTail(result);

        return result.toString();
    }

    public static void main(String[] args) {
        // 输入字符串
        String input = "基本案情: 审理法院: 桑植县人民法院开始时间: 09时00分开庭地点: 六楼审判庭开庭日期（听证日期）: 2022年09月29日承办人: 侯永勇案件名称: 福建省东昇建设工程有限公司桑植分公司诉王胜年工伤保险待遇纠纷一案案号: （2022）湘0822民初1599号案由: 工伤保险待遇纠纷";

        // 调用格式化方法
        String formattedText = addLineBreakBeforeKeywords(input);

        // 打印结果
        System.out.println(formattedText);
    }
}