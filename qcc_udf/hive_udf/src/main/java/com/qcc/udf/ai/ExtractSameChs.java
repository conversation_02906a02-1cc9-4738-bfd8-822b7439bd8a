package com.qcc.udf.ai;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.HashSet;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 抽取两个文本中相同的字符，除去标点符号等
 */
public class ExtractSameChs extends UDF {

    final static Pattern removePattern = Pattern.compile("[《》？；：“”\\{\\}【】\\|﹃﹄﹁﹂『』「」、〈 〉，。/‘’—！@#￥%…&*（）\\\\·｜!:\"',\\.<>@#\\$%\\^&\\*\\(\\)_+=\\? \r\n\t-]");

    /**
     * 提取相同的字符
     *
     * @param text1
     * @param text2
     * @param flag = 1 去除标点符号，flag = 0 不去除标点符号
     * @return
     */
    public String evaluate(String text1, String text2, String flag) {
        if ("1".equals(flag)) {
            text1 = preprocess(text1);
            text2 = preprocess(text2);
        }
        text1 = text1.toLowerCase();
        text2 = text2.toLowerCase();
        HashSet<String> words1 = strToSet(text1);
        HashSet<String> words2 = strToSet(text2);

        words1.retainAll(words2);

        return StringUtils.join(words1, "");
    }

    /**
     * 提取相同的字符
     *
     * @param text1
     * @param text2
     * @return
     */
    public String evaluate(String text1, String text2) {
        return evaluate(text1, text2, "1");
    }

    /**
     * 预处理 去掉一些 文本
     * @param text
     * @return
     */
    public String preprocess(String text) {
        // 去除标点符号等空格，再转成小写字符
        Matcher m = removePattern.matcher(text);
        return m.replaceAll("");
    }

    public HashSet<String> strToSet(String text) {
        HashSet<String> words = new HashSet<>();
        for (int i = 0; i < text.length(); i++) {
            words.add(text.substring(i, i+1));
        }
        return words;
    }


//    public static void main(String[] args) {
//        String s = "住建部@修订个办法修出了太多的尴尬!";
//        String s1 = "住建部修订个办法!";
//        System.out.println(new ExtractSameChs().evaluate(s, s1, "0"));
//    }

}