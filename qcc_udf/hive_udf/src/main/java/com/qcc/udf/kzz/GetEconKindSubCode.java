package com.qcc.udf.kzz;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 从企业类型中提取子类型
 * <AUTHOR>
 * @date 2022/4/29
 */
public class GetEconKindSubCode extends UDF{

    /**
     * 51- 法人独资
     * 52- 港澳台独资
     * 53-个人独资企业
     * 54-国有独资
     * 55-外商独资
     * 56-自然人独资
     *
     * @param econKind
     * @param econKindCode
     * @return
     */
    public static String evaluate(String econKind,String econKindCode) {
        String result=econKindCode;
        try{
            if(StringUtils.isNotBlank(econKindCode)){
                List<String> econKindCodeList = Arrays.asList(econKindCode.split(","));
                List<String> econKindCodeTempList = econKindCodeList.stream().collect(Collectors.toList());
                if(econKindCodeList.contains("50")){
                    //提取独资企业的子类型
                    if(econKind.contains("国有独资")){
                        econKindCodeTempList.add("54");
                    }
                    if(econKind.contains("个人独资")){
                        econKindCodeTempList.add("53");
                    }
                    if(econKind.contains("外国")||econKind.contains("外商")){
                        if(econKind.contains("独资")&&!econKind.contains("非独资")){
                            econKindCodeTempList.add("55");
                        }
                    }else if(econKind.contains("台")||econKind.contains("港")||econKind.contains("澳")){
                        econKindCodeTempList.add("52");
                    }else if(econKind.contains("自然人独资")){
                        econKindCodeTempList.add("56");
                    }else if(econKind.contains("法人独资")){
                        econKindCodeTempList.add("51");
                    }
                }
                result = econKindCodeTempList.stream().collect(Collectors.joining(","));
            }
        }catch (Exception e){

        }
        return result;
    }

//    public static void main(String[] args) {
//        String content ="有限责任公司(法人独资,私营)";
//        String percent = evaluate(content,"50");
//        System.out.println(percent+"-"+content);
//
//    }
}
