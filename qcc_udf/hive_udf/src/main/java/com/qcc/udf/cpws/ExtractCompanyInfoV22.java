package com.qcc.udf.cpws;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.casesearch_v3.role.CaseNoTrialRoundEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;
import scala.Tuple2;

import java.util.*;
import java.util.stream.Collectors;

public class ExtractCompanyInfoV22 extends UDF {
    public static List<String> roleList = Arrays.asList("案外人", "保全申请人", "保全申请人(原告)", "被保全人", "被保全人(被告)",
            "被处罚人", "被罚款人", "被吿", "被告", "被告(案外人)", "被告(被保全人)", "被告(被告)", "被告(被申请人)", "被告(被申请执行人)", "被告(被执行人)",
            "被告(本诉被告)", "被告(并案被告)", "被告(并案原告)", "被告(第二被告)", "被告(第三被告)", "被告(第三人)", "被告(第一被告)", "被告(反诉被告)", "被告(反诉第三人)", "被告(反诉人)", "被告(反诉原告)", "被告(复议机关)", "被告(后诉原告)", "被告(互诉原告)", "被告(互为原告)", "被告(暨原告)", "被告(另案原告)", "被告(申请保全人)", "被告(申请人)", "被告(申请执行人", "被告(申请执行人)", "被告(提出反对意见的债权人)", "被告(异议人)", "被告(原告)", "被告(原审被告)", "被告(原审原告)", "被告(原行政机关)", "被告(债权人)", "被告(执行案外人)", "被告(执行申请人)", "被告(追加)", "被告/被上诉人", "被告[被执行人]", "被告[执行案外人]", "被告单位", "被告当事人", "被告方", "被告暨法定代理人", "被告暨反诉原告", "被告暨原告", "被告人", "被告人(附带民事诉讼被告)", "被告人(附带民事诉讼被告人)", "被告人(暨附带民事诉讼被告人)", "被告人暨附带民事公益诉讼被告", "被告人暨附带民事公益诉讼被告人", "被告人暨附带民事诉讼被告人", "被告人暨刑事附带民事诉讼被告人", "被告上诉人", "被告申请人", "被告执行人", "被害单位", "被害人", "被监护人", "被解除保全申请人", "被救助人", "被拘留、罚款人", "被拘留人", "被抗诉人(原审被告人)", "被判处罚金人", "被起诉人", "被请求人", "被请执行人", "被上诉(原审被告)", "被上诉(原审原告)", "被上诉人", "被上诉人(案外人)", "被上诉人(被告)", "被上诉人(被申请人)", "被上诉人(被申请人、原审被告)", "被上诉人(被申请人、原审原告)",
            "被上诉人(被申诉人、原审被告)", "被上诉人(被申诉人、原审原告)", "被上诉人(被执行人)", "被上诉人(本诉被告、反诉原告)", "被上诉人(本诉原告、反诉被告)", "被上诉人(第三人)", "被上诉人(反诉被告)", "被上诉人(反诉原告)", "被上诉人(附带民事诉讼原告人)",
            "被上诉人(申请执行人)", "被上诉人(申诉人、原审被告)", "被上诉人(一审被告)", "被上诉人(一审被告、案外人)", "被上诉人(一审被告、被申请人)", "被上诉人(一审被告、被执行人)", "被上诉人(一审被告、二审被上诉人)", "被上诉人(一审被告、反诉原告)", "被上诉人(一审被告、复议机关)", "被上诉人(一审被告、申请执行人)", "被上诉人(一审被告、一审反诉原告)", "被上诉人(一审被告、再审被申请人)", "被上诉人(一审被告、再审申请人)", "被上诉人(一审被告、执行案外人)", "被上诉人(一审被告人)", "被上诉人(一审被起诉人)", "被上诉人(一审被申请人)", "被上诉人(一审本诉被告、反诉原告)", "被上诉人(一审本诉原告、反诉被告)", "被上诉人(一审第三人)", "被上诉人(一审第三人、被执行人)", "被上诉人(一审附带民事诉讼原告人)", "被上诉人(一审原告)", "被上诉人(一审原告、案外人)", "被上诉人(一审原告、被告)", "被上诉人(一审原告、被申请人)", "被上诉人(一审原告、二审被上诉人)", "被上诉人(一审原告、反诉被告)", "被上诉人(一审原告、一审反诉被告)", "被上诉人(一审原告、再审被申请人)", "被上诉人(一审原告、再审申请人)", "被上诉人(一审原告、执行案外人)", "被上诉人(一审原告暨反诉被告)", "被上诉人(原告)", "被上诉人(原告被告)", "被上诉人(原告原告)", "被上诉人(原审)", "被上诉人(原审被告)", "被上诉人(原审被告)被告", "被上诉人(原审被告、案外人)", "被上诉人(原审被告、被保全人)", "被上诉人(原审被告、被申请人)",
            "被上诉人(原审被告、被申诉人)", "被上诉人(原审被告、被执行人)", "被上诉人(原审被告、并案原告)", "被上诉人(原审被告、反诉被告)", "被上诉人(原审被告、反诉第三人)", "被上诉人(原审被告、反诉原告)",
            "被上诉人(原审被告、复议机关)", "被上诉人(原审被告、互诉原告)", "被上诉人(原审被告、互为原告)", "被上诉人(原审被告、另案原告)", "被上诉人(原审被告、申请执行人)", "被上诉人(原审被告、申诉人)", "被上诉人(原审被告、原告)",
            "被上诉人(原审被告、原审反诉原告)", "被上诉人(原审被告、原审原告)", "被上诉人(原审被告、原行政机关)", "被上诉人(原审被告、再审被申请人)", "被上诉人(原审被告、再审被申诉人)", "被上诉人(原审被告、再审申请人)", "被上诉人(原审被告、债权人)",
            "被上诉人(原审被告、执行案外人)", "被上诉人(原审被告并为原告)", "被上诉人(原审被告二)", "被上诉人(原审被告反诉原告)", "被上诉人(原审被告及反诉原告)", "被上诉人(原审被告暨反诉原告)", "被上诉人(原审被告暨复议机关)", "被上诉人(原审被告暨原告)",
            "被上诉人(原审被告六)", "被上诉人(原审被告人)", "被上诉人(原审被告三)", "被上诉人(原审被告四)", "被上诉人(原审被告一)", "被上诉人(原审被起诉人)", "被上诉人(原审被申请人)", "被上诉人(原审被申诉人)", "被上诉人(原审本诉被告)",
            "被上诉人(原审本诉被告、反诉原告)", "被上诉人(原审本诉原告、反诉被告)", "被上诉人(原审本诉原告、原审反诉被告)", "被上诉人(原审第二被告)", "被上诉人(原审第三被告)", "被上诉人(原审第三人)", "被上诉人(原审第三人、被执行人)",
            "被上诉人(原审第三人、反诉被告)", "被上诉人(原审第一被告)", "被上诉人(原审反诉被告)", "被上诉人(原审反诉原告)", "被上诉人(原审附带民事诉讼被告)", "被上诉人(原审附带民事诉讼被告人)", "被上诉人(原审附带民事诉讼原告人)",
            "被上诉人(原审互为原、被告)", "被上诉人(原审互为原被告)", "被上诉人(原审申请人)", "被上诉人(原审诉讼地位)", "被上诉人(原审原、被告)", "被上诉人(原审原告&lt;反诉被告&gt;)", "被上诉人(原审原告)", "被上诉人(原审原告)(反诉被告)",
            "被上诉人(原审原告、案外人)", "被上诉人(原审原告、被告)", "被上诉人(原审原告、被申请人)", "被上诉人(原审原告、被申诉人)", "被上诉人(原审原告、并案被告)", "被上诉人(原审原告、反诉被告)", "被上诉人(原审原告、反诉原告)", "被上诉人(原审原告、��诉被告)",
            "被上诉人(原审原告、互为被告)", "被上诉人(原审原告、另案被告)", "被上诉人(原审原告、申请执行人)", "被上诉人(原审原告、原审被告)", "被上诉人(原审原告、原审反诉被告)", "被上诉人(原审原告、再审被申请人)", "被上诉人(原审原告、再审被申诉人)",
            "被上诉人(原审原告、再审申请人)", "被上诉人(原审原告、执行案外人)", "被上诉人(原审原告并为被告)", "被上诉人(原审原告二)", "被上诉人(原审原告反诉被告)", "被上诉人(原审原告及反诉被告)", "被上诉人(原审原告暨被告)", "被上诉人(原审原告暨反诉被告)",
            "被上诉人(原审原告暨诉讼代表人)", "被上诉人(原审原告兼反诉被告)", "被上诉人(原审原告一)", "被上诉人(原审原审)", "被上诉人(原审追加被告)", "被上诉人(再审被告)", "被上诉人(再审被申请人)", "被上诉人(再审被申请人、原审被告)",
            "被上诉人(再审被申请人、原审原告)", "被上诉人(再审被申诉人、一审被告)", "被上诉人(再审第三人)", "被上诉人(再审申请人)", "被上诉人(再审申请人、原审被告)", "被上诉人(再审申请人、原审原告)", "被上诉人(再审原告)",
            "被上诉人[原审被告(原告)]", "被上诉人[原审原告(被告)]", "被上诉人二(原审被告)", "被上诉人二(原审被告二)", "被上诉人三(原审被告)", "被上诉人一(原审被告)", "被上诉人一(原审被告一)", "被上诉人一(原审原告)", "被申请保全人",
            "被申请变更人", "被申请变更人(申请执行人)", "被申请方", "被申请人", "被申请人(案外人)", "被申请人(案外人、一审被告、二审被上诉人)", "被申请人(保全申请人)", "被申请人(被保全人)", "被申请人(被告)", "被申请人(被告人)", "被申请人(被上诉人、原审被告)", "被申请人(被申请追加人)", "被申请人(被执行人)", "被申请人(被执行人、一审被告、二审被上诉人)", "被申请人(第三人)", "被申请人(二审被上诉人)", "被申请人(二审被上诉人、一审被告)", "被申请人(二审被上诉人、一审原告)", "被申请人(二审被上诉人、原审被告)", "被申请人(二审被上诉人、原审原告)", "被申请人(二审上诉人)", "被申请人(二审上诉人、一审被告)", "被申请人(二审上诉人、一审原告)", "被申请人(劳动仲裁申请人)", "被申请人(利害关系人)", "被申请人(申请保全人)", "被申请人(申请执行人)", "被申请人(一、二审第三人)", "被申请人(一审、二审第三人)", "被申请人(一审被告)", "被申请人(一审被告，二审上诉人)", "被申请人(一审被告、被上诉人)", "被申请人(一审被告、被执行人)", "被申请人(一审被告、被执行人、二审被上诉人)", "被申请人(一审被告、并案原告、二审上诉人)", "被申请人(一审被告、二审被上诉人)", "被申请人(一审被告、二审被上诉人、被执行人)", "被申请人(一审被告、二审被上诉人、申请执行人)", "被申请人(一审被告、二审被申请人)", "被申请人(一审被告、二审被诉人)", "被申请人(一审被告、二审上诉人)", "被申请人(一审被告、二审上诉人、申请执行人)", "被申请人(一审被告、二审原审被告)", "被申请人(一审被告、反诉被告、二审被上诉人)", "被申请人(一审被告、反诉原告)", "被申请人(一审被告、反诉原告、二审被上诉人)", "被申请人(一审被告、反诉原告、二审上诉人)", "被申请人(一审被告、申请执行人、二审被上诉人)", "被申请人(一审被告、一审反诉原告、二审被上诉人)", "被申请人(一审被告、一审反诉原告、二审上诉人)", "被申请人(一审被告、原告、二审被��诉人)", "被申请人(一审被告、原告、二审上诉人)", "被申请人(一审被告二审被上诉人)", "被申请人(一审被告暨反诉原告、二审被上诉人)", "被申请人(一审被告暨反诉原告、二审上诉人)", "被申请人(一审被起诉人、二审被上诉人)", "被申请人(一审被申请人、二审被上诉人)", "被申请人(一审本诉原告、反诉被告、二审被上诉人)", "被申请人(一审第三人)", "被申请人(一审第三人、二审被上诉人)", "被申请人(一审第三人、二审第三人)", "被申请人(一审第三人、二审上诉人)", "被申请人(一审反诉被告)", "被申请人(一审原告)", "被申请人(一审原告、被告、二审被上诉人)", "被申请人(一审原告、被告、二审上诉人)", "被申请人(一审原告、被上诉人)", "被申请人(一审原告、二审被上诉人)", "被申请人(一审原告、二审被申请人)", "被申请人(一审原告、二审被诉人)", "被申请人(一审原告、二审上诉人)", "被申请人(一审原告、二审上诉人、执行案外人)", "被申请人(一审原告、反诉被告)", "被申请人(一审原告、反诉被告、二审被上诉人)", "被申请人(一审原告、反诉被告、二审上诉人)", "被申请人(一审原告、一审反诉被告、二审被上诉人)", "被申请人(一审原告、一审反诉被告、二审上诉人)", "被申请人(一审原告二审被上诉人)", "被申请人(一审原告及反诉被告、二审被上诉人)", "被申请人(一审原告即反诉被告、二审被上诉人)", "被申请人(一审原告暨反诉被告、二审被上诉人)", "被申请人(一审原告暨反诉被告、二审上诉人)", "被申请人(异议人)", "被申请人(原案被执行人)", "被申请人(原保全申请人)", "被申请人(原被申请人)", "被申请人(原被申请执行人)", "被申请人(原被执行人)", "被申请人(原告)", "被申请人(原申请人)", "被申请人(原申请执行人)", "被申请人(原审被告)", "被申请人(原审被告、被上诉人)", "被申请人(原审被告、二审被上诉人)", "被申请人(原审被告、二审上诉人)", "被申请人(原审被告、反诉原告)", "被申请人(原审被上诉人)", "被申请人(原审被申请人)", "被申请人(原审被执行人)", "被申请人(原审第三人)", "被申请人(原审第三人、二审被上诉人)", "被申请人(原审申请执行人)", "被申请人(原审原告)", "被申请人(原审原告、被上诉人)", "被申请人(原审原告、二审被上诉人)", "被申请人(原审原告、二审上诉人)", "被申请人(原审原告、反诉被告)", "被申请人(原审原告、反诉被告、二审被上诉人)", "被申请人(原一审被告)", "被申请人(原一审被告、二审被上诉人)", "被申请人(原一审被告、二审上诉人)", "被申请人(原一审被告、原二审被上诉人)", "被申请人(原一审原告)", "被申请人(原一审原告、二审被上诉人)", "被申请人(原一审原告、二审上诉人)", "被申请人(原仲裁被申请人)", "被申请人(原仲裁申请人)", "被申请人(债务人)", "被申请人(仲裁被申请人)", "被申请人(仲裁第二被申请人)", "被申请人(仲裁申请人)", "被申请人(仲裁申请人、反请求被申请人)", "被申请人执行人", "被申请再审人", "被申请再审人(原审被告)", "被申请再审人(原审原告)", "被申请执行人", "被申请追加人", "被申请追加主体", "被申诉人", "被申诉人(被执行人)", "被申诉人(赔偿义务机关)", "被申诉人(申请执行人)", "被申诉人(一审被告)", "被申诉人(一审被告、二审被上诉人)", "被申诉人(一审被告、二审被上诉人、被申请人)", "被申诉人(一审被告、二审被上诉人、原被申请人)", "被申诉人(一审被告、二审被上诉人、原再审被申请人)", "被申诉人(一审被告、二审被上诉人、再审被申请人)", "被申诉人(一审被告、二审上诉人)", "被申诉人(一审被告、二审上诉人、被申请人)", "被申诉人(一审被告、二审上诉人、原被申请人)", "被申诉人(一审被告、二审上诉人、原再审被申请人)", "被申诉人(一审被告、二审上诉人、再审被申请人)", "被申诉人(一审被告、二审上诉人、再审申请人)", "被申诉人(一审被告、反诉原告、二审被上诉人)", "被申诉人(一审被告、反诉原告、二审上诉人)", "被申诉人(一审被告、再审被申请人)", "被申诉人(一审第三人)", "被申诉人(一审第三人、二审被上诉人)", "被申诉人(一审第三人、二审上诉人)", "被申诉人(一审原告)", "被申诉人(一审原告、二审被上诉人)", "被申诉人(一审原告、二审被上诉人、被申请人)", "被申诉人(一审原告、二审被上诉人、原被申请人)", "被申诉人(一审原告、二审被上诉人、原再审被申请人)", "被申诉人(一审原告、二审被上诉人、再审被申请人)", "被申诉人(一审原告、二审上诉人)", "被申诉人(一审原告、二审上诉人、被申请人)", "被申诉人(一审原告、二审上诉人、再审被申请人)", "被申诉人(一审原告、反诉被告、二审被上诉人)", "被申诉人(一审原告、反诉被告、二审上诉人)", "被申诉人(一审原告、再审被申请人)", "被申诉人(原赔偿义务机关)", "被申诉人(原审被告)", "被申诉人(原审被告、二审被上诉人)", "被申诉人(原审第三人)", "被申诉人(原审原告)", "被诉监护人", "被诉人", "被诉人(一审原告)", "被诉人(原审被告)", "被诉人(原审原告)", "被移送人", "被异议人", "被异议人(案外人)", "被异议人(被申请人)", "被异议人(被执行人)", "被异议人(申请人)", "被异议人(申请执行人)", "被异议人(原案被执行人)", "被异议人(原案申请执行人)", "被异议人(原申请执行人)", "被再审申请人(一审被告、二审被上诉人)", "被再审申请人(一审原告、二审被上诉人)", "被执人", "被执行单位", "被执行第三人", "被执行人", "被执行人(被保全人)", "被执行人(被告)", "被执行人(被告、被申请人)", "被执行人(被告人)", "被执行人(被申请人)", "被执行人(担保人)", "被执行人(单位)", "被执行人(刑事被告人)", "被执行人(异议人)", "被执行人(原审被告)", "被执行人(原异议人)", "被执行人(仲裁被申请人)", "被执行人(追加)", "被执行人被告", "被执行人被告人", "被执行申请人", "被执执行人", "被中请人", "被追加人", "担保人", "当事人", "第三人", "第三人(原审第三人)", "二审被上诉人", "二审被上诉人(一审被告)", "二审被上诉人(一审被告、反诉原告)", "二审被上诉人(一审第三人", "二审被上诉人(一审第三人)", "二审被上诉人(一审原告)", "二审被上诉人(一审原告、反诉被告)", "二审被上诉人(原审被告)", "二审被上诉人(原审原告)", "二审被上诉人、一审被告", "二审被上诉人、一审原告", "二审第三人", "二审第三人(一审第三人)", "二审上诉人", "二审上诉人(一审被告)", "二审上诉人(一审被告、反诉原告)", "二审上诉人(一审第三人)", "二审上诉人(一审起诉人)", "二审上诉人(一审原告)", "二审上诉人(一审原告、反诉被告)", "二审上诉人(原审被告)", "二审上诉人(原审原告)", "二审上诉人、一审被告", "二审原审被告(一审被告)", "二审原审第三人(一审第三人)", "犯罪嫌疑人", "附带民事诉讼原告", "附带民事诉���原告单位", "附带民事诉讼原告人", "附带民事原告人", "附带民诉原告人", "复议被申请人", "复议机关", "复议申请人", "复议申请人(案外人)", "复议申请人(案外人、异议人)", "复议申请人(保全申请人)", "复议申请人(被保全人)", "复议申请人(被罚款人)", "复议申请人(被告)", "复议申请人(被害人)", "复议申请人(被申请人)", "复议申请人(被申请追加人)", "复议申请人(被执行人)", "复议申请人(被执行人、异议人)", "复议申请人(被执行人、原异议人)", "复议申请人(担保人)", "复议申请人(第三人)", "复议申请人(利害关系人)", "复议申请人(利害关系人、异议人)", "复议申请人(申请保全人)", "复议申请人(申请人)", "复议申请人(申请人、申请执行人)", "复议申请人(申请执行人)", "复议申请人(申请执行人、异议人)", "复议申请人(协助执行人)", "复议申请人(异议人)", "复议申请人(异议人、案外人)", "复议申请人(异议人、被保全人)", "复议申请人(异议人、被申请人)", "复议申请人(异议人、被执行人)", "复议申请人(异议人、第三人)", "复议申请人(异议人、利害关系人)", "复议申请人(异议人、申请执行人)", "复议申请人(原被执行人)", "复议申请人(原告)", "复议申请人(原申请执行人)", "复议申请人(原审案外人)", "复议申请人(原审第三人)", "复议申请人(原审申请执行人)", "复议申请人(原审异议人)", "复议申请人(原审异议人、利害关系人)", "复议申请人(原异议人)", "复议申请人(原异议人、被执行人)", "复议申请人(原异议人、利害关系人)", "复议申请人(原异议人、申请执行人)", "复议申请人(追加被执行人)", "公诉机关", "公诉机关暨附带民事公益诉讼起诉人", "公诉人", "解除保全申请人", "解除保全申请人(案外人)", "解除保全申请人(保全被申请人)", "解除保全申请人(保全申请人)", "解除保全申请人(被保全人)", "解除保全申请人(被告)", "解除保全申请人(被申请人)", "解除保全申请人(申请保全人)", "解除保全申请人(申请人)", "解除保全申请人(原被申请人)", "解除保全申请人(原告)", "解除保全申请人(原申请人)", "救助申请人", "抗诉机关", "抗诉机关(原公诉机关)", "利害关系人", "赔偿请求人", "赔偿义务机关", "起诉人", "上诉人", "上诉人(案外人)", "上诉人(被告)", "上诉人(被告、反诉原告)", "上诉人(被告人)", "上诉人(被上诉人、原审被告)", "上诉人(被上诉人、原审被告、反诉原告)", "上诉人(被上诉人、原审原告)", "上诉人(被上诉人、原审原告、反诉被告)", "上诉人(被申请人)", "上诉人(被申请人、原审被告)", "上诉人(被申请人、原审原告)", "上诉人(被申诉人、原审原告)", "上诉人(本诉被告、反诉原告)", "上诉人(本诉原告、反诉被告)", "上诉人(第三人)", "上诉人(反诉原告)", "上诉人(附带民事诉讼被告人)", "上诉人(附带民事诉讼原告人)", "上诉人(起诉人)", "上诉人(申请人)", "上诉人(申请再审人、原审被告)", "上诉人(申请执行人)", "上诉人(申请执行人、原审被告)", "上诉人(申诉人、原审被告)", "上诉人(申诉人、原审原告)", "上诉人(一审被告)", "上诉人(一审被告、案外人)", "上诉人(一审被告、被申请人)", "上诉人(一审被告、并案原告)", "上诉人(一审被告、反诉原告)", "上诉人(一审被告、申请执行人)", "上诉人(一审被告、申诉人)", "上诉人(一审被告、一审反诉原告)", "上诉人(一审被告、原告)", "上诉人(一审被告、再审被申请人)", "上诉人(一审被告、再审申请人)", "上诉人(一审被告、再审申诉人)", "上诉���(一审被告反诉原告)", "上诉人(一审被告暨反诉原告)", "上诉人(一审被告暨原告)", "上诉人(一审被告人)", "上诉人(一审本诉被告、反诉原告)", "上诉人(一审本诉原告、反诉被告)", "上诉人(一审第三人)", "上诉人(一审反诉人)", "上诉人(一审附带民事诉讼原告人)", "上诉人(一审互为原被告)", "上诉人(一审起诉人)", "上诉人(一审申请人)", "上诉人(一审原告)", "上诉人(一审原告、案外人)", "上诉人(一审原告、被告)", "上诉人(一审原告、被申请人)", "上诉人(一审原告、被申诉人)", "上诉人(一审原告、并案被告)", "上诉人(一审原告、二审上诉人、再审申请人)", "上诉人(一审原告、反诉被告)", "上诉人(一审原告、申请执行人)", "上诉人(一审原告、一审反诉被告)", "上诉人(一审原告、再审被申请人)", "上诉人(一审原告、再审申请人)", "上诉人(一审原告、执行案外人)", "上诉人(一审原告暨被告)", "上诉人(一审原告暨反诉被告)", "上诉人(一审原告暨诉讼代表人)", "上诉人(一审自诉人)", "上诉人(原被告人)", "上诉人(原附带民事诉讼原告人)", "上诉人(原告)", "上诉人(原告被告)", "上诉人(原告原告)", "上诉人(原告原告、反诉被告)", "上诉人(原起诉人)", "上诉人(原申请人)", "上诉人(原审)", "上诉人(原审被告(反诉原告))", "上诉人(原审被告(原告))", "上诉人(原审被告)", "上诉人(原审被告)(反诉原告)", "上诉人(原审被告))", "上诉人(原审被告)被告", "上诉人(原审被告、案外人)", "上诉人(原审被告、被上诉人)", "上诉人(原审被告、被申请人)", "上诉人(原审被告、被申诉人)", "上诉人(原审被告、被执行人)", "上诉人(原审被告、并案原告)", "上诉人(原审被告、反诉被告)", "上诉人(原审被告、反诉第三人)", "上诉人(原审被告、反诉人)", "上诉人(原审被告、反诉原告)", "上诉人(原审被告、后诉原告)", "上诉人(原审被告、互诉原告)", "上诉人(原审被告、互为原告)", "上诉人(原审被告、另案原告)", "上诉人(原审被告、申请再审人)", "上诉人(原审被告、申请执行人)", "上诉人(原审被告、申诉人)", "上诉人(原审被告、原告)", "上诉人(原审被告、原审并案原告)", "上诉人(原审被告、原审反诉原告)", "上诉人(原审被告、原审原告)", "上诉人(原审被告、再审被申请人)", "上诉人(原审被告、再审申请人)", "上诉人(原审被告、再审申诉人)", "上诉人(原审被告、执行案外人)", "上诉人(原审被告/原告)", "上诉人(原审被告并案原告)", "上诉人(原审被告并为原告)", "上诉人(原审被告单位)", "上诉人(原审被告二)", "上诉人(原审被告反诉原告)", "上诉人(原审被告及反诉原告)", "上诉人(原审被告暨反诉原告)", "上诉人(原审被告暨原告)", "上诉人(原审被告暨原审反诉原告)", "上诉人(原审被告兼反诉原告)", "上诉人(原审被告人)", "上诉人(原审被告人)自报", "上诉人(原审被告人暨附带民事公益诉讼被告)", "上诉人(原审被告人暨附带民事诉讼被告人)", "上诉人(原审被告人暨附带民事诉讼原告人)", "上诉人(原审被告人暨原审附带民事诉讼被告人)", "上诉人(原审被告三)", "上诉人(原审被告四)", "上诉人(原审被告一)", "上诉人(原审被告原告)", "上诉人(原审被申请人)", "上诉人(原审被申诉人)", "上诉人(原审本诉被告)", "上诉人(原审本诉被告、反诉原告)", "上诉人(原审本诉被告、原审反诉原告)", "上诉人(原审本诉原告、反诉被告)", "上诉人(原审本诉原告、原审反诉被告)", "上诉人(原审撤销��请人)", "上诉人(原审第二被告)", "上诉人(原审第三被告)", "上诉人(原审第三人)", "上诉人(原审第三人、被执行人)", "上诉人(原审第三人、反诉被告)", "上诉人(原审第三人、反诉原告)", "上诉人(原审第四被告)", "上诉人(原审第一被告)", "上诉人(原审反诉被告)", "上诉人(原审反诉人)", "上诉人(原审反诉原告)", "上诉人(原审附带民事诉讼被告)", "上诉人(原审附带民事诉讼被告单位)", "上诉人(原审附带民事诉讼被告人)", "上诉人(原审附带民事诉讼原告)", "上诉人(原审附带民事诉讼原告人)", "上诉人(原审附带民事诉讼原告人暨被害人)", "上诉人(原审附带民事原告人)", "上诉人(原审告)", "上诉人(原审互为原、被告)", "上诉人(原审互为原被告)", "上诉人(原审起诉人)", "上诉人(原审起诉人)(诉讼代表人)", "上诉人(原审上诉人)", "上诉人(原审申请人)", "上诉人(原审申请执行人)", "上诉人(原审申诉人)", "上诉人(原审诉讼地位)", "上诉人(原审刑事附带民事诉讼被告人)", "上诉人(原审刑事附带民事诉讼原告人)", "上诉人(原审原、被告)", "上诉人(原审原被告)", "上诉人(原审原告(被告))", "上诉人(原审原告(反诉被告))", "上诉人(原审原告)", "上诉人(原审原告)(反诉被告)", "上诉人(原审原告)(原告)", "上诉人(原审原告))", "上诉人(原审原告、案外人)", "上诉人(原审原告、案外异议人)", "上诉人(原审原告、保全申请人)", "上诉人(原审原告、被告)", "上诉人(原审原告、被上诉人)", "上诉人(原审原告、被申请人)", "上诉人(原审原告、被申诉人)", "上诉人(原审原告、被执行人)", "上诉人(原审原告、并案被告)", "上诉人(原审原告、第三人)", "上诉人(原审原告、反诉被告)", "上诉人(原审原告、反诉原告)", "上诉人(原审原告、后诉被告)", "上诉人(原审原告、互诉被告)", "上诉人(原审原告、互为被告)", "上诉人(原审原告、另案被告)", "上诉人(原审原告、申请执行人)", "上诉人(原审原告、申诉人)", "上诉人(原审原告、诉前保全申请人)", "上诉人(原审原告、诉讼代表人)", "上诉人(原审原告、原审被告)", "上诉人(原审原告、原审并案被告)", "上诉人(原审原告、原审反诉被告)", "上诉人(原审原告、再审被申请人)", "上诉人(原审原告、再审被申诉人)", "上诉人(原审原告、再审申请人)", "上诉人(原审原告、再审申诉人)", "上诉人(原审原告、债权人)", "上诉人(原审原告、执行案外人)", "上诉人(原审原告被告)", "上诉人(原审原告并案被告)", "上诉人(原审原告二)", "上诉人(原审原告反诉被告)", "上诉人(原审原告及反诉被告)", "上诉人(原审原告暨被告)", "上诉人(原审原告暨反诉被告)", "上诉人(原审原告暨诉讼代表人)", "上诉人(原审原告兼反诉被告)", "上诉人(原审原告人)", "上诉人(原审原告一)", "上诉人(原审再审申请人)", "上诉人(原审追加被告)", "上诉人(原审自诉人)", "上诉人(原审自诉人暨附带民事诉讼原告人)", "上诉人(原再审申请人)", "上诉人(再审被告)", "上诉人(再审被申请人)", "上诉人(再审被申请人、原审被告)", "上诉人(再审被申请人、原审原告)", "上诉人(再审第三人)", "上诉人(再审申请人)", "上诉人(再审申请人、一审被告)", "上诉人(再审申请人、原审被告)", "上诉人(再审申请人、原审原告)", "上诉人(再审原告)", "上诉人(自诉人)", "上诉人、被上诉人(原审被告)", "上诉人[原审被告(反诉原告)]", "上诉人[原审被告(原告)]", "上诉人[原审原告(被告)]", "上诉人二(原审被告)", "上诉人暨被上诉人(一审被告)", "上诉人暨被上诉人(一审原告)", "上诉人暨被上诉人(原审被告)", "上诉人一(原审被告)", "上诉人一(原审原告)", "申报人", "申请(起诉)人", "申请保全人", "申请保全人(原告)", "申请变更人", "申请单位", "申请方", "申请复���人", "申请复议人(案外人)", "申请复议人(被执行人)", "申请复议人(被执行人、异议人)", "申请复议人(第三人)", "申请复议人(利害关系人)", "申请复议人(申请执行人)", "申请复议人(申请执行人、异议人)", "申请复议人(异议人)", "申请复议人(异议人、案外人)", "申请复议人(异议人、被执行人)", "申请复议人(异议人、利害关系人)", "申请复议人(异议人、申请执行人)", "申请复议人(原被执行人)", "申请复议人(原申请执行人)", "申请复议人(原审被执行人)", "申请复议人(原审异议人)", "申请复议人(原审异议人、原审被执行人)", "申请复议人(原异议人)", "申请复议人(原异议人、被执行人)", "申请复议人(追加被执行人)", "申请复议人(追加的被执行人)", "申请机关", "申请解除保全人", "申请救助人", "申请人", "申请人(案外人)", "申请人(被保全人)", "申请人(被告)", "申请人(被执行人)", "申请人(担保人)", "申请人(第三人)", "申请人(反诉原告)", "申请人(甲方)", "申请人(劳动仲裁被申请人)", "申请人(利害关系人)", "申请人(申请执行人)", "申请人(一审被告)", "申请人(一审被告、二审被上诉人)", "申请人(一审被告、二审上诉人)", "申请人(一审原告)", "申请人(一审原告、二审被上诉人)", "申请人(一审原告、二审上诉人)", "申请人(乙方)", "申请人(原案申请执行人)", "申请人(原被申请人)", "申请人(原裁决被申请人)", "申请人(原告)", "申请人(原申请人)", "申请人(原申请执行人)", "申请人(原审被告)", "申请人(原审原告)", "申请人(原仲裁案被申请人)", "申请人(原仲裁被申请人)", "申请人(原仲裁裁决被申请人)", "申请人(原仲裁第一被申请人)", "申请人(原仲裁申请人)", "申请人(债权人)", "申请人(债务人)", "申请人(仲裁被申请人)", "申请人(仲裁被申请人、反请求申请人)", "申请人(仲裁被申请人、仲裁反请求申请人)", "申请人(仲裁第二被申请人)", "申请人(仲裁第三被申请人)", "申请人(仲裁第一被申请人)", "申请人(仲裁申请人)", "申请人(仲裁申请人、反请求被申请人)", "申请人保全人", "申请人执行人", "申请行人", "申请异议人", "申请异议人(案外人)", "申请异议人(被执行人)", "申请再审人", "申请再审人(案外人)", "申请再审人(一审被告)", "申请再审人(一审被告、二审被上诉人)", "申请再审人(一审被告、二审上诉人)", "申请再审人(一审被告、反诉原告、二审上诉人)", "申请再审人(一审第三人、二审上诉人)", "申请再审人(一审起诉人、二审上诉人)", "申请再审人(一审原告)", "申请再审人(一审原告、二审被上诉人)", "申请再审人(一审原告、二审上诉人)", "申请再审人(一审原告、二审上诉人、原申诉人)", "申请再审人(一审原告、反诉被告、二审被上诉人)", "申请再审人(一审原告、反诉被告、二审上诉人)", "申请再审人(原审被告)", "申请再审人(原审被告、二审上诉人)", "申请再审人(原审第三人)", "申请再审人(原审上诉人)", "申请再审人(原审原告)", "申请再审人(原审原告、二审上诉人)", "申请执人", "申请执行单位", "申请执行人", "申请执行人(变更后)", "申请执行人(申请保全人)", "申请执行人(申请人)", "申请执行人(异议人)", "申请执行人(原告)", "申请执行人(原告、保全申请人)", "申请执行人(原审原告)", "申请执行人(仲裁申请人)", "申请执行人人", "申请执行人申请人", "申请执行人原告", "申请追加被执行人", "申请追加人", "申请追加人(申请执行人)", "申诉人", "申诉人(案外人)", "申诉人(被执行人)", "申诉人(被执行人、异议人、复议申请人)", "申诉人(第三人)", "申诉人(复议申请人、申请执行人)", "申诉人(利害关系人)", "申诉人(赔偿请求人)", "申诉人(申请执行人)", "申诉人(一审被告)", "申诉人(一审被告、二审被上诉人)", "申诉人(一审被告、二审被上诉人、原再审申请人)", "申诉人(一审被告、二审被上诉人、再审申请人)", "申诉人(一审被告、二审上诉人)", "申诉人(一审被告、二审上诉人、申请再审人)", "申诉人(一审被告、二审上诉人、原再审申请人)", "申诉人(一审被告、二审上诉人、再审被申请人)", "申诉人(一审被告、二审上诉人、再审申请人)", "申诉人(一审被告、反诉原告、二审被上诉人)", "申诉人(一审被告、反诉原告、二审上诉人)", "申诉人(一审被告、反诉原告、二审上诉人、再审申请人)", "申诉人(一审被告、再审申请人)", "申诉人(一审第三人)", "申诉人(一审第三人、二审被上诉人)", "申诉人(一审第三人、二审上诉人)", "申诉人(一审起诉人、二审上诉人)", "申诉人(一审原告)", "申诉人(一审原告、二审被上诉人)", "申诉人(一审原告、二审被上诉人、原再审申请人)", "申诉人(一审原告、二审被上诉人、再审被申请人)", "申诉人(一审原告、二审被上诉人、再审申请人)", "申诉人(一审原告、二审上诉人)", "申诉人(一审原告、二审上诉人、申请再审人)", "申诉人(一审原告、二审上诉人、原申请再审人)", "申诉人(一审原告、二审上诉人、原再审申请人)", "申诉人(一审原告、二审上诉人、再审被申请人)", "申诉人(一审原告、二审上诉人、再审申请人)", "申诉人(一审原告、反诉被告、二审被上诉人)", "申诉人(一审原告、反诉被告、二审上诉人)", "申诉人(原赔偿请求人)", "申诉人(原审案外人)", "申诉人(原审被告)", "申诉人(原审被告、二审上诉人)", "申诉人(原审被告、反诉原告)", "申诉人(原审被告、再审申请人)", "申诉人(原审第三人)", "申诉人(原审原告)", "申诉人(原审原告、二审上诉人)", "诉讼参与人", "特别程序被申请人", "特别程序申请人", "嫌疑人", "刑事附带民事诉讼原告人", "刑事附带民事原告人", "一审、二审第三人", "一审被告", "一审被告(被执行人)", "一审被告(二审被上诉人)", "一审被告(二审上诉人)", "一审被告(反诉原告)", "一审被告(申请执行人)", "一审被告(再审被申请人)", "一审被告、被上诉人", "一审被告、二审被上诉人", "一审被告、二审上诉人", "一审被告、二审原审被告", "一审被告、反诉第三人", "一审被告、反诉原告", "一审被告、反诉原告、二审被上诉人", "一审被告、反诉原告、二审上诉人", "一审被告、原告、二审被上诉人", "一审被告人", "一审第三人", "一审第三人(被执行人)", "一审第三人(二审被上诉人)", "一审第三人(二审上诉人)", "一审第三人、被执行人", "一审第三人、二审被上诉人", "一审第三人、二审第三人", "一审第三人、二审上诉人", "一审第三人、二审原审第三人", "一审反诉被告", "一审附带民事诉讼被告人", "一审附带民事诉讼原告人", "一审起诉人", "一审起诉人、二审上诉人", "一审原告", "一审原告(二审被上诉人)", "一审原告(二审上诉人)", "一审原告(反诉被告)", "一审原告、二审被上诉人", "一审原告、二审上诉人", "一审原告、反诉被告", "一审原告、反诉被告、二审上诉人", "异议被申请人", "异议被申请人(被执行人)", "异议被申请人(申请执行人)", "异议被申请人(原案被执行人)", "异议人", "异议人(案外人)", "异议人(案外人、利害关系人)", "异议人(保全被申请人)", "异议人(被保全人)", "异议人(被告)", "异议人(被申请保全人)", "异议人(被申请人)", "异议人(被申请执行人)", "异议人(被执行人)", "异议人(本案被执行人)", "异议人(参与分配人)", "异议人(担保人)", "异议人(第三人)", "异议人(暨被执行人)", "异议人(利害关系人)", "异议人(买受人)", "异议人(申请人)", "异议人(申请执行人)", "异议人(同是本案被执行人)", "异议人(协助义务人)", "异议人(协助执行人)", "异议人(原案被执行人)", "异议人(原案申请人)", "异议人(原案申请执行人)", "异议人(原被执行人)", "异议人(债权人)", "异议人(执行案外人)", "异议人(追加被执行人)", "异议申请人", "异议申请人(案外人)", "异议申请人(被执行人)", "异议申请人(利害关系人)", "异议申请人(申请执行人)", "原案案外人", "原案被执行人", "原案当事人", "原案申请人", "原案异议人", "原告", "原告(案外人)", "原告(保全申请人)", "原告(被反诉人)", "原告(被告)", "原告(被申请人)", "原告(被执行人)", "原告(本诉被告)", "原告(并案被告)", "原告(第三人)", "原告(反诉被告)", "原告(反诉第三人)", "原告(反诉原告)", "原告(后诉被告)", "原告(互诉被告)", "原告(互为被告)", "原告(暨被告)", "原告(暨反诉被告)", "原告(另案被告)", "原告(起诉人)", "原告(申请保全人)", "原告(申请人)", "原告(申请执行人)", "原告(亦为被告)", "原告(异议人)", "原告(原审原告)", "原告(债权人)", "原告(执行案外人)", "原告(执行申请人)", "原告(执行异议人)", "原告(追加)", "原告(追加被执行人)", "原告/上诉人", "原告[申请执行人]", "原告方", "原告暨被告", "原告暨反诉被告", "原告人", "原公诉机关", "原审(被告)", "原审(一审)", "原审(一审或二审)", "原审案外人", "原审被告", "原审被告(案外人)", "原审被告(被告)", "原审被告(被申请人)", "原审被告(被申诉人)", "原审被告(被执行人)", "原审被告(二审被上诉人", "原审被告(二审被上诉人)", "原审被告(二审上诉人)", "原审被告(反诉被告)", "原审被告(反诉第三人)", "原审被告(反诉原告)", "原审被告(申请执行人)", "原审被告(申诉人)", "原审被告(一审被告)", "原审被告(一审被告、二审被上诉人)", "原审被告(一审被告、二审上诉人)", "原审被告(原审被告)", "原审被告(原审反诉第三人)", "原审被告(原审反诉原告)", "原审被告(再审被申请人)", "原审被告(再审申请人)", "原审被告(追加)", "原审被告、被执行人", "原审被告、二审被上诉人", "原审被告、二审上诉人", "原审被告、反诉第三人", "原审被告、反诉原告", "原审被告、原审反诉第三人", "原审被告、原审反诉原告", "原审被告、再审被申请人", "原审被告单位", "原审被告人", "原审被告人暨附带民事诉讼被告人", "原审被害人", "原审被害人暨附带民事诉讼原告人", "原审被起诉人", "原审被上诉人", "原审被上诉人(一审被告)", "原审被上诉人(一审被告、反诉原告)", "原审被上诉人(一审第三人)", "原审被上诉人(一审原告)", "原审被上诉人(一审原告、反诉被告)", "原审被上诉人(原审被告)", "原审被申请人", "原审被申请追加人", "原审被执行人", "原审本诉被告", "原审当事人", "原审当事人(原审被告)", "原审当事人(原审第三人)", "原审第二被告", "原审第三被告", "原审第三人", "原审第三人(案外人)", "原审第三人(被申请人)", "原审第三人(被申请执行人)", "原审第三人(被执行人)", "原审第三人(第三人)", "原审第三人(二审被上诉人)", "原审第三人(反诉被告)", "原审第三人(反诉第三人)", "原审第三人(申请执行人)", "原审第三人(未提出反对意见的债权人)", "原审第三人(一审第三人)", "原审第三人(原审被告)", "原审第三人(执行案外人)", "原审第三人、被执行人", "原审第三人、二审被上诉人", "原审第四被告", "原审第一被告", "原审反诉被告", "原审反诉第三人", "原审附带民事被告人", "原审附带民事诉讼被告", "原审附带民事诉讼被告人", "原审附带民事诉讼原告人", "原审附带民事诉讼原告人暨被害人", "原审附带民事诉讼原告人暨附带民事诉讼被告人", "原审附带民事原告人", "原审公诉机关", "原审起诉人", "原审上诉人", "原审上诉人(一审被告)", "原审上诉人(一审被告、反诉原告)", "原审上诉人(一审被告人)", "原审上诉人(一审第三人", "原审上诉人(一审第三人)", "原审上诉人(一审原告)", "原审上诉人(一审原告、反诉被告)", "原审上诉人(原审被告)", "原审上诉人(原审被告人)", "原审申请人", "原审申请执行人", "原审同案被告人", "原审刑事附带民事诉讼被告人", "原审刑事附带民事诉讼原告人", "原审异议人", "原审异议人(利害关系人)", "原审原告", "原审原告(被申请人)", "原审原告(被申诉人)", "原审原告(二审被上诉人)", "原审原告(二审上诉人)", "原审原告(反诉被告)", "原审原告(一审原告)", "原审原告(原审反诉被告)", "原审原告(再审被申请人)", "原审原告、二审被上诉人", "原审原告、二审上诉人", "原审原告、反诉被告", "起诉机关", "原审追加被告", "原审自诉人", "原审自诉人暨附带民事诉讼原告人", "再审被告(原审被告)", "再审被上诉人(一审被告)", "再审被上诉人(一审原告)", "再审被上诉人(原审被告)", "再审被上诉人(原审原告)", "再审被申请人", "再审被申请人(一审被告)", "再审被申请人(一审被告、二审被上诉人)", "再审被申请人(一审被告、二审上诉人)", "再审被申请人(一审被告、反诉原告、二审被上诉人)", "再审被申请人(一审第三人)", "再审被申请人(一审第三人、二审被上诉人)", "再审被申请人(一审原告)", "再审被申请人(一审原告、二审被上诉人)", "再审被申请人(一审原告、二审上诉人)", "再审被申请人(一审原告、反诉被告、二审被上诉人)", "再审被申请人(原审被告)", "再审被申请人(原审被告、二审被上诉人)", "再审被申请人(原审第三人)", "再审被申请人(原审原告)", "再审被申请人(原审原告、二审被上诉人)", "再审第三人", "再审上诉人", "再审上诉人(一审被告)", "再审上诉人(一审原告)", "再审上诉人(原审被告)", "再审上诉人(原审被告、再审申请人)", "再审上诉人(原审原告)", "再审申请人", "再审申请人(案外人)", "再审申请人(被告)", "再审申请人(二审被上诉人)", "再审申请人(二审被上诉人、一审被告)", "再审申请人(二审被上诉人、一审原告)", "再审申请人(二审上诉人)", "再审申请人(二审上诉人、一审被告)", "再审申请人(二审上诉人、一审原告)", "再审申请人(二审上诉人、原审被告)", "再审申请人(二审上诉人、原审原告)", "再审申请人(上诉人、原审被告)", "再审申请人(申请执行人、一审原告、二审上诉人)", "再审申请人(一、二审被告)", "再审申请人(一、二审第三人)", "再审申请人(一审被告)", "再审申请人(一审被告、被上诉人)", "再审申请人(一审被告、二审被上诉人)", "再审申请人(一审被告、二审被上诉人、申请执行人)", "再审申请人(一审被告、二审上诉人)", "再审申请人(一审被告、二审上诉人、申请执行人)", "再审申请人(一审被告、二审原审被告)", "再审申请人(一审被告、反诉原告)", "再审申请人(一审被告、反诉原告、二审被上诉人)", "再审申请人(一审被告、反诉原告、二审上诉人)", "再审申请人(一审被告、反诉原告；二审上诉人)", "再审申请人(一审被告、上诉人)", "再审申请人(一审被告、申请执行人)", "再审申请人(一审被告、一审反诉原告、二审被上诉人)", "再审申请人(一审被告、一审反诉原告、二审上诉人)", "再审申请人(一审被告、原告、二审上诉人)", "再审申请人(一审被告二审上诉人)", "再审申请人(一审被告反诉原告、二审上诉人)", "再审申请人(一审被告及反诉原告、二审上诉人)", "再审申请人(一审被告暨反诉原告、二审被上诉人)", "再审申请人(一审被告暨反诉原告、二审上诉人)", "再审申请人(一审本诉被告、反诉原告、二审上诉人)", "再审申请人(一审本诉原告、反诉被告)", "再审申请人(一审本诉原告、反诉被告、二审上诉人)", "再审申请人(一审第三人)", "再审申请人(一审第三人��二审被上诉人)", "再审申请人(一审第三人、二审第三人)", "再审申请人(一审第三人、二审上诉人)", "再审申请人(一审互为原被告、二审上诉人)", "再审申请人(一审起诉人)", "再审申请人(一审起诉人、二审上诉人)", "再审申请人(一审申请人、二审上诉人)", "再审申请人(一审原被告、二审上诉人)", "再审申请人(一审原告)", "再审申请人(一审原告，二审被上诉人)", "再审申请人(一审原告、被告、二审上诉人)", "再审申请人(一审原告、被上诉人)", "再审申请人(一审原告、并案被告、二审上诉人)", "再审申请人(一审原告、二审被上诉人)", "再审申请人(一审原告、二审被上诉人、执行案外人)", "再审申请人(一审原告、二审上诉人)", "再审申请人(一审原告、二审上诉人))", "再审申请人(一审原告、二审上诉人、案外人)", "再审申请人(一审原告、二审上诉人、申请执行人)", "再审申请人(一审原告、二审上诉人、执行案外人)", "再审申请人(一审原告、反诉被告)", "再审申请人(一审原告、反诉被告、二审被上诉人)", "再审申请人(一审原告、反诉被告、二审上诉人)", "再审申请人(一审原告、反诉被告；二审上诉人)", "再审申请人(一审原告、上诉人)", "再审申请人(一审原告、一审被告、二审上诉人)", "再审申请人(一审原告、一审反诉被告、二审被上诉人)", "再审申请人(一审原告、一审反诉被告、二审上诉人)", "再审申请人(一审原告、执行案外人)", "再审申请人(一审原告、执行案外人、二审上诉人)", "再审申请人(一审原告二审上诉人)", "再审申请人(一审原告反诉被告、二审上诉人)", "再审申请人(一审原告及反诉被告、二审上诉人)", "再审申请人(一审原告暨被告、二审上诉人)", "再审申请人(一审原告暨反诉被告、二审被上诉人)", "再审申请人(一审原告暨反诉被告、二审上诉人)", "再审申请人(原审案外人)", "再审申请人(原审被告)", "再审申请人(原审被告、二审被上诉人)", "再审申请人(原审被告、二审上诉人)", "再审申请人(原审被告、反诉原告)", "再审申请人(原审被告、反诉原告、二审上诉人)", "再审申请人(原审被告、上诉人)", "再审申请人(原审被执行人)", "再审申请人(原审第三人)", "再审申请人(原审第三人、二审上诉人)", "再审申请人(原审起诉人)", "再审申请人(原审起诉人、二审上诉人)", "再审申请人(原审上诉人)", "再审申请人(原审申请人)", "再审申请人(原审原告)", "再审申请人(原审原告、二审被上诉人)", "再审申请人(原审原告、二审上诉人)", "再审申请人(原审原告、反诉被告)", "再审申请人(原审原告、反诉被告、二审上诉人)", "再审申请人(原审原告、上诉人)", "再审申请人(原一审被告)", "再审申请人(原一审被告、二审被上诉人)", "再审申请人(原一审被告、二审上诉人)", "再审申请人(原一审原告、二审上诉人)", "再审申请人(原一审原告、原二审上诉人)", "再审申请人(执行案外人、一审原告、二审上诉人)", "再审申请人[一审被告(反诉原告)、二审上诉人]", "再审原告", "再审追加第三人", "执行机关", "执行申请人",
            "追加被执行人", "自诉人", "自诉人暨附带民事诉讼原告人", "罪犯", "申诉人(一审原告、再审申请人)");

    public static String evaluate(String ygInfo, String bgInfo, String partys, String content,
                                  String courtCode, String anNo, String appllor) throws Exception {
        // 遍历所有当事人，获取身份信息
        Map<String, String> roleMap = new LinkedHashMap<>();
        JSONObject result = new JSONObject();
        result.put("Result", new JSONArray());
        if (StringUtils.isNotEmpty(appllor)) {
            String plaintiff = "";
            String defendant = "";
            String thirdParty = "";
            String otherParty = "";
            String allParty = appllor;
            String[] arr = allParty.split(",");

            ygInfo = ygInfo == null || ygInfo.equals("原告") || ygInfo.equals("被告") || ygInfo.equals("上诉人") || ygInfo.equals("第三人") ? "" : CommonUtil.full2Half(ygInfo);
            bgInfo = bgInfo == null || bgInfo.equals("原告") || bgInfo.equals("被告") || bgInfo.equals("上诉人") || bgInfo.equals("第三人") ? "" : CommonUtil.full2Half(bgInfo);
            partys = partys == null || partys.equals("原告") || partys.equals("被告") || partys.equals("上诉人") || partys.equals("第三人") ? "" : CommonUtil.full2Half(partys);
            content = content == null || content.equals("原告") || content.equals("被告") || content.equals("上诉人") || content.equals("第三人") ? "" : CommonUtil.full2Half(content);
            String allInfo = ygInfo.concat(";").concat(bgInfo).concat(";").concat((partys)).concat(";").replace("（", "(").replace("）", ")");
            Map<String, Tuple2<Integer, Integer>> roleMapContent = getRoleMap(allInfo);
            for (String str : arr) {
                String role = "";
                if (StringUtils.isNotEmpty(str)) {
                    if (StringUtils.isNotEmpty(allInfo)) {
                        List<Integer> idxList = new ArrayList<>();
                        int index = allInfo.indexOf(str);
                        while (index >= 0) {
                            idxList.add(index);
                            index = allInfo.indexOf(str, index + 1);
                        }
//                        int idx = allInfo.indexOf(str);
                        if (CollectionUtils.isNotEmpty(idxList)) {
                            role = getRoleByRoleSet(roleMapContent, idxList);
                        }
                    }

                    roleMap.put(str, role);
                }
            }

            String allInfoV2 = content.replace("（", "(").replace("）", ")");
            Map<String, String> mapContent = new HashedMap();

            Map<String, Tuple2<Integer, Integer>> roleMapContentV2 = getRoleMap(allInfoV2);

            for (String str : arr) {
                if (StringUtils.isEmpty(roleMap.get(str))) {
                    mapContent.put(str, allInfoV2);
                }
            }

            for (String str : arr) {
                String role = "";
                if (StringUtils.isNotEmpty(str) && StringUtils.isEmpty(roleMap.get(str))) {
                    if (StringUtils.isNotEmpty(allInfoV2)) {
                        List<Integer> idxList = new ArrayList<>();
                        int index = allInfoV2.indexOf(str);
                        while (index >= 0) {
                            idxList.add(index);
                            index = allInfoV2.indexOf(str, index + 1);
                        }
//                        int idx = allinfoTemp.indexOf(str);
                        if (CollectionUtils.isNotEmpty(idxList)) {
                            role = getRoleByRoleSet(roleMapContentV2, idxList);
                        }
                    }

                    roleMap.put(str, role);
                }
            }

            // 反向验证身份
            Set<String> keySet = roleMap.keySet();
            Set<String> keySet2 = roleMap.keySet();
            JSONArray array = new JSONArray();
            for (String str : keySet) {
                if (roleMap.get(str).contains("_")) {
                    int start = Integer.parseInt(roleMap.get(str).split("_")[2]);
                    if (mapContent.containsKey(str)) {
                        allInfo = mapContent.get(str);
                    }
                    int end = allInfo.indexOf(str);
                    if (end > start) {

                        String subInfo = allInfo.substring(start, end);

                        for (String sub : keySet2) {
                            if (roleMap.get(sub).equals(roleMap.get(str))) {
                                subInfo = subInfo.replace(sub, "");
                            }
                        }
                        subInfo = subInfo.replace(roleMap.get(str), "").replace(",", "").replace(";", "")
                                .replace(" ", "").replace("、", "").replace(":", "").replace("(", "").replace(")", "");

                        if (subInfo.length() > 3) {
                            roleMap.put(str, "");
                        }
                    }
                }

                JSONObject json1 = new JSONObject();
                json1.put("Name", str);
                json1.put("Role", roleMap.get(str));
                array.add(json1);
            }


            // 案号不为空，根据审理进程补充身份
            if (StringUtils.isNotEmpty(anNo)) {
                CaseNoTrialRoundEnum caseNoTrialRoundEnum = CaseNoTrialRoundEnum.getCaseNoTrialRoundEnum(anNo);
                Set<String> keySet3 = roleMap.keySet();
                for (String str : keySet3) {
                    if (StringUtils.isEmpty(roleMap.get(str))) {
                        int roleTag = 3;
                        if (plaintiff.contains(str)) {
                            roleTag = 0;
                        }
                        if (defendant.contains(str)) {
                            roleTag = 1;
                        }
                        if (thirdParty.contains(str)) {
                            roleTag = 2;
                        }
                        String role = "";
                        if (caseNoTrialRoundEnum != null) {
                            role = caseNoTrialRoundEnum.findRole(roleTag);
                            if (roleTag == 3) {
                                if (content.contains("诉")) {
                                    List<String> strSet = Arrays.stream(content.split("[,.。]")).filter(x -> x.contains("诉") && x.contains(str)).collect(Collectors.toList());
                                    if (CollectionUtils.isEmpty(strSet)) {
                                        continue;
                                    }
                                    String[] tmpArray = strSet.get(0).split("诉");
                                    if (tmpArray[0].contains(str)) {
                                        roleTag = 0;
                                    }
                                    if (tmpArray.length > 1 && tmpArray[1].contains(str)) {
                                        roleTag = 1;
                                    }

                                    if (roleTag != 3) {
                                        role = caseNoTrialRoundEnum.findRole(roleTag);
                                    }
                                }
                            }
                        }
                        if (StringUtils.isNotEmpty(role)) {
                            roleMap.put(str, role);
                        }
                    }
                }
            } else {
                // 如果没有找到，空案号的情况下，根据法院code赋值
                Set<String> keySet3 = roleMap.keySet();
                for (String str : keySet3) {
                    if (StringUtils.isEmpty(roleMap.get(str)) && ((courtCode != null && courtCode.length() == 6 && !courtCode.endsWith("00")) || StringUtils.isEmpty(courtCode))) {
                        if (plaintiff.contains(str)) {
                            roleMap.put(str, "原告");
                        } else if (defendant.contains(str)) {
                            roleMap.put(str, "被告");
                        } else if (thirdParty.contains(str)) {
                            roleMap.put(str, "第三人");
                        }
                    }
                }
            }

            // 没有身份，统一成当事人
            JSONArray array2 = new JSONArray();
            Set<String> keySet3 = roleMap.keySet();
            for (String str : keySet3) {
                if (StringUtils.isEmpty(roleMap.get(str))) {
                    //roleMap.put(str, "当事人");
                    if (plaintiff.contains(str)) {
                        roleMap.put(str, "原告/上诉人");
                    }
                    if (defendant.contains(str)) {
                        roleMap.put(str, "被告/被上诉人");
                    }
                    if (thirdParty.contains(str)) {
                        roleMap.put(str, "第三人");
                    }
                    if (otherParty.contains(str)) {
                        roleMap.put(str, "当事人");
                    }

                    if (StringUtils.isEmpty(roleMap.get(str))) {
                        roleMap.put(str, "当事人");
                    }
                }
                JSONObject json1 = new JSONObject();
                json1.put("Name", str);
                json1.put("Role", roleMap.get(str).split("_")[0]);
                array2.add(json1);
            }

            // 查看裁判文书反哺的身份
            Set<String> keySet4 = roleMap.keySet();
            for (String str : keySet4) {
                if (StringUtils.isEmpty(roleMap.get(str)) || roleMap.get(str).equals("当事人")) {
                    if ((courtCode != null && courtCode.length() == 6 && !courtCode.endsWith("00")) || StringUtils.isEmpty(courtCode)) {
                        if (content.contains("诉")) {
                            String[] tmpArray = content.split("诉");
                            if (tmpArray[0].contains(str)) {
                                roleMap.put(str, "原告");
                            }
                            if (tmpArray[1].contains(str)) {
                                roleMap.put(str, "被告");
                            }
                        }
                    }
                    if (courtCode != null && courtCode.length() == 6 && courtCode.endsWith("00") && !courtCode.endsWith("0000")) {
                        if (content.contains("诉")) {
                            String[] tmpArray = content.split("诉");
                            if (tmpArray[0].contains(str)) {
                                roleMap.put(str, "上诉人");
                            }
                            if (tmpArray[1].contains(str)) {
                                roleMap.put(str, "被上诉人");
                            }
                        }
                    }
                    if (courtCode != null && courtCode.length() == 6 && courtCode.endsWith("0000")) {
                        if (content.contains("诉")) {
                            String[] tmpArray = content.split("诉");
                            if (tmpArray[0].contains(str)) {
                                roleMap.put(str, "原告/上诉人");
                            }
                            if (tmpArray[1].contains(str)) {
                                roleMap.put(str, "被告/被上诉人");
                            }
                        }
                    }
                }
            }
            result.put("Result", array2);
        }


        return JSON.toJSONString(result.get("Result"));
    }

    public static Map<String, Tuple2<Integer, Integer>> getRoleMap(String info) {
        Map<String, Tuple2<Integer, Integer>> roleMap = new LinkedHashMap<>();
        Map<String, Tuple2<Integer, Integer>> tmpRoleMap = new LinkedHashMap<>();
        for (String obj : roleList) {
            if (obj.equals("被执行人")) {
                System.out.println(1);
            }
            if (info.contains(obj)) {
                int idx = 0;
                while ((idx = info.indexOf(obj, idx)) != -1) {
                    int nowIdx = info.indexOf(obj, idx);
                    tmpRoleMap.put(obj.concat("_").concat(String.valueOf(nowIdx)).concat("_").concat(String.valueOf(nowIdx + obj.length())), new Tuple2<>(nowIdx, nowIdx + obj.length()));
                    idx += obj.length();
                }
            }
        }
        Set<String> keySet = tmpRoleMap.keySet();
        Set<String> keySet2 = tmpRoleMap.keySet();
        Set<String> keySet3 = new LinkedHashSet<>();
        Set<String> removeSet = new LinkedHashSet<>();
        for (String str : keySet) {
            int start = tmpRoleMap.get(str)._1;
            int end = tmpRoleMap.get(str)._2;

            for (String sub : keySet2) {
                int start2 = tmpRoleMap.get(sub)._1;
                int end2 = tmpRoleMap.get(sub)._2;
                if (!str.equals(sub) && str.contains(sub)) {
                    if (start < start2 && end > end2) {
                        removeSet.add(sub);
                    }
                    if (start <= start2 && end > end2) {
                        removeSet.add(sub);
                    }
                    if (start < start2 && end >= end2) {
                        removeSet.add(sub);
                    }
                }
                keySet3.add(str);

            }
        }
        for (String str : keySet3) {
            if (!removeSet.contains(str)) {
                roleMap.put(str, tmpRoleMap.get(str));
            }
        }

        return roleMap;
    }

    public static String getRoleByRoleSet(Map<String, Tuple2<Integer, Integer>> roleMap, List<Integer> idxList) {
        String role = "";
        Set<String> roleSet = roleMap.keySet();
        int flag = 0;
//        Map<Integer, String> idexRoleMap = new HashedMap();
        for (String entity : roleSet) {
            for (Integer idx : idxList) {
                Tuple2<Integer, Integer> roleIdx = roleMap.get(entity);

                if (roleIdx._2 <= idx && roleIdx._2 >= flag) {
                    if (StringUtils.isNotEmpty(role)) {
                        int start1 = Integer.parseInt(role.split("_")[1]);
                        int end1 = Integer.parseInt(role.split("_")[2]);
                        int start2 = Integer.parseInt(entity.split("_")[1]);
                        int end2 = Integer.parseInt(entity.split("_")[2]);
//                        if (StringUtils.isNotEmpty(role) && !role.contains("当事人")) {
//
//                            continue;
//                        }

                        if (end2 > end1) {
                            role = entity;
                        } else if (end2 == end1) {
                            String tmpRole = role;
                            role = entity;
                            if (start2 > start1) {
                                role = tmpRole;
                            }
                        }
//                        idexRoleMap.put(idx - end2, role);
                    } else {
                        role = entity;
                    }

                    flag = roleIdx._2;
                }
            }
        }
//        if (idexRoleMap.size() > 0) {
//            int i = idexRoleMap.keySet().stream().min(Comparator.comparing(m -> m)).get();
//            role = idexRoleMap.get(i);
//        }
        return role;
    }


    public static void main(String[] args) throws Exception {
        System.out.println(evaluate("", "", "金华市四宝纸张有限公司、南昌玛俪文化用品有限公司等合同纠纷一审民事判决书原告：金华市四宝纸张有限公司，住所地浙江省金华市婺城区秋滨街道南二环西路3188号D2幢第2层，组织机构代码证：91330703566984299G。 法定代表人：陈珊，经理、执行董事。 委托诉讼代理人：杜炜彪，浙江八风易咏律师事务所律师。 委托诉讼代理人：张旭冉，浙江八风易咏律师事务所实习律师。 被告：南昌玛俪文化用品有限公司，住所地南昌市南昌高新技术产业开发区麻丘镇高胡村高田胡家自然村177号，组织机构代码证：91360111MA35U71C48。 法定代表人：陈国斌。 被告：张春红，男，****年**月**日出生，汉族，住**。 委托诉讼代理人：施泽鹏，北京炜衡（宁波）律师事务所律师。 被告：陈亮，男，****年**月**日出生，汉族，住**。\n",
                "原告金华市四宝纸张有限公司与被告南昌玛俪文化用品有限公司、张春红、陈亮、陈国斌合同纠纷一案，本院于2023年1月31日立案后，依法适用简易程序由审判员独任审判。因无法向被告南昌玛俪文化用品有限公司、陈亮等直接送达诉讼文书，同年2月1日依法转为普通程序，仍由审判员独任审理（在公告送达起诉状副本期间与被告取得联系）。在审理期间，原告申请撤回对被告陈国斌的起诉，本院予以准许。本案于同年4月4日、6月27日两次公开开庭进行了审理。原告金华市四宝纸张有限公司的法定代表人陈珊，委托诉讼代理人杜炜彪、张旭冉（第一次），被告南昌玛俪文化用品有限公司的法定代表人陈国斌（线上第二次）、被告张春红的委托诉讼代理人施泽鹏到庭参加诉讼，被告陈亮经传票传唤，无正当理由拒不到庭参加诉讼。本案现已缺席审理终结。\n",
                "31,3307,330702", "（2023）浙0702民初569号", "金华市四宝纸张有限公司,南昌玛俪文化用品有限公司,陈亮,陈国斌,张春红"));
    }

}
