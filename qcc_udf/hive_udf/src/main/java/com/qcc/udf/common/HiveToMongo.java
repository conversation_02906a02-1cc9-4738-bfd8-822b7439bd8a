package com.qcc.udf.common;

import com.mongodb.*;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Filters;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDF;
import org.bson.Document;
import org.bson.conversions.Bson;

import java.util.Arrays;
import java.util.List;

@Description(name = "hive_to_mongo",
        value = "_FUNC_(String connUrl, String bson);" +
                "Parameters format - connUrl:'userName:pwd:source:dataBase:collectName'" +
                ",source Express database auth,database:write database;collectName:write database.collection; " +
                " SELECT _FUNC_('user:pwd:source:dataBase:collectName',{'_id':'1','data':'data_value'}) " +
                " - Return _id")
public class HiveToMongo extends UDF {

    private static MongoClient mongoClient = null;

    /**
     * 计算
     *
     * @param connUrl
     * @param bsonData
     * @return
     */
    public static String evaluate(String connUrl, String bsonData) throws InterruptedException, MongoException {
        try {
            if (StringUtils.isBlank(connUrl) || StringUtils.isBlank(bsonData))
                throw new InterruptedException("连接参数或者bson文档不能空");
            String[] conns = connUrl.split(":");
            if (conns.length != 5) {
                throw new InterruptedException("连接参数不符合规范");
            }
            Document doc = new Document().parse(bsonData);
            String _id = doc.getString("_id");
            if (doc == null || StringUtils.isBlank(_id)) {
                throw new InterruptedException("bson文档中,_id的值不能为空");
            }
            AddOrUpdate(conns, doc);
            return _id;
        } catch (Exception ex) {
            throw ex;
        }
    }


    /***
     *
     * @param conns
     * @param doc
     * @return
     */
    public static void AddOrUpdate(String[] conns, Document doc) throws MongoException {
        try {
            String userName = conns[0];
            String pwd = conns[1];
            String source = conns[2];
            String dataBase = conns[3];
            String collectName = conns[4];
            HiveToMongo.getMongoClient(userName, pwd, source);
            MongoDatabase mongoDB = mongoClient.getDatabase(dataBase);
            MongoCollection<Document> mongoDoc = mongoDB.getCollection(collectName);
            Bson filter = Filters.eq("_id", doc.get("_id"));
            MongoCursor cursor = mongoDoc.find(filter).cursor();
            if (cursor.hasNext()) mongoDoc.replaceOne(filter, doc);
            else mongoDoc.insertOne(doc);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取mongo连接
     *
     * @param userName
     * @param pwd
     * @param dataBase
     * @throws MongoException
     */
    private static void getMongoClient(String userName, String pwd, String dataBase) throws MongoException {
        if (mongoClient != null) return;
        int connectionsPerHost = 100;
        int threadsAllowedToBlock = 20;
        int maxWaitTime = 1000 * 60 * 2; //1000*60*2
        int connectTimeout = 1000 * 60 * 1;//1000*60*1;
        // 其他参数根据实际情况进行添加
        try {
            MongoClientOptions.Builder build = new MongoClientOptions.Builder();
            build.connectionsPerHost(connectionsPerHost);   //与目标数据库能够建立的最大connection数量为50
            build.threadsAllowedToBlockForConnectionMultiplier(threadsAllowedToBlock); //如果当前所有的connection都在使用中，则每个connection上可以有50个线程排队等待
            build.maxWaitTime(maxWaitTime);
            build.connectTimeout(connectTimeout);    //与数据库建立连接的timeout设置为n分钟
            MongoClientOptions options = build.build();
            List<ServerAddress> adds = Arrays.asList(
                    new ServerAddress("mongos.ld-hadoop.com", 20000));
            MongoCredential mongoCredential = MongoCredential.createScramSha1Credential(userName, dataBase, pwd.toCharArray());
            mongoClient = new MongoClient(adds, mongoCredential, options);
        } catch (MongoException e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) throws Exception {
        evaluate("admin:mongo_666_Qcc:admin:derek1:derek_test_1", "{\"_id\":\"223\",\"name\":{\"name1\":\"name_1\",\"name2\":\"name_2\"}}");
    }
}
