package com.qcc.udf.cpws.wjp;

import org.apache.hadoop.hive.ql.exec.UDF;
import org.apache.hadoop.io.Text;

import java.time.DateTimeException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DateCleanerUDF3 extends UDF {
    private static final DateTimeFormatter OUT_FMT =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    // 带时分秒的正则（支持英文/中文分隔符，跳过范围段）
//    private static final Pattern DT_P = Pattern.compile(
//            "(?<year>\\d{4})[年\\-/\\.]"
//                    + "(?<month>\\d{1,2})[月\\-/\\.]"
//                    + "(?<day>\\d{1,2})[日号]?"
//                    + "(?:[至\\-]\\d{1,2}[月\\-/\\.]\\d{1,2}[日号]?)?"    // 跳过“至…日”范围
//                    + "[,，\\s]*"
//                    + "(?<ampm>上午|下午)?\\s*"
//                    + "(?<hour>\\d{1,2}|[一二三四五六七八九十两])"
//                    + "(?:[时点:\\.]?(?<minute>\\d{1,2}))?"
//                    + "(?:[分:\\.]?(?<second>\\d{1,2}))?"
//                    + "(?:秒)?"
//    );
    private static final Pattern DT_P = Pattern.compile(
            "(?<year>\\d{4})[年\\-/\\.]"
                    + "(?<month>\\d{1,2})[月\\-/\\.]"
                    + "(?<day>\\d{1,2})[日号]?"
                    + "(?:[,，\\s]*)"                      // 日期和时间之间允许无空格
                    + "(?<ampm>上午|下午)?\\s*"
                    + "(?<hour>\\d{1,2})"
                    + "(?:[时点:：\\.h](?<minute>\\d{1,2}))?"
                    + "(?:[分m:：\\.](?<second>\\d{1,2}))?"
                    + "(?:秒)?"
    );
    // 仅日期的正则
    private static final Pattern D_P = Pattern.compile(
            "(?<year>\\d{4})[年\\-/\\.]"
                    + "(?<month>\\d{1,2})[月\\-/\\.]"
                    + "(?<day>\\d{1,2})[日号]?"
    );

    // 中文日期预处理：二〇一六年十二月八日 → 2016年12月8日
    private static final Pattern CN_DATE_P = Pattern.compile(
            "(?<yearcn>[零〇一二三四五六七八九]{4})年"
                    + "(?<monthcn>[一二三四五六七八九十]{1,3})月"
                    + "(?<daycn>[一二三四五六七八九十]{1,3})[日号]"
    );

    // 中文时间预处理：上午九点三十分 → 上午9时30分
    private static final Pattern CN_TIME_P = Pattern.compile(
            "(?<ampm>上午|下午)?"
                    + "(?<hourcn>[一两二三四五六七八九十]{1,3})点"
                    + "(?<minutecn>[一两二三四五六七八九十]{1,3})?分?"
    );

    // 中文数字到阿拉伯数字
// 中文数字到阿拉伯数字（支持 0–99 范围内的表示）
    private static int parseChineseNumber(String cn) {
        if (cn == null || cn.isEmpty()) {
            return 0;
        }
        // 基础数字映射
        Map<Character, Integer> map = new HashMap<>();
        map.put('零', 0);
        map.put('〇', 0);
        map.put('一', 1);
        map.put('二', 2);
        map.put('三', 3);
        map.put('四', 4);
        map.put('五', 5);
        map.put('六', 6);
        map.put('七', 7);
        map.put('八', 8);
        map.put('九', 9);
        map.put('两', 2);
        // 如果包含“十”，分几种情况
        if (cn.contains("十")) {
            // 纯“十”
            if (cn.equals("十")) {
                return 10;
            }
            // 形如 “十三” → 10 + 3
            if (cn.startsWith("十")) {
                char tail = cn.charAt(1);
                return 10 + map.getOrDefault(tail, 0);
            }
            // 形如 “二十” → 2 * 10
            if (cn.endsWith("十")) {
                char head = cn.charAt(0);
                return map.getOrDefault(head, 0) * 10;
            }
            // 形如 “二十三” → 2*10 + 3
            String[] parts = cn.split("十", 2);
            int head = parts[0].isEmpty()
                    ? 1  // 理论上不会走到这里，但保险起见
                    : map.getOrDefault(parts[0].charAt(0), 0);
            int tail = parts.length > 1 && !parts[1].isEmpty()
                    ? map.getOrDefault(parts[1].charAt(0), 0)
                    : 0;
            return head * 10 + tail;
        }

        // 纯个位或多位直写（如 “二〇一六”）
        int val = 0;
        for (char c : cn.toCharArray()) {
            val = val * 10 + map.getOrDefault(c, 0);
        }
        return val;
    }

    public static String evaluate(String input) {
        try {


            if (input == null) return null;
            String s = input.toString().trim();
            if (s.isEmpty()) return null;
            s = s.replace("两", "2");
            s = s.replaceAll("(?<=\\d日|\\d号)?(上午|下午)", " $1");

            if(!s.contains("下午")&&!s.contains("上午")){
                s = CommonDateCleanUtil.findDateStr(s);
            }

            // —— 1. 中文日期规范化 ——
            Matcher cnDateM = CN_DATE_P.matcher(s);
            if (cnDateM.find()) {
                int y = parseChineseNumber(cnDateM.group("yearcn"));
                int M = parseChineseNumber(cnDateM.group("monthcn"));
                int d = parseChineseNumber(cnDateM.group("daycn"));
                String repl = String.format("%04d年%02d月%02d日", y, M, d);
                s = cnDateM.replaceFirst(repl);
            }

            // —— 2. 中文时间规范化 ——
            Matcher cnTimeM = CN_TIME_P.matcher(s);
            if (cnTimeM.find()) {
                String ampm = cnTimeM.group("ampm") != null ? cnTimeM.group("ampm") : "";
                int h = parseChineseNumber(cnTimeM.group("hourcn"));
                int mi = cnTimeM.group("minutecn") != null
                        ? parseChineseNumber(cnTimeM.group("minutecn")) : 0;
                String repl = String.format("%s%d时%d分", ampm, h, mi);
                s = cnTimeM.replaceFirst(repl);
            }
            // —— 3. 后续同前述：分段、统一破折号、正则提取 ——
            // 3.1 逗号分段取具体时间
            String[] parts = s.split("[,，]");
            String seg = parts.length > 1 ? parts[1].trim() : parts[0].trim();
            // 3.2 统一各种破折号
            seg = seg.replace('—', '-').replace('–', '-');

            // 3.3 按有没有时间标记选用正则
            boolean hasTime = seg.matches(".*(时|点|:|\\.).*");
            Matcher m = hasTime ? DT_P.matcher(seg) : D_P.matcher(seg);

            if (!m.find()) {
                return input;
            }

            // 3.4 拆字段
            int year = Integer.parseInt(m.group("year"));
            int month = Integer.parseInt(m.group("month"));
            int day = Integer.parseInt(m.group("day"));
            int hour = 0, minute = 0, second = 0;
            String am = null;
            if (hasTime) {
                hour = Integer.parseInt(m.group("hour"));
                minute = m.group("minute") != null
                        ? Integer.parseInt(m.group("minute"))
                        : 0;
                second = m.group("second") != null
                        ? Integer.parseInt(m.group("second"))
                        : 0;
                am = m.group("ampm");
                if ("下午".equals(am) && hour < 12) {
                    hour += 12;
                }
            }

            // 3.5 校验范围，超出则回退 00:00:00
            boolean okTime = hour >= 0 && hour < 24 && minute >= 0 && minute < 60 && second >= 0 && second < 60;
            if (!okTime) {
                hour = 0;
                minute = 0;
                second = 0;
            }

            // 3.6 构造输出
            LocalDateTime dt;
            try {
                dt = LocalDateTime.of(
                        LocalDate.of(year, month, day),
                        LocalTime.of(hour, minute, second)
                );
            } catch (DateTimeException ex) {
                dt = LocalDateTime.of(
                        LocalDate.of(year, month, day),
                        LocalTime.MIDNIGHT
                );
            }
            return (dt.format(OUT_FMT));
        } catch (Exception e) {
            return (input);

        }
    }

    // —— 简单测试 ——
    public static void main(String[] args) {
        String[] tests = {
                "2017年05月02日至2017年05月31日,2017-05-03 09:30–10:00",
                "2017年5月31日至6月2日,2017-05-31 10:00",
                "2022-10-18\",2022-10-18\" ",
                "2024年4月24日,2024-04-24 09:30",
                "2017年3月13日—2017年3月28日,2017.3.17上午9.00",
                "2017年05月02日至2017年05月31日,2017-05-03 09:30–10:00"};

        for (String in : tests) {
            System.out.printf("%s  ->  %s%n",
                    in,
                    evaluate((in))
            );
        }
    }
}
