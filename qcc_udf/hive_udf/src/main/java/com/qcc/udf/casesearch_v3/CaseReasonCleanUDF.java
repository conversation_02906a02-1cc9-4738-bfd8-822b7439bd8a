package com.qcc.udf.casesearch_v3;

import com.qcc.udf.casesearch_v3.enums.CaseReasonEnum;
import org.apache.hadoop.hive.ql.exec.UDF;
import parquet.Strings;

/**
 * @Auther: zhanqgiang
 * @Date: 2020/11/19 10:00
 * @Description: 案号简单清洗
 */
public class CaseReasonCleanUDF extends UDF {
    public static String evaluate(String content) {
        if (Strings.isNullOrEmpty(content)) {
            return "";
        }
        return CaseReasonEnum.find(content);
    }




    public static void main(String[] args) {
        String content = "经认定，该公司在2020年8月6日之后，仍未按照要求停止非吸业务，继续以口口相传等方式，打广安世界名花生态旅游项目、隆泉明都棚户区改造项目旗号，承诺高息回报、向公众吸收资金，截至目前，城南店非吸资金约600万元，涉及投资者110余人，未兑付资金约440万元；城北店非吸资金约600万元，涉及投资者60余人，未兑付资金约526万元；合计非吸资金1200万元，涉及投资者170余人，未兑付资金约966万元，上述行为符合《防范和处置非法集资条例》界定的&ldquo;非法性&rdquo;&ldquo;利诱性&rdquo;&ldquo;社会性&rdquo;三大要件。同时，在调查问询中，未严格按要求完整提供相关合同文本、业务台账、清退凭证等资料。";
        System.out.println(evaluate(content));
    }

}
