package com.qcc.udf.casesearch_v3.tmp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import com.qcc.udf.casesearch_v3.entity.input.CaseRoleEntity;
import com.qcc.udf.casesearch_v3.entity.input.InfoListEntity;
import com.qcc.udf.casesearch_v3.entity.output.*;
import com.qcc.udf.cpws.CommonUtil;
import com.qcc.udf.isvalidKeyno;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2022年05月12日 10:58
 */
public class CaseDebtUDF extends UDF {
    static Set<String> BQ_TAG_SET = Sets.newHashSet("1", "4", "7", "17", "21", "8", "9");

    public static void main(String[] args) {
        String json = "[{\"AmtInfo\":{\"9172a437cd0ccd44732fa2ff4baf2cc1\":{\"Amt\":\"69658000\",\"IsValid\":\"0\",\"Type\":\"执行标的\"},\"a44da298df40de501f4b91eb3469911c\":{\"Amt\":\"69658000\",\"IsValid\":\"0\",\"Type\":\"执行标的\"},\"p84696b8305670980ba2ed21ed98479f\":{\"Amt\":\"69658000\",\"IsValid\":\"0\",\"Type\":\"执行标的\"}},\"AnNoList\":\"（2015）鄂襄阳中民三初字第99号,（2016）鄂0607执347号,（2016）鄂06执监15号,（2018）鄂0607执恢56号,（2021）鄂0607执异112号,（2021）鄂0607执恢397号\",\"AnnoCnt\":6,\"CaseCnt\":4,\"CaseName\":\"中国农业银行股份有限公司襄阳襄州支行与万宝粮油有限公司,柴顺功,武汉宏诚达贸易有限责任公司民事案件\",\"CaseNameClean\":\"中国农业银行股份有限公司襄阳襄州支行与万宝粮油有限公司,柴顺功,武汉宏诚达贸易有限责任公司民事案件\",\"CaseReason\":\"\",\"CaseRole\":\"[{\\\"D\\\":\\\"一审原告,执行监督申请执行人,首次执行原告,恢复执行申请执行人\\\",\\\"N\\\":\\\"d93638b3e434d4594681e7a2cb1a5099\\\",\\\"O\\\":0,\\\"P\\\":\\\"中国农业银行股份有限公司襄阳襄州支行\\\",\\\"R\\\":\\\"原告\\\",\\\"RL\\\":[{\\\"R\\\":\\\"原告\\\",\\\"T\\\":\\\"一审\\\"},{\\\"R\\\":\\\"申请执行人\\\",\\\"T\\\":\\\"执行监督\\\"},{\\\"R\\\":\\\"原告\\\",\\\"T\\\":\\\"首次执行\\\"},{\\\"R\\\":\\\"申请执行人\\\",\\\"T\\\":\\\"恢复执行\\\"}]},{\\\"D\\\":\\\"一审被告,执行监督被执行人,首次执行被执行人,恢复执行被执行人,执行异议被执行人\\\",\\\"N\\\":\\\"9172a437cd0ccd44732fa2ff4baf2cc1\\\",\\\"O\\\":0,\\\"P\\\":\\\"万宝粮油有限公司\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"},{\\\"LR\\\":\\\"18\\\",\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"执行监督\\\"},{\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"首次执行\\\"},{\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"执行异议\\\"},{\\\"LR\\\":\\\"17\\\",\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"恢复执行\\\"}]},{\\\"D\\\":\\\"一审被告,执行监督被执行人,首次执行被执行人,恢复执行被执行人,执行异议被执行人\\\",\\\"N\\\":\\\"p84696b8305670980ba2ed21ed98479f\\\",\\\"O\\\":2,\\\"P\\\":\\\"柴顺功\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"},{\\\"LR\\\":\\\"18\\\",\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"执行监督\\\"},{\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"首次执行\\\"},{\\\"LR\\\":\\\"21\\\",\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"恢复执行\\\"},{\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"执行异议\\\"}]},{\\\"D\\\":\\\"一审被告,执行监督被执行人,首次执行被执行人,恢复执行被执行人,执行异议被执行人\\\",\\\"N\\\":\\\"a44da298df40de501f4b91eb3469911c\\\",\\\"O\\\":0,\\\"P\\\":\\\"武汉宏诚达贸易有限责任公司\\\",\\\"R\\\":\\\"被告\\\",\\\"RL\\\":[{\\\"R\\\":\\\"被告\\\",\\\"T\\\":\\\"一审\\\"},{\\\"LR\\\":\\\"18\\\",\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"执行监督\\\"},{\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"首次执行\\\"},{\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"执行异议\\\"},{\\\"LR\\\":\\\"17\\\",\\\"R\\\":\\\"被执行人\\\",\\\"T\\\":\\\"恢复执行\\\"}]}]\",\"CaseType\":\"执行案件,民事案件\",\"CfdfCnt\":0,\"CfgsCnt\":0,\"CfxyCnt\":0,\"CompanyKeywords\":\"008ae7a3c9b033b657cc65fd451605f4,9172a437cd0ccd44732fa2ff4baf2cc1,a44da298df40de501f4b91eb3469911c,d93638b3e434d4594681e7a2cb1a5099,p84696b8305670980ba2ed21ed98479f,pr4b4af5eda88ad6d988d83336c6138b,万宝粮油有限公司,中国农业银行股份有限公司襄阳襄州支行,姜岑岑,柴顺功,武汉宏诚达贸易有限责任公司,法联科技（武汉）有限公司,赵春生\",\"CourtList\":\"湖北省襄阳市中级人民法院,湖北省襄阳市襄州区人民法院\",\"EarliestDate\":1431446400,\"EarliestDateType\":\"民事一审|立案日期\",\"FyggCnt\":0,\"GqdjCnt\":0,\"GroupId\":\"a0977e92579a6ff3be19542994035675\",\"HbcfCnt\":0,\"Id\":\"7f07ea9cee994b8aa32f2018ac5c2d83\",\"InfoList\":[{\"AnNo\":\"（2015）鄂襄阳中民三初字第99号\",\"CaseList\":[],\"CaseReason\":\"\",\"CaseType\":\"民事案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"湖北省襄阳市中级人民法院\",\"Defendant\":[{\"KeyNo\":\"9172a437cd0ccd44732fa2ff4baf2cc1\",\"Name\":\"万宝粮油有限公司\",\"Org\":0,\"Role\":\"被告\"},{\"KeyNo\":\"p84696b8305670980ba2ed21ed98479f\",\"Name\":\"柴顺功\",\"Org\":2,\"Role\":\"被告\"},{\"KeyNo\":\"a44da298df40de501f4b91eb3469911c\",\"Name\":\"武汉宏诚达贸易有限责任公司\",\"Org\":0,\"Role\":\"被告\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1431446400,\"LianList\":[{\"Id\":\"8b3f25c3eddc7da593b60259a25ad157\",\"IsValid\":1,\"LianDate\":1431446400,\"NameAndKeyNo\":[{\"KeyNo\":\"d93638b3e434d4594681e7a2cb1a5099\",\"Name\":\"中国农业银行股份有限公司襄阳襄州支行\",\"Org\":0},{\"KeyNo\":\"9172a437cd0ccd44732fa2ff4baf2cc1\",\"Name\":\"万宝粮油有限公司\",\"Org\":0},{\"KeyNo\":\"p84696b8305670980ba2ed21ed98479f\",\"Name\":\"柴顺功\",\"Org\":2},{\"KeyNo\":\"a44da298df40de501f4b91eb3469911c\",\"Name\":\"武汉宏诚达贸易有限责任公司\",\"Org\":0}]}],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"d93638b3e434d4594681e7a2cb1a5099\",\"Name\":\"中国农业银行股份有限公司襄阳襄州支行\",\"Org\":0,\"Role\":\"原告\"}],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"民事一审\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]},{\"AnNo\":\"（2016）鄂0607执347号\",\"CaseList\":[],\"CaseReason\":\"\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"湖北省襄阳市襄州区人民法院\",\"Defendant\":[{\"KeyNo\":\"9172a437cd0ccd44732fa2ff4baf2cc1\",\"Name\":\"万宝粮油有限公司\",\"Org\":0,\"Role\":\"被执行人\"},{\"KeyNo\":\"p84696b8305670980ba2ed21ed98479f\",\"Name\":\"柴顺功\",\"Org\":2,\"Role\":\"被执行人\"},{\"KeyNo\":\"a44da298df40de501f4b91eb3469911c\",\"Name\":\"武汉宏诚达贸易有限责任公司\",\"Org\":0,\"Role\":\"被执行人\"}],\"ExecuteNo\":\"（2015）鄂襄阳中民三初字第99号民事判决书\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1477843200,\"LianList\":[{\"Id\":\"421692ef2c80dbcea2d15da0adcc7e5b\",\"IsValid\":1,\"LianDate\":1457452800,\"NameAndKeyNo\":[{\"KeyNo\":\"d93638b3e434d4594681e7a2cb1a5099\",\"Name\":\"中国农业银行股份有限公司襄阳襄州支行\",\"Org\":0},{\"KeyNo\":\"\",\"Name\":\"中国农业银行给付\",\"Org\":-1},{\"KeyNo\":\"9172a437cd0ccd44732fa2ff4baf2cc1\",\"Name\":\"万宝粮油有限公司\",\"Org\":0},{\"KeyNo\":\"p84696b8305670980ba2ed21ed98479f\",\"Name\":\"柴顺功\",\"Org\":2},{\"KeyNo\":\"a44da298df40de501f4b91eb3469911c\",\"Name\":\"武汉宏诚达贸易有限责任公司\",\"Org\":0}]}],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"\",\"Name\":\"中国农业银行给付\",\"Org\":-1,\"Role\":\"原告\"},{\"KeyNo\":\"d93638b3e434d4594681e7a2cb1a5099\",\"Name\":\"中国农业银行股份有限公司襄阳襄州支行\",\"Org\":0,\"Role\":\"原告\"}],\"SdggList\":[],\"SxList\":[{\"ActionType\":\"其他有履行能力而拒不履行生效法律文书确定义务\",\"ExecuteStatus\":\"全部未履行\",\"Id\":\"6ea5781c3d3fea7546fd4cf58f839a9b2\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"p84696b8305670980ba2ed21ed98479f\",\"Name\":\"柴顺功\",\"Org\":2}],\"PublishDate\":1461686400},{\"ActionType\":\"其他有履行能力而拒不履行生效法律文书确定义务\",\"ExecuteStatus\":\"全部未履行\",\"Id\":\"a17de877e302a90814abadcf946857592\",\"IsValid\":0,\"NameAndKeyNo\":[{\"KeyNo\":\"a44da298df40de501f4b91eb3469911c\",\"Name\":\"武汉宏诚达贸易有限责任公司\",\"Org\":0}],\"PublishDate\":1461686400}],\"TrialRound\":\"首次执行\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[{\"ExecuteObject\":\"55171667\",\"FailureAct\":\"0\",\"Id\":\"18b43d16e2d7c35a6b61d120b2244f2a\",\"IsValid\":1,\"JudgeDate\":1477843200,\"NameAndKeyNo\":[{\"KeyNo\":\"p84696b8305670980ba2ed21ed98479f\",\"Name\":\"柴顺功\",\"Org\":2}]},{\"ExecuteObject\":\"55171667\",\"FailureAct\":\"0\",\"Id\":\"3f09fc28bdab8b262f239be815cc5368\",\"IsValid\":1,\"JudgeDate\":1477843200,\"NameAndKeyNo\":[{\"KeyNo\":\"a44da298df40de501f4b91eb3469911c\",\"Name\":\"武汉宏诚达贸易有限责任公司\",\"Org\":0}]},{\"ExecuteObject\":\"55171667\",\"FailureAct\":\"0\",\"Id\":\"74cc346ac9b38026b83a15a2111beefb\",\"IsValid\":1,\"JudgeDate\":1477843200,\"NameAndKeyNo\":[{\"KeyNo\":\"9172a437cd0ccd44732fa2ff4baf2cc1\",\"Name\":\"万宝粮油有限公司\",\"Org\":0}]}],\"ZxList\":[{\"Biaodi\":\"55171668\",\"Id\":\"a17de877e302a90814abadcf946857591\",\"IsValid\":0,\"LianDate\":1457452800,\"NameAndKeyNo\":[{\"KeyNo\":\"a44da298df40de501f4b91eb3469911c\",\"Name\":\"武汉宏诚达贸易有限责任公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]},{\"Biaodi\":\"55171668\",\"Id\":\"dbfe1c3b3eaff6813a1de6350952dd4f1\",\"IsValid\":0,\"LianDate\":1457452800,\"NameAndKeyNo\":[{\"KeyNo\":\"9172a437cd0ccd44732fa2ff4baf2cc1\",\"Name\":\"万宝粮油有限公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[]}]},{\"AnNo\":\"（2016）鄂06执监15号\",\"CaseList\":[{\"Amt\":\"\",\"CaseType\":\"执行裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"942b5d147907a3a2a1e0289aa6659e300\",\"IsValid\":1,\"JudgeDate\":1455667200,\"Result\":\"湖北省襄阳市中级人民法院作出的（2015）鄂襄阳中民三初字第00099号民事判决一案指定襄州区人民法院执行。\",\"ResultType\":\"裁定结果\",\"ShieldCaseFlag\":0}],\"CaseReason\":\"借款合同纠纷案件执行\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"湖北省襄阳市中级人民法院\",\"Defendant\":[{\"KeyNo\":\"9172a437cd0ccd44732fa2ff4baf2cc1\",\"LR\":\"18\",\"Name\":\"万宝粮油有限公司\",\"Org\":0,\"Role\":\"被执行人\"},{\"KeyNo\":\"p84696b8305670980ba2ed21ed98479f\",\"LR\":\"18\",\"Name\":\"柴顺功\",\"Org\":2,\"Role\":\"被执行人\"},{\"KeyNo\":\"a44da298df40de501f4b91eb3469911c\",\"LR\":\"18\",\"Name\":\"武汉宏诚达贸易有限责任公司\",\"Org\":0,\"Role\":\"被执行人\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1455667200,\"LianList\":[],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"d93638b3e434d4594681e7a2cb1a5099\",\"Name\":\"中国农业银行股份有限公司襄阳襄州支行\",\"Org\":0,\"Role\":\"申请执行人\"}],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"执行监督\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]},{\"AnNo\":\"（2018）鄂0607执恢56号\",\"CaseList\":[{\"Amt\":\"\",\"CaseType\":\"执行裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"153071b91a3ab0f2fb4a72c45a0878bc0\",\"IsValid\":1,\"JudgeDate\":1554134400,\"Result\":\"终结本次执行程序。\",\"ResultType\":\"裁定结果\",\"ShieldCaseFlag\":0}],\"CaseReason\":\"借款合同纠纷案件执行\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"湖北省襄阳市襄州区人民法院\",\"Defendant\":[{\"KeyNo\":\"9172a437cd0ccd44732fa2ff4baf2cc1\",\"LR\":\"21\",\"Name\":\"万宝粮油有限公司\",\"Org\":0,\"Role\":\"被执行人\"},{\"KeyNo\":\"p84696b8305670980ba2ed21ed98479f\",\"LR\":\"21\",\"Name\":\"柴顺功\",\"Org\":2,\"Role\":\"被执行人\"},{\"KeyNo\":\"a44da298df40de501f4b91eb3469911c\",\"LR\":\"21\",\"Name\":\"武汉宏诚达贸易有限责任公司\",\"Org\":0,\"Role\":\"被执行人\"}],\"ExecuteNo\":\"（2015）鄂襄阳中民三初字第00099号\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1554220800,\"LianList\":[{\"Id\":\"0938932050c8ef8197ccc46dd86bbe9a\",\"IsValid\":1,\"LianDate\":1523116800,\"NameAndKeyNo\":[{\"KeyNo\":\"d93638b3e434d4594681e7a2cb1a5099\",\"Name\":\"中国农业银行股份有限公司襄阳襄州支行\",\"Org\":0},{\"KeyNo\":\"9172a437cd0ccd44732fa2ff4baf2cc1\",\"Name\":\"万宝粮油有限公司\",\"Org\":0},{\"KeyNo\":\"p84696b8305670980ba2ed21ed98479f\",\"Name\":\"柴顺功\",\"Org\":2},{\"KeyNo\":\"a44da298df40de501f4b91eb3469911c\",\"Name\":\"武汉宏诚达贸易有限责任公司\",\"Org\":0}]}],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"d93638b3e434d4594681e7a2cb1a5099\",\"Name\":\"中国农业银行股份有限公司襄阳襄州支行\",\"Org\":0,\"Role\":\"申请执行人\"}],\"SdggList\":[],\"SfpmList\":[{\"AmountUnit\":\"元\",\"BiaoDi\":\"变卖武汉宏诚达贸易公司所有位于武汉青山区建设二路3-6号1-2层商网1、2、3号房产\",\"EvaluationPrice\":\"41494800\",\"Id\":\"4eae1c98ebf2029e558ef841e93d9766\",\"IsValid\":1,\"LianDate\":1543334400,\"Name\":\"变卖武汉宏诚达贸易公司所有位于武汉青山区建设二路3-6号1-2层商网1、2、3号房产\",\"OwnerKeyNoArray\":[{\"KeyNo\":\"a44da298df40de501f4b91eb3469911c\",\"Name\":\"武汉宏诚达贸易有限责任公司\",\"Org\":0}],\"YiWu\":\"33195840\"},{\"AmountUnit\":\"元\",\"BiaoDi\":\"武汉宏诚达贸易公司所有位于武汉青山区建设二路3-6号1-2层商网1、2、3号房产\",\"EvaluationPrice\":\"41494800\",\"Id\":\"a91bf9bd40f05cf7795a6b782008551e\",\"IsValid\":1,\"LianDate\":1530720000,\"Name\":\"一拍武汉宏诚达贸易公司所有位于武汉青山区建设二路3-6号1-2层商网1、2、3号房产\",\"OwnerKeyNoArray\":[{\"KeyNo\":\"a44da298df40de501f4b91eb3469911c\",\"Name\":\"武汉宏诚达贸易有限责任公司\",\"Org\":0}],\"YiWu\":\"41494800\"}],\"SxList\":[{\"ActionType\":\"有履行能力而拒不履行生效法律文书确定义务\",\"ExecuteStatus\":\"全部未履行\",\"Id\":\"4e6e17feb89f1a5b3378dc8d9731f1742\",\"IsValid\":0,\"NameAndKeyNo\":[{\"KeyNo\":\"a44da298df40de501f4b91eb3469911c\",\"Name\":\"武汉宏诚达贸易有限责任公司\",\"Org\":0}],\"PublishDate\":1554134400},{\"ActionType\":\"有履行能力而拒不履行生效法律文书确定义务\",\"ExecuteStatus\":\"全部未履行\",\"Id\":\"b19757cdc23178046699b2edc11adfa12\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"9172a437cd0ccd44732fa2ff4baf2cc1\",\"Name\":\"万宝粮油有限公司\",\"Org\":0}],\"PublishDate\":1554134400},{\"ActionType\":\"有履行能力而拒不履行生效法律文书确定义务\",\"ExecuteStatus\":\"全部未履行\",\"Id\":\"da4d86ebac3a32fdda13c2950594e4712\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"p84696b8305670980ba2ed21ed98479f\",\"Name\":\"柴顺功\",\"Org\":2}],\"PublishDate\":1554134400}],\"TrialRound\":\"恢复执行\",\"XgList\":[{\"CompanyInfo\":[{\"KeyNo\":\"9172a437cd0ccd44732fa2ff4baf2cc1\",\"Name\":\"万宝粮油有限公司\",\"Org\":0}],\"GlNameAndKeyNo\":[{\"KeyNo\":\"p84696b8305670980ba2ed21ed98479f\",\"Name\":\"柴顺功\",\"Org\":2}],\"Id\":\"1be0b83d7c06d7e9abd1144504e5be3d\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"9172a437cd0ccd44732fa2ff4baf2cc1\",\"Name\":\"万宝粮油有限公司\",\"Org\":0}],\"PublishDate\":1554134400,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"d93638b3e434d4594681e7a2cb1a5099\",\"Name\":\"中国农业银行股份有限公司襄阳襄州支行\",\"Org\":0}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"9172a437cd0ccd44732fa2ff4baf2cc1\",\"Name\":\"万宝粮油有限公司\",\"Org\":0}]},{\"CompanyInfo\":[{\"KeyNo\":\"a44da298df40de501f4b91eb3469911c\",\"Name\":\"武汉宏诚达贸易有限责任公司\",\"Org\":0}],\"GlNameAndKeyNo\":[{\"KeyNo\":\"pr4b4af5eda88ad6d988d83336c6138b\",\"Name\":\"姜岑岑\",\"Org\":2}],\"Id\":\"5c993e7d1d78a33dc6de90caedeaa260\",\"IsValid\":1,\"NameAndKeyNo\":[{\"KeyNo\":\"a44da298df40de501f4b91eb3469911c\",\"Name\":\"武汉宏诚达贸易有限责任公司\",\"Org\":0}],\"PublishDate\":1554220800,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"d93638b3e434d4594681e7a2cb1a5099\",\"Name\":\"中国农业银行股份有限公司襄阳襄州支行\",\"Org\":0}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"a44da298df40de501f4b91eb3469911c\",\"Name\":\"武汉宏诚达贸易有限责任公司\",\"Org\":0}]},{\"CompanyInfo\":[{\"KeyNo\":\"p84696b8305670980ba2ed21ed98479f\",\"Name\":\"柴顺功\",\"Org\":2}],\"GlNameAndKeyNo\":[],\"Id\":\"af26a2403b16f64f044c8bef29ec8af7\",\"IsValid\":0,\"NameAndKeyNo\":[{\"KeyNo\":\"p84696b8305670980ba2ed21ed98479f\",\"Name\":\"柴顺功\",\"Org\":2}],\"PublishDate\":1554134400,\"SqrNameAndKeyNo\":[{\"KeyNo\":\"d93638b3e434d4594681e7a2cb1a5099\",\"Name\":\"中国农业银行股份有限公司襄阳襄州支行\",\"Org\":0}],\"XglNameAndKeyNo\":[{\"KeyNo\":\"p84696b8305670980ba2ed21ed98479f\",\"Name\":\"柴顺功\",\"Org\":2}]}],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[{\"Biaodi\":\"69658000\",\"Id\":\"4e6e17feb89f1a5b3378dc8d9731f1741\",\"IsValid\":0,\"LianDate\":1523116800,\"NameAndKeyNo\":[{\"KeyNo\":\"a44da298df40de501f4b91eb3469911c\",\"Name\":\"武汉宏诚达贸易有限责任公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"d93638b3e434d4594681e7a2cb1a5099\",\"Name\":\"中国农业银行股份有限公司襄阳襄州支行\",\"Org\":0}]},{\"Biaodi\":\"69658000\",\"Id\":\"b19757cdc23178046699b2edc11adfa11\",\"IsValid\":0,\"LianDate\":1523116800,\"NameAndKeyNo\":[{\"KeyNo\":\"9172a437cd0ccd44732fa2ff4baf2cc1\",\"Name\":\"万宝粮油有限公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"d93638b3e434d4594681e7a2cb1a5099\",\"Name\":\"中国农业银行股份有限公司襄阳襄州支行\",\"Org\":0}]},{\"Biaodi\":\"69658000\",\"Id\":\"da4d86ebac3a32fdda13c2950594e4711\",\"IsValid\":0,\"LianDate\":1523116800,\"NameAndKeyNo\":[{\"KeyNo\":\"p84696b8305670980ba2ed21ed98479f\",\"Name\":\"柴顺功\",\"Org\":2}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"d93638b3e434d4594681e7a2cb1a5099\",\"Name\":\"中国农业银行股份有限公司襄阳襄州支行\",\"Org\":0}]}]},{\"AnNo\":\"（2021）鄂0607执异112号\",\"CaseList\":[{\"Amt\":\"\",\"CaseType\":\"执行裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"dc2144b0d527888cb15c05357cb9c8be0\",\"IsValid\":1,\"JudgeDate\":1637164800,\"Result\":\"变更赵春生（公民身份号码411121197403××××）为本案的申请执行人。\",\"ResultType\":\"裁定结果\",\"ShieldCaseFlag\":0}],\"CaseReason\":\"借款合同纠纷案件执行\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"湖北省襄阳市襄州区人民法院\",\"Defendant\":[{\"KeyNo\":\"9172a437cd0ccd44732fa2ff4baf2cc1\",\"Name\":\"万宝粮油有限公司\",\"Org\":0,\"Role\":\"被执行人\"},{\"KeyNo\":\"p84696b8305670980ba2ed21ed98479f\",\"Name\":\"柴顺功\",\"Org\":2,\"Role\":\"被执行人\"},{\"KeyNo\":\"a44da298df40de501f4b91eb3469911c\",\"Name\":\"武汉宏诚达贸易有限责任公司\",\"Org\":0,\"Role\":\"被执行人\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1637164800,\"LianList\":[{\"Id\":\"dc2144b0d527888cb15c05357cb9c8be\",\"IsValid\":1,\"LianDate\":1457452800,\"NameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"赵春生\",\"Org\":-2},{\"KeyNo\":\"008ae7a3c9b033b657cc65fd451605f4\",\"Name\":\"法联科技（武汉）有限公司\",\"Org\":0},{\"KeyNo\":\"p84696b8305670980ba2ed21ed98479f\",\"Name\":\"柴顺功\",\"Org\":2},{\"KeyNo\":\"9172a437cd0ccd44732fa2ff4baf2cc1\",\"Name\":\"万宝粮油有限公司\",\"Org\":0},{\"KeyNo\":\"a44da298df40de501f4b91eb3469911c\",\"Name\":\"武汉宏诚达贸易有限责任公司\",\"Org\":0}]}],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"008ae7a3c9b033b657cc65fd451605f4\",\"Name\":\"法联科技（武汉）有限公司\",\"Org\":0,\"Role\":\"申请执行人\"},{\"KeyNo\":\"\",\"LawFirmList\":[{\"LY\":[{\"N\":\"6050e0cb2cbe7f2df1803ac2dd73c105\",\"P\":\"李健\",\"R\":\"委托代理人\"}],\"N\":\"w5769dbae473569e4d9ce34f9282fb6c\",\"O\":4,\"P\":\"湖北炽升律师事务所\"}],\"Name\":\"赵春生\",\"Org\":-2,\"Role\":\"申请人\"}],\"SdggList\":[],\"SxList\":[],\"TrialRound\":\"执行异议\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[]},{\"AnNo\":\"（2021）鄂0607执恢397号\",\"CaseList\":[{\"Amt\":\"\",\"CaseType\":\"执行裁定书\",\"DocType\":\"裁定日期\",\"Id\":\"169b1207272035b63d05950a7f33e09c0\",\"IsValid\":1,\"JudgeDate\":1650988800,\"Result\":\"终结本院（2021）鄂0607执恢397号执行案件的执行。\",\"ResultType\":\"裁定结果\",\"ShieldCaseFlag\":0}],\"CaseReason\":\"借款合同纠纷案件执行\",\"CaseType\":\"执行案件\",\"CfdfList\":[],\"CfgsList\":[],\"CfxyList\":[],\"Court\":\"湖北省襄阳市襄州区人民法院\",\"Defendant\":[{\"KeyNo\":\"9172a437cd0ccd44732fa2ff4baf2cc1\",\"LR\":\"17\",\"Name\":\"万宝粮油有限公司\",\"Org\":0,\"Role\":\"被执行人\"},{\"KeyNo\":\"a44da298df40de501f4b91eb3469911c\",\"LR\":\"17\",\"Name\":\"武汉宏诚达贸易有限责任公司\",\"Org\":0,\"Role\":\"被执行人\"}],\"ExecuteNo\":\"\",\"FyggList\":[],\"GqdjList\":[],\"HbcfList\":[],\"KtggList\":[],\"LatestTimestamp\":1650988800,\"LianList\":[{\"Id\":\"71b9522cdbf20581098e0024f95f7448\",\"IsValid\":1,\"LianDate\":1635868800,\"NameAndKeyNo\":[{\"KeyNo\":\"008ae7a3c9b033b657cc65fd451605f4\",\"Name\":\"法联科技（武汉）有限公司\",\"Org\":0},{\"KeyNo\":\"9172a437cd0ccd44732fa2ff4baf2cc1\",\"Name\":\"万宝粮油有限公司\",\"Org\":0},{\"KeyNo\":\"p84696b8305670980ba2ed21ed98479f\",\"Name\":\"柴顺功\",\"Org\":2},{\"KeyNo\":\"a44da298df40de501f4b91eb3469911c\",\"Name\":\"武汉宏诚达贸易有限责任公司\",\"Org\":0}]}],\"PcczList\":[],\"Procuratorate\":\"\",\"Prosecutor\":[{\"KeyNo\":\"\",\"Name\":\"赵春生\",\"Org\":-2,\"Role\":\"申请执行人\"}],\"SdggList\":[],\"SfpmList\":[{\"AmountUnit\":\"元\",\"BiaoDi\":\"武汉市青山区建设二路3-6号1-2层商网1号房屋及分摊占用的土地使用权\",\"EvaluationPrice\":\"7962801\",\"Id\":\"53c053ca3d7a760250abd495109d55df\",\"IsValid\":1,\"LianDate\":1648483200,\"Name\":\"武汉市青山区建设二路3-6号1-2层商网1号房屋及分摊占用的土地使用权\",\"OwnerKeyNoArray\":[],\"YiWu\":\"6370240.80\"}],\"SxList\":[],\"TrialRound\":\"恢复执行\",\"XgList\":[],\"XjpgList\":[],\"XzcffList\":[],\"ZbList\":[],\"ZxList\":[{\"Biaodi\":\"69658000\",\"Id\":\"707fba33ca53260fa7e9f9c80693b1ec1\",\"IsValid\":0,\"LianDate\":1635868800,\"NameAndKeyNo\":[{\"KeyNo\":\"p84696b8305670980ba2ed21ed98479f\",\"Name\":\"柴顺功\",\"Org\":2}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"赵春生\",\"Org\":-2}]},{\"Biaodi\":\"69658000\",\"Id\":\"8d76697a8262d8abfa659fad8d54f7191\",\"IsValid\":0,\"LianDate\":1635868800,\"NameAndKeyNo\":[{\"KeyNo\":\"9172a437cd0ccd44732fa2ff4baf2cc1\",\"Name\":\"万宝粮油有限公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"赵春生\",\"Org\":-2}]},{\"Biaodi\":\"69658000\",\"Id\":\"8f19ebb0401f8918c0b6a11d31306a9c1\",\"IsValid\":0,\"LianDate\":1635868800,\"NameAndKeyNo\":[{\"KeyNo\":\"a44da298df40de501f4b91eb3469911c\",\"Name\":\"武汉宏诚达贸易有限责任公司\",\"Org\":0}],\"SqrNameAndKeyNo\":[{\"KeyNo\":\"\",\"Name\":\"赵春生\",\"Org\":-2}]}]}],\"KtggCnt\":0,\"LastestDate\":1650988800,\"LastestDateType\":\"恢复执行|裁定日期\",\"LatestTrialRound\":\"恢复执行\",\"LawyerIds\":\"5b6dcd19642c4219d2ca56c98ff802b4,6050e0cb2cbe7f2df1803ac2dd73c105\",\"LianCnt\":5,\"PcczCnt\":0,\"ProcuratorateList\":\"\",\"Province\":\"HUB\",\"SdggCnt\":0,\"SfpmCnt\":3,\"Source\":\"OT\",\"SxCnt\":5,\"Tags\":\"1,2,3,4,6,12,15\",\"Type\":1,\"XgCnt\":3,\"XjpgCnt\":0,\"XzcfCnt\":0,\"ZbCnt\":3,\"ZxCnt\":8}]";
        System.out.println(evaluate(json));
    }

    public static String evaluate(String json) {
        List<DebtType> typeList = new ArrayList<>();
        List<LawSuitV3OutputEntity> outList = JSON.parseArray(json, LawSuitV3OutputEntity.class);

        if (outList != null) {
            for (LawSuitV3OutputEntity outputEntity : outList) {
                List<InfoListEntity> infoList = outputEntity.getInfoList();
                if (Strings.isNullOrEmpty(outputEntity.getId()) || CollectionUtils.isEmpty(infoList)) {
                    continue;
                }
                Map<String, AmtInfo> amtInfo = outputEntity.getAmtInfo();
                Set<String> keyNoSet = Arrays.asList(outputEntity.getCompanyKeywords().split(","))
                        .stream().filter(isvalidKeyno::evaluate).collect(Collectors.toSet());
                if (CollectionUtils.isEmpty(keyNoSet)) {
                    continue;
                }
//                System.out.println("keyNoSet size->" + keyNoSet.size());
                String caseId = outputEntity.getId();

                long earliestDate = outputEntity.getEarliestDate();
                long lastestDate = outputEntity.getLastestDate();
                Set<Long> ktggDateSet = new HashSet<>();


                // 获取用于查询的caserole
                JSONObject caseRoleNew = CaseRoleUtil.evaluate(outputEntity);

                // 获取原被告
                String caseRoleSearch = caseRoleNew.getJSONArray("New").toString();
                JSONObject jsonObject = getRoleName(caseRoleSearch);
                String yg = jsonObject.getString("P");
                String bg = jsonObject.getString("D");
                List<CaseRoleEntity> roleList = JSON.parseArray(caseRoleSearch, CaseRoleEntity.class);

                Set<String> ygKeyNoSet = Arrays.asList(yg.split(","))
                        .stream().filter(isvalidKeyno::evaluate).collect(Collectors.toSet());

                Set<String> bgKeyNoSet = Arrays.asList(bg.split(","))
                        .stream().filter(isvalidKeyno::evaluate).collect(Collectors.toSet());
                keyNoSet.addAll(ygKeyNoSet);
                keyNoSet.addAll(bgKeyNoSet);

                Set<String> proSet = yg == null ?
                        new HashSet<>() :
                        Arrays.stream(yg.split(",")).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());


                List<CaseRoleEntity> caseRoleEntityList = JSON.parseArray(caseRoleSearch, CaseRoleEntity.class);
                List<CaseRoleEntity> proRoleList = new ArrayList<>();
                for (CaseRoleEntity caseRole : caseRoleEntityList) {
                    caseRole.setD(null);
                    caseRole.setT(null);
                    caseRole.setR(null);
                    caseRole.setRoleList(null);
                     if (proSet.contains(caseRole.getN()) || proSet.contains(caseRole.getP())) {
                        proRoleList.add(caseRole);
                    }
                }

                String caseReason = outputEntity.getCaseReason();

                String cateType = outputEntity.getCaseType();
                boolean isBqAJ = cateType.contains("保全案件");


                if (CollectionUtils.isEmpty(keyNoSet)) {
                    continue;
                }

                List<SXListEntity> sxList = new ArrayList<>();
                List<ZXListEntity> zxList = new ArrayList<>();
                List<XGListEntity> xgList = new ArrayList<>();
                for (InfoListEntity info : infoList) {
                    List<SXListEntity> subSxList = info.getSxList();
                    List<ZXListEntity> subZxList = info.getZxList();
                    List<XGListEntity> subXgList = info.getXgList();
                    subSxList = subSxList.stream().filter(x -> x.getIsValid() == 1).collect(Collectors.toList());
                    subZxList = subZxList.stream().filter(x -> x.getIsValid() == 1).collect(Collectors.toList());
                    subXgList = subXgList.stream().filter(x -> x.getIsValid() == 1).collect(Collectors.toList());
                    sxList.addAll(subSxList);
                    zxList.addAll(subZxList);
                    xgList.addAll(subXgList);
                    ktggDateSet.addAll(info.getKtggList().stream().map(x->x.getOpenDate()).collect(Collectors.toSet()));
                }
                Long ktggLastestDate = ktggDateSet.stream().sorted(Comparator.reverseOrder()).findFirst().orElse(-1L);
                Iterator<String> iterator = keyNoSet.iterator();
                while (iterator.hasNext()) {
                    String keyNo = iterator.next();
                    String ruleType = "";
                    String money = "";
                    String sourceId = "";
                    //1.current sx
                    //2.current zx
                    //3.current xg
                    lp1:
                    for (SXListEntity sub : sxList) {
                        for (NameAndKeyNoEntity nameKey : sub.getNameAndKeyNo()) {
                            if (Objects.equals(nameKey.getKeyNo(), keyNo)) {
                                ruleType = "SX";
                                money = "";
                                sourceId = sub.getId();
                                break lp1;
                            }
                        }

                    }
                    if (Strings.isNullOrEmpty(ruleType)) {
                        lp1:
                        for (ZXListEntity sub : zxList) {
                            for (NameAndKeyNoEntity nameKey : sub.getNameAndKeyNo()) {
                                if (Objects.equals(nameKey.getKeyNo(), keyNo)) {
                                    ruleType = "ZX";
                                    money = sub.getBiaodi();
                                    sourceId = sub.getId();
                                    break lp1;
                                }
                            }

                        }
                    }
                    if (Strings.isNullOrEmpty(ruleType)) {
                        lp1:
                        for (XGListEntity sub : xgList) {
                            for (NameAndKeyNoEntity nameKey : sub.getNameAndKeyNo()) {
                                if (Objects.equals(nameKey.getKeyNo(), keyNo)) {
                                    ruleType = "XG";
                                    money = "";
                                    sourceId = sub.getId();
                                    break lp1;
                                }
                            }

                        }
                    }
                    if (!Strings.isNullOrEmpty(ruleType)) {
                        iterator.remove();
                        typeList.add(new DebtType(keyNo, ruleType, money, sourceId, caseId,JSON.toJSONString(proRoleList),earliestDate,lastestDate,ktggLastestDate,caseReason));
                    }
                }
                Map<String, CaseRoleEntity> roleMap = roleList.stream().filter(x -> !Strings.isNullOrEmpty(x.getN()))
                        .collect(Collectors.groupingBy(CaseRoleEntity::getN,
                                Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(CaseRoleEntity::getP))
                                        , Optional::get)));
                Map<String, Set<String>> roleTagSetMap = new HashMap<>();
                roleMap.forEach((k, v) -> {
                    Set<String> lawTagSet = new HashSet<>();
                    for (CaseRoleSort caseRoleSort : v.getRoleList()) {
                        if (!Strings.isNullOrEmpty(caseRoleSort.getLawsuitResult())) {
                            lawTagSet.addAll(Arrays.asList(caseRoleSort.getLawsuitResult().split(",")));
                        }
                    }
                    if (CollectionUtils.isNotEmpty(lawTagSet)) {
                        roleTagSetMap.put(k, lawTagSet);
                    }

                });
                boolean caseReasonFlag = REASON_SET.contains(caseReason);
                //4. aj rule1
                if (caseReasonFlag) {
                    iterator = keyNoSet.iterator();
                    while (iterator.hasNext()) {
                        String keyNo = iterator.next();
                        //被告
                        if (!bgKeyNoSet.contains(keyNo)) {
                            continue;
                        }
                        Set<String> lawTagSet = roleTagSetMap.getOrDefault(keyNo, new HashSet<>());
                        long tagMatch = lawTagSet.stream().filter(x -> BQ_TAG_SET.contains(x)).count();
                        if (tagMatch == 0L && !isBqAJ) {
                            continue;
                        }
                        String money = amtInfo.get(keyNo) == null ? "" : amtInfo.get(keyNo).getAmt();
                        typeList.add(new DebtType(keyNo, "CASE-2", money, "", caseId,JSON.toJSONString(proRoleList),earliestDate,lastestDate,ktggLastestDate,caseReason));
                        iterator.remove();
                    }
                }


                if(caseReasonFlag){
                    //5. aj rule2
                    iterator = keyNoSet.iterator();
                    while (iterator.hasNext()) {
                        String keyNo = iterator.next();
                        //被告
                        if (!bgKeyNoSet.contains(keyNo)) {
                            continue;
                        }
                        String money = amtInfo.get(keyNo) == null ? "" : amtInfo.get(keyNo).getAmt();
                        typeList.add(new DebtType(keyNo, "CASE-3", money, "", caseId,JSON.toJSONString(proRoleList),earliestDate,lastestDate,ktggLastestDate,caseReason));
                    }
                }
//                System.out.println(JSON.toJSONString(typeList));

            }
        }
        return JSON.toJSONString(typeList);
    }

    public static JSONObject getRoleName(String caseRole) {
        JSONObject result = new JSONObject();
        result.put("P", "");
        result.put("D", "");
        result.put("O", "");
        result.put("PE", new JSONArray());
        result.put("DE", new JSONArray());
        if (StringUtils.isEmpty(caseRole)) {
            return result;
        }

        Iterator<Object> it = JSONArray.parseArray(caseRole).iterator();
        Set<String> pSet = new LinkedHashSet<>();
        Set<String> dSet = new LinkedHashSet<>();
        Set<String> oSet = new LinkedHashSet<>();
        JSONArray peArray = new JSONArray();
        JSONArray deArray = new JSONArray();
        while (it.hasNext()) {
            JSONObject jsonObject = (JSONObject) it.next();
            String r = CommonUtil.full2Half(jsonObject.getString("R"));

            Set<String> beigaoSet = new LinkedHashSet<>();
            beigaoSet.add("被执行人");
            beigaoSet.add("被告");
            beigaoSet.add("被申请人");
            beigaoSet.add("被申请执行人");
            beigaoSet.add("原审被告");
            beigaoSet.add("被上诉人(原审被告)");
            beigaoSet.add("被上诉人(原审被告)");
            beigaoSet.add("上诉人(原审被告)");
            beigaoSet.add("被告(反诉原告)");
            beigaoSet.add("被告人");
            beigaoSet.add("上诉人(一审被告)");
            beigaoSet.add("被上诉人(一审被告)");
            beigaoSet.add("被上诉人");
            beigaoSet.add("上诉人(原审被告反诉原告)");
            beigaoSet.add("被告二");
            beigaoSet.add("被告一");
            beigaoSet.add("原告(被告)");
            beigaoSet.add("被申请人(一审被告二审被上诉人)");
            beigaoSet.add("被申请人(原审被告)");
            beigaoSet.add("再审申请人(一审被告二审上诉人)");
            beigaoSet.add("再审申请人(原审被告)");
            beigaoSet.add("被申请人(仲裁被申请人)");
            beigaoSet.add("被申请人(原被执行人)");
            beigaoSet.add("再审被申请人");
            beigaoSet.add("上诉人(原审被告原审原告)");
            beigaoSet.add("原审被告单位");
            beigaoSet.add("被起诉人");
            beigaoSet.add("被告单位");

            Set<String> yuangaoSet = new LinkedHashSet<>();
            yuangaoSet.add("申请执行人");
            yuangaoSet.add("原告");
            yuangaoSet.add("申请人");
            yuangaoSet.add("被上诉人(原审原告)");
            yuangaoSet.add("复议申请人");
            yuangaoSet.add("上诉人(原审原告)");
            yuangaoSet.add("原告(反诉被告)");
            yuangaoSet.add("被上诉人(一审原告)");
            yuangaoSet.add("上诉人(一审原告)");
            yuangaoSet.add("上诉人");
            yuangaoSet.add("(一审原告)");
            yuangaoSet.add("(原审原告反诉被告)");
            yuangaoSet.add("原审原告");
            yuangaoSet.add("再审申请人");
            yuangaoSet.add("被告(原告)");
            yuangaoSet.add("被申请人(原审原告)");
            yuangaoSet.add("附带民事诉讼原告人");
            yuangaoSet.add("复议申请人(原申请执行人)");
            yuangaoSet.add("再审申请人(一审原告二审上诉人)");
            yuangaoSet.add("再审申请人(原审原告)");
            yuangaoSet.add("申请再审人(一审原告二审上诉人)");
            yuangaoSet.add("二审上诉人");
            yuangaoSet.add("原告人");
            yuangaoSet.add("附带民事诉讼原告");
            yuangaoSet.add("上诉人(原审原告原审被告)");
            yuangaoSet.add("起诉人");
            yuangaoSet.add("申请人(仲裁申请人)");
            yuangaoSet.add("赔偿请求人");
            yuangaoSet.add("被上诉人(原审原告反诉被告)");
            yuangaoSet.add("申请追加人(申请执行人)");

            if (yuangaoSet.contains(r)) {
                if (StringUtils.isNotEmpty(jsonObject.getString("P"))) {
                    pSet.add(CommonUtil.full2Half(jsonObject.getString("P")));
                }
                if (StringUtils.isNotEmpty(jsonObject.getString("N"))) {
                    pSet.add(jsonObject.getString("N"));
                }
                peArray.add(jsonObject);
            } else if (beigaoSet.contains(r)) {
                if (StringUtils.isNotEmpty(jsonObject.getString("P"))) {
                    dSet.add(CommonUtil.full2Half(jsonObject.getString("P")));
                }
                if (StringUtils.isNotEmpty(jsonObject.getString("N"))) {
                    dSet.add(jsonObject.getString("N"));
                }
                deArray.add(jsonObject);
            } else {
                if (StringUtils.isNotEmpty(jsonObject.getString("P"))) {
                    oSet.add(CommonUtil.full2Half(jsonObject.getString("P")));
                }
                if (StringUtils.isNotEmpty(jsonObject.getString("N"))) {
                    oSet.add(jsonObject.getString("N"));
                }
            }

        }

        result.put("P", String.join(",", pSet));
        result.put("D", String.join(",", dSet));
        result.put("O", String.join(",", oSet));
        result.put("PE", peArray);
        result.put("DE", deArray);
        return result;
    }


    public static Set<String> REASON_SET = new HashSet<>();

    static {
        String REASON_STR = "合同纠纷\n" +
                "缔约过失责任纠纷\n" +
                "确认合同效力纠纷\n" +
                "确认合同有效纠纷\n" +
                "确认合同无效纠纷\n" +
                "债权人代位权纠纷\n" +
                "债权人撤销权纠纷\n" +
                "债权转让合同纠纷\n" +
                "债务转移合同纠纷\n" +
                "债权债务概括转移合同纠纷\n" +
                "悬赏广告纠纷\n" +
                "买卖合同纠纷\n" +
                "分期付款买卖合同纠纷\n" +
                "凭样品买卖合同纠纷\n" +
                "试用买卖合同纠纷\n" +
                "互易纠纷\n" +
                "国际货物买卖合同纠纷\n" +
                "网络购物合同纠纷\n" +
                "电视购物合同纠纷\n" +
                "招标投标买卖合同纠纷\n" +
                "拍卖合同纠纷\n" +
                "建设用地使用权合同纠纷\n" +
                "建设用地使用权出让合同纠纷\n" +
                "建设用地使用权转让合同纠纷\n" +
                "临时用地合同纠纷\n" +
                "探矿权转让合同纠纷\n" +
                "采矿权转让合同纠纷\n" +
                "房地产开发经营合同纠纷\n" +
                "委托代建合同纠纷\n" +
                "合资、合作开发房地产合同纠纷\n" +
                "项目转让合同纠纷\n" +
                "房屋买卖合同纠纷\n" +
                "商品房预约合同纠纷\n" +
                "商品房预售合同纠纷\n" +
                "商品房销售合同纠纷\n" +
                "商品房委托代理销售合同纠纷\n" +
                "经济适用房转让合同纠纷\n" +
                "农村房屋买卖合同纠纷\n" +
                "房屋拆迁安置补偿合同纠纷\n" +
                "供用电合同纠纷\n" +
                "供用水合同纠纷\n" +
                "供用气合同纠纷\n" +
                "供用热力合同纠纷\n" +
                "赠与合同纠纷\n" +
                "公益事业捐赠合同纠纷\n" +
                "附义务赠与合同纠纷\n" +
                "借款合同纠纷\n" +
                "金融借款合同纠纷\n" +
                "同业拆借纠纷\n" +
                "企业借贷纠纷\n" +
                "民间借贷纠纷\n" +
                "小额借款合同纠纷\n" +
                "金融不良债权转让合同纠纷\n" +
                "金融不良债权追偿纠纷\n" +
                "保证合同纠纷\n" +
                "抵押合同纠纷\n" +
                "质押合同纠纷\n" +
                "定金合同纠纷\n" +
                "进出口押汇纠纷\n" +
                "储蓄存款合同纠纷\n" +
                "银行卡纠纷\n" +
                "借记卡纠纷\n" +
                "信用卡纠纷\n" +
                "租赁合同纠纷\n" +
                "土地租赁合同纠纷\n" +
                "房屋租赁合同纠纷\n" +
                "车辆租赁合同纠纷\n" +
                "建筑设备租赁合同纠纷\n" +
                "承租人优先购买权纠纷\n" +
                "融资租赁合同纠纷\n" +
                "承揽合同纠纷\n" +
                "加工合同纠纷\n" +
                "定作合同纠纷\n" +
                "修理合同纠纷\n" +
                "复制合同纠纷\n" +
                "测试合同纠纷\n" +
                "检验合同纠纷\n" +
                "铁路机车、车辆建造合同纠纷\n" +
                "建设工程合同纠纷\n" +
                "建设工程勘察合同纠纷\n" +
                "建设工程设计合同纠纷\n" +
                "建设工程施工合同纠纷\n" +
                "建设工程价款优先受偿权纠纷\n" +
                "建设工程分包合同纠纷\n" +
                "建设工程监理合同纠纷\n" +
                "装饰装修合同纠纷\n" +
                "铁路修建合同纠纷\n" +
                "农村建房施工合同纠纷\n" +
                "运输合同纠纷\n" +
                "公路旅客运输合同纠纷\n" +
                "公路货物运输合同纠纷\n" +
                "水路旅客运输合同纠纷\n" +
                "水路货物运输合同纠纷\n" +
                "航空旅客运输合同纠纷\n" +
                "航空货物运输合同纠纷\n" +
                "出租汽车运输合同纠纷\n" +
                "管道运输合同纠纷\n" +
                "城市公交运输合同纠纷\n" +
                "联合运输合同纠纷\n" +
                "多式联运合同纠纷\n" +
                "铁路货物运输合同纠纷\n" +
                "铁路旅客运输合同纠纷\n" +
                "铁路行李运输合同纠纷\n" +
                "铁路包裹运输合同纠纷\n" +
                "国际铁路联运合同纠纷\n" +
                "保管合同纠纷\n" +
                "仓储合同纠纷\n" +
                "委托合同纠纷\n" +
                "进出口代理合同纠纷\n" +
                "民用航空运输销售代理合同纠纷\n" +
                "诉讼、仲裁、人民调解代理合同纠纷\n" +
                "委托理财合同纠纷\n" +
                "金融委托理财合同纠纷\n" +
                "民间委托理财合同纠纷\n" +
                "行纪合同纠纷\n" +
                "居间合同纠纷\n" +
                "补偿贸易纠纷\n" +
                "借用合同纠纷\n" +
                "典当纠纷\n" +
                "合伙协议纠纷\n" +
                "种植、养殖回收合同纠纷\n" +
                "彩票、奖券纠纷\n" +
                "中外合作勘探开发自然资源合同纠纷\n" +
                "农业承包合同纠纷\n" +
                "林业承包合同纠纷\n" +
                "渔业承包合同纠纷\n" +
                "牧业承包合同纠纷\n" +
                "农村土地承包合同纠纷\n" +
                "土地承包经营权转包合同纠纷\n" +
                "土地承包经营权转让合同纠纷\n" +
                "土地承包经营权互换合同纠纷\n" +
                "土地承包经营权入股合同纠纷\n" +
                "土地承包经营权抵押合同纠纷\n" +
                "土地承包经营权出租合同纠纷\n" +
                "服务合同纠纷\n" +
                "电信服务合同纠纷\n" +
                "邮寄服务合同纠纷\n" +
                "医疗服务合同纠纷\n" +
                "法律服务合同纠纷\n" +
                "旅游合同纠纷\n" +
                "房地产咨询合同纠纷\n" +
                "房地产价格评估合同纠纷\n" +
                "旅店服务合同纠纷\n" +
                "财会服务合同纠纷\n" +
                "餐饮服务合同纠纷\n" +
                "娱乐服务合同纠纷\n" +
                "有线电视服务合同纠纷\n" +
                "网络服务合同纠纷\n" +
                "教育培训合同纠纷\n" +
                "物业服务合同纠纷\n" +
                "家政服务合同纠纷\n" +
                "庆典服务合同纠纷\n" +
                "殡葬服务合同纠纷\n" +
                "农业技术服务合同纠纷\n" +
                "农机作业服务合同纠纷\n" +
                "保安服务合同纠纷\n" +
                "银行结算合同纠纷\n" +
                "演出合同纠纷\n" +
                "劳务合同纠纷\n" +
                "离退休人员返聘合同纠纷\n" +
                "广告合同纠纷\n" +
                "展览合同纠纷\n" +
                "追偿权纠纷";
        for (String str : REASON_STR.split("\n")) {
            REASON_SET.add(str);
        }
        System.out.println(REASON_SET.size());
    }
}

@Data
@AllArgsConstructor
@NoArgsConstructor
class DebtCaseRoleInfo {
    private List<NameAndKeyNoEntity> prosecutor;
    private List<NameAndKeyNoEntity> defendant;
}

@Data
@AllArgsConstructor
class DebtType {
    private String keyNo;
    private String type;
    private String money;
    private String sourceId;
    private String caseId;
    private String proInfo;
    private Long earliestDate;
    private Long lastestDate;
    private Long ktggLastestDate;
    private String caseReason;
}
