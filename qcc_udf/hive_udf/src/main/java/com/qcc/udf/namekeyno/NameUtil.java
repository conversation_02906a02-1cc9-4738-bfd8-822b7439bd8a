package com.qcc.udf.namekeyno;

import com.google.common.base.Strings;

public class NameUtil {
    public static int getOrgByKeyNo(String keyNo) {
        if (Strings.isNullOrEmpty(keyNo)) {
            return PromoterTypeEnum.COMPANYWITHOUTKEYNO.getType();
        } else {
            String prefix = keyNo.substring(0, 1);
            byte var5 = -1;
            switch(prefix.hashCode()) {
                case 103:
                    if (prefix.equals("g")) {
                        var5 = 3;
                    }
                    break;
                case 104:
                    if (prefix.equals("h")) {
                        var5 = 1;
                    }
                case 105:
                case 107:
                case 108:
                case 109:
                case 110:
                case 113:
                case 114:
                case 117:
                case 118:
                default:
                    break;
                case 106:
                    if (prefix.equals("j")) {
                        var5 = 6;
                    }
                    break;
                case 111:
                    if (prefix.equals("o")) {
                        var5 = 8;
                    }
                    break;
                case 112:
                    if (prefix.equals("p")) {
                        var5 = 10;
                    }
                    break;
                case 115:
                    if (prefix.equals("s")) {
                        var5 = 0;
                    }
                    break;
                case 116:
                    if (prefix.equals("t")) {
                        var5 = 2;
                    }
                    break;
                case 119:
                    if (prefix.equals("w")) {
                        var5 = 5;
                    }
                    break;
                case 120:
                    if (prefix.equals("x")) {
                        var5 = 4;
                    }
                    break;
                case 121:
                    if (prefix.equals("y")) {
                        var5 = 7;
                    }
                    break;
                case 122:
                    if (prefix.equals("z")) {
                        var5 = 9;
                    }
            }

            PromoterTypeEnum type;
            switch(var5) {
                case 0:
                    type = PromoterTypeEnum.ORG;
                    break;
                case 1:
                    type = PromoterTypeEnum.HKCOMPANY;
                    break;
                case 2:
                    type = PromoterTypeEnum.TWCOMPANY;
                    break;
                case 3:
                case 4:
                case 5:
                case 6:
                    type = PromoterTypeEnum.GOVERNMENT;
                    break;
                case 7:
                    type = PromoterTypeEnum.HOSPITAL;
                    break;
                case 8:
                    type = PromoterTypeEnum.OVERSEA;
                    break;
                case 9:
                    type = PromoterTypeEnum.OVERSEA2;
                    break;
                case 10:
                    type = PromoterTypeEnum.EMPLOYEE;
                    break;
                default:
                    type = PromoterTypeEnum.COMPANY;
            }

            return type.getType();
        }
    }
}
