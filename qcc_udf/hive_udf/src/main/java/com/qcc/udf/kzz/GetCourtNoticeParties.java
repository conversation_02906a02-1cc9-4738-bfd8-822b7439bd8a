package com.qcc.udf.kzz;

import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 提取开庭公告当事人,(除原告被告第三方外无法识别的人)
 * <AUTHOR>
 * @date 2022/4/21
 */
public class GetCourtNoticeParties extends UDF {

    /**
     *
     * @param allParties 所有的人
     * @param prosecutor 原告
     * @param defendant 被告
     * @param third 第三方
     * @return
     */
    public static String evaluate(String allParties, String prosecutor, String defendant,String third) {
        String result="";
        try{
            if(StringUtils.isNotBlank(allParties)){
                List<Parties> allPartyList = JSON.parseArray(allParties,Parties.class);
                List<Parties>  partyList= new ArrayList<>();
                if(StringUtils.isNotBlank(prosecutor)){
                    List<Parties> prosecutorList = JSON.parseArray(prosecutor,Parties.class);
                    if(CollectionUtils.isNotEmpty(prosecutorList)){
                        partyList.addAll(prosecutorList);
                    }
                }
                if(StringUtils.isNotBlank(defendant)){
                    List<Parties> defendantList = JSON.parseArray(defendant,Parties.class);
                    if(CollectionUtils.isNotEmpty(defendantList)){
                        partyList.addAll(defendantList);
                    }
                }
                if(StringUtils.isNotBlank(third)){
                    List<Parties> thirdList = JSON.parseArray(third,Parties.class);
                    if(CollectionUtils.isNotEmpty(thirdList)){
                        partyList.addAll(thirdList);
                    }
                }
                List<String> partyKeyNoList =partyList.stream()
                        .filter(item->null!=item&&StringUtils.isNotBlank(item.getKeyNo())&&!item.getKeyNo().startsWith("p"))
                        .map(Parties::getKeyNo).collect(Collectors.toList());

                List<String> otherKeyNos = allPartyList.stream()
                        .filter(item->null!=item&&StringUtils.isNotBlank(item.getKeyNo())&&!item.getKeyNo().startsWith("p"))
                        .filter(item->!partyKeyNoList.contains(item.getKeyNo())).map(Parties::getKeyNo).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(otherKeyNos)){
                    result = otherKeyNos.stream().distinct().collect(Collectors.joining(","));
                }
            }

        }catch (Exception e){
            e.printStackTrace();

        }
        return result;
    }

    private static class Parties{
        private String keyNo;

        public String getKeyNo() {
            return keyNo;
        }

        public void setKeyNo(String keyNo) {
            this.keyNo = keyNo;
        }
    }

//    public static void main(String[] args) {
//        String content = "[\n" +
//                "  {\n" +
//                "    \"KeyNo\": \"931468fa89ed52729cdd79d345bdc3e9\",\n" +
//                "    \"Name\": \"汉唐智业（北京）国际教育科技股份有限公司\",\n" +
//                "    \"Org\": 0,\n" +
//                "    \"ShowName\": \"汉唐智业（北京）国际教育科技股份有限公司\"\n" +
//                "  },\n" +
//                "  {\n" +
//                "    \"KeyNo\": \"\",\n" +
//                "    \"Name\": \"段肯\",\n" +
//                "    \"Org\": -1,\n" +
//                "    \"ShowName\": \"段*\"\n" +
//                "  },\n" +
//                "  {\n" +
//                "    \"KeyNo\": \"e600a72d3f152230e756f7c658779651\",\n" +
//                "    \"Name\": \"北京中安汉唐国际教育科技有限公司\",\n" +
//                "    \"Org\": 0,\n" +
//                "    \"ShowName\": \"北京中安汉唐国际教育科技有限公司\"\n" +
//                "  },\n" +
//                "  {\n" +
//                "    \"KeyNo\": \"\",\n" +
//                "    \"Name\": \"段爱群\",\n" +
//                "    \"Org\": -1,\n" +
//                "    \"ShowName\": \"段**\"\n" +
//                "  },\n" +
//                "  {\n" +
//                "    \"KeyNo\": \"\",\n" +
//                "    \"Name\": \"李霞\",\n" +
//                "    \"Org\": -1,\n" +
//                "    \"ShowName\": \"李*\"\n" +
//                "  },\n" +
//                "  {\n" +
//                "    \"KeyNo\": \"9352f51d37a61015493eae28ddd27ad0\",\n" +
//                "    \"Name\": \"中安亚太控股有限公司\",\n" +
//                "    \"Org\": 0,\n" +
//                "    \"ShowName\": \"中安亚太控股有限公司\"\n" +
//                "  }\n" +
//                "]";
//        String prosecutor ="[{\n" +
//                "    \"KeyNo\": \"e600a72d3f152230e756f7c658779651\",\n" +
//                "    \"Name\": \"北京中安汉唐国际教育科技有限公司\",\n" +
//                "    \"Org\": 0,\n" +
//                "    \"ShowName\": \"北京中安汉唐国际教育科技有限公司\"\n" +
//                "  }]";
//        String defendant="[{\n" +
//                "    \"KeyNo\": \"931468fa89ed52729cdd79d345bdc3e9\",\n" +
//                "    \"Name\": \"汉唐智业（北京）国际教育科技股份有限公司\",\n" +
//                "    \"Org\": 0,\n" +
//                "    \"ShowName\": \"汉唐智业（北京）国际教育科技股份有限公司\"\n" +
//                "  }]";
//        String third = "[{\n" +
//                "    \"KeyNo\": \"9352f51d37a61015493eae28ddd27ad0\",\n" +
//                "    \"Name\": \"中安亚太控股有限公司\",\n" +
//                "    \"Org\": 0,\n" +
//                "    \"ShowName\": \"中安亚太控股有限公司\"\n" +
//                "  }]";
//        String region = evaluate(content,prosecutor,defendant,third);
//        System.out.println(region);
//    }

}
