package com.qcc.udf.court_notice;

import com.qcc.udf.court_notice.anUtils.MD5Util;
import com.qcc.udf.court_notice.anUtils.Util;
import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentLengthException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDTF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
* 导出到雷达监控
**/
public class Courtnotice_ForRadarUDTF extends GenericUDTF {

    String[] keynames = new String[]{"id","liandate","casereason","isvalid","prosecutorlist","defendantlist","created_date","updated_date","changestatus"};

    String[] valnames = new String[]{"Id","KeyNo","CompanyName","Category","ChangeContent","ChangeExtend","ObjectId","ChangeStatus","ChangeDate","DataStatus","CreateDate"};
    String[] valtypes = new String[]{"string","string","string","int","string","string","string","int","string","int","string"};
    Map<String, Integer> keyname_index = new HashMap<>();
    Map<String, Integer> valname_index = new HashMap<>();

    @Override
    public void close() throws HiveException {
        // TODO Auto-generated method stub

    }

    @Override
    public void process(Object[] args) throws HiveException {
        //初始化
        keyname_index.clear();
        valname_index.clear();
        Object[] Keys = new Object[keynames.length];
        Object[] Vals = new Object[valnames.length];
        for (int i = 0; i < keynames.length; i++) {
            keyname_index.put(keynames[i], i);
            Keys[i]=args[i];
        }
        for (int i = 0; i < valnames.length; i++) {
            valname_index.put(valnames[i], i);
            Vals[i] = null;
        }

        Vals[valname_index.get("Category")] = 18;
        Vals[valname_index.get("ChangeContent")] = args[keyname_index.get("casereason")];
        Vals[valname_index.get("ObjectId")] = args[keyname_index.get("id")];
        Vals[valname_index.get("ChangeStatus")] = Integer.parseInt(args[keyname_index.get("changestatus")].toString());
        Vals[valname_index.get("ChangeDate")] = args[keyname_index.get("updated_date")];
        Vals[valname_index.get("DataStatus")] = args[keyname_index.get("isvalid")].toString() =="true" ? 1: 0;
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        Vals[valname_index.get("CreateDate")] = sdf.format(new Date());

        if (Util.notEmpty(args[keyname_index.get("prosecutorlist")])) {
            String[] p = args[keyname_index.get("prosecutorlist")].toString().split("\\{\"keyno\":\"");
            for (int i = 1; i < p.length; i++) {
                if (p[i].split("\",\"name")[0].length() == 32) {
                    Vals[valname_index.get("KeyNo")] = p[i].split("\",\"name\":\"")[0];
                    Vals[valname_index.get("CompanyName")] = p[i].split("\",\"name\":\"")[1].replace("\"}", "")
                            .replace(",", "").replace("]", "");
                    Vals[valname_index.get("ChangeExtend")] = "0";
                    Vals[valname_index.get("Id")]= MD5Util.ecodeByMD5(Vals[valname_index.get("KeyNo")].toString()+
                            Vals[valname_index.get("CompanyName")].toString()+
                            Vals[valname_index.get("Category")].toString() +
                            Vals[valname_index.get("ObjectId")].toString()+
                            Vals[valname_index.get("ChangeContent")].toString()+
                            Vals[valname_index.get("ChangeExtend")].toString()+
                            Vals[valname_index.get("ChangeStatus")].toString());
                    forward(Vals);
                }
            }
        }
        if (Util.notEmpty(args[keyname_index.get("defendantlist")])) {
            String[] p = args[keyname_index.get("defendantlist")].toString().split("\\{\"keyno\":\"");
            for (int i = 1; i < p.length; i++) {
                if (p[i].split("\",\"name")[0].length() == 32) {
                    Vals[valname_index.get("KeyNo")] = p[i].split("\",\"name\":\"")[0];
                    Vals[valname_index.get("CompanyName")] = p[i].split("\",\"name\":\"")[1].replace("\"}", "")
                            .replace(",", "").replace("]", "");
                    Vals[valname_index.get("ChangeExtend")] = "1";
                    Vals[valname_index.get("Id")]= MD5Util.ecodeByMD5(Vals[valname_index.get("KeyNo")].toString()+
                            Vals[valname_index.get("CompanyName")].toString()+
                            Vals[valname_index.get("Category")].toString() +
                            Vals[valname_index.get("ObjectId")].toString()+
                            Vals[valname_index.get("ChangeContent")].toString()+
                            Vals[valname_index.get("ChangeExtend")].toString()+
                            Vals[valname_index.get("ChangeStatus")].toString());
                    forward(Vals);
                }
            }
        }


    }

    @Override
    public StructObjectInspector initialize(ObjectInspector[] args)
            throws UDFArgumentException {
        if (args.length != 9) {
            throw new UDFArgumentLengthException(
                    "this UDTF takes only 9 argument");
        }
        ArrayList<String> fieldNames = new ArrayList<String>();
        ArrayList<ObjectInspector> fieldOIs = new ArrayList<ObjectInspector>();

        if(valnames.length!=valtypes.length){
            throw new UDFArgumentLengthException(
                    "this UDTF output columns and types not match!while columns num is "+valnames.length+"and types num is "+valtypes.length);
        }

        for (int i = 0; i < valnames.length; i++) {
            switch (valtypes[i].toLowerCase()) {
                case "string":
                    fieldNames.add(valnames[i]);
                    fieldOIs.add(PrimitiveObjectInspectorFactory.javaStringObjectInspector);
                    break;
                case "int":
                    fieldNames.add(valnames[i]);
                    fieldOIs.add(PrimitiveObjectInspectorFactory.javaIntObjectInspector);
                    break;
                case "boolean":
                    fieldNames.add(valnames[i]);
                    fieldOIs.add(PrimitiveObjectInspectorFactory.javaBooleanObjectInspector);
                    break;
                case "float":
                    fieldNames.add(valnames[i]);
                    fieldOIs.add(PrimitiveObjectInspectorFactory.javaFloatObjectInspector);
                    break;
                case "double":
                    fieldNames.add(valnames[i]);
                    fieldOIs.add(PrimitiveObjectInspectorFactory.javaDoubleObjectInspector);
                    break;
                    default:throw new UDFArgumentLengthException(
                            "Column type:"+valtypes[i].toLowerCase()+" does not exists!");

            }
        }
        return ObjectInspectorFactory.getStandardStructObjectInspector(
                fieldNames, fieldOIs);

    }

}


