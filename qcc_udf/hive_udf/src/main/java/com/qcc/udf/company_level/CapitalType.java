package com.qcc.udf.company_level;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 获取公司的资本类型
 */
public class CapitalType extends UDF {

    /**
     * @param keyNo
     * @param companyName
     * @param arrayStr
     * @return
     * @throws Exception
     */

    public static String evaluate(String keyNo, String companyName, String arrayStr) throws Exception {
        String capitalType = "其他";
        if (StringUtils.isBlank(keyNo)) keyNo = "";
        if (StringUtils.isBlank(companyName)) companyName = "";
        if (StringUtils.isBlank(arrayStr)) arrayStr = "";
        if (Pattern.compile("外商|外国|外资").matcher(companyName).find()) capitalType = "外商资本";
        else if (!Pattern.compile("公司|合作社|有限合伙").matcher(companyName).find()) {
            if (Pattern.compile("厅|局|国有资|政府|委员会|管委会|国资委|共和国|国务院|财政|办事处").matcher(companyName).find()) {
                capitalType = "国有资本";
            } else if (Pattern.compile("村|居委会|镇").matcher(companyName).find()) {
                capitalType = "集体资本";
            }
        } else {
            Map<Integer, String> capitalArrar = new HashMap<>();
            int maxCount = 0;
            for (String str : arrayStr.split("\\|")) {
                String[] typeArray = str.split("-");
                Integer count = ParseToInt(typeArray[1]);
                capitalArrar.put(count, typeArray[0]);
                if (count > maxCount) maxCount = count;
            }
            List<String> capitalList = new ArrayList<>();
            for (int key : capitalArrar.keySet()) {
                if (key == maxCount) capitalList.add(CapitalStockType(keyNo, capitalArrar.get(key)));
            }
            capitalType = GetCapitalSort(capitalList);
        }
        return capitalType;
    }

    /**
     * 根据优先级获取资本类型
     *
     * @param capitalList
     * @return 资本类型
     * @throws Exception
     */
    private static String GetCapitalSort(List<String> capitalList) throws Exception {
        String capitalType = "";
        //初始化资本类型优先级
        Map<String, Integer> CapitalInit = new HashMap<String, Integer>() {{
            put("国有资本", 1);
            put("集体资本", 2);
            put("私有资本", 3);
            put("港澳台资本", 4);
            put("外商资本", 5);
            put("其他", 6);
        }};
        int cMax = 0;
        for (String s : capitalList) {
            if (CapitalInit.get(s) > cMax) {
                cMax = CapitalInit.get(s);
                capitalType = s;
            }
        }
        return capitalType;
    }

    /**
     * @param pKeyNo
     * @param stockType
     * @return
     * @throws Exception
     */
    private static String CapitalStockType(String pKeyNo, String stockType) throws Exception {
        if (StringUtils.isBlank(pKeyNo)) pKeyNo = "";
        if (StringUtils.isBlank(stockType)) stockType = "";
        if ((pKeyNo.length() == 32 && ("h".equals(pKeyNo.substring(0, 1)) || "t".equals(pKeyNo.substring(0, 1)))) || stockType.contains("港澳"))
            return "港澳台资本";
        if (Pattern.compile("外商|外国|外资").matcher(stockType).find())
            return "外商资本";
        if (stockType.contains("集体"))
            return "集体资本";
        if (Pattern.compile("国有|机关|事业").matcher(stockType).find())
            return "国有资本";
        if ((pKeyNo.length() == 32 && "p".equals(pKeyNo.substring(0, 1))) || Pattern.compile("自然人|法人股东|个人").matcher(stockType).find())
            return "私有资本";
        return "其他";
    }

    /**
     * string转int
     *
     * @param str
     * @return
     */
    private static int ParseToInt(String str) {
        try {
            return Integer.parseInt(str);
        } catch (Exception e) {
            return 0;
        }
    }
}
