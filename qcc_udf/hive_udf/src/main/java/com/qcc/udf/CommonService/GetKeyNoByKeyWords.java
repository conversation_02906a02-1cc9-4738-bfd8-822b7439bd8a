package com.qcc.udf.CommonService;

import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

public class GetKeyNoByKeyWords extends UDF {

//    public static void main(String[] args) {
//        System.out.println(evaluate("深圳稳盈进取股权投资企业（有限合伙）,983c23c5bdfae68fbee9f53cb99e6017,深圳市前海稳盈资产管理有限公司,深圳稳盈添宝股权投资企业(有限合伙),e26a91badff5bdf8a34920af237e1b88,574df38426d3c65ff12b5a37ffe578d0,深圳稳盈添宝股权投资企业（有限合伙）,深圳稳盈进取股权投资企业(有限合伙)"));
//    }

    public static String evaluate(String str) {
        if (StringUtils.isBlank(str)) return "";
        String[] strArray = str.split(",");
        String result = "";
        for (String s : strArray) {
            if (s.matches("^[0-9a-z][a-f0-9]{31}$")) {
                result += s + ",";
            }
        }
        if (result.length() > 0) result = result.substring(0, result.length() - 1);
        return result;
    }
}
