package com.qcc.udf.risk_analysis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;

public class GetHistoryLianListV2 extends UDF {

    public String evaluate(String keyNo, int type, List<String> infoList) {
        JSONArray array = new JSONArray();
        if (infoList != null && infoList.size() > 0){
            Map<String, JSONObject> infoMap = new LinkedHashMap<>();
            for (String str : infoList) {
                JSONObject json = JSONObject.parseObject(str);
                json.put("subkeyno", json.getJSONObject("nameandkeyno").getString("KeyNo"));
                json.put("subname", json.getJSONObject("nameandkeyno").getString("Name"));

                String key = "";
                if (StringUtils.isNotEmpty(json.getString("subkeyno"))) {
                    key = json.getString("subkeyno");
                } else if (StringUtils.isNotEmpty(json.getString("subname"))) {
                    key = RiskAnalysisUtil.full2Half(json.getString("subname"));
                }

                if (StringUtils.isNotEmpty(key)) {
                    if (infoMap.containsKey(key)) {
                        JSONObject item = infoMap.get(key);
                        item.put("KeyNo", keyNo);
                        item.put("NameKeyNo", json.getJSONObject("nameandkeyno"));
                        item.put("Cnt", item.getInteger("Cnt") + json.getInteger("cnt"));
                        JSONArray array1 = item.getJSONArray("AnnoArr");
                        JSONArray array2 = json.getJSONArray("info");
                        if (array2 != null && array2.size() > 0){
                            Iterator<Object> it = array2.iterator();
                            while (it.hasNext()){
                                JSONObject jsonObject = (JSONObject)it.next();
                                array1.add(jsonObject);
                            }
                        }
                        item.put("AnnoArr", array1);
                        item.put("SortDate", item.getInteger("SortDate") > json.getInteger("sortdate") ? item.getInteger("SortDate") : json.getInteger("sortdate"));
                        item.put("OriginId", item.getString("OriginId").concat(",").concat(json.getString("originid")));
                        item.put("Type", json.getString("type"));
                        item.put("RoleDetails", item.getString("RoleDetails").concat(",").concat(json.getString("roledetails")));

                        infoMap.put(key, item);
                    } else {
                        JSONObject item = new JSONObject();
                        item.put("KeyNo", keyNo);
                        item.put("NameKeyNo", json.getJSONObject("nameandkeyno"));
                        item.put("Cnt", json.getInteger("cnt"));
                        item.put("AnnoArr", json.getJSONArray("info"));
                        item.put("SortDate", json.getInteger("sortdate"));
                        item.put("OriginId", json.getString("originid"));
                        item.put("Type", json.getString("type"));
                        item.put("RoleDetails", json.getString("roledetails"));

                        infoMap.put(key, item);
                    }
                }
            }
            Set<String> sqrSet = infoMap.keySet();
            for (String str : sqrSet){
                JSONObject jsonObject = infoMap.get(str);
                jsonObject.put("Id", RiskAnalysisUtil.ecodeByMD5(keyNo.concat(String.valueOf(type)).concat(str).concat("_his")));
                array.add(jsonObject);
            }
        }


        return array.toString();
    }

    public static void main(String[] args) {
        GetHistoryLianListV2 aa = new GetHistoryLianListV2();
        List<String> infoList = JSON.parseArray("[\"{\\\"id\\\":\\\"650ddec33a4ff19169cf05ed6759d93c\\\",\\\"keyno\\\":\\\"00c06129acf4459a62d049634f4d4112\\\",\\\"nameandkeyno\\\":\\\"{\\\\\\\"KeyNo\\\\\\\":\\\\\\\"c3c0f682dc9a19c2eb20422d1ffe26e4\\\\\\\",\\\\\\\"Org\\\\\\\":0,\\\\\\\"Name\\\\\\\":\\\\\\\"江苏创能电器有限公司\\\\\\\"}\\\",\\\"cnt\\\":2,\\\"info\\\":\\\"[{\\\\\\\"A\\\\\\\":\\\\\\\"（2020）苏0902执保1278号\\\\\\\",\\\\\\\"B\\\\\\\":1607356800},{\\\\\\\"A\\\\\\\":\\\\\\\"（2020）苏0902民初5908号\\\\\\\",\\\\\\\"B\\\\\\\":1607270400}]\\\",\\\"sortdate\\\":\\\"1607356800\\\",\\\"originid\\\":\\\"f0512ab828ed1a48fe8072b844ba589a,937e31192bfd08e9c8211ad24f8fba49\\\",\\\"type\\\":1,\\\"roledetails\\\":\\\"一审原告,申请保全人\\\"}\"]", String.class);
        System.out.println(aa.evaluate("00c06129acf4459a62d049634f4d4112",1, infoList));

    }
}
