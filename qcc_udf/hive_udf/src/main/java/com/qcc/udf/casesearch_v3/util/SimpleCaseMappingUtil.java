package com.qcc.udf.casesearch_v3.util;

import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import com.qcc.udf.casesearch_v3.enums.RoleTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.mortbay.util.ajax.JSON;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * @Auther: zhangqiang
 * @Date: 2021/01/06 14:54
 * @Description:纯案号原被告映射
 */
public class SimpleCaseMappingUtil {
    static   Map<String, RoleInfo> roleMap = new HashMap<>();
    static Set<String> DEF_SET = Sets.newHashSet("被告","被上诉人");
    static {
        roleMap.put("二审上诉人", new RoleInfo("上诉人", ""));
        roleMap.put("复议申请人(原申请执行人)", new RoleInfo("", "申请执行人"));
        roleMap.put("上诉人(一审被告)", new RoleInfo("", "被告"));
        roleMap.put("上诉人(一审原告)", new RoleInfo("", "原告"));
        roleMap.put("上诉人(原审被告)", new RoleInfo("", "被告"));
        roleMap.put("上诉人(原审被告反诉原告)", new RoleInfo("", "被告"));
        roleMap.put("上诉人(原审第三人)", new RoleInfo("", "第三人"));
        roleMap.put("上诉人(原审原告)", new RoleInfo("", "原告"));
        roleMap.put("申请再审人(一审原告二审上诉人)", new RoleInfo("上诉人", "原告"));
        roleMap.put("再审申请人(一审被告、二审上诉人)", new RoleInfo("上诉人", "被告"));
        roleMap.put("再审申请人(一审被告二审上诉人)", new RoleInfo("上诉人", "被告"));
        roleMap.put("再审申请人(一审原告二审上诉人)", new RoleInfo("上诉人", "原告"));
        roleMap.put("再审申请人(原审被告)", new RoleInfo("", "被告"));
        roleMap.put("再审申请人(原审原告)", new RoleInfo("", "原告"));
        roleMap.put("被上诉人(一审被告)", new RoleInfo("", "被告"));
        roleMap.put("被上诉人(一审原告)", new RoleInfo("", "原告"));
        roleMap.put("被上诉人(原审被告)", new RoleInfo("", "被告"));
        roleMap.put("被上诉人(原审第三人)", new RoleInfo("", "第三人"));
        roleMap.put("被上诉人(原审原告)", new RoleInfo("", "原告"));
        roleMap.put("被上诉人(原审原告反诉被告)", new RoleInfo("", "原告"));
        roleMap.put("被申请人(一审被告二审被上诉人)", new RoleInfo("被上诉人", "被告"));
        roleMap.put("被申请人(一审原告、二审被上诉人)", new RoleInfo("被上诉人", "原告"));
        roleMap.put("被申请人(原被执行人)", new RoleInfo("", "被执行人"));
        roleMap.put("被申请人(原审被告)", new RoleInfo("", "被告"));
        roleMap.put("被申请人(原审原告)", new RoleInfo("", "原告"));
        roleMap.put("一审第三人", new RoleInfo("", "第三人"));
        roleMap.put("原审被告", new RoleInfo("", "被告"));
        roleMap.put("原审被告单位", new RoleInfo("", "被告"));
    }

    /**
     *
     * @param cpwsRole
     * @param trialRound
     * @return
     */
    public static RoleType getRole(String cpwsRole,String trialRound){
        if(Strings.isNullOrEmpty(cpwsRole) || Strings.isNullOrEmpty(trialRound)){
            return null;
        }


        RoleInfo roleInfo = roleMap.get(CommonV3Util.full2Half(cpwsRole));
        if(roleInfo == null){
            return null;
        }

        String roleName = "";
        if(trialRound.contains("一审")){
            roleName = roleInfo.getFirstTrial();
        }
        if(trialRound.contains("二审")){
            roleName = roleInfo.getSecondTrial();
        }
        if(Strings.isNullOrEmpty(roleName)){
            return null;
        }

        RoleType roleType = new RoleType();
        roleType.setRoleName(roleName);
        if(DEF_SET.contains(roleName)){
            roleType.setRoleType(RoleTypeEnum.DEF);
        }else{
            roleType.setRoleType(RoleTypeEnum.PRO);
        }

        return roleType;
    }
    public static void main(String[] args) {
        String role = "被申请人(原审原告)";
        String trialRound = "民事一审";

        System.out.println(JSON.toString(getRole(role,trialRound)));
    }
}

@Data
@AllArgsConstructor
@NoArgsConstructor
class RoleInfo {
    /**
     * 二理
     */
    private String secondTrial;
    /**
     * 一审
     */
    private String firstTrial;
}

@Data
class RoleType{
    private RoleTypeEnum roleType;
    private String roleName;
}