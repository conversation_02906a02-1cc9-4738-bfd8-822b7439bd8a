package com.qcc.udf.xg;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class gerReasonFromContent2 extends UDF {

    public static String evaluate(String content){
        String result = "";

        if (StringUtils.isNotEmpty(content)){
            Pattern pattern = Pattern.compile("(申请执行你)(单位)?([\\u4e00-\\u9fa5、])+(一案)");
            Matcher matcher = pattern.matcher(content);
            if (matcher.find()){
                result = matcher.group().replace("申请执行你", "").replace("单位", "").replace("一案", "");
            }
        }

        return result;
    }

    public static void main(String[] args) {
        System.out.println(evaluate("永诚财产保险股份有限公司江西分公司</a>申请执行你与公司、证券、保险、票据有关案件一案，因你未按执行通知书指定的期间履行生效法律文书确定的给付义务，本院依照《中华人民共和国民事诉讼法》第二百五十五条和《最高人民法院关于限制被执行人高消费及有关消费的若干规定》第一条、第三条的规定，对你采取限制消费措施，限制你实施以下高消第二百五十五条和《最高人民法院关于限制被执行人高消费及有关消费的若干规定》第一条"));
    }
}
