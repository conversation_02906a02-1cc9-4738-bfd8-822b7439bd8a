package com.qcc.udf.cpws.casereason;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.io.*;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 匹配Excel中的code，读取标签
 *
 * <AUTHOR>
 */
public class MatchCsvCode extends UDF {

    private static final ThreadLocal<Map<String, List<ExcelObj>>> LOCAL_VARS = new ThreadLocal<>();

    public String evaluate(String code) {
        JSONArray array = new JSONArray();
        Map<String, List<ExcelObj>> collect = LOCAL_VARS.get();
        List<ExcelObj> listRisk = null;

        if (collect != null && collect.size() > 0) {
            listRisk = collect.get(code);
        } else {
            URL resource = MatchCsvCode.class.getResource("/case_reason_sort.csv");
            String filePath = resource.getPath();

            File csv = new File(filePath);
            csv.setReadable(true);
            csv.setWritable(true);
            InputStreamReader isr = null;
            BufferedReader br = null;
            try {
                isr = new InputStreamReader(new FileInputStream(csv), "UTF-8");
                br = new BufferedReader(isr);
            } catch (Exception e) {
                e.printStackTrace();
            }
            String line = "";
            List<ExcelObj> list = new ArrayList<>();
            String historyFirst = "", historySecond = "";
            try {
                while ((line = br.readLine()) != null) {
                    if (line.indexOf('"') < 0) {
                        continue;
                    }
                    // System.out.println(line);
                    String[] firstAndSecond = line.substring(0, line.indexOf('"')).split(",");
                    String first = firstAndSecond.length > 0 ? firstAndSecond[0] : "";
                    String second = firstAndSecond.length > 1 ? firstAndSecond[1] : "";
                    if (StringUtils.isEmpty(first)) {
                        first = historyFirst;
                    } else {
                        historyFirst = first;
                    }
                    if ("-".equals(second)) {
                        historySecond = "";
                        second = "";
                    } else if (StringUtils.isEmpty(second)) {
                        second = historySecond;
                    } else {
                        historySecond = second;
                    }
                    ExcelObj obj = new ExcelObj();
                    obj.setFirstLabel(first);
                    obj.setSecondLabel(second);
                    String substring = line.substring(line.indexOf('"')).replaceAll("\"", "");
                    obj.setId(substring);
                    list.add(obj);
                }
                // System.out.println(JSON.toJSONString(list));
                collect = list.stream().collect(Collectors.groupingBy(it -> it.getId()));
                // System.out.println(JSON.toJSONString(collect));
                LOCAL_VARS.set(collect);

                listRisk = collect.get(code);

            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
                try {
                    isr.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        if (listRisk != null && !listRisk.isEmpty()) {
            for (ExcelObj item : listRisk) {
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(item));
                array.add(jsonObject);
            }
        }

        return array.toString();
    }

    @Data
    public static class ExcelObj {
        // 第一级标签名
        private String firstLabel;
        // 第二级标签名
        private String secondLabel;
        // 案由code
        private String id;
        // 页脚名称
        private String sheetName;
    }

    public static void main(String[] args) {
        String code = "A,A03,A0303,A030306";
        MatchCsvCode matchCsvCode = new MatchCsvCode();
        System.out.println(matchCsvCode.evaluate(code));
    }
}
