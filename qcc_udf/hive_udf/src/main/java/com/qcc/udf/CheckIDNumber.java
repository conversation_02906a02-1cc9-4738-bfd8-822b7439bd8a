package com.qcc.udf;

import org.apache.hadoop.hive.ql.exec.UDF;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Auther: wangb
 * @Date: 2022/1/12 16:24
 * 校验身份证号
 * 身份证格式强校验规则：
 * 1、号码的结构 公民身份号码是特征组合码，由十七位数字本体码和一位校验码组成。排列顺序从左至右依次为：六位数字地址码，八位数字出生日期码，三位数字顺序码和一位数字校验码。
 * 2、地址码(前六位数）表示编码对象常住户口所在县(市、旗、区)的行政区划代码，按GB/T2260的规定执行。具体参照：http://www.mca.gov.cn/article/sj/xzqh/2019/
 * 3、出生日期码（第七位至十四位）表示编码对象出生的年、月、日，按GB/T7408的规定执行，年、月、日代码之间不用分隔符。
 * 4、顺序码（第十五位至十七位）表示在同一地址码所标识的区域范围内，对同年、同月、同日出生的人编定的顺序号，顺序码的奇数分配给男性，偶数分配给女性。
 * 5、校验码（第十八位数）
 *（1）十七位数字本体码加权求和公式 S = Sum(Ai * Wi), i = 0, ... , 16 ，先对前17位数字的权求和
 * Ai:表示第i位置上的身份证号码数字值 Wi:表示第i位置上的加权因子 Wi: 7 9 10 5 8 4 2 1 6 3 7 9 10 5 8 4
 *（2）计算模 Y = mod(S, 11) （3）通过模得到对应的校验码 Y: 0 1 2 3 4 5 6 7 8 9 10 校验码: 1 0 X 9 8 7 6 5 4 3 2
 */

public class CheckIDNumber  extends UDF{

    //数字正则
    private static final String regexNum = "^[0-9]*$";
    //闰年生日正则
    private static final String regexBirthdayInLeapYear = "^((19[0-9]{2})|(2[0-9]{3}))((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))$";
    //平年生日正则
    private static final String regexBirthdayInCommonYear = "^((19[0-9]{2})|(2[0-9]{3}))((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|1[0-9]|2[0-8]))$";

    // 香港地域编码值
    private static final int HONGKONG_AREACODE = 810000;
    // 台湾地域编码值
    private static final int TAIWAN_AREACODE = 710000;
    // 澳门地域编码值
    private static final int MACAO_AREACODE = 820000;

    // 其他省份编码值
    private static final HashSet<Integer> provincesCode = new HashSet<Integer>(32);
    static {
        /*
            这里初始化的地区编码仅仅是省份编码，
            是因为经过核对GB/T2260-1999中的完整地区编码很多与身份证号的地区编码不符，
            因此没有比对完整的地区编码
        */
        provincesCode.add(11);
        provincesCode.add(12);
        provincesCode.add(13);
        provincesCode.add(14);
        provincesCode.add(15);
        provincesCode.add(21);
        provincesCode.add(22);
        provincesCode.add(23);
        provincesCode.add(31);
        provincesCode.add(32);
        provincesCode.add(33);
        provincesCode.add(34);
        provincesCode.add(35);
        provincesCode.add(36);
        provincesCode.add(37);
        provincesCode.add(41);
        provincesCode.add(42);
        provincesCode.add(43);
        provincesCode.add(44);
        provincesCode.add(45);
        provincesCode.add(46);
        provincesCode.add(50);
        provincesCode.add(51);
        provincesCode.add(52);
        provincesCode.add(53);
        provincesCode.add(54);
        provincesCode.add(61);
        provincesCode.add(62);
        provincesCode.add(63);
        provincesCode.add(64);
        provincesCode.add(65);
    }

    public static boolean evaluate(String idNumber) {
        // 去空格,并将字母修改为大写
        idNumber = idNumber.trim().toUpperCase();

        //只针对两种类型字符进行身份证校验：1、字符串全是数字 2、最后一位是X的数字字符(仅仅最后一位是X，其他位置不能有X)
        if (isDigit(idNumber) || idNumber.substring(idNumber.length()-1).equals("X") && (idNumber.length()-idNumber.replace("X","").length() != 1) ) {

            // 身份证正则校验
            if (!checkIdNumberRegex(idNumber)) {
                return false;
            }
            // 身份证地区码检查
            if (!checkIdNumberArea(idNumber.substring(0, 6))) {
                return false;
            }
            // 将15位身份证转换为18位
            idNumber = convertFifteenToEighteen(idNumber);
            // 身份证出生日期检查
            if (!checkBirthday(idNumber.substring(6, 14))) {
                return false;
            }
            // 身份证校验码检查
            if (!checkIdNumberVerifyCode(idNumber)) {
                return false;
            }
        }
        return true;
    }


    /**
     * 判断一个字符串是否都为数字
     */
    public static boolean isDigit(String strNum) {
        Pattern pattern = Pattern.compile("[0-9]{1,}");
        Matcher matcher = pattern.matcher((CharSequence) strNum);
        return matcher.matches();
    }

    /**
     * 身份证正则校验
     */
    private static boolean checkIdNumberRegex(String idNumber) {
        return Pattern.matches("^([0-9]{17}[0-9Xx])|([0-9]{15})$", idNumber);
    }

    /**
     * 将15位身份证转换为18位
     */
    private static String convertFifteenToEighteen(String idNumber) {
        if (15 != idNumber.length()) {
            return idNumber;
        }
        // 年份前加19，并加上数据校验位
        idNumber = idNumber.substring(0, 6) + "19" + idNumber.substring(6, 15);
        idNumber = idNumber + getVerifyCode(idNumber);
        return idNumber;
    }

    /**
     * 身份证地区码检查
     *  注意：这里仅仅对比了省份编码，没有对比市县镇编码，是因为经过核对GB/T2260-1999中的地区编码很多与身份证号的地区编码不符，因此没有比对完整的地区编码
     */
    private static boolean checkIdNumberArea(String idNumberArea) {
        // 比对港澳台地区编码
        int areaCode = Integer.parseInt(idNumberArea);
        if (areaCode == HONGKONG_AREACODE || areaCode == MACAO_AREACODE || areaCode == TAIWAN_AREACODE) {
            return true;
        }
        // 比对其他省份编码
        Integer provincesCodeTemp = Integer.parseInt(idNumberArea.substring(0,2));
        return  provincesCode.contains(provincesCodeTemp);
    }

    /**
     * 身份证出生日期嘛检查
     */
    private static boolean checkBirthday(String idNumberBirthdayStr) {
        Integer year = null;
        try {
            year = Integer.valueOf(idNumberBirthdayStr.substring(0, 4));
        } catch (Exception e) {
        }
        if (null == year) {
            return false;
        }

        boolean birthdayMatch;
        // 校验出生日期是否是标准的日期格式
        if (isLeapYear(year)) {
            // 闰年
            birthdayMatch = Pattern.matches(regexBirthdayInLeapYear, idNumberBirthdayStr);
        } else {
            birthdayMatch = Pattern.matches(regexBirthdayInCommonYear, idNumberBirthdayStr);
        }
        if(birthdayMatch){
            // 出生日期只能小于等于当前系统日期
            SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
            Date birthday;
            try {
                birthday = formatter.parse(idNumberBirthdayStr);
            } catch (ParseException e) {
                e.printStackTrace();
                return false;
            }
            int number = birthday.compareTo(new Date());
            return number == -1 || number ==0;
        }
        return birthdayMatch;
    }

    /**
     * 判断是否为闰年
     */
    private static boolean isLeapYear(int year) {
        return (year % 400 == 0) || (year % 100 != 0 && year % 4 == 0);
    }

    /**
     * 根据身份证前17位计算身份证校验码
     */
    private static String getVerifyCode(String idNumber) {
        if (!Pattern.matches(regexNum, idNumber.substring(0, 17))) {
            return null;
        }
        String[] ValCodeArr = { "1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2" };
        String[] Wi = { "7", "9", "10", "5", "8", "4", "2", "1", "6", "3", "7", "9", "10", "5", "8", "4", "2" };

        int sum = 0;
        for (int i = 0; i < 17; i++) {
            sum = sum + Integer.parseInt(String.valueOf(idNumber.charAt(i))) * Integer.parseInt(Wi[i]);
        }
        return ValCodeArr[sum % 11];
    }

    /**
     * 身份证校验码检查
     */
    private static boolean checkIdNumberVerifyCode(String idNumber) {
        return getVerifyCode(idNumber).equalsIgnoreCase(idNumber.substring(17));
    }

    public static void main(String[] args) {
        System.out.println(evaluate("916459207804061760"));

    }
}