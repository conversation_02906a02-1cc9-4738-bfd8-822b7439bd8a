package com.qcc.udf.lawer;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date ：Created in 2021/10/09 14:05
 * @description ：根据执业证号获取律师从业年限
 */
public class GetLawyerAge  extends UDF {

    public static String evaluate(String lawyerLicenseNo) {

        String result = "-1";
        //根据lawyer_license_no计算执业年限计算
        if (StringUtils.isNotEmpty(lawyerLicenseNo) && lawyerLicenseNo.length() == 17) {
            try {
                int year = Integer.parseInt(lawyerLicenseNo.substring(5,9));
                int nowYear = LocalDateTime.now().getYear();
                int diff = nowYear - year;
                if (diff > 0 && diff < 50) {
                    result = String.valueOf(diff);
                }
            } catch (Exception e) {

            }
        }
        return result;
    }

    public static void main(String[] args) {
        System.out.println(evaluate("1350220151022612"));
    }
}
