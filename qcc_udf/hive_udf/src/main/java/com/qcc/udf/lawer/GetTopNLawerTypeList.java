package com.qcc.udf.lawer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qcc.udf.casesearch_v3.entity.input.BaseCaseEntity;
import lombok.Data;
import org.apache.hadoop.hive.ql.exec.UDF;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021年10月25日 17:29
 */
public class GetTopNLawerTypeList extends UDF {
    public static String evaluate(String jsonDetail, int topN) {
        List<LawerCommonList> outList = JSON.parseArray(jsonDetail,LawerCommonList.class);
        outList = outList.stream().sorted(Comparator.comparing(LawerCommonList::getCnt,Comparator.reverseOrder())
                .thenComparing(LawerCommonList::getName,Comparator.reverseOrder())).collect(Collectors.toList());
        if(outList.size()>topN){
            outList = outList.subList(0,topN);
        }
        return JSON.toJSONString(outList);
    }

    public static void main(String[] args) {
        String json = "[{\"name\":\"买卖合同纠纷\",\"cnt\":8,\"casesearchids\":\"\"},{\"name\":\"金融借款合同纠纷\",\"cnt\":68,\"casesearchids\":\"\"},{\"name\":\"机动车交通事故责任纠纷\",\"cnt\":602,\"casesearchids\":\"\"},{\"name\":\"离婚纠纷\",\"cnt\":6,\"casesearchids\":\"\"},{\"name\":\"合同纠纷\",\"cnt\":6,\"casesearchids\":\"\"},{\"name\":\"财产损失保险合同纠纷\",\"cnt\":6,\"casesearchids\":\"\"},{\"name\":\"返还原物纠纷\",\"cnt\":5,\"casesearchids\":\"\"},{\"name\":\"提供劳务者受害责任纠纷\",\"cnt\":4,\"casesearchids\":\"\"},{\"name\":\"交通肇事罪\",\"cnt\":4,\"casesearchids\":\"\"},{\"name\":\"其他民事\",\"cnt\":3,\"casesearchids\":\"\"},{\"name\":\"人身保险合同纠纷\",\"cnt\":3,\"casesearchids\":\"\"},{\"name\":\"劳动争议\",\"cnt\":3,\"casesearchids\":\"\"},{\"name\":\"民间借贷纠纷\",\"cnt\":27,\"casesearchids\":\"\"},{\"name\":\"保险纠纷\",\"cnt\":24,\"casesearchids\":\"\"},{\"name\":\"保险人代位求偿权纠纷\",\"cnt\":23,\"casesearchids\":\"\"},{\"name\":\"追偿权纠纷\",\"cnt\":23,\"casesearchids\":\"\"},{\"name\":\"医疗损害责任纠纷\",\"cnt\":2,\"casesearchids\":\"\"},{\"name\":\"房屋租赁合同纠纷\",\"cnt\":2,\"casesearchids\":\"\"},{\"name\":\"建设工程施工合同纠纷\",\"cnt\":2,\"casesearchids\":\"\"},{\"name\":\"建筑设备租赁合同纠纷\",\"cnt\":2,\"casesearchids\":\"\"},{\"name\":\"人寿保险合同纠纷\",\"cnt\":2,\"casesearchids\":\"\"},{\"name\":\"责任保险合同纠纷\",\"cnt\":2,\"casesearchids\":\"\"},{\"name\":\"生命权、健康权、身体权纠纷\",\"cnt\":2,\"casesearchids\":\"\"},{\"name\":\"信用卡纠纷\",\"cnt\":193,\"casesearchids\":\"\"},{\"name\":\"财产保险合同纠纷\",\"cnt\":12,\"casesearchids\":\"\"},{\"name\":\"股权转让纠纷\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"容留他人吸毒罪\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"妨害公务罪\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"民事案件执行\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"机动车交通事故责任纠纷案件执行\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"小额借款合同纠纷\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"其他案由\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"银行卡纠纷案件执行\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"危险驾驶罪\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"非法侵入住宅罪\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"生产、销售不符合安全标准的产品罪\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"确认合同有效纠纷\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"劳动和社会保障行政管理（劳动、社会保障）\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"侵权责任纠纷\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"公路旅客运输合同纠纷\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"诉讼、仲裁、人民调解代理合同纠纷\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"收买被拐卖的妇女、儿童罪\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"财产损害赔偿纠纷\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"地面施工、地下设施损害责任纠纷\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"合伙协议纠纷\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"委托合同纠纷\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"工伤保险待遇纠纷\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"公路货物运输合同纠纷\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"装饰装修合同纠纷\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"建设用地使用权转让合同纠纷\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"商品房销售合同纠纷\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"借款合同纠纷案件执行\",\"cnt\":1,\"casesearchids\":\"\"},{\"name\":\"拒不支付劳动报酬罪\",\"cnt\":1,\"casesearchids\":\"\"}]";
        System.out.println(evaluate(json, 10));
    }
}

@Data
class LawerCommonList{
    private String name;
    private int cnt;
}
